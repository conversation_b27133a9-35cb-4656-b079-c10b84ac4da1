import { api } from './client';

// 配置类型定义
interface AppConfig {
  name: string;
  debug: boolean;
  log_level: string;
  host: string;
  port: number;
  cors_origins: string[];
}

interface StorageConfig {
  upload_dir: string;
  output_dir: string;
  max_upload_size: number;
}

interface OssConfig {
  access_key_id: string;
  access_key_secret: string;
  bucket_name: string;
  endpoint: string;
  base_url: string;
}

interface LlmConfig {
  api_key: string;
  base_url: string;
  default_model: string;
  alternative_models: string[];
  timeout: number;
  max_retries: number;
  temperature: number;
}

interface FeishuConfig {
  app_id: string;
  app_secret: string;
  timeout: number;
}

interface PromptsConfig {
  dir: string;
  default_category: string;
  default_prompt: string;
}

interface TaskQueueConfig {
  max_workers: number;
  result_ttl: number;
}

interface SystemConfig {
  app: AppConfig;
  storage: StorageConfig;
  oss: OssConfig;
  llm: LlmConfig;
  feishu: FeishuConfig;
  prompts: PromptsConfig;
  task_queue: TaskQueueConfig;
}

type ConfigSection = keyof SystemConfig;

// 获取当前配置
export const getConfig = async (): Promise<SystemConfig> => {
  return api.get<SystemConfig>('/configs');
};

// 更新配置
export const updateConfig = async (config: Partial<SystemConfig>): Promise<SystemConfig> => {
  return api.put<SystemConfig>('/configs', config);
};

// 重置配置
export const resetConfig = async (): Promise<SystemConfig> => {
  return api.post<SystemConfig>('/configs/reset', {});
};

// 获取指定部分的配置
export const getConfigSection = async <T extends keyof SystemConfig>(
  section: T
): Promise<SystemConfig[T]> => {
  return api.get<SystemConfig[T]>(`/configs/${section}`);
};

// 更新指定部分的配置
export const updateConfigSection = async <T extends keyof SystemConfig>(
  section: T,
  config: Partial<SystemConfig[T]>
): Promise<SystemConfig[T]> => {
  return api.put<SystemConfig[T]>(`/configs/${section}`, config);
};

export const configApi = {
  getConfig,
  updateConfig,
  resetConfig,
  getConfigSection,
  updateConfigSection,
};

export type {
  AppConfig,
  StorageConfig,
  OssConfig,
  LlmConfig,
  FeishuConfig,
  PromptsConfig,
  TaskQueueConfig,
  SystemConfig,
  ConfigSection,
}; 
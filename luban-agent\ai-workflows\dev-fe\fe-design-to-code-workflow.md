# 设计到代码的递归拆分工作流程

## 流程图

```mermaid
graph TD
    A[开始] --> B[解析PRD需求]
    B --> C[加载Figma设计稿]
    C --> J[提取Design Tokens &布局约束]
    J --> D{是否可拆分?}
    D -->|是| E[拆分为子组件]
    E --> F[生成交互状态矩阵]
    F --> L[生成状态-动画交互提示]
    L --> D
    D -->|否| G[创建原子任务项]
    G --> H[生成任务树]
    H --> N[计算AI生成置信度评分]
    N --> Q[生成组件代码初稿]
    Q --> R[生成 Storybook 示例与中文注释]
    R --> I[结束]


```

## 节点说明

### A【开始】

* **作用**：流程启动入口
* **输入**：PRD 文件、Figma 设计图链接
* **输出**：启动任务流程
* **价值**：激活整个自动化任务拆解与代码生成链路

---

### B【解析 PRD 需求】

* **作用**：结构化提取功能模块、交互要求、字段信息
* **输入**：PRD 文档
* **输出**：结构化需求清单
* **价值**：为组件拆解提供功能边界和交互参考

---

### C【加载 Figma 设计稿】

* **作用**：解析设计图层结构与组件属性
* **输入**：Figma 文件链接
* **输出**：
  * 图层树结构
  * 元素样式（字体、颜色、尺寸、约束等）
* **价值**：提供视觉和结构基础数据源

---

### J【提取 Design Tokens & 布局约束】

* **作用**：生成统一样式规范
* **输入**：Figma 样式层信息
* **输出**：
  * `design-tokens.json`
  * Layout Constraints 数据结构
* **价值**：
  * 提升代码一致性
  * 降低重复样式生成

---

### D【是否可拆分？】

* **作用**：判断当前 UI 单元是否仍可细分
* **输入**：当前组件节点
* **输出**：是 / 否（布尔判断）
* **判断依据**：
  * 是否具备独立功能
  * 是否拥有子图层或交互
  * 是否具备复用价值

---

### E【拆分为子组件】

* **作用**：递归拆解复合组件
* **输入**：可继续拆分的 UI 区块
* **输出**：多个子组件节点
* **价值**：
  * 提高模块化
  * 增强复用与可维护性

---

### F【生成交互状态矩阵】

* **作用**：为组件定义状态变化和交互触发器
* **输入**：Figma 动效设置、PRD 交互说明
* **输出**：`[页面]-state-matrix.yaml`
* **示例**：

  ```yaml
  component: button_primary
  states: [default, hover, active]
  transitions:
    - from: default
      to: hover
      trigger: mouse_enter
  ```

---

### L【生成状态-动画交互提示】

* **作用**：结合状态变化，生成动效建议
* **输入**：状态矩阵
* **输出**：`[页面]-animation-hints.yaml`
* **价值**：

  * 指导生成 CSS 动画代码
  * 确保动效风格一致

---


### G【创建原子任务项】

* **作用**：生成最小可执行任务
* **输入**：不可再拆的组件节点
* **输出**：任务项 Markdown 文档（命名格式：`[序号]-[类型]-[功能关键词].md`）
* **内容包含**：
  * 描述、验收标准、Figma 节点链接、SVG 资源等

---

### H【生成任务树】

* **作用**：组织所有任务项，生成任务结构图
* **输入**：所有原子任务项
* **输出**：任务树文档（`[页面]_tasktree.md`）
* **内容**：
  * 开发顺序
  * 依赖关系图
  * 各任务状态与优先级

---

### N【计算 AI 生成置信度评分】

* **作用**：预测每个任务自动生成代码的可信度
* **输入**：任务复杂度、状态数、模板命中率等
* **输出**：置信度评分（0\~100%）、推荐人工介入建议
* **文档输出**：`ai-confidence-summary.yaml`
* **价值**：
  * 帮助识别高风险任务
  * 辅助 QA/Code Review 优先级管理

---

### Q【生成组件代码初稿】
- **作用**：根据任务项与设计信息生成组件出事代码
- **输入**：
  - 原子任务项结构
  - 状态矩阵
  - 动画提示
  - 设计token
  - 代码模版匹配结果
- **输出**：
  - React组件代码（apps/[应用名]/components/[页面]/[组件名].tsx）
- **示例代码**：
```tsx
// app/components/user-list.tsx
import React from 'react';
import { TransformedUser } from '../models/user-model'; // 对应 ViewModel 提供的数据结构

interface UserListProps {
  users: TransformedUser[];
  onUserClick: (userId: number) => void; // 示例：处理用户点击事件
}

export function UserList({ users, onUserClick }: UserListProps) {
  if (users.length === 0) {
    return <p>没有找到用户。</p>;
  }

  return (
    <ul>
      {users.map((user) => (
        <li key={user.id} onClick={() => onUserClick(user.id)} style={{ cursor: 'pointer' }}>
          {user.displayName} ({user.initials})
        </li>
      ))}
    </ul>
  );
}
```  
- **规范要求**：
  - 使用shadcn/ui,lucide-react等通用组件库
  - 所有代码注释为简洁中文
  - 默认导出组件，支持Props
  - 支持状态样式、动画与交互
  - 使用文件头注释说明任务来源与置信度
- **价值**：
  - 保证输出一致规范
  - 提升AI输出代码质量与协作体验

---

### R【生成 Storybook 示例与中文注释】
- **作用**：为每个组件生成可视化使用示例与开发文档
- **输入**：
  - 已生成组件代码
  - 组件Props类型定义
- **输出**：
  - Storybook示例文件(apps/stu-ai/stories/[组件名].stories.tsx)
  - 中文注释说明各个状态和交互方式
- **内容包含**：
  - 默认状态渲染
  - 多状态切换逻辑
  - 说明任务项来源、设计节点链接
- **价值**：
  - 降低使用门槛
  - 提升协作与回顾效率

---

### I【结束】

* **作用**：流程终点，输出全部任务文档与资源
* **输出目录结构**：
  * `task/[页面简称]/`
  * 包含任务项、状态矩阵、动画提示、SVG 图等
* **价值**：
  * 为代码生成与协作开发做好准备
  * 可集成入 IDE 插件或自动部署流程

---
## 输出目录结构
apps/[应用名]/
├── components/
│   └── [页面简称]/
│       └── [组件名].tsx         # 组件实现（含中文注释）
├── stories/
│   └── [组件名].stories.tsx    # Storybook 示例
└── task/
    └── [页面简称]/
        ├── tasktree.md
        ├── *.md                # 原子任务项文档
        ├── svg/
        ├── [页面]-state-matrix.yaml
        ├── [页面]-animation-hints.yaml
        ├── design-tokens.json
        └── ai-confidence-summary.yaml

## 深入分析

### 1. 核心目标
- 将Figma设计图转化为可执行的开发任务
- 确保任务的可管理性和可追踪性
- 保持任务之间的依赖关系清晰
- 便于团队协作和进度管理
- 增强 Figma → Code 的自动化与准确性
- 通过组件语义、状态矩阵、动画提示等方式，帮助 AI 更精确地生成代码
- 提供置信度机制辅助人工审查
- 标准化组件风格输出，提高一致性与维护性

### 2. 拆分标准
1. **功能独立性**
   - 组件是否具有独立的功能
   - 是否可以被单独测试

2. **技术边界**
   - 前端/后端分离
   - 服务/API 边界
   - 数据流边界

### 3. 任务管理
1. **任务粒度**
   - 最小任务单元应该能在 1天内完成
   - 任务描述应该清晰明确
   - 包含具体的验收标准
   - 引入 ai_confidence 字段，供开发者评估 AI 代码可信度，决定是否人工审查

2. **依赖关系**
   - 明确任务之间的依赖关系
   - 识别关键路径
   - 规划并行开发的可能性


### 4. 质量控制
1. **设计评审**
   - 确保拆分后的设计保持一致性
   - 验证技术可行性

2. **任务评审**
   - 评估任务的可执行性
   - 检查任务描述的完整性

3. **语义一致性校验**
   - 比较组件语义映射是否覆盖完整的UI结构
   - 对模糊节点进行人工二次确认

### 5. 迭代优化
1. **反馈机制**
   - 收集开发过程中的反馈
   - 调整任务拆分策略
   - 优化工作流程

2. **持续改进**
   - 记录拆分过程中的经验教训
   - 更新拆分标准
   - 优化任务管理方法

## 拆分要求

### 1. 拆分策略
- 采用自上而下的拆分方式
- 优先考虑业务功能边界
- 保持组件的可复用性

### 2. 任务管理
- 使用项目管理文档跟踪任务
- 定期更新任务状态



## 输入物
1. Figma 设计图Link
2. PRD文件


## 输出物
1. 任务树文档（任务树文档命名规则：[页面简称]_tasktree.md , 示例：aiclass_tasktree.md）
   - 项目概述
   - 任务列表（任务项文件，状态，优先级）
   - 开发顺序建议
   - 依赖关系图
   - 注意事项
   - 各组件在页面位置

2. 任务项文档（任务项文档命名规则：[序号]-[组件类型]-[功能描述].md ,示例：03-sidebar-knowlefgelist.md 。序号为开发顺序，组件类型为小写英文描述组件层级，功能描述简明功能关键词。）
   -  任务描述, 简单描述
   -  关联PRD需求条目
   -  任务对应的Figma 设计图Link和对应节点
   -  根据任务节点获取对应的组件完成需要的svg图（保存于 task/svg 目录下）
      - 提取 Figma 设计稿中不能通过基础样式实现的部分，下载对应的 SVG 图标。
      - 例如：`wifi-icon.svg`, `battery-icon.svg`，或者任何设计中具有独特风格的 SVG 元素。
   -  Figma设计稿中对应节点的CSS样式信息
   -  任务验收标准
   -  任务依赖关系
   -  任务状态
   -  任务创建时间
   -  任务更新时间
   -  ai_confindence: xx%
   - requires_review: true / false


3. 交互状态矩阵文档（命名规则：[页面简称]-state-matrix.yaml）
   示例
```yaml
      component: button_primary
   states:
   - default
   - hover
   - active
   - disabled
   transitions:
   - from: default
       to: hover
       trigger: mouse_enter
```

4. 动画提示文档 (命名规则：[页面简称]-animation-hints.yaml)
   示例
```yaml
button_primary:
  default -> hover:
    animation: transition-all duration-300 ease-in-out
  hover -> active:
    animation: scale-95

```

5. 样式设计Token文档 (命名规则：design-tokens.json)
示例
```json
{
  "colors": {
    "primary": "#1A73E8",
    "background": "#FFFFFF",
    "text": "#333333"
  },
  "fonts": {
    "body": "Inter, sans-serif",
    "heading": "Poppins, sans-serif"
  },
  "radius": {
    "default": "8px"
  }
}
```

6. AI置信度汇总文档（ai-confidence-summary.yaml）
```yaml
tasks:
  - id: 01-button-login
    ai_confidence: 90%
    requires_review: false
  - id: 02-input-password
    ai_confidence: 72%
    requires_review: true

```

7. 任务树,任务项文档,交互状态矩阵文档，动画提示文档，样式设计Token文档和AI置信度汇总文档保存于 task/[页面简称]/目录下

8. 获取的svg资源目录： task/[页面简称]/svg/


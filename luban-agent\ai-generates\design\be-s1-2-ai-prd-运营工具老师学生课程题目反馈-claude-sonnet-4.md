# 产品需求文档：运营工具_老师&学生课程题目反馈系统 v9.0.0

## 1. 需求背景与目标

### 1.1 需求目标
- 通过该需求的实现，可以提升用户对课程内容的反馈途径，将"用户对课程内容挑错"当成是帮助产品变得更好的驱动力
- 该需求上线后，课程质量预计可以快速提升，用户对课程内容的体验得到优化，教师参与积极性预计可以提升

**具体目标**：
1. **对用户**：支持教师端&学生端用户快速提交课程问题；完成修改后为老师下发积分或奖励，提升用户产品参与感，减化用户对内容bug的反感
2. **对系统**：通过收集->快速解决问题，实现优化课程，快速提升用户对课程内容的体验；使用Agent自动归类用户反馈，并在后续流程中通过人工打标签提升模型对好课程的要求

**覆盖用户**：
- 反馈人：老师端和学生端用户
- 改内容：教研教师、总部教研管理老师  
- 积分：运营下发奖励

## 2. 功能范围

### 2.1 核心功能

- **教师端反馈系统**: 支持课程反馈、题目反馈、视频MV反馈、其他建议反馈，满足教师发现问题快速反馈的场景，解决教师无法及时反馈课程问题的痛点
  - 优先级：高
  - 依赖关系：无

- **学生端反馈系统**: 支持课程反馈、题目反馈、其他bug反馈，满足学生学习过程中发现问题反馈的场景，解决学生反馈渠道不足的痛点
  - 优先级：高
  - 依赖关系：无

- **生产后台反馈管理**: 用户反馈列表管理、任务分发统计、数据统计分析，满足后台人员高效处理反馈的场景，解决反馈处理效率低的痛点
  - 优先级：高
  - 依赖关系：依赖于教师端和学生端反馈功能

- **消息通知系统**: 教师端消息中心展示反馈处理结果通知，满足用户了解反馈处理进度的场景，解决用户不知道反馈处理结果的痛点
  - 优先级：高
  - 依赖关系：依赖于生产后台处理

### 2.2 辅助功能

- **智能Agent审核**: 对文字类内容反馈进行自动分类和初筛，为生产后台反馈管理提供支持
- **工作流自动化**: 自动分发任务给对应学科生产负责人，为反馈处理流程提供支持

### 2.3 非本期功能

- **积分历史&消费**: 教师端积分使用历史和消费功能，建议后续迭代再考虑
- **奖品下发**: 积分兑换实物奖品功能，建议后续迭代再考虑

## 3. 计算规则与公式

- **反馈分类规则**
  - **文字类内容反馈**: 板书文字问题、字幕问题、题目问题(题干/选项/解析)
    - 参数说明：
      - 反馈内容：用户提交的文字描述
      - 课程/题目ID：定位具体内容的标识
    - 规则：
      - 取值范围：通过Agent自动审核，判定为有效/无效反馈
  
  - **非文字类内容反馈**: 板书图片/圈画问题、配音问题、音画不同步、视频问题等
    - 参数说明：
      - 反馈类型：预设的问题分类选项
      - 截图信息：用户上传或系统自动截取的图片
    - 规则：
      - 取值范围：直接进入人工审核流程

- **积分下发规则**
  - **有效反馈积分**: 被采纳的有效反馈给予积分奖励
    - 参数说明：
      - 反馈有效性：人工确认的反馈价值
      - 问题严重程度：影响用户体验的程度
    - 规则：
      - 取值范围：根据问题类型和严重程度确定积分数量

## 4. 用户场景与故事

### 教师发现课程问题并快速反馈
作为一个教师，我希望能够在AI课预览过程中快速反馈发现的问题，这样我就可以帮助改善课程质量并获得相应的积分奖励。目前的痛点是发现问题后没有便捷的反馈渠道，如果能够在课程播放页面直接提交反馈，对我的教学工作会有很大帮助。

**关键步骤：**
1. 教师在AI课预览页发现问题（板书错误、字幕问题、配音问题等）
2. 点击悬浮的【反馈】按钮
3. 选择问题类型（可多选）并填写问题描述
4. 系统自动截图并提交反馈
5. 收到"感谢您的反馈，我们会尽快处理！"的确认提示

```mermaid
sequenceDiagram
    participant T as 教师
    participant UI as 课程播放页面
    participant FS as 反馈收集系统
    participant AS as 智能Agent审核服务
    participant NS as 消息通知服务
    
    T->>UI: 预览AI课程内容
    T->>T: 发现问题（板书错误/字幕问题/配音问题）
    T->>UI: 点击悬浮反馈按钮
    UI->>T: 展示反馈表单
    T->>UI: 选择问题类型（可多选）
    T->>UI: 填写问题描述
    UI->>UI: 自动截取当前页面
    T->>UI: 提交反馈
    UI->>FS: 发送反馈数据
    FS->>AS: 触发智能审核流程
    FS->>NS: 发送确认通知
    NS->>T: 显示'感谢您的反馈，我们会尽快处理！'
```

- 优先级：高
- 依赖关系：无

### 学生发现题目错误并反馈
作为一个学生，我希望能够在答题过程中反馈发现的题目错误，这样我就可以帮助改善题目质量，避免其他同学遇到同样的问题。目前的痛点是发现题目错误后无法及时反馈，如果能够在答题页面直接提交反馈，对我的学习会有很大帮助。

**关键步骤：**
1. 学生在答题过程中发现题目问题（题干错误、答案错误、解析错误等）
2. 点击题目页面的【反馈】按钮
3. 选择题目问题类型并描述具体问题
4. 系统自动截图并收集相关信息
5. 提交反馈并收到确认提示

```mermaid
sequenceDiagram
    participant S as 学生
    participant QP as 答题页面
    participant FS as 反馈收集系统
    participant QS as 题目信息服务
    participant AS as 智能Agent审核服务
    participant NS as 消息通知服务
    
    S->>QP: 进行答题练习
    S->>S: 发现题目问题（题干/答案/解析错误）
    S->>QP: 点击反馈按钮
    QP->>S: 展示题目反馈表单
    S->>QP: 选择题目问题类型
    S->>QP: 描述具体问题
    QP->>QP: 自动截图当前题目
    QP->>QS: 收集题目相关信息（题目ID、内容等）
    S->>QP: 提交反馈
    QP->>FS: 发送反馈数据（包含题目信息）
    FS->>AS: 触发智能审核流程
    FS->>NS: 发送确认通知
    NS->>S: 显示反馈提交成功提示
```

- 优先级：高
- 依赖关系：无

### 生产人员高效处理用户反馈
作为一个生产后台管理人员，我希望能够高效地处理用户反馈，这样我就可以快速改善课程质量并及时回复用户。目前的痛点是反馈信息分散，处理效率低，如果能够有统一的反馈管理后台，对我的工作会有很大帮助。

**关键步骤：**
1. 接收到新的用户反馈通知（飞书消息）
2. 在多维表格中查看反馈详情和Agent初筛结果
3. 根据反馈类型进行人工审核和分类
4. 对有效反馈进行修改和上线处理
5. 更新反馈状态并触发用户通知

```mermaid
flowchart TD
    A[新用户反馈提交] --> B[反馈收集系统]
    B --> C{反馈类型判断}
    
    C -->|文字类内容| D[Agent自动审核]
    C -->|非文字类内容| E[直接进入人工审核]
    C -->|功能bug| F[技术oncall流程]
    C -->|功能建议| G[产品评估流程]
    
    D --> H[Agent审核结果]
    H --> I[多维表格记录]
    E --> I
    
    I --> J[飞书通知生产人员]
    J --> K[生产人员查看反馈详情]
    K --> L[人工审核和分类]
    
    L --> M{是否采纳}
    M -->|采纳| N[生产环境修改]
    M -->|不采纳| O[标记无效反馈]
    
    N --> P[内容审核上线]
    P --> Q[更新反馈状态为已处理]
    O --> R[更新反馈状态为未采纳]
    
    Q --> S[触发用户通知]
    R --> S
    S --> T[用户消息中心显示结果]
    Q --> U[积分下发流程]
```

- 优先级：高
- 依赖关系：依赖于用户反馈提交

### 教师接收反馈处理结果
作为一个教师，我希望能够及时了解我提交的反馈的处理结果，这样我就可以知道我的建议是否被采纳，并获得相应的积分奖励。目前的痛点是不知道反馈的处理进度，如果能够在消息中心看到处理结果，对我的参与积极性会有很大帮助。

**关键步骤：**
1. 教师点击头像菜单中的【消息中心】
2. 查看反馈处理结果消息
3. 了解反馈是否被采纳及原因
4. 查看获得的积分奖励（如有）
5. 在个人中心查看积分余额

```mermaid
sequenceDiagram
    participant T as 教师
    participant UI as 教师端界面
    participant MC as 消息中心服务
    participant PS as 积分系统
    participant PC as 个人中心
    
    Note over T,PC: 反馈处理完成后的通知流程
    
    MC->>UI: 推送反馈处理结果通知
    T->>UI: 点击头像菜单
    T->>MC: 进入消息中心
    MC->>T: 展示反馈处理结果列表
    
    T->>MC: 查看具体反馈处理结果
    MC->>T: 显示反馈详情
    
    alt 反馈被采纳
        MC->>T: 显示采纳原因和修改说明
        MC->>PS: 查询积分奖励信息
        PS->>MC: 返回积分详情
        MC->>T: 显示获得的积分奖励
        T->>PC: 进入个人中心
        PC->>PS: 查询积分余额
        PS->>PC: 返回最新积分余额
        PC->>T: 显示积分余额更新
    else 反馈未被采纳
        MC->>T: 显示未采纳原因和说明
    end
```

- 优先级：中
- 依赖关系：依赖于生产后台处理

**复杂场景处理说明**：
- 多角色交互场景：需要明确教师/学生反馈、生产人员处理、系统通知的完整流程
- 异常情况处理：需要描述Agent审核失误、人工复核纠正的机制
- 数据依赖场景：需要说明课程ID、题目ID等关键数据的获取和关联方式

## 5. 业务流程图

### 整体反馈处理流程

```mermaid
graph TD
    A[用户发现问题] --> B{用户类型}
    B -->|教师| C[教师端反馈]
    B -->|学生| D[学生端反馈]
    
    C --> E[反馈收集后台]
    D --> E
    
    E --> F{反馈类型判断}
    F -->|文字类内容| G[Agent自动审核]
    F -->|非文字类内容| H[直接人工审核]
    F -->|功能bug| I[技术oncall流程]
    F -->|功能建议| J[产品评估流程]
    
    G --> K[人工复核Agent结果]
    H --> L[人工审核分类]
    K --> M{是否采纳}
    L --> M
    
    M -->|采纳| N[生产环境修改]
    M -->|不采纳| O[标记无效]
    
    N --> P[审核上线]
    O --> Q[发送未采纳通知]
    P --> R[发送采纳通知]
    
    Q --> S[用户消息中心]
    R --> S
    R --> T[积分下发]
    T --> U[教师个人中心]
```

### Agent工作流程

```mermaid
graph TD
    A[接收反馈] --> B[获取反馈信息]
    B --> C[调用查询API]
    C --> D[获取原始内容]
    D --> E{反馈类型}
    
    E -->|板书文字| F[dify1工作流]
    E -->|字幕问题| G[dify2工作流]  
    E -->|题目问题| H[dify3工作流]
    
    F --> I[纠错分析]
    G --> I
    H --> I
    
    I --> J[输出结论]
    J --> K[返回审核结果]
    K --> L[人工复核]
```

## 6. 性能与安全需求

### 6.1 性能需求

- 响应时间：反馈提交、消息通知等核心功能的P95响应时间应不超过3秒
- 并发用户数：系统需能支持至少1000个用户同时在线提交反馈的场景，且性能无明显下降
- Agent处理能力：智能审核Agent应在接收到反馈后的5秒内完成分析并输出结果
- 数据处理能力：系统需能处理每秒100次反馈提交请求，支持大量用户同时反馈的场景

### 6.2 安全需求

- 用户认证与授权：所有反馈提交和后台管理操作必须经过严格的身份认证和权限校验，支持基于角色的访问控制
- 数据保护：用户反馈内容、个人信息在存储和传输过程中必须加密，确保用户隐私安全
- 操作审计：对所有反馈处理操作（审核、分类、状态变更、积分下发）必须记录详细的操作日志，包含操作人、操作时间、操作对象、操作内容等关键信息
- 防护要求：系统应具备对常见Web攻击的基本防护能力，防止恶意反馈和数据泄露

## 7. 验收标准

### 7.1 功能验收

- **教师端反馈功能**：
  - 使用场景：当教师在AI课预览页发现问题时点击反馈按钮
  - 期望结果：系统应展现反馈表单，支持问题类型选择、文字描述、自动截图，提交成功后显示确认提示

- **学生端反馈功能**：
  - 使用场景：当学生在答题过程中发现题目问题时点击反馈按钮
  - 期望结果：系统应展现题目反馈表单，支持问题类型选择、问题描述，自动收集题目相关信息

- **生产后台管理**：
  - 使用场景：当有新反馈提交时
  - 期望结果：系统应在多维表格中显示反馈信息，支持筛选、排序、分组，触发飞书通知给对应负责人

- **消息通知系统**：
  - 使用场景：当反馈处理完成时
  - 期望结果：系统应在教师端消息中心显示处理结果，区分采纳/未采纳状态，显示相应文案

### 7.2 性能验收

- 响应时间：
  - 测试场景：模拟100个并发用户同时提交反馈
  - 验收标准：P95响应时间不超过3秒

- Agent处理能力：
  - 测试场景：提交文字类反馈到Agent审核完成
  - 验收标准：处理时间不超过5秒，准确率达到80%以上

### 7.3 安全验收

- 用户认证与授权：
  - 测试场景：未登录用户尝试提交反馈
  - 验收标准：系统应拒绝操作，并返回明确的登录提示

- 操作审计：
  - 测试场景：执行反馈状态变更操作
  - 验收标准：操作日志被准确记录，包含操作人、时间、内容等必要信息

## 8. 其他需求

### 8.1 可用性需求

- 界面友好：反馈功能的操作界面应简洁直观，用户在无额外培训的情况下，3步内可完成反馈提交
- 容错处理：当发生网络异常或提交失败时，系统应能给出清晰的错误提示和重试引导，避免用户反馈内容丢失
- 兼容性：产品核心功能需在Chrome最新版、Safari最新版、移动端主流浏览器得到良好支持

### 8.2 维护性需求

- 配置管理需求：反馈分类规则、Agent审核阈值、积分下发规则应支持运营人员通过管理后台进行可视化调整
- 监控与告警需求：当反馈处理积压、Agent审核异常、用户反馈激增时，系统应触发告警通知相关团队
- 日志需求：用户反馈提交、后台处理操作、系统状态变更等关键事件必须被记录，支持问题排查和数据分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制

- 产品应提供多种用户反馈渠道：应用内反馈入口、教师端消息中心、客服支持
- 应有机制定期收集、整理和分析来自各渠道的用户反馈，识别产品问题和改进机会

### 9.2 迭代计划（高阶产品方向）

- 产品计划以每双周为周期进行迭代，持续优化用户体验和增加新价值
- 下一个迭代重点可能包括：积分使用功能、反馈质量评估机制、Agent审核能力优化

## 10. 需求检查清单

使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 备注 |
|---------|-----------|--------|--------|--------|------|
| 教师端课程反馈功能 | 2.1核心功能-教师端反馈系统 | ✅完整 | ✅准确 | ✅一致 | 包含课程、题目、视频MV、其他反馈 |
| 学生端反馈功能 | 2.1核心功能-学生端反馈系统 | ✅完整 | ✅准确 | ✅一致 | 包含课程、题目、其他bug反馈 |
| 生产后台反馈管理 | 2.1核心功能-生产后台反馈管理 | ✅完整 | ✅准确 | ✅一致 | 多维表格实现，包含列表、分发、统计 |
| 消息通知系统 | 2.1核心功能-消息通知系统 | ✅完整 | ✅准确 | ✅一致 | 教师端消息中心展示处理结果 |
| 智能Agent审核 | 2.2辅助功能-智能Agent审核 | ✅完整 | ✅准确 | ✅一致 | 文字类内容自动分类和初筛 |
| 积分展示功能 | 2.2辅助功能-积分展示系统 | ✅完整 | ✅准确 | ✅一致 | 教师端个人中心积分展示 |
| 反馈分类规则（11类） | 3.计算规则与公式-反馈分类规则 | ✅完整 | ✅准确 | ✅一致 | 文字类和非文字类分类处理 |
| 教师反馈场景 | 4.用户场景-教师发现课程问题并快速反馈 | ✅完整 | ✅准确 | ✅一致 | 包含完整操作步骤和Mermaid图表 |
| 学生反馈场景 | 4.用户场景-学生发现题目错误并反馈 | ✅完整 | ✅准确 | ✅一致 | 包含完整操作步骤和Mermaid图表 |
| 生产人员处理场景 | 4.用户场景-生产人员高效处理用户反馈 | ✅完整 | ✅准确 | ✅一致 | 包含后台处理流程和Mermaid图表 |
| 教师接收结果场景 | 4.用户场景-教师接收反馈处理结果 | ✅完整 | ✅准确 | ✅一致 | 包含消息通知流程和Mermaid图表 |
| 整体业务流程 | 5.业务流程图-整体反馈处理流程 | ✅完整 | ✅准确 | ✅一致 | 完整的端到端流程 |
| Agent工作流程 | 5.业务流程图-Agent工作流程 | ✅完整 | ✅准确 | ✅一致 | 三个dify工作流的处理逻辑 |
| Agent性能要求 | 6.1性能需求-Agent处理能力 | ✅完整 | ✅准确 | ✅一致 | 5秒内完成分析，80%准确率 |
| 多维表格功能 | 7.1功能验收-生产后台管理 | ✅完整 | ✅准确 | ✅一致 | 反馈列表、筛选、通知功能 |
| 飞书通知机制 | 2.2辅助功能-工作流自动化 | ✅完整 | ✅准确 | ✅一致 | 自动分发任务给对应负责人 |
| 积分下发规则 | 3.计算规则与公式-积分下发规则 | ⚠️部分完整 | ✅准确 | ✅一致 | 具体积分数量需要进一步确认 |

## 11. 附录

### 11.1 原型图
- 教师端反馈界面原型图（见原始文档中的UI截图）
- 学生端反馈界面原型图（见原始文档中的UI截图）
- 生产后台多维表格界面（见原始文档中的截图）

### 11.2 术语表
- **Agent**: 智能审核系统，用于自动分类和初筛用户反馈
- **多维表格**: 飞书多维表格，用于实现生产后台反馈管理功能
- **Dify工作流**: 用于实现Agent审核的具体工作流程
- **P0/P1/P2**: 功能优先级标识，P0为最高优先级

### 11.3 参考资料
- 原始PRD文档：Format-运营工具_老师&学生课程_题目反馈.md
- 多维表格技术文档：https://open.feishu.cn/document/server-docs/docs/bitable-v1/bitable-overview
- 飞书开放平台文档：https://open.feishu.cn/document/introduction-2#6b060ed7

---

**PRD内容填充与完善已完成，所有用户场景已配备Mermaid图表，质量检查通过**
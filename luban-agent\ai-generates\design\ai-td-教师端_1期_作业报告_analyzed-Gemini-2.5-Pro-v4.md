# 服务端架构设计文档：教师端作业报告 V1.0

**1. 引言 (Introduction)**

*   **1.1. 文档目的 (Purpose of Document)**
    *   为"教师端作业报告系统 V1.0"提供清晰的服务端架构蓝图，指导核心模块/服务的设计与开发，确保系统满足关键的业务需求和质量属性，支持高效运行和未来扩展。
*   **1.2. 系统概述 (System Overview)**
    *   本系统是为教师设计的作业报告分析工具，核心目标是帮助教师高效监控学生在各项作业任务中的学习进度、完成情况、答题表现（如正确率、错题分析），并基于系统策略辅助教师识别需要特别关注或鼓励的学生，以提升教学效率和反馈质量。系统将处理来自学生作答系统的数据，生成结构化的报告供教师查阅。
*   **1.3. 设计目标与核心原则 (Design Goals and Core Principles)**
    *   **设计目标:**
        *   **功能性:** 完整实现PRD定义的核心功能，包括作业列表、作业报告详情（学生报告、答题结果、学生提问）、单学生报告及相关策略应用。
        *   **易用性:** 提供清晰、准确的报告数据，方便教师快速获取所需信息。
        *   **可扩展性:** 架构应能支持未来功能迭代（如P1的提醒设置）和用户量增长。
        *   **可维护性:** 模块职责清晰，代码易于理解和修改。
        *   **性能:** 报告生成和查询响应迅速，满足教师日常使用需求。
    *   **核心原则:** (遵循 `s2-gen-ai-td.md` 指南)
        *   单一职责原则 (SRP)
        *   开闭原则 (OCP)
        *   里氏替换原则 (LSP)
        *   接口隔离原则 (ISP)
        *   依赖倒置原则 (DIP)
        *   迪米特法则 (LoD) / 最少知识原则
        *   高内聚低耦合
        *   KISS (Keep It Simple, Stupid)
        *   YAGNI (You Ain't Gonna Need It)
        *   关注点分离 (SoC)
        *   DRY (Don't Repeat Yourself)
*   **1.4. 范围 (Scope)**
    *   **覆盖范围:**
        *   服务端核心业务逻辑实现，包括作业数据的接入、处理、分析、存储和报告生成。
        *   支持PRD中定义的P0核心功能所需的服务端接口。
        *   服务间的交互和数据流设计。
    *   **不覆盖范围:**
        *   前端UI具体实现。
        *   详细的数据库表结构设计、索引优化。
        *   具体的第三方服务集成协议细节（如登录认证的具体对接方式）。
        *   服务部署、运维、监控的具体方案。
        *   非本期功能（P1）的详细设计。
*   **1.5. 术语与缩写 (Glossary and Abbreviations)**
    *   PRD: 产品需求文档 (Product Requirement Document)
    *   MVP: 最小可行产品 (Minimum Viable Product)
    *   API: 应用程序接口 (Application Programming Interface)
    *   SRP: 单一职责原则 (Single Responsibility Principle)
    *   P0: 最高优先级 (Priority 0)

**2. 需求提炼与架构驱动因素 (Key Requirements & Architectural Drivers)**

*   **2.1. 核心业务能力 (Core Business Capabilities)**
    *   **作业管理与查询:** 展示教师负责的作业列表，支持筛选和搜索。
    *   **作业报告生成:**
        *   **班级整体分析:** 计算班级平均进度、平均正确率等。
        *   **学生个体分析:** 展示学生个体进度、正确率，并根据偏离度进行提示。
        *   **题目答题分析:** 计算题目正确率、答错人数、作答人数，识别共性错题。
    *   **策略应用:** 根据预设规则识别"建议鼓励"和"建议关注"的学生。
    *   **学生提问汇总:** 展示学生针对作业提出的问题。
    *   **权限控制:** 确保教师只能查看其负责或有权限的作业数据。
*   **2.2. 关键质量属性 (Key Quality Attributes - Non-Functional Requirements)**
    *   **性能:** 报告查询和生成在可接受时间内完成（例如，大部分常用报告秒级响应）。数据计算量大的报告可考虑异步生成。
    *   **可用性:** 系统核心功能稳定可用。
    *   **可伸缩性:** 能够应对未来班级数、学生数、作业量的增长，服务可水平扩展。
    *   **可维护性:** 清晰的模块划分和接口定义，便于团队协作和后续功能迭代。
    *   **数据准确性:** 报告数据计算准确无误。
*   **2.3. 主要约束与依赖 (Key Constraints and Dependencies)**
    *   **数据源依赖:** 依赖学生作答系统、题目库系统、用户中心（教师、学生、班级信息）等提供原始数据。交互模式需明确。
    *   **计算规则依赖:** 部分计算规则（如班级平均进度/正确率、共性错题阈值、鼓励/关注策略细节）需在PRD中进一步明确或依赖外部配置。
    *   **技术栈:** 遵循团队现有技术栈或推荐选型（模板中提及Golang, k8s，此处侧重通用架构，具体技术选型在第4节讨论原则）。

**3. 宏观架构设计 (High-Level Architectural Design)**

*   **3.1. 架构风格与模式 (Architectural Style and Patterns)**
    *   **微服务架构:** 采纳微服务架构风格，将系统按业务能力拆分为一组独立部署、松耦合的服务。这有助于提高可维护性、可伸缩性，并允许各服务独立演进。
    *   **事件驱动模式 (部分场景):** 对于报告数据的更新、策略触发等场景，可考虑使用事件驱动模式，实现服务间的解耦和异步处理。
    *   **分层架构:** 在单个服务内部，遵循经典的分层架构（接口层、应用层、领域层、基础设施层）。
*   **3.2. 系统分层视图 (Layered View)**
    *   **3.2.1. 逻辑分层模型 (Logical Layering Model)**
        *   系统内各服务推荐采用如下逻辑分层模型，确保职责清晰和依赖单向：
        *   **图表示例:**
            ```mermaid
            graph TD
                A[外部调用方--教师端应用/其他系统] --> B{接口呈现层--API Layer};
                B --> C{应用服务层--Application Service Layer};
                C --> D{领域逻辑层--Domain Logic Layer};
                D --> E{基础设施层--Infrastructure Layer};
                E --> F[外部依赖--数据库, 消息队列, 依赖服务];

                subgraph LayerResponsibilities
                    direction LR
                    B_Desc["职责：- 处理外部HTTP/RPC请求- 数据格式校验与转换 DTO--VO- 用户认证与权限校验初步- 调用应用服务"]

                    C_Desc["职责：- 编排业务用例与流程- 协调领域对象和领域服务- 管理事务边界 可选-- 不包含具体业务规则"]

                    D_Desc["职责：- 包含核心业务规则和逻辑- 领域模型 实体--值对象--领域事件-- 领域服务- 纯粹的业务表达--无技术框架依赖"]

                    E_Desc["职责：- 提供技术能力支持- 数据持久化实现- 消息队列适配- 外部服务客户端实现- 实现领域层定义的接口 仓储接口等--"]
                end

                style B fill:#add8e6,stroke:#333,stroke-width:2px
                style C fill:#90ee90,stroke:#333,stroke-width:2px
                style D fill:#f0e68c,stroke:#333,stroke-width:2px
                style E fill:#f08080,stroke:#333,stroke-width:2px
            ```
        *   **图表说明**：
            *   **接口呈现层 (API Layer):** 负责处理外部请求（如来自教师端前端的HTTP请求），进行数据格式转换（例如，请求参数到内部DTO，内部实体到响应VO），执行初步的认证和授权检查，并将业务操作委托给应用服务层。
            *   **应用服务层 (Application Service Layer):** 负责编排具体的业务用例和流程。它协调领域对象和领域服务来完成用户请求，可以管理事务边界，但不包含具体的业务规则。
            *   **领域逻辑层 (Domain Logic Layer):** 包含系统的核心业务规则、领域模型（如实体、值对象、领域事件）和领域服务。这一层是纯粹的业务表达，不依赖于具体的技术实现框架。例如，作业报告的统计计算逻辑、鼓励/关注策略的判断逻辑等位于此层。
            *   **基础设施层 (Infrastructure Layer):** 提供技术能力支持，如数据持久化（通过仓储实现）、消息队列的发送与接收、对外部依赖服务（如用户中心、题目服务）的访问等。它负责实现领域层定义的接口（如仓储接口）。
*   **3.3. 模块/服务划分视图 (Module/Service Decomposition View)**
    *   **3.3.1. 核心模块/服务识别与职责 (Identification and Responsibilities)**
        *   **作业服务 (Assignment Service):**
            *   职责：管理作业元数据（来自外部作业系统），处理教师作业列表的查询、筛选、搜索请求。
            *   对外能力：提供作业列表查询接口，作业基本信息查询接口。
        *   **报告数据采集与预处理服务 (Report Data Aggregation Service):**
            *   职责：从学生作答系统、题目库等源头收集原始数据，进行清洗、转换和初步聚合，为报告生成做准备。可能采用异步/批量方式处理。
            *   对外能力：接收/拉取作答数据，提供预处理后的基础数据给报告分析服务。
        *   **报告分析服务 (Report Analysis Service):**
            *   职责：核心服务。根据作业ID、班级ID、学生ID等维度，执行各种统计计算（班级/学生正确率、完成率、题目统计等），应用PRD中定义的"鼓励"、"关注"、"共性错题"等策略。
            *   对外能力：提供各种维度报告数据的查询接口，执行策略分析。
        *   **学生提问服务 (Student Question Service):**
            *   职责：管理学生针对作业提出的问题，支持查询和展示。
            *   对外能力：提供按作业、按学生查询提问列表的接口。
        *   **用户与权限服务 (User & Permission Service Proxy/Adapter):**
            *   职责：对接已有的用户中心和权限系统，获取教师、学生、班级信息，校验教师对特定作业/班级数据的访问权限。自身不存储用户数据，主要作为代理或适配器。
            *   对外能力：提供用户信息查询、权限校验接口。
    *   **3.3.2. 模块/服务交互模式与数据契约 (Interaction Patterns and Data Contracts)**
        *   服务间交互优先采用轻量级同步调用（如内部RPC/HTTP）或异步消息机制（如Kafka, NATS）。
        *   **图表示例 (核心服务交互):**
            ```mermaid
            graph LR
                WebApp[教师端Web应用]

                subgraph "核心报告服务群"
                    AssignmentSvc[作业服务]
                    ReportAnalysisSvc[报告分析服务]
                    ReportDataAggSvc[报告数据采集与预处理服务]
                    StudentQuestionSvc[学生提问服务]
                end

                subgraph "支撑服务/外部依赖"
                    UserPermSvc[用户与权限服务代理]
                    StudentAnsweringSys[学生作答系统]
                    QuestionBankSys[题目库系统]
                    UserCenter[用户中心]
                end

                WebApp -- API请求 --> AssignmentSvc
                WebApp -- API请求 --> ReportAnalysisSvc
                WebApp -- API请求 --> StudentQuestionSvc

                AssignmentSvc -- 数据查询 --> ReportDataAggSvc
                ReportAnalysisSvc -- 依赖作业元数据 --> AssignmentSvc
                ReportAnalysisSvc -- 依赖预处理数据 --> ReportDataAggSvc
                ReportAnalysisSvc -- 依赖题目信息 --> QuestionBankSys
                ReportAnalysisSvc -- 依赖学生/班级信息 --> UserPermSvc

                ReportDataAggSvc -- 拉取/接收数据 --> StudentAnsweringSys
                ReportDataAggSvc -- 获取题目详情 --> QuestionBankSys

                UserPermSvc -- 同步用户信息 --> UserCenter
                UserPermSvc -- 同步权限信息 --> UserCenter

                %% Styling
                classDef coreSvc fill:#A6E22E,stroke:#333,color:black;
                classDef supportSvc fill:#66D9EF,stroke:#333,color:black;
                classDef externalSys fill:#FD971F,stroke:#333,color:black;

                class AssignmentSvc,ReportAnalysisSvc,ReportDataAggSvc,StudentQuestionSvc coreSvc;
                class UserPermSvc supportSvc;
                class StudentAnsweringSys,QuestionBankSys,UserCenter externalSys;
            ```
        *   **图表说明**：此图展示了核心服务及其主要依赖关系。教师端Web应用主要与作业服务、报告分析服务和学生提问服务交互。报告分析服务是核心，它依赖作业服务获取作业基本信息，依赖报告数据采集服务获取处理后的学生作答数据，并可能直接或间接依赖题目库和用户权限服务。数据采集服务负责从外部作答系统和题目库拉取原始数据。
*   **3.4. 关键业务流程的高层视图 (High-Level View of Key Business Flows)**
    *   **3.4.1. 教师查看作业报告详情流程 (序列图示例):**
        ```mermaid
        sequenceDiagram
            actor Teacher as 教师
            participant WebApp as 教师端Web应用
            participant AssignSvc as 作业服务
            participant AnalysisSvc as 报告分析服务
            participant UserPermSvc as 用户与权限服务代理
            participant DataAggSvc as 报告数据采集服务

            Teacher->>WebApp: 点击查看某作业报告 (作业ID)
            WebApp->>UserPermSvc: 校验教师权限 (教师ID--作业ID)
            UserPermSvc-->>WebApp: 权限校验结果
            alt 权限通过
                WebApp->>AssignSvc: 请求作业基本信息 (作业ID)
                AssignSvc-->>WebApp: 返回作业信息
                WebApp->>AnalysisSvc: 请求报告数据 (作业ID--班级ID等筛选条件)
                AnalysisSvc->>DataAggSvc: 获取已处理的作答数据 (作业ID--班级ID)
                DataAggSvc-->>AnalysisSvc: 返回作答数据
                AnalysisSvc->>AnalysisSvc: 计算统计指标--应用策略
                AnalysisSvc-->>WebApp: 返回报告数据 (学生列表--题目统计等)
                WebApp-->>Teacher: 展示作业报告详情页
            else 权限不足
                WebApp-->>Teacher:提示无权限查看
            end
        ```
    *   **3.4.2. 系统生成学生报告并应用鼓励/关注策略 (活动图示例):**
        *   (此流程更侧重数据处理，可能在`ReportAnalysisService`内部或由`ReportDataAggregationService`触发)
        ```mermaid
        stateDiagram-v2
            [*] --> 数据准备
            state 数据准备 {
                [*] --> 获取作业原始答题数据
                获取作业原始答题数据 --> 获取学生列表与班级信息
                获取学生列表与班级信息 --> 获取题目信息
                获取题目信息 --> [*]
            }
            数据准备 --> 数据计算与分析
            state 数据计算与分析 {
                [*] --> 计算学生个体正确率与进度
                计算学生个体正确率与进度 --> 计算班级平均正确率与进度
                计算班级平均正确率与进度 --> 计算题目维度统计数据
                计算题目维度统计数据 --> [*]
            }
            数据计算与分析 --> 策略应用
            state 策略应用 {
                [*] --> 应用'鼓励学生'策略
                应用'鼓励学生'策略 --> 应用'关注学生'策略
                应用'关注学生'策略 --> 应用'共性错题'识别策略
                应用'共性错题'识别策略 --> [*]
            }
            策略应用 --> 生成报告数据
            生成报告数据 --> 持久化报告结果
            持久化报告结果 --> [*]
        ```
*   **3.5. 数据架构概述 (Data Architecture Overview)**
    *   **3.5.1. 主要数据存储考量 (Primary Data Storage Considerations)**
        *   **关系型数据库 (如PostgreSQL, MySQL):** 用于存储作业元数据、学生提问、结构化的报告结果摘要（如班级统计、学生统计的快照，便于快速查询）、策略配置等需要事务保证和复杂查询的数据。
        *   **NoSQL数据库/搜索引擎 (如Elasticsearch, MongoDB):**
            *   可能用于存储原始或半结构化的学生作答明细（如果量巨大且查询模式灵活）。
            *   用于题目内容、解析的存储和搜索（如果题目库服务不直接提供此类查询）。
        *   **缓存 (如Redis):** 用于缓存热点作业的报告数据、用户会话、权限信息等，以提高访问速度。
    *   **3.5.2. 核心领域对象/数据结构的概念定义 (Conceptual Definition of Core Domain Objects/Data Structures)**
        *   **`作业 (Assignment)`:** 作业ID, 名称, 类型, 班级列表, 学科, 布置日期, 关联素材范围。
        *   **`作业报告 (AssignmentReport)`:** 报告ID, 关联作业ID, 班级ID, 生成时间, 班级平均完成率, 班级平均正确率, 共性错题列表。
        *   **`学生作业表现 (StudentAssignmentPerformance)`:** 关联作业ID, 学生ID, 完成状态, 提交时间, 正确率, 各题目作答详情, 是否建议鼓励, 是否建议关注。
        *   **`题目答题统计 (QuestionStats)`:** 关联作业ID, 题目ID, 正确率, 答错人数, 作答人数, 学生答案分布。
        *   **`学生提问 (StudentQuestion)`:** 提问ID, 关联作业ID, 学生ID, 提问内容, 提问时间, 回复状态。
    *   **3.5.3. 数据一致性与同步策略 (Data Consistency and Synchronization Strategies)**
        *   **原始数据采集:** `ReportDataAggregationService` 定期或通过事件触发从外部系统（学生作答系统、题目库）拉取数据。可采用增量同步机制。
        *   **报告数据生成:**
            *   对于可接受轻微延迟的报告，可异步生成，例如学生提交作业后，触发事件异步计算其个人报告和更新班级报告。
            *   对于需要实时性的查询（如教师点开报告），若数据量不大或已有预计算结果，则实时计算；否则可提示用户报告正在生成中，或展示最近一次的快照。
        *   **最终一致性:** 跨服务的业务操作，如更新作业状态并触发报告重新计算，可采用最终一致性模型，通过可靠消息队列保证操作的最终完成。
        *   **缓存更新:** 缓存数据需设定合理的过期策略，并在源数据变更时主动失效或更新缓存。

**4. 技术考量与选型原则 (Technical Considerations & Selection Principles)**

*   **4.1. 开发语言与环境**
    *   原则：选择团队熟悉、生态成熟、性能满足需求的语言。如模板建议的Golang，因其高并发处理能力、静态类型、优秀的标准库和部署效率，适合构建高性能服务端应用。
*   **4.2. 通信协议**
    *   **服务间通信:** 优先考虑基于IDL的RPC框架（如gRPC）实现强类型、高性能的内部调用。对于简单场景或需要广泛兼容性的，也可使用RESTful API。
    *   **对前端/客户端:** 通常使用RESTful API (JSON over HTTP/S)。
*   **4.3. 数据持久化**
    *   原则：根据数据特性（结构化程度、一致性要求、查询复杂度、数据量）选择合适的存储方案。避免单一数据库解决所有问题。
    *   推荐使用ORM或轻量级SQL构建器简化数据库操作，但需关注其性能开销和抽象泄漏。
*   **4.4. 异步处理与消息队列**
    *   原则：对于耗时操作、削峰填谷、服务解耦等场景，应采用异步处理和消息队列。
    *   选择消息队列时需考虑消息可靠性（投递保证）、吞吐量、延迟、社区支持和运维成本。
*   **4.5. 缓存策略**
    *   原则：对高频访问且变化不频繁的数据应引入缓存（本地缓存/分布式缓存）。
    *   需仔细设计缓存的粒度、过期策略、更新机制（如Cache-Aside, Read-Through, Write-Through）以及处理缓存穿透、雪崩等问题。
*   **4.6. 可观测性**
    *   原则：所有服务都应具备良好的可观测性，包括日志、指标（Metrics）和链路追踪（Tracing）。
    *   **日志:** 采用结构化日志，便于收集和分析。
    *   **指标:** 暴露关键业务指标和系统性能指标，接入监控系统（如Prometheus）。
    *   **追踪:** 集成链路追踪系统（如OpenTelemetry），便于排查分布式系统中的问题。

**5. 总结 (Conclusion)**

本文档为"教师端作业报告系统 V1.0"提供了服务端架构设计方案。通过采用微服务架构、清晰的分层模型和模块划分，旨在构建一个高内聚、低耦合、易于维护和扩展的系统。设计中强调了核心业务能力的实现、关键质量属性的保障以及遵循了既定的架构原则。后续的详细设计和开发应以此架构为指导，并根据实际情况持续演进。

--- 
等待您的命令，指挥官 
# 教师服务端

## 提供如下服务和能力

1. 查询个人信息，包括：教师基本信息（姓名、职务等）、任教学校、任教班级、任教科目、任教课程
2. 教师查询课表，包括：科目教师可以查询自己任教科目和班级的课表，班主任可以查询任教班级的全部课表
3. 教师课堂管理，包括：进入上课课堂、查询上课记录和上课详情、对课堂进行管理、课堂互动、课堂通知、课堂作业、课堂测试、课堂资源
4. 教师布置任务，包括：课堂任务、作业任务、测试任务、资源任务
5. 教师检查作业，包括：查询作业布置记录、查询班级作答信息、查询学生作业作答详情、批改作业

## 接口

### 统一规范

#### 请求规范

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 是 |登录凭证|
|organizationId|header|int| 是 |学校id|

#### 返回规范

```json
{
  "code": 0,            // 错误码，0 表示正常，非 0 表示异常
  "message": "string",  // 错误信息，code 非 0 时返回错误信息
  "data": {             // 完整的返回数据结构
    "anyKey": anyValue, // 业务数据
    "pageInfo": {       // 分页信息
      "page": 0,
      "pageSize": 0,
      "total": 0
    }
  }
}
```

#### 环境地址

* local: `http://teacher.local.xiaoluxue.cn`
* test: ``
* prod: ``

### GET 获取作业报告列表

GET /api/v1/task/report/list

获取教师布置的作业任务列表，按布置时间倒序返回

#### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|page|query|integer| 否 |默认 1|
|pageSize|query|integer| 否 |默认10，最大 100|
|keyword|query|string| 否 |检索关键字，模糊查询|
|startDate|query|string| 否 |任务查询范围开始日期，格式：2025-01-01|
|endDate|query|string| 否 |任务查询范围截止日期，格式：2025-01-01|
|taskType|query|integer| 否 |任务类型，0 所有，10课程，20作业，30测验，40资源|
|groupType|query|integer| 否 |0 全部，1 群组，2 班级，3 临时小组|
|groupId|query|integer| 否 |查询对象 id，group_type 为 1 时是群组 id，为 2 时是班级 id，临时小组没有 id|
|subject|query|integer| 否 |学科 id，1语文,2数学,3英语,4物理,5化学,6生物,7历史,8地理,9道德与法治；班主任可查看全部报告，学科教师只能查看任教科目报告|
|Authorization|header|string| 否 |none|
|organizationId|header|string| 否 |none|
|userTypeId|header|string| 否 |none|

#### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||0 表示正常，非 0 表示异常|
|» message|string|true|none||code 非 0 时返回错误信息|
|» data|object|true|none||完整的返回数据结构|
|»» tasks|[object]|true|none||布置的任务信息|
|»»» creatorId|integer|true|none||创建者 ID|
|»»» subject|integer|true|none||学科 id|
|»»» taskId|integer|true|none||任务 id|
|»»» taskName|string|true|none||任务名称|
|»»» taskType|integer|true|none||任务类型，10课程任务，20作业任务，30测试任务，40资源任务|
|»»» resources|[object]|true|none||任务资源列表|
|»»»» id|string|true|none||ID 编号，不同资源在不同服务维护，ID 可能重复，需要通过 type 区分|
|»»»» type|integer|true|none||资源类型，101AI课，102巩固练习，103试题，104试卷|
|»»»» name|string|true|none||名称|
|»»» reports|[object]|true|none||任务报告，单个任务可以布置给多个对象|
|»»»» assignId|integer|true|none||任务布置ID|
|»»»» assignObject|object|true|none||布置对象信息|
|»»»»» id|integer|true|none||布置对象ID|
|»»»»» type|integer|true|none||布置对象类型，班级、学生组、临时组等|
|»»»»» name|string|true|none||布置对象名称，如：高三 1 班|
|»»»» statData|object¦null|false|none||任务统计数据|
|»»»»» completionRate|number|false|none||完成进度，非资源类任务|
|»»»»» correctRate|number|false|none||正确率，非资源类任务|
|»»»»» needAttentionQuestionNum|integer|false|none||待关注题目数，非资源类任务|
|»»»»» averageProgress|number|false|none||平均进度，资源类任务|
|»»»»» classHours|integer|false|none||课时数，资源类任务|
|»»»»» startTime|integer|true|none||开始时间，时间戳秒|
|»»»»» deadline|integer|true|none||结束时间，时间戳秒|
|»» pageInfo|object|true|none||none|
|»»» page|integer|true|none||none|
|»»» pageSize|integer|true|none||none|
|»»» total|integer|true|none||none|

## SDK(如果有)

### 服务`go-SDK`使用方法

* 包名：`***********************:be-app/study-sdk.git`

```go
// 使用 demo
```

# 数据库设计文档 - 巩固练习相关策略 - V1.0

**最后更新日期**: 2024-08-03
**设计负责人**: Claude 3.7 Sonnet
**评审人**: 待填写
**状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期    | 修订人             | 修订内容摘要 |
| ------ | ----------- | ------------------ | ------------ |
| V1.0   | 2024-08-03  | Claude 3.7 Sonnet  | 初始草稿     |

## 数据库表清单

| 表名                                 | 核心功能/用途简述                                               | 预定数据库类型    |
| ------------------------------------ | --------------------------------------------------------------- | ----------------- |
| `exercise_sessions`                  | 存储用户的巩固练习会话信息，包括开始时间、结束时间、完成状态等   | PostgreSQL        |
| `exercise_answer_records`            | 记录用户在巩固练习中的每一次答题记录，包括答题时间、是否正确等   | PostgreSQL        |
| `exercise_user_states`               | 维护用户在练习过程中的状态，包括专注度、连续答对/错数等          | PostgreSQL        |
| `exercise_user_knowledge_masteries`  | 存储用户对各知识点的掌握度数据                                  | PostgreSQL        |
| `tbl_exercise_stats`                 | 存储练习统计数据，如每日完成量、正确率等指标                     | ClickHouse        |
| `tbl_attentiveness_events`           | 记录专注度相关事件，用于分析用户行为模式                         | ClickHouse        |
| `tbl_recommendation_logs`            | 记录推题策略的执行日志，包括推荐结果和依据                       | ClickHouse        |
| `exercise_strategy_configs`          | 存储推题策略相关配置参数                                         | PostgreSQL        |

## 1. 项目概述与设计目标

### 1.1 项目背景与范围

本项目是巩固练习相关策略服务的数据库设计，旨在支持AI课程学习流程中的"巩固练习"环节，为学生提供智能推题能力。系统需要根据学生能力水平和题目难度，推荐最适合学生当前学习阶段的题目，帮助学生有效地掌握课程重点知识点。

本设计覆盖以下核心功能的数据存储需求：
* 巩固练习题目推荐策略
* 再练一次题目推荐策略
* 不认真答题判断和专注度评估
* 掌握度计算与更新
* 熔断策略执行记录

### 1.2 设计目标回顾

本数据库设计旨在达成以下核心目标：
* 提供完整的、符合规范的表创建语句（DDL），确保数据结构准确表达业务需求
* 清晰描述表间关系，并提供ER图，便于理解整体数据模型
* 确保数据模型支持巩固练习推题、再练一次、专注度评估等核心用户场景
* 设计合适的索引策略，支持高频查询和保证查询性能，满足低延迟（核心推题接口P99延迟<100ms）的性能要求
* 进行数据量预估和增长趋势分析，确保系统支持未来2年用户量增长5倍，峰值QPS达5k的需求
* 优化存储策略，合理设计分析型数据和事务型数据的存储方案
* 确保数据模型的可扩展性，便于后续添加新的推题策略

## 2. 数据库环境与总体说明

### 2.1 目标数据库类型

* 操作型数据（OLTP）: PostgreSQL 15.x
* 分析型数据（OLAP）: ClickHouse 23.x

### 2.2 数据库创建语句 (如果适用)

本设计使用已存在的数据库实例，PostgreSQL数据库名称为`db_exercise_strategy`，ClickHouse数据库名称为`db_exercise_logs`。

### 2.3 数据库特定设计要点回顾

**PostgreSQL设计要点**:
* 按照规范使用`snake_case`命名方式，模块名作为表名前缀
* 所有表都设计有标准字段，包括：`id`, `created_at`, `updated_at`, `deleted_at`, `status`, `created_by`, `updated_by`
* 严格禁止在数据库层面使用外键约束(FOREIGN KEY)，表间关联通过应用层维护
* 为高频查询和索引字段添加适当的索引
* 所有表和字段均添加详细的中文注释

**ClickHouse设计要点**:
* 表名使用`tbl_`前缀，使用小写字母和下划线
* 适当使用LowCardinality类型优化低基数字段
* 根据查询模式合理设计分区键和排序键
* 合理利用数据编码和压缩提高存储和查询效率
* 采用适当的存储引擎，核心分析表采用MergeTree族引擎
* 设置合理的TTL策略管理历史数据

## 3. 数据库表详细设计

### 3.1 表名: exercise_sessions

#### 3.1.1 表功能说明

本表用于存储用户的巩固练习会话信息，记录每次练习的基本信息，包括练习类型（巩固练习/再练一次）、开始和结束时间、练习状态、完成情况等。作为练习流程的主表，它与答题记录和用户状态表关联，用于跟踪和管理整个练习过程。

#### 3.1.2 表结构定义 (DDL)

```sql
CREATE TABLE exercise_sessions (
  -- 主键
  id BIGSERIAL PRIMARY KEY,
  
  -- 业务字段
  user_id BIGINT NOT NULL,                -- 用户ID
  knowledge_point_id BIGINT NOT NULL,     -- 知识点ID
  exercise_type SMALLINT NOT NULL DEFAULT 1,  -- 练习类型: 1-巩固练习 2-再练一次
  start_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 开始时间
  end_time TIMESTAMPTZ NULL,              -- 结束时间
  predicted_question_count SMALLINT NOT NULL,  -- 预估题目数量
  actual_question_count SMALLINT NOT NULL DEFAULT 0,  -- 实际答题数量
  completed_question_count SMALLINT NOT NULL DEFAULT 0, -- 完成题目数量
  correct_count SMALLINT NOT NULL DEFAULT 0,   -- 正确题目数量
  incorrect_count SMALLINT NOT NULL DEFAULT 0, -- 错误题目数量 
  completion_status SMALLINT NOT NULL DEFAULT 0,  -- 完成状态: 0-进行中 1-已完成 2-已熔断 3-用户中断
  attentiveness_score SMALLINT NOT NULL DEFAULT 10, -- 专注度分值，默认10分
  mastery_before NUMERIC(4,3) NULL,       -- 练习前掌握度
  mastery_after NUMERIC(4,3) NULL,        -- 练习后掌握度
  is_recommended_to_retry BOOLEAN NOT NULL DEFAULT FALSE, -- 是否推荐再练一次
  breaker_reason SMALLINT NULL,           -- 熔断原因: 1-连续错误 2-专注度过低 3-没有合适题目
  
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,     -- 状态: 0-禁用 1-启用
  lang VARCHAR(10) NOT NULL DEFAULT 'zh-CN', -- 语言
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL, -- 删除时间（软删除）
  created_by BIGINT NOT NULL,             -- 创建人
  updated_by BIGINT NOT NULL              -- 更新人
);

-- 添加表和字段注释
COMMENT ON TABLE exercise_sessions IS '巩固练习会话表 - 存储用户练习会话的基本信息';
COMMENT ON COLUMN exercise_sessions.id IS '主键ID';
COMMENT ON COLUMN exercise_sessions.user_id IS '用户ID - 外部用户服务的用户ID';
COMMENT ON COLUMN exercise_sessions.knowledge_point_id IS '知识点ID - 外部内容平台的知识点ID';
COMMENT ON COLUMN exercise_sessions.exercise_type IS '练习类型: 1-巩固练习 2-再练一次';
COMMENT ON COLUMN exercise_sessions.start_time IS '开始时间 - 练习开始的时间';
COMMENT ON COLUMN exercise_sessions.end_time IS '结束时间 - 练习结束的时间，NULL表示尚未结束';
COMMENT ON COLUMN exercise_sessions.predicted_question_count IS '预估题目数量 - 系统计算的预计答题量';
COMMENT ON COLUMN exercise_sessions.actual_question_count IS '实际答题数量 - 用户实际作答的题目数量';
COMMENT ON COLUMN exercise_sessions.completed_question_count IS '完成题目数量 - 用户已完成的题目数量';
COMMENT ON COLUMN exercise_sessions.correct_count IS '正确题目数量 - 答对的题目数量';
COMMENT ON COLUMN exercise_sessions.incorrect_count IS '错误题目数量 - 答错的题目数量';
COMMENT ON COLUMN exercise_sessions.completion_status IS '完成状态: 0-进行中 1-已完成 2-已熔断 3-用户中断';
COMMENT ON COLUMN exercise_sessions.attentiveness_score IS '专注度分值 - 用户当前练习的专注度评分，初始值10分';
COMMENT ON COLUMN exercise_sessions.mastery_before IS '练习前掌握度 - 开始练习时的知识点掌握度';
COMMENT ON COLUMN exercise_sessions.mastery_after IS '练习后掌握度 - 完成练习后的知识点掌握度';
COMMENT ON COLUMN exercise_sessions.is_recommended_to_retry IS '是否推荐再练一次 - 是否满足再练一次条件';
COMMENT ON COLUMN exercise_sessions.breaker_reason IS '熔断原因: 1-连续错误 2-专注度过低 3-没有合适题目，NULL表示未熔断';
COMMENT ON COLUMN exercise_sessions.status IS '状态: 0-禁用 1-启用';
COMMENT ON COLUMN exercise_sessions.lang IS '语言 - 默认中文';
COMMENT ON COLUMN exercise_sessions.created_at IS '创建时间';
COMMENT ON COLUMN exercise_sessions.updated_at IS '更新时间';
COMMENT ON COLUMN exercise_sessions.deleted_at IS '删除时间 - NULL表示未删除';
COMMENT ON COLUMN exercise_sessions.created_by IS '创建人ID';
COMMENT ON COLUMN exercise_sessions.updated_by IS '更新人ID';

-- 添加索引
CREATE INDEX idx_exercise_sessions_user_id ON exercise_sessions(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_sessions_knowledge_point_id ON exercise_sessions(knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_sessions_user_knowledge ON exercise_sessions(user_id, knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_sessions_start_time ON exercise_sessions(start_time) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_sessions_completion_status ON exercise_sessions(completion_status) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_sessions_exercise_type ON exercise_sessions(exercise_type) WHERE deleted_at IS NULL;
```

### 3.2 表名: exercise_answer_records

#### 3.2.1 表功能说明

本表用于记录用户在巩固练习和再练一次过程中的每一次答题记录，包括用户ID、会话ID、题目信息、用户作答信息、答题时长、是否正确等详细信息。是评估用户掌握度和专注度的核心数据来源，同时也用于再练一次功能中的错题推荐。

#### 3.2.2 表结构定义 (DDL)

```sql
CREATE TABLE exercise_answer_records (
  -- 主键
  id BIGSERIAL PRIMARY KEY,
  
  -- 业务字段
  session_id BIGINT NOT NULL,              -- 练习会话ID
  user_id BIGINT NOT NULL,                 -- 用户ID
  knowledge_point_id BIGINT NOT NULL,      -- 知识点ID
  question_id BIGINT NOT NULL,             -- 题目ID
  question_group_id BIGINT NOT NULL,       -- 题组ID
  question_difficulty NUMERIC(3,1) NOT NULL, -- 题目难度系数
  is_must_do BOOLEAN NOT NULL DEFAULT FALSE, -- 是否必做题
  expected_mastery_increment NUMERIC(5,4) NOT NULL, -- 预期掌握度增量
  answer_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 作答时间
  answer_duration_seconds INT NOT NULL,    -- 答题时长（秒）
  answer_content TEXT NULL,                -- 作答内容
  is_correct BOOLEAN NOT NULL,             -- 是否正确
  mastery_before NUMERIC(4,3) NOT NULL,    -- 作答前掌握度
  mastery_after NUMERIC(4,3) NOT NULL,     -- 作答后掌握度
  mastery_increment NUMERIC(5,4) NOT NULL, -- 实际掌握度增量
  attentiveness_change SMALLINT NOT NULL DEFAULT 0, -- 专注度变化: 0-无变化 -1-减少1分
  attentiveness_trigger SMALLINT NULL,     -- 专注度触发原因: 1-快速作答且错误 2-连续做错题 3-做错简单题
  consecutive_correct INT NOT NULL DEFAULT 0, -- 当前连续答对题数
  consecutive_incorrect INT NOT NULL DEFAULT 0, -- 当前连续答错题数
  
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,      -- 状态: 0-禁用 1-启用
  lang VARCHAR(10) NOT NULL DEFAULT 'zh-CN', -- 语言
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL, -- 删除时间（软删除）
  created_by BIGINT NOT NULL,              -- 创建人
  updated_by BIGINT NOT NULL               -- 更新人
);

-- 添加表和字段注释
COMMENT ON TABLE exercise_answer_records IS '练习答题记录表 - 存储用户在练习过程中的答题记录';
COMMENT ON COLUMN exercise_answer_records.id IS '主键ID';
COMMENT ON COLUMN exercise_answer_records.session_id IS '练习会话ID - 关联exercise_sessions表';
COMMENT ON COLUMN exercise_answer_records.user_id IS '用户ID - 外部用户服务的用户ID';
COMMENT ON COLUMN exercise_answer_records.knowledge_point_id IS '知识点ID - 外部内容平台的知识点ID';
COMMENT ON COLUMN exercise_answer_records.question_id IS '题目ID - 外部内容平台的题目ID';
COMMENT ON COLUMN exercise_answer_records.question_group_id IS '题组ID - 外部内容平台的题组ID';
COMMENT ON COLUMN exercise_answer_records.question_difficulty IS '题目难度系数 - 取值范围0.2-1.0';
COMMENT ON COLUMN exercise_answer_records.is_must_do IS '是否必做题 - 标记是否为老师指定的必做题';
COMMENT ON COLUMN exercise_answer_records.expected_mastery_increment IS '预期掌握度增量 - 推题时计算的预期掌握度增量';
COMMENT ON COLUMN exercise_answer_records.answer_time IS '作答时间 - 用户提交答案的时间';
COMMENT ON COLUMN exercise_answer_records.answer_duration_seconds IS '答题时长 - 用户答题所用的时间，单位秒';
COMMENT ON COLUMN exercise_answer_records.answer_content IS '作答内容 - 用户的答案内容';
COMMENT ON COLUMN exercise_answer_records.is_correct IS '是否正确 - 用户答案是否正确';
COMMENT ON COLUMN exercise_answer_records.mastery_before IS '作答前掌握度 - 作答前用户对该知识点的掌握度';
COMMENT ON COLUMN exercise_answer_records.mastery_after IS '作答后掌握度 - 作答后用户对该知识点的掌握度';
COMMENT ON COLUMN exercise_answer_records.mastery_increment IS '实际掌握度增量 - 答题后实际增加的掌握度';
COMMENT ON COLUMN exercise_answer_records.attentiveness_change IS '专注度变化: 0-无变化 -1-减少1分';
COMMENT ON COLUMN exercise_answer_records.attentiveness_trigger IS '专注度触发原因: 1-快速作答且错误 2-连续做错题 3-做错简单题，NULL表示未触发';
COMMENT ON COLUMN exercise_answer_records.consecutive_correct IS '当前连续答对题数 - 截止到当前题目的连续答对数量';
COMMENT ON COLUMN exercise_answer_records.consecutive_incorrect IS '当前连续答错题数 - 截止到当前题目的连续答错数量';
COMMENT ON COLUMN exercise_answer_records.status IS '状态: 0-禁用 1-启用';
COMMENT ON COLUMN exercise_answer_records.lang IS '语言 - 默认中文';
COMMENT ON COLUMN exercise_answer_records.created_at IS '创建时间';
COMMENT ON COLUMN exercise_answer_records.updated_at IS '更新时间';
COMMENT ON COLUMN exercise_answer_records.deleted_at IS '删除时间 - NULL表示未删除';
COMMENT ON COLUMN exercise_answer_records.created_by IS '创建人ID';
COMMENT ON COLUMN exercise_answer_records.updated_by IS '更新人ID';

-- 添加索引
CREATE INDEX idx_exercise_answer_records_session_id ON exercise_answer_records(session_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_answer_records_user_id ON exercise_answer_records(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_answer_records_question_id ON exercise_answer_records(question_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_answer_records_question_group_id ON exercise_answer_records(question_group_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_answer_records_knowledge_point_id ON exercise_answer_records(knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_answer_records_user_knowledge ON exercise_answer_records(user_id, knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_answer_records_is_correct ON exercise_answer_records(is_correct) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_answer_records_answer_time ON exercise_answer_records(answer_time) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_answer_records_user_question ON exercise_answer_records(user_id, question_id) WHERE deleted_at IS NULL;
```

### 3.3 表名: exercise_user_states

#### 3.3.1 表功能说明

本表用于维护用户在练习过程中的状态数据，包括当前专注度、连续答对/答错数、当前学习状态等。该表记录用户在特定知识点练习中的实时状态，用于推题策略的决策和专注度评估。表中的数据在练习过程中实时更新，练习结束后可用于分析用户行为模式。

#### 3.3.2 表结构定义 (DDL)

```sql
CREATE TABLE exercise_user_states (
  -- 主键
  id BIGSERIAL PRIMARY KEY,
  
  -- 业务字段
  user_id BIGINT NOT NULL,                 -- 用户ID
  knowledge_point_id BIGINT NOT NULL,      -- 知识点ID
  session_id BIGINT NULL,                  -- 当前练习会话ID，NULL表示不在练习中
  current_mastery NUMERIC(4,3) NOT NULL,   -- 当前掌握度
  target_mastery NUMERIC(4,3) NOT NULL,    -- 目标掌握度
  attentiveness_score SMALLINT NOT NULL DEFAULT 10, -- 当前专注度分值
  consecutive_correct INT NOT NULL DEFAULT 0, -- 当前连续答对题数
  consecutive_incorrect INT NOT NULL DEFAULT 0, -- 当前连续答错题数
  last_question_id BIGINT NULL,            -- 上一道题目ID
  last_question_difficulty NUMERIC(3,1) NULL, -- 上一道题目难度系数
  last_answer_correct BOOLEAN NULL,        -- 上一道题目是否答对
  total_exercise_count INT NOT NULL DEFAULT 0, -- 总练习次数
  total_question_count INT NOT NULL DEFAULT 0, -- 总答题数量
  total_correct_count INT NOT NULL DEFAULT 0,  -- 总答对数量
  current_learning_state SMALLINT NOT NULL DEFAULT 0, -- 当前学习状态: 0-未学习 1-学习中 2-已掌握 3-需巩固
  last_exercise_time TIMESTAMPTZ NULL,     -- 上次练习时间
  
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,      -- 状态: 0-禁用 1-启用
  lang VARCHAR(10) NOT NULL DEFAULT 'zh-CN', -- 语言
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL, -- 删除时间（软删除）
  created_by BIGINT NOT NULL,              -- 创建人
  updated_by BIGINT NOT NULL               -- 更新人
);

-- 添加表和字段注释
COMMENT ON TABLE exercise_user_states IS '用户练习状态表 - 存储用户在练习过程中的状态数据';
COMMENT ON COLUMN exercise_user_states.id IS '主键ID';
COMMENT ON COLUMN exercise_user_states.user_id IS '用户ID - 外部用户服务的用户ID';
COMMENT ON COLUMN exercise_user_states.knowledge_point_id IS '知识点ID - 外部内容平台的知识点ID';
COMMENT ON COLUMN exercise_user_states.session_id IS '当前练习会话ID - 关联exercise_sessions表，NULL表示不在练习中';
COMMENT ON COLUMN exercise_user_states.current_mastery IS '当前掌握度 - 用户对该知识点的当前掌握程度，取值0-1';
COMMENT ON COLUMN exercise_user_states.target_mastery IS '目标掌握度 - 该知识点的目标掌握程度，取值0-1';
COMMENT ON COLUMN exercise_user_states.attentiveness_score IS '当前专注度分值 - 用户当前练习的专注度评分，取值0-10';
COMMENT ON COLUMN exercise_user_states.consecutive_correct IS '当前连续答对题数 - 当前练习中连续答对的题目数量';
COMMENT ON COLUMN exercise_user_states.consecutive_incorrect IS '当前连续答错题数 - 当前练习中连续答错的题目数量';
COMMENT ON COLUMN exercise_user_states.last_question_id IS '上一道题目ID - 最近回答的题目ID';
COMMENT ON COLUMN exercise_user_states.last_question_difficulty IS '上一道题目难度系数 - 最近回答题目的难度，取值0.2-1.0';
COMMENT ON COLUMN exercise_user_states.last_answer_correct IS '上一道题目是否答对 - 最近回答题目的结果';
COMMENT ON COLUMN exercise_user_states.total_exercise_count IS '总练习次数 - 用户在该知识点上的练习次数';
COMMENT ON COLUMN exercise_user_states.total_question_count IS '总答题数量 - 用户在该知识点上的总答题数量';
COMMENT ON COLUMN exercise_user_states.total_correct_count IS '总答对数量 - 用户在该知识点上的总答对数量';
COMMENT ON COLUMN exercise_user_states.current_learning_state IS '当前学习状态: 0-未学习 1-学习中 2-已掌握 3-需巩固';
COMMENT ON COLUMN exercise_user_states.last_exercise_time IS '上次练习时间 - 用户上次对该知识点进行练习的时间';
COMMENT ON COLUMN exercise_user_states.status IS '状态: 0-禁用 1-启用';
COMMENT ON COLUMN exercise_user_states.lang IS '语言 - 默认中文';
COMMENT ON COLUMN exercise_user_states.created_at IS '创建时间';
COMMENT ON COLUMN exercise_user_states.updated_at IS '更新时间';
COMMENT ON COLUMN exercise_user_states.deleted_at IS '删除时间 - NULL表示未删除';
COMMENT ON COLUMN exercise_user_states.created_by IS '创建人ID';
COMMENT ON COLUMN exercise_user_states.updated_by IS '更新人ID';

-- 添加索引
CREATE UNIQUE INDEX uk_exercise_user_states_user_knowledge ON exercise_user_states(user_id, knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_user_states_session_id ON exercise_user_states(session_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_user_states_current_mastery ON exercise_user_states(current_mastery) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_user_states_learning_state ON exercise_user_states(current_learning_state) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_user_states_last_exercise_time ON exercise_user_states(last_exercise_time) WHERE deleted_at IS NULL;
```

### 3.4 表名: exercise_user_knowledge_masteries

#### 3.4.1 表功能说明

本表用于存储用户对各知识点的掌握度历史数据，记录用户掌握度随时间的变化情况。该表与exercise_user_states表不同，exercise_user_states记录当前最新的掌握度数据，而本表记录掌握度的历史变更，便于后续分析用户的学习轨迹和进步情况。同时也可作为掌握度数据的备份，防止数据丢失。

#### 3.4.2 表结构定义 (DDL)

```sql
CREATE TABLE exercise_user_knowledge_masteries (
  -- 主键
  id BIGSERIAL PRIMARY KEY,
  
  -- 业务字段
  user_id BIGINT NOT NULL,                 -- 用户ID
  knowledge_point_id BIGINT NOT NULL,      -- 知识点ID
  mastery_value NUMERIC(4,3) NOT NULL,     -- 掌握度值，取值0-1
  update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  update_reason SMALLINT NOT NULL,         -- 更新原因: 1-练习结束 2-教师评估 3-系统校准 4-题目作答
  related_session_id BIGINT NULL,          -- 相关练习会话ID
  related_question_id BIGINT NULL,         -- 相关题目ID
  mastery_delta NUMERIC(5,4) NOT NULL,     -- 掌握度变化量
  previous_mastery NUMERIC(4,3) NOT NULL,  -- 更新前掌握度
  target_mastery NUMERIC(4,3) NOT NULL,    -- 目标掌握度
  
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,      -- 状态: 0-禁用 1-启用
  lang VARCHAR(10) NOT NULL DEFAULT 'zh-CN', -- 语言
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL, -- 删除时间（软删除）
  created_by BIGINT NOT NULL,              -- 创建人
  updated_by BIGINT NOT NULL               -- 更新人
);

-- 添加表和字段注释
COMMENT ON TABLE exercise_user_knowledge_masteries IS '用户知识点掌握度历史表 - 记录用户掌握度的变更历史';
COMMENT ON COLUMN exercise_user_knowledge_masteries.id IS '主键ID';
COMMENT ON COLUMN exercise_user_knowledge_masteries.user_id IS '用户ID - 外部用户服务的用户ID';
COMMENT ON COLUMN exercise_user_knowledge_masteries.knowledge_point_id IS '知识点ID - 外部内容平台的知识点ID';
COMMENT ON COLUMN exercise_user_knowledge_masteries.mastery_value IS '掌握度值 - 表示用户对该知识点的掌握程度，取值0-1';
COMMENT ON COLUMN exercise_user_knowledge_masteries.update_time IS '更新时间 - 掌握度更新的时间';
COMMENT ON COLUMN exercise_user_knowledge_masteries.update_reason IS '更新原因: 1-练习结束 2-教师评估 3-系统校准 4-题目作答';
COMMENT ON COLUMN exercise_user_knowledge_masteries.related_session_id IS '相关练习会话ID - 导致掌握度变更的练习会话ID，如有';
COMMENT ON COLUMN exercise_user_knowledge_masteries.related_question_id IS '相关题目ID - 导致掌握度变更的题目ID，如有';
COMMENT ON COLUMN exercise_user_knowledge_masteries.mastery_delta IS '掌握度变化量 - 本次掌握度变更的增量或减量';
COMMENT ON COLUMN exercise_user_knowledge_masteries.previous_mastery IS '更新前掌握度 - 变更前的掌握度值';
COMMENT ON COLUMN exercise_user_knowledge_masteries.target_mastery IS '目标掌握度 - 该知识点的目标掌握程度';
COMMENT ON COLUMN exercise_user_knowledge_masteries.status IS '状态: 0-禁用 1-启用';
COMMENT ON COLUMN exercise_user_knowledge_masteries.lang IS '语言 - 默认中文';
COMMENT ON COLUMN exercise_user_knowledge_masteries.created_at IS '创建时间';
COMMENT ON COLUMN exercise_user_knowledge_masteries.updated_at IS '更新时间';
COMMENT ON COLUMN exercise_user_knowledge_masteries.deleted_at IS '删除时间 - NULL表示未删除';
COMMENT ON COLUMN exercise_user_knowledge_masteries.created_by IS '创建人ID';
COMMENT ON COLUMN exercise_user_knowledge_masteries.updated_by IS '更新人ID';

-- 添加索引
CREATE INDEX idx_exercise_user_knowledge_masteries_user_id ON exercise_user_knowledge_masteries(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_user_knowledge_masteries_knowledge_point_id ON exercise_user_knowledge_masteries(knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_user_knowledge_masteries_user_knowledge ON exercise_user_knowledge_masteries(user_id, knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_user_knowledge_masteries_update_time ON exercise_user_knowledge_masteries(update_time) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_user_knowledge_masteries_related_session_id ON exercise_user_knowledge_masteries(related_session_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_user_knowledge_masteries_update_reason ON exercise_user_knowledge_masteries(update_reason) WHERE deleted_at IS NULL;
```

### 3.5 表名: tbl_exercise_stats

#### 3.5.1 表功能说明

本表为ClickHouse分析表，用于存储练习相关的统计数据，支持数据分析和报表生成。该表按天汇总用户练习数据，包括练习次数、题目数量、正确率、掌握度变化等关键指标，用于分析练习效果、用户参与度和学习效率。数据可按多维度（用户、知识点、时间等）聚合查询，支持各类分析和报表需求。

#### 3.5.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_exercise_stats
(
    -- 业务字段
    stat_date Date,                        -- 统计日期
    user_id UInt64,                        -- 用户ID
    knowledge_point_id UInt64,             -- 知识点ID
    session_count UInt32,                  -- 练习会话数量
    question_count UInt32,                 -- 答题数量
    correct_count UInt32,                  -- 正确题目数量
    incorrect_count UInt32,                -- 错误题目数量
    total_duration_seconds UInt32,         -- 总答题时长(秒)
    avg_duration_seconds Float32,          -- 平均每题答题时长(秒)
    mastery_start Float32,                 -- 日初始掌握度
    mastery_end Float32,                   -- 日结束掌握度
    mastery_delta Float32,                 -- 日掌握度变化
    completion_rate Float32,               -- 练习完成率
    attentiveness_avg Float32,             -- 平均专注度
    breaker_count UInt16,                  -- 熔断次数
    retry_count UInt16,                    -- 再练一次次数
    is_knowledge_mastered UInt8,           -- 是否已掌握知识点

    -- 维度字段
    exercise_type LowCardinality(String),  -- 练习类型
    knowledge_point_difficulty Float32,    -- 知识点难度系数
    knowledge_point_name LowCardinality(String), -- 知识点名称

    -- 创建时间
    created_at DateTime DEFAULT now()      -- 记录创建时间
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(stat_date)
ORDER BY (stat_date, user_id, knowledge_point_id)
TTL stat_date + INTERVAL 1 YEAR DELETE
SETTINGS index_granularity = 8192;

-- 添加表和列的注释
COMMENT ON TABLE tbl_exercise_stats IS '练习统计数据表 - 存储练习相关的每日统计指标';
-- 业务字段注释
COMMENT ON COLUMN tbl_exercise_stats.stat_date IS '统计日期 - 数据所属日期';
COMMENT ON COLUMN tbl_exercise_stats.user_id IS '用户ID - 外部用户服务的用户ID';
COMMENT ON COLUMN tbl_exercise_stats.knowledge_point_id IS '知识点ID - 外部内容平台的知识点ID';
COMMENT ON COLUMN tbl_exercise_stats.session_count IS '练习会话数量 - 该日该用户在该知识点上的练习会话数量';
COMMENT ON COLUMN tbl_exercise_stats.question_count IS '答题数量 - 该日该用户在该知识点上的答题总数';
COMMENT ON COLUMN tbl_exercise_stats.correct_count IS '正确题目数量 - 该日该用户在该知识点上答对的题目数量';
COMMENT ON COLUMN tbl_exercise_stats.incorrect_count IS '错误题目数量 - 该日该用户在该知识点上答错的题目数量';
COMMENT ON COLUMN tbl_exercise_stats.total_duration_seconds IS '总答题时长 - 该日该用户在该知识点上的总答题时长，单位秒';
COMMENT ON COLUMN tbl_exercise_stats.avg_duration_seconds IS '平均每题答题时长 - 该日该用户在该知识点上的平均答题时长，单位秒';
COMMENT ON COLUMN tbl_exercise_stats.mastery_start IS '日初始掌握度 - 该日该用户在该知识点上的初始掌握度';
COMMENT ON COLUMN tbl_exercise_stats.mastery_end IS '日结束掌握度 - 该日该用户在该知识点上的最终掌握度';
COMMENT ON COLUMN tbl_exercise_stats.mastery_delta IS '日掌握度变化 - 该日该用户在该知识点上的掌握度变化量';
COMMENT ON COLUMN tbl_exercise_stats.completion_rate IS '练习完成率 - 该日该用户在该知识点上的练习完成率';
COMMENT ON COLUMN tbl_exercise_stats.attentiveness_avg IS '平均专注度 - 该日该用户在该知识点上的平均专注度评分';
COMMENT ON COLUMN tbl_exercise_stats.breaker_count IS '熔断次数 - 该日该用户在该知识点上触发练习熔断的次数';
COMMENT ON COLUMN tbl_exercise_stats.retry_count IS '再练一次次数 - 该日该用户在该知识点上进行再练一次的次数';
COMMENT ON COLUMN tbl_exercise_stats.is_knowledge_mastered IS '是否已掌握知识点 - 表示到该日结束时，用户是否已掌握该知识点';
-- 维度字段注释
COMMENT ON COLUMN tbl_exercise_stats.exercise_type IS '练习类型 - 练习的类型，如"巩固练习"、"再练一次"';
COMMENT ON COLUMN tbl_exercise_stats.knowledge_point_difficulty IS '知识点难度系数 - 该知识点的难度系数';
COMMENT ON COLUMN tbl_exercise_stats.knowledge_point_name IS '知识点名称 - 该知识点的名称';
-- 创建时间注释
COMMENT ON COLUMN tbl_exercise_stats.created_at IS '记录创建时间 - 该统计记录的创建时间';
```

### 3.6 表名: tbl_attentiveness_events

#### 3.6.1 表功能说明

本表为ClickHouse分析表，用于记录专注度相关事件的详细信息，支持专注度评估算法的优化和调整。该表存储所有导致专注度变化的事件，包括事件类型、触发原因、专注度变化前后值等信息。通过分析这些数据，可以评估专注度算法的有效性，找出影响用户专注度的关键因素，为算法优化提供数据支持。

#### 3.6.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_attentiveness_events
(
    -- 事件基础信息
    event_time DateTime64(3),              -- 事件时间，毫秒精度
    event_date Date MATERIALIZED toDate(event_time), -- 事件日期
    user_id UInt64,                        -- 用户ID
    session_id UInt64,                     -- 练习会话ID
    knowledge_point_id UInt64,             -- 知识点ID
    question_id UInt64,                    -- 相关题目ID
    question_group_id UInt64,              -- 题组ID
    
    -- 专注度事件信息
    event_type LowCardinality(String),     -- 事件类型: fast_answer, consecutive_wrong, simple_wrong, reminder, circuit_breaker
    event_trigger_id UInt8,                -- 事件触发ID: 1-快速作答且错误 2-连续做错题 3-做错简单题 4-专注度提醒 5-熔断触发
    attentiveness_before UInt8,            -- 事件前专注度
    attentiveness_after UInt8,             -- 事件后专注度
    attentiveness_change Int8,             -- 专注度变化量
    
    -- 事件上下文
    answer_duration_seconds UInt16,        -- 答题时长(秒)
    is_answer_correct UInt8,               -- 答案是否正确
    consecutive_incorrect UInt8,           -- 当前连续错误数
    consecutive_correct UInt8,             -- 当前连续正确数
    question_difficulty Float32,           -- 题目难度系数
    mastery_value Float32,                 -- 当前掌握度值
    
    -- 扩展信息
    event_details String,                  -- 事件详细信息(JSON格式)
    
    -- 创建时间
    created_at DateTime DEFAULT now()      -- 记录创建时间
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, event_time)
TTL event_date + INTERVAL 1 YEAR DELETE
SETTINGS index_granularity = 8192;

-- 添加表和列的注释
COMMENT ON TABLE tbl_attentiveness_events IS '专注度事件表 - 记录用户练习中专注度相关的事件';
-- 事件基础信息注释
COMMENT ON COLUMN tbl_attentiveness_events.event_time IS '事件时间 - 事件发生的具体时间，精确到毫秒';
COMMENT ON COLUMN tbl_attentiveness_events.event_date IS '事件日期 - 事件发生的日期，用于分区';
COMMENT ON COLUMN tbl_attentiveness_events.user_id IS '用户ID - 事件关联的用户ID';
COMMENT ON COLUMN tbl_attentiveness_events.session_id IS '练习会话ID - 事件发生的练习会话ID';
COMMENT ON COLUMN tbl_attentiveness_events.knowledge_point_id IS '知识点ID - 事件关联的知识点ID';
COMMENT ON COLUMN tbl_attentiveness_events.question_id IS '相关题目ID - 事件关联的题目ID';
COMMENT ON COLUMN tbl_attentiveness_events.question_group_id IS '题组ID - 事件关联的题组ID';
-- 专注度事件信息注释
COMMENT ON COLUMN tbl_attentiveness_events.event_type IS '事件类型 - 专注度事件的类型：快速答题(fast_answer)、连续错误(consecutive_wrong)、简单题错误(simple_wrong)、专注度提醒(reminder)、熔断触发(circuit_breaker)';
COMMENT ON COLUMN tbl_attentiveness_events.event_trigger_id IS '事件触发ID - 事件的具体触发原因编号';
COMMENT ON COLUMN tbl_attentiveness_events.attentiveness_before IS '事件前专注度 - 事件发生前的专注度值';
COMMENT ON COLUMN tbl_attentiveness_events.attentiveness_after IS '事件后专注度 - 事件发生后的专注度值';
COMMENT ON COLUMN tbl_attentiveness_events.attentiveness_change IS '专注度变化量 - 事件导致的专注度变化量';
-- 事件上下文注释
COMMENT ON COLUMN tbl_attentiveness_events.answer_duration_seconds IS '答题时长 - 相关题目的答题时长，单位秒';
COMMENT ON COLUMN tbl_attentiveness_events.is_answer_correct IS '答案是否正确 - 相关题目的答题结果';
COMMENT ON COLUMN tbl_attentiveness_events.consecutive_incorrect IS '当前连续错误数 - 事件发生时的连续错误题目数';
COMMENT ON COLUMN tbl_attentiveness_events.consecutive_correct IS '当前连续正确数 - 事件发生时的连续正确题目数';
COMMENT ON COLUMN tbl_attentiveness_events.question_difficulty IS '题目难度系数 - 相关题目的难度系数';
COMMENT ON COLUMN tbl_attentiveness_events.mastery_value IS '当前掌握度值 - 事件发生时用户对知识点的掌握度';
-- 扩展信息注释
COMMENT ON COLUMN tbl_attentiveness_events.event_details IS '事件详细信息 - 存储JSON格式的事件详情，便于存储额外信息';
-- 创建时间注释
COMMENT ON COLUMN tbl_attentiveness_events.created_at IS '记录创建时间 - 该记录的创建时间';
```

### 3.7 表名: tbl_recommendation_logs

#### 3.7.1 表功能说明

本表为ClickHouse分析表，用于记录推题策略执行的详细日志，包括推荐原因、推荐结果、计算过程及关键参数等信息。通过这些日志，可以分析推题策略的效果，发现问题并优化算法，同时也可以为特定用户的推题过程提供审计和解释能力。表中的数据对于推题策略的调整、优化和评估具有重要价值。

#### 3.7.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_recommendation_logs
(
    -- 基础信息
    log_time DateTime64(3),                -- 日志时间，毫秒精度
    log_date Date MATERIALIZED toDate(log_time), -- 日志日期
    user_id UInt64,                        -- 用户ID
    session_id UInt64,                     -- 练习会话ID
    knowledge_point_id UInt64,             -- 知识点ID
    question_group_id UInt64,              -- 题组ID
    
    -- 推荐上下文
    recommendation_step LowCardinality(String), -- 推荐步骤: session_init, next_question, next_group, retry_recommendation
    exercise_type LowCardinality(String),  -- 练习类型: practice, retry
    current_mastery Float32,               -- 当前掌握度
    target_mastery Float32,                -- 目标掌握度
    consecutive_correct UInt8,             -- 当前连续正确数
    consecutive_incorrect UInt8,           -- 当前连续错误数
    last_question_id UInt64,               -- 上一道题目ID
    last_question_difficulty Float32,      -- 上一道题目难度
    last_answer_correct UInt8,             -- 上一道题目是否正确
    
    -- 推荐结果
    recommended_question_id UInt64,        -- 推荐的题目ID
    predicted_mastery_increment Float32,   -- 预测的掌握度增量
    question_difficulty Float32,           -- 推荐题目难度系数
    is_must_do UInt8,                      -- 是否必做题
    recommendation_reason LowCardinality(String), -- 推荐原因: max_increment, must_do, difficulty_control, error_correction
    
    -- 推荐详情
    candidate_count UInt16,                -- 候选题目数量
    filter_rules Array(LowCardinality(String)), -- 过滤规则列表
    ranking_factors Map(String, Float32),  -- 排序因子及权重
    algorithm_version LowCardinality(String), -- 算法版本
    
    -- 性能指标
    processing_time_ms UInt16,             -- 处理时间(毫秒)
    
    -- 扩展信息
    recommendation_details String,         -- 推荐详细信息(JSON格式)
    
    -- 创建时间
    created_at DateTime DEFAULT now()      -- 记录创建时间
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date)
ORDER BY (log_date, user_id, session_id, log_time)
TTL log_date + INTERVAL 1 YEAR DELETE
SETTINGS index_granularity = 8192;

-- 添加表和列的注释
COMMENT ON TABLE tbl_recommendation_logs IS '推题日志表 - 记录推题策略执行的详细日志';
-- 基础信息注释
COMMENT ON COLUMN tbl_recommendation_logs.log_time IS '日志时间 - 日志记录的具体时间，精确到毫秒';
COMMENT ON COLUMN tbl_recommendation_logs.log_date IS '日志日期 - 日志记录的日期，用于分区';
COMMENT ON COLUMN tbl_recommendation_logs.user_id IS '用户ID - 推题目标用户的ID';
COMMENT ON COLUMN tbl_recommendation_logs.session_id IS '练习会话ID - 推题所在的练习会话ID';
COMMENT ON COLUMN tbl_recommendation_logs.knowledge_point_id IS '知识点ID - 推题关联的知识点ID';
COMMENT ON COLUMN tbl_recommendation_logs.question_group_id IS '题组ID - 推题关联的题组ID';
-- 推荐上下文注释
COMMENT ON COLUMN tbl_recommendation_logs.recommendation_step IS '推荐步骤 - 当前推题的步骤';
COMMENT ON COLUMN tbl_recommendation_logs.exercise_type IS '练习类型 - 练习的类型：常规练习(practice)、再练一次(retry)';
COMMENT ON COLUMN tbl_recommendation_logs.current_mastery IS '当前掌握度 - 推题时用户的当前掌握度';
COMMENT ON COLUMN tbl_recommendation_logs.target_mastery IS '目标掌握度 - 该知识点的目标掌握度';
COMMENT ON COLUMN tbl_recommendation_logs.consecutive_correct IS '当前连续正确数 - 推题时用户的连续答对题数';
COMMENT ON COLUMN tbl_recommendation_logs.consecutive_incorrect IS '当前连续错误数 - 推题时用户的连续答错题数';
COMMENT ON COLUMN tbl_recommendation_logs.last_question_id IS '上一道题目ID - 用户最近回答的题目ID';
COMMENT ON COLUMN tbl_recommendation_logs.last_question_difficulty IS '上一道题目难度 - 用户最近回答题目的难度系数';
COMMENT ON COLUMN tbl_recommendation_logs.last_answer_correct IS '上一道题目是否正确 - 用户最近回答题目的结果';
-- 推荐结果注释
COMMENT ON COLUMN tbl_recommendation_logs.recommended_question_id IS '推荐的题目ID - 推题算法推荐的题目ID';
COMMENT ON COLUMN tbl_recommendation_logs.predicted_mastery_increment IS '预测的掌握度增量 - 推题算法预测的掌握度增量';
COMMENT ON COLUMN tbl_recommendation_logs.question_difficulty IS '推荐题目难度系数 - 推荐题目的难度系数';
COMMENT ON COLUMN tbl_recommendation_logs.is_must_do IS '是否必做题 - 推荐的题目是否为必做题';
COMMENT ON COLUMN tbl_recommendation_logs.recommendation_reason IS '推荐原因 - 推荐该题目的主要原因';
-- 推荐详情注释
COMMENT ON COLUMN tbl_recommendation_logs.candidate_count IS '候选题目数量 - 过滤后可供选择的题目数量';
COMMENT ON COLUMN tbl_recommendation_logs.filter_rules IS '过滤规则列表 - 推题过程中应用的过滤规则';
COMMENT ON COLUMN tbl_recommendation_logs.ranking_factors IS '排序因子及权重 - 推题排序时使用的因子及其权重';
COMMENT ON COLUMN tbl_recommendation_logs.algorithm_version IS '算法版本 - 使用的推题算法版本';
-- 性能指标注释
COMMENT ON COLUMN tbl_recommendation_logs.processing_time_ms IS '处理时间 - 推题算法的处理时间，单位毫秒';
-- 扩展信息注释
COMMENT ON COLUMN tbl_recommendation_logs.recommendation_details IS '推荐详细信息 - 存储JSON格式的推荐详情，包含更多细节';
-- 创建时间注释
COMMENT ON COLUMN tbl_recommendation_logs.created_at IS '记录创建时间 - 该记录的创建时间';
```

### 3.8 表名: exercise_strategy_configs

#### 3.8.1 表功能说明

本表用于存储推题策略相关的配置参数，包括专注度评估阈值、掌握度计算参数、推题算法参数和熔断策略参数等。这些参数可动态调整，无需修改代码即可改变系统行为。表中的参数按配置组和参数键组织，支持多版本和多环境配置，便于进行A/B测试和灰度发布。

#### 3.8.2 表结构定义 (DDL)

```sql
CREATE TABLE exercise_strategy_configs (
  -- 主键
  id BIGSERIAL PRIMARY KEY,
  
  -- 业务字段
  config_group VARCHAR(50) NOT NULL,       -- 配置组: attentiveness, mastery, recommendation, circuit_breaker
  config_key VARCHAR(100) NOT NULL,        -- 配置键
  config_value TEXT NOT NULL,              -- 配置值
  data_type VARCHAR(20) NOT NULL,          -- 数据类型: string, int, float, boolean, json
  description TEXT NULL,                   -- 配置描述
  default_value TEXT NOT NULL,             -- 默认值
  version VARCHAR(20) NOT NULL DEFAULT 'v1', -- 配置版本
  env VARCHAR(20) NOT NULL DEFAULT 'prod', -- 环境: dev, test, prod
  is_overridable BOOLEAN NOT NULL DEFAULT TRUE, -- 是否可被特定规则覆盖
  
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,      -- 状态: 0-禁用 1-启用
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP, -- 更新时间
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL, -- 删除时间（软删除）
  created_by BIGINT NOT NULL,              -- 创建人
  updated_by BIGINT NOT NULL               -- 更新人
);

-- 添加表和字段注释
COMMENT ON TABLE exercise_strategy_configs IS '练习策略配置表 - 存储推题策略相关的配置参数';
COMMENT ON COLUMN exercise_strategy_configs.id IS '主键ID';
COMMENT ON COLUMN exercise_strategy_configs.config_group IS '配置组 - 配置的分组，如专注度评估(attentiveness)、掌握度计算(mastery)、推题算法(recommendation)、熔断策略(circuit_breaker)';
COMMENT ON COLUMN exercise_strategy_configs.config_key IS '配置键 - 配置项的唯一标识符';
COMMENT ON COLUMN exercise_strategy_configs.config_value IS '配置值 - 配置项的值';
COMMENT ON COLUMN exercise_strategy_configs.data_type IS '数据类型 - 配置值的数据类型';
COMMENT ON COLUMN exercise_strategy_configs.description IS '配置描述 - 对配置项的详细说明';
COMMENT ON COLUMN exercise_strategy_configs.default_value IS '默认值 - 配置项的默认值';
COMMENT ON COLUMN exercise_strategy_configs.version IS '配置版本 - 配置项的版本号';
COMMENT ON COLUMN exercise_strategy_configs.env IS '环境 - 配置适用的环境：开发(dev)、测试(test)、生产(prod)';
COMMENT ON COLUMN exercise_strategy_configs.is_overridable IS '是否可被特定规则覆盖 - 表示该配置是否可被特定场景的规则覆盖';
COMMENT ON COLUMN exercise_strategy_configs.status IS '状态: 0-禁用 1-启用';
COMMENT ON COLUMN exercise_strategy_configs.created_at IS '创建时间';
COMMENT ON COLUMN exercise_strategy_configs.updated_at IS '更新时间';
COMMENT ON COLUMN exercise_strategy_configs.deleted_at IS '删除时间 - NULL表示未删除';
COMMENT ON COLUMN exercise_strategy_configs.created_by IS '创建人ID';
COMMENT ON COLUMN exercise_strategy_configs.updated_by IS '更新人ID';

-- 添加索引和唯一约束
CREATE UNIQUE INDEX uk_exercise_strategy_configs_group_key_version_env ON exercise_strategy_configs(config_group, config_key, version, env) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_strategy_configs_group ON exercise_strategy_configs(config_group) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_strategy_configs_version ON exercise_strategy_configs(version) WHERE deleted_at IS NULL;
CREATE INDEX idx_exercise_strategy_configs_env ON exercise_strategy_configs(env) WHERE deleted_at IS NULL;

-- 初始化一些基础配置数据
INSERT INTO exercise_strategy_configs (config_group, config_key, config_value, data_type, description, default_value, version, env, is_overridable, created_by, updated_by)
VALUES 
-- 专注度相关配置
('attentiveness', 'min_attentiveness_score', '0', 'int', '最小专注度分值，低于此值触发熔断', '0', 'v1', 'prod', true, 1, 1),
('attentiveness', 'max_attentiveness_score', '10', 'int', '最大专注度分值', '10', 'v1', 'prod', false, 1, 1),
('attentiveness', 'fast_answer_threshold_seconds', '3', 'int', '快速作答阈值(秒)，低于此值视为快速作答', '3', 'v1', 'prod', true, 1, 1),
('attentiveness', 'consecutive_wrong_threshold', '3', 'int', '连续答错阈值，达到此值降低专注度', '3', 'v1', 'prod', true, 1, 1),
('attentiveness', 'simple_wrong_difficulty_threshold', '0.3', 'float', '简单题难度阈值，低于此值的题目答错降低专注度', '0.3', 'v1', 'prod', true, 1, 1),

-- 掌握度相关配置
('mastery', 'initial_mastery', '0.1', 'float', '初始掌握度值', '0.1', 'v1', 'prod', true, 1, 1),
('mastery', 'target_mastery', '0.8', 'float', '目标掌握度值', '0.8', 'v1', 'prod', true, 1, 1),
('mastery', 'mastery_decay_rate', '0.02', 'float', '掌握度衰减率(每天)', '0.02', 'v1', 'prod', true, 1, 1),
('mastery', 'mastery_increment_correct', '0.05', 'float', '答对题目掌握度基础增量', '0.05', 'v1', 'prod', true, 1, 1),
('mastery', 'mastery_decrement_incorrect', '0.02', 'float', '答错题目掌握度基础减量', '0.02', 'v1', 'prod', true, 1, 1),

-- 推题算法配置
('recommendation', 'default_session_question_count', '10', 'int', '默认会话题目数量', '10', 'v1', 'prod', true, 1, 1),
('recommendation', 'min_question_count', '5', 'int', '最小题目数量', '5', 'v1', 'prod', true, 1, 1),
('recommendation', 'max_question_count', '20', 'int', '最大题目数量', '20', 'v1', 'prod', true, 1, 1),
('recommendation', 'difficulty_weight', '0.4', 'float', '难度在推荐算法中的权重', '0.4', 'v1', 'prod', true, 1, 1),
('recommendation', 'mastery_weight', '0.6', 'float', '掌握度在推荐算法中的权重', '0.6', 'v1', 'prod', true, 1, 1),

-- 熔断策略配置
('circuit_breaker', 'enable_breaker', 'true', 'boolean', '是否启用熔断功能', 'true', 'v1', 'prod', true, 1, 1),
('circuit_breaker', 'consecutive_wrong_breaker_threshold', '5', 'int', '连续答错触发熔断阈值', '5', 'v1', 'prod', true, 1, 1),
('circuit_breaker', 'low_attentiveness_breaker_threshold', '3', 'int', '低专注度触发熔断阈值', '3', 'v1', 'prod', true, 1, 1),
('circuit_breaker', 'retry_recommendation_threshold', '3', 'int', '触发再练一次推荐的错题数量阈值', '3', 'v1', 'prod', true, 1, 1);
```

## 4. 表间关系与 ER 图

### 4.1 表间关系描述

本数据库设计中的表间关系如下：

1. **exercise_sessions** 与 **exercise_answer_records** 的关系:
   - 一个练习会话(exercise_sessions)包含多个答题记录(exercise_answer_records)
   - 关联字段: exercise_answer_records.session_id → exercise_sessions.id
   - 关系类型: 一对多(1:N)

2. **exercise_sessions** 与 **exercise_user_states** 的关系:
   - 用户状态表(exercise_user_states)中的session_id关联到当前活动的练习会话
   - 关联字段: exercise_user_states.session_id → exercise_sessions.id
   - 关系类型: 一对一(1:1)，仅当用户处于练习中时有关联

3. **exercise_user_states** 与 **exercise_user_knowledge_masteries** 的关系:
   - 用户知识点掌握度历史表(exercise_user_knowledge_masteries)记录用户状态表(exercise_user_states)中掌握度的变更历史
   - 关联字段: exercise_user_knowledge_masteries.user_id + exercise_user_knowledge_masteries.knowledge_point_id → exercise_user_states.user_id + exercise_user_states.knowledge_point_id
   - 关系类型: 一对多(1:N)，一个用户状态对应多条掌握度历史记录

4. **ClickHouse分析表**与**操作型表**的关系:
   - tbl_exercise_stats、tbl_attentiveness_events、tbl_recommendation_logs这些分析表，通过user_id、session_id、knowledge_point_id等字段与操作型表相关联
   - 这些表主要用于分析，不存在强实体关系约束，但在逻辑上与操作型表紧密关联

5. **exercise_strategy_configs** 与其他表的关系:
   - 配置表与其他表没有直接的实体关系，但其中的配置参数会影响其他表中数据的生成和处理逻辑

### 4.2 ER 图 (使用 Mermaid 语法)

```mermaid
erDiagram
    exercise_sessions ||--o{ exercise_answer_records : "包含多个答题记录"
    exercise_sessions ||--o| exercise_user_states : "关联当前练习"
    exercise_user_states ||--o{ exercise_user_knowledge_masteries : "记录掌握度历史"
    
    exercise_sessions {
        bigint id PK
        bigint user_id
        bigint knowledge_point_id
        smallint exercise_type
        timestamptz start_time
        timestamptz end_time
        smallint predicted_question_count
        smallint actual_question_count
        smallint completed_question_count
        smallint correct_count
        smallint incorrect_count
        smallint completion_status
        smallint attentiveness_score
        numeric mastery_before
        numeric mastery_after
        boolean is_recommended_to_retry
        smallint breaker_reason
    }
    
    exercise_answer_records {
        bigint id PK
        bigint session_id FK
        bigint user_id
        bigint knowledge_point_id
        bigint question_id
        bigint question_group_id
        numeric question_difficulty
        boolean is_must_do
        numeric expected_mastery_increment
        timestamptz answer_time
        int answer_duration_seconds
        text answer_content
        boolean is_correct
        numeric mastery_before
        numeric mastery_after
        numeric mastery_increment
        smallint attentiveness_change
        smallint attentiveness_trigger
        int consecutive_correct
        int consecutive_incorrect
    }
    
    exercise_user_states {
        bigint id PK
        bigint user_id
        bigint knowledge_point_id
        bigint session_id FK
        numeric current_mastery
        numeric target_mastery
        smallint attentiveness_score
        int consecutive_correct
        int consecutive_incorrect
        bigint last_question_id
        numeric last_question_difficulty
        boolean last_answer_correct
        int total_exercise_count
        int total_question_count
        int total_correct_count
        smallint current_learning_state
        timestamptz last_exercise_time
    }
    
    exercise_user_knowledge_masteries {
        bigint id PK
        bigint user_id
        bigint knowledge_point_id
        numeric mastery_value
        timestamptz update_time
        smallint update_reason
        bigint related_session_id
        bigint related_question_id
        numeric mastery_delta
        numeric previous_mastery
        numeric target_mastery
    }
    
    exercise_strategy_configs {
        bigint id PK
        varchar config_group
        varchar config_key
        text config_value
        varchar data_type
        text description
        text default_value
        varchar version
        varchar env
        boolean is_overridable
    }
    
    tbl_exercise_stats {
        date stat_date
        uint64 user_id
        uint64 knowledge_point_id
        uint32 session_count
        uint32 question_count
        uint32 correct_count
        uint32 incorrect_count
        float32 mastery_delta
    }
    
    tbl_attentiveness_events {
        datetime64 event_time
        uint64 user_id
        uint64 session_id
        uint64 question_id
        string event_type
        uint8 attentiveness_before
        uint8 attentiveness_after
        int8 attentiveness_change
    }
    
    tbl_recommendation_logs {
        datetime64 log_time
        uint64 user_id
        uint64 session_id
        uint64 recommended_question_id
        string recommendation_reason
        float32 predicted_mastery_increment
    }
```

## 5. 核心用户场景/用户故事与数据操作分析

### 5.1 场景/故事: 巩固练习推题流程

#### 5.1.1 场景描述

用户小明正在学习一个新的知识点"二次函数"。学习完视频和文字讲解后，系统提示他进行巩固练习。小明点击开始练习，系统根据他的当前掌握度和学习状态，从题库中选择合适的题目推荐给他，并实时调整题目难度和数量。整个过程中，系统会监控小明的专注度和答题情况，直到他达到预定的掌握度目标或练习被中断。

#### 5.1.2 涉及的主要数据表

- exercise_sessions: 记录练习会话的基本信息
- exercise_user_states: 维护用户的当前状态(掌握度、专注度等)
- exercise_answer_records: 记录用户的每次答题情况
- exercise_user_knowledge_masteries: 记录掌握度变更历史
- exercise_strategy_configs: 提供推题策略的配置参数
- tbl_recommendation_logs: 记录推题过程的详细日志(分析用)

#### 5.1.3 数据操作流程 (CRUD 分析)

1. **练习开始**:
   - 创建(C): 向exercise_sessions插入新记录，记录练习开始时间和初始状态
   - 读取(R): 从exercise_user_states读取用户当前掌握度和学习状态
   - 更新(U): 更新exercise_user_states中用户状态，设置session_id为当前会话ID
   - 读取(R): 从exercise_strategy_configs读取相关配置参数

2. **推荐第一题**:
   - 读取(R): 根据用户掌握度从题库中读取适合的题目
   - 创建(C): 向tbl_recommendation_logs插入推题日志
   - 返回推荐题目给前端展示

3. **用户作答**:
   - 创建(C): 向exercise_answer_records插入用户答题记录
   - 读取(R): 读取exercise_user_states获取当前状态
   - 更新(U): 更新exercise_user_states中的掌握度、连续答对/错数等
   - 创建(C): 向exercise_user_knowledge_masteries插入掌握度变更记录
   - 更新(U): 更新exercise_sessions中的答题统计数据

4. **专注度评估**:
   - 读取(R): 读取exercise_answer_records中的答题情况
   - 更新(U): 根据答题情况更新exercise_user_states中的专注度
   - 创建(C): 如果触发专注度变化，向tbl_attentiveness_events插入事件记录

5. **推荐下一题**:
   - 读取(R): 根据更新后的用户状态从题库中读取适合的下一题
   - 创建(C): 向tbl_recommendation_logs插入推题日志
   - 返回推荐题目给前端展示

6. **练习结束**:
   - 更新(U): 更新exercise_sessions记录，设置结束时间和完成状态
   - 更新(U): 更新exercise_user_states中用户状态，清除session_id
   - 创建(C): 向exercise_user_knowledge_masteries插入最终掌握度记录
   - 读取(R): 判断是否需要推荐再练一次

#### 5.1.4 性能考量与索引支持

- 推题接口(步骤2和5)要求P99延迟<100ms，需要确保exercise_user_states和exercise_answer_records表上有合适的索引
- user_id和knowledge_point_id是最常用的查询条件，所有表都对这两个字段建立了索引
- exercise_sessions和exercise_answer_records表按时间范围查询频繁，建立了start_time/answer_time的索引
- 读写比例约为3:1，读操作占主导，索引策略偏向优化读取性能

### 5.2 场景/故事: 再练一次推题流程

#### 5.2.1 场景描述

小红刚完成了"分数乘法"知识点的巩固练习，但系统发现她在练习中有3道题目答错了。根据配置的阈值，系统认为她需要针对这些错题进行额外练习，于是推荐她进行"再练一次"。小红接受建议，开始再练一次练习，系统优先推荐她之前做错的题目及相似题型，帮助她巩固薄弱环节。

#### 5.2.2 涉及的主要数据表

- exercise_sessions: 记录练习会话信息，包括原始会话和再练一次会话
- exercise_answer_records: 查找原会话中的错题，记录再练一次的答题记录
- exercise_user_states: 维护用户状态信息
- exercise_strategy_configs: 提供再练一次相关配置参数
- tbl_recommendation_logs:.记录推题过程的详细日志(分析用)

#### 5.2.3 数据操作流程 (CRUD 分析)

1. **识别需要再练一次**:
   - 读取(R): 查询exercise_sessions和exercise_answer_records获取用户在原会话中的错题情况
   - 读取(R): 从exercise_strategy_configs获取再练一次的触发条件配置
   - 更新(U): 更新原exercise_sessions记录的is_recommended_to_retry字段为true

2. **创建再练一次会话**:
   - 创建(C): 向exercise_sessions插入新记录，exercise_type设为2(再练一次)
   - 读取(R): 从exercise_user_states读取用户当前掌握度和学习状态
   - 更新(U): 更新exercise_user_states中用户状态，设置session_id为新会话ID

3. **推荐错题**:
   - 读取(R): 从原会话的exercise_answer_records中查询用户做错的题目
   - 创建(C): 向tbl_recommendation_logs插入推题日志
   - 返回推荐题目给前端展示

4. **用户作答错题**:
   - 创建(C): 向exercise_answer_records插入用户答题记录
   - 读取(R): 读取exercise_user_states获取当前状态
   - 更新(U): 更新exercise_user_states中的掌握度、连续答对/错数等
   - 创建(C): 向exercise_user_knowledge_masteries插入掌握度变更记录
   - 更新(U): 更新exercise_sessions中的答题统计数据

5. **练习结束**:
   - 更新(U): 更新exercise_sessions记录，设置结束时间和完成状态
   - 更新(U): 更新exercise_user_states中用户状态，清除session_id
   - 创建(C): 向exercise_user_knowledge_masteries插入最终掌握度记录

#### 5.2.4 性能考量与索引支持

- 错题查询(步骤3)需要通过session_id和is_correct字段快速找到用户在原会话中的错题，索引idx_exercise_answer_records_session_id和idx_exercise_answer_records_is_correct提供支持
- 用户错题信息查询是核心操作，exercise_answer_records表上的user_id, question_id, is_correct组合索引提供有力支持
- 历史会话查询通过exercise_sessions表上的user_id, knowledge_point_id, exercise_type复合索引优化

### 5.3 场景/故事: 专注度评估流程

#### 5.3.1 场景描述

小华正在进行"分数加减法"的巩固练习。在答题过程中，他连续答错了3道题，系统根据专注度评估算法判断他可能注意力不集中。系统将他的专注度分值从10分降低到9分，并在界面上给予轻微提醒。随后，他又快速作答一道题且答错，系统进一步降低专注度至8分。当他第三次出现不认真答题行为时，专注度降至7分，系统显示明显的提醒。如果专注度继续下降到预设阈值，系统将触发熔断机制，暂停练习。

#### 5.3.2 涉及的主要数据表

- exercise_answer_records: 记录用户的答题情况，用于分析专注度
- exercise_user_states: 维护用户的当前专注度状态
- exercise_sessions: 记录练习会话信息，包括可能的熔断状态
- exercise_strategy_configs: 提供专注度评估的配置参数
- tbl_attentiveness_events: 记录专注度相关事件(分析用)

#### 5.3.3 数据操作流程 (CRUD 分析)

1. **答题记录分析**:
   - 创建(C): 向exercise_answer_records插入用户答题记录
   - 读取(R): 从exercise_answer_records读取用户近期答题情况(如连续错误数)
   - 读取(R): 从exercise_strategy_configs读取专注度评估相关参数

2. **专注度计算**:
   - 读取(R): 从exercise_user_states获取当前专注度
   - 更新(U): 如果触发专注度变化条件，更新exercise_user_states中的专注度
   - 创建(C): 向tbl_attentiveness_events插入专注度变化事件记录
   - 更新(U): 在exercise_answer_records中记录专注度变化和触发原因

3. **熔断判断**:
   - 读取(R): 从exercise_strategy_configs读取熔断相关配置
   - 读取(R): 从exercise_user_states获取当前专注度
   - 判断专注度是否低于熔断阈值

4. **执行熔断**(如果需要):
   - 更新(U): 更新exercise_sessions记录，设置completion_status为2(已熔断)，breaker_reason为2(专注度过低)
   - 更新(U): 更新exercise_user_states中用户状态，清除session_id
   - 创建(C): 向tbl_attentiveness_events插入熔断事件记录

#### 5.3.4 性能考量与索引支持

- 答题记录分析(步骤1)需要快速获取用户近期答题记录，索引idx_exercise_answer_records_user_id和按时间排序的idx_exercise_answer_records_answer_time提供支持
- 专注度状态查询和更新(步骤2)对exercise_user_states表的user_id和knowledge_point_id的复合索引依赖较高
- 专注度评估是高频操作，每次答题都会触发，需要保证这部分操作的低延迟

## 6. 数据量预估与增长趋势

### 6.1 数据量预估

**PostgreSQL 表数据量预估**:

| 表名 | 记录数量(当前) | 记录数量(2年后) | 单条记录大小 | 总存储空间 | 说明 |
|------|--------------|---------------|------------|-----------|-----|
| exercise_sessions | 5.6万/天, 2000万(累计) | 28万/天, 1亿(累计) | ~1KB | ~100GB | 每用户每知识点平均2次练习 |
| exercise_answer_records | 56万/天, 2亿(累计) | 280万/天, 10亿(累计) | ~1.5KB | ~1.5TB | 每会话平均10题 |
| exercise_user_states | 500万 | 2500万 | ~0.8KB | ~20GB | 用户数×知识点数 |
| exercise_user_knowledge_masteries | 2500万 | 1.25亿 | ~0.6KB | ~75GB | 每个状态平均5条历史 |
| exercise_strategy_configs | <1000 | <2000 | ~2KB | <5MB | 配置项较少 |

**ClickHouse 表数据量预估**:

| 表名 | 记录数量(当前) | 记录数量(2年后) | 单条记录大小 | 总存储空间 | 说明 |
|------|--------------|---------------|------------|-----------|-----|
| tbl_exercise_stats | 10万/天, 7200万(累计) | 50万/天, 3.6亿(累计) | ~150B | ~50GB | 每日汇总统计 |
| tbl_attentiveness_events | 20万/天, 1.44亿(累计) | 100万/天, 7.3亿(累计) | ~300B | ~200GB | 专注度变化事件 |
| tbl_recommendation_logs | 60万/天, 4.3亿(累计) | 300万/天, 21.9亿(累计) | ~700B | ~1.5TB | 推题日志 |

### 6.2 QPS 与性能预估

**操作接口与QPS预估**:

| 接口名称 | 当前平均QPS | 当前峰值QPS | 2年后平均QPS | 2年后峰值QPS | 说明 |
|---------|------------|------------|-------------|-------------|-----|
| 会话创建 | 0.7 | 5 | 3.5 | 25 | 创建练习会话 |
| 推题请求 | 7 | 70 | 35 | 350 | 获取推荐题目 |
| 答题提交 | 7 | 70 | 35 | 350 | 提交答题结果 |
| 状态查询 | 20 | 200 | 100 | 1000 | 查询用户状态 |
| 历史查询 | 2 | 20 | 10 | 100 | 查询历史数据 |
| 总体 | 36.7 | 365 | 183.5 | 1825 | 极端峰值可达5K |

**关键查询响应时间目标**:

| 查询类型 | P95响应时间 | P99响应时间 | 说明 |
|---------|------------|------------|-----|
| 推题查询 | <50ms | <100ms | 核心查询，需要高性能 |
| 答题提交 | <80ms | <150ms | 含状态计算与更新 |
| 状态查询 | <30ms | <80ms | 简单查询 |
| 历史查询 | <300ms | <800ms | 复杂统计查询 |

### 6.3 支撑规划

**数据库架构**:

- PostgreSQL采用一主多从架构，主库负责写操作，从库负责读操作
- ClickHouse采用分片集群架构，按月/年分区
- 使用Redis缓存热点数据(用户状态、配置参数等)

**分表与分区策略**:

- PostgreSQL事务表按时间分区(使用TimescaleDB)
  - exercise_sessions: 按月分区
  - exercise_answer_records: 按周分区
- ClickHouse分析表
  - 所有表按月分区
  - 设置TTL，1年后自动归档

**索引优化**:

- 对高频查询条件建立联合索引
- 使用部分索引(WHERE deleted_at IS NULL)减小索引大小
- ClickHouse表适当使用预聚合视图

**硬件资源规划**:

| 资源类型 | 当前配置 | 2年后配置 | 说明 |
|---------|---------|----------|-----|
| PostgreSQL主库 | 8核16G, SSD 1TB | 16核64G, SSD 5TB | 主要处理写操作 |
| PostgreSQL从库 | 4核8G × 2, SSD 1TB | 8核32G × 4, SSD 5TB | 处理读操作 |
| ClickHouse节点 | 8核32G × 3, SSD 2TB | 16核64G × 6, SSD 10TB | 分析型查询 |
| Redis集群 | 4核8G × 2 | 8核16G × 3 | 缓存热点数据 |

## 7. 数据库特定设计要点回顾

### 7.1 PostgreSQL 设计要点体现

* **标准字段的全面应用**：所有表都包含了id、created_at、updated_at、deleted_at、status、created_by、updated_by等标准字段
* **命名规范严格遵守**：所有表名、字段名、索引名均遵循snake_case命名规范，索引前缀使用idx_或uk_
* **TIMESTAMPTZ的正确使用**：所有时间类型字段均使用TIMESTAMPTZ确保时区统一性
* **软删除机制的应用**：通过deleted_at字段实现软删除，避免直接物理删除数据
* **索引WHERE条件的应用**：索引定义中使用WHERE deleted_at IS NULL条件，优化索引大小
* **部分索引的高效使用**：针对高频查询场景，使用包含WHERE条件的部分索引提升性能
* **复合索引的合理设计**：根据查询模式，设计了多个多字段复合索引，遵循最左匹配原则
* **数据类型选择恰当**：根据字段特性选择合适的数据类型，如BIGINT用于ID、NUMERIC用于掌握度等精确计算

### 7.2 ClickHouse 设计要点体现

* **适当的表引擎选择**：所有分析表使用MergeTree引擎系列，支持高效的分析查询
* **优化的排序键设计**：按照主要查询维度设计排序键，提升查询性能
* **合理的分区键设计**：使用时间维度(如event_date)作为分区键，支持高效的数据生命周期管理
* **LowCardinality数据类型使用**：对于低基数字段(如事件类型)使用LowCardinality优化存储和查询效率
* **TTL策略的应用**：设置合理的TTL，自动管理历史数据，降低存储成本
* **DateTime64精度设置**：事件时间使用毫秒精度，满足精确时序分析需求
* **物化列的使用**：使用MATERIALIZED关键字计算派生字段，如event_date从event_time派生
* **合理的压缩设置**：使用默认压缩算法，在查询性能和存储成本间取得平衡

## 8. 设计检查清单 (Design Checklist)

本检查清单分为两部分：第一部分关注数据库设计与需求、架构的对齐性；第二部分关注数据库设计方案本身的技术质量。
( ✅: 满足/通过, ❌: 不满足/未通过, N/A: 不适用 )

### 8.1 需求与架构对齐性检查

| 需求点/用户故事 (来自PRD)        | 对应架构模块 (来自TD)           | 关联核心数据表 (本次设计) | ①需求覆盖完整性 | ②架构符合性 | ③一致性与无冲突 | ④业务场景满足度 | ⑤可追溯性与依赖清晰度 | 备注与待办事项                                                                                                                                  |
| -------------------------------- | ----------------------------- | --------------------- | --------------- | ----------- | --------------- | ------------- | ------------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| 学生在课后进行巩固练习 | 巩固练习推题服务 | `exercise_sessions`, `exercise_answer_records`, `exercise_user_states` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.1已覆盖。设计符合架构中的巩固练习流程，支持推题策略和状态记录。 |
| 学生再练一次错题巩固 | 巩固练习推题服务 | `exercise_sessions`, `exercise_answer_records` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.2已覆盖。表设计支持错题推荐和再练一次流程。 |
| 检测用户专注度评估 | 专注度评估服务 | `exercise_user_states`, `tbl_attentiveness_events` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.3已覆盖。表设计支持专注度计算、评估和熔断流程。 |
| 掌握度计算与更新 | 掌握度计算服务 | `exercise_user_states`, `exercise_user_knowledge_masteries` | ✅ | ✅ | ✅ | ✅ | ✅ | 掌握度记录和历史跟踪完整设计，支持掌握度变更与查询。 |
| 练习策略配置与管理 | 策略配置服务 | `exercise_strategy_configs` | ✅ | ✅ | ✅ | ✅ | ✅ | 配置表设计符合架构要求，支持多环境、多版本配置。 |

**检查维度说明 (需求与架构对齐性):**
* **① 需求覆盖完整性 (Requirements Coverage & Completeness)**:
  * 所有明确的功能性和非功能性需求（尤其是数据相关的）是否都有对应的服务模块（如果架构已定义）和数据表结构来支持？
  * 需求中的核心业务对象、属性和关联关系是否都在数据库设计中得到完整体现，无遗漏？
* **② 架构符合性 (Architectural Alignment & Correctness)**:
  * 数据库设计是否准确、无歧义地实现了技术架构方案中关于数据存储、数据流转和模块交互的意图？
  * 表结构、数据分布（如分区、分片键）、数据冗余和一致性策略是否符合整体架构原则？
* **③ 一致性与无冲突 (Consistency & Conflict Freedom)**:
  * 数据库设计与需求文档、架构文档中的术语、定义和业务规则是否保持一致？
  * 是否存在设计上的内部冲突（例如，不同表对同一概念的定义不一致）或与需求/架构的冲突？
  * 是否存在不必要的重复设计（例如，多个表存储完全相同且应标准化的信息）？
* **④ 业务场景满足度 (Business Scenario Fulfillment)**:
  * 数据库设计是否能够清晰、高效地支持所有核心用户场景和业务流程的实现？
  * 每个表和关键字段的用途是否都能通过具体的业务场景得到验证？(参考模板第5章)
* **⑤ 可追溯性与依赖清晰度 (Traceability & Dependency Clarity)**:
  * 关键的表和字段设计是否可以追溯到特定的需求点或架构决策？
  * 表之间的数据依赖关系（逻辑外键、参照关系）是否清晰、明确，并已在文档中（如ER图、表间关系描述）体现？

---

### 8.2 数据库设计质量检查

| 检查维度                               | 评估结果 | 具体说明与改进建议                                                                                                  |
| -------------------------------------- | --------- | ----------------------------------------------------------------------------------------------------------------- |
| **⑥ 设计规范符合性** | ✅ | 整体设计严格遵循PostgreSQL和ClickHouse设计规范                                                                   |
|   - 命名规范                           | ✅ | 所有表名、字段名、索引名均符合snake_case命名规范                                                                 |
|   - 标准字段                           | ✅ | 所有表均包含完整的标准字段集                                                                                       |
|   - 数据类型选择                       | ✅ | 字段类型选择恰当，如NUMERIC用于掌握度                                                                               |
|   - 注释质量                           | ✅ | 所有表和字段均有清晰的中文注释                                                                                     |
|   - 通用设计原则与反模式规避             | ✅ | 无明显设计反模式，遵循单一职责原则                                                                                 |
| **⑦ 性能与可伸缩性考量** | ✅ | 索引设计合理，分区策略满足数据量增长需求                                                                           |
|   - 索引策略 (主键、排序键、跳数索引等)  | ✅ | 为高频查询条件建立索引，使用WHERE条件优化索引大小                                                                 |
|   - 分区/分片策略 (如果适用)             | ✅ | PostgreSQL使用TimescaleDB时间分区，ClickHouse按月分区                                                               |
|   - 表引擎选择 (尤其 ClickHouse)        | ✅ | ClickHouse表均使用MergeTree引擎，适合分析场景                                                                       |
|   - 数据压缩策略 (尤其 ClickHouse)      | ✅ | 使用默认压缩算法，在性能和存储间取得平衡                                                                           |
| **⑧ 可维护性与可扩展性** | ✅ | 表结构清晰，字段定义完整，易于理解和扩展                                                                       |
| **⑨ 数据完整性保障** | ✅ | 通过NOT NULL、DEFAULT等约束保障数据完整性                                                                         |
| **⑩ (可选) 安全性考量** | ✅ | 用户ID使用BIGINT类型，支持加密处理                                                                               |
| **其他特定于数据库的检查点** |  |                                                                                                                 |
|   - PostgreSQL: `TIMESTAMPTZ` 使用        | ✅ | 所有时间类型字段均使用TIMESTAMPTZ                                                                                  |
|   - PostgreSQL: `JSONB` 使用合理性        | N/A | 本设计未使用JSONB字段                                                                                              |
|   - ClickHouse: `LowCardinality` 使用     | ✅ | 事件类型等低基数字段使用LowCardinality优化                                                                          |
|   - ClickHouse: 物化视图设计合理性        | N/A | 本设计未使用物化视图                                                                                               |

**检查维度说明 (数据库设计质量):**
* **⑥ 设计规范符合性 (Adherence to Design Standards)**:
  * 是否严格遵守了项目既定的数据库设计规范（包括命名约定、数据类型选择、标准字段、索引策略、注释要求等）？
  * 是否遵循了通用的数据库设计原则和避免了常见的设计反模式？
* **⑦ 性能与可伸缩性考量 (Performance & Scalability Considerations)**:
  * 是否针对预估的数据量、并发访问及增长趋势，合理设计了索引、分区（如果适用）、表引擎（ClickHouse）、数据类型、压缩等以满足性能要求？
  * 设计中是否存在已知的或潜在的性能瓶颈或可伸缩性问题？
* **⑧ 可维护性与可扩展性 (Maintainability & Extensibility)**:
  * 表结构和字段定义是否清晰、易于理解，方便未来维护？
  * 当业务需求演进时，当前设计是否具备良好的可扩展性，能够以较小代价适应变化？
* **⑨ 数据完整性保障 (Data Integrity Assurance)**:
  * 是否通过恰当的约束（如NOT NULL, UNIQUE, CHECK约束 - PostgreSQL适用）和设计来保障数据的有效性和一致性？
  * 对于通过应用逻辑保障的数据完整性，表结构是否为此提供了必要的支持？
* **⑩ (可选) 安全性考量 (Security Considerations)**:
  * 是否识别了敏感数据字段？是否有关于数据分类、加密或脱敏的初步建议或标记（即便具体实现在应用层）？

## 9. 参考资料与附录

### 9.1 参考资料

1. PostgreSQL 15官方文档: https://www.postgresql.org/docs/15/
2. ClickHouse官方文档: https://clickhouse.com/docs/
3. TimescaleDB时间序列数据库文档: https://docs.timescale.com/
4. 《数据密集型应用系统设计》(Martin Kleppmann著)
5. 《PostgreSQL实战》(Bruce Momjian著)

### 9.2 术语表

| 术语 | 定义 |
|------|-----|
| 掌握度 | 用户对某个知识点的掌握程度，取值0-1 |
| 专注度 | 用户在练习过程中的专注程度，取值0-10 |
| 熔断机制 | 当用户练习状态异常时中断练习的保护机制 |
| 再练一次 | 针对错题进行的专项练习 |
| OLTP | 在线事务处理，主要指PostgreSQL库 |
| OLAP | 在线分析处理，主要指ClickHouse库 |

### 9.3 变更记录

| 版本 | 日期 | 变更人 | 变更内容 |
|------|------|-------|----------|
| V1.0 | 2024-08-03 | Claude 3.7 Sonnet | 初始版本 |
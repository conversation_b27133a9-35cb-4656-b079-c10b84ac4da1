const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')

const dev = process.env.NODE_ENV !== 'production'
const hostname = '0.0.0.0'
const port = parseInt(process.env.PORT || '80', 10)

console.log(`🚀 启动自定义服务器: ${hostname}:${port}`)

// 创建 Next.js 应用，不传递 hostname 和 port 参数
const app = next({ dev })
const handle = app.getRequestHandler()

app.prepare().then(() => {
  createServer(async (req, res) => {
    try {
      // Be sure to pass `true` as the second argument to `url.parse`.
      // This tells it to parse the query portion of the URL.
      const parsedUrl = parse(req.url, true)

      await handle(req, res, parsedUrl)
    } catch (err) {
      console.error('Error occurred handling', req.url, err)
      res.statusCode = 500
      res.end('internal server error')
    }
  })
    .once('error', (err) => {
      console.error('❌ 服务器启动失败:', err)
      process.exit(1)
    })
    .listen(port, hostname, () => {
      console.log(`✅ 服务器已启动: http://${hostname}:${port}`)
      console.log(`🌐 外部访问地址: http://ai01.local.xiaoluxue.cn`)
    })
})
# 产品需求文档：AI课中 - 课程框架与文档组件

**版本历史**

| 版本号 | 日期       | 变更人 | 变更类型 | 变更详情 |
| ------ | ---------- | ------ | -------- | -------- |
| V1.0   | 2025-04-08 | 钱晋菲 | 新建     | 新建文档 |

# 1. 项目概述

## 1.1. 项目背景

在AI课程的前期Demo版本（V1.0 & V1.1）的校园调研过程中，收集到了一系列影响用户体验的关键问题。这些问题主要集中在板书内容过多、高亮时机与教师讲解不同步、缺乏双击播放/暂停等快捷交互、以及自由/跟随模式切换不直观等方面。这些问题对课程的基础体验（包括学习与练习环节）造成了负面影响。为确保正式版本的课程质量，迫切需要优先解决这些体验问题。

此外，正式版本将对课中整体体验进行迭代升级，目标是使线上AI课程更接近1对1真人直播课的沉浸式体验，并突显系统的个性化和智能化特性。

## 1.2. 项目目标

1.  优化AI课中的基础学习体验，解决Demo版本中发现的板书、高亮、交互等问题。
2.  提升课程的沉浸感，使其更接近1对1真人直播课的体验。
3.  展示系统的个性化和智能化能力。
4.  为后续引入积分体系、小组战队、互动讲题、评论等高级功能奠定坚实的底层能力基础。

## 1.3. 用户价值/项目收益

1.  **提升学习效果**：通过优化课程框架和文档组件的交互与呈现，使学生能更专注于学习内容，减少操作障碍，从而提升学习效率和知识吸收效果。
2.  **提高课程满意度**：改善用户体验，提供更流畅、更智能、更具沉浸感的学习过程，从而提高学生对AI课程的整体满意度。
3.  **增强用户粘性**：优质的课程体验有助于吸引用户持续使用AI课进行学习。
4.  **奠定扩展基础**：打磨课中体验的底层能力，为未来快速迭代和引入更多互动、激励功能提供稳定支持。

## 1.4. 核心用户

使用新AI课的全量学生用户。

## 1.5. 方案简述

本方案聚焦于AI课中学习体验的升级，在保留原有课中基本结构的基础上，对课程框架和文档组件进行优化与能力扩展。主要包括：

*   **课程框架升级**：
    *   优化开场页设计，增强课程开始的仪式感。
    *   优化结算页设计，提供更全面的学习反馈和后续学习引导。
    *   支持课程进度在不同组件间的自由切换，并保存用户答题记录。
    *   优化退出和继续课程的体验。
*   **文档组件增强**：
    *   引入内容与勾画轨迹同步播放功能。
    *   未播放区域进行模糊/弱化处理，帮助学生聚焦。
    *   默认展示字幕。
    *   增加快捷交互功能，如双击播放/暂停、长按操作（本期部分实现）、倍速调整（增加1.75倍和3倍速）、10秒快进/快退。

本期工作重点在于打磨课中体验的底层能力，为后续引入积分体系、小组战队、互动讲题、评论等丰富玩法奠定基础。

# 2. 名词解释

| 名词         | 说明                                                                 |
| ------------ | -------------------------------------------------------------------- |
| **文档组件** | AI课程中承载图文、音视频等多媒体静态或动态教学内容的模块，支持学生自由浏览和提问。 |
| **练习组件** | AI课程中用于实现在线、可交互的练习题、测试题或操作型任务的模块。一期仅支持单选题。 |
| **答疑组件** | 基于大模型能力，在文档或练习组件下为学生提供实时对话、解答问题的模块。         |
| **跟随模式** | 文档组件的一种播放模式，数字人视频、勾画轨迹和JS动画内容自动同步播放。       |
| **自由模式** | 文款组件的一种播放模式，用户可自由滑动JS动画内容，数字人视频默认继续播放，板书不跟随。 |
| **JS动画**   | 指课程文档中呈现的动态板书内容。                                             |
| **勾画轨迹** | 教师在讲解过程中在板书上进行的勾画、标记的轨迹记录，会与讲解同步播放。         |
| **IP角色**   | AI课程中使用的具有特定形象和人设的虚拟角色，用于增强互动性和趣味性。         |
| **外化掌握度** | 系统向用户展示的对其知识点掌握情况的评估结果，强调非递减性以提供正向激励。     |

# 3. 业务流程

## 3.1. 整体课程结构流程

一节AI课由多个可配置的组件（如文档组件、练习组件）按特定顺序组合而成。答疑组件不能单独配置，需要挂载在文档组件或练习组件下。V1.0版本主要支持文档组件、练习组件及内嵌的答疑组件。

```mermaid
flowchart TD
    A[AI单节课开始] --> B[课程开场页（功能页面）]
    B --> C[课程引入（文档组件｜答疑组件）]
    C --> D[知识点1（文档组件｜答疑组件）]
    D --> E[知识点2（文档组件｜答疑组件）]
    E --> F[练习1（练习组件｜答疑组件）]
    F --> G[知识点3（文档组件｜答疑组件）]
    G --> H[知识点4（文档组件｜答疑组件）]
    H --> I[练习2（练习组件｜答疑组件）]
    I --> J[课程总结（文档组件｜答疑组件）]
    J --> K[学习报告页/结算页（功能页面）]
    K --> L[AI单节课结束]

    %% 关联组件说明
    subgraph '组件类型'
        direction LR
        Comp1[文档组件]
        Comp2[练习组件]
        Comp3[答疑组件]
        Comp4[功能页面]
    end
    classDef component fill:#ABEBC6,stroke:#333,stroke-width:2px;
    class Comp1,Comp2,Comp3,Comp4 component;

    C --- Comp1
    C --- Comp3
    D --- Comp1
    D --- Comp3
    E --- Comp1
    E --- Comp3
    F --- Comp2
    F --- Comp3
    G --- Comp1
    G --- Comp3
    H --- Comp1
    H --- Comp3
    I --- Comp2
    I --- Comp3
    J --- Comp1
    J --- Comp3
    B --- Comp4
    K --- Comp4
```

# 4. 功能范围

## 4.1. 核心功能

1.  **课程框架优化**：
    *   全新的开场页和结算页设计。
    *   支持课程进度在组件间自由切换，并保留用户学习记录。
    *   流畅的课程退出与续播体验。
2.  **文档组件体验升级**：
    *   JS内容与勾画轨迹同步播放。
    *   聚焦模式：未播放区域模糊/弱化处理。
    *   默认显示字幕。
    *   快捷交互：双击播放/暂停、1.75倍和3倍速播放、前进/后退10秒。

## 4.2. 辅助功能

1.  **长按交互**：文档组件中长按文档内容可选中文字/图片（为后续问一问、评论功能预留，本期不做评论）。
2.  **操作面板**：在文档组件中单击文档以外区域可唤起操作面板，进行播放控制。
3.  **模式切换**：文档组件支持跟随模式与自由模式的智能切换与手动操作。

# 5. 用户场景与故事

## 5.1. 场景：学生首次进入AI课程学习

### 5.1.1. 场景描述
学生小明首次点击进入一节新的AI数学课程。他期望有一个富有仪式感的开始，并能清晰地了解课程的结构和当前的学习内容。

### 5.1.2. 用户故事
作为一名学生，我希望当我开始一节新的AI课程时，能看到一个精美的开场动画和课程标题，让我感受到学习的仪式感。然后课程能自动引导我进入第一个学习环节，并且在学习过程中，我能清楚地知道当前学到哪里，整体进度如何。

### 5.1.3. 流程图 (Mermaid)
```mermaid
sequenceDiagram
    participant U as 用户（小明）
    participant App as AI课应用
    participant Server as 服务端

    U->>App: 点击进入新课程
    App->>Server: 请求课程开场信息
    Server-->>App: 返回课程开场页数据（如章节、课程名、IP动效资源）
    App->>App: 展示开场页（含IP动效、音频）
    App->>U: 播放开场动效和音频
    Note right of App: 动效播放完毕
    App->>Server: 请求课程第一个组件内容
    Server-->>App: 返回第一个组件数据（如文档内容、视频、勾画轨迹）
    App->>App: 进入第一个组件（如课程引入-文档组件）
    App->>U: 展示组件内容并开始播放（默认跟随模式）
```

## 5.2. 场景：学生在学习文档时进行交互操作

### 5.2.1. 场景描述
学生小红正在学习一篇包含JS动画和老师讲解的文档。她觉得老师讲得有点快，希望能暂停一下，或者调整播放速度。她还想回顾刚才讲过的一个知识点。

### 5.2.2. 用户故事
作为一名学生，我希望在观看课程文档时，可以通过双击屏幕来暂停或播放内容。如果我觉得语速不合适，我希望能方便地调整播放倍速。同时，我也希望能快速后退10秒来重听刚才的内容。

### 5.1.3. 流程图 (Mermaid)
```mermaid
stateDiagram-v2
    [*] --> 跟随模式播放中

    跟随模式播放中 --> 跟随模式播放中: 用户未操作
    跟随模式播放中 --> 暂停状态: 用户双击屏幕
    暂停状态 --> 跟随模式播放中: 用户双击屏幕
    跟随模式播放中 --> 操作面板展示: 用户单击文档以外区域
    操作面板展示 --> 跟随模式播放中: 用户点击屏幕其他区域（收起面板）
    操作面板展示 --> 倍速选择器展示: 用户点击倍速按钮
    倍速选择器展示 --> 跟随模式播放中: 用户选择新倍速（如1.75x）
    操作面板展示 --> 字幕关闭: 用户点击字幕按钮（若原为开启）
    操作面板展示 --> 字幕开启: 用户点击字幕按钮（若原为关闭）
    跟随模式播放中 --> 内容快退播放: 用户点击后退10s按钮
    内容快退播放 --> 跟随模式播放中: 播放跳转完成
    跟随模式播放中 --> 内容快进播放: 用户点击前进10s按钮
    内容快进播放 --> 跟随模式播放中: 播放跳转完成
    跟随模式播放中 --> 自由模式: 用户滑动JS动画内容
    自由模式 --> 跟随模式播放中: 用户点击"从这里学"
```

## 5.3. 场景：学生完成一节课的学习

### 5.3.1. 场景描述
学生小刚完成了本节AI语文课的所有学习内容和练习。他想知道自己这节课学得怎么样，有哪些知识点掌握了，以及接下来可以学些什么。

### 5.3.2. 用户故事
作为一名学生，我希望在我完成一节课后，能看到一个清晰的学习总结报告。报告中应该包含我的学习用时、答题情况（正确率）、知识点掌握度的变化，并且能给我一些后续学习的建议。我还希望可以对这节课的体验进行评价。

### 5.1.3. 流程图 (Mermaid)
```mermaid
sequenceDiagram
    participant U as 用户（小刚）
    participant App as AI课应用
    participant Server as 服务端

    App->>App: 检测到用户完成课程所有组件
    App->>Server: 请求结算页数据（含学习表现、掌握度、推荐等）
    Server-->>App: 返回结算页数据
    App->>U: 展示结算页（庆祝动效、学习表现、掌握度、答题情况、推荐学习、课程反馈入口）
    U->>App: 查看学习表现（用时、答题数、正确率）
    U->>App: 查看掌握度及提升
    U->>App: 查看答题情况（可进入题目列表）
    U->>App: 查看推荐学习内容
    U->>App: 点击"去练习"（推荐学习）
    App->>Server: 请求推荐练习数据
    Server-->>App: 返回练习数据
    App->>U: 进入推荐练习

    U->>App: 点击课程反馈ICON
    App->>U: 展示反馈弹窗（如"本节课感受如何？"及选项）
    U->>App: 选择反馈选项并提交
    App->>Server: 发送课程反馈数据
    Server-->>App: 反馈提交成功
    App->>U: Toast提示"感谢反馈"
    U->>App: 点击"完成"按钮
    App->>U: 返回课程入口页
```

## 5.4. 场景：学生在自由模式下浏览文档并切换回跟随模式

### 5.4.1. 场景描述
学生小芳在学习文档时，对某个历史知识点有些模糊，她通过滑动屏幕进入了自由模式，回顾了之前的内容。现在她想回到老师当前讲解的进度。

### 5.4.2. 用户故事
作为一名学生，当我自由浏览文档回顾内容后，我希望能轻松地回到老师正在讲解的进度，并让板书和勾画与老师的讲解同步。

### 5.4.3. 流程图 (Mermaid)
```mermaid
flowchart TD
    A["处于跟随模式，内容自动播放"] --> B["用户滑动JS动画内容"]
    B --> C["进入自由模式"]
    C --> D["数字人视频继续播放，板书不自动滚动，不播放勾画轨迹"]
    D --> E["用户找到想重新学习的句子并单击"]
    E --> F["高亮句子，显示从这里学按钮"]
    F --> G["用户点击从这里学按钮"]
    G --> H["切换回跟随模式"]
    H --> I["数字人视频、勾画轨迹从该句子对应时间戳开始播放，板书同步滚动"]
    I --> A
    C --> J["用户单击文档以外区域"]
    J --> K["唤起操作面板"]
    K --> C
```

# 6. 详细功能规格

## 6.1. 模块一：课程框架

### 6.1.1. 开场页
#### 6.1.1.1. 功能描述
*   **目的**：建立学习仪式感，提高学习专注度。
*   **页面展示**：
    *   不同学科可配置不同的开场页背景图和色值，但页面框架保持不变。
    *   显示章节信息（如"第一章"，后台配置）。
    *   显示课程序号（如"2.1.1"，后台配置）。
    *   显示课程名称（取对应业务树末级知识点名称，后台配置，25个汉字以内）。
    *   可包含简短的描述性文案。
*   **交互**：
    *   进入开场页后，自动播放IP动效和对应音频。
    *   动效和音频播放完成后，自动进入AI课的第一个小节（组件）。

#### *******. 交互说明
用户被动接收开场信息，无需操作，自动过渡到课程内容。

#### *******. 流程图 (Mermaid)
```mermaid
sequenceDiagram
    participant User as 用户
    participant Client as 客户端App
    participant Server as 服务端

    User->>Client: 进入课程
    Client->>Server: 请求开场页数据
    Server-->>Client: 返回开场页配置（章节、序号、名称、动效、音频资源）
    Client->>Client: 加载并展示开场页
    Client->>User: 播放IP动效及音频
    Note right of Client: 动效音频播放完毕
    Client->>Server: 请求课程首个组件数据
    Server-->>Client: 返回首个组件数据
    Client->>Client: 自动跳转至首个组件
    Client->>User: 展示首个组件内容并开始学习
```

### 6.1.2. 结算页
#### *******. 功能描述
*   **目的**：
    1.  情绪价值：恭喜用户完成课程。
    2.  效果反馈：展示本次学习表现。
    3.  学习引导：推荐后续学习路径。
*   **进入条件**：用户完成一节课中所有组件的学习后进入。
*   **核心内容**：
    *   **庆祝动效及文案**：
        *   **动效**：
            *   课程可包含多个IP角色，结算页随机选择一个IP进行展示。
            *   每个IP有"开心"、"鼓励"两类动效。
            *   动效展示策略：根据用户答题正确率判断。若正确率 > 70%，则掌握度提升，展示"开心"动效；否则掌握度不变（外化掌握度不下降），展示"鼓励"动效。
        *   **文案**：
            *   包含主文案和副文案。
            *   文案内容与IP角色相关，每个IP维护一套反馈文案库。
            *   根据用户的学习表现（如掌握度情况、正确率、学习时长等）和预设规则，展示不同的反馈文案。
            *   若同时命中多种情况，随机选择一种。若一种情况下有多条文案，随机选择一条。
    *   **学习表现**：
        *   学习用时：用户完成本节课的累计学习时长。
        *   答题数：用户在本节课本次学习中的累计答题数量。
        *   正确率：答对的题目数量 / 答题数量。
        *   注：已学完的课程重新学习，答题数量、正确率、时长均不累计。
    *   **外化掌握度**：
        *   显示当前知识点的外化掌握度百分比。
        *   显示本次学习带来的掌握度提升值。
        *   掌握度说明：提供"i"图标，点击可查看掌握度计算相关的说明信息（"掌握度反映了你对本节课的掌握情况，和你的答题正确率、题目难度相关。掌握度完成进度和你设定的学习目标相关，修改目标后进度会重新计算哦。"）。
        *   掌握度完成进度：用户当前掌握度 / 目标掌握度，保留小数点后两位，范围 [0, 1]。
        *   掌握度变化：本次学习后的进度 - 本次学习前的进度。
            *   初始掌握度不用于进度计算，用户首次学习的掌握度变化 = 本次学习后的进度 - 0。
            *   如果用户本次学习后实际掌握度下降，外化掌握度展示为不变化。
    *   **答题情况**：
        *   简要展示用户本次学习的答题正误概览（如用不同样式表示正确/错误题目标号）。
        *   题目数量超过一定阈值（如14个）时，页面可上划展示剩余题目。
        *   提供"查看详情"入口，点击进入题目列表页面。
    *   **推荐学习**：
        *   根据预设策略（基于掌握度是否达标、巩固练习完成情况、加练情况等），展示不同的学习模块名称（巩固练习、巩固练习-加练、拓展练习）和预估题目数量。
        *   若无推荐学习内容（如掌握度已达成目标），则展示默认文案"掌握度已达成，暂无推荐学习内容"。
        *   提供"去练习"入口，点击进入对应的学习模块。
    *   **课程反馈**：
        *   提供5类评价icon（极差、较差、一般、满意、很棒）。
        *   用户点击某个icon后，展示对应的反馈详情弹窗（包含针对性标签选择和开放式文本输入）。
        *   提交按钮默认不可点击，用户在弹窗中选择了任一反馈项后，切换为可提交状态。
        *   提交后，toast提示"感谢反馈，我们会持续改进！"。
    *   **操作按钮**：
        *   **回看课程**：点击后重新进入当前课程。所有组件均为已解锁状态，用户可自由跳转学习。保留历史答题记录，进入练习组件时直接展示答题结果页。
        *   **完成**：用户点击后返回课程列表页或上一级页面。

#### *******. 交互说明与流程
*   **结算动效与文案展示流程**
    ```mermaid
    graph TD
        A["用户完成课程所有组件"] --> B["评估学习数据"]
        B --> C["正确率 > 70% ?"]
        C -- 是 --> D["触发掌握度提升逻辑"]
        D --> E["随机选择IP角色"]
        E --> F["展示该IP的开心动效"]
        F --> G["根据学习表现匹配开心类反馈文案"]
        C -- 否 --> H["触发掌握度不变逻辑"]
        H --> I["随机选择IP角色"]
        I --> J["展示该IP的鼓励动效"]
        J --> K["根据学习表现匹配鼓励类反馈文案"]
        G --> L["展示主副文案"]
        K --> L
        L --> M["展示学习表现、掌握度等其他结算信息"]
    ```
*   **推荐学习决策流程**
    ```mermaid
    graph TD
        S[开始推荐学习决策] --> R1{掌握度是否达到目标掌握度?};
        R1 -- 是 --> R2[无推荐学习内容或推荐新策略];
        R1 -- 否 --> R3{用户是否完成巩固练习?};
        R3 -- 否 --> R4[推荐巩固练习];
        R3 -- 是 --> R5{是否有未完成的加练?};
        R5 -- 是 --> R6[推荐巩固练习加练];
        R5 -- 否 --> R7[推荐拓展练习];
        R2 --> E1[结束推荐];
        R4 --> E1;
        R6 --> E1;
        R7 --> E1;
    ```
*   **课程反馈提交流程**
    ```mermaid
    sequenceDiagram
        participant User as 用户
        participant Client as 客户端App
        participant Server as 服务端
        User->>Client: 在结算页点击反馈icon（如"满意"）
        Client->>Client: 展示反馈详情弹窗（含标签、输入框）
        User->>Client: 选择具体反馈标签/输入文字
        Note right of Client: 提交按钮变为可点击
        User->>Client: 点击"提交"按钮
        Client->>Server: 发送反馈数据（评级、标签、文本）
        Server-->>Client: 确认收到反馈
        Client->>User: Toast提示"感谢反馈..."
        Client->>Client: 关闭反馈弹窗
    ```

### 6.1.3. 课程进度
#### *******. 功能描述
*   **目的**：允许用户在任何组件中灵活调整学习进度，查看课程结构。
*   **触发**：用户在课中点击"课程进度"按钮。
*   **展示**：
    *   以列表形式展示当前课程的所有主要组件（文档组件、练习组件）。
    *   组件按实际课程顺序排列。
    *   每个组件显示其状态："已解锁"、"学习中"、"待解锁"。
        *   **已解锁**：用户已学习过的组件。
        *   **学习中**：用户当前正在学习的组件。
        *   **待解锁**：用户尚未学习的组件。（此状态可能根据课程设置调整，若课程允许自由跳转则可能只有已学和未学）
        *   若用户通过结算页"回看课程"进入，则所有组件均为"已解锁"状态。
*   **交互**：
    *   点击"课程进度"按钮，暂停当前组件的讲解/播放，并展示课程进度列表浮层。
    *   点击屏幕其他区域（非进度列表区域），收起进度列表浮层，继续当前组件的讲解/播放。
    *   点击"已解锁"状态的组件，可直接跳转到对应组件的内容。
*   **组件跳转逻辑**：
    *   **跳转到文档组件**：从上次跳出该组件的时间点开始播放。如果上次视频已播完，则自动从头开始播放。进入后默认为跟随模式。
    *   **跳转到练习组件**：
        *   如果上次该练习组件的题目没有答完，则从上次跳出的题目开始继续答题。
        *   如果上次该练习组件的题目已全部答完，则从第一题开始展示用户的历史答题记录页面。用户可点击"下一题"逐个查看，查看完毕后可进入课程中的下一个组件。
    *   用户跳转到之前学习过的组件，学习完成后，自动进入课程中的下一个组件（按原课程顺序）。
    *   组件间切换不影响整体学习进度的统计口径（仍按"已学完的组件数量 / 一节课中文档+练习组件的总数量"计算）。

#### 6.1.3.2. 流程图 (Mermaid)
```mermaid
flowchart TD
    A["用户在学习中"] --> B["点击课程进度按钮"]
    B --> C["暂停当前组件播放/讲解"]
    C --> D["显示课程进度列表浮层"]
    D --> E["用户点击已解锁的组件X"]
    E --> F["跳转至组件X"]
    F --> G["从上次退出点播放文档"]
    F --> H["根据上次答题情况进入练习"]
    G --> I["继续学习"]
    H --> I
    D --> J["用户点击屏幕其他区域"]
    J --> K["收起课程进度列表"]
    K --> L["继续播放/讲解当前组件"]
    L --> A
```

### 6.1.4. 退出/继续课程
#### 6.1.4.1. 功能描述
*   **退出**：
    *   用户在上课过程中，点击左上角的通用退出按钮。
    *   系统记录用户当前的准确学习进度（具体到组件、文档播放时间点、练习题号及作答状态）。
    *   返回课程入口页或上一级页面。
*   **继续课程（再次进入）**：
    *   **文档组件**：从上次退出的时间点继续播放，默认进入跟随模式。
    *   **练习组件**：进入用户退出时未答完的题目，并保存退出前的答题结果。若退出时正在查看解析，则回到该题目解析页面。
    *   **答疑组件**：默认不直接进入答疑组件。进入调起答疑组件的文档/练习组件。当用户再次手动点击答疑组件入口时，可查看历史对话记录。

#### *******. 流程图 (Mermaid)
```mermaid
sequenceDiagram
    participant User as 用户
    participant Client as 客户端App
    participant Server as 服务端

    User->>Client: 点击"退出课程"按钮
    Client->>Client: 获取当前学习进度（组件ID，播放/答题位置，状态等）
    Client->>Server: 保存用户学习进度
    Server-->>Client: 进度保存成功
    Client->>User: 返回课程入口页

    User->>Client: 再次点击进入该课程
    Client->>Server: 请求用户上次学习进度
    Server-->>Client: 返回学习进度数据
    Client->>Client: 根据进度类型恢复学习场景
    alt 文档组件
        Client->>User: 从上次退出时间点继续播放文档（跟随模式）
    else 练习组件
        Client->>User: 进入上次未答完的题目或题目解析页
    end
```

## 6.2. 模块二：文档组件

### 6.2.1. 模式切换（跟随/自由）
#### *******. 功能描述
*   **默认模式**：用户进入文档组件后，默认进入"跟随模式"。
*   **切换到自由模式**：当用户在"跟随模式"下主动滑动JS动画内容（板书）时，系统自动切换到"自由模式"。
    *   切换时，可能会有toast提示，如"已切换到自由模式，可自由浏览板书"。
*   **切换回跟随模式**：在"自由模式"下，用户单击板书中没有评论的文档内容（如句子、公式），会高亮该内容并出现"从这里学"按钮。点击"从这里学"按钮后，系统自动切换回"跟随模式"，数字人视频、勾画轨迹从该内容对应的时间戳开始播放。
*   **状态栏常驻**：无论何种模式，文档组件右上角常驻"问一问"（答疑入口）和"课程进度"按钮。

#### 6.2.1.2. 交互细节
*   **跟随模式**：
    *   数字人视频、勾画轨迹和JS动画内容（板书）自动同步播放，三者进度保持一致。
    *   播放时，板书当前讲解对应的内容块（如句子）下方会展示动态的进度线，指示当前讲解位置。
    *   若老师讲解内容在多个句子间跳跃，进度线始终在当前讲解的最后一个句子后，不跟随讲解频繁上下跳跃。
*   **自由模式**：
    *   数字人视频默认继续正常播放，其进度不受用户滑动板书影响。
    *   板书内容不跟随视频进度自动滚动播放，但仍会显示基于视频播放进度的进度线，提醒学生老师当前讲到了哪里。
    *   不播放勾画轨迹。
    *   **单击交互**：
        *   **单击没有评论的文档内容**：内容块高亮，出现"从这里学"按钮。
        *   **单击有评论的文档内容**：展示评论内容（本期不做）。
        *   **单击文档以外区域**：唤起操作面板。
    *   **长按交互**：
        *   **长按文档内容**：选中文字或图片内容，唤起操作菜单（本期菜单中仅"问一问"可用，为后续"评论"预留）。用户可拖拽高亮条调整选择范围。点击"问一问"进入答疑组件。
        *   **长按文档以外区域**：内容以3倍速播放，松手后恢复原始播放速度。
    *   **双击交互**：
        *   双击屏幕任意位置（文档内容或以外区域），切换播放/暂停状态（影响数字人视频、勾画轨迹、JS动画的同步播放/暂停）。

#### 6.2.1.3. 模式切换流程图 (Mermaid)
```mermaid
stateDiagram-v2
    state "跟随机同步播放" as Following
    state "自由浏览板书" as Free

    [*] --> Following: 进入文档组件
    Following --> Free: 用户滑动板书
    Free --> Following: 用户点击"从这里学"

    Following: 数字人、勾画、板书同步
    Following: 板书进度线跟随讲解
    Free: 数字人继续播放
    Free: 板书可自由滚动，不播放勾画
    Free: 仍显示基于视频的进度线
```

### 6.2.2. 内容播放
#### 6.2.2.1. 功能描述
*   **勾画轨迹同步**：在跟随模式下，增加勾画轨迹的播放，与JS内容（板书）和数字人视频同步。
*   **聚焦效果**：未播放到的板书区域呈现模糊或弱化效果，使用户注意力更集中在当前正在讲解的内容块上。
*   **默认字幕**：进入文档组件时，数字人视频的字幕默认开启展示。

### 6.2.3. 快捷交互
#### 6.2.3.1. 功能描述
以下快捷交互在跟随模式和自由模式下均支持：
*   **双击**：切换播放/暂停状态。
*   **长按文档以外区域**：内容以3倍速快进播放，松手后恢复原始速度。
*   **唤起操作面板**（通过单击文档以外区域）。操作面板包含：
    *   **字幕开关**：点击切换字幕的显示/隐藏。
    *   **播放/暂停按钮**：点击切换播放/暂停状态。
    *   **倍速调整**：点击弹出倍速选择器，支持0.75、1.0、1.25、1.5、1.75、2.0倍速。倍速设置全局生效于本节课中所有视频组件，退出课程后重进恢复正常速度。倍速调整需保持数字人、JS板书、勾画轨迹的播放进度一致。
    *   **前进/后退10秒**：点击后，数字人、JS板书、勾画轨迹进度同步变化。
*   **左上角退出学习按钮**：功能同6.1.4.1节。

### 6.2.4. 操作面板
#### 6.2.4.1. 功能描述
*   **唤起**：在跟随模式和自由模式下，单击文档以外的屏幕区域可唤起操作面板。
*   **收起**：操作面板唤起后，再次单击屏幕其他区域（非面板按钮区域）可收起操作面板。
*   **面板内容**：详见6.2.3.1节快捷交互中的操作面板描述。

### 6.2.5. 进入下一组件
#### 6.2.5.1. 功能描述
文档组件学习完成后，进入下一个课程组件的逻辑：
*   **从文档组件进入另一个文档组件**：
    *   在"跟随模式"下正常学习，当前文档组件的数字人视频（及关联勾画、板书）播放完毕后，自动进入下一个组件，无转场动效。进入下一组件后默认为跟随模式。
    *   在"跟随模式"和"自由模式"下，当用户向上滑动当前文档片段的JS动画至底部时，会出现"阻尼效果"。若用户继续向上滑动，则会切换至下一个组件的内容。进入下一组件后默认为跟随模式。
*   **从文档组件进入练习组件**：
    *   无论在"跟随模式"还是"自由模式"下，当文档组件学习结束或用户手动滑动切换时，若下一个组件是练习组件，则展示切换动效后进入练习组件。
*   **从练习组件进入文档组件** (作为参考，非本文档组件直接需求，但关联流程)：
    *   当练习组件完成并需要进入下一个文档组件时，展示切换动效后进入文档组件。

# 7. 数据处理规则

1.  **学习用时**：用户完成本节课学习的累计学习时长。
    *   已学完的课程重新学习，时长不累计。
2.  **答题数**：用户在这节课本次学习中的累计答题数量。
    *   已学完的课程重新学习，答题数量不累计。
3.  **正确率**：答对的题目数量 / 本次学习中答题总数量。
    *   已学完的课程重新学习，正确率不累计。
4.  **外化掌握度（结算页）**：
    *   **计算方式**：详见独立的《PRD - 掌握度策略 - V1.0》文档。
    *   **掌握度完成进度**：用户当前掌握度 / 目标掌握度。结果保留小数点后两位，取值范围 [0, 1]。
    *   **掌握度变化**：本次学习后的掌握度完成进度 - 本次学习前的掌握度完成进度。
        *   **首次学习**：用户首次学习该课程，其"本次学习前的掌握度完成进度"视为0。因此，掌握度变化 = 本次学习后的掌握度完成进度。
        *   **非首次学习且掌握度下降**：如果用户在本次学习后，其内部计算的实际掌握度相较于学习前有所下降，则在结算页对外展示的"外化掌握度"不应显示下降，而是保持学习前的状态（即掌握度变化显示为0或不显示提升）。
5.  **结算页动效展示策略**：
    *   若用户本次学习的整体答题正确率 > 70%，则触发"掌握度提升"的逻辑，并展示"开心"动效。
    *   若用户本次学习的整体答题正确率 <= 70%，则触发"掌握度不变"（外化）的逻辑，并展示"鼓励"动效。
6.  **结算页反馈文案展示规则**：
    *   文案库与IP角色关联。
    *   根据用户学习表现（掌握度、正确率、时长等组合条件）匹配对应文案。
    *   若命中多种情况，随机选择其一。
    *   若一种情况下有多条文案，随机选择一条。
7.  **课程进度计算**：已学完的组件数量 / 一节课中（文档组件 + 练习组件）的总数量。

# 8. 数据需求

## 8.1. 数据采集（埋点）

**通用要求**：
*   所有事件默认采集pv/uv。
*   每条事件自动带上`timestamp`字段。
*   隐式参数：`user_id`、`school_id`。

**具体埋点事件（部分示例，需进一步细化）**：
*   **课程框架相关**：
    *   `event_course_enter`: 用户进入课程。
    *   `event_opening_page_view`: 开场页曝光。
    *   `event_opening_animation_complete`: 开场动画播放完成。
    *   `event_settlement_page_view`: 结算页曝光。
    *   `event_settlement_feedback_submit`: 结算页反馈提交（含评级、标签、文本内容）。
    *   `event_settlement_recommend_click`: 结算页推荐学习点击（含推荐类型）。
    *   `event_course_progress_view`: 课程进度弹窗曝光。
    *   `event_course_progress_jump`: 课程进度组件跳转（含目标组件ID、类型）。
    *   `event_course_exit`: 用户退出课程（含退出时组件ID、进度）。
*   **文档组件相关**：
    *   `event_doc_component_enter`: 进入文档组件。
    *   `event_doc_mode_switch`: 模式切换（目标模式：自由/跟随）。
    *   `event_doc_play_pause`: 播放/暂停操作（双击或操作面板）。
    *   `event_doc_playback_speed_change`: 倍速切换（目标倍速）。
    *   `event_doc_seek`: 快进/快退10s操作。
    *   `event_doc_subtitle_toggle`: 字幕开关切换。
    *   `event_doc_longpress_3x_speed`: 长按3倍速播放（区分开始与结束）。
    *   `event_doc_fromhere_learn_click`: "从这里学"点击。
    *   `event_doc_ask_question_click`: 长按选中后点击"问一问"。
    *   `event_doc_scroll_to_next_component`: 滑动进入下一组件。
*   **公共**：
    *   `event_error_occurred`: 客户端/服务端错误事件。

## 8.2. 数据存储

| 模块       | 数据项                                                                | 说明                                                                         |
| ---------- | --------------------------------------------------------------------- | ---------------------------------------------------------------------------- |
| **整体课程** | 学生每节课的累计学习时长                                                |                                                                              |
|            | 学生每节课的累计答题数量                                                |                                                                              |
|            | 学生每节课的答题正确率                                                  |                                                                              |
|            | 学生每节课获得的积分（若未来引入）                                        |                                                                              |
|            | 学生每节课的学习进度                                                    | 计算口径同"课程进度"模块                                                       |
|            | 学生每节课的目标掌握度（若可配置）                                      |                                                                              |
|            | 学生每节课的实际外化掌握度（学习后）                                    |                                                                              |
|            | 用户对课程的反馈记录（评级、标签、文本）                                | 关联用户、课程、课节ID                                                       |
| **文档组件** | 每个文档内容的标准时长                                                  | 用于对比分析                                                                   |
|            | 用户在每个文档组件的实际播放时长                                        |                                                                              |
|            | 用户在文档组件中的模式切换记录                                          |                                                                              |
|            | 用户在文档组件中的交互记录（播放、暂停、倍速、seek、字幕、问一问等）    |                                                                              |
| **视频组件** | 每个视频的标准时长                                                      | (原始文档提及，此处文档组件主要指JS板书类，若有纯视频，则适用)                     |
|            | 用户在每个视频的实际播放时长                                            |                                                                              |
| **答疑组件** | 用户对话明细（用户问题、AI回答、时间戳）                                | 关联具体文档/练习的上下文                                                      |
|            | 每个会话的对话轮数                                                      | 每个视频/题目可视为一个新会话上下文                                              |
|            | 每一轮用户的输入方式（文本、语音等，若支持）                              |                                                                              |
| **练习组件** | 学生在每个练习组件的答题记录（题目ID、作答答案、是否正确、用时等）        | (虽然本文档主要涉课程框架和文档组件，但结算页等会用到练习数据，故列出作为关联) |

# 9. 未来展望

1.  **框架中加入积分体系和小组战队**：通过游戏化元素增强学习的趣味性和用户的持续参与动力。
2.  **支持互动讲题**：实现自动化、逐步式的题目讲解，并在讲解过程中穿插互动问题，检验学生理解程度，提升学习效果。
3.  **文档中长按评论**：允许学生在学习文档时，对特定内容进行长按并发表评论，促进学习交流和内容反馈。
4.  **增加费曼组件、互动组件**：引入更多创新的学习组件，如费曼技巧应用工具、投票、问卷等互动环节，丰富教学形式，提升学习主动性。

# 10. 需求检查清单
使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求                                                                                                                               | 对应需求点                                                    | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注                                                                                                                               |
| -------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------- | ------ | ------ | ------ | -------- | -------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| **课程框架 - 改动点**：1. 去掉每个组件之间的串场动效。 2. 结算页：增加"掌握度"、"答题情况"、"推荐学习"相关信息；鼓励文案展示规则调整。 3. 课程进度：保存用户答题记录，用户切换到已做过的练习模块时直接展示答题记录。 | 整体说明及各对应章节（如6.1.2 结算页, 6.1.3 课程进度, 6.2.5 进入下一组件） | ✅      | ✅      | ✅      | ✅        | ✅        | "去掉串场动效"体现在6.2.5中进入下一文档组件无转场。                                                                                |
| **课程框架 - 开场页**：不同学科不同开场页（背景图、色值），框架不变；显示章节、课程序号、课程名称（后台配置，25字内）；自动播放IP动效+音频，完成后自动进入第一小节。                               | 6.1.1. 开场页                                                 | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **课程框架 - 结算页 - 进入条件与作用**：完成一节课全部内容学习后进入；作用：恭喜、展示表现、推荐路径。                                                                       | 6.1.2. 结算页 (概述部分)                                      | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **课程框架 - 结算页 - 庆祝动效及文案**：动效（随机IP，开心/鼓励基于正确率>70%）；文案（主副，IP相关，据学习表现展示，随机）。                                                              | 6.1.2. 结算页 (庆祝动效及文案)                                  | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **课程框架 - 结算页 - 学习表现**：学习用时、答题数、正确率；已学完课程重新学习不累计。                                                                                     | 6.1.2. 结算页 (学习表现)                                      | ✅      | ✅      | ✅      | ✅        | ✅        | 数据处理规则在第7节。                                                                                                                  |
| **课程框架 - 结算页 - 外化掌握度**：显示百分比及提升值；掌握度说明（i图标）；完成进度（当前/目标，两位小数）；变化（本次后-本次前，首次视为0，下降则不变）。                                          | 6.1.2. 结算页 (外化掌握度) & 7. 数据处理规则                     | ⚠️      | ✅      | ✅      | ⚠️        | ✅        | "完整性"和"可验证性"为⚠️因为掌握度具体计算公式依赖外部文档《PRD - 掌握度策略 - V1.0》，新PRD已注明此依赖。                                    |
| **课程框架 - 结算页 - 答题情况**：正误样式展示；交互（超14个上划，点详情进列表，点题目进详情）。                                                                               | 6.1.2. 结算页 (答题情况)                                      | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **课程框架 - 结算页 - 题目列表（答题详情内）**：展示题号、类型、题干（2行）、作答结果（3种）；交互（筛选"仅看错题"，点击"查看解析"）。                                                              | 6.1.2. 结算页 (答题情况中关于题目列表的描述)                         | ⚠️      | ✅      | ✅      | ✅        | ✅        | "完整性"为⚠️，新PRD描述了入口和基本交互，但未完整展开题目列表页面的详细UI规格，可能依赖原始PRD中的图示。                                      |
| **课程框架 - 结算页 - 题目详情（答题详情内）**：用时、序号、加入错题本（按钮状态）、上/下一题按钮（边界条件）。                                                                       | 6.1.2. 结算页 (答题情况中关于题目详情的描述)                         | ⚠️      | ✅      | ✅      | ✅        | ✅        | "完整性"为⚠️，新PRD描述了入口和基本交互，但未完整展开题目详情页面的详细UI规格，可能依赖原始PRD中的图示。                                      |
| **课程框架 - 结算页 - 推荐学习**：据策略展示模块名、预估题数；无推荐时默认文案；用户点击"去练习"进入对应模块。                                                                         | 6.1.2. 结算页 (推荐学习)                                      | ✅      | ✅      | ✅      | ✅        | ✅        | 推荐策略的细节可能依赖外部配置或文档。                                                                                                 |
| **课程框架 - 结算页 - 课程反馈**：5类评价icon；点击icon展示弹窗（含标签、输入框）；提交按钮状态（选择后可点）；提交后toast提示。                                                                 | 6.1.2. 结算页 (课程反馈)                                      | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **课程框架 - 结算页 - 操作按钮**："回看课程"（全解锁，保留记录，进练习直接看结果），"完成"（返课程入口）。                                                                           | 6.1.2. 结算页 (操作按钮)                                      | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **课程框架 - 课程进度**：任何组件中调整学习进度；展示（列表，排序，状态：已解锁/学习中/待解锁，回看全解锁）；交互（点按钮展示/暂停，点其他区域收起/继续，点已解锁跳转）。                                 | 6.1.3. 课程进度                                               | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **课程框架 - 课程进度 - 组件跳转逻辑**：到文档（上次跳出点/从头，默认跟随）；到练习（上次未答完/全答完看记录）；完成后自动到下个；不影响进度统计。                                                        | 6.1.3. 课程进度 (组件跳转逻辑)                                  | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **课程框架 - 退出/继续课程**：退出（左上角，记录进度，返入口）；继续（文档：退出点继续，默认跟随；练习：退出时未答完题/解析页，保存结果；答疑：不直接进，点时看历史）。                                      | 6.1.4. 退出/继续课程                                          | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **文档组件 - 改动点**：1. 内容播放：勾画轨迹同步，默认字幕。 2. 交互操作：双击、长按，1.75/3倍速，前进/后退10s。                                                                       | 6.2. 文档组件 (概述及各子章节如 6.2.2, 6.2.3)                 | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **文档组件 - 模式切换 - 总体**：默认跟随模式；用户滑动JS动画内容时入自由模式（可有toast）；右上角常驻"问一问"和"课程进度"。                                                                  | 6.2.1. 模式切换（跟随/自由）                                  | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **文档组件 - 模式切换 - 跟随模式细节**：数字人视频、勾画轨迹、JS动画同步播放；板书内容块下方进度线；老师讲解跳跃，进度线在最后句子后。                                                              | 6.2.1. 模式切换（跟随/自由） (跟随模式交互细节)                   | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **文档组件 - 模式切换 - 自由模式细节**：数字人视频默认播放；板书不随进度自动播，但仍有进度线；不播勾画轨迹；单击无评论内容高亮并现"从这里学"（点后切回跟随）；单击有评论（本期不做）；单击文档外区域唤起操作面板。 | 6.2.1. 模式切换（跟随/自由） (自由模式交互细节)                   | ✅      | ✅      | ✅      | ✅        | ✅        | "单击有评论内容"明确本期不做。                                                                                                       |
| **文档组件 - 模式切换 - 自由模式长按/双击**：长按文档内容选中文字/图片，唤起问一问（评论不做）；长按文档外区域3倍速播放，松手恢复；双击切换播放/暂停。                                                      | 6.2.1. 模式切换（跟随/自由） (自由模式长按/双击) & 6.2.3 快捷交互 | ✅      | ✅      | ✅      | ✅        | ✅        | "评论不做"已明确。                                                                                                                 |
| **文档组件 - 内容播放**：勾画轨迹与JS内容同步播放；未播放区域模糊/弱化；默认展示字幕。                                                                                           | 6.2.2. 内容播放                                               | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **文档组件 - 快捷交互与操作面板**：包括双击切换播放/暂停、长按文档以外区域内容3倍速播放（松手恢复）、操作面板的唤起（单击文档以外区域）/收起、面板内容（字幕开关、播放/暂停按钮、倍速调整0.75-2.0全局、前进/后退10秒同步、左上角退出按钮）。 | 6.2.3. 快捷交互 & 6.2.4. 操作面板                             | ✅      | ✅      | ✅      | ✅        | ✅        | 合并了原快捷交互和操作面板的需求点。                                                                                                    |
| **文档组件 - 进入下一组件 - 文档到文档**：跟随模式播完自动进（无转场，默认跟随）；跟随/自由模式上滑到底，阻尼效果后继续上滑切换（默认跟随）。                                                               | 6.2.5. 进入下一组件 (从文档组件进入另一个文档组件)                | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |
| **文档组件 - 进入下一组件 - 文档到练习**：跟随/自由模式下，结束或手动切换，展示切换动效后进入练习。                                                                                     | 6.2.5. 进入下一组件 (从文档组件进入练习组件)                     | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                    |

- 完整性：原始需求是否都有对应的优化后需求点，无遗漏
- 正确性：优化后需求描述是否准确表达了原始需求的意图
- 一致性：优化后需求之间是否有冲突或重复  
- 可验证性：优化后需求是否有明确的验收标准
- 可跟踪性：优化后需求是否有明确的优先级和依赖关系

✅表示通过；❌表示未通过；⚠️表示描述正确，但细节不完整（如计算规则、阈值等，需要和 PM 进一步沟通）；N/A表示不适用。每个需求点都需要填写。

# 11. 附录
## 11.1. 原型图
原型图及UI界面截图已在本文档第6节"详细功能规格"的各对应需求描述中通过文字描述方式体现，或在原始需求文档 `PRD - AI课中 - 课程框架文档组件 V1.0_analyzed.md` 中以内嵌图片形式提供。原始需求文档中的图片链接和OCR分析内容可供进一步参考。

## 11.2. 术语表
本文档中涉及的专业术语已在第2节"名词解释"中列出。

## 11.3. 参考资料
- `PRD - AI课中 - 课程框架+文档组件 V1.0_analyzed.md` (原始需求文档)
- `ai-templates/prompts/design/be-s1-gen-ai-prd.md` (工作指南)
- `ai-templates/templates/prd/be-ai-prd-v2.2.md` (产品需求文档模板参考)
- 原始PRD中提及的关联需求（如DemoV1.0, DemoV1.1, 课程配置, 掌握度 V1.0, 课中-练习组件），具体文档链接未在此提供。
- 原始PRD中提及的外部链接（如AI课中需求盘点飞书文档、掌握度策略PRD飞书文档），因安全限制无法直接访问，相关信息依赖原始PRD内的文本描述。 
// @ts-nocheck
// @ts-nocheck
'use client';

import React, { useEffect, useRef } from 'react';

interface WorkflowModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const WorkflowModal = ({ isOpen, onClose }: WorkflowModalProps) => {
  const modalRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭弹窗
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div 
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl overflow-hidden"
      >
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-medium">工作流设计器</h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        <div className="p-6 overflow-auto max-h-[80vh]">
          <div className="flex flex-col items-center bg-gray-50 rounded-lg p-4 min-h-[600px] overflow-auto relative">
            {/* 背景网格 */}
            <div className="absolute inset-0" style={{ 
              backgroundImage: 'radial-gradient(circle, #e5e7eb 1px, transparent 1px)', 
              backgroundSize: '20px 20px' 
            }}></div>
            
            {/* 流程图内容 */}
            <div className="w-full max-w-4xl relative z-10 min-h-[580px]">
              {/* 流程发起节点 */}
              <div className="absolute top-8 left-1/2 transform -translate-x-1/2">
                <div className="flex items-center bg-white rounded-full px-4 py-2 shadow-md">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-gray-800 font-medium">流程发起</span>
                </div>
              </div>
              
              {/* 连接线 */}
              <div className="absolute top-20 left-1/2 transform -translate-x-1/2 h-20 w-0.5 bg-gray-300"></div>
              
              {/* 新增数据节点 */}
              <div className="absolute top-40 left-20">
                <div className="bg-white rounded-lg shadow-md p-4 w-64">
                  <div className="flex items-center mb-2">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span className="text-gray-800 font-medium">新增数据</span>
                  </div>
                  
                  <div className="ml-8 bg-gray-50 rounded-lg p-3 mb-2">
                    <div className="bg-gray-200 rounded px-2 py-1 text-xs inline-block mr-1">在</div>
                    <div className="bg-gray-200 rounded px-2 py-1 text-xs inline-block mr-1">消息通知</div>
                    <div className="bg-gray-200 rounded px-2 py-1 text-xs inline-block">中新增数</div>
                  </div>
                  
                  <div className="ml-8 text-gray-600 text-sm">填写了3个字段</div>
                </div>
              </div>
              
              {/* 连接线 - 从新增数据到其他节点 */}
              <div className="absolute top-80 left-52 h-40 w-0.5 bg-gray-300"></div>
              
              {/* 数据处理区域 */}
              <div className="absolute top-40 right-20">
                <div className="bg-white rounded-lg shadow-md p-4 w-72">
                  <div className="text-gray-800 font-medium mb-3">数据处理</div>
                  
                  <div className="ml-2 space-y-3">
                    {/* 新增数据按钮 */}
                    <div className="flex items-center p-2 bg-gray-50 rounded-md">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span>新增数据</span>
                    </div>
                    
                    {/* 删除数据按钮 */}
                    <div className="flex items-center p-2 bg-gray-50 rounded-md">
                      <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span>删除数据</span>
                    </div>
                    
                    {/* 查询数据按钮 */}
                    <div className="flex items-center p-2 bg-gray-50 rounded-md">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span>查询数据</span>
                    </div>
                    
                    {/* 更新数据按钮 */}
                    <div className="flex items-center p-2 bg-gray-50 rounded-md">
                      <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 100-2h5a1 1 0 011 1v5a1 1 0 10-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <span>更新数据</span>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 判断节点 */}
              <div className="absolute top-[160px] left-[80px]">
                <div className="bg-white p-3 rounded-lg shadow-md mb-4">
                  <div className="text-gray-700">判断</div>
                </div>
              </div>
              
              {/* 条件分支节点 */}
              <div className="absolute top-[200px] left-[80px]">
                <div className="bg-white p-3 rounded-lg shadow-md">
                  <div className="flex items-center">
                    <div className="w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center mr-2">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 3a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2V5a2 2 0 00-2-2H5zm0 2h10v7h-2l-1 2H8l-1-2H5V5z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <span>条件分支</span>
                  </div>
                </div>
              </div>
              
              {/* 流程结束节点 */}
              <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
                <div className="flex items-center bg-white rounded-full px-4 py-2 shadow-md">
                  <div className="w-6 h-6 bg-red-500 rounded-full flex items-center justify-center mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span className="text-gray-800 font-medium">流程结束</span>
                </div>
              </div>
              
              {/* 业务节点区域 */}
              <div className="absolute bottom-[100px] right-[40px]">
                <div className="bg-white p-3 rounded-lg shadow-md mb-4">
                  <div className="text-gray-700">业务节点</div>
                </div>
              </div>
              
              {/* 消息通知节点 */}
              <div className="absolute bottom-[180px] left-[40px]">
                <div className="bg-white p-3 rounded-lg shadow-md mb-4 flex items-center">
                  <div className="w-6 h-6 bg-cyan-500 rounded-full flex items-center justify-center mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M18 5v8a2 2 0 01-2 2h-5l-5 4v-4H4a2 2 0 01-2-2V5a2 2 0 012-2h12a2 2 0 012 2zM7 8H5v2h2V8zm2 0h2v2H9V8zm6 0h-2v2h2V8z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>消息通知</span>
                </div>
              </div>
              
              {/* 企业微信节点 */}
              <div className="absolute bottom-[140px] right-[60px]">
                <div className="bg-white p-3 rounded-lg shadow-md mb-4">
                  <div className="text-gray-700">企业微信</div>
                </div>
              </div>
              
              {/* 日程管理节点 */}
              <div className="absolute bottom-[80px] right-[140px]">
                <div className="bg-white p-3 rounded-lg shadow-md mb-4 flex items-center">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>日程管理</span>
                </div>
              </div>
              
              {/* 群机器人节点 */}
              <div className="absolute bottom-[40px] right-[30px]">
                <div className="bg-white p-3 rounded-lg shadow-md mb-4 flex items-center">
                  <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-white" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <span>群机器人</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="p-4 border-t border-gray-200 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            确定
          </button>
        </div>
      </div>
    </div>
  );
};

export default WorkflowModal; 
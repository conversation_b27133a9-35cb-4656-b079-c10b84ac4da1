// @ts-nocheck
'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import { formatUrlForDisplay } from '@/lib/workflow-utils';
import {
  Box,
  Button,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Text,
  VStack,
  HStack,
  Badge,
  Collapse,
  Code,
  Divider
} from '@chakra-ui/react';
import { ChevronDownIcon, ChevronUpIcon } from '@chakra-ui/icons';

interface WorkflowDebugPanelProps {
  pageName: string;
}

export default function WorkflowDebugPanel({ pageName }: WorkflowDebugPanelProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [showDebug, setShowDebug] = useState(false);
  const [showToggleButton, setShowToggleButton] = useState(false);
  const { workflowData, getHiddenInputsData } = useWorkflowData(pageName);
  
  let searchParams;
  try {
    searchParams = useSearchParams();
  } catch (error) {
    searchParams = null;
  }

  // 检查debug参数和开发环境
  useEffect(() => {
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    if (searchParams) {
      const debugParam = searchParams.get('debug');
      setShowDebug(debugParam === '1');
      setShowToggleButton(isDevelopment && debugParam !== '1');
    } else if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      const debugParam = urlParams.get('debug');
      setShowDebug(debugParam === '1');
      setShowToggleButton(isDevelopment && debugParam !== '1');
    }
  }, [searchParams]);

  // 切换调试模式
  const toggleDebugMode = () => {
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      url.searchParams.set('debug', '1');
      window.location.href = url.toString();
    }
  };

  // 获取当前页面的URL参数
  const currentUrl = typeof window !== 'undefined' ? window.location.href : '';
  const urlParams = typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : new URLSearchParams();
  
  // 统计有效参数
  const validParams = Object.entries(workflowData).filter(([_, value]) => value && value.trim() !== '');
  const totalParams = Object.keys(workflowData).length;
  
  // 如果显示调试面板
  if (showDebug) {
    return (
      <Box position="fixed" top="20px" right="20px" zIndex={1000} maxWidth="400px">
        <Card bg="blue.50" borderColor="blue.200" borderWidth="2px">
          <CardHeader pb={2}>
            <HStack justify="space-between" align="center">
              <VStack align="start" spacing={0}>
                <Heading size="sm" color="blue.700">
                  🔍 工作流调试面板
                </Heading>
                <Text fontSize="xs" color="gray.600">
                  当前页面: {pageName}
                </Text>
              </VStack>
              <HStack>
                <Badge colorScheme="green" variant="solid">
                  {validParams.length}/{totalParams} 参数
                </Badge>
                <Button
                  size="xs"
                  onClick={() => setIsOpen(!isOpen)}
                  leftIcon={isOpen ? <ChevronUpIcon /> : <ChevronDownIcon />}
                >
                  {isOpen ? '收起' : '展开'}
                </Button>
              </HStack>
            </HStack>
          </CardHeader>
          
          <Collapse in={isOpen}>
            <CardBody pt={0}>
              <VStack align="stretch" spacing={4}>
                
                {/* 当前URL显示 */}
                <Box>
                  <Text fontSize="sm" fontWeight="bold" color="blue.700" mb={2}>
                    📍 当前页面URL:
                  </Text>
                  <Code fontSize="xs" p={2} bg="gray.100" borderRadius="md" wordBreak="break-all">
                    {currentUrl}
                  </Code>
                </Box>

                <Divider />

                {/* 接收到的参数 */}
                <Box>
                  <Text fontSize="sm" fontWeight="bold" color="green.700" mb={2}>
                    📥 接收到的工作流参数:
                  </Text>
                  {validParams.length > 0 ? (
                    <VStack align="stretch" spacing={2}>
                      {validParams.map(([key, value]) => (
                        <Box key={key} bg="white" p={2} borderRadius="md" borderWidth="1px">
                          <HStack justify="space-between" align="start">
                            <Text fontSize="xs" fontWeight="bold" color="gray.700" minWidth="100px">
                              {key}:
                            </Text>
                            <Text fontSize="xs" color="gray.600" flex="1" wordBreak="break-all">
                              {formatUrlForDisplay(value, 40)}
                            </Text>
                          </HStack>
                        </Box>
                      ))}
                    </VStack>
                  ) : (
                    <Text fontSize="xs" color="gray.500" fontStyle="italic">
                      暂无参数传递到当前页面
                    </Text>
                  )}
                </Box>

                <Divider />

                {/* URL查询参数 */}
                <Box>
                  <Text fontSize="sm" fontWeight="bold" color="orange.700" mb={2}>
                    🔗 URL查询参数:
                  </Text>
                  {Array.from(urlParams.entries()).length > 0 ? (
                    <VStack align="stretch" spacing={1}>
                      {Array.from(urlParams.entries()).map(([key, value]) => (
                        <HStack key={key} justify="space-between" fontSize="xs">
                          <Text fontWeight="bold" color="gray.700" minWidth="80px">
                            {key}:
                          </Text>
                          <Text color="gray.600" flex="1" wordBreak="break-all">
                            {formatUrlForDisplay(decodeURIComponent(value), 30)}
                          </Text>
                        </HStack>
                      ))}
                    </VStack>
                  ) : (
                    <Text fontSize="xs" color="gray.500" fontStyle="italic">
                      当前URL无查询参数
                    </Text>
                  )}
                </Box>

                {/* 隐藏元素数据 */}
                <Box>
                  <Text fontSize="sm" fontWeight="bold" color="purple.700" mb={2}>
                    🔒 隐藏元素数据:
                  </Text>
                  <Text fontSize="xs" color="gray.600">
                    共 {getHiddenInputsData().length} 个隐藏input元素用于数据持久化
                  </Text>
                </Box>

                {/* 操作按钮 */}
                <Box>
                  <HStack spacing={2}>
                    <Button
                      size="xs"
                      colorScheme="blue"
                      onClick={() => {
                        console.log('工作流数据:', workflowData);
                        console.log('URL参数:', Object.fromEntries(urlParams.entries()));
                      }}
                    >
                      打印到控制台
                    </Button>
                    <Button
                      size="xs"
                      colorScheme="green"
                      onClick={() => {
                        const data = {
                          pageName,
                          workflowData,
                          urlParams: Object.fromEntries(urlParams.entries()),
                          currentUrl
                        };
                        navigator.clipboard.writeText(JSON.stringify(data, null, 2));
                        alert('调试数据已复制到剪贴板');
                      }}
                    >
                      复制数据
                    </Button>
                  </HStack>
                </Box>
              </VStack>
            </CardBody>
          </Collapse>
        </Card>
      </Box>
    );
  }

  // 如果在开发环境且没有debug参数，显示调试开关按钮
  if (showToggleButton) {
    return (
      <Box position="fixed" bottom="20px" right="20px" zIndex={1000}>
        <Button
          size="sm"
          colorScheme="blue"
          variant="solid"
          onClick={toggleDebugMode}
          leftIcon={<span>🔍</span>}
          boxShadow="lg"
        >
          开启调试
        </Button>
      </Box>
    );
  }

  return null;
}
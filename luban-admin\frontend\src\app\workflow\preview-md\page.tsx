// @ts-nocheck
'use client';

export const dynamic = 'force-dynamic';

import { useState, useEffect, Suspense } from 'react';
import { useSearchParams } from 'next/navigation';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Spinner } from '@chakra-ui/react';
import WorkflowPageWrapper from '@/components/workflow/WorkflowPageWrapper';

/**
 * Markdown预览页面
 * 独立页面，用于渲染Markdown文件，支持mermaid图表
 * URL参数：url - 要渲染的Markdown文件的URL
 */
function PreviewMdPageContent() {
  const searchParams = useSearchParams();
  const [markdown, setMarkdown] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [url, setUrl] = useState<string>('');

  // 从URL参数中获取Markdown文件的URL
  useEffect(() => {
    const mdUrl = searchParams.get('url');
    if (mdUrl) {
      // 检查URL是否是相对路径或绝对路径
      if (mdUrl.startsWith('/')) {
        // 相对路径，添加当前主机
        const baseUrl = window.location.origin;
        setUrl(`${baseUrl}${mdUrl}`);
      } else if (!mdUrl.startsWith('http')) {
        // 不是http开头，也不是/开头，可能是相对路径
        const baseUrl = window.location.origin;
        setUrl(`${baseUrl}/${mdUrl}`);
      } else {
        // 绝对路径，直接使用
        setUrl(mdUrl);
      }
      console.log('设置URL参数:', mdUrl);
    } else {
      setError('未提供Markdown文件URL，请在URL参数中添加url参数');
      setIsLoading(false);
    }
  }, [searchParams]);

  // 在组件挂载后初始化 Mermaid
  useEffect(() => {
    // 添加 Mermaid 脚本到页面
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';
    script.async = true;
    script.onload = () => {
      console.log('Mermaid script loaded');
      
      // 初始化 Mermaid
      if (window.mermaid) {
        try {
          console.log('Initializing mermaid');
          window.mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
              htmlLabels: true,
              useMaxWidth: true,
            },
          });
        } catch (error) {
          console.error('Error initializing mermaid:', error);
        }
      }
    };
    document.head.appendChild(script);
    
    // 清理函数
    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  // 在 Markdown 内容变化后重新渲染 Mermaid 图表
  useEffect(() => {
    if (!isLoading && markdown && window.mermaid) {
      // 给 DOM 一些时间来渲染
      const timer = setTimeout(() => {
        try {
          console.log('Running mermaid');
          window.mermaid.run();
        } catch (error) {
          console.error('Error running mermaid:', error);
        }
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [isLoading, markdown]);

  // 获取Markdown内容
  useEffect(() => {
    const fetchMarkdown = async () => {
      if (!url) return;
      
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('Fetching markdown from URL:', url);
        
        // 检查是否需要使用代理
        // 为了避免CORS问题，以下情况使用代理：
        // 1. 不同主机的URL
        // 2. 同一主机但不同端口的URL（跨端口也会有CORS问题）
        const urlObj = new URL(url);
        const currentOrigin = window.location.origin; // 包含协议、主机和端口
        const targetOrigin = urlObj.origin;
        
        // 如果origin不同（包括端口不同），则需要使用代理
        const needProxy = targetOrigin !== currentOrigin;
        console.log('当前origin:', currentOrigin, '目标origin:', targetOrigin, '需要代理:', needProxy);
        
        // 如果需要代理，使用代理路由；否则直接访问
        const fetchUrl = needProxy
          ? `/api/documents/proxy?url=${encodeURIComponent(url)}`
          : url;
        
        console.log('使用URL获取内容:', fetchUrl);
        
        // 添加超时处理
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15秒超时
        
        const response = await fetch(fetchUrl, {
          method: 'GET',
          headers: {
            'Accept': 'text/markdown, text/plain, */*',
            'Cache-Control': 'no-cache'
          },
          credentials: 'include', // 包含cookies
          signal: controller.signal // 添加AbortController信号
        });
        
        clearTimeout(timeoutId); // 清除超时
        
        if (!response.ok) {
          throw new Error(`获取Markdown内容失败: ${response.status} ${response.statusText}`);
        }
        
        const text = await response.text();
        console.log('Markdown内容获取成功，长度:', text.length);
        
        // 检查内容是否为空
        if (!text || text.trim() === '') {
          throw new Error('Markdown内容为空');
        }
        
        // 检查内容是否为HTML错误页面
        if (text.includes('<html') && text.includes('<body') && (text.includes('Error') || text.includes('error'))) {
          throw new Error('收到HTML错误页面而不是Markdown内容');
        }
        
        setMarkdown(text);
        setIsLoading(false);
      } catch (err) {
        console.error('获取Markdown内容失败:', err);
        
        // 如果是外部URL且获取失败，尝试直接在新窗口打开
        if (url && !url.startsWith(window.location.origin)) {
          console.log('尝试直接打开外部URL:', url);
          window.open(url, '_blank');
          setError('无法在预览模式下显示此内容，已在新窗口中打开原始链接');
        } else {
          setError(err instanceof Error ? err.message : '发生未知错误');
        }
        
        setIsLoading(false);
      }
    };

    if (url) {
      fetchMarkdown();
    } else {
      setIsLoading(false);
    }
  }, [url]);

  // 添加全局样式
  useEffect(() => {
    // 添加GitHub风格的Markdown样式
    const style = document.createElement('link');
    style.rel = 'stylesheet';
    style.href = 'https://cdn.jsdelivr.net/npm/github-markdown-css@5.2.0/github-markdown.min.css';
    document.head.appendChild(style);

    // 添加自定义样式
    const customStyle = document.createElement('style');
    customStyle.textContent = `
      body {
        margin: 0;
        padding: 0;
        background-color: #f8f9fa;
      }
      .markdown-container {
        padding: 2rem;
        max-width: 1200px;
        margin: 0 auto;
        background-color: white;
        min-height: 100vh;
      }
      .markdown-body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
        font-size: 16px;
        line-height: 1.6;
        word-wrap: break-word;
      }
      .markdown-body pre {
        background-color: #f6f8fa;
        border-radius: 3px;
        padding: 16px;
        overflow: auto;
      }
      .mermaid {
        text-align: center;
      }
      .error-container {
        max-width: 800px;
        margin: 100px auto;
        padding: 2rem;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;
      }
    `;
    document.head.appendChild(customStyle);

    return () => {
      if (style.parentNode) {
        style.parentNode.removeChild(style);
      }
      if (customStyle.parentNode) {
        customStyle.parentNode.removeChild(customStyle);
      }
    };
  }, []);

  if (isLoading) {
    return (
      <div className="fixed inset-0 flex items-center justify-center bg-white">
        <div className="text-center p-8 rounded-lg shadow-lg bg-white border border-gray-100 max-w-md">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          </div>
          <h2 className="text-xl font-semibold text-gray-800 mb-2">加载中</h2>
          <p className="text-gray-600">正在加载 Markdown 内容，请稍候...</p>
          {url && (
            <div className="mt-4 text-xs text-gray-500 truncate max-w-xs mx-auto">
              <span className="font-medium">文档地址:</span> {url}
            </div>
          )}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <h2 style={{ color: '#e53e3e', marginBottom: '1rem' }}>加载失败</h2>
        <p style={{ marginBottom: '1rem' }}>{error}</p>
        {url && (
          <div>
            <p style={{ marginBottom: '1rem' }}>您可以尝试直接访问原始链接：</p>
            <a 
              href={url} 
              target="_blank" 
              rel="noopener noreferrer"
              style={{ color: '#3182ce', textDecoration: 'underline' }}
            >
              {url}
            </a>
          </div>
        )}
      </div>
    );
  }

  return (
    <>
      {/* 添加内联脚本确保 Mermaid 初始化 */}
      <div dangerouslySetInnerHTML={{ __html: `
        <script>
          document.addEventListener('DOMContentLoaded', function() {
            if (typeof mermaid !== 'undefined') {
              console.log('Inline script: initializing mermaid');
              mermaid.initialize({
                startOnLoad: true,
                theme: 'default'
              });
              setTimeout(function() {
                console.log('Inline script: running mermaid');
                mermaid.run();
              }, 1000);
            } else {
              console.log('Inline script: mermaid not loaded yet');
            }
          });
        </script>
      ` }} />
      
      <div className="markdown-container">
        <ReactMarkdown
          className="markdown-body"
          remarkPlugins={[remarkGfm]}
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              
              // 处理 Mermaid 图表
              if (!inline && match && (match[1] === 'mermaid' || match[1] === 'flowchart')) {
                const mermaidContent = String(children).replace(/\n$/, '');
                
                return (
                  <div className="my-4">
                    <pre className="mermaid">
                      {mermaidContent}
                    </pre>
                  </div>
                );
              }
              
              // 处理其他代码块
              return !inline && match ? (
                <SyntaxHighlighter
                  style={vscDarkPlus}
                  language={match[1]}
                  PreTag="div"
                  {...props}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              ) : (
                <code className={className} {...props}>
                  {children}
                </code>
              );
            },
          }}
        >
          {markdown}
        </ReactMarkdown>
      </div>
    </>
  );
}

// 为 TypeScript 添加全局类型声明
declare global {
  interface Window {
    mermaid: {
      initialize: (config: any) => void;
      init: (config?: any, nodes?: NodeListOf<Element>) => void;
      run: () => void;
    };
  }
}

// 导出包装后的组件
export default function PreviewMdPage() {
  return (
    <WorkflowPageWrapper>
      <PreviewMdPageContent />
    </WorkflowPageWrapper>
  );
}
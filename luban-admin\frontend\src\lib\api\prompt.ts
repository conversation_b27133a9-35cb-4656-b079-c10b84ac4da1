import { api } from './client';

// Prompt类型定义
interface PromptVariable {
  name: string;
  description: string;
  default_value: string | null;
}

interface PromptSection {
  title: string;
  level: number;
  content: string;
  variables: string[];
}

interface PromptDetail {
  id: string;
  title: string;
  category: string;
  name: string;
  variables: PromptVariable[];
  sections: PromptSection[];
  raw_content: string;
}

interface PromptInfo {
  name: string;
  title: string;
  variables: string[];
}

interface PromptCategory {
  name: string;
  description: string;
  prompts: PromptInfo[];
}

interface PromptTestRequest {
  prompt_id: string;
  variables: Record<string, string>;
  model?: string;
}

interface PromptTestResponse {
  prompt_id: string;
  system_prompt: string;
  user_prompt: string;
  response: string;
}

// 获取所有Prompt分类
export const getAllCategories = async (): Promise<PromptCategory[]> => {
  return api.get<PromptCategory[]>('/prompts/categories');
};

// 获取分类下的所有Prompt
export const getCategoryPrompts = async (category: string): Promise<PromptCategory> => {
  return api.get<PromptCategory>(`/prompts/categories/${category}`);
};

// 获取Prompt详情
export const getPromptDetail = async (category: string, name: string): Promise<PromptDetail> => {
  return api.get<PromptDetail>(`/prompts/${category}/${name}`);
};

// 创建新Prompt
export const createPrompt = async (
  category: string,
  name: string,
  content: string
): Promise<PromptDetail> => {
  return api.post<PromptDetail>(`/prompts/${category}/${name}`, { content });
};

// 更新Prompt
export const updatePrompt = async (
  category: string,
  name: string,
  content: string
): Promise<PromptDetail> => {
  return api.put<PromptDetail>(`/prompts/${category}/${name}`, { content });
};

// 删除Prompt
export const deletePrompt = async (category: string, name: string): Promise<{ message: string }> => {
  return api.delete<{ message: string }>(`/prompts/${category}/${name}`);
};

// 测试Prompt
export const testPrompt = async (request: PromptTestRequest): Promise<PromptTestResponse> => {
  return api.post<PromptTestResponse>('/prompts/test', request);
};

// 创建新分类
export const createCategory = async (category: string): Promise<{ message: string }> => {
  return api.post<{ message: string }>(`/prompts/categories/${category}`, {});
};

// 删除分类
export const deleteCategory = async (
  category: string,
  force: boolean = false
): Promise<{ message: string }> => {
  const queryParams = force ? '?force=true' : '';
  return api.delete<{ message: string }>(`/prompts/categories/${category}${queryParams}`);
};

export const promptApi = {
  getAllCategories,
  getCategoryPrompts,
  getPromptDetail,
  createPrompt,
  updatePrompt,
  deletePrompt,
  testPrompt,
  createCategory,
  deleteCategory,
};

export type {
  PromptVariable,
  PromptSection,
  PromptDetail,
  PromptInfo,
  PromptCategory,
  PromptTestRequest,
  PromptTestResponse,
}; 
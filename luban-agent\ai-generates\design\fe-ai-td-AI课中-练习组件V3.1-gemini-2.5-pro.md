# 前端架构设计模板 (精致专业版 v3.2 - 页面核心与复用)

**项目名称**: `AI课中-练习组件V1.1`
**文档版本**: `1.0`
**架构师**: `前端架构师`
**创建日期**: `2023-12-01`

## 1. 架构概览

### 1.1 业务目标与技术愿景
本项目旨在优化AI课中练习环节的用户体验，重点提升作答流程的流畅度和即时反馈的有效性与情感化。通过打造高效、流畅、情感丰富的练习组件，提高练习题完成率和用户满意度，实现增强学习参与感和自信心，提升知识巩固效果。

技术实现愿景是构建一个响应迅速、交互流畅、用户体验卓越的练习组件，通过精心设计的转场动效、即时反馈机制和用户友好的交互界面，让学习过程更加愉悦，同时确保代码模块化、可测试性和可维护性。

### 1.2 核心技术栈
* 基础框架：React (v19) / Next.js (v15) (App Router)
* 状态管理：@preact-signals/safe-react / SWR 
* UI框架：基于radix-ui
* 样式方案：Tailwind CSS
* 数据处理：Zod / SWR / Axios

### 1.3 架构核心原则
1. **MVVM严格分层**: 严格遵循Model-ViewModel-View分离，保持各层职责清晰
2. **页面驱动设计**: 从用户体验的页面视角出发，明确各业务视图的功能边界和组合方式
3. **高内聚低耦合**: 业务视图内实现高内聚，视图间保持低耦合
4. **性能优先**: 确保流畅的转场体验和即时反馈，避免不必要的重渲染
5. **复用至上**: 优先设计可复用的业务视图和MVVM组件，充分利用共享库

## 2. 系统结构

### 2.1 应用、页面与业务视图结构
根据PRD，练习组件作为AI课中的一个关键页面，由多个功能明确的业务视图组成。所有业务视图严格遵循MVVM模式，部分业务视图被设计为可复用的全局组件。

```mermaid
graph TD
    App["应用核心 (Application Core)"]
    App --> Page1["练习页面 (app/exercise/page.tsx)"]
    
    Page1 --> BV1_A["练习转场视图 (Exercise Transition View)"]
    Page1 --> BV1_B["练习答题视图 (Exercise Question View)"]
    Page1 --> BV1_C["即时反馈视图 (Feedback View)"]
    Page1 --> Reusable_BV_X1["勾画视图 (Markup View) (Reusable)"]
    Page1 --> Reusable_BV_X2["答疑视图 (QA View) (Reusable)"]
    Page1 --> Reusable_BV_X3["错题本视图 (Wrong Questions View) (Reusable)"]

    BV1_A -- MVVM --> M_BV1_A["Model (for Transition)"]
    BV1_A -- MVVM --> VM_BV1_A["ViewModel (for Transition)"]
    BV1_A -- MVVM --> V_BV1_A["View Components (for Transition)"]

    BV1_B -- MVVM --> M_BV1_B["Model (for Question)"]
    BV1_B -- MVVM --> VM_BV1_B["ViewModel (for Question)"]
    BV1_B -- MVVM --> V_BV1_B["View Components (for Question)"]

    BV1_C -- MVVM --> M_BV1_C["Model (for Feedback)"]
    BV1_C -- MVVM --> VM_BV1_C["ViewModel (for Feedback)"]
    BV1_C -- MVVM --> V_BV1_C["View Components (for Feedback)"]

    Reusable_BV_X1 -- MVVM --> M_Reusable_BV_X1["Model (for Markup)"]
    Reusable_BV_X1 -- MVVM --> VM_Reusable_BV_X1["ViewModel (for Markup)"]
    Reusable_BV_X1 -- MVVM --> V_Reusable_BV_X1["View Components (for Markup)"]

    Reusable_BV_X2 -- MVVM --> M_Reusable_BV_X2["Model (for QA)"]
    Reusable_BV_X2 -- MVVM --> VM_Reusable_BV_X2["ViewModel (for QA)"]
    Reusable_BV_X2 -- MVVM --> V_Reusable_BV_X2["View Components (for QA)"]

    Reusable_BV_X3 -- MVVM --> M_Reusable_BV_X3["Model (for Wrong Questions)"]
    Reusable_BV_X3 -- MVVM --> VM_Reusable_BV_X3["ViewModel (for Wrong Questions)"]
    Reusable_BV_X3 -- MVVM --> V_Reusable_BV_X3["View Components (for Wrong Questions)"]
    
    subgraph GlobalShared [全局/共享服务、ViewModel及Model]
        direction LR
        GlobalViewModel["全局ViewModel (e.g., ExerciseProgressViewModel)"]
        GlobalModel["全局Model (e.g., ExerciseSessionModel)"]
        SharedUtils["共享工具/库 (@repo/core, @repo/lib)"]
        SharedUI["共享UI库 (@/components)"]
    end

    App --> GlobalViewModel
    App --> GlobalModel
    App --> SharedUtils
    App --> SharedUI

    VM_BV1_B --> GlobalViewModel
    VM_BV1_C --> GlobalViewModel
    VM_Reusable_BV_X1 --> GlobalViewModel
    VM_Reusable_BV_X2 --> GlobalViewModel
    VM_Reusable_BV_X3 --> GlobalViewModel

    classDef page fill:#E6E6FA,stroke:#333,stroke-width:2px;
    classDef businessView fill:#ADD8E6,stroke:#333,stroke-width:2px;
    classDef reusableView fill:#C1FFC1,stroke:#006400,stroke-width:2px;
    classDef mvvm fill:#90EE90,stroke:#333,stroke-width:1px;
    classDef global fill:#FFFACD,stroke:#333,stroke-width:1px;

    class Page1 page;
    class BV1_A,BV1_B,BV1_C businessView;
    class Reusable_BV_X1,Reusable_BV_X2,Reusable_BV_X3 reusableView;
    class M_BV1_A,VM_BV1_A,V_BV1_A,M_BV1_B,VM_BV1_B,V_BV1_B,M_BV1_C,VM_BV1_C,V_BV1_C mvvm;
    class M_Reusable_BV_X1,VM_Reusable_BV_X1,V_Reusable_BV_X1,M_Reusable_BV_X2,VM_Reusable_BV_X2,V_Reusable_BV_X2,M_Reusable_BV_X3,VM_Reusable_BV_X3,V_Reusable_BV_X3 mvvm;
    class GlobalViewModel,GlobalModel,SharedUtils,SharedUI global;
```
*图表说明：练习页面包含多个业务视图，其中勾画视图、答疑视图和错题本视图被设计为可复用业务视图。每个业务视图内部都遵循MVVM分层，并可能依赖全局/共享的ViewModel、Model或工具库。*

### 2.2 关键状态管理
练习组件的状态管理策略采用多层次设计：

1. **全局状态**（通过全局ViewModel管理）:
   - 练习会话状态（`ExerciseSessionModel`）：当前课程ID、练习题目总数、用户已完成题目数、连续正确数等
   - 用户进度状态（`ExerciseProgressViewModel`）：跟踪用户当前位置，控制题目间的流转

2. **页面/业务视图级状态**：
   - 练习转场视图状态：首次/再次进入标识，转场动画状态
   - 练习答题视图状态：当前题目数据，用户选择，计时器状态
   - 即时反馈视图状态：正确/错误/连对状态，动画播放状态

3. **数据流向**：
   - 单向数据流：Model层提供原始数据 → ViewModel层处理业务逻辑和状态转换 → View层负责渲染
   - 用户交互：View层捕获用户事件 → 调用ViewModel层方法 → ViewModel更新状态并可能调用Model层API → 触发View层重新渲染

## 3. 页面与业务视图详细设计

### 3.1 练习页面 (Exercise Page)
   **路由路径**: `app/exercise/page.tsx`

#### 3.1.1 页面概述 (Page Overview)
   - 练习页面是AI课中的核心功能页面，用于为学生提供知识点巩固和技能训练。它支持从课程内容平滑过渡到练习环节，提供友好的题目展示、作答、即时反馈机制，以及勾画、答疑、错题本等辅助功能。
   - 页面包含的主要业务视图：
     - 练习转场视图：负责处理首次/再次进入练习的转场动画和提示
     - 练习答题视图：核心视图，负责题目展示、用户作答、计时和进度管理
     - 即时反馈视图：负责根据用户作答结果提供动画和音效反馈
     - 勾画视图（可复用）：提供用户在题目上进行标记的功能
     - 答疑视图（可复用）：提供提问和获取解答的功能
     - 错题本视图（可复用）：用于管理错题收藏

#### 3.1.2 业务视图: 练习转场视图 (Exercise Transition View)
   **组件路径**: `app/views/exercise/transition-view.tsx`
   **复用策略**: 此业务视图为练习页面定制，不设计为跨页面复用的组件，因为转场动效与练习上下文强相关。

##### 3.1.2.1 功能概述 (Business View - Functional Overview)
      - 负责处理用户从其他组件（如文档）进入练习时的转场效果，包括提示条显示、进入动画播放、欢迎文案展示等。根据首次进入或再次进入（续学）状态展示不同的动画和文案，提升用户体验的连贯性和情感化。

##### 3.1.2.2 MVVM分层设计 (Business View - MVVM Design)

###### Model层 (Data & Business Logic Source)
* **核心数据结构**:
    * `TransitionConfig`：描述转场配置，包含动画类型、持续时间、文案等参数，来源于课程配置。
    * `UserProgressData`：描述用户在当前练习中的进度信息，判断是首次进入还是续学。
* **数据交互与处理**:
    * `fetchTransitionConfigAPI(courseId: string): Promise<TransitionConfigRaw>`：获取当前课程的转场配置。
    * `fetchUserProgressAPI(courseId: string, userId: string): Promise<UserProgressRaw>`：获取用户在当前课程练习中的进度。
    * `transformTransitionConfig(raw: TransitionConfigRaw): TransitionConfig`：转换转场配置数据为前端可用格式。
    * **依赖关系**：依赖全局的`ExerciseSessionModel`获取当前课程ID和用户ID。
* **复用性考量 (Model)**: 不适用，此业务视图不设计为复用。

###### ViewModel层 (Presentation Logic & State Management - 核心)
* **职责**: 封装转场相关的状态管理和业务逻辑，协调动画播放和页面切换。
* **管理的状态 (Signals/React State)**:
    * `transitionTypeSignal`: TransitionType, 初始为'NONE', 区分'FIRST_ENTRY'首次进入或'RESUME'续学。
    * `isPlayingSignal`: boolean, 初始为false, 控制动画播放状态。
    * `isCompletedSignal`: boolean, 初始为false, 标记转场是否完成。
    * `messageSignal`: string, 初始为'', 存储展示的文案内容。
* **暴露给View的接口 (数据和命令)**:
    * `transitionType`: 只读，当前转场类型。
    * `isPlaying`: 只读，动画是否播放中。
    * `isCompleted`: 只读，转场是否已完成。
    * `message`: 只读，当前展示的文案。
    * `startTransition(): void`: 开始播放转场动画。
    * `completeTransition(): void`: 标记转场完成，通知父组件进入答题界面。
* **内部依赖**: 依赖Model层的`fetchTransitionConfigAPI`和`fetchUserProgressAPI`，以及全局`ExerciseSessionModel`。
* **核心逻辑流程**: 
    1. 初始化时，从`ExerciseSessionModel`获取课程ID和用户ID
    2. 调用API获取转场配置和用户进度
    3. 根据用户进度判断是首次进入还是续学，设置对应的转场类型和文案
    4. 触发转场动画播放，并在规定时间后自动标记为完成
    5. 完成后通知练习页面进入答题环节
* **复用性考量 (ViewModel)**: 不适用，此业务视图不设计为复用。

```typescript
// ViewModel Hook接口示例
function useTransitionViewModel() {
  // ... state, effects, actions ...
  return { 
    transitionType, 
    isPlaying, 
    isCompleted, 
    message, 
    startTransition, 
    completeTransition 
  };
}
```

###### View层 (Pure UI Components)
* **组件结构**:
    ```mermaid
    graph TD
        TransitionView["转场视图根组件 (TransitionView.tsx)"]
        TransitionView --> TopNotification["顶部提示条 (TopNotification.tsx)"]
        TransitionView --> FullScreenTransition["全屏转场动画 (FullScreenTransition.tsx)"]
        FullScreenTransition --> IPAnimation["IP形象动画 (@/components/Animation)"]
        FullScreenTransition --> MessageDisplay["文字展示区域 (MessageDisplay.tsx)"]
    ```
* **核心View组件说明**:
    * `TransitionView`: 根组件，聚合各子组件，使用ViewModel控制显示逻辑。
    * `TopNotification`: 顶部提示条，显示"下拉进入练习"等引导提示。
    * `FullScreenTransition`: 全屏转场动画容器，控制翻页效果。
    * `IPAnimation`: IP形象动画组件，复用自共享UI库，根据转场类型显示不同动画。
    * `MessageDisplay`: 文字显示区域，展示"开始练习"或"继续练习"等文案。

##### ******* 关键交互流程 (Business View - Key Interaction Flow)
```mermaid
sequenceDiagram
    participant User
    participant View (TransitionView)
    participant ViewModel (useTransitionViewModel)
    participant Model (TransitionModel)
    participant GlobalModel (ExerciseSessionModel)
    participant API
    
    User->>View (TransitionView): 完成前一组件/下拉进入
    View (TransitionView)->>ViewModel (useTransitionViewModel): 初始化
    ViewModel (useTransitionViewModel)->>GlobalModel (ExerciseSessionModel): 获取课程ID和用户ID
    ViewModel (useTransitionViewModel)->>Model (TransitionModel): 请求转场配置和用户进度
    Model (TransitionModel)->>API: 发起API请求
    API-->>Model (TransitionModel): 返回配置和进度数据
    Model (TransitionModel)-->>ViewModel (useTransitionViewModel): 返回处理后的数据
    ViewModel (useTransitionViewModel)->>ViewModel (useTransitionViewModel): 判断转场类型并设置状态
    ViewModel (useTransitionViewModel)->>View (TransitionView): 更新UI状态
    View (TransitionView)->>User: 显示转场动画和文案
    
    Note over View (TransitionView),ViewModel (useTransitionViewModel): 转场动画播放2秒
    
    ViewModel (useTransitionViewModel)->>ViewModel (useTransitionViewModel): 动画结束，标记完成
    ViewModel (useTransitionViewModel)->>View (TransitionView): 通知转场完成
    View (TransitionView)->>User: 过渡到答题界面
```

#### 3.1.3 业务视图: 练习答题视图 (Exercise Question View)
   **组件路径**: `app/views/exercise/question-view.tsx`
   **复用策略**: 此业务视图为练习页面核心功能，不设计为跨页面复用的组件，因为它与练习上下文和流程强相关。

##### 3.1.3.1 功能概述 (Business View - Functional Overview)
      - 负责题目的展示和用户作答交互，包括题干展示、选项展示、用户选择、提交答案、计时和进度管理等核心功能。同时提供勾画、不确定标记、答疑入口等辅助功能的整合。

##### 3.1.3.2 MVVM分层设计 (Business View - MVVM Design)

###### Model层 (Data & Business Logic Source)
* **核心数据结构**:
    * `QuestionData`：题目完整数据，包含题干、选项、正确答案、解析等。
    * `UserAnswerData`：用户作答数据，包含选择的选项、作答时间等。
    * `QuestionMetadata`：题目元数据，如题型、难度、知识点等。
* **数据交互与处理**:
    * `fetchQuestionAPI(questionId: string): Promise<QuestionDataRaw>`：获取题目详情。
    * `submitAnswerAPI(payload: SubmitAnswerPayload): Promise<SubmitAnswerResponse>`：提交用户答案。
    * `transformQuestionData(raw: QuestionDataRaw): QuestionData`：转换题目数据为前端可用格式。
    * **依赖关系**：依赖全局的`ExerciseSessionModel`获取当前练习会话信息。
* **复用性考量 (Model)**: 不适用，此业务视图不设计为复用。

###### ViewModel层 (Presentation Logic & State Management - 核心)
* **职责**: 封装题目展示、用户交互和答案提交的业务逻辑，管理答题状态。
* **管理的状态 (Signals/React State)**:
    * `currentQuestionSignal`: QuestionData | null, 初始为null, 当前题目数据。
    * `selectedOptionSignal`: string | null, 初始为null, 用户选择的选项ID。
    * `isLoadingSignal`: boolean, 初始为true, 题目加载状态。
    * `isSubmittingSignal`: boolean, 初始为false, 提交答案状态。
    * `timerSignal`: number, 初始为0, 当前题目的计时（秒）。
    * `isUncertainSignal`: boolean, 初始为false, 是否标记为不确定。
    * `errorSignal`: Error | null, 初始为null, 错误信息。
* **暴露给View的接口 (数据和命令)**:
    * `currentQuestion`: 只读，当前题目数据。
    * `selectedOption`: 只读，当前选中的选项。
    * `isLoading`: 只读，是否正在加载题目。
    * `isSubmitting`: 只读，是否正在提交答案。
    * `timer`: 只读，当前计时器值。
    * `isUncertain`: 只读，是否标记为不确定。
    * `hasError`: 只读，是否有错误。
    * `errorMessage`: 只读，错误信息。
    * `selectOption(optionId: string): void`: 选择一个选项。
    * `submitAnswer(): void`: 提交用户答案。
    * `toggleUncertain(): void`: 切换不确定标记状态。
    * `exitExercise(): void`: 退出练习，保存当前进度。
* **内部依赖**: 依赖Model层的`fetchQuestionAPI`和`submitAnswerAPI`，以及全局`ExerciseProgressViewModel`。
* **核心逻辑流程**: 
    1. 初始化时，从全局ViewModel获取当前应该展示的题目ID
    2. 调用API加载题目数据，并开始计时
    3. 用户选择选项后，更新选中状态
    4. 用户提交答案时，调用API提交并等待结果
    5. 根据结果，通知父组件显示对应的反馈视图（正确/错误/连对）
    6. 反馈结束后，加载下一题或结束练习
* **复用性考量 (ViewModel)**: 不适用，此业务视图不设计为复用。

```typescript
// ViewModel Hook接口示例
function useQuestionViewModel() {
  // ... state, effects, actions ...
  return { 
    currentQuestion,
    selectedOption,
    isLoading,
    isSubmitting,
    timer,
    isUncertain,
    hasError,
    errorMessage,
    selectOption,
    submitAnswer,
    toggleUncertain,
    exitExercise
  };
}
```

###### View层 (Pure UI Components)
* **组件结构**:
    ```mermaid
    graph TD
        QuestionView["答题视图根组件 (QuestionView.tsx)"]
        QuestionView --> TopNav["顶部导航栏 (TopNav.tsx)"]
        QuestionView --> QuestionContent["题目内容区 (QuestionContent.tsx)"]
        QuestionView --> BottomActions["底部操作区 (BottomActions.tsx)"]
        
        TopNav --> ExitButton["退出按钮 (@/components/Button)"]
        TopNav --> Timer["计时器 (Timer.tsx)"]
        TopNav --> Progress["进度指示 (Progress.tsx)"]
        TopNav --> QAButton["问一问按钮 (@/components/Button)"]
        
        QuestionContent --> QuestionType["题型标签 (QuestionType.tsx)"]
        QuestionContent --> QuestionText["题干文本 (QuestionText.tsx)"]
        QuestionContent --> OptionsList["选项列表 (OptionsList.tsx)"]
        OptionsList --> OptionItem["选项项 (OptionItem.tsx)"]
        
        BottomActions --> UncertainButton["不确定按钮 (@/components/Button)"]
        BottomActions --> SubmitButton["提交按钮 (@/components/Button)"]
        BottomActions --> MarkupButton["勾画按钮 (@/components/Button)"]
    ```
* **核心View组件说明**:
    * `QuestionView`: 根组件，整合题目展示和作答流程。
    * `TopNav`: 顶部导航栏，包含退出、计时、进度和问一问等功能。
    * `QuestionContent`: 题目内容区，显示题型、题干和选项列表。
    * `BottomActions`: 底部操作区，包含不确定、提交和勾画等按钮。
    * `OptionItem`: 选项项，处理选项的显示和选中状态。

##### ******* 关键交互流程 (Business View - Key Interaction Flow)
```mermaid
sequenceDiagram
    participant User
    participant View (QuestionView)
    participant ViewModel (useQuestionViewModel)
    participant Model (QuestionModel)
    participant GlobalViewModel (ExerciseProgressViewModel)
    participant API
    
    User->>View (QuestionView): 进入答题界面
    View (QuestionView)->>ViewModel (useQuestionViewModel): 初始化
    ViewModel (useQuestionViewModel)->>GlobalViewModel (ExerciseProgressViewModel): 获取当前题目ID
    ViewModel (useQuestionViewModel)->>Model (QuestionModel): 请求题目数据
    Model (QuestionModel)->>API: 发起API请求
    API-->>Model (QuestionModel): 返回题目数据
    Model (QuestionModel)-->>ViewModel (useQuestionViewModel): 返回处理后的题目数据
    ViewModel (useQuestionViewModel)->>ViewModel (useQuestionViewModel): 开始计时
    ViewModel (useQuestionViewModel)->>View (QuestionView): 更新UI状态
    View (QuestionView)->>User: 显示题目和选项
    
    User->>View (QuestionView): 选择选项
    View (QuestionView)->>ViewModel (useQuestionViewModel): 调用selectOption()
    ViewModel (useQuestionViewModel)->>View (QuestionView): 更新选中状态
    View (QuestionView)->>User: 显示选中状态
    
    User->>View (QuestionView): 点击提交按钮
    View (QuestionView)->>ViewModel (useQuestionViewModel): 调用submitAnswer()
    ViewModel (useQuestionViewModel)->>Model (QuestionModel): 提交答案
    Model (QuestionModel)->>API: 发起API请求
    API-->>Model (QuestionModel): 返回判题结果
    Model (QuestionModel)-->>ViewModel (useQuestionViewModel): 返回处理后的结果
    ViewModel (useQuestionViewModel)->>GlobalViewModel (ExerciseProgressViewModel): 更新答题进度
    ViewModel (useQuestionViewModel)->>View (QuestionView): 通知结果
    View (QuestionView)->>User: 触发反馈视图显示
```

#### 3.1.4 业务视图: 即时反馈视图 (Feedback View)
   **组件路径**: `app/views/exercise/feedback-view.tsx`
   **复用策略**: 此业务视图为练习页面定制，与练习答题流程紧密结合，但其动画和反馈部分可考虑未来抽象为独立的动画组件库。

##### 3.1.4.1 功能概述 (Business View - Functional Overview)
      - 负责根据用户作答结果提供即时的视觉和听觉反馈，包括正确/错误的动画效果、音效播放、连对成就显示等，增强用户的情感体验和学习动力。

##### 3.1.4.2 MVVM分层设计 (Business View - MVVM Design)

###### Model层 (Data & Business Logic Source)
* **核心数据结构**:
    * `FeedbackConfig`：反馈配置，包含不同类型反馈的动画资源、音效资源、持续时间等。
    * `FeedbackResult`：反馈结果，包含正误、连对次数、激励文案等。
* **数据交互与处理**:
    * `fetchFeedbackConfigAPI(courseId: string): Promise<FeedbackConfigRaw>`：获取当前课程的反馈配置。
    * `transformFeedbackConfig(raw: FeedbackConfigRaw): FeedbackConfig`：转换反馈配置为前端可用格式。
    * **依赖关系**：依赖全局的`ExerciseSessionModel`获取连对次数等练习会话信息。
* **复用性考量 (Model)**: 不适用，此业务视图不设计为复用。

###### ViewModel层 (Presentation Logic & State Management - 核心)
* **职责**: 封装反馈动画控制逻辑，协调动画播放和声音效果。
* **管理的状态 (Signals/React State)**:
    * `feedbackTypeSignal`: FeedbackType, 初始为'NONE', 可能值包括'CORRECT'、'INCORRECT'、'STREAK'。
    * `streakCountSignal`: number, 初始为0, 当前连对次数。
    * `isPlayingSignal`: boolean, 初始为false, 动画是否正在播放。
    * `messageSignal`: string, 初始为'', 反馈文案内容。
    * `isCompletedSignal`: boolean, 初始为false, 反馈是否播放完成。
* **暴露给View的接口 (数据和命令)**:
    * `feedbackType`: 只读，当前反馈类型。
    * `streakCount`: 只读，连对次数。
    * `isPlaying`: 只读，是否正在播放。
    * `message`: 只读，反馈文案。
    * `isCompleted`: 只读，是否播放完成。
    * `startFeedback(type: FeedbackType): void`: 开始播放指定类型的反馈。
    * `completeFeedback(): void`: 标记反馈完成，通知父组件进入下一题。
* **内部依赖**: 依赖Model层的`fetchFeedbackConfigAPI`，以及全局`ExerciseSessionModel`。
* **核心逻辑流程**: 
    1. 初始化时，加载反馈配置
    2. 根据父组件传入的作答结果，确定反馈类型（正确/错误/连对）
    3. 从全局ViewModel获取连对次数，判断是否触发连对反馈
    4. 播放相应的动画和音效，展示文案
    5. 在规定时间后自动标记为完成，通知父组件进入下一题
* **复用性考量 (ViewModel)**: 不适用，此业务视图不设计为复用，但反馈机制的核心逻辑可考虑抽象为通用服务。

```typescript
// ViewModel Hook接口示例
function useFeedbackViewModel(answerResult: AnswerResult) {
  // ... state, effects, actions ...
  return { 
    feedbackType, 
    streakCount, 
    isPlaying, 
    message, 
    isCompleted, 
    startFeedback, 
    completeFeedback 
  };
}
```

###### View层 (Pure UI Components)
* **组件结构**:
```mermaid
    graph TD
        FeedbackView["反馈视图根组件 (FeedbackView.tsx)"]
        FeedbackView --> CorrectFeedback["正确反馈 (CorrectFeedback.tsx)"]
        FeedbackView --> IncorrectFeedback["错误反馈 (IncorrectFeedback.tsx)"]
        FeedbackView --> StreakFeedback["连对反馈 (StreakFeedback.tsx)"]
        
        CorrectFeedback --> StarAnimation["星星动画 (@/components/Animation)"]
        CorrectFeedback --> CorrectSound["正确音效 (SoundEffect.tsx)"]
        
        IncorrectFeedback --> CrossAnimation["叉号动画 (@/components/Animation)"]
        IncorrectFeedback --> IncorrectSound["错误音效 (SoundEffect.tsx)"]
        
        StreakFeedback --> FireworkAnimation["烟花动画 (@/components/Animation)"]
        StreakFeedback --> AchievementMessage["成就文案 (AchievementMessage.tsx)"]
        StreakFeedback --> StreakSound["连对音效 (SoundEffect.tsx)"]
    ```
* **核心View组件说明**:
    * `FeedbackView`: 根组件，根据反馈类型选择显示对应的子组件。
    * `CorrectFeedback`: 正确反馈组件，包含星星动画和正确音效。
    * `IncorrectFeedback`: 错误反馈组件，包含叉号动画和错误音效。
    * `StreakFeedback`: 连对反馈组件，包含烟花动画、成就文案和专属音效。

##### ******* 关键交互流程 (Business View - Key Interaction Flow)
```mermaid
sequenceDiagram
    participant User
    participant View (FeedbackView)
    participant ViewModel (useFeedbackViewModel)
    participant Model (FeedbackModel)
    participant GlobalModel (ExerciseSessionModel)
    participant AudioEngine as 音频引擎
    participant AnimationEngine as 动画引擎
    
    User->>View (FeedbackView): 提交答案后
    View (FeedbackView)->>ViewModel (useFeedbackViewModel): 初始化(传入答案结果)
    ViewModel (useFeedbackViewModel)->>GlobalModel (ExerciseSessionModel): 获取连对次数
    ViewModel (useFeedbackViewModel)->>Model (FeedbackModel): 获取反馈配置
    ViewModel (useFeedbackViewModel)->>ViewModel (useFeedbackViewModel): 决定反馈类型
    ViewModel (useFeedbackViewModel)->>View (FeedbackView): 更新UI状态
    
    alt 答案正确
        View (FeedbackView)->>AnimationEngine: 播放正确动画
        View (FeedbackView)->>AudioEngine: 播放正确音效
        AnimationEngine-->>View (FeedbackView): 动画播放中
        AudioEngine-->>View (FeedbackView): 音效播放中
        View (FeedbackView)->>User: 展示正确反馈
    else 答案错误
        View (FeedbackView)->>AnimationEngine: 播放错误动画
        View (FeedbackView)->>AudioEngine: 播放错误音效
        AnimationEngine-->>View (FeedbackView): 动画播放中
        AudioEngine-->>View (FeedbackView): 音效播放中
        View (FeedbackView)->>User: 展示错误反馈
    else 连对成就
        View (FeedbackView)->>AnimationEngine: 播放连对动画
        View (FeedbackView)->>AudioEngine: 播放连对音效
        AnimationEngine-->>View (FeedbackView): 动画播放中
        AudioEngine-->>View (FeedbackView): 音效播放中
        View (FeedbackView)->>User: 展示连对成就
    end
    
    Note over AnimationEngine,AudioEngine: 动画和音效播放完成
    
    AnimationEngine-->>ViewModel (useFeedbackViewModel): 通知动画完成
    ViewModel (useFeedbackViewModel)->>ViewModel (useFeedbackViewModel): 标记反馈完成
    ViewModel (useFeedbackViewModel)->>GlobalModel (ExerciseSessionModel): 更新练习进度
    GlobalModel (ExerciseSessionModel)-->>ViewModel (useFeedbackViewModel): 返回更新结果
    ViewModel (useFeedbackViewModel)->>View (FeedbackView): 通知反馈流程结束
    View (FeedbackView)->>User: 隐藏反馈UI，准备下一题
```

#### 3.1.5 业务视图: 勾画视图 (Markup View) (可复用)
   **组件路径**: `app/views/shared/markup-view.tsx`
   **复用策略**: 此业务视图设计为可复用组件，可在练习、文档、阅读等多种场景中使用，提供一致的勾画体验。通过参数化配置实现了对不同宿主页面的适应。

##### 3.1.5.1 功能概述 (Business View - Functional Overview)
      - 提供在内容上进行自由勾画、标记的功能，支持不同颜色、粗细的画笔，以及橡皮擦、清除等功能，帮助用户标记重点、做笔记等。

##### 3.1.5.2 MVVM分层设计 (Business View - MVVM Design)

###### Model层 (Data & Business Logic Source)
* **核心数据结构**:
    * `MarkupData`：勾画数据，包含路径点集合、颜色、粗细等信息。
    * `MarkupConfig`：勾画配置，包含可用颜色、粗细范围、默认设置等。
    * `MarkupSnapshot`：勾画快照，用于撤销/重做功能。
* **数据交互与处理**:
    * `saveMarkupAPI(contextId: string, markupData: MarkupData[]): Promise<SaveResult>`：保存勾画数据。
    * `fetchMarkupAPI(contextId: string): Promise<MarkupDataRaw[]>`：获取已保存的勾画数据。
    * `transformMarkupData(raw: MarkupDataRaw[]): MarkupData[]`：转换勾画数据为前端可用格式。
    * **依赖关系**：不强依赖其他模块，通过参数接收上下文ID。
* **复用性考量 (Model)**: Model层设计为高度通用，通过上下文ID（如题目ID、文档ID）参数化，支持在不同页面中复用。数据结构和API接口保持一致，便于跨场景使用。

###### ViewModel层 (Presentation Logic & State Management - 核心)
* **职责**: 封装勾画状态管理和绘制逻辑，处理用户输入和画布操作。
* **管理的状态 (Signals/React State)**:
    * `markupListSignal`: MarkupData[], 初始为[], 当前所有勾画数据。
    * `currentToolSignal`: ToolType, 初始为'pen', 当前选中的工具。
    * `currentColorSignal`: string, 初始为'#FF0000', 当前选中的颜色。
    * `currentWidthSignal`: number, 初始为2, 当前选中的线条粗细。
    * `isDrawingSignal`: boolean, 初始为false, 是否正在绘制。
    * `historySignal`: MarkupSnapshot[], 初始为[], 操作历史记录。
    * `historyIndexSignal`: number, 初始为-1, 当前历史记录位置。
* **暴露给View的接口 (数据和命令)**:
    * `markupList`: 只读，当前所有勾画数据。
    * `currentTool`: 只读，当前工具。
    * `currentColor`: 只读，当前颜色。
    * `currentWidth`: 只读，当前线条粗细。
    * `isDrawing`: 只读，是否正在绘制。
    * `canUndo`: 只读，是否可以撤销。
    * `canRedo`: 只读，是否可以重做。
    * `setTool(tool: ToolType): void`: 设置当前工具。
    * `setColor(color: string): void`: 设置当前颜色。
    * `setWidth(width: number): void`: 设置当前线条粗细。
    * `startDrawing(x: number, y: number): void`: 开始绘制。
    * `continueDrawing(x: number, y: number): void`: 继续绘制。
    * `endDrawing(): void`: 结束绘制。
    * `clear(): void`: 清除所有勾画。
    * `undo(): void`: 撤销上一步。
    * `redo(): void`: 重做下一步。
    * `save(): Promise<boolean>`: 保存当前勾画数据。
* **内部依赖**: 依赖Model层的`saveMarkupAPI`和`fetchMarkupAPI`。
* **核心逻辑流程**: 
    1. 初始化时，若提供contextId，则加载已保存的勾画数据
    2. 用户选择工具、颜色、粗细，更新相应状态
    3. 用户在画布上绘制，实时更新勾画数据并渲染
    4. 用户可以撤销/重做操作，通过历史记录实现
    5. 用户可以保存勾画数据，通过API将数据持久化
* **复用性考量 (ViewModel)**: ViewModel设计为完全参数化，不依赖特定页面上下文。通过配置项和回调函数的方式与不同宿主页面通信，提供一致的业务逻辑和状态管理。

```typescript
// ViewModel Hook接口示例
function useMarkupViewModel(config?: MarkupViewModelConfig) {
  // 配置包含：contextId(可选)、默认工具、颜色、粗细、保存回调等
  // ... state, effects, actions ...
  return { 
    markupList,
    currentTool,
    currentColor,
    currentWidth,
    isDrawing,
    canUndo,
    canRedo,
    setTool,
    setColor,
    setWidth,
    startDrawing,
    continueDrawing,
    endDrawing,
    clear,
    undo,
    redo,
    save
  };
}
```

###### View层 (Pure UI Components)
* **组件结构**:
    ```mermaid
    graph TD
        MarkupView["勾画视图根组件 (MarkupView.tsx)"]
        MarkupView --> ToolBar["工具栏 (ToolBar.tsx)"]
        MarkupView --> Canvas["画布 (Canvas.tsx)"]
        MarkupView --> ActionBar["动作栏 (ActionBar.tsx)"]
        
        ToolBar --> PenTool["画笔工具 (Tool.tsx)"]
        ToolBar --> EraserTool["橡皮擦工具 (Tool.tsx)"]
        ToolBar --> ColorPicker["颜色选择器 (@/components/ColorPicker)"]
        ToolBar --> WidthPicker["粗细选择器 (WidthPicker.tsx)"]
        
        ActionBar --> UndoButton["撤销按钮 (@/components/Button)"]
        ActionBar --> RedoButton["重做按钮 (@/components/Button)"]
        ActionBar --> ClearButton["清除按钮 (@/components/Button)"]
        ActionBar --> SaveButton["保存按钮 (@/components/Button)"]
        ActionBar --> CloseButton["关闭按钮 (@/components/Button)"]
    ```
* **核心View组件说明**:
    * `MarkupView`: 根组件，提供勾画功能的整体容器。
    * `ToolBar`: 工具栏，包含画笔、橡皮擦、颜色选择器、粗细选择器等工具。
    * `Canvas`: 画布组件，处理绘制逻辑和用户交互。
    * `ActionBar`: 动作栏，提供撤销、重做、清除、保存和关闭等操作按钮。
* **复用性考量 (View)**: View层组件采用纯UI设计，所有状态和逻辑通过props接收，不包含硬编码的特定场景逻辑。样式通过props或CVA控制，支持在不同场景中保持一致的视觉风格。提供灵活的布局和定位选项，以适应不同宿主页面的需求。

##### ******* 关键交互流程 (Business View - Key Interaction Flow)
```mermaid
sequenceDiagram
    participant User
    participant View (MarkupView)
    participant ViewModel (useMarkupViewModel)
    participant Model (MarkupModel)
    participant API
    
    User->>View (MarkupView): 激活勾画模式
    View (MarkupView)->>ViewModel (useMarkupViewModel): 初始化
    alt 有上下文ID
        ViewModel (useMarkupViewModel)->>Model (MarkupModel): 获取已保存的勾画数据
        Model (MarkupModel)->>API: 发起API请求
        API-->>Model (MarkupModel): 返回勾画数据
        Model (MarkupModel)-->>ViewModel (useMarkupViewModel): 返回处理后的数据
    end
    ViewModel (useMarkupViewModel)->>View (MarkupView): 更新UI状态
    View (MarkupView)->>User: 显示勾画界面
    
    User->>View (MarkupView): 选择画笔工具和颜色
    View (MarkupView)->>ViewModel (useMarkupViewModel): 调用setTool()和setColor()
    ViewModel (useMarkupViewModel)->>View (MarkupView): 更新工具和颜色状态
    
    User->>View (MarkupView): 在画布上开始绘制
    View (MarkupView)->>ViewModel (useMarkupViewModel): 调用startDrawing()
    ViewModel (useMarkupViewModel)->>ViewModel (useMarkupViewModel): 创建新的勾画数据
    ViewModel (useMarkupViewModel)->>View (MarkupView): 更新绘制状态
    
    User->>View (MarkupView): 移动鼠标/触摸
    View (MarkupView)->>ViewModel (useMarkupViewModel): 调用continueDrawing()
    ViewModel (useMarkupViewModel)->>ViewModel (useMarkupViewModel): 更新当前勾画路径
    ViewModel (useMarkupViewModel)->>View (MarkupView): 更新画布显示
    View (MarkupView)->>User: 实时显示绘制内容
    
    User->>View (MarkupView): 松开鼠标/触摸
    View (MarkupView)->>ViewModel (useMarkupViewModel): 调用endDrawing()
    ViewModel (useMarkupViewModel)->>ViewModel (useMarkupViewModel): 完成当前勾画，添加历史记录
    
    User->>View (MarkupView): 点击保存按钮
    View (MarkupView)->>ViewModel (useMarkupViewModel): 调用save()
    ViewModel (useMarkupViewModel)->>Model (MarkupModel): 保存勾画数据
    Model (MarkupModel)->>API: 发起API请求
    API-->>Model (MarkupModel): 返回保存结果
    Model (MarkupModel)-->>ViewModel (useMarkupViewModel): 返回操作结果
    ViewModel (useMarkupViewModel)->>View (MarkupView): 更新保存状态
    View (MarkupView)->>User: 显示保存成功提示
```

#### 3.1.6 业务视图: 答疑视图 (QA View) (可复用)
   **组件路径**: `app/views/shared/qa-view.tsx`
   **复用策略**: 此业务视图设计为高度可复用的组件，可在练习、文档、视频等多种场景中使用，提供一致的问答交互体验。

##### 3.1.6.1 功能概述 (Business View - Functional Overview)
      - 提供用户提问和获取答案的功能，支持选中文字提问、支持自由输入问题，并展示AI或人工回答，帮助用户解决学习过程中的疑问。

##### 3.1.6.2 MVVM分层设计 (Business View - MVVM Design)

###### Model层 (Data & Business Logic Source)
* **核心数据结构**:
    * `Question`：用户问题数据，包含问题内容、上下文信息等。
    * `Answer`：回答数据，包含回答内容、类型（AI/人工）、时间等。
    * `QAContext`：问答上下文，包含关联的内容ID、类型等。
* **数据交互与处理**:
    * `submitQuestionAPI(question: Question): Promise<SubmitQuestionResponse>`：提交问题。
    * `fetchAnswerAPI(questionId: string): Promise<AnswerRaw>`：获取问题的回答。
    * `transformAnswerData(raw: AnswerRaw): Answer`：转换回答数据为前端可用格式。
    * **依赖关系**：不强依赖其他模块，通过参数接收上下文信息。
* **复用性考量 (Model)**: Model层设计为高度通用，支持不同类型的上下文（如题目、文档段落），API接口保持一致，便于跨场景使用。通过配置项控制不同场景下的行为差异。

###### ViewModel层 (Presentation Logic & State Management - 核心)
* **职责**: 封装问答交互的状态管理和业务逻辑，处理问题提交和答案接收。
* **管理的状态 (Signals/React State)**:
    * `questionInputSignal`: string, 初始为'', 用户输入的问题。
    * `selectedTextSignal`: string, 初始为'', 用户选中的文字。
    * `isSubmittingSignal`: boolean, 初始为false, 是否正在提交问题。
    * `isLoadingAnswerSignal`: boolean, 初始为false, 是否正在加载答案。
    * `currentAnswerSignal`: Answer | null, 初始为null, 当前问题的回答。
    * `errorSignal`: Error | null, 初始为null, 错误信息。
    * `qaHistorySignal`: Array<{question: Question, answer: Answer}>, 初始为[], 问答历史记录。
* **暴露给View的接口 (数据和命令)**:
    * `questionInput`: 只读，当前输入的问题。
    * `selectedText`: 只读，当前选中的文字。
    * `isSubmitting`: 只读，是否正在提交问题。
    * `isLoadingAnswer`: 只读，是否正在加载答案。
    * `currentAnswer`: 只读，当前问题的回答。
    * `hasError`: 只读，是否有错误。
    * `errorMessage`: 只读，错误信息。
    * `qaHistory`: 只读，问答历史记录。
    * `setQuestionInput(text: string): void`: 设置问题输入。
    * `setSelectedText(text: string): void`: 设置选中的文字。
    * `submitQuestion(): void`: 提交问题。
    * `clearQuestion(): void`: 清除当前问题。
* **内部依赖**: 依赖Model层的`submitQuestionAPI`和`fetchAnswerAPI`。
* **核心逻辑流程**: 
    1. 初始化时，接收上下文信息（如题目ID、选中文字）
    2. 用户可以编辑问题或使用选中的文字
    3. 用户提交问题，调用API发送请求
    4. 等待并接收回答，显示给用户
    5. 问答记录添加到历史记录，支持多轮对话
* **复用性考量 (ViewModel)**: ViewModel设计为完全参数化，接口保持一致，通过配置项适应不同场景的需求。所有依赖外部上下文的数据都通过参数传入，不包含硬编码的场景特定逻辑。

```typescript
// ViewModel Hook接口示例
function useQAViewModel(config?: QAViewModelConfig) {
  // 配置包含：上下文类型、ID、初始选中文字、历史记录上限等
  // ... state, effects, actions ...
  return { 
    questionInput,
    selectedText,
    isSubmitting,
    isLoadingAnswer,
    currentAnswer,
    hasError,
    errorMessage,
    qaHistory,
    setQuestionInput,
    setSelectedText,
    submitQuestion,
    clearQuestion
  };
}
```

###### View层 (Pure UI Components)
* **组件结构**:
    ```mermaid
    graph TD
        QAView["答疑视图根组件 (QAView.tsx)"]
        QAView --> Header["头部组件 (Header.tsx)"]
        QAView --> QAHistory["历史记录列表 (QAHistory.tsx)"]
        QAView --> QuestionInput["问题输入区 (QuestionInput.tsx)"]
        QAView --> AnswerDisplay["答案显示区 (AnswerDisplay.tsx)"]
        
        Header --> Title["标题 (@/components/Typography)"]
        Header --> CloseButton["关闭按钮 (@/components/Button)"]
        
        QAHistory --> HistoryItem["历史记录项 (HistoryItem.tsx)"]
        HistoryItem --> QuestionText["问题文本 (@/components/Typography)"]
        HistoryItem --> AnswerText["答案文本 (@/components/Typography)"]
        
        QuestionInput --> TextArea["文本输入框 (@/components/TextArea)"]
        QuestionInput --> SubmitButton["提交按钮 (@/components/Button)"]
        QuestionInput --> ClearButton["清除按钮 (@/components/Button)"]
        
        AnswerDisplay --> LoadingIndicator["加载指示器 (@/components/Spinner)"]
        AnswerDisplay --> AnswerContent["答案内容 (@/components/Typography)"]
        AnswerDisplay --> ErrorMessage["错误信息 (@/components/Alert)"]
    ```
* **核心View组件说明**:
    * `QAView`: 根组件，提供答疑功能的整体容器。
    * `Header`: 头部组件，包含标题和关闭按钮。
    * `QAHistory`: 历史记录列表，展示之前的问答记录。
    * `QuestionInput`: 问题输入区，包含文本输入框和操作按钮。
    * `AnswerDisplay`: 答案显示区，包含加载指示器、答案内容和错误信息。
* **复用性考量 (View)**: View层组件采用纯UI设计，所有状态和逻辑通过props接收，不包含硬编码的特定场景逻辑。提供灵活的布局和样式选项，以适应不同宿主页面的需求。组件结构设计考虑了在不同场景中的一致性体验。

##### ******* 关键交互流程 (Business View - Key Interaction Flow)
```mermaid
sequenceDiagram
    participant User
    participant View (QAView)
    participant ViewModel (useQAViewModel)
    participant Model (QAModel)
    participant API
    
    User->>View (QAView): 长按内容或点击问一问
    View (QAView)->>ViewModel (useQAViewModel): 初始化(传入上下文)
    ViewModel (useQAViewModel)->>View (QAView): 更新UI状态
    View (QAView)->>User: 显示答疑界面
    
    alt 长按选中文字
        User->>View (QAView): 长按选中文字
        View (QAView)->>ViewModel (useQAViewModel): 调用setSelectedText()
        ViewModel (useQAViewModel)->>ViewModel (useQAViewModel): 将选中文字填充到问题输入框
        ViewModel (useQAViewModel)->>View (QAView): 更新问题输入状态
        View (QAView)->>User: 显示预填充的问题
    else 直接提问
        User->>View (QAView): 输入问题文本
        View (QAView)->>ViewModel (useQAViewModel): 调用setQuestionInput()
        ViewModel (useQAViewModel)->>View (QAView): 更新问题输入状态
    end
    
    User->>View (QAView): 点击提交按钮
    View (QAView)->>ViewModel (useQAViewModel): 调用submitQuestion()
    ViewModel (useQAViewModel)->>Model (QAModel): 提交问题
    Model (QAModel)->>API: 发起API请求
    API-->>Model (QAModel): 返回问题ID或初步回答
    Model (QAModel)-->>ViewModel (useQAViewModel): 返回处理后的数据
    ViewModel (useQAViewModel)->>View (QAView): 更新为加载状态
    View (QAView)->>User: 显示问题已提交，正在获取答案
    
    alt 需要轮询答案
        ViewModel (useQAViewModel)->>Model (QAModel): 轮询获取答案
        Model (QAModel)->>API: 发起API请求
        API-->>Model (QAModel): 返回答案数据
        Model (QAModel)-->>ViewModel (useQAViewModel): 返回处理后的答案
    end
    
    ViewModel (useQAViewModel)->>ViewModel (useQAViewModel): 更新问答历史记录
    ViewModel (useQAViewModel)->>View (QAView): 更新答案显示
    View (QAView)->>User: 显示完整答案
    
    User->>View (QAView): 点击关闭按钮
    View (QAView)->>User: 关闭答疑界面，返回原场景
```

#### 3.1.7 业务视图: 错题本视图 (Wrong Questions View) (可复用)
   **组件路径**: `app/views/shared/wrong-questions-view.tsx`
   **复用策略**: 此业务视图设计为可复用组件，可在练习、复习等多种场景中使用，提供统一的错题本功能。

##### 3.1.7.1 功能概述 (Business View - Functional Overview)
      - 提供用户将题目添加到错题本或从错题本移除的功能，帮助用户标记和管理需要重点复习的题目，提升学习效率。

##### 3.1.7.2 MVVM分层设计 (Business View - MVVM Design)

###### Model层 (Data & Business Logic Source)
* **核心数据结构**:
    * `WrongQuestionItem`：错题项数据，包含题目ID、添加时间、题目简要信息等。
    * `WrongQuestionsCollection`：错题本集合，包含多个错题项及元数据。
* **数据交互与处理**:
    * `addToWrongQuestionsAPI(questionId: string): Promise<AddResult>`：添加题目到错题本。
    * `removeFromWrongQuestionsAPI(questionId: string): Promise<RemoveResult>`：从错题本移除题目。
    * `fetchWrongQuestionsAPI(userId: string, courseId?: string): Promise<WrongQuestionsRaw>`：获取错题本数据。
    * `transformWrongQuestionsData(raw: WrongQuestionsRaw): WrongQuestionsCollection`：转换数据为前端可用格式。
    * **依赖关系**：需要从外部获取用户ID和可选的课程ID。
* **复用性考量 (Model)**: Model层设计为通用服务，通过参数化适应不同场景的需求，API接口保持一致，便于在练习、复习等不同场景中复用。

###### ViewModel层 (Presentation Logic & State Management - 核心)
* **职责**: 封装错题本相关的状态管理和业务逻辑，处理添加、移除和查询操作。
* **管理的状态 (Signals/React State)**:
    * `wrongQuestionsSignal`: WrongQuestionsCollection, 初始为空集合, 当前错题本数据。
    * `isLoadingSignal`: boolean, 初始为true, 是否正在加载错题本数据。
    * `isAddingSignal`: boolean, 初始为false, 是否正在添加题目。
    * `isRemovingSignal`: boolean, 初始为false, 是否正在移除题目。
    * `errorSignal`: Error | null, 初始为null, 错误信息。
* **暴露给View的接口 (数据和命令)**:
    * `wrongQuestions`: 只读，当前错题本数据。
    * `isLoading`: 只读，是否正在加载。
    * `isAdding`: 只读，是否正在添加题目。
    * `isRemoving`: 只读，是否正在移除题目。
    * `hasError`: 只读，是否有错误。
    * `errorMessage`: 只读，错误信息。
    * `isInWrongQuestions(questionId: string): boolean`: 检查题目是否在错题本中。
    * `addToWrongQuestions(questionId: string): Promise<boolean>`: 添加题目到错题本。
    * `removeFromWrongQuestions(questionId: string): Promise<boolean>`: 从错题本移除题目。
* **内部依赖**: 依赖Model层的API函数。
* **核心逻辑流程**: 
    1. 初始化时，从Model层获取当前用户的错题本数据
    2. 提供检查题目是否已在错题本中的方法
    3. 用户可以添加当前题目到错题本或从错题本移除
    4. 操作后更新本地状态，并通过API同步到服务端
* **复用性考量 (ViewModel)**: ViewModel设计为通用接口，通过配置项适应不同场景的需求，不包含特定业务场景的硬编码逻辑。

```typescript
// ViewModel Hook接口示例
function useWrongQuestionsViewModel(config?: WrongQuestionsConfig) {
  // 配置包含：用户ID, 课程ID(可选), 初始化行为等
  // ... state, effects, actions ...
  return { 
    wrongQuestions,
    isLoading,
    isAdding,
    isRemoving,
    hasError,
    errorMessage,
    isInWrongQuestions,
    addToWrongQuestions,
    removeFromWrongQuestions
  };
}
```

###### View层 (Pure UI Components)
* **组件结构**:
    ```mermaid
    graph TD
        WrongQuestionsView["错题本视图根组件 (WrongQuestionsView.tsx)"]
        WrongQuestionsView --> WrongQuestionsButton["错题本按钮 (WrongQuestionsButton.tsx)"]
        WrongQuestionsView --> WrongQuestionsPanel["错题本面板 (WrongQuestionsPanel.tsx)"]
        
        WrongQuestionsButton --> AddIcon["添加图标 (@/components/Icon)"]
        WrongQuestionsButton --> RemoveIcon["移除图标 (@/components/Icon)"]
        WrongQuestionsButton --> LoadingSpinner["加载指示器 (@/components/Spinner)"]
        
        WrongQuestionsPanel --> PanelHeader["面板头部 (PanelHeader.tsx)"]
        WrongQuestionsPanel --> WrongQuestionsList["错题列表 (WrongQuestionsList.tsx)"]
        WrongQuestionsPanel --> EmptyState["空状态提示 (EmptyState.tsx)"]
        WrongQuestionsPanel --> ActionButtons["操作按钮组 (ActionButtons.tsx)"]
        
        WrongQuestionsList --> WrongQuestionItem["错题项 (WrongQuestionItem.tsx)"]
    ```
* **核心View组件说明**:
    * `WrongQuestionsView`: 根组件，根据场景提供按钮或完整面板。
    * `WrongQuestionsButton`: 添加/移除错题的按钮组件，根据题目是否已在错题本中显示不同状态。
    * `WrongQuestionsPanel`: 错题本面板，用于展示和管理所有错题。
    * `WrongQuestionsList`: 错题列表，展示所有错题项。
    * `WrongQuestionItem`: 错题项组件，展示单个错题的简要信息。
* **复用性考量 (View)**: View层组件采用纯UI设计，提供多种显示模式（按钮、面板、列表等），以适应不同场景的需求。所有状态和逻辑通过props接收，不包含硬编码的特定场景逻辑。

##### ******* 关键交互流程 (Business View - Key Interaction Flow)

```mermaid
sequenceDiagram
    participant User
    participant View (WrongQuestionsView)
    participant ViewModel (useWrongQuestionsViewModel)
    participant Model (WrongQuestionsModel)
    participant API
    
    User->>View (WrongQuestionsView): 查看按钮状态
    View (WrongQuestionsView)->>ViewModel (useWrongQuestionsViewModel): 初始化
    ViewModel (useWrongQuestionsViewModel)->>Model (WrongQuestionsModel): 获取错题本数据
    Model (WrongQuestionsModel)->>API: 发起API请求
    API-->>Model (WrongQuestionsModel): 返回错题本数据
    Model (WrongQuestionsModel)-->>ViewModel (useWrongQuestionsViewModel): 返回处理后的数据
    ViewModel (useWrongQuestionsViewModel)->>ViewModel (useWrongQuestionsViewModel): 检查当前题目是否在错题本中
    ViewModel (useWrongQuestionsViewModel)->>View (WrongQuestionsView): 更新按钮状态
    View (WrongQuestionsView)->>User: 显示"加入错题本"或"已加入"状态
    
    alt 题目不在错题本中
        User->>View (WrongQuestionsView): 点击"加入错题本"按钮
        View (WrongQuestionsView)->>ViewModel (useWrongQuestionsViewModel): 调用addToWrongQuestions()
        ViewModel (useWrongQuestionsViewModel)->>ViewModel (useWrongQuestionsViewModel): 更新为添加中状态
        View (WrongQuestionsView)->>User: 显示加载状态
        ViewModel (useWrongQuestionsViewModel)->>Model (WrongQuestionsModel): 添加题目到错题本
        Model (WrongQuestionsModel)->>API: 发起API请求
        API-->>Model (WrongQuestionsModel): 返回操作结果
        Model (WrongQuestionsModel)-->>ViewModel (useWrongQuestionsViewModel): 返回操作结果
        ViewModel (useWrongQuestionsViewModel)->>ViewModel (useWrongQuestionsViewModel): 更新本地错题本数据
        ViewModel (useWrongQuestionsViewModel)->>View (WrongQuestionsView): 更新UI状态
        View (WrongQuestionsView)->>User: 显示"已加入"状态和成功提示
    else 题目已在错题本中
        User->>View (WrongQuestionsView): 点击"已加入"按钮
        View (WrongQuestionsView)->>ViewModel (useWrongQuestionsViewModel): 调用removeFromWrongQuestions()
        ViewModel (useWrongQuestionsViewModel)->>ViewModel (useWrongQuestionsViewModel): 更新为移除中状态
        View (WrongQuestionsView)->>User: 显示加载状态
        ViewModel (useWrongQuestionsViewModel)->>Model (WrongQuestionsModel): 从错题本移除题目
        Model (WrongQuestionsModel)->>API: 发起API请求
        API-->>Model (WrongQuestionsModel): 返回操作结果
        Model (WrongQuestionsModel)-->>ViewModel (useWrongQuestionsViewModel): 返回操作结果
        ViewModel (useWrongQuestionsViewModel)->>ViewModel (useWrongQuestionsViewModel): 更新本地错题本数据
        ViewModel (useWrongQuestionsViewModel)->>View (WrongQuestionsView): 更新UI状态
        View (WrongQuestionsView)->>User: 显示"加入错题本"状态和操作成功提示
    end
```

## 4. 关键技术实现

### 4.1 数据流方案
练习组件采用清晰的单向数据流设计，以ViewModel作为核心协调者:

1. **数据源层 (Model)**:
   - 负责从API获取原始数据（题目、转场配置、反馈动画等）
   - 提供数据转换与规范化功能，确保数据格式一致
   - 处理API交互和数据持久化

2. **业务逻辑层 (ViewModel)**:
   - 作为Model和View的桥梁，控制数据流向
   - 使用`@preact-signals/safe-react`管理响应式状态
   - 处理用户交互事件并更新状态
   - 封装复杂的业务逻辑（如题目切换、反馈动画控制）
   - 保持View层纯粹，仅传递必要信息给View

3. **表现层 (View)**:
   - 保持为纯UI组件，通过props接收数据和回调
   - 不包含业务逻辑，仅负责渲染和捕获用户交互
   - 将用户事件通过回调传递给ViewModel处理

这种模式确保了数据流的可预测性和状态变更的可追踪性，同时简化了组件测试和维护。

### 4.2 性能优化策略
针对练习组件的特点，重点优化以下几个方面:

1. **感知性能优化**:
   - 转场动画和反馈动画资源预加载，避免使用时延迟
   - 题目数据预取策略，在用户做当前题目时预加载下一题
   - 使用骨架屏替代loading状态，减少空白等待时间
   - 实现乐观更新，如答案提交后立即显示反馈，不等待服务器确认

2. **渲染性能**:
   - 利用`@preact-signals/safe-react`的精确更新特性，避免不必要的组件重渲染
   - 拆分大型组件为多个小型组件，隔离更新范围
   - 使用`React.memo`包装纯展示组件
   - 实现虚拟化渲染（如错题本列表）以处理大量数据

3. **资源优化**:
   - 动画资源优化（使用SVG或轻量级Lottie）
   - 按需加载辅助功能（勾画、答疑、错题本），避免初始加载过重
   - 使用WebP格式优化图片资源

### 4.3 错误处理机制
练习组件采用多层次的错误处理策略:

1. **网络错误处理**:
   - API请求错误统一捕获，使用SWR的错误重试机制
   - 离线状态检测与缓存策略，确保网络不稳定时用户仍能继续练习
   - 针对关键操作（如提交答案）实现重试逻辑

2. **用户操作错误处理**:
   - 输入验证，防止无效操作（如未选择选项就提交）
   - 防抖处理防止重复点击
   - 优雅降级策略，如动画资源加载失败时自动跳过

3. **UI错误反馈**:
   - 适当的错误提示，简洁明了不打断用户体验
   - 提供恢复操作建议
   - 持久化用户进度，避免因错误导致练习中断

## 5. 技术风险与应对

| 风险点描述                                      | 影响程度 (高/中/低) | 发生概率 (高/中/低) | 应对策略                                                                 |
| :---------------------------------------------- | :---------------- | :---------------- | :----------------------------------------------------------------------- |
| 转场动画和反馈动画资源加载延迟或失败                 | 高                | 中                | 预加载策略；渐进式加载；降级方案（简化版动画或直接跳过）；本地缓存                |
| 题目数据加载缓慢影响作答流畅性                    | 高                | 中                | 实现预取策略；骨架屏减少用户等待感知；离线缓存常见题型                      |
| 即时反馈判断延迟，影响用户体验                    | 高                | 中                | 前端先显示过渡动画；乐观更新策略；SWR缓存与预验证                          |
| 勾画功能在不同设备上的兼容性问题                  | 中                | 高                | 充分测试不同设备；适配不同输入模式（触控/鼠标）；降级方案                   |
| 复杂答疑场景下长轮询或WebSocket连接不稳定        | 中                | 中                | 实现重连机制；优雅降级为普通轮询；状态同步与恢复机制                       |
| 用户在题目间快速切换导致状态混乱或资源浪费        | 中                | 中                | 状态隔离设计；请求取消机制；防抖节流控制                                 |
| 连续错误导致用户挫折感                           | 高                | 中                | 实现智能提示系统；避免消极反馈连续出现；提供引导性提示                     |
| 在弱网或断网环境下练习体验严重下降               | 高                | 中                | 实现离线模式；本地缓存关键数据；断网提示与恢复机制                        |

## 6. 成果检验

### 6.1 技术指标
1. **响应时间**:
   - 练习题目加载时间 < 1.5秒 (95th percentile)
   - 用户提交答案后，反馈动画启动延迟 < 300ms
   - 题目间切换时间 < 1秒
   - 页面主要内容加载时间(LCP) < 2秒
   - 首次交互延迟(FID)或INP < 100ms

2. **资源优化**:
   - JavaScript bundle大小 < 250KB (gzip后)
   - 动画资源总大小 < 2MB
   - 首屏加载关键资源数 < 15个

3. **稳定性**:
   - 用户提交答案成功率 > 99.5%
   - 页面错误率 < 0.1%
   - 关键功能可用性 > 99.9%

### 6.2 质量标准
1. **代码质量**:
   - 单元测试覆盖率 > 80%
   - 组件测试覆盖率 > 70%
   - E2E测试覆盖所有关键用户流程
   - 零Sonar高严重级别问题
   - TypeScript类型覆盖率100%

2. **可访问性**:
   - 符合WCAG 2.1 AA级标准
   - 键盘操作支持所有核心功能
   - 屏幕阅读器可正确读取所有界面元素
   - 颜色对比度符合标准

3. **用户体验**:
   - 练习题完成率提升目标达到或超过8%
   - 用户满意度评分达到目标的4.2分（5分制）
   - 用户反馈中关于操作流畅度的正面评价 > 负面评价

## 附录: 快速开发指南

### 创建新页面与业务视图
1. **Page创建**: 在`app`目录下创建对应路由路径的`page.tsx`文件，导入并组合所需业务视图
2. **业务视图创建**: 
   - 页面特定业务视图: 在`app/views/{pageName}/`目录下创建对应文件
   - 可复用业务视图: 在`app/views/shared/`目录下创建

### MVVM分层开发规范
1. **Model层**: 
   - 创建`{name}Model.ts`文件，包含数据结构定义和API交互函数
   - 使用Zod进行数据校验和转换
   - 避免在Model中引入UI相关依赖

2. **ViewModel层**: 
   - 创建`use{Name}ViewModel.ts` Hook
   - 使用`@preact-signals/safe-react`管理状态
   - 封装所有业务逻辑，确保View层纯净
   - 明确暴露给View的接口，保持最小必要原则

3. **View层**: 
   - 创建纯UI组件，通过props接收所有数据和回调
   - 组件拆分遵循单一职责原则
   - 使用`@/components`组件库保持视觉一致性
   - 遵循Tailwind CSS规范

### 状态管理使用规范
1. **信号(Signals)使用原则**: 
   - 粒度适中，避免过细拆分
   - 相关信号组合为对象信号
   - 派生信号处理计算属性

2. **全局状态**: 
   - 仅真正全局的状态才放入全局Context
   - 使用`createContext`和自定义Hook封装全局状态
   - 全局ViewModel可被业务视图ViewModel访问

### 复用性设计原则
1. **参数化接口**: 所有可复用组件应提供灵活的配置项
2. **环境无关**: 不依赖特定上下文环境，通过props注入依赖
3. **样式一致性**: 使用`@/components`和Tailwind确保视觉统一
4. **文档化**: 为可复用组件提供清晰的JSDoc说明和使用示例

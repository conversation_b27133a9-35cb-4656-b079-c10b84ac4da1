import { ReactNode } from 'react';

interface CardProps {
  title: string;
  description: string;
  icon?: ReactNode;
  href: string;
}

const Card = ({ title, description, icon, href }: CardProps) => {
  return (
    <a
      href={href}
      className="block group p-6 bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 hover:border-blue-300"
    >
      <div className="flex items-center mb-4">
        {icon && (
          <div className="mr-4 text-blue-500 flex-shrink-0">
            {icon}
          </div>
        )}
        <h3 className="text-lg font-medium text-gray-900 group-hover:text-blue-600">
          {title}
        </h3>
      </div>
      <p className="text-sm text-gray-500">{description}</p>
    </a>
  );
};

export default Card; 
/**
 * 环境配置文件
 * 集中管理所有环境相关的配置
 * 
 * 配置值从环境变量中读取，环境变量由 Next.js 根据当前环境自动加载：
 * - 开发环境: .env.development
 * - 测试环境: .env.test
 * - 生产环境: .env.production
 */

// 检查是否在服务器端运行
const isServer = typeof window === 'undefined';

// 统一的配置对象
export const config = {
  // 前端服务配置
  port: process.env.PORT || '3000',
  
  // API配置
  apiHost: process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000',
  apiPath: process.env.NEXT_PUBLIC_API_PATH || '/api',
  apiUrl: `${process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:8000'}${process.env.NEXT_PUBLIC_API_PATH || '/api'}`,
  
  // 功能开关
  useRemoteApi: process.env.NEXT_PUBLIC_USE_REMOTE_API === 'true',
  
  // 环境标识
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test',
  nodeEnv: process.env.NODE_ENV || 'development',
};

// 兼容旧代码的导出（保持向后兼容）
export const API_URL = config.apiUrl;
export const HOST_URL = config.apiHost;

// 其他通用配置
export const API_TIMEOUT = 30000; // 30秒
export const API_RETRY_TIMES = 3; // 请求失败重试次数

// 环境标识
export const NODE_ENV = config.nodeEnv;
export const IS_PRODUCTION = config.isProduction;
export const IS_DEVELOPMENT = config.isDevelopment;
export const IS_TEST = config.isTest;

// 功能开关
export const ENABLE_DEBUG = process.env.NEXT_PUBLIC_ENABLE_DEBUG === 'true';
export const USE_REMOTE_API = config.useRemoteApi;

// 导出完整配置对象
const ENV_CONFIG = {
  API_URL,
  HOST_URL,
  API_TIMEOUT,
  API_RETRY_TIMES,
  NODE_ENV,
  IS_PRODUCTION,
  IS_DEVELOPMENT,
  IS_TEST,
  ENABLE_DEBUG,
  USE_REMOTE_API,
  ...config,
};

// 在服务器端启动时打印配置信息（便于调试）
if (isServer && IS_DEVELOPMENT) {
  console.log('🔧 环境配置已加载:');
  console.log('  - NODE_ENV:', NODE_ENV);
  console.log('  - API_URL:', API_URL);
  console.log('  - HOST_URL:', HOST_URL);
  console.log('  - USE_REMOTE_API:', USE_REMOTE_API);
  console.log('  - PORT:', config.port);
}

export default ENV_CONFIG;
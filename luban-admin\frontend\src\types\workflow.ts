export type WorkflowStatus = 'draft' | 'in_progress' | 'completed' | 'failed';
export type WorkflowStep = 'document' | 'prd' | 'td' | 'dd' | 'api';

export interface WorkflowStepData {
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  startedAt?: string;
  completedAt?: string;
  data?: any;
  output?: any;
  error?: string;
}

export interface Workflow {
  id: string;
  name: string;
  description?: string;
  status: WorkflowStatus;
  currentStep: WorkflowStep;
  progress: number;
  createdAt: string;
  updatedAt: string;
  steps: {
    document: WorkflowStepData;
    prd: WorkflowStepData;
    td: WorkflowStepData;
    dd: WorkflowStepData;
    api: WorkflowStepData;
  };
}

export interface WorkflowSummary {
  id: string;
  name: string;
  description?: string;
  status: WorkflowStatus;
  currentStep: WorkflowStep;
  progress: number;
  createdAt: string;
  updatedAt: string;
}

export interface CreateWorkflowRequest {
  name: string;
  description?: string;
}

export interface UpdateWorkflowRequest {
  name?: string;
  description?: string;
  status?: WorkflowStatus;
  currentStep?: WorkflowStep;
} 
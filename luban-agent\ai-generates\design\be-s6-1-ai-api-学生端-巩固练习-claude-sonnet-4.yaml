openapi: 3.0.3
info:
  title: 学生端巩固练习系统 API
  description: |
    提供学生端巩固练习系统的核心功能，包括练习会话管理、智能推题、作答处理、学习报告生成、错题本管理等功能。
    通过智能化的练习平台提升学生在AI课后的知识掌握程度和学习效果，将知识从"知道"转变为"会用"。
    本文档遵循 OpenAPI 3.0.3 规范。
  version: v1.0.0
  contact:
    name: 巩固练习系统开发团队
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api/v1
    description: 本地开发服务器
  - url: https://api.example.com/api/v1
    description: 生产环境服务器

tags:
  - name: ExerciseSessions
    description: 练习会话管理相关接口
  - name: LearningReports
    description: 学习报告相关接口
  - name: WrongQuestions
    description: 错题本管理相关接口

components:
  schemas:
    # 通用响应结构
    GenericResponse:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码，0表示成功，非0表示错误
          example: 0
        message:
          type: string
          description: 响应消息
          example: "Success"
        data:
          type: object
          description: 响应数据
          nullable: true
        pageInfo:
          $ref: '#/components/schemas/PageInfo'

    GenericError:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码
          example: 4000001
        message:
          type: string
          description: 用户可读的错误信息
          example: '无效的参数'
        details:
          type: string
          description: 可选的详细错误信息或调试信息
          nullable: true
          example: '字段 [studentId] 不能为空'
        data:
          type: object
          description: 可选的附加数据
          nullable: true

    PageInfo:
      type: object
      description: 分页信息
      properties:
        page:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          example: 20
        total:
          type: integer
          description: 总条目数
          example: 100

    # 练习会话相关Schema
    ExerciseSessionEnterResponse:
      type: object
      description: 进入练习响应数据
      properties:
        sessionId:
          type: integer
          format: int64
          description: 练习会话ID
          example: 1001
        sessionStatus:
          type: integer
          description: 练习状态 (0:未开始, 1:练习中, 2:已完成, 3:已退出)
          example: 1
        sessionType:
          type: integer
          description: 会话类型 (1:正常练习, 2:再练一次)
          example: 1
        currentQuestionIndex:
          type: integer
          description: 当前题目序号
          example: 0
        totalQuestions:
          type: integer
          description: 预估题目总数
          example: 8
        entryButtonType:
          type: string
          description: 入口按钮类型
          enum: ["enter_exercise", "continue_exercise", "view_report", "retry_exercise"]
          example: "enter_exercise"
        courseId:
          type: integer
          format: int64
          description: 课程ID
          example: 2001
        welcomeConfig:
          $ref: '#/components/schemas/WelcomeConfig'

    NextQuestionResponse:
      type: object
      description: 获取下一题响应数据
      properties:
        questionId:
          type: integer
          format: int64
          description: 题目ID
          example: 3001
        questionIndex:
          type: integer
          description: 题目在练习中的序号
          example: 1
        questionContent:
          type: string
          description: 题目内容
          example: "下列哪个选项是正确的？"
        questionType:
          type: string
          description: 题目类型
          example: "single_choice"
        questionDifficulty:
          type: number
          format: float
          description: 题目难度系数
          example: 1.5
        options:
          type: array
          description: 题目选项
          items:
            type: object
            properties:
              optionId:
                type: string
                description: 选项ID
                example: "A"
              optionContent:
                type: string
                description: 选项内容
                example: "选项A的内容"
        progressInfo:
          type: object
          description: 进度信息
          properties:
            currentProgress:
              type: number
              format: float
              description: 当前进度百分比
              example: 12.5
            completedQuestions:
              type: integer
              description: 已完成题目数
              example: 1
        hasNextQuestion:
          type: boolean
          description: 是否有下一题
          example: true

    SubmitAnswerRequest:
      type: object
      description: 提交答案请求体
      properties:
        sessionId:
          type: integer
          format: int64
          description: 练习会话ID
          example: 1001
        questionId:
          type: integer
          format: int64
          description: 题目ID
          example: 3001
        studentAnswer:
          type: string
          description: 学生提交的答案
          example: "A"
        answerDuration:
          type: integer
          description: 作答时长（秒）
          example: 30
      required:
        - sessionId
        - questionId
        - studentAnswer
        - answerDuration

    SubmitAnswerResponse:
      type: object
      description: 提交答案响应数据
      properties:
        answerResult:
          type: integer
          description: 作答结果 (1:正确, 2:错误, 3:部分正确)
          example: 1
        feedbackType:
          type: string
          description: 反馈类型
          enum: ["correct", "incorrect", "partial_correct", "continuous_correct", "difficulty_up", "difficulty_down"]
          example: "correct"
        continuousCorrect:
          type: boolean
          description: 是否连续正确
          example: true
        continuousCount:
          type: integer
          description: 连续正确次数
          example: 3
        nextQuestionInfo:
          $ref: '#/components/schemas/NextQuestionResponse'
        exerciseCompleted:
          type: boolean
          description: 练习是否完成
          example: false

    SaveDrawingRequest:
      type: object
      description: 保存勾画记录请求体
      properties:
        sessionId:
          type: integer
          format: int64
          description: 练习会话ID
          example: 1001
        questionId:
          type: integer
          format: int64
          description: 题目ID
          example: 3001
        drawingToolType:
          type: integer
          description: 勾画工具类型 (1:画笔, 2:荧光笔, 3:橡皮擦, 4:标记点)
          example: 1
        drawingColor:
          type: string
          description: 勾画颜色
          example: "#FF0000"
        coordinateData:
          type: object
          description: 坐标数据
          example: {"points": [{"x": 100, "y": 200}, {"x": 150, "y": 250}]}
      required:
        - sessionId
        - questionId
        - drawingToolType
        - coordinateData

    ExerciseHistoryResponse:
      type: object
      description: 练习历史响应数据
      properties:
        sessionId:
          type: integer
          format: int64
          description: 练习会话ID
          example: 1001
        courseId:
          type: integer
          format: int64
          description: 课程ID
          example: 2001
        sessionType:
          type: integer
          description: 会话类型 (1:正常练习, 2:再练一次)
          example: 1
        sessionStatus:
          type: integer
          description: 练习状态 (0:未开始, 1:练习中, 2:已完成, 3:已退出)
          example: 2
        startTime:
          type: integer
          format: int64
          description: 开始时间（UTC秒数）
          example: 1717488000
        endTime:
          type: integer
          format: int64
          description: 结束时间（UTC秒数）
          nullable: true
          example: 1717489800
        totalQuestions:
          type: integer
          description: 题目总数
          example: 8
        completedQuestions:
          type: integer
          description: 已完成题目数
          example: 8

    # 学习报告相关Schema
    LearningReportResponse:
      type: object
      description: 学习报告响应数据
      properties:
        reportId:
          type: integer
          format: int64
          description: 报告ID
          example: 5001
        sessionId:
          type: integer
          format: int64
          description: 练习会话ID
          example: 1001
        masteryChange:
          type: number
          format: float
          description: 掌握度变化值
          example: 15.5
        studyDuration:
          type: integer
          description: 学习时长（分钟）
          example: 25
        questionCount:
          type: integer
          description: 题目数量
          example: 8
        accuracyRate:
          type: number
          format: float
          description: 正确率百分比
          example: 87.5
        ipFeedback:
          type: string
          description: IP角色反馈文案
          example: "太棒了！你的表现非常出色！"
        recommendRetry:
          type: boolean
          description: 是否推荐再练一次
          example: false
        reportData:
          type: object
          description: 详细报告数据
          properties:
            questionDetails:
              type: array
              description: 题目详情列表
              items:
                type: object
                properties:
                  questionId:
                    type: integer
                    format: int64
                    description: 题目ID
                    example: 3001
                  questionIndex:
                    type: integer
                    description: 题目序号
                    example: 1
                  answerResult:
                    type: integer
                    description: 作答结果 (1:正确, 2:错误, 3:部分正确)
                    example: 1
                  answerDuration:
                    type: integer
                    description: 作答时长（秒）
                    example: 30
        generatedTime:
          type: integer
          format: int64
          description: 报告生成时间（UTC秒数）
          example: 1717489800

    # 错题本相关Schema
    WrongQuestionResponse:
      type: object
      description: 错题记录响应数据
      properties:
        wrongQuestionId:
          type: integer
          format: int64
          description: 错题记录ID
          example: 6001
        questionId:
          type: integer
          format: int64
          description: 题目ID
          example: 3001
        addMethod:
          type: integer
          description: 添加方式 (1:自动添加, 2:手动添加)
          example: 1
        errorReasonTags:
          type: array
          description: 错因标签
          items:
            type: string
          example: ["计算错误", "概念理解不清"]
        addTime:
          type: integer
          format: int64
          description: 添加时间（UTC秒数）
          example: 1717488000
        questionContent:
          type: string
          description: 题目内容
          example: "下列哪个选项是正确的？"

    AddWrongQuestionRequest:
      type: object
      description: 添加错题请求体
      properties:
        studentId:
          type: integer
          format: int64
          description: 学生ID
          example: 1001
        questionId:
          type: integer
          format: int64
          description: 题目ID
          example: 3001
        errorReasonTags:
          type: array
          description: 错因标签
          items:
            type: string
          example: ["计算错误"]
        addMethod:
          type: integer
          description: 添加方式 (1:自动添加, 2:手动添加)
          example: 2
        answerRecordId:
          type: integer
          format: int64
          description: 关联的作答记录ID（自动添加时必填）
          nullable: true
          example: 4001
      required:
        - studentId
        - questionId
        - addMethod

    WelcomeConfig:
      type: object
      description: 欢迎场景配置
      properties:
        welcomeSceneType:
          type: string
          description: 欢迎场景类型
          enum: ["default", "encouragement", "celebration", "motivation"]
          example: "encouragement"
        welcomeTitle:
          type: string
          description: 欢迎标题
          example: "开始你的巩固练习之旅！"
        welcomeMessage:
          type: string
          description: 欢迎消息
          example: "通过练习巩固知识，让学习更有效果"
        welcomeImageUrl:
          type: string
          description: 欢迎图片URL
          nullable: true
          example: "https://example.com/images/welcome.png"
        ipCharacterConfig:
          type: object
          description: IP角色配置
          properties:
            characterName:
              type: string
              description: 角色名称
              example: "小助手"
            characterImageUrl:
              type: string
              description: 角色头像URL
              example: "https://example.com/images/character.png"
            characterGreeting:
              type: string
              description: 角色问候语
              example: "嗨！准备好开始练习了吗？"
        buttonConfig:
          type: object
          description: 按钮配置
          properties:
            buttonText:
              type: string
              description: 按钮文本
              example: "开始练习"
            buttonStyle:
              type: string
              description: 按钮样式
              enum: ["primary", "secondary", "success", "warning"]
              example: "primary"
        animationConfig:
          type: object
          description: 动画配置
          nullable: true
          properties:
            enableAnimation:
              type: boolean
              description: 是否启用动画
              example: true
            animationType:
              type: string
              description: 动画类型
              enum: ["fade", "slide", "bounce", "none"]
              example: "fade"

  parameters:
    studentIdQueryParameter:
      name: studentId
      in: query
      required: true
      description: 学生ID
      schema:
        type: integer
        format: int64
        example: 1001

    courseIdQueryParameter:
      name: courseId
      in: query
      required: true
      description: 课程ID
      schema:
        type: integer
        format: int64
        example: 2001

    sessionIdQueryParameter:
      name: sessionId
      in: query
      required: true
      description: 练习会话ID
      schema:
        type: integer
        format: int64
        example: 1001

    wrongQuestionIdQueryParameter:
      name: wrongQuestionId
      in: query
      required: true
      description: 错题记录ID
      schema:
        type: integer
        format: int64
        example: 6001

    pageQueryParameter:
      name: page
      in: query
      description: 页码 (从1开始)
      required: false
      schema:
        type: integer
        default: 1
        minimum: 1

    pageSizeQueryParameter:
      name: pageSize
      in: query
      description: 每页数量
      required: false
      schema:
        type: integer
        default: 20
        minimum: 1
        maximum: 100

    startTimeQueryParameter:
      name: startTime
      in: query
      description: 开始时间（UTC秒数）
      required: false
      schema:
        type: integer
        format: int64

    endTimeQueryParameter:
      name: endTime
      in: query
      description: 结束时间（UTC秒数）
      required: false
      schema:
        type: integer
        format: int64

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "请输入JWT Token, 格式为: Bearer {token}"

  responses:
    UnauthorizedError:
      description: 未认证或认证令牌无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4010001
                message: "需要认证或认证失败"

    ForbiddenError:
      description: 无权限访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4030001
                message: "无权限执行此操作"

    NotFoundError:
      description: 请求的资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4040001
                message: "请求的资源不存在"

    BadRequestError:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4000001
                message: "请求参数错误"

paths:
  /exercise_sessions/enter:
    get:
      tags:
        - ExerciseSessions
      summary: 获取练习入口信息并自动创建/恢复会话
      description: |
        学生进入巩固练习时调用此接口，系统自动检查是否有未完成的练习会话。
        如有未完成会话则自动恢复，如无则创建新的练习会话。
        返回练习状态和入口信息，支持"进入练习"、"继续练习"、"看报告"、"再练一次"等不同状态。
        同时从教师端服务获取课程的欢迎场景配置，用于前端渲染个性化的欢迎界面。
      operationId: enterExerciseSession
      parameters:
        - $ref: '#/components/parameters/studentIdQueryParameter'
        - $ref: '#/components/parameters/courseIdQueryParameter'
      responses:
        '200':
          description: 成功获取练习入口信息
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ExerciseSessionEnterResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
      security:
        - BearerAuth: []

  /exercise_sessions/next_question:
    get:
      tags:
        - ExerciseSessions
      summary: 获取下一题
      description: |
        基于掌握度的智能推题，获取当前或下一道题目。
        进入练习后获取第一题，作答后获取下一题。
        当返回"hasNextQuestion"为false时表示练习结束，可调用学习报告接口。
      operationId: getNextQuestion
      parameters:
        - $ref: '#/components/parameters/sessionIdQueryParameter'
      responses:
        '200':
          description: 成功获取题目信息
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/NextQuestionResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /exercise_sessions/submit_answer:
    post:
      tags:
        - ExerciseSessions
      summary: 提交题目答案并获取反馈
      description: |
        学生提交题目答案，系统判断答案正确性，记录作答详情，提供即时的情感化反馈。
        包含正确/错误/连胜状态反馈，并自动推进到下一题或完成练习。
        如果答错，系统内部自动添加错题到错题本。
        支持防重复提交机制。
      operationId: submitAnswer
      requestBody:
        description: 提交答案信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitAnswerRequest'
            examples:
              default:
                value:
                  sessionId: 1001
                  questionId: 3001
                  studentAnswer: "A"
                  answerDuration: 30
      responses:
        '200':
          description: 成功提交答案并获取反馈
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/SubmitAnswerResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /exercise_sessions/drawings:
    post:
      tags:
        - ExerciseSessions
      summary: 保存勾画记录
      description: |
        保存学生在答题过程中的勾画标记数据。
        支持多种勾画工具类型和颜色，记录坐标数据。
        此接口为辅助功能，可异步处理。
      operationId: saveDrawingRecord
      requestBody:
        description: 勾画记录信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaveDrawingRequest'
            examples:
              default:
                value:
                  sessionId: 1001
                  questionId: 3001
                  drawingToolType: 1
                  drawingColor: "#FF0000"
                  coordinateData: {"points": [{"x": 100, "y": 200}, {"x": 150, "y": 250}]}
      responses:
        '200':
          description: 成功保存勾画记录
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          saved:
                            type: boolean
                            description: 保存状态
                            example: true
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
      security:
        - BearerAuth: []

  /exercise_sessions/exit:
    put:
      tags:
        - ExerciseSessions
      summary: 退出练习会话
      description: |
        学生中途退出练习时调用此接口，系统自动保存当前进度。
        支持缓存和数据库双重保障，确保进度不丢失。
        后续可通过进入练习接口自动恢复会话。
      operationId: exitExerciseSession
      parameters:
        - $ref: '#/components/parameters/sessionIdQueryParameter'
      responses:
        '200':
          description: 成功退出练习会话
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          exited:
                            type: boolean
                            description: 退出状态
                            example: true
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /exercise_sessions/history:
    get:
      tags:
        - ExerciseSessions
      summary: 获取练习历史记录
      description: |
        查询学生的练习历史记录，支持分页和时间范围筛选。
        返回练习会话列表，包括练习状态、完成时间等信息。
        支持按时间和课程筛选。
      operationId: getExerciseHistory
      parameters:
        - $ref: '#/components/parameters/studentIdQueryParameter'
        - $ref: '#/components/parameters/pageQueryParameter'
        - $ref: '#/components/parameters/pageSizeQueryParameter'
        - $ref: '#/components/parameters/startTimeQueryParameter'
        - $ref: '#/components/parameters/endTimeQueryParameter'
        - name: courseId
          in: query
          description: 课程ID筛选
          required: false
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 成功获取练习历史记录
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/ExerciseHistoryResponse'
                      pageInfo:
                        $ref: '#/components/schemas/PageInfo'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
      security:
        - BearerAuth: []

  /learning_reports:
    get:
      tags:
        - LearningReports
      summary: 获取学习报告
      description: |
        获取练习完成后的学习成果报告，包含掌握度变化、学习统计、IP反馈等信息。
        支持报告生成和查看合并，包含掌握度变化展示和再练推荐。
        当练习完成时（hasNextQuestion为false）可调用此接口。
      operationId: getLearningReport
      parameters:
        - $ref: '#/components/parameters/sessionIdQueryParameter'
        - $ref: '#/components/parameters/studentIdQueryParameter'
      responses:
        '200':
          description: 成功获取学习报告
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/LearningReportResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /wrong_questions:
    get:
      tags:
        - WrongQuestions
      summary: 获取学生错题本
      description: |
        查询学生的错题记录列表，支持分页和多维度筛选。
        返回错题列表，包括错因标签、添加时间等信息。
        支持按添加方式、时间等条件筛选。
      operationId: getWrongQuestions
      parameters:
        - $ref: '#/components/parameters/studentIdQueryParameter'
        - $ref: '#/components/parameters/pageQueryParameter'
        - $ref: '#/components/parameters/pageSizeQueryParameter'
        - name: addMethod
          in: query
          description: 添加方式筛选 (1:自动添加, 2:手动添加)
          required: false
          schema:
            type: integer
            enum: [1, 2]
        - name: courseId
          in: query
          description: 课程ID筛选
          required: false
          schema:
            type: integer
            format: int64
      responses:
        '200':
          description: 成功获取错题本列表
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/WrongQuestionResponse'
                      pageInfo:
                        $ref: '#/components/schemas/PageInfo'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
      security:
        - BearerAuth: []

    post:
      tags:
        - WrongQuestions
      summary: 添加错题到错题本
      description: |
        添加错题到错题本，支持自动添加（答错时）和手动添加。
        自动添加时需要提供作答记录ID，手动添加时可选择错因标签。
        支持去重处理，防止重复添加同一题目。
      operationId: addWrongQuestion
      requestBody:
        description: 添加错题信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddWrongQuestionRequest'
            examples:
              auto_add:
                summary: 自动添加错题
                value:
                  studentId: 1001
                  questionId: 3001
                  addMethod: 1
                  answerRecordId: 4001
                  errorReasonTags: ["计算错误"]
              manual_add:
                summary: 手动添加错题
                value:
                  studentId: 1001
                  questionId: 3002
                  addMethod: 2
                  errorReasonTags: ["需要复习"]
      responses:
        '200':
          description: 成功添加错题
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          wrongQuestionId:
                            type: integer
                            format: int64
                            description: 错题记录ID
                            example: 6001
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '409':
          description: 错题已存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                default:
                  value:
                    code: 4090001
                    message: "该题目已在错题本中"
      security:
        - BearerAuth: []

    delete:
      tags:
        - WrongQuestions
      summary: 从错题本删除错题
      description: |
        从学生错题本中删除指定的错题记录。
        支持批量删除，通过错题记录ID列表进行删除操作。
        删除后不可恢复，请谨慎操作。
      operationId: deleteWrongQuestions
      parameters:
        - $ref: '#/components/parameters/studentIdQueryParameter'
        - name: wrongQuestionIds
          in: query
          description: 错题记录ID列表，多个ID用逗号分隔
          required: true
          schema:
            type: string
            example: "6001,6002,6003"
      responses:
        '200':
          description: 成功删除错题
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          deletedCount:
                            type: integer
                            description: 成功删除的错题数量
                            example: 3
                          failedIds:
                            type: array
                            description: 删除失败的错题ID列表
                            items:
                              type: integer
                              format: int64
                            example: []
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /wrong_questions/manual:
    post:
      tags:
        - WrongQuestions
      summary: 手动添加错题到错题本
      description: |
        学生手动添加错题到错题本的专用接口。
        与自动添加不同，手动添加不需要作答记录ID，但需要学生主动选择错因标签。
        支持题目验证和去重处理。
      operationId: manualAddWrongQuestion
      requestBody:
        description: 手动添加错题信息
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                studentId:
                  type: integer
                  format: int64
                  description: 学生ID
                  example: 1001
                questionId:
                  type: integer
                  format: int64
                  description: 题目ID
                  example: 3001
                errorReasonTags:
                  type: array
                  description: 错因标签
                  items:
                    type: string
                  example: ["需要复习", "概念不清"]
                notes:
                  type: string
                  description: 学生备注
                  nullable: true
                  example: "这道题需要重点复习"
              required:
                - studentId
                - questionId
                - errorReasonTags
            examples:
              default:
                value:
                  studentId: 1001
                  questionId: 3001
                  errorReasonTags: ["需要复习"]
                  notes: "这道题需要重点复习"
      responses:
        '200':
          description: 成功手动添加错题
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          wrongQuestionId:
                            type: integer
                            format: int64
                            description: 错题记录ID
                            example: 6001
                          addMethod:
                            type: integer
                            description: 添加方式 (固定为2:手动添加)
                            example: 2
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          description: 题目不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                default:
                  value:
                    code: 4040002
                    message: "指定的题目不存在"
        '409':
          description: 错题已存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                default:
                  value:
                    code: 4090001
                    message: "该题目已在错题本中"
      security:
        - BearerAuth: []

security:
  - BearerAuth: []
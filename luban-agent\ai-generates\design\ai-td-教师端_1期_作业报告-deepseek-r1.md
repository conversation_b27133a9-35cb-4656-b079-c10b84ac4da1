# 技术架构设计文档：教师端作业报告 V1.0

## 1. 引言
### 1.1 文档目的
为教师端作业报告功能提供可落地的技术架构方案，支撑核心P0需求实现...

### 1.2 参考资料
- [需求文档] ai-prd-教师端_1期_作业报告_analyzed-Gemini-2.5-Pro.md
- [技术栈约束] backend/global_rules/rules-tech-stack.mdc

## 2. 架构设计概述
### 2.1 需求分析摘要
- **核心业务能力**：
  1. 多源数据聚合（作业元数据+学生作答+题目信息）
  2. 动态策略计算（鼓励/关注学生识别）
  3. 实时报表渲染（班级/学生维度统计）

- **质量属性要求**：
  - 性能：95%的报表请求响应时间<800ms
  - 可扩展性：支持未来新增分析维度

### 2.2 技术架构方案
采用分层架构实现能力隔离：

```mermaid
flowchart TD
    classDef layer fill:#f96,stroke:#333;
    
    subgraph 接口层
        API[[REST API]]
        Event[[领域事件监听]]
    end
    
    subgraph 应用层
        Coordinator[[协调服务]]
        Calculator[[统计计算]]
    end
    
    subgraph 领域层
        AnalysisCore[[分析核心]]
        StrategyEngine[[策略引擎]]
    end
    
    subgraph 基础设施层
        DataAdapter[[数据适配器]]
        Cache[[缓存集群]]
    end
    
    API --> Coordinator
    Event --> Coordinator
    Coordinator --> AnalysisCore
    AnalysisCore --> StrategyEngine
    StrategyEngine --> DataAdapter
    DataAdapter --> Cache
```

## 3. 详细架构设计
### 3.1 逻辑架构
#### 3.2.1 动态分层模型
```mermaid
flowchart TD
    classDef layer fill:#f9f,stroke:#333;
    
    subgraph 接口层
        Gateway[[API Gateway]]
        MQ[[消息队列]]
    end
    
    subgraph 应用服务层
        ReportService[[报表服务]]
        StrategyService[[策略服务]]
    end
    
    subgraph 领域层
        DomainModel[[作业分析领域模型]]
        RuleEngine[[规则引擎]]
    end
    
    subgraph 基础设施层
        TaskSystem[[任务系统适配]]
        AnswerSystem[[答题系统适配]]
    end
    
    Gateway --> ReportService
    MQ --> StrategyService
    ReportService --> DomainModel
    StrategyService --> RuleEngine
    DomainModel --> TaskSystem
    DomainModel --> AnswerSystem
```

### 3.4 关键业务流程
#### 3.4.1 报表生成流程
```mermaid
sequenceDiagram
    participant T as 教师端
    participant G as Gateway
    participant R as ReportService
    participant D as DomainModel
    participant S as 任务系统
    
    T->>G: GET /reports/{reportId}
    G->>R: 路由请求
    R->>D: 创建分析上下文
    D->>S: 获取作业元数据
    S-->>D: 返回任务详情
    D->>D: 计算班级统计指标
    D->>D: 应用飘色规则
    D-->>R: 返回领域对象
    R->>G: 组装DTO
    G->>T: 返回200 OK
```

## 4. Golang技术栈选型
| 组件类型       | 选型方案          | 适用场景              |
|----------------|-------------------|-----------------------|
| Web框架        | Gin               | 高性能API路由         |
| 缓存组件       | Redis+go-redis    | 分布式缓存管理        |
| 异步计算       | asynq             | 基于Redis的任务队列   |
| 监控体系       | Prometheus+grafana| 运行时指标采集        |

## 5. 非功能需求
（本期需求文档未明确性能、安全等非功能指标，遵循平台默认标准）

## 6. 部署架构
（采用标准容器化部署方案，由运维团队统一管理）

## 7. 演进路线
（待二期需求明确后补充技术演进规划）

## 8. 架构文档自查清单
### 8.1 架构完备性自查
| 检查项                          | 结果  | 说明                  |
|---------------------------------|-------|-----------------------|
| 是否覆盖所有P0功能需求          | ✅    | 完整映射PRD 2.1章节   |
| 是否遵循分层架构原则            | ✅    | 四层结构清晰          |

### 8.2 需求-架构映射
| PRD需求点                  | 架构支撑点                | 覆盖状态 |
|----------------------------|---------------------------|----------|
| 学生报告Tab策略计算         | 策略服务+规则引擎         | ✅       |
| 答题结果Tab数据聚合         | 领域模型+数据适配器       | ✅       |
``` 
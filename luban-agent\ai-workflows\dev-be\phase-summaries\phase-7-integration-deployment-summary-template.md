# Phase 7: 集成测试和部署完成汇总

**项目名称**: {project_name}
**业务模块**: {biz_module}
**完成时间**: {completion_time}
**执行人**: {developer_name}

## 1. 已生成文件清单

### 1.1 集成测试文件
- `test/integration/{biz_module}_test.go` - {biz_module}集成测试
- `test/integration/api_test.go` - API集成测试
- `test/integration/database_test.go` - 数据库集成测试
- `test/integration/cache_test.go` - 缓存集成测试
- `test/integration/auth_test.go` - 认证集成测试

### 1.2 端到端测试文件
- `test/e2e/{biz_module}_e2e_test.go` - {biz_module}端到端测试
- `test/e2e/user_journey_test.go` - 用户流程测试
- `test/e2e/performance_test.go` - 性能测试
- `test/e2e/load_test.go` - 负载测试

### 1.3 部署配置文件
- `deployments/docker/Dockerfile` - Docker镜像配置
- `deployments/docker/docker-compose.yml` - Docker Compose配置
- `deployments/k8s/deployment.yaml` - Kubernetes部署配置
- `deployments/k8s/service.yaml` - Kubernetes服务配置
- `deployments/k8s/ingress.yaml` - Kubernetes入口配置
- `deployments/k8s/configmap.yaml` - Kubernetes配置映射

### 1.4 CI/CD配置文件
- `.github/workflows/ci.yml` - GitHub Actions CI配置
- `.github/workflows/cd.yml` - GitHub Actions CD配置
- `scripts/build.sh` - 构建脚本
- `scripts/deploy.sh` - 部署脚本
- `scripts/test.sh` - 测试脚本

### 1.5 监控配置文件
- `deployments/monitoring/prometheus.yml` - Prometheus配置
- `deployments/monitoring/grafana-dashboard.json` - Grafana仪表板
- `deployments/monitoring/alertmanager.yml` - 告警配置
- `deployments/logging/filebeat.yml` - 日志收集配置

### 1.6 文档文件
- `docs/deployment.md` - 部署文档
- `docs/api.md` - API文档
- `docs/troubleshooting.md` - 故障排除文档
- `docs/monitoring.md` - 监控文档
- `README.md` - 项目说明文档

## 2. 集成测试汇总

### 2.1 数据库集成测试
- **文件位置**: `test/integration/database_test.go`
```go
// TestDatabaseIntegration 数据库集成测试
func TestDatabaseIntegration(t *testing.T) {
    // 设置测试数据库
    db := setupTestDatabase()
    defer cleanupTestDatabase(db)
    
    t.Run("数据库连接测试", func(t *testing.T) {
        err := db.Ping()
        assert.NoError(t, err, "数据库连接失败")
    })
    
    t.Run("事务测试", func(t *testing.T) {
        tx := db.Begin()
        
        // 创建测试数据
        entity := &entity.{Entity1}{
            {Field1}: "test_value",
            {Field2}: "test_value",
            Status:   1,
        }
        
        err := tx.Create(entity).Error
        assert.NoError(t, err, "创建数据失败")
        
        // 回滚事务
        tx.Rollback()
        
        // 验证数据未提交
        var count int64
        db.Model(&entity.{Entity1}{}).Where("{field1} = ?", "test_value").Count(&count)
        assert.Equal(t, int64(0), count, "事务回滚失败")
    })
    
    t.Run("并发测试", func(t *testing.T) {
        var wg sync.WaitGroup
        errors := make(chan error, 10)
        
        for i := 0; i < 10; i++ {
            wg.Add(1)
            go func(index int) {
                defer wg.Done()
                
                entity := &entity.{Entity1}{
                    {Field1}: fmt.Sprintf("concurrent_test_%d", index),
                    {Field2}: "test_value",
                    Status:   1,
                }
                
                if err := db.Create(entity).Error; err != nil {
                    errors <- err
                }
            }(i)
        }
        
        wg.Wait()
        close(errors)
        
        for err := range errors {
            assert.NoError(t, err, "并发操作失败")
        }
    })
}
```

### 2.2 API集成测试
- **文件位置**: `test/integration/api_test.go`
```go
// TestAPIIntegration API集成测试
func TestAPIIntegration(t *testing.T) {
    // 启动测试服务器
    server := setupTestServer()
    defer server.Close()
    
    client := &http.Client{Timeout: 30 * time.Second}
    baseURL := server.URL + "/api/v1/{biz_module}"
    
    t.Run("完整CRUD流程测试", func(t *testing.T) {
        // 1. 创建{entity1}
        createReq := map[string]interface{}{
            "{field1}": "integration_test_{entity1}",
            "{field2}": "test_description",
            "status":   1,
        }
        
        createResp := makeAPIRequest(t, client, "POST", baseURL+"/{entity1}", createReq)
        assert.Equal(t, 0, int(createResp["code"].(float64)), "创建失败")
        
        entityData := createResp["data"].(map[string]interface{})
        entityID := int64(entityData["id"].(float64))
        
        // 2. 获取{entity1}详情
        getResp := makeAPIRequest(t, client, "GET", fmt.Sprintf("%s/{entity1}/%d", baseURL, entityID), nil)
        assert.Equal(t, 0, int(getResp["code"].(float64)), "获取详情失败")
        
        getData := getResp["data"].(map[string]interface{})
        assert.Equal(t, entityData["id"], getData["id"], "ID不匹配")
        assert.Equal(t, createReq["{field1}"], getData["{field1}"], "{field1}不匹配")
        
        // 3. 更新{entity1}
        updateReq := map[string]interface{}{
            "{field1}": "updated_integration_test_{entity1}",
            "status":   2,
        }
        
        updateResp := makeAPIRequest(t, client, "PUT", fmt.Sprintf("%s/{entity1}/%d", baseURL, entityID), updateReq)
        assert.Equal(t, 0, int(updateResp["code"].(float64)), "更新失败")
        
        // 4. 验证更新结果
        getUpdatedResp := makeAPIRequest(t, client, "GET", fmt.Sprintf("%s/{entity1}/%d", baseURL, entityID), nil)
        updatedData := getUpdatedResp["data"].(map[string]interface{})
        assert.Equal(t, updateReq["{field1}"], updatedData["{field1}"], "更新后{field1}不匹配")
        assert.Equal(t, float64(2), updatedData["status"], "更新后状态不匹配")
        
        // 5. 获取{entity1}列表
        listResp := makeAPIRequest(t, client, "GET", baseURL+"/{entity1}?page=1&pageSize=10", nil)
        assert.Equal(t, 0, int(listResp["code"].(float64)), "获取列表失败")
        
        listData := listResp["data"].(map[string]interface{})
        list := listData["list"].([]interface{})
        assert.True(t, len(list) > 0, "列表为空")
        
        // 6. 删除{entity1}
        deleteResp := makeAPIRequest(t, client, "DELETE", fmt.Sprintf("%s/{entity1}/%d", baseURL, entityID), nil)
        assert.Equal(t, 0, int(deleteResp["code"].(float64)), "删除失败")
        
        // 7. 验证删除结果
        getDeletedResp := makeAPIRequest(t, client, "GET", fmt.Sprintf("%s/{entity1}/%d", baseURL, entityID), nil)
        assert.Equal(t, 40404, int(getDeletedResp["code"].(float64)), "删除后仍能获取到数据")
    })
    
    t.Run("业务流程测试", func(t *testing.T) {
        // 创建测试数据
        entity := createTestEntity(t, client, baseURL)
        
        // 测试状态变更
        statusReq := map[string]interface{}{"status": 2}
        statusResp := makeAPIRequest(t, client, "PATCH", 
            fmt.Sprintf("%s/{entity1}/%d/status", baseURL, entity["id"]), statusReq)
        assert.Equal(t, 0, int(statusResp["code"].(float64)), "状态变更失败")
        
        // 测试业务操作
        businessReq := map[string]interface{}{
            "param1": "test_value",
            "param2": 123,
        }
        businessResp := makeAPIRequest(t, client, "POST", 
            fmt.Sprintf("%s/{entity1}/%d/{business_action1}", baseURL, entity["id"]), businessReq)
        assert.Equal(t, 0, int(businessResp["code"].(float64)), "业务操作失败")
        
        // 清理测试数据
        cleanupTestEntity(t, client, baseURL, entity["id"].(float64))
    })
}

// makeAPIRequest 发送API请求
func makeAPIRequest(t *testing.T, client *http.Client, method, url string, body interface{}) map[string]interface{} {
    var reqBody io.Reader
    if body != nil {
        jsonBody, _ := json.Marshal(body)
        reqBody = bytes.NewBuffer(jsonBody)
    }
    
    req, err := http.NewRequest(method, url, reqBody)
    assert.NoError(t, err, "创建请求失败")
    
    if body != nil {
        req.Header.Set("Content-Type", "application/json")
    }
    
    // 添加认证头（如果需要）
    req.Header.Set("Authorization", "Bearer "+getTestToken())
    
    resp, err := client.Do(req)
    assert.NoError(t, err, "发送请求失败")
    defer resp.Body.Close()
    
    var result map[string]interface{}
    err = json.NewDecoder(resp.Body).Decode(&result)
    assert.NoError(t, err, "解析响应失败")
    
    return result
}
```

### 2.3 缓存集成测试
- **文件位置**: `test/integration/cache_test.go`
```go
// TestCacheIntegration 缓存集成测试
func TestCacheIntegration(t *testing.T) {
    // 设置测试缓存
    cache := setupTestCache()
    defer cleanupTestCache(cache)
    
    t.Run("基础缓存操作", func(t *testing.T) {
        key := "test_key"
        value := "test_value"
        
        // 设置缓存
        err := cache.Set(context.Background(), key, value, time.Minute)
        assert.NoError(t, err, "设置缓存失败")
        
        // 获取缓存
        result, err := cache.Get(context.Background(), key)
        assert.NoError(t, err, "获取缓存失败")
        assert.Equal(t, value, result, "缓存值不匹配")
        
        // 删除缓存
        err = cache.Delete(context.Background(), key)
        assert.NoError(t, err, "删除缓存失败")
        
        // 验证删除结果
        _, err = cache.Get(context.Background(), key)
        assert.Error(t, err, "删除后仍能获取到缓存")
    })
    
    t.Run("缓存过期测试", func(t *testing.T) {
        key := "expire_test_key"
        value := "expire_test_value"
        
        // 设置短期缓存
        err := cache.Set(context.Background(), key, value, 100*time.Millisecond)
        assert.NoError(t, err, "设置缓存失败")
        
        // 立即获取
        result, err := cache.Get(context.Background(), key)
        assert.NoError(t, err, "获取缓存失败")
        assert.Equal(t, value, result, "缓存值不匹配")
        
        // 等待过期
        time.Sleep(200 * time.Millisecond)
        
        // 验证过期
        _, err = cache.Get(context.Background(), key)
        assert.Error(t, err, "缓存未过期")
    })
}
```

### 2.4 认证集成测试
- **文件位置**: `test/integration/auth_test.go`
```go
// TestAuthIntegration 认证集成测试
func TestAuthIntegration(t *testing.T) {
    server := setupTestServer()
    defer server.Close()
    
    client := &http.Client{Timeout: 30 * time.Second}
    baseURL := server.URL
    
    t.Run("登录流程测试", func(t *testing.T) {
        // 1. 用户登录
        loginReq := map[string]interface{}{
            "username": "test_user",
            "password": "test_password",
        }
        
        loginResp := makeAPIRequest(t, client, "POST", baseURL+"/auth/login", loginReq)
        assert.Equal(t, 0, int(loginResp["code"].(float64)), "登录失败")
        
        data := loginResp["data"].(map[string]interface{})
        token := data["token"].(string)
        assert.NotEmpty(t, token, "Token为空")
        
        // 2. 使用Token访问受保护资源
        req, _ := http.NewRequest("GET", baseURL+"/api/v1/{biz_module}/{entity1}", nil)
        req.Header.Set("Authorization", "Bearer "+token)
        
        resp, err := client.Do(req)
        assert.NoError(t, err, "请求失败")
        assert.Equal(t, http.StatusOK, resp.StatusCode, "访问受保护资源失败")
        
        // 3. 刷新Token
        refreshReq := map[string]interface{}{
            "refreshToken": data["refreshToken"].(string),
        }
        
        refreshResp := makeAPIRequest(t, client, "POST", baseURL+"/auth/refresh", refreshReq)
        assert.Equal(t, 0, int(refreshResp["code"].(float64)), "刷新Token失败")
        
        // 4. 用户登出
        logoutReq, _ := http.NewRequest("POST", baseURL+"/auth/logout", nil)
        logoutReq.Header.Set("Authorization", "Bearer "+token)
        
        logoutResp, err := client.Do(logoutReq)
        assert.NoError(t, err, "登出请求失败")
        assert.Equal(t, http.StatusOK, logoutResp.StatusCode, "登出失败")
    })
    
    t.Run("权限控制测试", func(t *testing.T) {
        // 普通用户Token
        userToken := getUserToken(t, client, baseURL, "user")
        
        // 管理员Token
        adminToken := getUserToken(t, client, baseURL, "admin")
        
        // 测试普通用户访问管理员接口
        req, _ := http.NewRequest("GET", baseURL+"/admin/users", nil)
        req.Header.Set("Authorization", "Bearer "+userToken)
        
        resp, err := client.Do(req)
        assert.NoError(t, err, "请求失败")
        assert.Equal(t, http.StatusForbidden, resp.StatusCode, "权限控制失败")
        
        // 测试管理员访问管理员接口
        req, _ = http.NewRequest("GET", baseURL+"/admin/users", nil)
        req.Header.Set("Authorization", "Bearer "+adminToken)
        
        resp, err = client.Do(req)
        assert.NoError(t, err, "请求失败")
        assert.Equal(t, http.StatusOK, resp.StatusCode, "管理员访问失败")
    })
}
```

## 3. 端到端测试汇总

### 3.1 用户流程测试
- **文件位置**: `test/e2e/user_journey_test.go`
```go
// TestUserJourney 用户流程端到端测试
func TestUserJourney(t *testing.T) {
    // 启动完整系统
    system := setupFullSystem()
    defer system.Cleanup()
    
    client := &http.Client{Timeout: 60 * time.Second}
    baseURL := system.GetBaseURL()
    
    t.Run("完整用户流程", func(t *testing.T) {
        // 1. 用户注册
        registerReq := map[string]interface{}{
            "username": "e2e_test_user",
            "email":    "<EMAIL>",
            "password": "test_password123",
        }
        
        registerResp := makeAPIRequest(t, client, "POST", baseURL+"/auth/register", registerReq)
        assert.Equal(t, 0, int(registerResp["code"].(float64)), "用户注册失败")
        
        // 2. 用户登录
        loginReq := map[string]interface{}{
            "username": "e2e_test_user",
            "password": "test_password123",
        }
        
        loginResp := makeAPIRequest(t, client, "POST", baseURL+"/auth/login", loginReq)
        assert.Equal(t, 0, int(loginResp["code"].(float64)), "用户登录失败")
        
        token := loginResp["data"].(map[string]interface{})["token"].(string)
        
        // 3. 创建业务数据
        createReq := map[string]interface{}{
            "{field1}": "e2e_test_data",
            "{field2}": "e2e_test_description",
            "status":   1,
        }
        
        createResp := makeAuthenticatedRequest(t, client, "POST", 
            baseURL+"/api/v1/{biz_module}/{entity1}", createReq, token)
        assert.Equal(t, 0, int(createResp["code"].(float64)), "创建业务数据失败")
        
        entityID := int64(createResp["data"].(map[string]interface{})["id"].(float64))
        
        // 4. 查看数据列表
        listResp := makeAuthenticatedRequest(t, client, "GET", 
            baseURL+"/api/v1/{biz_module}/{entity1}?page=1&pageSize=10", nil, token)
        assert.Equal(t, 0, int(listResp["code"].(float64)), "获取数据列表失败")
        
        // 5. 修改数据
        updateReq := map[string]interface{}{
            "{field1}": "e2e_updated_data",
            "status":   2,
        }
        
        updateResp := makeAuthenticatedRequest(t, client, "PUT", 
            fmt.Sprintf("%s/api/v1/{biz_module}/{entity1}/%d", baseURL, entityID), updateReq, token)
        assert.Equal(t, 0, int(updateResp["code"].(float64)), "修改数据失败")
        
        // 6. 执行业务操作
        businessReq := map[string]interface{}{
            "action": "approve",
            "reason": "e2e test approval",
        }
        
        businessResp := makeAuthenticatedRequest(t, client, "POST", 
            fmt.Sprintf("%s/api/v1/{biz_module}/{entity1}/%d/approve", baseURL, entityID), businessReq, token)
        assert.Equal(t, 0, int(businessResp["code"].(float64)), "业务操作失败")
        
        // 7. 导出数据
        exportResp := makeAuthenticatedRequest(t, client, "GET", 
            baseURL+"/api/v1/{biz_module}/{entity1}/export?format=csv", nil, token)
        assert.Equal(t, 0, int(exportResp["code"].(float64)), "导出数据失败")
        
        // 8. 删除数据
        deleteResp := makeAuthenticatedRequest(t, client, "DELETE", 
            fmt.Sprintf("%s/api/v1/{biz_module}/{entity1}/%d", baseURL, entityID), nil, token)
        assert.Equal(t, 0, int(deleteResp["code"].(float64)), "删除数据失败")
        
        // 9. 用户登出
        logoutResp := makeAuthenticatedRequest(t, client, "POST", 
            baseURL+"/auth/logout", nil, token)
        assert.Equal(t, 0, int(logoutResp["code"].(float64)), "用户登出失败")
    })
}
```

### 3.2 性能测试
- **文件位置**: `test/e2e/performance_test.go`
```go
// TestPerformance 性能测试
func TestPerformance(t *testing.T) {
    system := setupFullSystem()
    defer system.Cleanup()
    
    baseURL := system.GetBaseURL()
    
    t.Run("API响应时间测试", func(t *testing.T) {
        client := &http.Client{Timeout: 10 * time.Second}
        token := getTestToken()
        
        endpoints := []string{
            "/api/v1/{biz_module}/{entity1}",
            "/api/v1/{biz_module}/{entity2}",
            "/health",
            "/metrics",
        }
        
        for _, endpoint := range endpoints {
            t.Run(endpoint, func(t *testing.T) {
                var totalDuration time.Duration
                iterations := 100
                
                for i := 0; i < iterations; i++ {
                    start := time.Now()
                    
                    req, _ := http.NewRequest("GET", baseURL+endpoint, nil)
                    if strings.HasPrefix(endpoint, "/api/") {
                        req.Header.Set("Authorization", "Bearer "+token)
                    }
                    
                    resp, err := client.Do(req)
                    assert.NoError(t, err, "请求失败")
                    resp.Body.Close()
                    
                    duration := time.Since(start)
                    totalDuration += duration
                }
                
                avgDuration := totalDuration / time.Duration(iterations)
                t.Logf("接口 %s 平均响应时间: %v", endpoint, avgDuration)
                
                // 断言平均响应时间小于500ms
                assert.Less(t, avgDuration, 500*time.Millisecond, 
                    "接口响应时间超过500ms: %v", avgDuration)
            })
        }
    })
    
    t.Run("并发性能测试", func(t *testing.T) {
        concurrency := 50
        requests := 1000
        
        var wg sync.WaitGroup
        var successCount int64
        var errorCount int64
        
        start := time.Now()
        
        for i := 0; i < concurrency; i++ {
            wg.Add(1)
            go func() {
                defer wg.Done()
                
                client := &http.Client{Timeout: 30 * time.Second}
                token := getTestToken()
                
                for j := 0; j < requests/concurrency; j++ {
                    req, _ := http.NewRequest("GET", baseURL+"/api/v1/{biz_module}/{entity1}", nil)
                    req.Header.Set("Authorization", "Bearer "+token)
                    
                    resp, err := client.Do(req)
                    if err != nil {
                        atomic.AddInt64(&errorCount, 1)
                        continue
                    }
                    
                    if resp.StatusCode == http.StatusOK {
                        atomic.AddInt64(&successCount, 1)
                    } else {
                        atomic.AddInt64(&errorCount, 1)
                    }
                    
                    resp.Body.Close()
                }
            }()
        }
        
        wg.Wait()
        duration := time.Since(start)
        
        qps := float64(requests) / duration.Seconds()
        successRate := float64(successCount) / float64(requests) * 100
        
        t.Logf("并发测试结果:")
        t.Logf("  总请求数: %d", requests)
        t.Logf("  成功请求数: %d", successCount)
        t.Logf("  失败请求数: %d", errorCount)
        t.Logf("  成功率: %.2f%%", successRate)
        t.Logf("  QPS: %.2f", qps)
        t.Logf("  总耗时: %v", duration)
        
        // 断言成功率大于95%
        assert.Greater(t, successRate, 95.0, "成功率低于95%")
        
        // 断言QPS大于100
        assert.Greater(t, qps, 100.0, "QPS低于100")
    })
}
```

### 3.3 负载测试
- **文件位置**: `test/e2e/load_test.go`
```go
// TestLoad 负载测试
func TestLoad(t *testing.T) {
    if testing.Short() {
        t.Skip("跳过负载测试")
    }
    
    system := setupFullSystem()
    defer system.Cleanup()
    
    baseURL := system.GetBaseURL()
    
    t.Run("持续负载测试", func(t *testing.T) {
        duration := 5 * time.Minute
        concurrency := 100
        
        var wg sync.WaitGroup
        var requestCount int64
        var errorCount int64
        
        ctx, cancel := context.WithTimeout(context.Background(), duration)
        defer cancel()
        
        start := time.Now()
        
        for i := 0; i < concurrency; i++ {
            wg.Add(1)
            go func() {
                defer wg.Done()
                
                client := &http.Client{Timeout: 30 * time.Second}
                token := getTestToken()
                
                for {
                    select {
                    case <-ctx.Done():
                        return
                    default:
                        req, _ := http.NewRequest("GET", baseURL+"/api/v1/{biz_module}/{entity1}", nil)
                        req.Header.Set("Authorization", "Bearer "+token)
                        
                        resp, err := client.Do(req)
                        atomic.AddInt64(&requestCount, 1)
                        
                        if err != nil || resp.StatusCode != http.StatusOK {
                            atomic.AddInt64(&errorCount, 1)
                        }
                        
                        if resp != nil {
                            resp.Body.Close()
                        }
                        
                        time.Sleep(10 * time.Millisecond) // 控制请求频率
                    }
                }
            }()
        }
        
        wg.Wait()
        actualDuration := time.Since(start)
        
        avgQPS := float64(requestCount) / actualDuration.Seconds()
        errorRate := float64(errorCount) / float64(requestCount) * 100
        
        t.Logf("负载测试结果:")
        t.Logf("  测试时长: %v", actualDuration)
        t.Logf("  总请求数: %d", requestCount)
        t.Logf("  错误请求数: %d", errorCount)
        t.Logf("  错误率: %.2f%%", errorRate)
        t.Logf("  平均QPS: %.2f", avgQPS)
        
        // 断言错误率小于5%
        assert.Less(t, errorRate, 5.0, "错误率超过5%")
        
        // 断言平均QPS大于50
        assert.Greater(t, avgQPS, 50.0, "平均QPS低于50")
    })
}
```

## 4. 部署配置汇总

### 4.1 Docker配置
- **文件位置**: `deployments/docker/Dockerfile`
```dockerfile
# 多阶段构建
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY go.mod go.sum ./
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

# 运行阶段
FROM alpine:latest

# 安装必要的包
RUN apk --no-cache add ca-certificates tzdata

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
RUN addgroup -g 1001 appgroup && \
    adduser -D -s /bin/sh -u 1001 -G appgroup appuser

# 设置工作目录
WORKDIR /app

# 复制构建产物
COPY --from=builder /app/main .
COPY --from=builder /app/configs ./configs
COPY --from=builder /app/web ./web

# 更改文件所有者
RUN chown -R appuser:appgroup /app

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:8080/health || exit 1

# 启动应用
CMD ["./main"]
```

### 4.2 Docker Compose配置
- **文件位置**: `deployments/docker/docker-compose.yml`
```yaml
version: '3.8'

services:
  app:
    build:
      context: ../..
      dockerfile: deployments/docker/Dockerfile
    ports:
      - "8080:8080"
    environment:
      - APP_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - app-network
    volumes:
      - ./logs:/app/logs
      - ./storage:/app/storage

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - app-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    restart: unless-stopped
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - app-network

volumes:
  postgres_data:
  redis_data:

networks:
  app-network:
    driver: bridge
```

### 4.3 Kubernetes配置
- **文件位置**: `deployments/k8s/deployment.yaml`
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {project_name}-{biz_module}
  labels:
    app: {project_name}-{biz_module}
    version: v1.0.0
spec:
  replicas: 3
  selector:
    matchLabels:
      app: {project_name}-{biz_module}
  template:
    metadata:
      labels:
        app: {project_name}-{biz_module}
        version: v1.0.0
    spec:
      containers:
      - name: app
        image: {registry}/{project_name}-{biz_module}:latest
        ports:
        - containerPort: 8080
        env:
        - name: APP_ENV
          value: "production"
        - name: DB_HOST
          valueFrom:
            configMapKeyRef:
              name: {project_name}-config
              key: db.host
        - name: DB_PORT
          valueFrom:
            configMapKeyRef:
              name: {project_name}-config
              key: db.port
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: {project_name}-secret
              key: db.name
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: {project_name}-secret
              key: db.user
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {project_name}-secret
              key: db.password
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: {project_name}-config
              key: redis.host
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: {project_name}-config
              key: redis.port
        resources:
          requests:
            memory: "256Mi"
            cpu: "250m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: config-volume
          mountPath: /app/configs
        - name: storage-volume
          mountPath: /app/storage
      volumes:
      - name: config-volume
        configMap:
          name: {project_name}-config
      - name: storage-volume
        persistentVolumeClaim:
          claimName: {project_name}-storage-pvc
      imagePullSecrets:
      - name: registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: {project_name}-{biz_module}-service
  labels:
    app: {project_name}-{biz_module}
spec:
  selector:
    app: {project_name}-{biz_module}
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {project_name}-{biz_module}-ingress
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - api.{domain}
    secretName: {project_name}-tls
  rules:
  - host: api.{domain}
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: {project_name}-{biz_module}-service
            port:
              number: 80
```

## 5. CI/CD配置汇总

### 5.1 GitHub Actions CI配置
- **文件位置**: `.github/workflows/ci.yml`
```yaml
name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Run tests
      run: |
        go test -v -race -coverprofile=coverage.out ./...
      env:
        DB_HOST: localhost
        DB_PORT: 5432
        DB_NAME: test_db
        DB_USER: postgres
        DB_PASSWORD: postgres
        REDIS_HOST: localhost
        REDIS_PORT: 6379

    - name: Generate coverage report
      run: go tool cover -html=coverage.out -o coverage.html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.out

    - name: Run linter
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest

    - name: Run security scan
      uses: securecodewarrior/github-action-gosec@master
      with:
        args: './...'

  build:
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: '1.21'

    - name: Build application
      run: |
        CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main .

    - name: Build Docker image
      run: |
        docker build -t ${{ github.repository }}:${{ github.sha }} -f deployments/docker/Dockerfile .

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: '${{ github.repository }}:${{ github.sha }}'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'
```

### 5.2 GitHub Actions CD配置
- **文件位置**: `.github/workflows/cd.yml`
```yaml
name: CD

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Login to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ secrets.REGISTRY_URL }}
        username: ${{ secrets.REGISTRY_USERNAME }}
        password: ${{ secrets.REGISTRY_PASSWORD }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ secrets.REGISTRY_URL }}/{project_name}-{biz_module}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=semver,pattern={{version}}
          type=semver,pattern={{major}}.{{minor}}
          type=sha

    - name: Build and push Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: deployments/docker/Dockerfile
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Deploy to staging
      if: github.ref == 'refs/heads/main'
      run: |
        echo "Deploying to staging environment"
        # 部署到预发布环境的脚本
        ./scripts/deploy.sh staging

    - name: Deploy to production
      if: startsWith(github.ref, 'refs/tags/v')
      run: |
        echo "Deploying to production environment"
        # 部署到生产环境的脚本
        ./scripts/deploy.sh production
      env:
        KUBE_CONFIG: ${{ secrets.KUBE_CONFIG }}
        REGISTRY_URL: ${{ secrets.REGISTRY_URL }}
        IMAGE_TAG: ${{ steps.meta.outputs.tags }}

    - name: Run smoke tests
      run: |
        echo "Running smoke tests"
        ./scripts/smoke-test.sh
```

### 5.3 部署脚本
- **文件位置**: `scripts/deploy.sh`
```bash
#!/bin/bash

set -e

ENVIRONMENT=$1
if [ -z "$ENVIRONMENT" ]; then
    echo "Usage: $0 <environment>"
    echo "Environment: staging, production"
    exit 1
fi

echo "Deploying to $ENVIRONMENT environment..."

# 设置环境变量
case $ENVIRONMENT in
    staging)
        NAMESPACE="staging"
        REPLICAS=2
        ;;
    production)
        NAMESPACE="production"
        REPLICAS=3
        ;;
    *)
        echo "Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

# 检查必要的环境变量
if [ -z "$KUBE_CONFIG" ]; then
    echo "KUBE_CONFIG environment variable is required"
    exit 1
fi

if [ -z "$REGISTRY_URL" ]; then
    echo "REGISTRY_URL environment variable is required"
    exit 1
fi

if [ -z "$IMAGE_TAG" ]; then
    echo "IMAGE_TAG environment variable is required"
    exit 1
fi

# 设置kubectl配置
echo "$KUBE_CONFIG" | base64 -d > /tmp/kubeconfig
export KUBECONFIG=/tmp/kubeconfig

# 创建命名空间（如果不存在）
kubectl create namespace $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

# 更新部署配置
sed -i "s|{registry}/{project_name}-{biz_module}:latest|$REGISTRY_URL/{project_name}-{biz_module}:$IMAGE_TAG|g" deployments/k8s/deployment.yaml
sed -i "s|replicas: 3|replicas: $REPLICAS|g" deployments/k8s/deployment.yaml

# 应用Kubernetes配置
kubectl apply -f deployments/k8s/ -n $NAMESPACE

# 等待部署完成
kubectl rollout status deployment/{project_name}-{biz_module} -n $NAMESPACE --timeout=300s

# 验证部署
kubectl get pods -n $NAMESPACE -l app={project_name}-{biz_module}

echo "Deployment to $ENVIRONMENT completed successfully!"

# 清理临时文件
rm -f /tmp/kubeconfig
```

## 6. 监控配置汇总

### 6.1 Prometheus配置
- **文件位置**: `deployments/monitoring/prometheus.yml`
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: '{project_name}-{biz_module}'
    static_configs:
      - targets: ['{project_name}-{biz_module}-service:8080']
    metrics_path: /metrics
    scrape_interval: 10s
    scrape_timeout: 5s

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
```

### 6.2 Grafana仪表板
- **文件位置**: `deployments/monitoring/grafana-dashboard.json`
```json
{
  "dashboard": {
    "id": null,
    "title": "{project_name} {biz_module} Dashboard",
    "tags": ["{project_name}", "{biz_module}"],
    "timezone": "browser",
    "panels": [
      {
        "id": 1,
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{job=\"{project_name}-{biz_module}\"}[5m])",
            "legendFormat": "{{method}} {{path}}"
          }
        ],
        "yAxes": [
          {
            "label": "Requests/sec"
          }
        ]
      },
      {
        "id": 2,
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"{project_name}-{biz_module}\"}[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"{project_name}-{biz_module}\"}[5m]))",
            "legendFormat": "50th percentile"
          }
        ],
        "yAxes": [
          {
            "label": "Seconds"
          }
        ]
      },
      {
        "id": 3,
        "title": "Error Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total{job=\"{project_name}-{biz_module}\",status=~\"4..|5..\"}[5m]) / rate(http_requests_total{job=\"{project_name}-{biz_module}\"}[5m])",
            "legendFormat": "Error Rate"
          }
        ],
        "yAxes": [
          {
            "label": "Percentage",
            "max": 1,
            "min": 0
          }
        ]
      },
      {
        "id": 4,
        "title": "Database Connections",
        "type": "graph",
        "targets": [
          {
            "expr": "pg_stat_database_numbackends{datname=\"{db_name}\"}",
            "legendFormat": "Active Connections"
          }
        ]
      }
    ],
    "time": {
      "from": "now-1h",
      "to": "now"
    },
    "refresh": "5s"
  }
}
```

### 6.3 告警规则
- **文件位置**: `deployments/monitoring/alert_rules.yml`
```yaml
groups:
- name: {project_name}-{biz_module}
  rules:
  - alert: HighErrorRate
    expr: rate(http_requests_total{job="{project_name}-{biz_module}",status=~"5.."}[5m]) > 0.1
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "High error rate detected"
      description: "Error rate is {{ $value }} errors per second"

  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job="{project_name}-{biz_module}"}[5m])) > 1
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High response time detected"
      description: "95th percentile response time is {{ $value }} seconds"

  - alert: DatabaseConnectionHigh
    expr: pg_stat_database_numbackends{datname="{db_name}"} > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "High database connection count"
      description: "Database has {{ $value }} active connections"

  - alert: ServiceDown
    expr: up{job="{project_name}-{biz_module}"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "Service is down"
      description: "{project_name}-{biz_module} service is not responding"
```

## 7. 测试结果汇总

### 7.1 单元测试结果
- **总测试用例数**: {total_unit_tests}
- **通过用例数**: {passed_unit_tests}
- **失败用例数**: {failed_unit_tests}
- **代码覆盖率**: {unit_test_coverage}%
- **执行时间**: {unit_test_duration}s

### 7.2 集成测试结果
- **总测试用例数**: {total_integration_tests}
- **通过用例数**: {passed_integration_tests}
- **失败用例数**: {failed_integration_tests}
- **测试覆盖率**: {integration_test_coverage}%
- **执行时间**: {integration_test_duration}s

### 7.3 端到端测试结果
- **总测试用例数**: {total_e2e_tests}
- **通过用例数**: {passed_e2e_tests}
- **失败用例数**: {failed_e2e_tests}
- **用户流程覆盖率**: {e2e_test_coverage}%
- **执行时间**: {e2e_test_duration}s

### 7.4 性能测试结果
- **平均响应时间**: {avg_response_time}ms
- **95%响应时间**: {p95_response_time}ms
- **99%响应时间**: {p99_response_time}ms
- **最大QPS**: {max_qps}
- **并发用户数**: {max_concurrent_users}
- **错误率**: {error_rate}%

## 8. 部署验证汇总

### 8.1 健康检查验证
- ✅ 应用启动成功
- ✅ 数据库连接正常
- ✅ Redis连接正常
- ✅ 外部服务连接正常
- ✅ 健康检查接口响应正常

### 8.2 功能验证
- ✅ 用户认证功能正常
- ✅ API接口响应正常
- ✅ 数据库操作正常
- ✅ 缓存功能正常
- ✅ 文件上传下载正常

### 8.3 性能验证
- ✅ 响应时间符合要求
- ✅ 并发处理能力达标
- ✅ 资源使用率正常
- ✅ 内存使用稳定
- ✅ CPU使用率正常

### 8.4 安全验证
- ✅ 认证机制工作正常
- ✅ 权限控制有效
- ✅ 输入验证正确
- ✅ SQL注入防护有效
- ✅ XSS防护有效

## 9. 监控指标汇总

### 9.1 应用指标
- **请求总数**: {total_requests}
- **平均QPS**: {avg_qps}
- **错误请求数**: {error_requests}
- **平均响应时间**: {avg_response_time}ms
- **内存使用**: {memory_usage}MB
- **CPU使用率**: {cpu_usage}%

### 9.2 数据库指标
- **连接数**: {db_connections}
- **查询响应时间**: {db_response_time}ms
- **慢查询数**: {slow_queries}
- **锁等待时间**: {lock_wait_time}ms
- **缓存命中率**: {db_cache_hit_rate}%

### 9.3 缓存指标
- **命中率**: {cache_hit_rate}%
- **内存使用**: {cache_memory_usage}MB
- **连接数**: {cache_connections}
- **操作响应时间**: {cache_response_time}ms
- **过期键数**: {expired_keys}

## 10. 文档汇总

### 10.1 技术文档
- ✅ API文档完整
- ✅ 数据库设计文档
- ✅ 架构设计文档
- ✅ 部署文档
- ✅ 运维文档

### 10.2 用户文档
- ✅ 用户手册
- ✅ 快速开始指南
- ✅ 常见问题解答
- ✅ 故障排除指南
- ✅ 更新日志

### 10.3 开发文档
- ✅ 开发环境搭建
- ✅ 代码规范
- ✅ 测试指南
- ✅ 贡献指南
- ✅ 版本发布流程

## 11. 上线检查清单

### 11.1 代码质量
- ✅ 代码评审完成
- ✅ 单元测试通过
- ✅ 集成测试通过
- ✅ 代码覆盖率达标
- ✅ 静态代码分析通过
- ✅ 安全扫描通过

### 11.2 部署准备
- ✅ 生产环境配置就绪
- ✅ 数据库迁移脚本准备
- ✅ 监控告警配置完成
- ✅ 日志收集配置完成
- ✅ 备份策略制定
- ✅ 回滚方案准备

### 11.3 运维准备
- ✅ 运维文档完整
- ✅ 监控仪表板配置
- ✅ 告警规则配置
- ✅ 值班人员安排
- ✅ 应急响应流程
- ✅ 性能基线建立

## 12. 验收确认

- ✅ 所有集成测试通过
- ✅ 端到端测试通过
- ✅ 性能测试达标
- ✅ 安全测试通过
- ✅ 部署配置完成
- ✅ CI/CD流水线正常
- ✅ 监控告警配置完成
- ✅ 文档完整齐全
- ✅ 上线检查清单完成
- ✅ 生产环境验证通过
- ✅ 用户验收测试通过
- ✅ 性能基线建立
- ✅ 运维交接完成

**Phase 7 集成测试和部署完成，整个项目开发流程结束，系统已成功上线并稳定运行。**

## 13. 后续维护建议

### 13.1 监控维护
- 定期检查监控指标
- 优化告警规则
- 更新性能基线
- 监控容量规划

### 13.2 代码维护
- 定期更新依赖包
- 修复安全漏洞
- 优化性能瓶颈
- 重构技术债务

### 13.3 文档维护
- 更新API文档
- 维护运维文档
- 记录变更日志
- 更新故障处理手册

**项目已成功交付，进入稳定运维阶段。**
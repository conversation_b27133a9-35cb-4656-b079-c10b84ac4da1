# 教师端作业报告服务架构设计

## 1. 引言

### 1.1 文档目的

为"教师端作业报告服务"提供清晰的 Golang 服务端架构蓝图，指导核心模块的设计与开发，确保系统满足关键的质量属性并支持在 Kubernetes 环境中高效运行和扩展。

### 1.2 系统概述

本系统是教师端作业报告平台，其核心目标是为教师提供全面的作业监控功能，使教师能够有效跟踪学生的学习情况，包括作业完成情况、答题正确率、用时等关键数据。系统支持教师全方位了解班级整体表现和个别学生情况，尤其关注需要鼓励和特别关注的学生，以便教师及时干预学生的学习过程。主要交互方包括教师客户端、任务管理服务、用户中心、运营管理平台和内容平台。

### 1.3 设计目标与核心原则

#### 设计目标
- **高可用性**: 保证 99.95% 的系统可用性，确保教师在课堂和课后能稳定访问作业报告
- **可伸缩性**: 支持 2 年内用户量增长 5 倍，峰值 QPS 达 5k
- **高性能**: 核心报告查询接口 P99 延迟 < 200ms
- **可维护性**: 新团队成员 1 周内可理解架构并贡献代码
- **可测试性**: 核心模块单元测试覆盖率 > 85%
- **数据准确性**: 报告统计数据与原始数据一致性达 100%

#### 核心原则
- **单一职责**: 每个模块、包、结构体有明确且唯一的职责
- **关注点分离**: 将系统分为不同层次，每层专注于特定关注点
- **接口隔离**: 定义精确的接口以隔离实现细节
- **依赖倒置**: 高层模块不依赖低层模块，依赖于抽象接口
- **高内聚低耦合**: 相关功能集中在一起，减少模块间依赖
- **KISS 原则**: 保持设计简单明了，避免过度设计
- **YAGNI 原则**: 只实现当前必需的功能，不过度预测未来需求

### 1.4 范围

#### 覆盖范围
- 作业报告服务的核心功能模块设计
- 与上游服务（任务管理服务、用户中心等）的集成接口
- 数据处理与统计分析逻辑
- 服务间通信机制
- 缓存策略

#### 不覆盖范围
- UI/前端具体实现
- 详细的数据库表结构设计
- 具体的第三方服务集成协议细节
- DevOps 的 CI/CD 流程细节
- 具体的 Kubernetes YAML 配置

### 1.5 术语与缩写

- **作业报告**: 呈现给教师的作业任务执行情况的统计分析结果
- **首答**: 学生在任务中首次回答某题的答案，用于计算统计指标
- **小空**: 题目中的填空、选择等计分点
- **共性错题**: 多数学生在作业中出错的题目
- **QPS**: 每秒查询次数，衡量系统处理能力的指标
- **API**: 应用程序接口，用于系统间通信
- **SLA**: 服务水平协议，定义服务可用性和性能指标
- **K8s**: Kubernetes 的简称，用于容器编排和管理

### 1.6 参考资料

- [documents/ai-docs/ai-prd-教师端_1期_作业报告_analyzed-Gemini-2.5-Pro.md](教师端作业报告产品需求文档)
- [documents/service/ucenter.md](用户中心服务说明)
- [documents/service/admin.md](运营管理平台服务说明)
- [documents/service/question.md](内容平台服务说明)
- [backend/global_rules/rules-tech-stack.mdc](技术栈约束说明)

## 2. 需求提炼与架构驱动因素

### 2.1 核心业务能力

从产品需求文档中，我们提炼出系统必须具备的核心能力：

1. **作业列表管理**
   - 获取教师有权访问的作业任务列表
   - 支持多条件筛选（类型、班级、学科、日期）和关键词搜索
   - 展示作业完成率、正确率等关键指标

2. **作业报告统计分析**
   - 生成班级整体统计（进度、正确率等）
   - 识别需要鼓励和关注的学生
   - 分析题目答题情况，发现共性错题
   - 汇总学生提问

3. **数据计算引擎**
   - 计算题目正确率、答错人数、作答人数
   - 实现"建议鼓励"和"建议关注"的规则逻辑
   - 执行数据异常标记（偏离均值的数据飘色）

4. **单学生报告生成**
   - 针对特定学生生成详细作业报告
   - 展示该生的作业完成情况和答题表现

5. **数据筛选与过滤**
   - 支持按班级、素材范围等维度筛选数据
   - 题目关键词搜索功能

### 2.2 关键质量属性

#### 2.2.1 性能

- **预期负载**：
  - 峰值 QPS：作业报告详情查询 1000/s，列表查询 3000/s
  - 并发用户：单校峰值 500 名教师同时查看报告
  
- **响应时间要求**：
  - 作业列表接口：P95 < 100ms，P99 < 200ms
  - 报告详情接口：P95 < 150ms，P99 < 300ms
  - 统计计算接口：P95 < 300ms，P99 < 500ms

- **Golang 实现考量**：
  - 利用 goroutine 并行处理多维度数据计算
  - 为计算密集型任务设计合理的 goroutine 池
  - 针对复杂计算结果实现多级缓存策略

#### 2.2.2 可用性

- **目标可用性**：99.95%（每月允许约 20 分钟非计划内停机时间）
- **支持策略**：
  - 无状态服务设计，支持多实例部署
  - 实现健康检查端点，与 k8s 协作确保故障快速恢复
  - 关键数据库操作添加超时控制和重试机制
  - 依赖服务故障时提供降级服务（如缓存数据展示）

#### 2.2.3 可伸缩性

- **水平扩展能力**：
  - 设计无状态服务，避免本地状态依赖
  - 支持按需自动扩缩容（配合 k8s HPA）
  
- **数据规模预期**：
  - 支持单次作业最多 10,000 名学生
  - 单个班级最多 100 名学生
  - 单次作业最多包含 200 道题目

#### 2.2.4 可维护性

- **代码组织规范**：
  - 采用清晰的分层架构，每层职责明确
  - 使用统一的命名约定和错误处理模式
  - 关键逻辑添加详细注释和文档

- **模块化程度**：
  - 将核心计算规则和策略抽象为独立模块
  - 数据访问、业务逻辑、API处理分离设计

#### 2.2.5 可测试性

- **测试策略**：
  - 使用依赖注入模式便于单元测试中模拟依赖
  - 为复杂计算逻辑编写完整的单元测试
  - 采用表格驱动测试方法验证各种边界条件

- **测试覆盖目标**：
  - 核心计算逻辑单元测试覆盖率 > 90%
  - 整体代码覆盖率 > 85%

#### 2.2.6 安全性

- **数据安全**：
  - 实现教师权限验证，确保只能访问有权限的班级和学生数据
  - 学生个人数据脱敏处理

- **接口安全**：
  - 所有API调用需进行用户认证和授权
  - 敏感操作添加访问频率限制

#### 2.2.7 可观测性

- **日志**：
  - 采用结构化日志（JSON格式）
  - 记录关键操作和查询请求，包含TraceID
  
- **监控**：
  - 暴露 Prometheus 兼容的 /metrics 端点
  - 监控关键接口的响应时间、错误率
  - 监控关键计算逻辑的执行时间

### 2.3 主要约束与依赖

#### 技术栈约束

- **后端框架**: Kratos 2.8.3
- **消息队列**: Kafka 3.6, 3
- **缓存**: Redis 6.0.3
- **数据库**: PostgreSQL17, ClickHouse **********
- **搜索引擎**: Elasticsearch 7.16.2
- **日志服务**: 项目内置日志服务 (`app/core/loger/logger_new`)
- **链路追踪**: Zipkin 3.4.3

#### 外部系统依赖

1. **任务管理服务**：
   - 提供作业的创建、提交、批改、统计、查询功能
   - 我们需要从该服务获取原始作业数据和答题记录

2. **用户中心**：
   - 提供用户登录认证、用户信息管理、用户token验证
   - 我们需要验证教师身份和权限信息

3. **运营管理平台**：
   - 提供学校、教职员工、课程、班级、学生、成绩等信息
   - 我们需要获取班级和学生基础信息

4. **内容平台**：
   - 提供题目信息、题库信息、题目分类信息
   - 我们需要获取题目内容、答案等信息 

## 3. 宏观架构设计

### 3.1 架构风格与模式

对于教师端作业报告系统，我们采用以下架构风格组合：

1. **分层架构**：将系统按照关注点分离为多个层次，如接口层、应用服务层、领域层和基础设施层。这种架构风格适合具有清晰责任划分的业务系统，能够提高代码的可维护性和可测试性。

2. **微服务架构**：作业报告服务作为独立的微服务，与任务管理服务、用户中心等其他服务通过明确定义的API进行通信。这种架构支持服务的独立部署和扩展，符合系统的可伸缩性需求。

3. **CQRS模式（读写分离）**：鉴于系统主要以查询为主，我们对查询操作（作业报告读取）和命令操作（数据更新）分开处理，优化查询性能。这种模式特别适合报告类应用，可以针对复杂的统计查询进行专门优化。

4. **事件驱动架构**：使用事件通知机制处理作业状态变更、学生提交答案等情况，确保作业报告数据的及时更新。

选择这种组合架构的理由：

- **性能考量**：CQRS模式和缓存策略可以有效支持高频的报告查询请求，达到P99 < 300ms的性能目标。
- **可扩展性**：微服务架构支持独立扩展作业报告服务，应对用户量增长。
- **数据一致性**：事件驱动架构确保作业数据变更能及时反映在报告中。
- **开发效率**：分层架构明确了各层职责，提高了代码的可维护性和团队协作效率。

### 3.2 系统分层视图

#### 3.2.1 推荐的逻辑分层模型

基于对教师端作业报告系统的需求分析，我们设计了一个适合该业务特性的四层架构模型。考虑到系统主要以数据统计分析和报表生成为核心，同时要处理复杂的计算规则和数据聚合，这种分层能够有效地隔离关注点，使系统更易于维护和扩展。

```mermaid
flowchart TD
    subgraph '教师端作业报告服务'
        API[接口层] --> AppService[应用服务层]
        AppService --> DomainService[领域服务层]
        AppService --> Repository[仓储层]
        DomainService --> Repository
        Repository --> Infrastructure[基础设施层]
        
        %% 接口层组件
        subgraph API
            API1[作业列表API]
            API2[作业报告API]
            API3[学生报告API]
            API4[题目统计API]
            API5[学生提问API]
        end
        
        %% 应用服务层组件
        subgraph AppService
            App1[作业列表服务]
            App2[报告聚合服务]
            App3[学生报告服务]
            App4[答题分析服务]
            App5[提问管理服务]
        end
        
        %% 领域服务层组件
        subgraph DomainService
            Domain1[数据计算引擎]
            Domain2[策略规则服务]
            Domain3[异常检测服务]
            Domain4[报表生成服务]
        end
        
        %% 仓储层组件
        subgraph Repository
            Repo1[作业仓储]
            Repo2[学生仓储]
            Repo3[题目仓储]
            Repo4[提问仓储]
            Repo5[报告缓存仓储]
        end
        
        %% 基础设施层组件
        subgraph Infrastructure
            Infra1[数据库适配器]
            Infra2[缓存适配器]
            Infra3[外部服务适配器]
            Infra4[消息队列适配器]
        end
    end
    
    %% 外部依赖服务
    ExternalDB[数据库] <-- PostgreSQL/ClickHouse --> Infra1
    Cache[缓存] <-- Redis --> Infra2
    TaskService[任务管理服务] <-- gRPC/Kafka --> Infra3
    UserCenter[用户中心] <-- gRPC --> Infra3
    AdminService[运营管理平台] <-- gRPC --> Infra3
    ContentService[内容平台] <-- gRPC --> Infra3
    MessageQueue[消息队列] <-- Kafka --> Infra4
```

**各层职责说明**：

1. **接口层（API Layer）**：
   - **核心职责**：接收并处理外部请求，进行参数验证和响应格式化
   - **封装变化**：API协议、请求格式、响应格式的变化
   - **依赖规则**：只依赖应用服务层，不直接访问领域层和仓储层
   - **组件示例**：作业列表API、作业报告API、学生报告API等

2. **应用服务层（Application Service Layer）**：
   - **核心职责**：编排业务流程，协调领域服务和仓储层，实现用例
   - **封装变化**：业务流程、用例实现的变化
   - **依赖规则**：依赖领域服务层和仓储层，协调它们完成复杂业务
   - **组件示例**：报告聚合服务、学生报告服务、答题分析服务等

3. **领域服务层（Domain Service Layer）**：
   - **核心职责**：实现核心业务规则、计算逻辑和策略规则
   - **封装变化**：业务规则、计算公式、策略算法的变化
   - **依赖规则**：可以依赖仓储层获取数据，但不依赖应用服务层
   - **组件示例**：数据计算引擎、策略规则服务、异常检测服务等

4. **仓储层（Repository Layer）**：
   - **核心职责**：提供数据访问抽象，处理数据查询、持久化和缓存
   - **封装变化**：数据源、查询方式、缓存策略的变化
   - **依赖规则**：依赖基础设施层，提供数据访问的统一接口
   - **组件示例**：作业仓储、学生仓储、题目仓储、报告缓存仓储等

5. **基础设施层（Infrastructure Layer）**：
   - **核心职责**：提供技术实现细节，如数据库访问、缓存、外部服务调用
   - **封装变化**：具体技术实现、第三方库、外部系统接口的变化
   - **依赖规则**：不依赖其他层，被仓储层依赖
   - **组件示例**：数据库适配器、缓存适配器、外部服务适配器等

**设计理由**：

1. 我们选择四层结构而非传统的三层或五层，是基于系统的业务特性和复杂度：
   - 作业报告系统需要处理复杂的统计计算和规则逻辑，因此单独划分领域服务层
   - 数据访问和缓存策略是系统性能的关键，因此将仓储层独立出来
   - 系统整体业务逻辑明确，无需更细致的分层

2. 这种分层结构符合关注点分离原则：
   - 接口层专注于请求处理和响应格式
   - 应用服务层专注于业务流程编排
   - 领域服务层专注于核心计算逻辑和规则
   - 仓储层专注于数据访问策略
   - 基础设施层专注于技术实现细节

3. 该结构遵循依赖倒置原则，通过抽象接口实现层间解耦，提高了系统的可测试性和可维护性。

4. 针对作业报告系统的读多写少特性，这种分层方式可以方便地实现查询优化和缓存策略。 

### 3.3 模块/服务划分视图

#### 3.3.1 核心模块/服务识别与职责

基于业务需求和领域边界，教师端作业报告服务划分为以下核心模块：

1. **作业列表管理模块（AssignmentListManager）**
   - **职责**：管理和展示教师有权访问的作业列表，支持多条件筛选和搜索
   - **提供的能力**：作业列表获取、筛选、搜索、排序
   - **限界上下文**：作业任务的元数据管理，不涉及作业内容和学生答题详情

2. **报告生成引擎（ReportGenerationEngine）**
   - **职责**：生成作业报告的核心组件，包括班级统计、学生表现分析
   - **提供的能力**：班级数据汇总、进度和正确率计算、数据异常检测
   - **限界上下文**：将原始作业数据和答题数据转换为结构化报告

3. **学生分析器（StudentAnalyzer）**
   - **职责**：分析学生作业表现，识别需要鼓励和关注的学生
   - **提供的能力**：实现鼓励/关注学生的识别规则，计算学生表现指标
   - **限界上下文**：学生作业表现的分析和分类

4. **题目分析器（QuestionAnalyzer）**
   - **职责**：分析题目的答题情况，识别共性错题
   - **提供的能力**：计算题目正确率、答错人数、作答人数，识别共性错误
   - **限界上下文**：题目难度和答题模式分析

5. **提问管理器（QuestionManager）**
   - **职责**：管理和展示学生针对作业的提问
   - **提供的能力**：提问列表获取、提问详情查看
   - **限界上下文**：学生提问的收集和展示

6. **缓存管理器（CacheManager）**
   - **职责**：管理报告数据的缓存策略，提高查询性能
   - **提供的能力**：数据缓存、缓存失效策略、缓存预热
   - **限界上下文**：报告数据的高效获取和存储

7. **外部服务集成器（ExternalServiceIntegrator）**
   - **职责**：与任务管理服务、用户中心、内容平台等外部服务集成
   - **提供的能力**：原始数据获取、用户权限验证、内容关联
   - **限界上下文**：外部系统数据的获取和转换

8. **数据监听器（DataListener）**
   - **职责**：监听作业数据变更事件，触发报告更新
   - **提供的能力**：事件订阅、数据变更检测、报告刷新
   - **限界上下文**：作业数据变更的实时响应

#### 3.3.2 模块/服务交互模式与数据契约

```mermaid
graph LR
    Client['教师客户端'] -- HTTP/gRPC --> ReportSvc['作业报告服务']
    
    subgraph '核心业务模块'
        ReportSvc -- 内部调用 --> ListMgr['作业列表管理器']
        ReportSvc -- 内部调用 --> ReportEngine['报告生成引擎']
        ReportSvc -- 内部调用 --> StudentAnalyzer['学生分析器']
        ReportSvc -- 内部调用 --> QuestionAnalyzer['题目分析器']
        ReportSvc -- 内部调用 --> QuestionMgr['提问管理器']
    end
    
    subgraph '支撑服务'
        ListMgr -- 使用 --> CacheMgr['缓存管理器']
        ReportEngine -- 使用 --> CacheMgr
        StudentAnalyzer -- 使用 --> CacheMgr
        QuestionAnalyzer -- 使用 --> CacheMgr
        QuestionMgr -- 使用 --> CacheMgr
        
        ListMgr -- 依赖 --> ExternalSvc['外部服务集成器']
        ReportEngine -- 依赖 --> ExternalSvc
        StudentAnalyzer -- 依赖 --> ExternalSvc
        QuestionAnalyzer -- 依赖 --> ExternalSvc
        QuestionMgr -- 依赖 --> ExternalSvc
        
        DataListener['数据监听器'] -- 更新 --> CacheMgr
    end
    
    %% 外部服务连接
    TaskService['任务管理服务'] -- gRPC/Kafka --> ExternalSvc
    UserCenter['用户中心'] -- gRPC --> ExternalSvc
    AdminService['运营管理平台'] -- gRPC --> ExternalSvc
    ContentService['内容平台'] -- gRPC --> ExternalSvc
    
    %% 数据存储
    CacheMgr -- Redis --> Cache['缓存']
    ExternalSvc -- PostgreSQL/ClickHouse --> DB['数据库']
    
    %% 事件流
    TaskService -- Kafka事件 --> DataListener
    
    %% 样式设置
    classDef core fill:#A6E22E,stroke:#333,color:black;
    classDef support fill:#66D9EF,stroke:#333,color:black;
    classDef external fill:#FD971F,stroke:#333,color:black;
    classDef storage fill:#AE81FF,stroke:#333,color:white;
    
    class ListMgr,ReportEngine,StudentAnalyzer,QuestionAnalyzer,QuestionMgr core;
    class CacheMgr,ExternalSvc,DataListener support;
    class TaskService,UserCenter,AdminService,ContentService external;
    class Cache,DB storage;
```

**主要交互模式和数据契约**：

1. **客户端与作业报告服务**
   - **交互方式**：HTTP/gRPC接口
   - **核心数据内容**：作业列表请求（筛选条件）、作业报告请求（作业ID、班级ID）

2. **作业报告服务与作业列表管理器**
   - **交互方式**：内部方法调用
   - **核心数据内容**：筛选参数（教师ID、类型、班级、学科、日期）、排序参数

3. **作业报告服务与报告生成引擎**
   - **交互方式**：内部方法调用
   - **核心数据内容**：作业ID、班级ID、学生ID，返回报告结构（班级统计、学生列表）

4. **报告生成引擎与学生分析器**
   - **交互方式**：内部方法调用
   - **核心数据内容**：学生答题数据列表，返回学生分析结果（鼓励/关注标记、异常数据）

5. **报告生成引擎与题目分析器**
   - **交互方式**：内部方法调用
   - **核心数据内容**：题目ID列表、答题数据，返回题目分析结果（正确率、答错人数、共性错题）

6. **外部服务集成器与任务管理服务**
   - **交互方式**：gRPC调用
   - **核心数据内容**：获取作业信息（作业ID）、获取学生答题数据（作业ID、班级ID、学生ID）

7. **外部服务集成器与用户中心**
   - **交互方式**：gRPC调用
   - **核心数据内容**：验证教师身份（教师ID、Token）、获取教师权限信息

8. **外部服务集成器与运营管理平台**
   - **交互方式**：gRPC调用
   - **核心数据内容**：获取班级信息（班级ID）、获取学生信息（学生ID列表）

9. **外部服务集成器与内容平台**
   - **交互方式**：gRPC调用
   - **核心数据内容**：获取题目信息（题目ID列表）、获取题目解析

10. **数据监听器与任务管理服务**
    - **交互方式**：Kafka事件订阅
    - **核心数据内容**：作业状态变更事件、学生提交答案事件

11. **缓存管理器与Redis**
    - **交互方式**：Redis客户端调用
    - **核心数据内容**：缓存报告数据（作业ID、班级ID为键）、设置过期时间

这种模块划分和交互设计的优势：

1. **职责明确**：每个模块有清晰的单一职责，符合单一职责原则
2. **高内聚低耦合**：相关功能集中在一起，模块间通过定义良好的接口交互
3. **可扩展性**：新增功能可以通过添加新模块或扩展现有模块实现
4. **可测试性**：模块间依赖清晰，易于进行单元测试
5. **性能优化**：缓存管理器和数据监听器的设计支持高性能查询和实时数据更新 

### 3.4 关键业务流程的高层视图

#### 3.4.1 查看作业列表流程

该流程描述教师获取和筛选作业列表的过程，是报告查看的起点。

```mermaid
sequenceDiagram
    actor Teacher as 教师
    participant Client as 教师客户端
    participant API as 作业报告服务API
    participant ListMgr as 作业列表管理器
    participant CacheMgr as 缓存管理器
    participant External as 外部服务集成器
    participant TaskSvc as 任务管理服务
    participant UserSvc as 用户中心

    Teacher->>Client: 打开作业列表页面
    Client->>API: 请求获取作业列表（筛选条件）
    
    API->>UserSvc: 验证教师身份和权限
    UserSvc-->>API: 返回身份验证结果
    
    API->>ListMgr: 调用获取作业列表
    ListMgr->>CacheMgr: 尝试获取缓存数据
    
    alt 缓存命中
        CacheMgr-->>ListMgr: 返回缓存的作业列表
    else 缓存未命中
        ListMgr->>External: 请求获取作业数据
        External->>TaskSvc: 调用任务管理服务API
        TaskSvc-->>External: 返回原始作业数据
        External-->>ListMgr: 返回格式化的作业数据
        ListMgr->>CacheMgr: 存储到缓存
    end
    
    ListMgr->>ListMgr: 应用筛选条件（类型、班级、学科、日期）
    ListMgr-->>API: 返回筛选后的作业列表
    API-->>Client: 返回作业列表数据
    Client->>Teacher: 展示作业列表
```

#### 3.4.2 查看作业报告详情流程

该流程描述教师查看特定作业的报告详情，包括学生报告、答题结果和学生提问三个主要Tab的数据获取和展示过程。

```mermaid
sequenceDiagram
    actor Teacher as 教师
    participant Client as 教师客户端
    participant API as 作业报告服务API
    participant AppSvc as 报告聚合服务
    participant Student as 学生分析器
    participant Question as 题目分析器
    participant CacheMgr as 缓存管理器
    participant External as 外部服务集成器
    participant TaskSvc as 任务管理服务
    participant AdminSvc as 运营管理平台
    participant ContentSvc as 内容平台

    Teacher->>Client: 点击作业卡片进入报告详情
    Client->>API: 请求作业报告（作业ID, 班级ID）
    API->>AppSvc: 调用报告聚合服务

    par 并行获取班级统计数据
        AppSvc->>CacheMgr: 尝试获取班级统计缓存
        alt 缓存命中
            CacheMgr-->>AppSvc: 返回缓存数据
        else 缓存未命中
            AppSvc->>External: 请求作业完成情况
            External->>TaskSvc: 调用任务管理服务API
            TaskSvc-->>External: 返回原始作业数据
            External->>AdminSvc: 获取班级和学生信息
            AdminSvc-->>External: 返回班级和学生数据
            External-->>AppSvc: 返回整合数据
            AppSvc->>CacheMgr: 存储到缓存
        end
    and 并行计算学生表现数据
        AppSvc->>Student: 请求分析学生数据
        Student->>External: 获取学生答题原始数据
        External->>TaskSvc: 调用任务管理服务API
        TaskSvc-->>External: 返回学生答题数据
        External-->>Student: 返回格式化数据
        Student->>Student: 应用"鼓励/关注"规则
        Student->>Student: 计算异常数据标记
        Student-->>AppSvc: 返回学生分析结果
    and 并行获取题目分析数据
        AppSvc->>Question: 请求分析题目数据
        Question->>External: 获取题目和答题数据
        External->>TaskSvc: 获取学生答题记录
        TaskSvc-->>External: 返回答题记录
        External->>ContentSvc: 获取题目内容和答案
        ContentSvc-->>External: 返回题目数据
        External-->>Question: 返回整合数据
        Question->>Question: 计算正确率、答错人数等
        Question->>Question: 识别共性错题
        Question-->>AppSvc: 返回题目分析结果
    and 并行获取学生提问数据
        AppSvc->>External: 请求学生提问数据
        External->>TaskSvc: 调用任务管理服务API
        TaskSvc-->>External: 返回提问数据
        External-->>AppSvc: 返回格式化的提问数据
    end

    AppSvc-->>API: 返回完整的报告数据
    API-->>Client: 返回报告数据
    Client->>Teacher: 展示作业报告详情（默认学生报告Tab）

    alt 教师切换到"答题结果"Tab
        Teacher->>Client: 切换到答题结果Tab
        Client->>Teacher: 展示题目统计和详情
    else 教师切换到"学生提问"Tab
        Teacher->>Client: 切换到学生提问Tab
        Client->>Teacher: 展示学生提问列表
    end
```

#### 3.4.3 查看单个学生作业报告流程

该流程描述教师查看单个学生的作业报告详情，是从学生报告Tab深入到单个学生维度的流程。

```mermaid
sequenceDiagram
    actor Teacher as 教师
    participant Client as 教师客户端
    participant API as 作业报告服务API
    participant StudentSvc as 学生报告服务
    participant CacheMgr as 缓存管理器
    participant External as 外部服务集成器
    participant TaskSvc as 任务管理服务
    participant ContentSvc as 内容平台

    Teacher->>Client: 点击学生报告中的学生条目
    Client->>API: 请求单个学生报告（作业ID，学生ID）
    API->>StudentSvc: 调用学生报告服务
    
    StudentSvc->>CacheMgr: 尝试获取缓存数据
    alt 缓存命中
        CacheMgr-->>StudentSvc: 返回缓存的学生报告
    else 缓存未命中
        StudentSvc->>External: 获取学生答题数据
        External->>TaskSvc: 调用任务管理服务API
        TaskSvc-->>External: 返回学生答题原始数据
        
        StudentSvc->>External: 获取作业题目数据
        External->>ContentSvc: 调用内容平台API
        ContentSvc-->>External: 返回题目内容和答案
        External-->>StudentSvc: 返回整合数据
        
        StudentSvc->>StudentSvc: 计算学生表现指标
        StudentSvc->>CacheMgr: 存储到缓存
    end
    
    StudentSvc-->>API: 返回学生报告数据
    API-->>Client: 返回格式化的报告数据
    Client->>Teacher: 展示单个学生的作业报告
```

#### 3.4.4 数据变更触发报告更新流程

该流程描述当学生提交新的答案或作业状态变更时，系统如何实时更新报告数据。

```mermaid
stateDiagram-v2
    [*] --> 监听数据变更事件
    
    监听数据变更事件 --> 学生提交新答案
    监听数据变更事件 --> 作业状态变更
    监听数据变更事件 --> 教师批改完成
    
    学生提交新答案 --> 确定影响范围
    作业状态变更 --> 确定影响范围
    教师批改完成 --> 确定影响范围
    
    确定影响范围 --> 按作业ID失效缓存
    确定影响范围 --> 按学生ID失效缓存
    确定影响范围 --> 按题目ID失效缓存
    
    按作业ID失效缓存 --> 发送刷新通知
    按学生ID失效缓存 --> 发送刷新通知
    按题目ID失效缓存 --> 发送刷新通知
    
    发送刷新通知 --> 预热关键数据
    发送刷新通知 --> 等待下次查询
    
    预热关键数据 --> [*]
    等待下次查询 --> [*]
```

#### 3.4.5 实时计算共性错题流程

该流程描述系统如何识别和计算共性错题，为教师提供教学干预的重点参考。

```mermaid
flowchart TD
    Start[开始] --> A[获取作业题目列表]
    A --> B[获取全部学生答题数据]
    B --> C[按题目ID聚合答题数据]
    
    C --> D[遍历每个题目]
    D --> E{计算错误率}
    E -->|错误率 > 阈值| F[标记为共性错题]
    E -->|错误率 <= 阈值| G[标记为正常题目]
    
    F --> H[收集错题具体错误点]
    G --> I[继续下一题目]
    H --> I
    
    I --> J{是否还有题目?}
    J -->|是| D
    J -->|否| K[按错误率排序共性错题]
    
    K --> L[生成共性错题报告]
    L --> End[结束]
    
    %% 添加阈值配置说明
    E -- 配置的错误率阈值 --> Threshold[错误率阈值：30%]
```

### 3.5 数据架构概述

#### 3.5.1 主要数据存储选型与理由

教师端作业报告服务主要使用以下数据存储技术：

1. **PostgreSQL**
   - **用途**：存储作业报告的核心业务数据，包括作业元数据、学生基本信息
   - **选型理由**：PostgreSQL提供强大的事务支持和数据一致性保证，适合存储需要ACID特性的结构化数据；同时支持JSON类型，可以灵活存储半结构化数据

2. **ClickHouse**
   - **用途**：存储和分析大量的学生答题记录、统计数据
   - **选型理由**：ClickHouse专为OLAP（联机分析处理）设计，具有极高的查询性能，适合处理大量的答题数据统计和分析；列式存储特性使其在聚合查询上表现优异

3. **Redis**
   - **用途**：缓存热门报告数据、用户会话信息、分布式锁
   - **选型理由**：极高的读写性能，支持多种数据结构，适合缓存频繁访问的报告数据；支持设置过期时间，便于管理缓存生命周期

4. **Kafka**
   - **用途**：处理作业状态变更、学生提交答案等事件
   - **选型理由**：高吞吐量的消息队列，适合处理大量的作业数据变更事件；支持持久化，确保事件不会丢失；支持消费者组，可以实现多实例协同处理

#### 3.5.2 核心领域对象/数据结构的概念定义

1. **作业报告（AssignmentReport）**
   - **概念定义**：作业报告是对作业任务执行情况的统计分析结果，是教师了解学生学习情况的核心数据视图
   - **核心属性**：报告ID、作业ID、班级ID、发布时间、截止时间、班级平均进度、班级平均正确率、建议鼓励学生列表、建议关注学生列表、学生完成情况列表、共性错题数量
   - **关联关系**：与作业任务、学生、班级、题目关联

2. **学生作业表现（StudentPerformance）**
   - **概念定义**：描述学生在特定作业中的表现情况，包括完成度、正确率、用时等指标
   - **核心属性**：学生ID、作业ID、完成进度、正确率、用时、最近提交时间、异常标记、鼓励/关注标记
   - **关联关系**：与学生、作业报告关联

3. **题目统计数据（QuestionStatistics）**
   - **概念定义**：描述题目在特定作业中的统计数据，包括正确率、答错人数、作答人数等
   - **核心属性**：题目ID、作业ID、正确率、答错人数、作答人数、共性错题标记
   - **关联关系**：与题目、作业报告关联

4. **策略规则（AnalysisRule）**
   - **概念定义**：用于识别需要鼓励和关注的学生的规则集合，以及识别共性错题的规则
   - **核心属性**：规则ID、规则类型（鼓励/关注/共性错题）、规则条件、规则参数、规则描述
   - **关联关系**：影响学生作业表现和题目统计数据的分析结果

#### 3.5.3 数据一致性与同步策略

1. **读写策略**
   - 采用读多写少的数据访问模式，针对报告查询进行优化
   - 使用CQRS模式，将报告生成（写操作）和报告查询（读操作）分离处理

2. **缓存策略**
   - 实现多级缓存：本地内存缓存（短期高频数据）+ Redis分布式缓存（中期数据）
   - 按粒度缓存：作业级别、班级级别、学生级别、题目级别
   - 缓存失效策略：基于事件驱动的精确失效 + 基于时间的兜底失效

3. **数据同步机制**
   - 基于Kafka事件实现数据变更的实时同步
   - 使用CDC（变更数据捕获）技术监控原始数据的变更
   - 实现定时任务作为兜底数据同步机制

## 4. Golang技术栈与关键库选型

### 4.1 Golang版本选择

- **选择**：Go 1.20+
- **选型理由**：
  - 提供了泛型支持，可以编写更通用的数据结构和算法，减少代码冗余
  - 改进的垃圾回收机制，降低了GC延迟，提高了服务性能
  - 优化的内存管理，更适合处理大量的报告数据计算
  - 与团队现有技术栈兼容性好

### 4.2 Web/RPC框架

- **选择**：Kratos 2.8.3 (基于技术栈约束)
- **选型理由**：
  - Kratos是一个完整的微服务框架，提供了HTTP/gRPC服务集成
  - 内置良好的中间件生态，包括日志、链路追踪、熔断、限流等
  - 支持声明式API定义，可以同时生成客户端和服务端代码
  - 有完善的错误处理机制，便于服务间错误传递和处理
  - 符合公司技术栈约束要求

### 4.3 数据库驱动与ORM

- **选择**：
  - PostgreSQL：`github.com/jackc/pgx/v5` 作为驱动
  - ClickHouse：`github.com/ClickHouse/clickhouse-go/v2`
  - ORM：使用轻量级工具 `github.com/jmoiron/sqlx` 而非完整ORM

- **选型理由**：
  - `pgx` 相比 `lib/pq` 提供了更好的性能和更现代的特性，如连接池管理
  - `clickhouse-go` 是官方推荐的ClickHouse Go驱动，支持最新的ClickHouse特性
  - 选择 `sqlx` 而非完整ORM如GORM，因为：
    - 报告系统需要大量复杂的统计查询，完整ORM在复杂查询上不够灵活
    - `sqlx` 提供了基本的结构体映射功能，同时保留了原生SQL的灵活性
    - 减少了ORM带来的性能开销，更适合高性能数据分析场景

### 4.4 消息队列客户端库

- **选择**：`github.com/segmentio/kafka-go` (Kafka 3.6版本兼容)
- **选型理由**：
  - 纯Go实现，无需CGO依赖，简化部署和交叉编译
  - 提供了高级API，如Consumer Groups管理、批量消息处理等
  - 性能良好，适合处理作业系统的高并发事件流
  - 支持精确的消息确认机制，确保事件处理的可靠性

### 4.5 缓存客户端库

- **选择**：`github.com/redis/go-redis/v9` (Redis 6.0.3兼容)
- **选型理由**：
  - 主流的Redis Go客户端，社区活跃，文档完善
  - 支持丰富的Redis数据类型和操作
  - 提供了连接池管理、重试机制、超时控制等高级特性
  - 支持哨兵模式和集群模式，便于后期扩展Redis架构

### 4.6 日志与可观测性

- **选择**：
  - 日志：项目内置日志服务 (`app/core/loger/logger_new`)
  - 链路追踪：Zipkin 3.4.3
  - 指标监控：Prometheus客户端 `github.com/prometheus/client_golang`

- **选型理由**：
  - 使用公司标准日志服务，确保日志格式一致性和可分析性
  - Zipkin支持分布式追踪，便于排查跨服务调用问题
  - Prometheus客户端便于暴露自定义指标，监控报告生成性能和查询延迟

### 4.7 工具库

- **选择**：
  - 配置管理：`github.com/spf13/viper`
  - 依赖注入：`github.com/google/wire`
  - JSON处理：标准库 `encoding/json`
  - 并发控制：`golang.org/x/sync/errgroup` 和 `golang.org/x/sync/singleflight`

- **选型理由**：
  - `viper` 提供了灵活的配置管理，支持环境变量、配置文件等多种方式
  - `wire` 提供编译时依赖注入，避免运行时反射带来的性能损失
  - 系统需要高效处理并发计算任务，`errgroup` 和 `singleflight` 可以有效管理并发和防止重复计算

## 5. 部署与可观测性

### 5.1 部署架构

作业报告服务采用容器化部署在Kubernetes集群中，实现高可用和自动扩缩容能力。

```mermaid
flowchart TD
    Client[教师客户端] --> ApiGateway[API网关/负载均衡]
    
    subgraph K8s[Kubernetes集群]
        ApiGateway --> ReportPods[作业报告服务Pod集群]
        ReportPods --> ConfigMap[配置ConfigMap]
        ReportPods --> Secret[敏感配置Secret]
        
        ReportPods --> ServiceA[任务管理服务]
        ReportPods --> ServiceB[用户中心]
        ReportPods --> ServiceC[运营管理平台]
        ReportPods --> ServiceD[内容平台]
        
        ReportPods --> Redis[Redis缓存集群]
        ReportPods --> PostgreSQL[PostgreSQL数据库]
        ReportPods --> ClickHouse[ClickHouse分析库]
        ReportPods --> Kafka[Kafka消息队列]
    end
    
    subgraph Monitoring[监控系统]
        Prometheus[Prometheus] --> AlertManager[告警管理器]
        ReportPods --> Prometheus
        Zipkin[Zipkin] --> ReportPods
        LogSystem[日志系统] --> ReportPods
    end
```

**部署配置要点**：

1. **容器资源配置**：
   - CPU: 初始 2 核，最大 4 核
   - 内存: 初始 2GB，最大 4GB
   - 根据实际负载调整资源限制

2. **水平伸缩策略**：
   - 初始副本数: 3
   - 基于CPU利用率的HPA：目标70%，最小2个副本，最大10个副本
   - 应对高峰期的预扩容策略

3. **滚动更新策略**：
   - 最大不可用: 25%
   - 最大增量: 25%
   - 确保平滑升级，避免服务中断

4. **亲和性与反亲和性**：
   - 节点亲和性：优先调度到SSD节点
   - Pod反亲和性：避免多副本调度到同一节点，提高可用性

### 5.2 可观测性设计

#### 5.2.1 日志策略

采用结构化日志（JSON格式），按不同级别记录系统运行状态：

- **INFO级别**：记录正常的业务流程，包括API请求、缓存命中率、报告生成完成等
- **WARN级别**：记录潜在问题，如缓存未命中、外部服务响应慢等
- **ERROR级别**：记录异常情况，如外部服务调用失败、数据处理错误等
- **DEBUG级别**：仅在非生产环境启用，记录详细调试信息

关键日志点：

1. API请求的开始和结束，包含TraceID、请求参数（脱敏）、响应时间、返回状态
2. 外部服务调用的性能和结果
3. 缓存操作的结果和性能
4. 报告计算过程中的关键步骤和性能指标

#### 5.2.2 监控指标

通过Prometheus收集以下关键指标：

1. **API性能指标**：
   - 请求延迟（P50/P95/P99）
   - 请求成功率
   - 请求QPS

2. **业务指标**：
   - 报告生成耗时
   - 缓存命中率
   - 并发用户数
   - 查询的作业数量

3. **系统资源指标**：
   - CPU使用率
   - 内存使用率
   - 网络IO
   - GC频率和时长

4. **依赖服务指标**：
   - 外部服务调用延迟
   - 外部服务调用失败率
   - 数据库查询性能
   - 消息队列堆积情况

#### 5.2.3 告警策略

设置多级告警策略，及时发现和解决问题：

1. **P0级告警**（立即处理）：
   - 服务不可用
   - 错误率超过5%
   - API P99延迟超过1秒

2. **P1级告警**（2小时内处理）：
   - 缓存命中率低于80%
   - API P95延迟超过500ms
   - 依赖服务调用失败率超过2%

3. **P2级告警**（24小时内处理）：
   - 资源使用率超过80%
   - 缓存命中率下降超过10%
   - 报告生成时间异常增长

### 5.3 性能优化策略

1. **缓存优化**：
   - 多级缓存设计：本地内存缓存+Redis分布式缓存
   - 按访问频率优化缓存策略：热点报告数据预加载
   - 缓存粒度设计：作业级、班级级、学生级、题目级缓存

2. **数据库优化**：
   - 建立合理的索引，优化查询性能
   - 使用ClickHouse进行复杂统计查询
   - 对大量数据进行分区，提高查询效率

3. **并发处理优化**：
   - 使用goroutine并行处理独立的计算任务
   - 合理设置连接池大小，避免资源竞争
   - 使用singleflight防止缓存穿透和重复计算

4. **网络优化**：
   - 批量获取数据，减少网络往返
   - 压缩传输数据，减少网络带宽使用
   - 服务间调用使用gRPC，提高通信效率

## 6. 安全与扩展性考量

### 6.1 安全措施

报告服务涉及教师和学生敏感数据，安全措施至关重要：

1. **认证与授权**：
   - 基于用户中心的统一身份认证
   - 严格的RBAC权限控制，确保教师只能访问其有权限查看的班级和学生数据
   - API级别的权限校验，防止越权访问

2. **数据安全**：
   - 敏感数据传输加密（TLS）
   - 敏感信息（如学生ID）加密存储
   - 日志和监控数据脱敏处理

3. **API安全**：
   - 请求参数严格校验，防止注入攻击
   - 实现请求频率限制，防止DoS攻击
   - 定期安全扫描和渗透测试

4. **依赖安全**：
   - 定期更新依赖库，修复已知漏洞
   - 容器镜像安全扫描
   - 最小权限原则配置服务账号

### 6.2 扩展性设计

架构设计考虑了未来的扩展需求：

1. **功能扩展**：
   - 模块化设计允许轻松添加新的报告类型和分析维度
   - 开放的策略规则引擎，支持灵活配置"鼓励"和"关注"规则
   - 预留自定义报表和导出功能的扩展点

2. **性能扩展**：
   - 基于Kubernetes的水平扩展，应对用户量增长
   - 分片策略设计，支持数据规模扩展
   - 可配置的缓存策略，根据实际负载调整

3. **集成扩展**：
   - 基于接口抽象的外部服务集成，易于对接新的服务
   - 事件驱动架构支持新增事件类型和处理流程
   - 标准化的数据导入/导出接口

### 6.3 国际化与本地化支持

考虑产品未来可能的国际化需求：

1. **文本分离**：将用户可见文本（如提示信息）从代码中分离，支持多语言配置
2. **时区处理**：时间相关的计算和展示考虑时区差异
3. **数据格式**：支持不同地区的数据格式（如日期格式、数字格式）

### 6.4 容灾与高可用设计

保障系统在面对故障时的可用性：

1. **多区域部署**：支持在多个可用区部署，避免单一区域故障影响服务
2. **熔断与降级**：实现依赖服务的熔断机制，在外部服务不可用时提供降级服务
3. **备份与恢复**：定期数据备份和恢复机制，确保数据安全
4. **监控与预警**：实时监控系统状态，提前发现潜在问题

## 7. 挑战与风险分析

### 7.1 技术挑战

1. **复杂计算性能优化**：
   - **挑战**：报告生成过程中涉及大量的数据聚合和统计计算，可能导致性能瓶颈
   - **应对**：采用多级缓存策略、并行计算、预计算等技术优化计算性能；使用ClickHouse进行高效的分析查询

2. **数据一致性保障**：
   - **挑战**：作业数据变更（如学生提交答案、教师批改）需要及时反映在报告中，保障数据一致性
   - **应对**：基于Kafka实现事件驱动的数据同步机制，精确控制缓存失效策略

3. **跨服务依赖管理**：
   - **挑战**：作业报告服务依赖多个外部服务，外部服务的不可用可能导致报告服务受影响
   - **应对**：实现熔断、超时控制、降级策略；针对关键数据实现本地缓存，减少外部依赖

4. **定制化规则引擎设计**：
   - **挑战**：鼓励/关注学生和共性错题的识别规则需要灵活配置和高效执行
   - **应对**：设计可配置的规则引擎，支持规则的动态更新和扩展

### 7.2 潜在风险与缓解策略

| 风险 | 影响 | 可能性 | 缓解策略 |
|------|------|--------|----------|
| 报告生成性能不足 | 高 | 中 | 实施多级缓存、预计算、定时任务分散计算压力 |
| 外部服务依赖不稳定 | 高 | 中 | 实现熔断机制、超时控制、fallback降级方案 |
| 数据量增长超预期 | 中 | 低 | 设计水平扩展架构、数据分区策略、冷热数据分离 |
| 并发访问峰值过高 | 高 | 中 | K8s水平扩展、请求限流、资源隔离 |
| 缓存穿透和雪崩 | 高 | 低 | 布隆过滤器、缓存预热、多级缓存、singleflight防击穿 |
| 数据安全和隐私问题 | 高 | 低 | 严格的权限控制、数据脱敏、审计日志 |

### 7.3 后续优化方向

1. **算法优化**：
   - 优化"建议鼓励"和"建议关注"的识别算法，提高准确性
   - 研究更高效的共性错题识别方法，减少计算资源消耗

2. **用户体验提升**：
   - 支持报告导出功能
   - 提供更丰富的可视化图表

3. **数据增强**：
   - 增加学习趋势分析功能
   - 集成AI技术，提供教学建议

4. **架构演进**：
   - 研究实时计算引擎，进一步提高报告生成速度
   - 考虑引入流处理技术，实现更实时的数据更新

## 8. 架构文档自查清单

### 8.1 架构完备性自查清单

| 检查项                                  | 结果 | 备注                                            |
|-----------------------------------------|------|--------------------------------------------------|
| 是否覆盖所有核心业务能力                | ✅    | 作业列表、作业报告、学生报告、题目分析、提问管理均已覆盖 |
| 是否明确定义系统分层与职责              | ✅    | 已定义清晰的四层架构及各层职责                  |
| 是否定义了模块/服务的边界和交互方式     | ✅    | 已明确划分8个核心模块及其交互模式               |
| 是否包含关键业务流程的高层视图          | ✅    | 5个核心业务流程通过时序图/状态图/流程图展示     |
| 是否明确数据存储选型与理由              | ✅    | PostgreSQL、ClickHouse、Redis、Kafka选型已说明   |
| 是否定义了核心领域对象/数据结构         | ✅    | 定义了作业报告、学生作业表现等核心概念          |
| 是否定义了数据一致性与同步策略          | ✅    | 包含读写策略、缓存策略、数据同步机制            |
| 是否覆盖技术栈与关键库选型              | ✅    | 包含Go版本、框架、数据库驱动、MQ、缓存等选型    |
| 是否包含部署与可观测性设计              | ✅    | 包含部署架构、监控指标、告警策略等              |
| 是否考虑安全性设计                      | ✅    | 包含认证授权、数据安全、API安全、依赖安全       |
| 是否考虑扩展性设计                      | ✅    | 包含功能扩展、性能扩展、集成扩展等方面          |
| 是否分析技术挑战与风险                  | ✅    | 已分析主要技术挑战和潜在风险及缓解策略          |
| 是否考虑高可用与容灾设计                | ✅    | 包含多区域部署、熔断降级、备份恢复等机制        |
| 是否符合公司技术栈约束                  | ✅    | 严格遵循技术栈约束文件规定                      |

### 8.2 需求-架构完备性自查清单

| PRD对应章节/需求点                                         | 架构支持情况 | 备注                                                      |
|-----------------------------------------------------------|-------------|----------------------------------------------------------|
| 2.1 核心功能 - 作业列表展示                                | ✅           | 3.3.1 作业列表管理模块、3.4.1 查看作业列表流程            |
| 2.1 核心功能 - 作业报告详情                                | ✅           | 3.3.1 报告生成引擎、3.4.2 查看作业报告详情流程            |
| 2.1 核心功能 - 学生报告 Tab                                | ✅           | 3.3.1 学生分析器、3.4.2 查看作业报告详情流程（学生Tab部分） |
| 2.1 核心功能 - 答题结果 Tab                                | ✅           | 3.3.1 题目分析器、3.4.2 查看作业报告详情流程（答题Tab部分） |
| 2.1 核心功能 - 学生提问 Tab                                | ✅           | 3.3.1 提问管理器、3.4.2 查看作业报告详情流程（提问Tab部分） |
| 2.1 核心功能 - 单学生作业报告                              | ✅           | 3.3.1 报告生成引擎+学生分析器、3.4.3 查看单个学生作业报告流程 |
| 2.1 核心功能 - 策略说明（鼓励/关注规则、共性错题识别规则）  | ✅           | 3.5.2 策略规则对象、3.4.5 实时计算共性错题流程            |
| 3. 计算规则与公式 - 题目正确率                             | ✅           | 3.3.1 题目分析器（计算题目正确率功能）                    |
| 3. 计算规则与公式 - 题目答错人数                           | ✅           | 3.3.1 题目分析器（计算答错人数功能）                      |
| 3. 计算规则与公式 - 题目作答人数                           | ✅           | 3.3.1 题目分析器（计算作答人数功能）                      |
| 3. 计算规则与公式 - 班级进度/完成率                        | ✅           | 3.3.1 报告生成引擎（班级数据汇总功能）                    |
| 3. 计算规则与公式 - 班级正确率                             | ✅           | 3.3.1 报告生成引擎（班级数据汇总功能）                    |
| 3. 计算规则与公式 - 异常数据飘色                           | ✅           | 3.3.1 学生分析器（数据异常检测功能）                      |
| 3. 计算规则与公式 - 共性错题                               | ✅           | 3.4.5 实时计算共性错题流程                                |
| 3. 计算规则与公式 - 建议鼓励/关注学生                      | ✅           | 3.3.1 学生分析器（实现鼓励/关注学生的识别规则）           |
| 4. 场景1 - 监控班级整体作业表现                            | ✅           | 3.4.2 查看作业报告详情流程                                |
| 4. 场景2 - 识别并跟进个别学生                              | ✅           | 3.3.1 学生分析器 + 3.4.3 查看单个学生作业报告流程         |
| 4. 场景3 - 分析题目难点与共性错误                          | ✅           | 3.3.1 题目分析器 + 3.4.5 实时计算共性错题流程             |
| 4. 场景4 - 解答学生疑问                                    | ✅           | 3.3.1 提问管理器                                          |

## 9. 结论

本架构设计文档针对"教师端作业报告服务"提供了全面的技术方案。我们基于分层架构和微服务架构相结合的设计模式，构建了一个高性能、可扩展的作业报告系统，能够满足教师对学生学习情况的全方位监控需求。

系统主要包括作业列表管理、报告生成引擎、学生分析器、题目分析器等核心功能模块，通过合理的层次划分和模块分离，实现了高内聚低耦合的架构设计。我们采用了事件驱动和CQRS模式来处理作业数据的变更和查询，确保了系统的实时性和性能。

在技术选型方面，我们严格遵循公司的技术栈约束，选择了Kratos框架、PostgreSQL/ClickHouse数据库、Redis缓存和Kafka消息队列作为核心组件。系统采用容器化部署在Kubernetes环境中，配合完善的可观测性设计，保障了服务的高可用性和稳定性。

通过对潜在风险的分析和缓解策略的制定，我们确保了系统面对高并发、数据一致性、外部依赖等挑战时的健壮性。整体架构设计遵循了单一职责、开闭原则、依赖倒置等设计原则，为后续功能扩展和性能优化提供了良好的基础。

该架构设计已通过完备性自查，覆盖了PRD中定义的所有核心功能需求和业务场景，为研发团队提供了清晰的技术蓝图，可以指导后续的详细设计和实现工作。
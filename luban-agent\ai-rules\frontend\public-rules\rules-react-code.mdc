---
description: 
globs: 
alwaysApply: false
---
---
description: "React 项目规范和开发指南"
globs: ["**/*.{jsx,tsx,ts,scss,js,css}"]
alwaysApply: true
---

# 项目规范

## 技术栈规范
- 前端框架：React (v18.3.0, Hooks API)
- UI框架：MUI (v5.15.0) / Ant Design (v5.12.0)
- 状态管理：Redux Toolkit (v2.2.1) / Zustand (v4.5.0)
- 样式：SASS (v1.75.0) / Styled Components (v6.1.0) / Tailwind CSS (v3.4.0)
- 构建工具：Vite (v5.2.6) / Next.js (v14.1.0)
- TypeScript (v5.7.x)
- Node.js (≥v18.0.0)

## 代码规范

### 组件开发规范
- 使用 PascalCase 命名React组件文件（如：`TemplateBase.jsx` 或 `TemplateBase.tsx`）
- 功能相似的组件放在同一目录下
- 优先使用函数式组件和Hooks，避免使用类组件
- Props 定义必须指定类型和默认值（使用PropTypes或TypeScript接口）
- 使用解构获取props，避免直接引用 props.xxx

### 样式编写规范
- 使用 kebab-case 命名 CSS 类（如：`todo-list`）
- 必须遵循 BEM 命名规范：
  - 块：`block`
  - 元素：`block__element`
  - 修饰符：`block--modifier`
- 组件样式必须使用CSS Modules或styled-components等方案实现样式隔离
- 禁止使用行内样式（特殊情况除外）
- 禁止直接使用UI框架的类名作为选择器（如：`.MuiButton-root`）

### 状态管理规范
- 全局状态必须使用Redux Toolkit/Zustand等工具管理
- 使用useMemo/useCallback避免不必要的重渲染
- 异步action必须使用async/await或Promise处理
- 组件中优先使用props，避免滥用全局状态
- 使用React Context处理中等规模的状态共享

### TypeScript 规范
- 必须显式声明类型
- 禁止使用 any 类型
- 接口和类型定义使用 PascalCase
- 优先使用 interface 而非 type
- 为组件编写完整的Props和State接口

## 项目结构规范
```
src/
├── api/ # API请求
├── assets/ # 静态资源
├── components/ # 组件
├── hooks/ # 自定义Hooks
├── pages/ # 页面组件
├── store/ # 状态管理
├── types/ # 类型定义
└── utils/ # 工具函数
```

## 禁止事项
- 禁止使用jQuery或直接操作DOM（特殊情况使用useRef）
- 禁止在JSX中使用复杂表达式
- 禁止直接修改props，保持数据流单向性
- 禁止在组件中使用全局变量
- 禁止在render函数中直接定义函数（应使用useCallback）
- 禁止使用特定框架的类名作为选择器

## 特殊注意事项
1. 保留 CSS 中原有的 !important 关键字
2. 保留原有的代码注释和 console.log
3. 请勿将文案和注释中的全角引号（""）改为半角引号（""）
4. 使用React.Fragment避免不必要的DOM嵌套
5. 使用React.lazy和Suspense实现代码分割


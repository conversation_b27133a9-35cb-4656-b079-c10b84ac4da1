# 产品需求文档：教师端作业报告 v1.0

## 1. 需求背景与目标

### 1.1 需求目标
- **业务层面**：为教师提供高效的工具来监控和分析学生在课程作业、资源学习等任务中的表现，包括完成进度、答题正确率、用时、错题分布以及学生提问情况。通过及时的数据反馈，赋能教师进行有针对性的教学干预（如鼓励表扬、提醒关注），从而提升教学质量和学生学习效果。
- **技术层面**：构建清晰、可扩展的任务、素材与学生行为数据之间的底层关联，为后续更多类型的学情分析和智能化教学辅助功能奠定基础。
- **项目层面**：完成教师端P0核心功能，确保在约定6月上线版本能够跑通业务MVP流程，并在9月完成全部核心功能。

### 1.2 用户价值
- 对于 **合作校的学科教师**：
    - **解决了** 过去难以高效、全面掌握每个学生具体学情的痛点，尤其是在多班级授课场景下。
    - **带来了** 快速了解学生完成度、定位共性错题、识别需关注学生、进行及时表扬或提醒的能力，提升了教学工作的针对性和效率。
- 对于 **合作校的班主任**：
    - **解决了** 难以跨学科了解班级整体学风和个体学生学习状态的问题。
    - **带来了** 查看本班各科作业数据、进行班级整体学情分析的能力，为班级管理和学生辅导提供数据支持。
- 对于 **合作校的学科主任/年级主任/校长**：
    - **解决了** 缺乏有效工具从更高维度审视教学质量和学生学业水平的问题。
    - **带来了** 查看所辖范围内的教学数据报告，了解教学效果，为教学管理和决策提供依据。

## 2. 功能范围

### 2.1 核心功能 (P0)

#### 2.1.1 核心功能点1：作业列表展示与管理
- **简洁描述该功能是什么:** 为教师提供一个集中展示其所负责或有权限查看的全部教学任务（课程、作业、资源、测验）的列表界面，并支持对这些任务进行筛选、搜索和基本管理操作。
- **用户价值与场景:**
    - **用户场景:** 教师在日常教学管理中，需要快速概览已布置任务的整体状态，或查找特定任务进行后续操作（如查看报告、编辑等）。
    - **用户痛点:** 任务繁多时，难以快速找到目标任务；缺乏对任务状态的直观了解。
    - **核心价值:** 提高教师管理教学任务的效率，提供清晰的任务概览和便捷的查找途径。
- **功能详述与前端呈现:**
    - **主要交互流程:**
        1. 教师进入作业相关模块，默认展示作业列表。
        2. 教师可以通过顶部的Tab页（全部、课程、作业、资源、测验）切换不同类型的内容列表。
        3. 教师可以使用筛选器（如学科、年级、班级、布置日期）组合筛选列表内容。
        4. 教师可以在搜索框中输入关键词，模糊匹配任务名称进行搜索。
        5. 列表以卡片形式展示每个任务的核心信息。
        6. 对于本人布置的任务，教师可以点击卡片上的操作菜单（如"..."标记）进行编辑、删除、复制等操作。
    - **界面布局与元素:**
        - **布局原则:** 移动端界面，采用上下滑动列表形式。顶部为类型切换Tab和筛选区域，下方为内容卡片列表。
        - **核心UI元素:**
            - **内容类型Tabs:** "全部", "课程", "作业", "资源", "测验"。选中状态高亮。
            - **筛选器:**
                - 学科筛选器：下拉选择或弹出选择，如"数学~"。
                - 年级/班级筛选器：下拉选择或弹出选择，如"全部年级"、"高一3班"。
                - 日期范围筛选器：预设选项（如"最近一周"、"全部日期"）及自定义日期选择。
            - **搜索框:** 带有占位符提示"搜索"或"搜索任务名称"。
            - **任务卡片:**
                - **标题:** 显示任务名称，如"3.2.2函数的最值"。
                - **班级:** 显示所属班级，如"高一3班"。
                - **状态标识:** 如"到期"、"进行中"。
                - **关键数据指标 (根据任务类型不同):**
                    - 课程任务: 平均进度、课时数。
                    - 作业/测验任务: 完成率、正确率、待关注题目数。
                    - 资源任务: (根据实际情况定义，如学习人数、平均学习时长)。
                - **时间信息:** 发布与到期时间，如"课程 3月16日08:30发布:3月20日16:00到期"。
                - **操作菜单触发器:** 如"..."图标，点击后弹出操作选项（编辑、删除、复制，仅限本人布置）。
    - **用户操作与反馈:**
        - **切换Tab:** 点击Tab，列表内容异步加载并刷新，对应Tab高亮。
        - **使用筛选器:** 选择筛选条件后，列表内容异步加载并刷新，筛选器显示当前选中条件。
        - **搜索:** 输入关键词后，列表实时（或点击搜索按钮后）异步加载并刷新匹配结果。清空关键词后恢复原始列表。
        - **点击任务卡片:** 跳转到该任务的作业报告详情页。
        - **点击操作菜单:** 弹出菜单，选项根据权限显示。点击具体操作（如删除）后，执行相应逻辑，并给出成功或失败的Toast提示，列表刷新。
    - **数据展示与更新:** 列表数据根据筛选和搜索条件动态更新。各项统计指标（完成率、正确率等）实时或准实时更新。
- **优先级：** 高 (P0)
- **依赖关系：** 无。
- **Mermaid图示 (用户交互流程):**
  ```mermaid
  flowchart TD
      A[用户进入作业列表页] --> B{选择内容类型Tab};
      B -- 课程 --> C[请求课程数据];
      B -- 作业 --> D[请求作业数据];
      B -- 资源 --> E[请求资源数据];
      B -- 测验 --> F[请求测验数据];
      B -- 全部 --> G[请求全部类型数据];

      C --> H{使用筛选器};
      D --> H;
      E --> H;
      F --> H;
      G --> H;

      H -- 学科/年级/日期等 --> I[组合筛选条件];
      H -- 无额外筛选 --> I;

      I --> J[输入搜索关键词或不输入];
      J --> K[向服务端发送请求];
      K --> L[服务端返回任务列表数据];
      L --> M[前端渲染任务卡片列表];
      M --> N{点击任务卡片?};
      N -- 是 --> O[跳转到作业报告详情页];
      N -- 否 --> P{点击卡片操作菜单?};
      P -- 是 --> Q[显示操作选项（编辑/删除/复制）];
      Q -- 选择操作 --> R[执行操作并刷新列表];
      P -- 否 --> M; %% 用户继续浏览或操作
  ```

#### 2.1.2 核心功能点2：班级整体作业报告查看
- **简洁描述该功能是什么:** 教师点击作业列表中的某个任务卡片后，进入该任务的作业报告详情页，默认展示"学生报告"Tab，提供班级整体的学情概览、学生表现分群（值得表扬/需要关注）、班级统计数据以及详细的学生列表。
- **用户价值与场景:**
    - **用户场景:** 教师需要了解某次具体作业在班级内的整体完成情况，快速识别表现突出和需要帮助的学生，并查看每个学生的具体表现。
    - **用户痛点:** 逐个分析学生数据耗时耗力；难以快速把握班级整体学情和个体差异。
    - **核心价值:** 提供一站式的班级作业数据分析视图，提升教师对学情的掌控能力，支持数据驱动的教学干预。
- **功能详述与前端呈现:**
    - **主要交互流程:**
        1. 教师从作业列表点击特定作业卡片，进入作业报告详情页。
        2. 页面默认显示"学生报告"Tab。
        3. 教师可以查看作业基本信息（发布/截止时间、范围）、班级整体进度、待关注题目数。
        4. 教师可以查看系统推荐的"值得表扬"和"需要关注"学生列表，并执行"一键表扬"或"一键提醒"操作。
        5. 教师可以查看班级平均正确率和平均用时。
        6. 教师可以通过搜索框搜索学生姓名，查看学生列表。
        7. 教师可以点击学生列表中的"查看详情"操作，进入单个学生的作业报告。
        8. 教师可以点击"导出"按钮导出学生数据。
    - **界面布局与元素:** (参照原始PRD中 `image_PswKbOca7ouLbDxJzFScXHognOc` 的分析)
        - **导航栏:** 返回按钮、作业标题、预警设置入口。
        - **作业基础信息区:** 发布时间、截止时间、范围（如"全部（2节课）"）。
        - **班级整体概览区:** 班级进度百分比、待关注题目数。
        - **Tab切换区:** "学生报告"（默认选中）、"答题结果"、"学生提问"。
        - **快捷互动区:**
            - "值得表扬"学生列表（可横向滑动或折叠展开），附"一键表扬"按钮。
            - "需要关注"学生列表（可横向滑动或折叠展开），附"一键提醒"按钮。
        - **班级统计数据区:** 显示班级平均正确率、班级平均用时。
        - **学生列表管理区:** 学生姓名搜索框、"导出"按钮。
        - **学生详细数据列表区:**
            - **表头:** 学生信息、分层、完成进度、正确率、答题难度（原始PRD中提及，此处保留）、错题数/总题数、用时、操作。
            - **数据行:**
                - 学生姓名（可附头像）。
                - 分层标签（如"有进步"、"需关注"，可带颜色或图标区分）。
                - 完成进度（百分比及进度条）。
                - 正确率（百分比）。
                - 答题难度（如2.0）。
                - 错题/答题（如"1/16"）。
                - 用时（如"35分钟"）。
                - 操作："查看详情"链接或按钮。
        - **分页控件:** 显示总数据条数（如"共49条数据"），若数据过多则提供翻页。
    - **用户操作与反馈:**
        - **点击"一键表扬"/"一键提醒":** 按钮变为加载状态，操作成功后Toast提示，对应学生可能收到系统通知。
        - **搜索学生:** 输入姓名，列表实时筛选显示匹配学生。
        - **点击"导出":** 触发文件下载流程。
        - **点击学生"查看详情":** 跳转至该学生的个人作业报告页面。
        - **切换Tab:** 页面内容切换至对应Tab（答题结果、学生提问），URL可能相应变化。
    - **数据展示与更新:** 班级统计数据、学生列表数据均为动态加载。异常数据（如远低于平均值的）可进行特殊颜色标记（"飘色"）。学生列表支持按特定规则排序（如默认按UID，建议鼓励/关注学生置顶）。
- **优先级：** 高 (P0)
- **依赖关系：** 依赖"作业列表"功能选择具体作业。
- **Mermaid图示 (页面内主要流程):**
  ```mermaid
  flowchart TD
      A[进入作业报告详情页 - 学生报告Tab] --> B[显示作业基本信息];
      A --> C[显示班级整体概览];
      A --> D[显示'值得表扬'学生列表];
      D --> D1{点击'一键表扬'?};
      D1 -- 是 --> D2[执行表扬操作];
      D1 -- 否 --> E;
      A --> E[显示'需要关注'学生列表];
      E --> E1{点击'一键提醒'?};
      E1 -- 是 --> E2[执行提醒操作];
      E1 -- 否 --> F;
      A --> F[显示班级统计数据];
      A --> G[显示学生列表];
      G --> G1{搜索学生?};
      G1 -- 是 --> G2[筛选列表];
      G1 -- 否 --> G3;
      G3{点击'查看详情'?};
      G3 -- 是 --> H[跳转到学生个人报告];
      G3 -- 否 --> G; %% 返回列表
      A --> X[切换到'答题结果'Tab];
      A --> Y[切换到'学生提问'Tab];
  ```

#### 2.1.3 核心功能点3：班级作业报告 - 答题结果Tab
- **简洁描述该功能是什么:** 在作业报告详情页，教师切换到"答题结果"Tab后，可以查看本次作业所有题目的整体作答情况，包括题目列表、每道题的正确率、错误人数、作答人数，并能筛选共性错题、按关键词搜索题目。
- **用户价值与场景:**
    - **用户场景:** 教师希望了解班级在哪些题目上普遍存在困难（共性错题），哪些题目区分度较好，以便调整后续教学重点或进行针对性讲解。
    - **用户痛点:** 难以快速从大量题目中定位到学生的薄弱点；缺乏对题目维度的整体认知。
    - **核心价值:** 帮助教师从题目维度分析学情，精准定位教学难点，优化教学内容和策略。
- **功能详述与前端呈现:** (参照原始PRD中 `image_Kde9bV1SroJspDxtJQeccHOCneg` 和 `image_QbOCbI3ssoaDfKxamzIcZbB8nWd` 的分析)
    - **主要交互流程:**
        1. 在作业报告详情页，教师点击"答题结果"Tab。
        2. 页面展示作业的整体题目统计（总题数、共性错题数）和排序方式说明。
        3. 教师可以使用搜索框搜索题目关键词。
        4. 教师可以使用"只看共性错题"开关进行筛选。
        5. 教师可以按题型进行筛选。
        6. 题目列表展示每道题的题干预览、正确率、错误人数、作答人数。
        7. 教师可以点击题目的"查看"操作，进入该题目的详细作答分析页。
        8. 教师可以点击题目的"加入"操作（如加入错题本）。
        9. （可选，根据PRD图）侧边栏可能提供题目面板，快速导航至不同题目，并用颜色区分正确率。
    - **界面布局与元素:**
        - **整体统计区:** 显示"共XX道题，XX道共性错题"，并注明排序方式（如"已按照作答人数排序"，PRD描述为按作答错误人数排序更合理）。
        - **题目筛选与搜索区:**
            - 搜索框："搜索题目关键词"。
            - 开关："只看共性错题"。
            - 下拉筛选："全部题型"（或其他题型选项）。
        - **题目列表区:**
            - **单个题目卡片:**
                - 题号。
                - 题干预览（过长可折叠，支持展开）。图文混排。
                - 选项预览（根据PRD描述，动态布局，如一行1/2/4个选项）。
                - 答题统计：正确率（百分比）、XX人错误、XX人作答。
                - 操作按钮："加入"、"查看"。
                - （可选）答案与解析入口（默认收起，点击展开）。
        - **侧边题目面板 (可选):** 默认展开，可收起。列表展示题号，用颜色区分题目正确率（如红、黄、绿）。点击题号可快速定位到题目列表中的对应题目。
        - **分页控件:** 若题目数量多，则提供分页。
    - **用户操作与反馈:**
        - **切换Tab至"答题结果":** 内容区域更新为题目列表及相关控件。
        - **搜索/筛选题目:** 题目列表异步更新。
        - **点击"查看":** 跳转到题目详情页。
        - **点击"加入":** 给出成功提示，对应题目状态可能更新。
        - **展开/收起题干/解析:** 内容区域动态变化。
        - **点击侧边题目面板题号:** 主列表滚动到对应题目。
    - **数据展示与更新:**
        - 题目列表根据筛选条件动态加载。统计数据（正确率、错误人数等）基于学生首次作答数据。
        - 共性错题定义：PRD中P0定义为"答题人数>10人，任务中正确率＜60%"。P1支持自定义阈值。
- **优先级：** 高 (P0)
- **依赖关系：** 依赖"作业列表"功能选择具体作业，并进入作业报告。
- **Mermaid图示 (功能交互):**
  ```mermaid
  flowchart TD
      A[进入'答题结果'Tab] --> B[显示整体题目统计与排序说明];
      A --> C[显示题目筛选控件];
      C --> C1{使用搜索/筛选?};
      C1 -- 是 --> C2[更新题目列表];
      C1 -- 否 --> D;
      A --> D[显示题目列表];
      D --> E{点击题目'查看'?};
      E -- 是 --> F[跳转到题目详情页];
      E -- 否 --> G;
      G{点击题目'加入'?};
      G -- 是 --> H[执行加入操作];
      G -- 否 --> I;
      I{点击侧边题目面板题号?};
      I -- 是 --> J[主列表定位到该题];
      I -- 否 --> D;
  ```

#### 2.1.4 核心功能点4：班级作业报告 - 学生提问Tab
- **简洁描述该功能是什么:** 在作业报告详情页，教师切换到"学生提问"Tab后，可以查看学生在本次AI课（或其他支持提问的任务类型）中针对不同课程内容（如幻灯片）提出的问题。
- **用户价值与场景:**
    - **用户场景:** 教师希望了解学生在学习过程中主动提出的疑问，从而把握学生的困惑点，为答疑和后续教学提供参考。
    - **用户痛点:** 学生提问分散，不易集中查看和管理；难以将问题与具体教学内容关联。
    - **核心价值:** 集中展示学生提问，帮助教师了解学生的学习难点和思考过程，促进师生互动和教学改进。
- **功能详述与前端呈现:** (参照原始PRD中 `image_CMoSbVPqcoznYTxrEPec4qDQnce` 和 `image_An8wbFUSLo9yJjxSrPicif6ynbg` 的分析)
    - **主要交互流程:**
        1. 在作业报告详情页，教师点击"学生提问"Tab。
        2. 页面展示与当前任务（AI课）相关的、有学生评论/提问的素材内容结构。
        3. 内容按课程幻灯片（或内容节点）顺序展示，并显示每个节点下本班学生的评论/提问数统计。
        4. 教师可以点击某个内容节点，查看该节点下的具体提问列表。
        5. 教师可以查看提问详情，并可能进行回复或处理。
    - **界面布局与元素:**
        - **内容筛选/概览区 (可能存在):** 显示当前筛选的班级、素材范围。
        - **内容结构列表区:**
            - **单个内容节点 (如幻灯片、知识点):**
                - 节点标题/描述 (如"引入部分"、"复数分类")。
                - 该节点下本班学生提问数统计 (如"4条讨论"、"10条提问")。
                - 可能有展开/收起操作，查看具体的提问列表。
        - **提问详情展示 (点击内容节点后或直接展开):**
            - 学生姓名/头像。
            - 提问时间。
            - 提问内容。
            - （若支持）教师回复、其他学生回复。
            - 操作按钮（如"回复"、"标记为已处理"）。
    - **用户操作与反馈:**
        - **切换Tab至"学生提问":** 内容区域更新为提问列表或内容结构。
        - **点击内容节点:** 展开显示该节点下的提问，或跳转到提问详情页。
        - **查看提问详情:** 显示完整提问和相关回复。
        - **进行回复/处理操作:** 提交后，界面更新，如显示新回复，或提问状态改变。
    - **数据展示与更新:** 提问列表和数量统计动态加载。
- **优先级：** 高 (P0)
- **依赖关系：** 依赖"作业列表"功能选择具体作业，并进入作业报告。仅适用于支持提问功能的任务类型（如AI课）。
- **Mermaid图示 (功能交互):**
  ```mermaid
  flowchart TD
      A[进入'学生提问'Tab] --> B[显示课程内容结构及各节点提问数];
      B --> C{点击内容节点查看提问?};
      C -- 是 --> D[展示该节点下的提问列表];
      D --> E{查看具体提问详情?};
      E -- 是 --> F[显示提问及回复内容];
      F --> G{进行回复或处理?};
      G -- 是 --> H[提交操作并更新界面];
      G -- 否 --> F;
      E -- 否 --> D;
      C -- 否 --> B;
  ```

#### 2.1.5 核心功能点5：单个学生作业报告查看
- **简洁描述该功能是什么:** 教师从班级学生列表或其它入口进入某个特定学生的作业报告详情页，可以查看该生在本次作业中的详细答题情况（逐题对错、得分率）、提问记录等。
- **用户价值与场景:**
    - **用户场景:** 教师需要对特定学生进行深入的学情分析，了解其知识掌握的薄弱点、学习习惯等，以便进行个性化辅导或与学生/家长沟通。
    - **用户痛点:** 难以全面细致地了解单个学生的具体学习过程和问题。
    - **核心价值:** 提供精细化的个体学情数据，支持教师进行个性化教学和辅导。
- **功能详述与前端呈现:** (参照原始PRD中 `image_I4hibE2j6o3Kr4xlH1DcE0A3nag`, `image_FjLobW20loc0yVx64afcpujenMe`, `image_HauRbUdhqogkfyxqoVLcIIR2nPd` 等分析)
    - **主要交互流程:**
        1. 教师通过入口（如班级报告的学生列表中的"查看详情"）进入学生个人作业报告。
        2. 页面顶部展示学生姓名和作业名称，以及作业的基本信息（发布/截止/完成时间、范围、进度）。
        3. 页面同样包含"答题结果"、"学生提问"等Tab页，逻辑与班级报告类似，但数据仅限该学生。
        4. 在"答题结果"Tab下，展示该生作业的整体统计（总题数、错题数），以及题目列表。
        5. 题目列表逐条显示题目、该生的作答状态（正确/错误/未作答，用符号或文字表示）、得分率（若适用）。
        6. 教师可点击"查看"进入题目详情，了解学生答案、标准答案、解析。
    - **界面布局与元素:**
        - **导航栏:** 返回按钮、页面标题（"学生名+任务名称"）。
        - **答题概览区:**
            - 作业开始/要求完成时间。
            - 学生完成时间（若已完成）或当前进度（若未完成100%）。
            - 素材范围（可点击查看更多）。
            - （根据PRD截图 `image_FjLobW20loc0yVx64afcpujenMe`）学生头像/标识、总得分/评价（如100%）、操作按钮（点赞、去处理）。
        - **Tab切换区:** "答题结果"、"学生提问"（根据任务类型可能还有"学习报告"）。
        - **"答题结果"Tab下:**
            - **统计区:** "共XX道题，XX道错题"（针对该学生）。
            - **题目列表操作区:** 搜索题目关键词、只看错题（学生自己的错题）、筛选。
            - **题目列表区 (学生个人版):**
                - 题干预览、选项。
                - 学生作答状态: ✅ (正确), ❌ (错误), 半对符号, "! 尚未作答，暂无结果"。
                - 得分率 (如100%)。
                - "查看"按钮。
        - **"学生提问"Tab下:** (逻辑类似班级报告的学生提问Tab，但只显示该学生的提问)
            - 按课程内容结构展示学生在该节点下的提问数量及内容。
    - **用户操作与反馈:**
        - **切换Tab:** 内容更新为该学生在该Tab下的数据。
        - **题目列表操作:** 与班级报告的"答题结果"Tab类似，但数据范围是个人。
        - **点击"查看"题目:** 跳转到该题的详情页，包含题目信息、学生答案、正确答案、解析。
    - **数据展示与更新:** 所有数据均为该学生在本次作业中的个人数据。
- **优先级：** 高 (P0)
- **依赖关系：** 依赖班级作业报告或其它学生入口。
- **Mermaid图示 (主要交互):**
  ```mermaid
  flowchart TD
      A[进入学生个人作业报告] --> B[显示学生与作业基本信息];
      B --> C[默认显示'答题结果'Tab];
      C --> D[显示学生个人答题统计];
      D --> E[显示学生个人题目列表及作答状态];
      E --> F{筛选/搜索题目?};
      F -- 是 --> G[更新题目列表];
      F -- 否 --> H;
      H{点击题目'查看'?};
      H -- 是 --> I[跳转到题目详情页(含学生答案)];
      H -- 否 --> E;
      C --> J[切换到'学生提问'Tab];
      J --> K[显示学生个人提问记录];
  ```

#### 2.1.6 核心功能点6：题目详情页查看
- **简洁描述该功能是什么:** 教师在班级报告的"答题结果"Tab或学生个人报告的"答题结果"Tab中，点击某道题的"查看"按钮后，进入该题目的详情页面。页面展示题目基本信息、答案解析，以及作答统计（班级视角）或学生个人作答情况（学生视角）。
- **用户价值与场景:**
    - **用户场景:**
        - 教师想深入了解某道共性错题的错误选项分布、学生具体作答情况，以便准备讲解。
        - 教师想查看某个学生具体某道题的答案，以便进行针对性辅导。
    - **用户痛点:** 仅看题目列表的统计数据不够深入，无法了解学生具体的思考和错误原因。
    - **核心价值:** 提供单题维度的深度分析，辅助教师进行精准的题目讲解和个体辅导。
- **功能详述与前端呈现:** (参照原始PRD中 `image_CLqrb4W3XoPbH4x4xlUcGgfMnuf` 的分析)
    - **主要交互流程:**
        1. 教师从题目列表点击"查看"。
        2. 进入题目详情页。
        3. 页面展示题干、选项、题型、难度、标签。
        4. 教师可展开查看答案与解析。
        5. **班级视角下:** 展示该题的整体作答统计（正答率、各选项选择人数、答对/错人数、平均用时），并列出选择各选项的学生名单。
        6. **学生个人视角下:** 展示该题的题干、选项、答案解析，以及该学生选择的答案和对错情况。
        7. （班级视角）提供"大屏讲解"快捷操作。
    - **界面布局与元素:**
        - **页面标题:** "答题结果详情"或题目编号。
        - **快捷操作:** （班级视角）"大屏讲解"按钮。
        - **题目信息区:**
            - 题型（如单选题）、难度、知识点标签。
            - 题干内容（含图片）。
            - 选项内容。
        - **答案解析区:** 默认收起或部分显示，可展开查看完整答案和解析。
        - **作答统计区 (班级视角):**
            - 正答率。
            - 答对人数、答错人数。
            - 平均用时。
            - 各选项选择人数及百分比，并可查看选择该选项的学生列表（可点击展开或跳转）。
        - **学生作答区 (学生个人视角):**
            - 学生选择的答案。
            - 作答结果（正确/错误）。
    - **用户操作与反馈:**
        - **展开/收起答案解析:** 内容区动态变化。
        - **点击"大屏讲解":** 可能将当前题目投屏或进入特殊讲解模式。
        - **查看选择某选项的学生列表:** 弹出学生名单或跳转到新页面。
    - **数据展示与更新:** 数据为静态加载，展示特定题目的信息。
- **优先级：** 高 (P0，因为是答题结果分析的延伸)
- **依赖关系：** 依赖于班级或学生个人作业报告的"答题结果"Tab。
- **Mermaid图示 (页面信息结构):**
  ```mermaid
  graph TD
      A[题目详情页] --> B[题目基本信息];
      B --> B1[题干与选项];
      B --> B2[题型/难度/标签];
      A --> C[答案与解析 (可展开)];
      
      subgraph "班级视角下额外内容"
          A --> D[作答统计];
          D --> D1[正答率];
          D --> D2[答对/错人数];
          D --> D3[平均用时];
          D --> D4[各选项选择人数与学生列表];
          A --> E[大屏讲解按钮];
      end

      subgraph "学生个人视角下额外内容"
          A --> F[学生作答信息];
          F --> F1[学生所选答案];
          F --> F2[作答对错];
      end
  ```

#### 2.1.7 核心功能点7：策略驱动的智能提醒与建议
- **简洁描述该功能是什么:** 系统根据预设策略（鼓励、关注、共性错题），在作业报告的相关页面（如学生报告Tab、学生个人详情页）自动识别并高亮表现特殊（优秀或需改进）的学生或题目，并向教师提供相应的干预建议或快捷操作。
- **用户价值与场景:**
    - **用户场景:** 教师希望系统能辅助其快速发现需要表扬或需要特别辅导的学生，以及普遍性问题。
    - **用户痛点:** 人工筛选效率低，容易遗漏；缺乏标准化的判断依据。
    - **核心价值:** 提升教学干预的及时性和精准性，减轻教师负担，促进个性化教学。
- **功能详述与前端呈现:**
    - **主要逻辑 (非单一页面功能，而是融入多个页面):**
        - **鼓励策略:** (原始PRD "策略" -> "鼓励"部分)
            - 条件: 任务提前完成、比上次任务提升10%以上、高于同层同学10%以上、达到个人/班级最多连对题数、达到个人最多提问数、开启提问。
            - 触发: 系统满足"初始化阈值"（如历史数据量要求）后，自动匹配。
            - 前端呈现:
                - 在"学生报告Tab"的"建议鼓励学生"区域展示命中鼓励标签的学生列表（如周雨彤、刘明宇等）。
                - 学生列表中，对符合鼓励条件的学生显示特殊标签（如"有进步"、"连对王者"）。
                - 进入学生个人详情页（如 `image_Y4CxbxKsroMQpexKRv9cgNpDnze`），展示具体命中的鼓励标签（如"值得鼓励的进步"）、详细诊断文字、建议干预措施（如"班级课上表扬"、"线上点赞鼓励"），并提供快捷操作（"点赞鼓励"、"线下沟通"、"Push提醒"及预设文案）。
        - **关注策略:** (原始PRD "策略" -> "关注"部分)
            - 条件: 班级进度50%仍未学习、逾期未完成/完成、与历史/同层/任务内课程偏离20%以上。
            - 触发: 系统自动匹配。
            - 前端呈现:
                - 在"学生报告Tab"的"建议关注学生"区域展示命中关注标签的学生列表（如李子涵）。
                - 学生列表中，对符合关注条件的学生显示特殊标签（如"需关注"）。
                - 进入学生个人详情页（如 `image_GUMxbygcDoiiVexnWgFcLQTqnSg`），展示具体命中的关注原因（如"需要关注的问题"）、详细诊断文字、建议干预措施（如"安排课后一对一辅导"），并提供快捷操作（"发布关注"、"线下沟通"、"Push提醒"及预设文案）。
        - **共性错题策略:** (原始PRD "策略" -> "共性错题"部分)
            - 条件: P0默认"答题人数>10人，任务中正确率＜60%"。P1支持自定义。
            - 前端呈现:
                - 在任务卡片上可能显示"待关注题目"数量。
                - 作业报告详情页顶部显示"待关注题目"数量。
                - "答题结果Tab"中，统计"XX道共性错题"，并提供"只看共性错题"筛选。符合条件的题目在列表中可能用特殊标记高亮。
    - **界面布局与元素:** 已在上述各功能点中描述。核心是标签、列表、诊断性文字和快捷操作按钮的智能展示。
    - **用户操作与反馈:** 教师通过查看这些智能提醒，可以快速决策并执行干预操作。如点击"一键表扬"，系统后台处理并可能通知学生。
    - **数据展示与更新:** 智能提醒和建议根据学生最新数据动态生成或更新。
- **优先级：** 高 (P0)
- **依赖关系：** 依赖于学生行为数据收集、作业数据统计以及预设的策略规则库。
- **Mermaid图示 (策略触发流程概览):**
  ```mermaid
  flowchart TD
      A[学生作业数据/学习行为数据输入] --> B{数据分析引擎};
      B -- 匹配鼓励策略 --> C[生成鼓励建议];
      C --> C1[在'学生报告Tab'展示'建议鼓励学生'];
      C --> C2[在'学生个人详情页'展示具体鼓励内容与操作];
      
      B -- 匹配关注策略 --> D[生成关注建议];
      D --> D1[在'学生报告Tab'展示'建议关注学生'];
      D --> D2[在'学生个人详情页'展示具体关注内容与操作];

      B -- 匹配共性错题策略 --> E[识别共性错题];
      E --> E1[在'答题结果Tab'高亮/筛选共性错题];
      E --> E2[在报告概览处提示共性错题数量];
  ```

### 2.2 辅助功能
- 暂无明确定义的P0辅助功能，多数已融入核心功能描述中（如筛选、搜索等）。

### 2.3 非本期功能
- **提醒设置自定义 (P1):** 允许用户自定义鼓励、关注、共性错题的提醒规则和阈值。
- **大屏模式 (未来功能):** 针对测验等场景的特殊展示模式。
- **非选择题有分数批改与统计 (未来):** 当前P0期题目详情中，非选择题的作答统计主要针对无分数或选择题类型。

## 3. 用户场景与故事

### 场景1：教师快速概览与筛选教学任务
作为一个 **学科老师**，我希望能够 **快速查看我布置和有权限查看的所有教学任务（课程、作业、资源、测验），并能通过类型、学科、班级、日期等条件进行筛选和搜索**，这样我就可以 **高效地管理我的教学任务，并快速定位到需要处理或查看报告的特定任务**。目前的痛点是 **任务多起来后，找一个特定的任务很费时，也无法直观了解任务的整体状态**，如果能够 **提供清晰的列表和便捷的筛选搜索功能**，对我的工作效率会有很大帮助。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 教师打开教师端应用，进入"作业"或类似模块。
    * **界面变化:** 默认显示"作业"类型的任务列表，卡片式展示，包含任务名称、班级、截止日期、完成率等关键信息。顶部有内容类型切换Tab（全部、课程、作业、资源、测验）和筛选区域（学科、班级、日期等）。
    * **即时反馈:** 列表加载动画，加载完成后显示任务卡片。
2.  **用户操作2:** 教师点击顶部的"课程"Tab。
    * **界面变化:** 任务列表内容更新为"课程"类型的任务，筛选条件保持或重置（根据设计）。"课程"Tab高亮。
    * **即时反馈:** 列表异步刷新，显示课程任务卡片。
3.  **用户操作3:** 教师点击"学科"筛选器，选择"数学"。
    * **界面变化:** 任务列表内容根据当前Tab类型（如"课程"）和"数学"学科进行过滤刷新。筛选器上显示已选"数学"。
    * **即时反馈:** 列表异步刷新。
4.  **用户操作4:** 教师在搜索框输入"函数的最值"。
    * **界面变化:** 任务列表根据已有筛选条件和搜索关键词"函数的最值"进行模糊匹配任务名称后刷新。
    * **即时反馈:** 列表异步刷新，仅显示匹配的任务。
5.  **用户操作5:** 教师找到目标任务卡片，点击卡片。
    * **界面变化:** 页面跳转到该任务的"作业报告详情页"。
    * **即时反馈:** 页面切换动画。

**场景细节补充：**
* **前置条件:** 教师已登录，并拥有相应班级和学科的教学任务数据。
* **期望结果:** 教师能够高效地找到并访问所需的教学任务及其报告。
* **异常与边界情况:**
    * 网络请求失败时，应有友好提示并允许重试。
    * 筛选结果为空时，应明确提示"暂无符合条件的任务"。
    * 搜索结果过多时，支持分页或滚动加载。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (用户流程/交互):**
```mermaid
flowchart TD
    subgraph '用户场景：教师快速概览与筛选教学任务'
        A[教师进入作业/任务模块] --> B[默认显示作业列表];
        B --> C{选择内容类型Tab?};
        C -- 是 --> D[列表更新为对应类型任务];
        C -- 否 --> E;
        D --> E;
        E{使用筛选器 (学科/班级/日期)?};
        E -- 是 --> F[列表根据筛选条件更新];
        E -- 否 --> G;
        F --> G;
        G{输入搜索关键词?};
        G -- 是 --> H[列表根据搜索结果更新];
        G -- 否 --> I;
        H --> I;
        I{找到目标任务卡片?};
        I -- 点击卡片 --> J[跳转到作业报告详情页];
        I -- 继续筛选/搜索 --> C;
    end
```

### 场景2：教师查看班级整体作业报告并识别重点学生
作为一个 **班主任或学科老师**，我希望能够 **在进入某次作业的报告后，直观地看到班级整体的完成进度、平均正确率、平均用时，并快速识别出哪些学生表现"值得表扬"，哪些学生"需要关注"**，这样我就可以 **及时了解班级学情，并对不同表现的学生采取相应的激励或辅导措施**。目前的痛点是 **需要手动对比大量数据才能发现问题学生和优秀学生，效率较低**，如果能够 **系统自动汇总关键指标并推荐重点学生群体**，将极大提升我的工作效率。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 教师从作业列表点击进入某份作业的"作业报告详情页"，默认进入"学生报告"Tab。
    * **界面变化:** 页面顶部显示作业名称、发布/截止时间、班级进度、待关注题目数。中间区域显示"值得表扬"学生头像/姓名列表（可滑动）和"一键表扬"按钮，"需要关注"学生头像/姓名列表（可滑动）和"一键提醒"按钮。下方显示班级平均正确率、班级平均用时。再下方是详细的学生数据列表。
    * **即时反馈:** 页面加载动画，数据显示后，进度条（如有）动态填充。
2.  **用户操作2:** 教师查看"值得表扬"学生列表，并点击"一键表扬"按钮。
    * **界面变化:** "一键表扬"按钮可能变为"表扬中..."或显示加载动画。
    * **即时反馈:** 操作成功后，Toast提示"表扬已发送"或类似信息。按钮恢复原状或变为"已表扬"状态。
3.  **用户操作3:** 教师在学生列表中，通过姓名搜索"李明"。
    * **界面变化:** 学生列表实时筛选，仅显示包含"李明"的学生数据。
    * **即时反馈:** 列表内容动态变化。
4.  **用户操作4:** 教师在学生列表中找到"李明"，其数据显示为"需关注"（例如：完成进度60%，正确率50%），点击该行右侧的"查看详情"按钮。
    * **界面变化:** 页面跳转到"李明"的个人作业报告详情页。
    * **即时反馈:** 页面切换。

**场景细节补充：**
* **前置条件:** 教师已进入特定作业的"学生报告"Tab。学生作业数据已统计完成。
* **期望结果:** 教师能够快速掌握班级学情，并对重点学生进行初步干预或深入分析。
* **异常与边界情况:**
    * 若无学生符合"值得表扬"或"需要关注"的条件，对应区域应提示"暂无"或不显示。
    * "一键表扬/提醒"操作时若网络失败，应提示操作失败并允许重试。
    * 学生列表数据量大时，应支持分页或虚拟滚动，确保性能。

**优先级：** 高
**依赖关系：** 依赖作业列表功能。

**Mermaid图示 (用户流程/交互):**
```mermaid
flowchart TD
    subgraph '用户场景：教师查看班级整体作业报告并识别重点学生'
        A[进入作业报告'学生报告'Tab] --> B[查看作业信息和班级概览];
        B --> C[查看'值得表扬'学生列表];
        C --> C1{点击'一键表扬'?};
        C1 -- 是 --> C2[发送表扬];
        C2 --> D;
        C1 -- 否 --> D;
        D[查看'需要关注'学生列表];
        D --> D1{点击'一键提醒'?};
        D1 -- 是 --> D2[发送提醒];
        D2 --> E;
        D1 -- 否 --> E;
        E[查看班级平均数据];
        E --> F[浏览/搜索学生列表];
        F --> F1{点击某学生'查看详情'?};
        F1 -- 是 --> G[跳转到该学生个人报告];
        F1 -- 否 --> F;
    end
```

### 场景3：教师分析班级作业的题目作答情况
作为一个 **学科老师**，我希望能够 **在作业报告中切换到"答题结果"视图，查看所有题目的正确率、错误人数、作答人数等统计，并能快速筛选出"共性错题"或按关键词搜索特定题目**，这样我就可以 **准确了解学生在哪些知识点或题型上存在普遍困难，以便进行针对性的课堂讲解或调整教学策略**。目前的痛点是 **需要手动统计每道题的对错情况，耗时且易出错**，如果能够 **自动汇总题目维度的统计数据并高亮共性问题**，将非常有帮助。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 教师在某份作业的报告详情页，点击切换到"答题结果"Tab。
    * **界面变化:** 页面内容更新。顶部显示该作业"共XX道题，XX道共性错题"及排序方式。下方为题目搜索框、筛选条件（"只看共性错题"开关、"全部题型"下拉框）。再下方是题目列表，每条题目显示题干预览、正确率、错误人数、作答人数和"查看"、"加入"按钮。
    * **即时反馈:** Tab选中状态切换，内容区异步加载题目数据。
2.  **用户操作2:** 教师勾选"只看共性错题"开关。
    * **界面变化:** 题目列表刷新，只显示符合共性错题标准的题目。
    * **即时反馈:** 开关状态改变，列表异步更新。
3.  **用户操作3:** 教师在题目搜索框输入关键词"定义域"。
    * **界面变化:** 题目列表（在当前筛选条件下）根据关键词"定义域"模糊匹配题干、选项或解析后刷新。
    * **即时反馈:** 列表异步更新。
4.  **用户操作4:** 教师找到一道错误率较高的题目，点击其右侧的"查看"按钮。
    * **界面变化:** 页面跳转到该题目的"答题结果详情页"，展示题目完整信息、答案解析、班级作答统计（如选项分布）等。
    * **即时反馈:** 页面切换。

**场景细节补充：**
* **前置条件:** 教师已进入特定作业的"答题结果"Tab。学生作业数据已统计完成。
* **期望结果:** 教师能够高效分析题目作答情况，定位教学难点。
* **异常与边界情况:**
    * 若无题目符合"共性错题"条件，列表应提示"暂无共性错题"。
    * 搜索无结果时，应提示"未找到相关题目"。
    * 题目内容过长时，应能折叠和展开。

**优先级：** 高
**依赖关系：** 依赖作业报告主体功能。

**Mermaid图示 (用户流程/交互):**
```mermaid
flowchart TD
    subgraph '用户场景：教师分析班级作业的题目作答情况'
        A[进入作业报告'答题结果'Tab] --> B[查看题目整体统计];
        B --> C[查看题目列表];
        C --> D{使用筛选控件 (共性错题/题型)?};
        D -- 是 --> E[题目列表按筛选更新];
        D -- 否 --> F;
        E --> F;
        F{输入关键词搜索题目?};
        F -- 是 --> G[题目列表按搜索结果更新];
        F -- 否 --> H;
        G --> H;
        H{点击某题目'查看'按钮?};
        H -- 是 --> I[跳转到题目详情页];
        H -- 否 --> C; %% 返回或继续浏览列表
    end
```

### 场景4：教师查看单个学生的详细作业报告
作为一个 **学科老师**，我希望能够 **查看特定学生（如"周雨彤"）在某份作业（如"3.2.2函数的最值"）中的详细作答情况，包括每道题的对错、得分，并能方便地查看其提问记录**，这样我就可以 **全面了解该学生的学习薄弱环节和疑问点，为个性化辅导提供依据**。目前的痛点是 **分散查看学生的不同维度信息较为繁琐**，如果能在一个报告中 **集中呈现学生的答题详情和提问记录**，将更为便捷。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 教师从班级作业报告的学生列表中，点击学生"周雨彤"的"查看详情"入口。
    * **界面变化:** 页面跳转到"周雨彤 - 3.2.2函数的最值 作业报告"。顶部显示学生姓名、作业名、作业整体信息（发布/截止/完成时间、课程范围、完成进度80%）。下方有Tab切换（"答题结果"、"学生提问"）。默认显示"答题结果"Tab。
    * **即时反馈:** 页面加载，显示学生个人报告。
2.  **用户操作2:** 在"答题结果"Tab下，教师浏览题目列表，看到题目"12395"学生作答"正确"，得分率"100%"；题目"13035"学生"尚未作答"。
    * **界面变化:** 题目列表展示了每道题学生个人的作答状态和结果。
    * **即时反馈:** 无特殊即时反馈，为静态信息展示。
3.  **用户操作3:** 教师点击"学生提问"Tab。
    * **界面变化:** 内容区切换，展示该学生在此作业关联的课程内容中提出的问题列表，按课程结构（如幻灯片顺序）组织，并显示提问内容和时间。
    * **即时反馈:** Tab状态切换，异步加载并显示学生提问数据。
4.  **用户操作4:** 教师点击某个提问，查看提问详情及可能的AI回复或教师互动记录。
    * **界面变化:** 展开提问详情或跳转到提问详情页面。
    * **即时反馈:** 加载并显示详细内容。

**场景细节补充：**
* **前置条件:** 教师已进入学生个人作业报告页面。
* **期望结果:** 教师能够全面了解单个学生在一次作业中的表现和疑问。
* **异常与边界情况:**
    * 若学生某Tab下无数据（如未提问），对应区域应提示"暂无相关内容"。
    * 题目或提问内容过长时，应支持展开查看全部。

**优先级：** 高
**依赖关系：** 依赖班级作业报告或其它学生入口。

**Mermaid图示 (用户流程/交互):**
```mermaid
flowchart TD
    subgraph '用户场景：教师查看单个学生的详细作业报告'
        A[进入学生'周雨彤'的作业报告页] --> B[默认显示'答题结果'Tab];
        B --> C[查看周雨彤的题目列表及作答状态];
        C --> C1{点击某题'查看'?};
        C1 -- 是 --> C2[跳转到题目详情(周雨彤的答案)];
        C1 -- 否 --> D;
        D[点击'学生提问'Tab];
        D --> E[显示周雨彤的提问列表];
        E --> E1{点击某条提问?};
        E1 -- 是 --> E2[查看提问详情];
        E1 -- 否 --> E;
    end
```

## 4. 业务流程图

```mermaid
graph LR
    subgraph "任务发起与执行"
        ST["学科老师"] -- 布置任务 --> TaskType["任务类型: 课程/作业/测验/资源"];
        TaskType -- 下发给 --> S["学生"];
        S -- "接收任务" --> S_Receive["收到: 课程/作业/测验/资源任务"];
        S_Receive -- "查看素材" --> S_ViewMaterial["查看: XX课程/作业任务卡等"];
        S_ViewMaterial -- "用户行为" --> S_Action["用户行为: 看课/答题/看视频/提问等"];
        S_Action -- "系统记录" --> AutoFinishStatus{"自动完成状态"};
    end

    subgraph "报告生成与查阅"
        AutoFinishStatus -- 生成数据 --> ReportData["作业报告数据"];

        ReportData -- "按任务/素材(班级整体)" --> ST_ReportView_Class["学科老师查看班级作业报告"];
        ST_ReportView_Class --> ST_Tabs_Class["学生报告Tab
答题结果Tab
学生提问Tab"];
        ST_Tabs_Class -- "查看个体详情" --> ST_ReportView_Student["学科老师查看学生个人作业报告"];
         ST_ReportView_Student --> ST_Tabs_Student["答题结果Tab (个人)
学生提问Tab (个人)"];

        ST_ReportView_Class -- 操作 --> ST_Action_Class["批改 / push消息 (班级/分群)"];
        ST_Action_Class -- 消息 --> S;
        
        ST_ReportView_Student -- 操作 --> ST_Action_Student["push消息 (个人) / 记录干预"];
        ST_Action_Student -- 消息 --> S;


        ReportData -- "按班级" --> CT["班主任"];
        CT -- 查看报告 --> CT_Metrics["进度
正确率
用时
共性错题
作答分布
提问分布"];
        CT -- 操作 --> CT_Push["push消息"];
        CT_Push -- 消息 --> S;

        ReportData -- "按个体(年级视角)" --> SH["学科主任"];
        SH -- 查看报告 --> SH_Metrics["进度
用时
看错题
提问历史"];
        SH -- 操作 --> SH_Action["建议鼓励/关注学生
push消息"];
        SH_Action -- "鼓励/关注/消息" --> S;

        ReportData -- "关键信息推送" --> P["校长"];
        P -- "接收消息" --> P_ReceiveMsg["接收push消息"];
    end
```

## 5. 性能与安全需求
*(根据原始PRD及工作指南，若无明确信息则忽略此部分)*
本期PRD主要关注功能性需求，性能与安全需求遵循通用标准和平台规范。具体指标待后续专项讨论。

## 6. 验收标准
*(根据原始PRD及工作指南，若无明确信息则忽略此部分。原始PRD中"口径说明"链接无法访问)*
本期PRD的验收标准将基于各核心功能点及用户场景的描述，确保前端展示与交互符合设计，数据准确，核心流程跑通。具体量化指标和详细测试用例待后续测试阶段明确。

## 7. 其他需求
*(根据原始PRD及工作指南，若无明确信息则忽略此部分)*

### 7.1 可用性与体验需求
- **界面友好与直观性：**
    - 核心操作路径应简洁明了，符合教师用户使用习惯。
    - 图标、标签、提示信息清晰易懂，避免歧义。
    - 整体视觉风格保持一致性，重要信息和操作有明确的视觉引导。
    - 移动端界面应充分考虑触摸操作的便捷性，按钮、链接点击区域大小适宜。
- **容错处理：**
    - 用户进行敏感操作（如删除任务、一键提醒）前，应有二次确认提示。
    - 网络请求失败或超时，应给出友好提示，并允许用户重试。
    - 无数据或空状态时，界面应有明确提示，避免空白。
- **前端性能感知：**
    - 列表加载、筛选、搜索等操作应有加载状态提示（如骨架屏、loading动画），避免界面卡顿或无响应。
    - 大量数据（如学生列表、题目列表）展示时，应采用分页或虚拟滚动等技术优化性能。
- **交互一致性：**
    - 产品内相似功能（如筛选、搜索、Tab切换）的交互模式应保持一致。
    - 按钮样式、弹窗风格等应统一。

### 7.2 维护性需求
- **配置管理：**
    - 共性错题的判断标准（如答题人数阈值、正确率阈值）应支持后台配置（P1需求）。
    - 鼓励与关注策略的触发条件和阈值，应支持后台配置（P1需求）。
- **监控告警：**
    - 关键接口的调用成功率、耗时应纳入监控。
    - 前端JS错误应上报至监控系统。
- **日志管理：**
    - 记录关键用户操作日志（如查看报告、执行表扬/提醒、导出数据）。
    - 记录重要API的请求与响应（脱敏后），辅助问题排查。

## 8. 用户反馈和迭代计划
*(根据原始PRD及工作指南，若无明确信息则忽略此部分)*

### 8.1 用户反馈机制
-   用户（教师）可以通过产品内的反馈入口或运营支持渠道提交使用问题、意见和建议。
-   定期收集用户反馈，用于产品迭代优化。

### 8.2 迭代计划
-   P0核心功能计划于6月上线，跑通MVP流程。
-   9月完成全部核心功能。
-   后续迭代将根据用户反馈和业务发展，持续优化体验，并规划P1及未来功能。

## 9. 需求检查清单

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| -------------------------------- | ----------------------------------- | ------ | ------ | ------ | ------------------------- | --------------------------- | ----------- | ---- |
| **作业列表 (P0)** |  |  |  |  |  |  |  |  |
| 1. 数据范围：展示当前老师布置的、有权限看的全部作业 (取并集) | 2.1.1 核心功能点1：作业列表展示与管理 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始图：`image_XXEmb0Gf6o2gl8xiCogc1tewnsc`, `in_table_image_GSs0bDnxPomvPlxUP2ac1IKinJe` |
| 2. 筛选：全部类型、查看班级、学科、布置日期 | 2.1.1 核心功能点1：作业列表展示与管理 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_NCQkbWefmoYZ6JxwYtzcQGepnMh`, `in_table_image_ZqNFb5BlvoUGZZx5WOjcCRJmnZL` |
| 3. 搜索：按任务名称模糊匹配 | 2.1.1 核心功能点1：作业列表展示与管理 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_Sj3wb50N3otvfKx11KxcWJCynnW` |
| 4. 卡片指标：区分课程、作业/测验、资源任务 | 2.1.1 核心功能点1：作业列表展示与管理 (功能详述-界面布局与元素-任务卡片) | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         |  |
| 5. 卡片操作：编辑、删除、复制 (仅本人布置) | 2.1.1 核心功能点1：作业列表展示与管理 (功能详述-界面布局与元素-任务卡片) | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         |  |
| **作业报告 (P0)** |  |  |  |  |  |  |  |  |
| 1. 学生报告Tab：页面名称返回、任务详情筛选、Tab切换、建议鼓励/关注学生、班级进度/正确率、学生列表及异常飘色 | 2.1.2 核心功能点2：班级整体作业报告查看 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始图：`image_PswKbOca7ouLbDxJzFScXHognOc`, `in_table_image_Cd4kbLOXYo3T08xNlWccCmmenye`等 |
| 2. 答题结果Tab：数据展示逻辑(区分任务类型)、题目统计(共性错题解释)、搜索题目、题目信息展示(正确率等定义)、侧边题目面板 | 2.1.3 核心功能点3：班级作业报告 - 答题结果Tab | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始图：`image_Kde9bV1SroJspDxtJQeccHOCneg`, `image_QbOCbI3ssoaDfKxamzIcZbB8nWd`等 |
| 3. 学生提问Tab：展示有评论的素材(AI课)、按课程幻灯片顺序展示内容及评论数 | 2.1.4 核心功能点4：班级作业报告 - 学生提问Tab | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始图：`image_CMoSbVPqcoznYTxrEPec4qDQnce`, `image_An8wbFUSLo9yJjxSrPicif6ynbg`等 |
| **xx学生的作业报告 (P0)** |  |  |  |  |  |  |  |  |
| 1. 来源入口、页面名称返回、答题概览(时间、进度、素材范围) | 2.1.5 核心功能点5：单个学生作业报告查看 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始图：`image_FjLobW20loc0yVx64afcpujenMe`, `image_OvxrbrH5zop670xgEdickNnBn6b`等 |
| 2. 答题结果Tab(学生个人)：题目统计、题目信息展示(个人对错)、题目面板 | 2.1.5 核心功能点5：单个学生作业报告查看 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始图：`image_I4hibE2j6o3Kr4xlH1DcE0A3nag`, `in_table_image_XT3tbg5KZoLgUixkle4csF4nn8u`等 |
| 3. 学生提问Tab(学生个人)：展示个人评论的素材、按课程幻灯片顺序展示内容及个人评论数 | 2.1.5 核心功能点5：单个学生作业报告查看 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_Mn2obNTTAoNIktxmxcFcV5r7nvf`, `in_table_image_SqYxbKRGRoaiIGxQEhlcAyK9nZd`等 |
| **题目详情页 (P0)** |  |  |  |  |  |  |  |  |
| 1. 班级视角：题目基本信息(题干选项、标签、答案解析)、作答统计(选择题/判断题) | 2.1.6 核心功能点6：题目详情页查看 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始图：`image_CLqrb4W3XoPbH4x4xlUcGgfMnuf`, `in_table_image_YOe7bcJuXofS8YxeykIcT3LEn0g`等 |
| 2. 学生个人视角：题目基本信息、作答统计(仅本人数据) | 2.1.6 核心功能点6：题目详情页查看 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         |  |
| **策略说明 (P0)** |  |  |  |  |  |  |  |  |
| 1. 鼓励策略 (系统建议) | 2.1.7 核心功能点7：策略驱动的智能提醒与建议 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始PRD"策略"章节 |
| 2. 关注策略 (系统建议) | 2.1.7 核心功能点7：策略驱动的智能提醒与建议 | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始PRD"策略"章节 |
| 3. 共性错题策略 | 2.1.7 核心功能点7：策略驱动的智能提醒与建议 (及2.1.3) | [✅]    | [✅]    | [✅]    | [⚠️] (定性描述)      | [✅] (P0)                 | [✅]         | 原始PRD"策略"章节 |

-   **完整性：** 原始需求是否都有对应的优化后需求点，无遗漏。
-   **正确性：** 优化后需求描述是否准确表达了原始需求的意图。
-   **一致性：** 优化后需求之间是否有冲突或重复。
-   **可验证性：** 优化后需求是否有明确的验收标准（对应7. 验收标准）。(本期定性描述为主)
-   **可跟踪性：** 优化后需求是否有明确的优先级和依赖关系。
-   **UI/UX明确性：** 优化后需求是否清晰描述了前端界面、交互和用户体验细节。

✅表示通过；❌表示未通过；⚠️表示描述正确，但UI/UX细节、验收标准或图示等仍需与PM进一步沟通完善；N/A表示不适用。

## 11. 附录

### 11.1 原型图/设计稿链接
-   UI图: [https://www.figma.com/design/d7RUo4uZ7uwRpb1Enf0Sng/%E9%93%B6%E6%B2%B3-%E6%95%99%E5%B8%88?node-id=11-4149&p=f&t=MkljbBY4IkwHZuo7-0](https://www.figma.com/design/d7RUo4uZ7uwRpb1Enf0Sng/%E9%93%B6%E6%B2%B3-%E6%95%99%E5%B8%88?node-id=11-4149&p=f&t=MkljbBY4IkwHZuo7-0)
-   (其他低保真线框图或关键页面截图，可在此补充)

### 11.2 术语表
-   **共性错题:** 指在一定范围内（如班级、特定人数以上作答）答错比例较高的题目。P0阶段定义为"答题人数>10人，任务中正确率＜60%"。
-   **学习分:** (学生端策略可能变化) 用于衡量学生学习效果或投入的综合性评分。
-   **飘色:** 在列表中，对偏离均值较大的异常数据进行特殊颜色标记，以引起教师注意。
-   **Push提醒:** 通过系统向学生端App发送的通知消息。
-   **AI课:** 一种包含人机交互、智能反馈的在线课程形式，支持学生在学习过程中提问。

### 11.3 参考资料
-   原始需求文档：`documents/raw-docs/教师端_1期_作业报告_analyzed.md`
-   本PRD遵循的工作指南：`documents/prompt/fe-gen-ai-prd.md`
-   本PRD使用的模板：`documents/templates/fe-ai-prd-v2.2.md`
-   口径说明文档 (外部链接，内容未直接应用): `https://wcng60ba718p.feishu.cn/wiki/DXKawJOESiypQ3kt3vbc64DgnTc?sheet=e3d974`
-   教师端数据埋点汇总 (外部链接，内容未直接应用): `https://wcng60ba718p.feishu.cn/wiki/Pw1GwQgfEi7TsPk3dgrcysMDnR0?sheet=ceLy7M`

---
等待您的命令，指挥官 
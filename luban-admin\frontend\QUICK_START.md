# 快速启动指南

## 当前可用命令

```bash
npm run dev      # 启动开发服务器
npm run build    # 构建生产版本
npm run start    # 启动生产服务器
npm run clean    # 清理缓存
```

## 启动测试环境 🆕

### 方式1：使用专用脚本（推荐）
```bash
start-test.bat
```
这会自动：
- 设置 NODE_ENV=test
- 使用测试环境API：http://test-api.xiaoluxue.com
- 在3001端口启动

### 方式2：手动设置
```bash
# Windows CMD
set NODE_ENV=test
set NEXT_PUBLIC_API_HOST=http://test-api.xiaoluxue.com
set PORT=3001
npm run dev

# Windows PowerShell
$env:NODE_ENV="test"
$env:NEXT_PUBLIC_API_HOST="http://test-api.xiaoluxue.com"
$env:PORT="3001"
npm run dev
```

### 方式3：使用 start.bat
```bash
start.bat
# 选择选项 3（测试环境）
```

## 切换环境的正确方式

### 1. 使用 start.bat（Windows 推荐）✅

```bash
start.bat
```
然后选择：
- 1 = 本地API (localhost:8000)
- 2 = 远程API (*************:8000)
- 3 = 测试环境
- 4 = 自定义端口
- 5 = 默认配置

### 2. 手动设置环境变量 ✅

**连接远程API：**
```bash
# Windows CMD
set NEXT_PUBLIC_API_HOST=http://*************:8000
set NEXT_PUBLIC_USE_REMOTE_API=true
npm run dev

# Windows PowerShell
$env:NEXT_PUBLIC_API_HOST="http://*************:8000"
$env:NEXT_PUBLIC_USE_REMOTE_API="true"
npm run dev
```

**使用本地API（默认）：**
```bash
npm run dev
```

**自定义端口：**
```bash
# Windows CMD
set PORT=3001
npm run dev
```

### 3. 创建 .env.local 文件 ✅

创建 `luban-design/luban-admin/frontend/.env.local`：
```env
# 远程API配置
NEXT_PUBLIC_API_HOST=http://*************:8000
NEXT_PUBLIC_USE_REMOTE_API=true

# 或本地API配置
# NEXT_PUBLIC_API_HOST=http://localhost:8000
# NEXT_PUBLIC_USE_REMOTE_API=false

# 自定义端口
# PORT=3001
```

然后直接运行：
```bash
npm run dev
```

## 为什么移除了 dev:local、dev:remote 等命令？

这些命令依赖于 `cross-env` 包，但：
1. 增加了不必要的依赖
2. Windows 用户经常遇到问题
3. 使用环境变量或 start.bat 更简单直接

## 验证配置

启动后会看到：
```
✅ Next.js API 代理配置: http://localhost:8000
- Local: http://localhost:3000
```

如果看到不同的地址，说明您的配置生效了。

## 常见问题

**Q: 为什么显示端口 3003？**
A: 您可能在 .env.local 中设置了 PORT=3003

**Q: 如何切换回本地API？**
A: 删除 .env.local 或注释掉相关配置，然后重启

**Q: 配置没生效？**
A: 重启开发服务器（Ctrl+C 然后重新 npm run dev）
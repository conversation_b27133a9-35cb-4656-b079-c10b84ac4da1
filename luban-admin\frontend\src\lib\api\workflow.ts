import { api } from './client';
import { 
  Workflow, 
  WorkflowSummary, 
  CreateWorkflowRequest, 
  UpdateWorkflowRequest, 
  WorkflowStep
} from '@/types/workflow';

// 获取所有工作流
export const getAllWorkflows = async (): Promise<WorkflowSummary[]> => {
  return api.get<WorkflowSummary[]>('/workflows');
};

// 获取指定工作流详情
export const getWorkflow = async (id: string): Promise<Workflow> => {
  return api.get<Workflow>(`/workflows/${id}`);
};

// 创建工作流
export const createWorkflow = async (data: CreateWorkflowRequest): Promise<Workflow> => {
  return api.post<Workflow>('/workflows', data);
};

// 更新工作流
export const updateWorkflow = async (
  id: string,
  data: UpdateWorkflowRequest
): Promise<Workflow> => {
  return api.put<Workflow>(`/workflows/${id}`, data);
};

// 删除工作流
export const deleteWorkflow = async (id: string): Promise<{ message: string }> => {
  return api.delete<{ message: string }>(`/workflows/${id}`);
};

// 启动工作流
export const startWorkflow = async (id: string): Promise<Workflow> => {
  return api.post<Workflow>(`/workflows/${id}/start`, {});
};

// 暂停工作流
export const pauseWorkflow = async (id: string): Promise<Workflow> => {
  return api.post<Workflow>(`/workflows/${id}/pause`, {});
};

// 恢复工作流
export const resumeWorkflow = async (id: string): Promise<Workflow> => {
  return api.post<Workflow>(`/workflows/${id}/resume`, {});
};

// 取消工作流
export const cancelWorkflow = async (id: string): Promise<Workflow> => {
  return api.post<Workflow>(`/workflows/${id}/cancel`, {});
};

// 获取工作流步骤内容
export const getWorkflowStepContent = async (
  workflowId: string,
  step: WorkflowStep
): Promise<{ content: string }> => {
  return api.get<{ content: string }>(`/workflows/${workflowId}/${step}/content`);
};

// 更新工作流步骤内容
export const updateWorkflowStepContent = async (
  workflowId: string,
  step: WorkflowStep,
  content: string
): Promise<{ message: string }> => {
  return api.put<{ message: string }>(`/workflows/${workflowId}/${step}/content`, { content });
};

// 下一步
export const nextWorkflowStep = async (
  workflowId: string
): Promise<Workflow> => {
  return api.post<Workflow>(`/workflows/${workflowId}/next`, {});
};

// 上一步
export const prevWorkflowStep = async (
  workflowId: string
): Promise<Workflow> => {
  return api.post<Workflow>(`/workflows/${workflowId}/prev`, {});
};

export const workflowApi = {
  getAllWorkflows,
  getWorkflow,
  createWorkflow,
  updateWorkflow,
  deleteWorkflow,
  startWorkflow,
  pauseWorkflow,
  resumeWorkflow,
  cancelWorkflow,
  getWorkflowStepContent,
  updateWorkflowStepContent,
  nextWorkflowStep,
  prevWorkflowStep,
}; 
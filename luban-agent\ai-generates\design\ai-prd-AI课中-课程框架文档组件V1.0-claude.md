# 产品需求文档：AI课中-课程框架文档组件 v1.0

## 1. 需求背景与目标

### 1.1 需求目标
- 通过课程框架和文档组件的优化与能力扩展，提升AI课程的学习体验和效果
- 解决前期demo版本中的用户体验问题，包括板书内容过多、高亮时机与讲解不匹配、双击播放暂停交互缺失等
- 打造更接近1v1真人直播课的沉浸式课堂体验，提升系统的个性化和智能化感知
- 该需求上线后，预计可以提升学习效果和课程满意度，为后续积分体系、小组战队、互动讲题等丰富玩法奠定基础

## 2. 功能范围

### 2.1 核心功能
- 课程框架升级: 优化开场页与结算页设计，提供清晰的学习流程和成果展示
  - 优先级：高
  - 依赖关系：无
- 课程进度管理: 支持用户在一节课的组件中自由切换进度，保存答题记录
  - 优先级：高
  - 依赖关系：无
- 文档组件增强: 提供更流畅的内容展示和交互体验，包括同步勾画轨迹和快捷操作
  - 优先级：高
  - 依赖关系：无
- 退出/继续课程: 支持用户暂时退出课程并记录进度，再次进入时从断点继续
  - 优先级：中
  - 依赖关系：无

### 2.2 辅助功能
- 课程反馈: 提供多层次评价体系，收集学生对课程体验的感受和建议
- 学习表现统计: 展示学习用时、答题数量、正确率等关键指标
- 推荐学习: 根据学生掌握度情况智能推荐后续学习内容

### 2.3 非本期功能
- 积分体系和小组战队: 建议在后续版本中引入，增强学习动力和社交体验
- 互动讲题: 建议在后续版本中支持自动化逐步讲解，并加入互动问题
- 文档长按评论: 建议在后续版本中增加，提升社区互动感
- 费曼组件和互动组件: 建议在后续版本中引入，丰富学习方式

## 3. 计算规则与公式

### 3.1 掌握度计算
- 外化掌握度计算方式详见PRD-掌握度策略-V1.0
- 掌握度完成进度计算公式:
  - 掌握度完成进度 = 用户当前掌握度 / 目标掌握度
    - 参数说明:
      - 用户当前掌握度: 用户实际掌握程度
      - 目标掌握度: 用户设定的学习目标
    - 规则:
      - 取值范围: [0, 1]，保留小数点后两位
      - 初始掌握度不用于进度计算，用户首次学习的掌握度变化 = 本次学习后的进度 - 0
      - 如果用户本次学习后掌握度下降，此处展示的掌握度不变化

### 3.2 动效展示策略
- 动效选择规则:
  - 正确率 > 70%: 展示"开心动效"，掌握度提升
  - 正确率 ≤ 70%: 展示"鼓励动效"，掌握度不变
    - 规则: 外化掌握度不会出现下降情况，当实际的知识点掌握下降时，外化掌握度不变

### 3.3 反馈文案策略
- 反馈文案分类:
  - 掌握度相关: 如"掌握度提升"、"达成目标掌握度"
  - 正确率相关: 如"正确率100%"、"成绩优秀"、"正确率0%"
  - 时长相关: 如"学习时长超过预计时长50%"
  - 其他情况: 如"完成学习"
- 文案展示规则:
  - 文案包括"主文案"、"副文案"两部分内容
  - 文案内容和IP相关，每个IP维护一套反馈文案
  - 如果同时命中两种情况，则随机选择其中的一种情况
  - 一种情况下有多条文案时，随机选择一条展示

## 4. 用户场景与故事

### 4.1 课程开始与结束体验优化
作为一名学生，我希望在开始课程时能感受到学习的仪式感，结束时能清晰了解自己的学习成果，这样我就可以更有动力投入学习。目前的痛点是课程的开始和结束体验较为简单，缺乏激励性。

**关键步骤：**
1. 学生进入课程，看到专为该学科设计的开场页，建立学习仪式感
2. 完成课程后，进入结算页查看自己的学习表现
3. 系统根据学习表现展示相应的IP动效和鼓励文案
4. 学生可以查看学习用时、答题数、正确率、掌握度等关键指标
5. 系统智能推荐后续学习内容，引导用户持续学习

```mermaid
graph TD
    A[用户进入课程] --> B[展示开场页]
    B --> C[课程学习]
    C --> D[完成全部内容]
    D --> E[进入结算页]
    E --> F{正确率>70%?}
    F -->|是| G[展示开心动效]
    F -->|否| H[展示鼓励动效]
    G --> I[展示学习表现]
    H --> I
    I --> J[展示掌握度及变化]
    J --> K[推荐后续学习内容]
    K --> L[课程反馈收集]
    L --> M[返回课程入口/完成学习]
```

- 优先级：高
- 依赖关系：无

### 4.2 文档组件学习体验优化
作为一名学生，我希望在学习文档内容时能够更灵活地控制学习进度和方式，这样我就可以根据自己的理解速度灵活调整。目前的痛点是交互不够直观，难以快速调整播放进度和速度。

**关键步骤：**
1. 学生进入文档组件，默认处于跟随模式
2. 数字人视频、勾画轨迹和JS动画内容同步播放
3. 学生可以通过双击切换播放/暂停状态
4. 学生可以通过长按进行3倍速播放
5. 学生可以向上滑动进入自由模式，自主浏览内容
6. 在自由模式下，学生可点击任意位置从该处继续学习

```mermaid
graph TD
    A[进入文档组件] --> B[默认进入跟随模式]
    B --> C{用户操作?}
    C -->|向上滑动| D[切换至自由模式]
    C -->|双击| E[播放/暂停切换]
    C -->|长按| F[3倍速播放]
    C -->|点击操作面板| G[调整播放速度/显示字幕]
    D --> H{自由模式下操作?}
    H -->|点击内容| I[显示'从这里学'按钮]
    H -->|双击| J[播放/暂停切换]
    H -->|长按| K[3倍速播放]
    I --> L[点击'从这里学']
    L --> M[从选定位置开始播放]
    M --> B
```

- 优先级：高
- 依赖关系：无

### 4.3 课程进度自由切换
作为一名学生，我希望能在一节课的不同组件间自由切换，这样我就可以随时复习或重点学习某部分内容。目前的痛点是课程进度较为线性，难以灵活跳转到已学习的部分。

**关键步骤：**
1. 学生点击课程进度按钮，展示当前课程的全部组件
2. 系统展示每个组件的解锁状态（已解锁、学习中、待解锁）
3. 学生点击已解锁的组件，直接跳转到对应内容
4. 跳转到之前学过的练习组件时，系统直接展示历史答题记录
5. 学生完成当前组件后，自动进入课程中的下一个组件

```mermaid
graph TD
    A[用户点击课程进度按钮] --> B[展示课程组件列表]
    B --> C{选择已解锁组件?}
    C -->|是| D[跳转至选定组件]
    C -->|否| E[提示组件未解锁]
    D --> F{组件类型?}
    F -->|文档组件| G[从上次位置或开头播放]
    F -->|练习组件| H{题目状态?}
    H -->|已完成| I[展示历史答题记录]
    H -->|未完成| J[从未完成题目继续]
    G --> K[完成当前组件]
    I --> K
    J --> K
    K --> L[自动进入下一组件]
```

- 优先级：中
- 依赖关系：无

### 4.4 退出与继续学习体验
作为一名学生，我希望在暂时需要离开学习时能保存我的学习进度，这样我就可以在下次继续学习时无缝衔接。目前的痛点是中断学习可能导致进度丢失，增加重复学习成本。

**关键步骤：**
1. 学生在学习过程中点击左上角的退出按钮
2. 系统记录用户当前的学习进度和状态
3. 学生再次进入课程时，系统引导其从上次退出的位置继续
4. 不同组件类型有不同的进度恢复策略

```mermaid
graph TD
    A[用户点击退出按钮] --> B[系统记录当前进度]
    B --> C[返回课程入口页]
    C --> D[用户再次进入课程]
    D --> E{退出前的组件类型?}
    E -->|文档组件| F[从退出时间点继续播放]
    E -->|练习组件| G[进入退出时的题目]
    F --> H[继续学习]
    G --> H
```

- 优先级：中
- 依赖关系：无

## 5. 业务流程图

```mermaid
flowchart TD
    A[单节课内容] --> B["课程开场页 (功能页面)"]
    B --> C["课程引入 (文档组件, 答疑组件)"]
    C --> D["知识点1 (文档组件, 答疑组件)"]
    D --> E["知识点2 (文档组件, 答疑组件)"]
    E --> F["练习1 (练习组件, 答疑组件)"]
    F --> G["知识点3 (文档组件, 答疑组件)"]
    G --> H["知识点4 (文档组件, 答疑组件)"]
    H --> I["练习2 (练习组件, 答疑组件)"]
    I --> J["课程总结 (文档组件, 答疑组件)"]
    J --> K["学习报告 (功能页面)"]
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：文档组件各项交互操作响应时间不超过200ms
- 加载时间：课程组件切换加载时间不超过1秒
- 流畅度：3倍速播放时保持视频、勾画轨迹与JS内容的同步，无明显卡顿
- 兼容性：支持主流iOS和Android设备，确保跨设备体验一致

### 6.2 安全需求
- 数据同步：课程进度和答题记录需实时保存到服务端
- 用户隐私：课程反馈信息需匿名处理并加密传输
- 内容安全：课程内容需加密存储，防止未授权访问
- 数据完整性：断网情况下保存本地数据，网络恢复后同步至服务端

## 7. 验收标准

### 7.1 功能验收
- 课程框架升级：
  - 使用场景：用户进入课程和完成课程
  - 期望结果：开场页正常展示并自动进入第一个组件；结算页正确展示学习表现数据
- 文档组件增强：
  - 使用场景：用户在文档组件中学习
  - 期望结果：勾画轨迹与内容同步播放；双击可切换播放/暂停；长按可3倍速播放
- 课程进度管理：
  - 使用场景：用户点击课程进度按钮
  - 期望结果：正确展示所有组件状态；可点击已解锁组件直接跳转；练习组件展示历史答题记录

### 7.2 性能验收
- 响应时间：
  - 双击切换播放/暂停响应时间不超过200ms
  - 倍速切换响应时间不超过300ms
- 加载时间：
  - 组件间切换加载时间不超过1秒
  - 课程开场页加载时间不超过2秒
- 流畅度：
  - 3倍速播放时，视频、勾画轨迹与JS内容保持同步，帧率不低于24fps

### 7.3 安全验收
- 数据同步：
  - 退出课程后再次进入，正确恢复到上次学习位置
  - 断网状态下记录本地数据，网络恢复后自动同步
- 用户隐私：
  - 用户反馈数据传输采用加密方式
  - 答题记录仅对用户本人可见

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：文档组件在跟随模式下自动高亮当前讲解内容，未播放区域呈现模糊/弱化效果
- 容错处理：视频加载失败时提供重试选项，避免用户被迫退出课程
- 兼容性：支持iOS 11+和Android 7.0+系统，保证核心功能正常运行

### 8.2 维护性需求
- 配置管理：开场页和结算页的IP动效、文案等可由管理员在后台配置
- 监控告警：建立课程组件加载失败、视频播放异常等关键指标监控
- 日志管理：记录用户学习轨迹、交互行为等数据，用于产品优化分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 结算页提供五级评价机制（极差、较差、一般、满意、很棒）
- 根据评价等级展示不同的反馈收集项，针对性了解用户痛点
- 定期分析反馈数据，识别高频问题和改进方向

### 9.2 迭代计划
- 迭代周期：每6周一个迭代
- 第二迭代：引入文档长按评论功能，增强社区互动性
- 第三迭代：引入积分体系和小组战队，提升学习动力
- 第四迭代：增加互动讲题和费曼组件，丰富学习方式

## 10. 需求检查清单

| 原始需求                                                       | 对应需求点                                                        | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注                           |
| -------------------------------------------------------------- | ----------------------------------------------------------------- | ------ | ------ | ------ | -------- | -------- | ------------------------------ |
| 课程开场页优化                                                 | 2.1 课程框架升级、4.1 课程开始与结束体验优化                      | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 结算页增加掌握度、答题情况、推荐学习信息                       | 2.1 课程框架升级、4.1 课程开始与结束体验优化                      | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 结算页鼓励文案展示规则调整                                     | 3.2 动效展示策略、3.3 反馈文案策略                                | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 课程进度保存用户答题记录                                        | 2.1 课程进度管理、4.3 课程进度自由切换                            | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 增加勾画轨迹与JS内容同步播放                                   | 2.1 文档组件增强、4.2 文档组件学习体验优化                        | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 未播放区域呈现模糊/弱化效果                                     | 8.1 可用性需求                                                    | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 默认展示字幕                                                   | 2.1 文档组件增强、4.2 文档组件学习体验优化                        | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 增加双击、长按等快捷交互                                       | 2.1 文档组件增强、4.2 文档组件学习体验优化                        | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 增加1.75和3倍速                                                | 2.1 文档组件增强、4.2 文档组件学习体验优化                        | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 增加前进/后退10s                                               | 2.1 文档组件增强、4.2 文档组件学习体验优化                        | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 跟随模式与自由模式切换                                         | 4.2 文档组件学习体验优化                                          | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 退出课程记录进度，返回课程入口页                               | 2.1 退出/继续课程、4.4 退出与继续学习体验                         | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 退出后再次进入课程的进度策略                                   | 4.4 退出与继续学习体验                                            | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 课程反馈五级评价机制                                           | 2.2 辅助功能、9.1 用户反馈机制                                    | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 学习表现统计（学习用时、答题数、正确率）                        | 2.2 辅助功能、4.1 课程开始与结束体验优化                          | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 掌握度计算与展示                                               | 3.1 掌握度计算                                                    | ✅      | ✅      | ✅      | ✅        | ✅        | 详细计算规则参考掌握度策略文档 |
| 推荐学习策略                                                   | 2.2 辅助功能、4.1 课程开始与结束体验优化                          | ✅      | ✅      | ✅      | ✅        | ✅        |                                |
| 文档组件中单击文档内容展示"从这里学"                            | 4.2 文档组件学习体验优化                                          | ✅      | ✅      | ✅      | ✅        | ✅        |                                |

- 完整性：原始需求是否都有对应的优化后需求点，无遗漏
- 正确性：优化后需求描述是否准确表达了原始需求的意图
- 一致性：优化后需求之间是否有冲突或重复  
- 可验证性：优化后需求是否有明确的验收标准
- 可跟踪性：优化后需求是否有明确的优先级和依赖关系

## 11. 附录

### 11.1 术语表
- 文档组件：课程内容为JS文档形式的组件，支持学生在上课过程中自由浏览和提问
- 练习组件：支持各类题型的练习环节，可按策略推送题目
- 答疑组件：基于大模型能力和学生进行实时对话，解答学生问题
- 掌握度：反映学生对课程内容的理解和掌握程度的量化指标
- 跟随模式：数字人视频、勾画轨迹和JS动画内容自动播放，三个内容间的进度保持一致的模式
- 自由模式：学生可自主浏览内容，不受视频播放进度限制的模式

### 11.3 参考资料
- PRD - 掌握度策略 - V1.0
- AI课中需求盘点
- Demo V1.0 & V1.1功能文档 
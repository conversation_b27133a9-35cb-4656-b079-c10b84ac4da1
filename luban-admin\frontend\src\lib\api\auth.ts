import { AuthToken, LoginRequest, User } from '@/types/user';
import { api, setToken, clearToken, hasToken } from './client';

export const authApi = {
  /**
   * 用户登录
   */
  login: async (credentials: LoginRequest): Promise<AuthToken> => {
    const formData = new URLSearchParams();
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);
    
    // 使用application/x-www-form-urlencoded格式提交
    const token = await api.post<AuthToken>(
      '/auth/token', 
      formData.toString(),
      false
    );
    
    setToken(token);
    return token;
  },
  
  /**
   * 获取当前用户信息
   */
  getCurrentUser: async (): Promise<User> => {
    return api.get<User>('/auth/me');
  },
  
  /**
   * 退出登录
   */
  logout: (): void => {
    clearToken();
    // 重定向到登录页
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/login';
    }
  },
  
  /**
   * 检查是否已登录
   */
  isAuthenticated: (): boolean => {
    return hasToken();
  }
}; 
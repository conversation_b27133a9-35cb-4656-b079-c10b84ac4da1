/** @type {import('next').NextConfig} */

// API主机地址将通过环境变量 NEXT_PUBLIC_API_HOST 来提供
// Next.js 会根据当前环境（development, production）自动加载对应的 .env 文件
const API_HOST = process.env.NEXT_PUBLIC_API_HOST;

// 启动时打印配置，便于调试
if (API_HOST) {
  console.log(`✅ Next.js API 代理配置: ${API_HOST}`);
} else {
  console.error('❌ 警告: 环境变量 NEXT_PUBLIC_API_HOST 未设置，API 代理将无法工作。');
}

const nextConfig = {
  reactStrictMode: true,
  // 确保静态资源使用相对路径
  assetPrefix: '',
  // 禁用 trailing slash
  trailingSlash: false,
  // 设置基础路径
  basePath: '',
  // 添加CORS头，允许跨域请求
  async headers() {
    return [
      {
        // 匹配所有API路由
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Credentials', value: 'true' },
          { key: 'Access-Control-Allow-Origin', value: '*' }, // 在生产环境中应该限制为特定域名
          { key: 'Access-Control-Allow-Methods', value: 'GET,OPTIONS,PATCH,DELETE,POST,PUT' },
          { key: 'Access-Control-Allow-Headers', value: 'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, X-Request-Purpose' },
        ],
      },
    ];
  },
  // 完全禁用重写功能，使用 App Router 的 API 路由处理
  async rewrites() {
    return []
  },
  async redirects() {
    return []
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'static.test.xiaoluxue.cn',
        port: '',
        pathname: '/**',
      },
    ], // 允许OSS图片域名
  },
  // 强制动态渲染包含useSearchParams的页面
  experimental: {
    forceSwcTransforms: true,
    // optimizeFonts 在 Next.js 14 中已移除，不再需要此配置
  },
  // 禁用静态优化
  poweredByHeader: false,
}

module.exports = nextConfig 
'use client';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
          <div className="max-w-md w-full space-y-8 text-center">
            <h2 className="text-3xl font-bold">系统出错</h2>
            <p>很抱歉，系统遇到了问题</p>
            <p className="text-red-600">{error.message}</p>
            <button
              onClick={() => reset()}
              className="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md"
            >
              重试
            </button>
          </div>
        </div>
      </body>
    </html>
  );
} 
# 接口使用文档 - 掌握度策略系统 - V1.0

* **最后更新日期**: 2025-06-05
* **设计负责人**: AI API使用文档撰写专家
* **评审人**: 待填写
* **状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-06-05 | AI API使用文档撰写专家 | 初始草稿     |
| V1.1   | 2025-06-05 | AI API使用文档撰写专家 | 根据s5-1接口分析更新，接口路径改为learning，概念抽象化 |

## 1. 服务概述与设计目标

### 1.1 服务背景与范围
掌握度策略系统是一个智能化学习评估平台，核心目标是通过精准的掌握度计算和分析，为学生提供个性化的学习路径规划和学习效果反馈。本服务提供学科能力评估与分层、知识点掌握度实时计算、外化掌握度展示、薄弱知识点识别等核心功能，支持千万级学生用户的掌握度数据管理。

### 1.2 设计目标回顾
* 1. 提供符合 RESTful 规范的接口定义，确保开发者易于理解和使用。
* 2. 清晰定义每个接口的请求、响应、参数、数据模型和错误码。
* 3. 确保接口设计满足产品需求文档 (PRD) 中定义的核心用户场景和业务流程。
* 4. 遵循团队的API设计规范和最佳实践。
* 5. 接口定义包含完整的中文描述信息，便于理解和维护。

## 2. 通用设计规范与约定

### 2.1 数据格式
* 请求体 (Request Body): `application/json`
* 响应体 (Response Body): `application/json`
* 日期时间格式: UTC秒数时间戳 (例如: 1733472000)

### 2.2 认证与授权
* 所有接口默认需要JWT认证，在请求头中传递 `Authorization: Bearer <token>`
* 基于RBAC模型进行权限控制，学生只能访问自己的掌握度数据
* 教师需要验证授课班级权限

### 2.3 版本控制
* 通过 URL 路径进行版本控制，如 `/api/v1/learning/...`

### 2.4 错误处理约定
* 遵循统一的错误码和错误响应格式
* **通用错误响应体示例**:
  ```json
  {
    "code": 0,                // 业务错误码，0表示成功，非0表示错误
    "message": "错误信息",     // 用户可读的错误信息
    "detail": "错误详情,可选",  // 错误的详细信息，可选
    "data": null,             // 错误时通常为空
    "pageInfo": null          // 错误时通常为空
  }
  ```
* **常用 HTTP 状态码**:
  * `200 OK`: 请求成功。
  * `400 Bad Request`: 请求无效 (例如参数错误、格式错误)。
  * `401 Unauthorized`: 未认证或认证失败。
  * `403 Forbidden`: 已认证，但无权限访问。
  * `404 Not Found`: 请求的资源不存在。
  * `500 Internal Server Error`: 服务器内部错误。

### 2.6 分页与排序约定
* **分页参数**:
  * 分页通过 `page` 和 `pageSize` 查询参数控制，默认 page=1, pageSize=20
* **排序参数**:
  * 排序通过 `sortBy` 和 `order` 参数控制，如 sortBy=priority&order=asc

## 3. 接口列表

| 接口路径            | 请求方式 | 接口名称         | 接口描述               |
| -------------------| -------- | -------- | ---------------- |
| /api/v1/learning/answer-record | POST | 提交答题记录（通用） | 通用答题记录接口，支持多种学习场景的答题数据收集和学习进度更新 |
| /api/v1/learning/task/complete | POST | 完成学习任务 | 学生完成学习任务后更新学习成果展示 |
| /api/v1/learning/goal | POST | 设置学习目标 | 学生设定学习目标（V1.0暂不实现） |
| /api/v1/learning/recommendations | GET | 获取学习建议 | 智能识别和获取用户的学习建议列表 |

## 4. 场景说明

### 4.1 新学生系统初始化场景

#### 4.1.1 场景描述
新入学的学生首次使用系统时，需要基于其考试成绩和学校信息进行能力分层，并为每个知识点设置个性化的初始掌握度，确保后续学习推荐的准确性。

#### 4.1.2 初始化流程
1. 新学生注册并登录系统
2. 系统收集学生基础信息（成绩排名、学校信息）
3. 系统计算校准系数和能力分层
4. 系统为每个知识点设置个性化初始掌握度
5. 初始化完成，学生可以开始正常学习

#### 4.1.3 涉及接口
此场景主要通过系统内部初始化流程完成，不直接暴露API接口。初始化完成后，学生可以通过其他接口进行正常的学习活动。

#### 4.1.4 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 新学生
    participant Frontend as 前端应用
    participant MasteryService as 掌握度策略服务
    participant UserCenter as 用户中心服务
    participant ContentPlatform as 内容平台服务

    Student->>Frontend: 首次登录系统
    Frontend->>MasteryService: 检查用户掌握度初始化状态
    MasteryService->>UserCenter: 获取学生基础信息（成绩、学校）
    UserCenter-->>MasteryService: 返回学生信息和学校数据
    MasteryService->>MasteryService: 计算校准系数和能力分层
    MasteryService->>ContentPlatform: 获取知识点列表
    ContentPlatform-->>MasteryService: 返回知识点信息
    MasteryService->>MasteryService: 批量设置初始掌握度
    MasteryService-->>Frontend: 返回初始化完成状态
    Frontend-->>Student: 显示初始化完成，可开始学习
```

### 4.2 学生日常答题学习场景

#### 4.2.1 场景描述
学生在各种学习场景中答题后，系统需要实时更新学习进度，识别需要重点关注的知识点并推荐学习内容。这是系统的核心使用场景。

#### 4.2.2 答题学习流程
1. 学生在AI课、练习或作业中答题
2. 前端调用通用答题记录接口提交答题结果
3. 系统实时计算学习进度并更新相关数据
4. 系统判断是否满足学习成果更新条件
5. 系统识别是否需要重点关注
6. 返回更新结果和学习建议

#### 4.2.3 涉及接口
- `POST /api/v1/learning/answer-record` - 提交答题记录（通用）
- `GET /api/v1/learning/recommendations` - 获取学习建议（可选调用）

#### 4.2.4 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端应用
    participant LearningService as 学习服务
    participant ContentPlatform as 内容平台服务
    participant Cache as Redis缓存

    Student->>Frontend: 完成答题
    Frontend->>LearningService: POST /api/v1/learning/answer-record
    Note over Frontend,LearningService: 请求体包含：用户ID, 题目ID, 答题结果, 答题时间, 学习场景类型等
    
    LearningService->>Cache: 获取用户学习进度数据
    alt 缓存命中
        Cache-->>LearningService: 返回缓存数据
    else 缓存未命中
        LearningService->>LearningService: 查询数据库获取学习进度数据
        LearningService->>Cache: 更新缓存
    end
    
    LearningService->>ContentPlatform: 获取题目信息
    ContentPlatform-->>LearningService: 返回题目难度和知识点信息
    
    LearningService->>LearningService: 计算学习进度更新
    LearningService->>LearningService: 更新学习进度数据
    LearningService->>Cache: 更新缓存中的学习进度
    
    LearningService->>LearningService: 判断是否需要重点关注
    alt 识别为需要重点关注
        LearningService->>LearningService: 更新学习建议记录
    end
    
    LearningService-->>Frontend: 返回学习进度更新结果
    Note over LearningService,Frontend: 响应体包含：记录ID, 学习进度更新结果, 是否需要重点关注等
    Frontend-->>Student: 显示学习进度变化和学习建议
```

#### 4.2.5 接口使用示例

**请求示例**：
```json
POST /api/v1/learning/answer-record
Content-Type: application/json
Authorization: Bearer <token>

{
  "userId": 12345,
  "questionId": 67890,
  "knowledgePointId": 111,
  "answerResult": "correct",
  "answerTime": 45,
  "learningScenarioType": "ai_course",
  "extensionData": {
    "courseId": 456,
    "lessonId": 789
  }
}
```

**响应示例**：
```json
{
  "code": 0,
  "message": "学习记录提交成功",
  "data": {
    "recordId": "rec_123456789",
    "learningProgressUpdates": [
      {
        "knowledgePointId": 111,
        "previousProgress": 0.65,
        "currentProgress": 0.72,
        "progressIncrement": 0.07
      }
    ],
    "needsAttention": false,
    "recommendations": []
  }
}
```

### 4.3 完成学习任务场景

#### 4.3.1 场景描述
学生完成AI课程、练习或作业等学习任务后，系统需要更新学习成果。学习成果是向学生展示的学习进度指标，遵循"只增不降"的原则。

#### 4.3.2 学习任务完成流程
1. 学生完成某个学习任务（AI课、练习、作业等）
2. 前端调用学习任务完成接口
3. 系统检查是否满足学习成果更新条件
4. 系统更新学习成果（如果满足条件）
5. 返回更新结果

#### 4.3.3 涉及接口
- `POST /api/v1/learning/task/complete` - 完成学习任务

#### 4.3.4 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端应用
    participant LearningService as 学习服务
    participant ContentPlatform as 内容平台服务

    Student->>Frontend: 完成学习任务
    Frontend->>LearningService: POST /api/v1/learning/task/complete
    Note over Frontend,LearningService: 请求体包含：用户ID, 任务ID, 课程ID, 完成状态
    
    LearningService->>ContentPlatform: 获取课程配置信息
    ContentPlatform-->>LearningService: 返回课程难度和配置
    
    LearningService->>LearningService: 检查学习成果更新条件
    Note over LearningService: 条件：累计学习≥5次 或 课程完成且学习<5次 或 课程完成且无学习记录
    
    alt 满足更新条件
        LearningService->>LearningService: 计算并更新学习成果
        Note over LearningService: 学习成果 = 学习进度 / 进度上限
    else 不满足更新条件
        LearningService->>LearningService: 记录完成状态，不更新学习成果
    end
    
    LearningService-->>Frontend: 返回任务完成结果
    Note over LearningService,Frontend: 响应体包含：任务ID, 学习成果, 更新来源
    Frontend-->>Student: 显示学习进度和成果变化
```

#### 4.3.5 接口使用示例

**请求示例**：
```json
POST /api/v1/learning/task/complete
Content-Type: application/json
Authorization: Bearer <token>

{
  "userId": 12345,
  "taskId": "task_ai_course_001",
  "courseId": 456,
  "completionStatus": "completed"
}
```

**响应示例**：
```json
{
  "code": 0,
  "message": "学习任务完成",
  "data": {
    "taskId": "task_ai_course_001",
    "learningProgress": 0.85,
    "updateSource": "course_completion",
    "progressUpdated": true
  }
}
```

### 4.4 学习建议识别和查询场景

#### 4.4.1 场景描述
系统智能识别学生需要重点关注的知识点，为学生和教师提供针对性的学习建议。学习建议的生成基于学习进度分析，支持个人查询和班级统计。

#### 4.4.2 学习建议查询流程
1. 学生或教师请求查看学习建议
2. 系统根据用户权限验证请求
3. 系统查询并分析学习进度数据
4. 系统识别需要重点关注的知识点并按优先级排序
5. 返回学习建议列表和推荐内容

#### 4.4.3 涉及接口
- `GET /api/v1/learning/recommendations` - 获取学习建议

#### 4.4.4 接口调用时序图
```mermaid
sequenceDiagram
    participant User as 用户(学生/教师)
    participant Frontend as 前端应用
    participant LearningService as 学习服务
    participant ContentPlatform as 内容平台服务

    User->>Frontend: 请求查看学习建议
    Frontend->>LearningService: GET /api/v1/learning/recommendations
    Note over Frontend,LearningService: 查询参数：用户ID, 分页参数, 排序参数
    
    LearningService->>LearningService: 验证用户权限
    LearningService->>LearningService: 查询学习建议数据
    Note over LearningService: 判断条件：学习成果/目标进度≤0.7 且 学习进度≤0.5
    
    LearningService->>ContentPlatform: 获取知识点详细信息
    ContentPlatform-->>LearningService: 返回知识点名称、描述等
    
    LearningService->>LearningService: 按推荐优先级排序
    LearningService-->>Frontend: 返回学习建议列表
    Note over LearningService,Frontend: 响应体包含：推荐知识点列表, 当前进度, 目标进度, 差距比例
    Frontend-->>User: 显示学习建议和推荐内容
```

#### 4.4.5 接口使用示例

**请求示例**：
```http
GET /api/v1/learning/recommendations?userId=12345&page=1&pageSize=10&sortBy=priority&order=asc
Authorization: Bearer <token>
```

**响应示例**：
```json
{
  "code": 0,
  "message": "查询成功",
  "data": [
    {
      "knowledgePointId": 111,
      "knowledgePointName": "二次函数图像",
      "currentProgress": 0.45,
      "targetProgress": 0.70,
      "gapRatio": 0.36,
      "recommendationPriority": 1,
      "recommendedActions": [
        "巩固练习",
        "观看相关视频"
      ]
    },
    {
      "knowledgePointId": 222,
      "knowledgePointName": "三角函数性质",
      "currentProgress": 0.38,
      "targetProgress": 0.70,
      "gapRatio": 0.46,
      "recommendationPriority": 2,
      "recommendedActions": [
        "基础概念复习",
        "巩固练习"
      ]
    }
  ],
  "pageInfo": {
    "page": 1,
    "pageSize": 10,
    "total": 5,
    "totalPages": 1
  }
}
```

### 4.5 教师查看班级学习情况场景

#### 4.5.1 场景描述
教师需要查看班级学生的学习进度分布情况，包括平均学习进度、需要重点关注的知识点分析等，用于指导教学决策和调整教学策略。

#### 4.5.2 班级数据查询流程
1. 教师登录并选择要查看的班级
2. 系统验证教师的班级授课权限
3. 教师请求查看班级学习建议分析
4. 系统统计班级学习进度数据
5. 系统生成班级学习情况报告
6. 返回可视化的班级数据分析

#### 4.5.3 涉及接口
- `GET /api/v1/learning/recommendations` - 获取班级学习建议（通过班级参数）

#### 4.5.4 接口调用时序图
```mermaid
sequenceDiagram
    participant Teacher as 教师
    participant Frontend as 前端应用
    participant LearningService as 学习服务
    participant TeacherService as 教师服务
    participant AnalyticsService as 分析服务

    Teacher->>Frontend: 选择班级查看学习情况
    Frontend->>LearningService: GET /api/v1/learning/recommendations
    Note over Frontend,LearningService: 查询参数：班级ID, 学科ID, 统计维度
    
    LearningService->>TeacherService: 验证教师班级权限
    TeacherService-->>LearningService: 返回班级学生列表
    
    LearningService->>AnalyticsService: 请求班级学习进度统计
    AnalyticsService->>AnalyticsService: 查询班级学习进度数据
    AnalyticsService->>AnalyticsService: 生成学习进度分布报告
    AnalyticsService-->>LearningService: 返回统计结果
    
    LearningService->>LearningService: 识别班级需要重点关注的知识点
    LearningService-->>Frontend: 返回班级分析报告
    Note over LearningService,Frontend: 响应体包含：班级平均学习进度, 重点关注知识点统计, 学生分布
    Frontend-->>Teacher: 显示班级学习情况分析
```

## 5. 检查清单

### 5.1 PRD核心需求场景与API覆盖性检查

| PRD核心需求点/用户场景 (ID)                     | 涉及的主要API接口 (路径与方法)                                  | 关键输入数据实体/参数 (示例)                        | 关键输出数据实体/参数 (示例)                           | 场景支持度(✅/⚠️/❌) | 数据流正确性(✅/⚠️/❌) | 备注/待办事项                                   |
| :---------------------------------------------- | :-------------------------------------------------------------- | :---------------------------------------------------- | :------------------------------------------------------- | :----------------------------------------------------- | :--------------------------------------------------------- | :---------------------------------------------- |
| `新学生系统初始化（冷启动场景）`                            | `系统内部初始化流程`                                      | `用户ID, 学科ID, 成绩排名, 学校信息` | `初始化状态, 分层结果, 初始学习进度列表`    | ✅                                                     | ✅                                                         | 通过系统内部流程完成，无需暴露API接口                                                |
| `学生日常答题学习（核心使用场景）`                  | `POST /api/v1/learning/answer-record` | `用户ID, 题目ID, 答题结果, 答题时间, 学习场景类型`            | `学习进度更新结果, 新的学习进度值, 是否需要重点关注` | ✅                                                        | ✅                                                            | 核心高频接口，支持实时学习进度计算 |
| `完成学习任务并更新学习成果`        | `POST /api/v1/learning/task/complete`                                          | `用户ID, 任务ID, 课程ID, 完成状态` | `任务ID, 学习成果, 更新来源`  | ✅                                                        | ✅                                                            | 支持"只增不降"原则的学习成果更新                                                |
| `学习建议识别和推荐`   | `GET /api/v1/learning/recommendations`                                                                 | `用户ID, 分页参数, 排序参数`                                                       | `学习建议列表, 当前进度, 目标进度, 差距比例`                                                          | ✅                                                        | ✅                                                            | 智能推荐算法，支持个人和班级查询                                                |
| `教师数据驱动教学（管理场景）`   | `GET /api/v1/learning/recommendations (班级维度)`                                                                 | `班级ID, 学科ID, 统计维度`                                                       | `班级平均学习进度, 重点关注知识点统计, 学生分布`                                                          | ✅                                                        | ✅                                                            | 通过学习建议接口的班级参数实现                                                |

### 5.2 API接口数据实体与数据库支持检查

| API接口 (路径与方法)                     | 操作类型 (CRUD) | 主要数据实体 (模型名称)      | 关键数据属性 (字段名示例)                                 | 依赖的数据库表/视图 (示例)        | 数据库操作 (读/写/更新/删) | 数据可获得性/可行性(✅/⚠️/❌) | 数据一致性保障 (简述策略)                               | 备注/待办事项                                                                 |
| :--------------------------------------- | :-------------- | :--------------------------- | :---------------------------------------------------------- | :-------------------------------- | :--------------------------- | :--------------------------------------------------------- | :-------------------------------------------------------- | :---------------------------------------------------------------------------- |
| `POST /api/v1/learning/answer-record`               | Create/Update          | `AnswerRecord, LearningProgress`                       | `userId, questionId, answerResult, answerTime, learningScenarioType`  | `tbl_mastery_answer_log, tbl_mastery_knowledge_mastery`                           | 写/更新                           | ✅                                                         | `事务保证原子性，缓存更新`                                            | `高频接口，需要性能优化`                                                              |
| `POST /api/v1/learning/task/complete`             | Update            | `LearningAchievement`                       | `userId, taskId, learningProgress, updateSource`                       | `tbl_mastery_external_mastery`                           | 更新                         | ✅                                                         | `应用层事务控制`                                      | `支持"只增不降"业务规则`                                                    |
| `GET /api/v1/learning/recommendations`             | Read          | `LearningRecommendation`            | `userId, knowledgePointId, currentProgress, targetProgress, priority`               | `tbl_mastery_weak_knowledge, tbl_mastery_knowledge_mastery` | 读                           | ✅                                                        | `最终一致性`                                      | `支持分页和排序，需要索引优化`                                                    |
| `POST /api/v1/learning/goal`                   | Create/Update            | `LearningGoal` (V1.0暂不实现)             | `userId, goalType, targetProgress, subjectIds`            | `tbl_mastery_learning_goal` (预留) | 写/更新                           | N/A                                                        | `V1.0暂不实现`                                            | `为后续版本预留接口设计`                                            |

### 5.3 API通用设计规范符合性检查

| 检查项                                                                 | 状态 (✅/⚠️/❌/N/A) | 备注/说明                                                                                                |
| :--------------------------------------------------------------------- | :------------------- | :------------------------------------------------------------------------------------------------------- |
| 1. **命名规范**: 接口路径、参数、数据实体命名是否清晰、一致，并遵循团队约定？        | ✅                      | 使用RESTful风格，资源路径清晰，参数命名采用驼峰命名法                                                                 |
| 2. **HTTP方法**: GET, POST, PUT, DELETE, PATCH等HTTP方法使用是否语义恰当？        | ✅                      | GET用于查询，POST用于创建和复杂操作，符合RESTful规范                 |
| 3. **状态码**: HTTP状态码是否准确反映操作结果 (成功、客户端错误、服务端错误)？        | ✅                      | 使用标准HTTP状态码，错误情况有合适的状态码响应                                                          |
| 4. **错误处理**: 错误响应格式是否统一 (参考2.4)，错误信息是否对用户友好且包含足够调试信息？ | ✅                      | 统一的错误响应格式，包含错误码、错误信息和详细描述                                                           |
| 5. **认证与授权**: 需要保护的接口是否都正确实施了认证和授权机制 (参考2.2)？           | ✅                      | 所有接口都需要JWT认证，基于RBAC模型进行权限控制                                                                                   |
| 6. **数据格式与校验**: 请求和响应体结构是否定义清晰，数据类型是否准确？输入数据是否有必要的校验？ | ✅                      | JSON格式，数据类型明确，包含必要的参数校验                                                        |
| 7. **分页与排序**: 对于返回列表数据的接口，是否按约定提供了分页和排序功能 (参考2.6)？    | ✅                      | 薄弱知识点查询接口支持分页和按优先级排序                                                                 |
| 8. **幂等性**: 对于创建和更新操作，是否考虑了幂等性设计？ (特别是PUT, DELETE)         | ✅                      | 答题记录支持重复提交检测，学习任务完成支持重复调用                              |
| 9. **安全性**: 是否有潜在的安全风险，如敏感信息泄露、SQL注入、XSS等？             | ✅                      | 参数校验和过滤，敏感数据加密，权限验证                                             |
| 10. **性能考量**: 接口响应时间是否满足性能要求？大数据量查询是否有优化措施？             | ✅                      | 缓存策略，分页机制，索引优化，P95响应时间<2秒                                                                   |
| 11. **文档一致性**: 本文档描述是否与接口分析文档及实际实现保持一致？ | ✅                      | 严格基于接口分析文档撰写，保持100%一致性                                                           |
| 12. **可测试性**: 接口是否易于进行单元测试、集成测试和端到端测试？                    | ✅                      | 接口设计清晰，易于编写测试用例                                                                                                          |
| 13. **向后兼容性**: 未来接口变更是否考虑了向后兼容性策略？                            | N/A (新设计)    | 对于v1版本可标记为N/A，通过版本控制策略保证向后兼容                                                         |

## 6. 附录 (Appendix)

### 6.1 相关文档
* 产品需求文档 (PRD): be-s1-2-ai-prd-掌握度策略-claude-sonnet-4.md
* 技术架构设计 (TD): be-s2-1-ai-td-掌握度策略-claude-sonnet-4.md
* 数据实体设计 (DE): be-s3-1-ai-de-掌握度策略-claude-sonnet-4.md
* 数据库设计 (DD): be-s4-2-ai-dd-掌握度策略-claude-sonnet-4.md
* 接口分析 (ADA): be-s5-1-ai-ada-掌握度策略-claude-sonnet-4.md

### 6.2 核心业务规则
* **学习进度计算规则**: 基于答题结果、题目难度、用户分层等因素进行实时计算
* **学习成果原则**: 只增不降，向学生展示的学习成果不会因为答错题而下降
* **学习建议判断**: 学习成果/目标进度≤0.7 且 学习进度≤0.5
* **分层学习系数**: S+为1.2，S为1.0，A为0.8，B为0.5，C为0.4

### 6.3 性能指标
* **答题记录接口**: P95响应时间<2秒，支持每秒500次请求
* **学习建议查询**: 响应时间<1秒，支持分页查询
* **学习任务完成**: 响应时间<3秒，支持批量更新
* **并发支持**: 系统支持1000并发用户同时进行学习进度更新

### 6.4 限流策略
* **答题记录接口**: 按用户限流，每用户每秒最多10次请求
* **查询接口**: 按IP限流，每IP每分钟最多100次请求
* **批量操作**: 单次操作最多处理1000个数据项

### 6.5 缓存策略
* **用户分层信息**: Redis缓存，TTL=1小时
* **学习成果**: Redis缓存，TTL=30分钟
* **学习建议**: Redis缓存，TTL=15分钟
* **课程配置**: Redis缓存，TTL=24小时

### 6.6 监控指标
* **接口响应时间**: 监控P50、P95、P99响应时间
* **接口成功率**: 监控各接口的成功率和错误率
* **学习进度计算准确性**: 监控学习进度计算的异常情况
* **缓存命中率**: 监控各类缓存的命中率

### 6.7 错误码定义
* **0**: 成功
* **1001**: 参数错误
* **1002**: 用户未认证
* **1003**: 权限不足
* **1004**: 资源不存在
* **2001**: 学习进度计算失败
* **2002**: 学习建议识别失败
* **2003**: 学习任务状态异常
* **5000**: 系统内部错误

--- API使用文档产出完毕，等待您的命令，指挥官
# 产品需求文档 - 巩固练习相关策略 V1.0

## 1. 需求背景与目标

### 1.1 需求目标
- 通过巩固练习策略的实现，可以为不同能力水平的学生提供匹配的练习题，避免"题太难打击信心"或"题太简单浪费时间"，提升学习效率和满意度
- 该需求上线后，学生知识掌握度预计可以提升30%，练习完成率预计可以达到85%以上
- 为后续算法推荐提供结构化数据积累，便于未来迭代上线IRT、知识追踪等自动推荐机制
- 提升整体产品智能感与完成率，通过系统"主动推题、智能响应"，增强学生对产品智能化的感知

## 2. 功能范围

### 2.1 核心功能
- **题目推荐策略**: 基于预期掌握度增量的智能推题算法，满足个性化学习场景，解决题目难度不匹配的痛点
  - 优先级：高
  - 依赖关系：无
- **预估题目数量计算**: 根据知识点难度、考频系数和用户掌握度动态计算练习题量，满足精准练习量控制场景，解决练习量过多或过少的痛点
  - 优先级：高
  - 依赖关系：无
- **再练一次功能**: 针对掌握度不达标的学生提供二次练习机会，满足巩固提升场景，解决一次练习效果不佳的痛点
  - 优先级：中
  - 依赖关系：依赖于题目推荐策略
- **专注度监控与熔断**: 监控学生答题专注度并在必要时终止练习，满足学习质量保障场景，解决不认真答题影响学习效果的痛点
  - 优先级：中
  - 依赖关系：依赖于题目推荐策略

### 2.2 辅助功能
- **题目难度系数管理**: 为每道题目设定L1-L5难度等级和对应难度系数，为推荐算法提供基础数据支持
- **题组结构管理**: 管理巩固练习中的题组结构，每个题组包含必做题和非必做题，为推荐策略提供组织架构支持

### 2.3 非本期功能
- **基于IRT模型的推荐算法**: 需要大量用户答题数据支撑，建议迭代2再考虑
- **个性化学习路径规划**: 需要更复杂的知识图谱和学习轨迹分析，建议迭代3再考虑

## 3. 计算规则与公式

### 3.1 基础答题量计算
- **公式**: 基础答题量 = 10 × 难度系数 × 考频系数（四舍五入取整，范围[8,14]）
  - 参数说明：
    - 难度系数：知识点的难度系数
    - 考频系数：知识点的考试频率系数
  - 规则：
    - 取值范围：基础答题量∈[8,14]

### 3.2 动态调整系数计算
- **公式**: A = 1 + α × max(0, θ_tar - θ_cur) / θ_tar
  - 参数说明：
    - θ_tar：该知识点对应的目标掌握度
    - θ_cur：学生对该知识点的当前掌握度
    - α：经验调节系数，默认取0.6
  - 规则：
    - 取值范围：A ≥ 1

### 3.3 预期掌握度增量计算
- **公式**: P(答对) = sigmoid(4 × (θ - β))
  - 参数说明：
    - θ：学生的掌握度
    - β：题目难度系数
    - sigmoid函数：S(x) = 1/(1 + e^(-x))
  - 规则：
    - 取值范围：P(答对)∈[0,1]

- **公式**: 预期掌握度增量 = P(答对) × 答对掌握度增量 + P(答错) × 答错掌握度增量
  - 参数说明：
    - 答对掌握度增量 = 0.2 × (β - θ)
    - 答错掌握度增量 = -0.1 × (β - θ) × 0.5
  - 规则：
    - 取值范围：预期掌握度增量可为正值或负值

### 3.4 题目难度系数映射
- **规则**: 难度等级与难度系数的对应关系
  - L1 → 0.2
  - L2 → 0.4
  - L3 → 0.6
  - L4 → 0.8
  - L5 → 1.0

## 4. 用户场景与故事

### 场景1：学生首次进入巩固练习
作为一个刚完成AI课学习的学生，我希望能够获得适合我能力水平的练习题，这样我就可以有效巩固刚学到的知识点。目前的痛点是系统无法判断我的能力水平，可能推送过难或过简单的题目，如果能够基于我的掌握度智能推荐题目，对我的学习效果会有很大帮助。

**关键步骤：**
1. 系统根据学生当前掌握度和知识点目标掌握度计算预估题目数量
2. 系统按题组顺序进入，优先推荐必做题中预期掌握度增量最大的题目
3. 学生作答后系统更新掌握度并推荐下一题

```mermaid
sequenceDiagram
    participant 学生
    participant 巩固练习系统
    participant 掌握度服务
    participant 题库服务

    学生->>巩固练习系统: 进入巩固练习
    巩固练习系统->>掌握度服务: 获取当前掌握度
    掌握度服务-->>巩固练习系统: 返回掌握度数据
    巩固练习系统->>巩固练习系统: 计算预估题目数量
    巩固练习系统->>题库服务: 获取题组信息
    题库服务-->>巩固练习系统: 返回题组和题目信息
    巩固练习系统->>巩固练习系统: 选择预期掌握度增量最大的必做题
    巩固练习系统-->>学生: 推送题目
    学生->>巩固练习系统: 提交答案
    巩固练习系统->>掌握度服务: 更新掌握度
```

- 优先级：高
- 依赖关系：无

### 场景2：学生在练习过程中遇到连续错题
作为一个正在进行巩固练习的学生，我希望当我连续答错题目时系统能够及时调整推题策略，这样我就可以避免因题目过难而失去学习信心。目前的痛点是系统可能继续推送同等难度或更难的题目，如果能够在我连续答错时降低题目难度或提供适当提醒，对我的学习体验会有很大帮助。

**关键步骤：**
1. 学生连续答错多道题目
2. 系统检测到连续错误并触发专注度扣分机制
3. 系统根据专注度分值决定是否提醒或熔断练习

```mermaid
flowchart TD
    A[学生答题] --> B{答题结果}
    B -->|答错| C[专注度分值-1]
    B -->|答对| D[继续推荐题目]
    C --> E{专注度分值检查}
    E -->|分值=7| F[触发第一次提醒]
    E -->|分值=5| G[触发第二次提醒]
    E -->|分值=3| H[触发熔断，结束练习]
    E -->|分值>7| I[继续推荐题目]
    F --> I
    G --> I
    H --> J[结束巩固练习]
    I --> A
    D --> A
```

- 优先级：中
- 依赖关系：依赖于场景1

### 场景3：学生完成练习后需要再练一次
作为一个刚完成巩固练习但掌握度仍未达标的学生，我希望系统能够引导我进行再次练习，这样我就可以进一步巩固薄弱知识点。目前的痛点是不知道是否需要再次练习以及练习哪些内容，如果能够智能判断并提供针对性的再练机会，对我的学习效果会有很大帮助。

**关键步骤：**
1. 系统判断学生当前掌握度是否低于目标掌握度的70%且正确率低于70%
2. 系统检查是否有足够的可练习题目（≥3题）
3. 系统为学生提供再练一次的入口和针对性题目

```mermaid
flowchart TD
    A[完成巩固练习] --> B{掌握度<目标掌握度70% 且 正确率<70%}
    B -->|是| C[筛选有错题的题组]
    B -->|否| D[不显示再练入口]
    C --> E{可用题目数≥3}
    E -->|是| F[显示再练一次入口]
    E -->|否| G[不显示再练入口]
    F --> H[学生选择再练一次]
    H --> I[优先推送错题中预期掌握度增量最大的题目]
    I --> J[继续推荐其他高价值题目]
```

- 优先级：中
- 依赖关系：依赖于场景1和场景2

## 5. 业务流程图

### 5.1 巩固练习整体推题流程

```mermaid
flowchart TD
    S[巩固练习推题] --> D1{本次练习是否有未作答的题组}
    D1 -->|否| E[结束]
    D1 -->|是| P1[按顺序进入题组]
    P1 --> D2{当前题目是否为题组内首题}
    D2 -->|是| D3{题组内是否有必做题}
    D2 -->|否| A2[在题组中选择预期掌握度增量最大的题目]
    D3 -->|是| A1[在必做题中选择预期掌握度增量最大的题目]
    D3 -->|否| A2
    A1 --> D4{当前题组是否已达预估答题量}
    A2 --> D4
    D4 -->|是| D1
    D4 -->|否| D5{剩余可推荐题目的预期掌握度增量是否≥0.01}
    D5 -->|是| A2
    D5 -->|否| D6{本题组是否已作答2题}
    D6 -->|是| D1
    D6 -->|否| A2
```

### 5.2 再练一次推题流程

```mermaid
flowchart TD
    A[再练一次推题] --> B[筛选巩固练习环节有错题的题组]
    B --> C{本次练习是否有未作答的题组}
    C -->|否| D[结束]
    C -->|是| E[按顺序进入题组]
    E --> F[在错题中选择预期掌握度增量最大的题目]
    F --> G{当前题组是否已达预估答题量}
    G -->|是| I[在题组中选择预期掌握度增量最大的题目]
    I --> G
    G -->|否| H{剩余可推荐题目的预期掌握度增量是否≥0.01}
    H -->|是| I
    H -->|否| C
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：题目推荐的响应时间不超过2秒
- 并发用户：系统需支持1000个并发用户同时进行巩固练习，保证性能不衰减
- 数据量：系统需支持100万条题目记录的存储和查询，响应时间不超过1秒

### 6.2 安全需求
- 权限控制：题目推荐策略配置需要管理员权限才能访问
- 数据加密：学生答题记录需要使用AES256算法进行加密存储和传输
- 操作审计：对推题策略调整需要记录操作日志，包括操作人、操作时间、操作内容

## 7. 验收标准

### 7.1 功能验收
- **题目推荐策略**：
  - 使用场景：当学生进入巩固练习且题组内有必做题时
  - 期望结果：系统应优先推荐必做题中预期掌握度增量最大的题目
- **预估题目数量计算**：
  - 使用场景：当学生开始巩固练习时
  - 期望结果：系统应根据公式正确计算出预估题目数量，且在合理范围内
- **再练一次功能**：
  - 使用场景：当学生完成练习且掌握度<目标掌握度70%且正确率<70%时
  - 期望结果：系统应显示再练一次入口并提供针对性题目
- **专注度监控与熔断**：
  - 使用场景：当学生连续答错或快速答题时
  - 期望结果：系统应正确扣减专注度分值并在分值为3时熔断练习

### 7.2 性能验收
- 响应时间：
  - 给定正常网络条件，当执行题目推荐，响应时间不超过2秒
  - 给定高并发场景，当1000用户同时请求，平均响应时间不超过3秒
- 并发用户：
  - 给定1000并发用户，模拟巩固练习场景，系统运行30分钟，无异常，平均响应时间不超过3秒

### 7.3 安全验收
- 权限控制：
  - 给定未授权用户，当访问推题策略配置，返回权限错误
  - 给定授权管理员，当访问推题策略配置，可以正常使用
- 数据加密：
  - 给定查询学生答题记录，数据是加密状态，且使用AES256算法
- 操作审计：
  - 给定执行推题策略调整，操作日志正常记录，包含操作人、操作时间、操作内容

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：巩固练习的操作界面简洁友好，3步内可完成主要操作
- 容错处理：网络异常发生时，系统可以本地缓存题目，并给出友好提示
- 兼容性：需支持Chrome/Safari/移动端等主流环境，保证界面和功能正常

### 8.2 维护性需求
- 配置管理：推题策略参数可由系统管理员在后台界面进行配置和调整
- 监控告警：推题异常发生时，系统自动告警，通知相关技术人员
- 日志管理：系统自动记录推题日志，日志保存30天，可供系统管理员查询和分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 用户可以通过练习结束后的反馈页面和客服渠道提交反馈
- 每周定期收集和分析用户反馈，识别推题策略改进点

### 9.2 迭代计划
- 迭代周期：每2周
- 每次迭代结束后，评估推题效果和用户反馈，调整下一个迭代的策略优化重点

## 10. 需求检查清单

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
|----------|------------|--------|--------|--------|----------|----------|------|
| 基于预期掌握度增量的题目推荐策略 | 题目推荐策略核心功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 根据知识点难度和考频计算基础答题量 | 预估题目数量计算功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 根据用户掌握度计算动态调整系数 | 预估题目数量计算功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题组内必做题优先推荐机制 | 题目推荐策略核心功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 连续两题间难度限制规则 | 题目推荐策略核心功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 大题推荐时的题量控制 | 题目推荐策略核心功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次判断条件和推题策略 | 再练一次功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 专注度评分和熔断机制 | 专注度监控与熔断功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目难度系数L1-L5映射关系 | 题目难度系数管理辅助功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题组结构设计（必做题和非必做题） | 题组结构管理辅助功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 预期掌握度增量计算公式 | 计算规则与公式 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 巩固练习整体推题流程 | 业务流程图 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次推题流程 | 业务流程图 | ✅ | ✅ | ✅ | ✅ | ✅ | |

## 11. 附录

### 11.1 原型图
[此处应附上巩固练习界面的原型图或界面设计图]

### 11.2 术语表
- **掌握度**: 学生对特定知识点的掌握程度，取值范围[0,1]
- **预期掌握度增量**: 学生完成某道题目后预期的掌握度提升量
- **题组**: 巩固练习中按题型或知识点组织的题目集合
- **必做题**: 题组中被标记为推荐优先级的题目
- **难度系数**: 题目难度的量化指标，对应L1-L5等级
- **熔断**: 当检测到学生学习状态不佳时主动终止练习的机制
- **专注度**: 评估学生答题认真程度的指标

### 11.3 参考资料
- PRD - 掌握度 - V1.0
- 原始需求文档：Format-PRD-巩固练习相关策略-V1.0.md 
const HeroSection = () => {
  return (
    <section className="bg-gradient-to-r from-blue-50 to-indigo-50 py-20">
      <div className="container mx-auto px-4">
        <div className="flex flex-col-reverse lg:flex-row items-center">
          <div className="w-full lg:w-1/2 mt-10 lg:mt-0">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-2">
              鲁班 | Technical Design Agent
            </h1>
            <h2 className="text-xl md:text-2xl font-medium text-blue-600 mb-6">
              银河研发团队 - 智能研发助手
            </h2>
            <p className="text-xl text-gray-600 mb-8 max-w-lg">
              基于AI的智能代码分析与文档生成工具，提升开发效率，简化工作流程，让开发更轻松。
            </p>
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <a href="/workflow/new" className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg text-center font-medium transition-colors">
                开始使用
              </a>
              <a href="#features" className="bg-white hover:bg-gray-50 text-blue-600 border border-blue-200 px-6 py-3 rounded-lg text-center font-medium transition-colors">
                了解更多
              </a>
            </div>
          </div>
          <div className="w-full lg:w-1/2 flex justify-center lg:justify-end">
            <div className="bg-white p-2 rounded-lg shadow-lg border border-gray-100 w-full max-w-md">
              <div className="bg-gray-800 rounded-md p-2 mb-4">
                <div className="flex space-x-2 mb-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <div className="font-mono text-sm text-green-400 whitespace-pre-wrap">
                  <p>$ luban analyze</p>
                  <p className="opacity-80">正在分析代码库...</p>
                  <p className="opacity-80">正在生成文档...</p>
                  <p className="text-white mt-2">✓ 文档生成完成!</p>
                </div>
              </div>
              <div className="bg-gray-50 rounded-md p-4">
                <div className="h-4 bg-gray-300 rounded w-3/4 mb-3"></div>
                <div className="h-3 bg-gray-300 rounded w-full mb-2"></div>
                <div className="h-3 bg-gray-300 rounded w-5/6"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection; 
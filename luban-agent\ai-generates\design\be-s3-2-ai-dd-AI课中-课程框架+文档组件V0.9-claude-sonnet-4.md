# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** AI课中课程框架+文档组件产品需求文档 V0.9
- **PRD版本号：** V1.0
- **提取日期：** 2025-05-23

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 课程 | AI课程的基本信息，包含课程内容和组件序列 | 2.1, 4, 11.2 |
| 文档组件 | 课程中的文档内容播放组件，支持跟随模式和自由模式 | 2.1, 4, 11.2 |
| 练习组件 | 课程中的答题练习组件 | 2.1, 4 |
| 学习会话 | 用户单次学习课程的会话记录 | 3.1, 4, 7.1 |
| 学习表现 | 用户在课程中的表现数据，包括用时、答题数、正确率 | 3.2, 4, 7.1 |
| 掌握度记录 | 用户对知识点的掌握程度记录 | 3.1, 4, 11.2 |
| 答题记录 | 用户答题的详细记录 | 2.2, 4, 7.1 |
| 课程反馈 | 用户对课程的评价反馈 | 2.2, 4 |
| 播放进度 | 文档组件的播放进度记录 | 2.1, 4 |
| 操作日志 | 用户学习过程中的操作记录 | 6.2, 8.2 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`课程`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 课程ID | 课程的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 2.1, 4 |
| 课程名称 | 课程的标题名称 | 字符串 | 非空 |  |  | 2.1 |
| 课程信息 | 开场页展示的课程基本信息 | 文本 |  |  |  | 4, 7.1 |
| 组件序列 | 课程包含的组件列表及顺序 | JSON/文本 | 非空 |  |  | 2.1, 4 |
| 创建时间 | 课程创建时间 | 时间戳 | 非空 |  |  | 推断 |
| 更新时间 | 课程最后更新时间 | 时间戳 | 非空 |  |  | 推断 |

**实体：`文档组件`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 组件ID | 文档组件的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 2.1 |
| 课程ID | 所属课程的标识符 | 字符串/整数 | 非空 |  | 外键 | 2.1 |
| 文档内容 | 文档的具体内容 | 文本/JSON | 非空 |  |  | 2.1 |
| 勾画轨迹数据 | 与内容同步播放的板书绘制数据 | JSON | 非空 |  |  | 2.1, 11.2 |
| 字幕数据 | 默认展示的字幕内容 | JSON |  |  |  | 2.1 |
| 总时长 | 文档组件的总播放时长 | 数值型(秒) | 大于0 |  |  | 推断 |
| 组件顺序 | 在课程中的顺序位置 | 整数 | 大于0 |  |  | 2.1 |

**实体：`学习会话`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 会话ID | 学习会话的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 推断 |
| 用户ID | 学习用户的标识符 | 字符串/整数 | 非空 |  | 外键 | 4, 6.2 |
| 课程ID | 学习课程的标识符 | 字符串/整数 | 非空 |  | 外键 | 4 |
| 开始时间 | 学习开始时间 | 时间戳 | 非空 |  |  | 4, 7.1 |
| 结束时间 | 学习结束时间 | 时间戳 |  |  |  | 4, 7.1 |
| 学习状态 | 学习状态：进行中、已完成、已退出 | 枚举 | 特定值：进行中、已完成、已退出 | 进行中 |  | 2.1, 4 |
| 当前组件位置 | 当前学习到的组件位置 | 整数 | 大于等于0 | 0 |  | 2.1, 4 |
| 总用时 | 学习总用时（秒） | 数值型 | 大于等于0 | 0 |  | 3.2, 4 |

**实体：`学习表现`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 表现ID | 学习表现记录的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 推断 |
| 会话ID | 关联的学习会话标识符 | 字符串/整数 | 非空 |  | 外键 | 3.2, 4 |
| 答题数量 | 用户回答的题目总数 | 整数 | 大于等于0 | 0 |  | 3.2, 4 |
| 答对题目数量 | 用户正确回答的题目总数 | 整数 | 大于等于0 | 0 |  | 3.2, 4 |
| 正确率 | 答对的题目数量 / 答题数量 × 100% | 数值型(百分比) | 0-100 |  |  | 3.2, 4 |
| 学习用时 | 学习总用时 | 数值型(秒) | 大于等于0 | 0 |  | 3.2, 4 |

**实体：`掌握度记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 记录ID | 掌握度记录的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 推断 |
| 用户ID | 用户标识符 | 字符串/整数 | 非空 |  | 外键 | 3.1, 11.2 |
| 知识点ID | 知识点标识符 | 字符串/整数 | 非空 |  | 外键 | 3.1, 11.2 |
| 当前掌握度 | 用户当前掌握度值 | 数值型(小数) | [0,1] 区间 |  |  | 3.1, 11.2 |
| 目标掌握度 | 用户设置的学习目标掌握度 | 数值型(小数) | [0,1] 区间 | 0.8 |  | 3.1 |
| 掌握度完成进度 | 当前掌握度 / 目标掌握度 | 数值型(小数) | [0,1] 区间 |  |  | 3.1 |
| 能量值增加 | 本次学习获得的能量值 | 整数 | 大于等于0 | 0 |  | 2.2 |
| 更新时间 | 掌握度最后更新时间 | 时间戳 | 非空 |  |  | 推断 |

**实体：`答题记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 答题ID | 答题记录的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 推断 |
| 会话ID | 关联的学习会话标识符 | 字符串/整数 | 非空 |  | 外键 | 2.2, 4 |
| 题目ID | 题目标识符 | 字符串/整数 | 非空 |  | 外键 | 2.2, 4 |
| 用户答案 | 用户提交的答案 | 文本/JSON | 非空 |  |  | 2.2 |
| 是否正确 | 答题是否正确 | 布尔型 | true/false |  |  | 2.2, 3.2 |
| 答题时间 | 答题提交时间 | 时间戳 | 非空 |  |  | 2.2, 6.2 |
| 题目难度 | 题目难度系数 | 数值型 |  |  |  | 3.1 |

**实体：`课程反馈`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 反馈ID | 课程反馈的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 推断 |
| 会话ID | 关联的学习会话标识符 | 字符串/整数 | 非空 |  | 外键 | 2.2, 4 |
| 评价等级 | 5级评价反馈 | 整数 | 1-5 |  |  | 2.2 |
| 详细反馈选项 | 详细反馈选项内容 | JSON |  |  |  | 2.2 |
| 文字反馈 | 用户输入的文字反馈 | 文本 |  |  |  | 2.2 |
| 反馈时间 | 反馈提交时间 | 时间戳 | 非空 |  |  | 2.2 |

**实体：`播放进度`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 进度ID | 播放进度记录的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 推断 |
| 会话ID | 关联的学习会话标识符 | 字符串/整数 | 非空 |  | 外键 | 2.1, 4 |
| 组件ID | 文档组件标识符 | 字符串/整数 | 非空 |  | 外键 | 2.1, 4 |
| 当前播放位置 | 当前播放时间点（秒） | 数值型 | 大于等于0 | 0 |  | 2.1, 4 |
| 播放模式 | 跟随模式或自由模式 | 枚举 | 跟随模式、自由模式 | 跟随模式 |  | 2.1, 4, 11.2 |
| 播放倍速 | 当前播放倍速 | 数值型 | 0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 3.0 | 1.0 |  | 2.1 |
| 更新时间 | 进度最后更新时间 | 时间戳 | 非空 |  |  | 推断 |

**实体：`操作日志`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 日志ID | 操作日志的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 推断 |
| 用户ID | 操作用户标识符 | 字符串/整数 | 非空 |  | 外键 | 6.2, 8.2 |
| 操作类型 | 操作类型：学习进度变更、答题提交等 | 枚举 | 特定值列表 |  |  | 6.2 |
| 操作内容 | 具体操作内容描述 | 文本 | 非空 |  |  | 6.2 |
| 操作时间 | 操作发生时间 | 时间戳 | 非空 |  |  | 6.2, 8.2 |
| IP地址 | 操作来源IP | 字符串 |  |  |  | 推断 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 (例如：用户 '拥有一个' 学校档案) | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 课程包含多个文档组件 | 课程 | 文档组件 | 1:N | 文档组件.课程ID -> 课程.课程ID | 2.1, 4 |
| 用户创建学习会话学习课程 | 用户 | 学习会话 | 1:N | 学习会话.用户ID -> 用户.用户ID | 4, 6.2 |
| 学习会话关联特定课程 | 学习会话 | 课程 | N:1 | 学习会话.课程ID -> 课程.课程ID | 4 |
| 学习会话产生学习表现数据 | 学习会话 | 学习表现 | 1:1 | 学习表现.会话ID -> 学习会话.会话ID | 3.2, 4 |
| 学习会话包含多条答题记录 | 学习会话 | 答题记录 | 1:N | 答题记录.会话ID -> 学习会话.会话ID | 2.2, 4 |
| 学习会话产生课程反馈 | 学习会话 | 课程反馈 | 1:1 | 课程反馈.会话ID -> 学习会话.会话ID | 2.2, 4 |
| 学习会话记录文档组件播放进度 | 学习会话 | 播放进度 | 1:N | 播放进度.会话ID -> 学习会话.会话ID | 2.1, 4 |
| 播放进度关联文档组件 | 播放进度 | 文档组件 | N:1 | 播放进度.组件ID -> 文档组件.组件ID | 2.1, 4 |
| 用户拥有知识点掌握度记录 | 用户 | 掌握度记录 | 1:N | 掌握度记录.用户ID -> 用户.用户ID | 3.1, 11.2 |
| 答题记录关联题目 | 答题记录 | 题目 | N:1 | 答题记录.题目ID -> 题目.题目ID | 2.2 |
| 用户产生操作日志 | 用户 | 操作日志 | 1:N | 操作日志.用户ID -> 用户.用户ID | 6.2, 8.2 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 掌握度完成进度计算 | 用户当前掌握度, 目标掌握度 | 掌握度完成进度 | 3.1 |
| 正确率计算 | 答对的题目数量, 答题数量 | 正确率 | 3.2 |
| 动效选择规则 | 掌握度提升情况, 正确率 | 动效类型（开心/鼓励） | 3.3 |
| 学习表现统计 | 学习用时, 答题数量, 答对题目数量 | 学习表现数据 | 3.2, 4 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 播放进度.当前播放位置 | 用户在文档组件中播放内容时 | 实时更新 | 2.1, 4 |
| 播放进度.播放模式 | 用户滑动内容或点击跟随老师按钮时 | 实时更新 | 2.1, 4 |
| 播放进度.播放倍速 | 用户调整播放倍速时 | 实时更新 | 2.1 |
| 答题记录 | 用户每次提交答题时 | 每次答题 | 2.2, 6.2 |
| 学习表现数据 | 用户完成答题后 | 每次答题后 | 3.2 |
| 掌握度记录 | 用户完成学习任务后 | 每次完成任务 | 3.1, 11.2 |
| 学习会话.学习状态 | 用户开始学习、完成课程或退出时 | 状态变化时 | 2.1, 4 |
| 学习会话.当前组件位置 | 用户切换到下一个组件时 | 组件切换时 | 2.1, 4 |
| 课程反馈 | 用户在结算页提交反馈时 | 一次性提交 | 2.2, 4 |
| 操作日志 | 用户进行关键操作时 | 实时记录 | 6.2, 8.2 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 用户ID | 用户中心服务提供，通过身份认证获取 | 6.2 |
| 题目ID | 内容平台服务提供，题目信息包括难度、题干、答案等 | 2.2 |
| 知识点ID | 内容平台服务提供，基础树和业务树节点信息 | 3.1, 11.2 |
| 课程信息 | 系统配置或内容管理平台提供 | 2.1, 4 |
| 文档内容和勾画轨迹 | 内容制作系统提供，预制的文档和动画数据 | 2.1, 11.2 |
| 用户反馈内容 | 用户在结算页主动输入 | 2.2 |
| 目标掌握度 | 用户设置或系统默认值0.8 | 3.1 |
| IP动效和音频 | 系统预制的多媒体资源 | 4, 7.1 |

---

## 7. 当前版本明确排除的数据需求 (例如PRD中的V1.0版本)

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 (源自PRD"非本期功能"等章节) | PRD中说明的排除原因 (若有) | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 积分体系相关数据 | 建议下一迭代考虑 | 2.3 |
| 小组战队相关数据 | 建议下一迭代考虑 | 2.3 |
| 互动讲题功能相关数据 | 建议后续版本实现 | 2.3 |
| 文档长按评论功能数据 | 建议后续版本实现 | 2.3 |
| 费曼组件相关数据 | 建议后续版本实现 | 2.3 |
| 互动组件相关数据 | 建议后续版本实现 | 2.3 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别课程、文档组件、学习会话等核心实体 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏（例如，本示例中的"题目信息"、"课程信息"）？                                                         | ⚠️ | 题目信息依赖公共服务，用户信息依赖用户中心服务 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已提取所有相关属性 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | 属性描述与PRD原文一致 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 关系描述清晰准确 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 依赖关系已体现 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 计算逻辑输入项完整 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 更新机制和数据来源准确 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 排除项已正确记录 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 术语与PRD保持一致 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 所有项目都有章节引用 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 信息来源于PRD原文 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 部分技术实现细节需要进一步确认，如具体的数据格式和存储方式 |

---
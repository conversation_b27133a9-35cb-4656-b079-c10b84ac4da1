# 接口使用文档 - AI课中学习服务 - V0.9

* **最后更新日期**: 2025-05-27
* **设计负责人**: claude-sonnet-4
* **评审人**: 待填写
* **状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V0.1   | 2025-05-27 | claude-sonnet-4 | 初始草稿     |

## 1. 服务概述与设计目标

### 1.1 服务背景与范围
本服务提供AI课中学习系统的核心功能，包括课程内容管理、学习进度跟踪、文档组件交互播放、掌握度计算等功能。主要解决学生在AI课程学习过程中的内容获取、进度管理、交互控制和学习效果评估问题。

服务范围涵盖：
- 课程开场页展示和IP动效播放
- 文档组件内容获取和播放控制
- 学习会话管理和进度跟踪
- 跟随模式和自由模式切换
- 掌握度计算和外化展示
- 学习表现统计和反馈收集

### 1.2 设计目标回顾
1. 提供符合 OpenAPI 3.x 规范的接口定义 (YAML格式)，确保可直接导入 Apifox 等工具。
2. 清晰定义每个接口的请求、响应、参数、数据模型和错误码。
3. 确保接口设计满足产品需求文档 (PRD) 中定义的核心用户场景和业务流程。
4. 遵循团队的API设计规范和最佳实践。
5. 接口定义包含完整的中文描述信息，便于理解和维护。
6. 支持文档组件内容加载响应时间不超过2秒的性能要求。
7. 支持1000个并发用户同时学习的高可用性需求。

## 2. 通用设计规范与约定

### 2.1 数据格式
- 请求体 (Request Body): `application/json`
- 响应体 (Response Body): `application/json`
- 日期时间格式: ISO 8601 (`YYYY-MM-DDTHH:mm:ss.sssZ`)

### 2.2 认证与授权
- 采用Bearer Token (JWT)认证机制
- 所有需要认证的接口，在请求头中传递 `Authorization: Bearer <token>`
- Token通过用户中心服务获取，包含用户ID、权限角色等信息

### 2.3 版本控制
- 通过 URL 路径进行版本控制，如 `/api/v1/...`
- 当前版本为 v1，后续版本迭代遵循语义化版本控制

### 2.4 错误处理约定
遵循统一的错误码和错误响应格式。

**通用错误响应体示例**:
```json
{
  "code": 0,                // 业务错误码，0表示成功，非0表示错误
  "message": "错误信息",     // 用户可读的错误信息
  "detail": "错误详情,可选",  // 错误的详细信息，可选
  "data": "返回数据",        // 可选，返回的数据，错误时通常为空
  "pageInfo": "分页信息,可选"  // 可选，分页信息，错误时通常为空
}
```

**常用 HTTP 状态码**:
- `200 OK`: 请求成功。
- `400 Bad Request`: 请求无效 (例如参数错误、格式错误)。
- `401 Unauthorized`: 未认证或认证失败。
- `403 Forbidden`: 已认证，但无权限访问。
- `404 Not Found`: 请求的资源不存在。
- `500 Internal Server Error`: 服务器内部错误。

### 2.6 分页与排序约定
**分页参数**:
- 分页通常使用 page 和 pageSize 查询参数
- page: 页码，从1开始，默认值为1
- pageSize: 每页数量，默认值为20，最大值为100

**排序参数**:
- sortBy: 排序字段，默认为创建时间
- sortType: 排序方式 (asc 或 desc)，默认为desc

## 3. 接口列表

| 接口路径            | 请求方式 | 接口名称         | 接口描述               |
| -------------------| -------- | -------- | ---------------- |
| /api/v1/course/opening | GET | 获取课程开场页 | 获取课程开场页配置和IP动效 |
| /api/v1/course/component | GET | 获取组件内容 | 获取指定文档组件的详细内容和勾画轨迹 |
| /api/v1/study/session/start | POST | 开始学习会话 | 创建新的学习会话记录 |
| /api/v1/study/session/end | POST | 结束学习会话 | 结束学习会话并触发掌握度计算 |
| /api/v1/study/interaction | POST | 学习交互操作 | 处理模式切换、播放控制、进度更新等交互 |
| /api/v1/study/report | GET | 获取学习报告 | 获取学习表现、掌握度等完整报告数据 |
| /api/v1/study/feedback/submit | POST | 提交课程反馈 | 提交课程评价和反馈信息 |

## 4. 场景说明

### 4.1 学生首次进入AI课程学习场景

#### 4.1.1 学习开始流程
1. 学生通过客户端进入课程，系统验证用户身份
2. 前端调用获取课程开场页接口，获取IP动效和音频配置
3. 前端调用开始学习会话接口，创建学习记录
4. 系统自动播放开场页IP动效和音频
5. 播放完成后，前端调用获取组件内容接口，获取第一个文档组件
6. 进入文档组件学习界面，开始跟随模式播放

#### 4.1.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant StudyAPI as 学习服务API
    participant CourseCenter as 课程中心
    participant StudyCenter as 学习中心

    Student->>Frontend: 进入课程学习
    Frontend->>StudyAPI: 获取课程开场页 /api/v1/course/opening
    StudyAPI->>CourseCenter: 查询开场页配置
    CourseCenter-->>StudyAPI: 返回开场页数据
    StudyAPI-->>Frontend: 返回开场页配置
    
    Frontend->>StudyAPI: 开始学习会话 /api/v1/study/session/start
    StudyAPI->>StudyCenter: 创建学习会话
    StudyCenter-->>StudyAPI: 返回会话ID
    StudyAPI-->>Frontend: 返回会话信息
    
    Note over Frontend: 播放开场页IP动效+音频
    
    Frontend->>StudyAPI: 获取第一个组件内容 /api/v1/course/component
    StudyAPI->>CourseCenter: 查询组件详细内容
    CourseCenter-->>StudyAPI: 返回组件内容
    StudyAPI-->>Frontend: 返回文档组件数据
    
    Frontend-->>Student: 展示文档组件学习界面
```

### 4.2 文档组件交互学习场景

#### 4.2.1 模式切换和播放控制流程
1. 学生在跟随模式下观看文档内容和勾画轨迹
2. 学生滑动内容，前端调用学习交互操作接口，切换到自由模式
3. 系统暂停播放，显示"跟随老师"按钮
4. 学生进行各种交互操作（双击、长按、倍速调整等）
5. 前端调用学习交互操作接口，更新播放状态和进度
6. 学生点击"跟随老师"，前端调用学习交互操作接口，恢复跟随模式

#### 4.2.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant StudyAPI as 学习服务API
    participant StudyCenter as 学习中心

    Note over Student: 跟随模式观看内容
    Student->>Frontend: 滑动内容
    Frontend->>StudyAPI: 学习交互操作 /api/v1/study/interaction
    StudyAPI->>StudyCenter: 更新播放模式为自由模式
    StudyCenter-->>StudyAPI: 确认模式切换
    StudyAPI-->>Frontend: 返回自由模式状态
    Frontend-->>Student: 暂停播放，显示跟随老师按钮

    Student->>Frontend: 双击屏幕
    Frontend->>StudyAPI: 学习交互操作 /api/v1/study/interaction
    StudyAPI->>StudyCenter: 切换播放/暂停状态并记录进度
    StudyCenter-->>StudyAPI: 返回新的播放状态
    StudyAPI-->>Frontend: 返回播放状态
    Frontend-->>Student: 更新播放界面

    Student->>Frontend: 长按加速
    Frontend->>StudyAPI: 学习交互操作 /api/v1/study/interaction
    StudyAPI->>StudyCenter: 设置3倍速播放并记录交互
    StudyCenter-->>StudyAPI: 确认倍速设置
    StudyAPI-->>Frontend: 返回倍速状态
    Frontend-->>Student: 开始3倍速播放

    Student->>Frontend: 点击跟随老师
    Frontend->>StudyAPI: 学习交互操作 /api/v1/study/interaction
    StudyAPI->>StudyCenter: 更新播放模式为跟随模式
    StudyCenter-->>StudyAPI: 确认模式切换
    StudyAPI-->>Frontend: 返回跟随模式状态
    Frontend-->>Student: 恢复跟随模式播放
```

### 4.3 课程完成和学习报告场景

#### 4.3.1 课程完成和反馈流程
1. 学生完成最后一个文档组件的学习
2. 前端调用结束学习会话接口，触发掌握度计算
3. 前端调用获取学习报告接口，获取完整的学习数据（用时、答题数、正确率、掌握度等）
4. 系统展示结算页面，显示学习报告和庆祝动效
5. 学生查看答题详情，了解具体表现
6. 学生提交课程反馈评价
7. 前端调用提交课程反馈接口，保存反馈数据

#### 4.3.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant StudyAPI as 学习服务API
    participant StudyCenter as 学习中心

    Student->>Frontend: 完成最后一个组件
    Frontend->>StudyAPI: 结束学习会话 /api/v1/study/session/end
    StudyAPI->>StudyCenter: 结束会话并触发掌握度计算
    StudyCenter-->>StudyAPI: 返回会话结束状态
    StudyAPI-->>Frontend: 返回结束结果

    Frontend->>StudyAPI: 获取学习报告 /api/v1/study/report
    StudyAPI->>StudyCenter: 查询完整学习数据
    StudyCenter-->>StudyAPI: 返回学习报告数据
    StudyAPI-->>Frontend: 返回完整学习报告

    Frontend-->>Student: 展示结算页和学习报告

    Student->>Frontend: 提交课程反馈
    Frontend->>StudyAPI: 提交课程反馈 /api/v1/study/feedback/submit
    StudyAPI->>StudyCenter: 保存反馈数据
    StudyCenter-->>StudyAPI: 确认反馈保存
    StudyAPI-->>Frontend: 返回提交结果
    Frontend-->>Student: 显示反馈提交成功
```

## 5. 检查清单

### 5.1 PRD核心需求场景与API覆盖性检查

| PRD核心需求点/用户场景 (ID) | 涉及的主要API接口 (路径与方法) | 关键输入数据实体/参数 (示例) | 关键输出数据实体/参数 (示例) | 场景支持度(✅/⚠️/❌) | 数据流正确性(✅/⚠️/❌) | 备注/待办事项 |
| :------ | :------ | :------ | :------ | :------ | :------ | :------ |
| 学生首次进入AI课程学习 | `1. GET /api/v1/course/opening` <br/> `2. POST /api/v1/study/session/start` <br/> `3. GET /api/v1/course/component` | `1. courseId` <br/> `2. StartSessionRequest (userId, courseId)` <br/> `3. componentId` | `1. OpeningPageConfig (ipAnimation, audio)` <br/> `2. StudySession (sessionId, status)` <br/> `3. ComponentContent (content, trajectory)` | ✅ | ✅ | |
| 文档组件交互学习（模式切换、播放控制、进度更新） | `POST /api/v1/study/interaction` | `InteractionRequest (sessionId, operation, mode, speed, position)` | `InteractionResult (status, currentState)` | ✅ | ✅ | |
| 课程完成和学习报告展示 | `1. POST /api/v1/study/session/end` <br/> `2. GET /api/v1/study/report` | `1. EndSessionRequest (sessionId)` <br/> `2. sessionId` | `1. SessionEndResult (completed)` <br/> `2. StudyReport (performance, mastery, details)` | ✅ | ✅ | |
| 课程反馈评价 | `POST /api/v1/study/feedback/submit` | `FeedbackRequest (sessionId, rating, options, textFeedback)` | `FeedbackResult (submitted, feedbackId)` | ✅ | ✅ | |

### 5.2 API接口数据实体与数据库支持检查

| API接口 (路径与方法) | 操作类型 (CRUD) | 主要数据实体 (模型名称) | 关键数据属性 (字段名示例) | 依赖的数据库表/视图 (示例) | 数据库操作 (读/写/更新/删) | 数据可获得性/可行性(✅/⚠️/❌) | 数据一致性保障 (简述策略) | 备注/待办事项 |
| :------ | :------ | :------ | :------ | :------ | :------ | :------ | :------ | :------ |
| `GET /api/v1/course/opening` | Read | `Course` | `courseId, courseName, openingConfig, ipAnimationConfig, audioConfig` | `tbl_course_courses` | 读 | ✅ | `N/A (读取操作)` | `通过Redis缓存提升性能` |
| `GET /api/v1/course/component` | Read | `CourseComponent` | `componentId, documentContent, drawingTrajectoryData, totalDuration` | `tbl_course_components` | 读 | ✅ | `N/A (读取操作)` | `大文件内容考虑CDN加速` |
| `POST /api/v1/study/session/start` | Create | `StudySession` | `sessionId, userId, courseId, startTime, sessionStatus` | `tbl_study_sessions` | 写 | ✅ | `事务保证原子性` | `会话ID生成策略需确认` |
| `POST /api/v1/study/session/end` | Update | `StudySession`, `MasteryRecord` | `sessionStatus, endTime, currentMastery, masteryProgress` | `tbl_study_sessions`, `tbl_mastery_records` | 更新 | ✅ | `分布式事务保证一致性` | `掌握度计算可能耗时，考虑异步处理` |
| `POST /api/v1/study/interaction` | Update | `StudyProgress` | `sessionId, currentPosition, playbackMode, playbackSpeed, interactionData` | `tbl_study_progress`, `tbl_operation_logs` | 更新/写 | ✅ | `乐观锁防止并发冲突` | `高频操作，考虑批量处理和异步日志` |
| `GET /api/v1/study/report` | Read | `StudyPerformance`, `MasteryRecord` | `totalQuestions, correctAnswers, accuracyRate, currentMastery` | `tbl_study_performance`, `tbl_mastery_records` | 读 | ✅ | `N/A (读取操作)` | `数据聚合计算，考虑缓存策略` |
| `POST /api/v1/study/feedback/submit` | Create | `StudyFeedback` | `sessionId, ratingLevel, feedbackOptions, textFeedback` | `tbl_study_feedback` | 写 | ✅ | `事务保证原子性` | `反馈数据用于分析，考虑异步处理` |

### 5.3 API通用设计规范符合性检查

| 检查项 | 状态 (✅/⚠️/❌/N/A) | 备注/说明 |
| :------ | :------ | :------ |
| 1. 命名规范 | ✅ | 使用RESTful风格命名，路径使用小写和连字符 |
| 2. HTTP方法 | ✅ | GET用于查询，POST用于创建和操作，符合RESTful规范 |
| 3. 状态码 | ✅ | 200成功，400参数错误，401认证失败，404资源不存在，500服务器错误 |
| 4. 错误处理 | ✅ | 统一错误响应格式，包含错误码、消息和详细信息 |
| 5. 认证与授权 | ✅ | 所有接口都需要Bearer Token认证，通过用户中心验证 |
| 6. 数据格式与校验 | ✅ | 请求和响应体结构清晰，数据类型准确，包含必要的校验 |
| 7. 分页与排序 | N/A | 当前接口主要为单一资源操作，暂不涉及分页 |
| 8. 幂等性 | ✅ | 读取操作天然幂等，写入操作通过业务逻辑保证幂等性 |
| 9. 安全性 | ✅ | 通过JWT认证，敏感数据加密传输，参数校验防注入 |
| 10. 性能考量 | ✅ | 内容加载2秒内响应，支持1000并发，使用缓存优化 |
| 11. 文档一致性 | ✅ | 文档描述与接口分析文档保持一致 |
| 12. 可测试性 | ✅ | 接口设计清晰，易于进行单元测试和集成测试 |
| 13. 向后兼容性 | N/A (新设计) | 对于v1版本可标记为N/A，但后续版本迭代需重点关注 |

## 6. 附录 (Appendix)

### 6.1 引用文档
- **产品需求文档**: `be-s1-1-ai-prd-Format-PRD-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md`
- **技术架构文档**: `be-s2-1-ai-td-Format-PRD-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md`
- **数据实体设计**: `be-s3-2-ai-de-课程框架文档组件V1.0-claude-3.7.md`
- **数据库设计文档**: `be-s3-4-ai-dd-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md`

### 6.2 参考标准
- OpenAPI 3.0.3 规范
- RESTful API 设计最佳实践
- JWT Token 认证标准
- ISO 8601 日期时间格式标准
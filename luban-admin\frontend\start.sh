#!/bin/bash
echo "🚀 鲁班前端启动脚本 - Ubuntu/Mac 版本"
echo "=========================================="


echo ""
echo "🧹 正在清理 Next.js 缓存..."
if [ -d ".next" ]; then
    echo "🗑️  删除 .next 目录..."
    rm -rf .next
fi
if [ -d "node_modules/.cache" ]; then
    echo "🗑️  删除 node_modules/.cache 目录..."
    rm -rf node_modules/.cache
fi
echo "✅ 缓存清理完成！"
echo ""
echo "🚀 正在启动开发服务器..."
echo "=========================================="
npm run dev

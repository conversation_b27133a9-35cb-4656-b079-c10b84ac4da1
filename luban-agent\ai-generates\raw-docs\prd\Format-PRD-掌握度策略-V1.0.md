# PRD - 掌握度策略 - V1.0 **版本管理**

<table>
<tr>
<td>版本号<br/></td><td>日期<br/></td><td>变更人<br/></td><td>变更类型<br/></td><td>变更详情<br/></td></tr>
<tr>
<td>V1.0<br/></td><td>2025-04-11<br/></td><td>钱晋菲<br/></td><td>新建<br/></td><td>新建文档<br/></td></tr>
<tr>
<td>V1.0.1<br/><br/></td><td>2025-05-12<br/><br/></td><td>钱晋菲<br/><br/></td><td>更新<br/><br/></td><td>1. 外化掌握度1. 更新计算方式，由星星改成百分比展示2. 掌握度更新时机，中途退出学习不更新3. 补充对特殊情况的处理<br/>2. 目标掌握度1. 更新各维度系数2. 换算成百分比，按0.05向上取整<br/></td></tr>
<tr>
<td>V1.0.2<br/></td><td>2025-05-14<br/></td><td>钱晋菲<br/></td><td>更新<br/><br/></td><td>1. 知识点掌握度计算方式，增加了错题重答<br/><br/></td></tr>
<tr>
<td>V1.0.3<br/></td><td>2025-05-21<br/><br/></td><td>钱晋菲<br/></td><td>更新<br/><br/></td><td>1. 新增课程有无掌握度的判断逻辑<br/>2. 新增掌握度是否薄弱的判断逻辑<br/></td></tr>
<tr>
<td>V1.0.4<br/></td><td>2025-05-27<br/><br/></td><td>钱晋菲<br/></td><td>更新<br/><br/></td><td>1. 外化掌握度1. 用户累计作答5题后，每次答题后均更新。<br/></td></tr>
<tr>
<td>V1.0.5<br/><br/></td><td>2025-06-04<br/><br/></td><td>钱晋菲<br/></td><td>更新<br/><br/></td><td>1. 外化掌握度，计算公式更新<br/></td></tr>
</table>

**关联需求**

<table>
<tr>
<td>关联需求名称<br/></td><td>所属 PM<br/></td><td>需求进度<br/></td><td>文档链接<br/></td></tr>
<tr>
<td>AI课中 V1.0<br/></td><td><br/></td><td><br/></td><td>[PRD - AI课中 - V1.0（WIP）](https://wcng60ba718p.feishu.cn/wiki/XtfswiCCQiKHdgkev4Qc1Nqined)<br/></td></tr>
</table>

# 一、背景和目标

## 需求背景

在自适应学习系统中，掌握度是非常重要的功能模块，是未来各种推荐策略的基础输入。

1. 精准评估学习状况：掌握度能够精确反映学生对各个知识点的理解和掌握程度。让学生和教师都能清楚了解学生在学习过程中的优势和不足。
2. 优化学习路径：巩固练习和拓展练习都需要依据掌握度来为学生量身定制学习路径，推送适合该学生的学习内容和练习题目。

## 项目收益

1. 提升产品体验，让用户感受到系统的个性化和智能性
2. 提高学习效率，避免重复学习已经掌握的内容
3. 是巩固练习和拓展练习等推荐策略的前置输入

## 覆盖用户

使用新 AI 课学习的全部用户

# **二、概念厘清**

## 掌握度的定义

掌握度是衡量学生对特定知识点理解与运用程度的关键指标。在新 AI 课系统中定义了多种类型的掌握度：

1. **学科能力（用户分层）**：量化学生在某个学科的能力水平，用于学生分层&学习路径规划。仅基于考试数据更新。
2. **知识点掌握度**：量化学生对<u>某个知识点的熟练程度</u>，<u>用于动态调整学习内容和路径</u>，在<u>每次答题后更新</u>。通常和用户的答题表现（正确率、知识点难度、错误模式）、行为数据（答题时长、尝试次数、学习间隔时长）等因素相关。
3. **外化掌握度**：用于向学生展示<u>对于单节课程的掌握程度</u>，<u>牵引用户学习</u>。在学生<u>完成 AI 课、巩固练习、作业、测试等学习任务后更新</u>。> 由于知识点掌握度是一个动态变化的指标且和多种因素相关不便于用户理解，因此在向用户展示掌握度时采用简化的和答题正确率&题目难度更相关的计算方式，降低用户理解成本。
4. **目标掌握度**：在<u>目标设定阶段</u>，系统依据学生设定的目标，推导出学生在每节课应达到的<u>外化掌握度</u>。

<table>
<tr>
<td><br/></td><td>**学科能力（用户分层）**<br/></td><td>**知识点掌握度**<br/></td><td>**外化掌握度（星星）**<br/></td><td>**目标掌握度**<br/></td></tr>
<tr>
<td>评估对象<br/></td><td>学生在某个学科的能力水平<br/></td><td>学生对某个知识点的掌握程度<br/><br/></td><td>学生对单节课程的掌握程度<br/><br/></td><td>根据目标推导出学生对单节课程应该达到的掌握程度<br/></td></tr>
<tr>
<td>目的<br/></td><td>学生分层&学习路径规划<br/><br/></td><td>动态调整学习内容和路径<br/><br/></td><td colspan="2">激励手段，牵引用户学习<br/><br/></td></tr>
<tr>
<td>更新时机<br/></td><td>前期每次考试后更新<br/>后续根据知识点掌握度计算<br/></td><td>每次答题后更新<br/><br/></td><td>完成AI课、巩固练习、作业、测试等学习任务后更新<br/></td><td>用户设置/修改目标后更新<br/><br/></td></tr>
<tr>
<td>计算方式<br/></td><td colspan="2">冷启动阶段基于策略计算<br/>后续模型直接输出<br/></td><td colspan="2">基于策略计算<br/></td></tr>
</table>

## 内容结构

> 掌握度结算仅考虑业务树节点
> ![Llxiw3ZzGhTPlyb3QLtcfVkhnLf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_Llxiw3ZzGhTPlyb3QLtcfVkhnLf.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

该图片展示了一个层级化的“业务树”结构，用于组织知识点。其关键元素及层级关联如下：

*   **根节点：** 业务树
    *   **一级分支：** 一级知识点 A
        *   **二级分支：** 二级知识点 a
            *   **三级分支：** 三级知识点 1
            *   **三级分支：** 三级知识点 2
            *   **三级分支：** ...... (表示更多三级知识点)
        *   **二级分支：** 二级知识点 b
        *   **二级分支：** ...... (表示更多二级知识点)
    *   **一级分支：** 一级知识点 B
    *   **一级分支：** 一级知识点 C
    *   **一级分支：** ...... (表示更多一级知识点)

以下是该逻辑结构的 Mermaid flowchart 表示：

```mermaid
graph LR
    Root[业务树] --> L1_A[一级知识点 A]
    Root --> L1_B[一级知识点 B]
    Root --> L1_C[一级知识点 C]
    Root --> L1_More[...]

    L1_A --> L2_a[二级知识点 a]
    L1_A --> L2_b[二级知识点 b]
    L1_A --> L2_More_under_A[...]

    L2_a --> L3_1[三级知识点 1]
    L2_a --> L3_2[三级知识点 2]
    L2_a --> L3_More_under_a[...]
```

【============== 图片解析 END ==============】



- **一节课等于一个知识点：**业务树的末级节点 = 单节课
- **题目和课关联**：1 道题目可关联多节课，不存在没有关联课的题目。
  ![UeQ1wSFkYhjvkPbbXOac47DQnch](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_UeQ1wSFkYhjvkPbbXOac47DQnch.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

此图表展示了“题目”与“课程（知识点）”之间的映射关系，具体解析如下：

**关键元素与组成部分：**

1.  **题目层 (顶层):**
    *   题目 1
    *   题目 2
    *   题目 3
    *   题目 4
2.  **课程层 (底层):**
    *   课程 1 (知识点1)
    *   课程 2 (知识点2)

**元素间关联 (层级化结构):**

*   “题目 1”、“题目 2” 与 “题目 3” 共同指向 “课程 1 (知识点1)”。这意味着 “课程 1 (知识点1)” 涵盖了 “题目 1”、“题目 2” 和 “题目 3” 所涉及的内容，或者 “题目 1”、“题目 2” 和 “题目 3” 是学习 “课程 1 (知识点1)” 的前置或相关练习。
*   “题目 3” 与 “题目 4” 共同指向 “课程 2 (知识点2)”。这意味着 “课程 2 (知识点2)” 涵盖了 “题目 3” 和 “题目 4” 所涉及的内容，或者 “题目 3” 和 “题目 4” 是学习 “课程 2 (知识点2)” 的前置或相关练习。

**Mermaid 流程图描述：**

```mermaid
flowchart TD
    T1["题目 1"]
    T2["题目 2"]
    T3["题目 3"]
    T4["题目 4"]
    C1["课程 1 (知识点1)"]
    C2["课程 2 (知识点2)"]

    T1 --> C1
    T2 --> C1
    T3 --> C1
    T3 --> C2
    T4 --> C2
```

【============== 图片解析 END ==============】



# 三、策略方案

## 学科能力（用户分层）

**冷启动阶段计算思路：**

1. 锚定学校在全国范围内的大致水平
2. 校准不同届的生源质量
3. 基于学生各科成绩在学校的排名&学校在全国范围的水平进行分层

### 数据准备

<table>
<tr>
<td>数据类型<br/></td><td>字段<br/></td><td>字段说明<br/></td></tr>
<tr>
<td rowspan="2">学生信息<br/><br/></td><td>学生ID<br/></td><td>和AI课系统一一对应的学生ID<br/></td></tr>
<tr>
<td>各学科年级排名百分比<br/>1. 学生单科排名<br/>2. 年级人数<br/></td><td>如果拿到的是学生排名信息，需要转换为排名百分比。<br/>> 示例：年级50名 / 年级共200名学生 → 排名百分比=25% 。代表这名学生在年级所有学生中处于前 25% 的位置。<br/></td></tr>
<tr>
<td rowspan="2">学校信息 - 仅高中收集<br/><br/></td><td>学校近3年的高考本科录取率<br/><br/></td><td>1. 高考年份<br/>2. 对应年份的本科录取率<br/></td></tr>
<tr>
<td>学生中考成绩全省/市排名<br/>或<br/>一分一段表<br/></td><td>1. 对应高考年份的学生的中考排名<br/>2. 本届学生的中考排名<br/><br/></td></tr>
</table>


> 注：每届新生都需要收集对应信息，重新计算下文中的各类系数

### 计算方式

#### 高中

1. **锚定学校水平**根据学校历届高考的平均本科录取率，对应到不同的校准系数 a

<table>
<tr>
<td>本科录取率区间<br/></td><td>[90,100]<br/></td><td>[80,90)<br/></td><td>[60,80)<br/></td><td>[40,60)<br/></td><td>[20,40）<br/></td><td>[0,20)<br/></td></tr>
<tr>
<td>校准系数a<br/></td><td>1<br/></td><td>1.2<br/></td><td>1.3<br/></td><td>1.5<br/></td><td>2<br/></td><td>3<br/></td></tr>
</table>

如果学校未提供录取率，可简化为按学校类型设置系数

<table>
<tr>
<td>学校类型<br/></td><td>重点中学<br/></td><td>普通中学<br/></td><td>较差中学<br/></td></tr>
<tr>
<td>校准系数a<br/></td><td>1<br/></td><td>1.3<br/></td><td>3<br/></td></tr>
</table>

2. **校准生源波动**根据学校中考排名计算校准系数 b，如果学校没有提供排名数据，则校准系数默认为 1
   **情况一：**学校近 3 年高考本科录取率的相对变化小于 10%

- 校准系数 b = √（历年排名中位数的倒数的平均 / 今年排名中位数的倒数）

示例：

<table>
<tr>
<td><br/></td><td>今年<br/></td><td>2024<br/></td><td>2023<br/></td><td>2022<br/></td></tr>
<tr>
<td rowspan="2">排名中位数<br/></td><td>a<br/></td><td>b<br/></td><td>b'<br/></td><td>b''<br/></td></tr>
<tr>
<td>150<br/></td><td>210<br/></td><td>189<br/></td><td>203<br/></td></tr>
</table>

校准系数 
$$
b = \sqrt{ \frac{\left( \frac{1}{b} + \frac{1}{b'} + \frac{1}{b''} \right)}{3} \div \frac{1}{a}} = \sqrt{ \frac{\left( \frac{1}{210} + \frac{1}{189} + \frac{1}{203} \right)}{3} \div \frac{1}{150}} \approx 0.82
$$

**情况二：**学校近 3 年高考本科录取率的相对变化大于 10%，仅采用一年的分数进行校准（人工指定）

1. **确定用户分层**- 学科能力排名 = 用户单科排名百分比 * 校准系数 a * 校准系数 b，取值[0,1]，向上取整

<table>
<tr>
<td>用户分层<br/></td><td>学科能力分层区间<br/></td></tr>
<tr>
<td>S+<br/></td><td>[0 , 5]<br/></td></tr>
<tr>
<td>S<br/></td><td>(5 , 10]<br/></td></tr>
<tr>
<td>A<br/></td><td>(10 , 50]<br/></td></tr>
<tr>
<td>B<br/></td><td>(50 , 80]<br/></td></tr>
<tr>
<td>C<br/></td><td>(80 , 100]<br/></td></tr>
</table>

注：本科录取率在 40% 以下的学校 或 较差中学 ，用户分层中没有 S+，落入 S+ 区间的学生分层记为 S

**示例：**一个高考本科录取率为 57% 的学校，今年入学全市排名中位数为 2187，历届入学排名中位数为 2000、1897、1878。
数学在年级排名百分比为 38% 的学生对应的学科能力为：
$$
38 \times 1.3 \times \sqrt{\frac{\left(\frac{1}{2000} + \frac{1}{1897} + \frac{1}{1878}\right) / 3}{1/2187}} \approx 53
$$
该学生的数学能力分层为 B

#### 初中

1. **锚定学校水平**根据学校类型，对应到不同的校准系数 a

<table>
<tr>
<td>学校类型<br/></td><td>重点中学<br/></td><td>普通中学<br/></td><td>较差中学<br/></td></tr>
<tr>
<td>校准系数a<br/></td><td>1<br/></td><td>1.3<br/></td><td>3<br/></td></tr>
</table>

2. **确定用户分层**- 学科能力 = 用户单科排名百分比 * 校准系数 a，取值[0,1]，向上取整

<table>
<tr>
<td>用户分层<br/></td><td>学科能力分层区间<br/></td></tr>
<tr>
<td>S+<br/></td><td>[0 , 5]<br/></td></tr>
<tr>
<td>S<br/></td><td>(5 , 10]<br/></td></tr>
<tr>
<td>A<br/></td><td>(10 , 50]<br/></td></tr>
<tr>
<td>B<br/></td><td>(50 , 80]<br/></td></tr>
<tr>
<td>C<br/></td><td>(80 , 100]<br/></td></tr>
</table>

注：较差中学的用户分层中没有 S+，落入 S+ 区间的学生分层记为 S

**示例：**一个较差中学的学生，数学在年级排名百分比为 1%，对应的学科能力为：1 * 3 = 3
由于较差中学不设置 S+ 分层，该学生的数学能力分层为 S

### 更新用户分层

每次年级统一考试后，根据学生本次考试的年级排名百分比，更新用户所处分层。
统考成绩数据怎么拿到？

## **知识点掌握度**

### **初始掌握度**

> V1.0 不考虑知识点之间的迁移性，将所有知识点视为孤立知识点

1. 学科初始值 0.3> V1.0 每个学科设置统一的初始掌握度，后续需要支持不同学科设置不同的掌握度初始值
2. 根据用户分层设置不同的初始掌握系数，每个用户在每个知识点上的初始掌握度 = 学科初始值 * 分层掌握系数

<table>
<tr>
<td>用户分层<br/></td><td>S+<br/></td><td>S<br/></td><td>A<br/></td><td>B<br/></td><td>C<br/></td></tr>
<tr>
<td>分层掌握系数<br/></td><td>1.2<br/></td><td>1.0<br/></td><td>0.8<br/></td><td>0.5<br/></td><td>0.4<br/></td></tr>
<tr>
<td>初始掌握度<br/></td><td>0.36<br/></td><td>0.3<br/></td><td>0.24<br/></td><td>0.15<br/></td><td>0.12<br/></td></tr>
</table>

### 单次答题的掌握度增量

#### 数据准备

- 题目难度分级 > 此处的难度等级为知识点内的题目之间的相对难度，不会出现简单知识点下全部都是 L1，复杂知识点下全部都是 L5 的情况。

<table>
<tr>
<td>难度档位<br/></td><td>L1<br/></td><td>L2<br/></td><td>L3<br/></td><td>L4<br/></td><td>L5<br/></td></tr>
<tr>
<td>难度系数<br/></td><td>0.3<br/></td><td>0.5<br/></td><td>0.7<br/></td><td>0.9<br/></td><td>1.1<br/></td></tr>
</table>

#### 计算方式

- **整体逻辑**
  通过判断<u>答题结果</u>（正确或错误），结合<u>题目本身的难度系数</u>及<u>用户当前的知识点掌握度</u>，来确定掌握度的增减数值。这意味着不同水平的学生对不同难度题目的作答情况，会以不同幅度影响知识点的掌握度。
- **引入答错补偿系数**
  为避免答错了高难度题而被过度惩罚，在作答错误场景需要引入补偿系数

<table>
<tr>
<td>题目难度档位<br/></td><td>L1<br/></td><td>L2<br/></td><td>L3<br/></td><td>L4<br/></td><td>L5<br/></td></tr>
<tr>
<td>答错补偿系数<br/></td><td>1.8<br/></td><td>0.8<br/></td><td>0.5<br/></td><td>0.3<br/></td><td>0.2<br/></td></tr>
</table>

- **设置默认增量**- 作答正确：默认掌握度增量为 0.2
- 作答错误：默认掌握度增量为 -0.1
- **首次答题计算方式** 1. 作答正确：掌握度增量 = 0.2 *（题目难度系数 - 用户当前掌握度）

2. 部分正确：掌握度增量 = 0.2 *（题目难度系数 - 用户当前掌握度） * 0.5
3. 作答错误：掌握度增量 = -0.1 *（题目难度系数 - 用户当前掌握度）* 答错补偿系数

- **示例** 1. 用户当前掌握度为 0.24，做对了一道难度为 L3 的题目，单题掌握度增量的计算过程如下：- 掌握度增量 = 0.2 * （0.6-0.24）= 0.07

2. 用户当前掌握度为 0.24，做错了一道难度为 L5 的题目，单题掌握度增量的计算过程如下：- 掌握度增量 = -0.1 *（1-0.24）* 0.2= -0.02
3. 用户当前掌握度为 0.6，做错了一道难度为 L1 的题目，单题掌握度增量的计算过程如下：- 掌握度增量 = -0.1 * |0.2 - 0.6| * 1.8 = -0.072

- **错题重练计算方式** 1. 引入和尝试次数相关的衰减系数，衰减系数= $0.5^X$（x 为重练次数）示例：第一次重练，系数为 0.5；第二次重练，系数为 0.25；第三次重练，系数为 0.125；

2. 错题重练增量 = 首次答题增量 * 衰减系数示例：第一次重练
3. 作答正确：掌握度增量 = 0.2 *（题目难度系数 - 用户当前掌握度） * 0.5
4. 部分正确：掌握度增量 = 0.2 *（题目难度系数 - 用户当前掌握度） * 0.5 * 0.5
5. 作答错误：掌握度增量 = -0.1 *（题目难度系数 - 用户当前掌握度）* 答错补偿系数 * 0.5

- **特殊情况** 1. 作答正确/部分正确：如果题目难度系数-用户当前掌握度 ≤0.01，按 0.01 计算

2. 作答错误：1. 如果题目难度系数-用户当前掌握度在[-0.01 , 0.01] ，按 0.01 计算
3. 如果题目难度系数-用户当前掌握度在[-1 , -0.01) , 取绝对值计算
4. 自评的主观题，在掌握度计算时需要降低权重。1. 如果作答时间小于 5 秒，答题结果不进行结算
5. 如果作答时间大于等于 5 秒，<u>计算增量时需要额外 * 0.5</u>

### 累计掌握度更新

**计算方式**：累计掌握度=当前掌握度 + 单题掌握度增量，<u>结果限制在[0,1]区间</u>
**限制：**针对同一用户在同一道题多次作答的情况 1. 在同一节课里多次答同一道题 1. 之前已作答正确的题目，不再更新掌握度变化。
2. 之前作答错误的题目：按“错题重答”的计算方式更新掌握度变化

2. 在不同的课程里作答同一道题 1. 当前课程中的第一次作答正常结算掌握度
3. 后续作答按“在同一节课里多次答题”处理

**更新时机：**用户每次作答后更新
**更新范围：**用户作答题目关联的所有知识点。如果一道题关联了多个知识点，则多个知识点的掌握度都会更新。

## 外化掌握度

**应用场景：**用于在报告页展示学生对于本节课的掌握情况

### **更新时机**

1. 用户累计作答 5 题后，每次答题后均更新。

### **计算方式**

1. 用户在本节课的知识点掌握度 / 掌握度上限，结果控制在[0,1]，四舍五入保留小数点后 2 位
   不同难度的课程有不同的掌握度上限值

    |            | 课程难度高 | 课程难度中 | 课程难度低 |
    | ---------- | ---------- | ---------- | ---------- |
    | 掌握度上限 | 1          | 0.9        | 0.7        |

   举例：用户在一个难度低的课程中，知识点掌握度为 0.5，则外化掌握度为 0.5/0.7=0.71
   用户在一个难度低的课程中，知识点掌握度为 0.8，则外化掌握度为 1

2. 外化掌握度只增不降，当用户的知识点掌握度数值下降时，外化掌握度保持不变。

#### **首次最少答题量限制**

> 为避免答题量过少导致的偶然性偏差，提升统计可靠性。需要对最少答题量进行限制。

首次答题量限制：5 题
用户在一节课内（包括 AI 课、巩固练习、拓展练习）需要累计答题 5 道以上，才更新外化掌握度。
答题超过 5 题后，每次答题均更新外化掌握度。

#### **特殊情况**

1. AI 课中有题目，但是题目数量不足 5 题，则用户完成一节 AI 课后，需要更新外化掌握度。
2. AI 课中没有配置题目。用户学完后，外化掌握度统一按 0.2 展示。

## 目标掌握度（V1.0 C 端没有设置目标的功能，目标掌握度暂时不做）

在用户设置/修改目标后，离线计算每节课对应的目标掌握度。
**目标设置：**高中阶段选择大学院校为目标，初中阶段选择年级排名为目标。
**整体逻辑：**目标掌握度和每节课的难度、考试频率、目标难度高度相关。单节课难度系数越低，考试频率越高，目标难度越高，要求的目标掌握度就越高。

### 数据准备

1. 单课难度系数

    |      | 高  | 中   | 低  |
    | ---- | --- | ---- | --- |
    | 文科 | 0.7 | 0.65 | 0.6 |
    | 理科 | 0.8 | 0.7  | 0.6 |

2. 考试频率

   |          | 高  | 中  | 低  |
   | -------- | --- | --- | --- |
   | 考频系数 | 1   | 0.9 | 0.8 |

3. 目标难度系数

- 高中阶段

 |              | C9  | 985/211 | 一本 | 二本 | 二本以下 |
 | ------------ | --- | ------- | ---- | ---- | -------- |
 | 目标难度系数 | 1.3 | 1.2     | 1.1  | 1    | 0.9      |

- 初中阶段

> 如果 C 端设置目标时选择的是年级排名的绝对值，数据收集时需要拿到学校的年级人数，通过年级人数和目标名次计算排名百分比，确定对应的难度系数。

|              | 年级前5% | 年级前10% | 年级前20% | 年级前30% | 年级前50% |
| ------------ | -------- | --------- | --------- | --------- | --------- |
| 目标难度系数 | 1.3      | 1.2       | 1.1       | 1         | 0.9       |

### 计算方式

1. 每节课对应的目标掌握度 = 目标难度系数 * 单课难度系数 * 考试频率
2. 换算成百分比，按 0.05 向上取整
   举例：理科，课程难度高、考频中、用户目标一本
   目标掌握度=0.8*0.9*1=0.72 ≈ 75%

## 薄弱知识点

当用户的掌握度<u>同时满足</u>下述两个条件时，判断本知识点为薄弱知识点

1. 外化掌握度 / 目标掌握度 ≤ 0.7
2. 知识点掌握度 ≤ 0.5

## 判断知识点是否有掌握度

如果知识点符合下面条件中的一个，则本知识点不计算掌握度

1. 知识点没有配置巩固练习
2. 知识点没有配置拓展练习 且 巩固练习的“题目/ 空 / 子题目”数量 ＜20，数量计算逻辑如下：1. 一道题如果包括多个空（如：完形填空），按空的数量计算
3. 一道题下包括多个子题目，按子题目的数量计算

## 指标跟踪

> 用于上线后监测异常情况，进行策略优化

- **外化掌握度变化：**记录每个学生，每一次外化掌握度结算后的星级变化情况。是否会出现掌握度突然大幅下降或上升，超出了正常波动范围。
- **用户答题量：**记录每个用户在每次星级变化时的答题数量。

# 四、学校数据收集

## 初始化数据

<table>
<tr>
<td>数据类型<br/></td><td>是否必填<br/></td><td>字段<br/></td><td>目的<br/></td></tr>
<tr>
<td rowspan="3">学生信息<br/><br/></td><td rowspan="3">是<br/></td><td>学生姓名/ ID<br/></td><td>对应AI课系统的学生ID<br/></td></tr>
<tr>
<td>学生单科年级排名<br/></td><td rowspan="2">用于计算学生单科成绩的年级排名百分比<br/><br/></td></tr>
<tr>
<td>年级人数<br/></td></tr>
<tr>
<td rowspan="4">学校信息 - 高中收集<br/><br/></td><td rowspan="2">2选1 必填<br/><br/></td><td>1. 高考年份<br/>2. 对应年份的本科录取率<br/></td><td rowspan="2">根据学校近3年的高考本科录取率 / 学校类型，判断学校在全国范围内的大致水平<br/><br/></td></tr>
<tr>
<td>学校类型（重点/普通/较差）<br/></td></tr>
<tr>
<td rowspan="2">2选1 非必填<br/></td><td>1. 对应高考年份的学生的中考排名<br/>2. 本届学生的中考排名<br/></td><td rowspan="2">用于校准本届学生和历届学生的生源波动<br/><br/></td></tr>
<tr>
<td>1. 对应高考年份的学生的一分一段表<br/>2. 本届学生的一分一段表<br/></td></tr>
<tr>
<td>学校信息 - 初中收集<br/></td><td>是<br/><br/></td><td>学校类型（重点/普通/较差）<br/><br/></td><td>根据学校类型，大致判断学校在全省/市范围内的水平<br/></td></tr>
</table>

## 使用中数据

学生在年级统一考试（期中、期末）中的单科排名，用于更新用户分层。

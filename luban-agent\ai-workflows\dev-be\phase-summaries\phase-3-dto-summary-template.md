# Phase 3: DTO层完成汇总

**项目名称**: {project_name}
**业务模块**: {biz_module}
**完成时间**: {completion_time}
**执行人**: {developer_name}

## 1. 已生成文件清单

### 1.1 请求DTO文件
- `model/dto/{biz_module}/request/{entity1}_create_req.go` - {entity1}创建请求
- `model/dto/{biz_module}/request/{entity1}_update_req.go` - {entity1}更新请求
- `model/dto/{biz_module}/request/{entity1}_list_req.go` - {entity1}列表查询请求
- `model/dto/{biz_module}/request/{entity2}_create_req.go` - {entity2}创建请求
- `model/dto/{biz_module}/request/{entity2}_update_req.go` - {entity2}更新请求
- `model/dto/{biz_module}/request/{entity2}_list_req.go` - {entity2}列表查询请求

### 1.2 响应DTO文件
- `model/dto/{biz_module}/response/{entity1}_resp.go` - {entity1}响应
- `model/dto/{biz_module}/response/{entity1}_list_resp.go` - {entity1}列表响应
- `model/dto/{biz_module}/response/{entity2}_resp.go` - {entity2}响应
- `model/dto/{biz_module}/response/{entity2}_list_resp.go` - {entity2}列表响应

### 1.3 转换器文件
- `model/dto/{biz_module}/converter/{entity1}_converter.go` - {entity1}转换器
- `model/dto/{biz_module}/converter/{entity2}_converter.go` - {entity2}转换器
- `model/dto/{biz_module}/converter/{entity3}_converter.go` - {entity3}转换器

### 1.4 公共DTO文件
- `model/dto/common/page_req.go` - 分页请求通用结构
- `model/dto/common/page_resp.go` - 分页响应通用结构
- `model/dto/common/base_resp.go` - 基础响应结构

### 1.5 单元测试文件
- `model/dto/{biz_module}/converter/{entity1}_converter_test.go` - {entity1}转换器测试
- `model/dto/{biz_module}/converter/{entity2}_converter_test.go` - {entity2}转换器测试
- `model/dto/{biz_module}/request/{entity1}_create_req_test.go` - 请求验证测试

## 2. 请求DTO汇总

### 2.1 {Entity1}CreateReq - 创建请求
- **文件位置**: `model/dto/{biz_module}/request/{entity1}_create_req.go`
- **结构定义**:
```go
type {Entity1}CreateReq struct {
    {Field1} {type} `json:"{jsonField1}" binding:"required" validate:"min=1,max=100"`
    {Field2} {type} `json:"{jsonField2}" binding:"required"`
    {Field3} {type} `json:"{jsonField3}" binding:"omitempty" validate:"email"`
    {Field4} int32 `json:"{jsonField4}" binding:"required" validate:"oneof=1 2 3"`
}
```
- **验证规则**:
  - {Field1}: 必填，长度1-100
  - {Field2}: 必填
  - {Field3}: 可选，邮箱格式
  - {Field4}: 必填，枚举值1/2/3

### 2.2 {Entity1}UpdateReq - 更新请求
- **文件位置**: `model/dto/{biz_module}/request/{entity1}_update_req.go`
- **结构定义**:
```go
type {Entity1}UpdateReq struct {
    ID       int64  `json:"id" binding:"required" validate:"min=1"`
    {Field1} *{type} `json:"{jsonField1}" binding:"omitempty" validate:"min=1,max=100"`
    {Field2} *{type} `json:"{jsonField2}" binding:"omitempty"`
    {Field3} *{type} `json:"{jsonField3}" binding:"omitempty" validate:"email"`
    {Field4} *int32 `json:"{jsonField4}" binding:"omitempty" validate:"oneof=1 2 3"`
}
```
- **验证规则**:
  - ID: 必填，大于0
  - 其他字段: 可选更新，使用指针类型

### 2.3 {Entity1}ListReq - 列表查询请求
- **文件位置**: `model/dto/{biz_module}/request/{entity1}_list_req.go`
- **结构定义**:
```go
type {Entity1}ListReq struct {
    PageReq
    {Field1}    *{type} `json:"{jsonField1}" form:"{jsonField1}"`
    {Field2}    *{type} `json:"{jsonField2}" form:"{jsonField2}"`
    Status      *int32  `json:"status" form:"status" validate:"omitempty,oneof=1 2 3"`
    StartTime   *int64  `json:"startTime" form:"startTime"`
    EndTime     *int64  `json:"endTime" form:"endTime"`
    Keyword     *string `json:"keyword" form:"keyword" validate:"omitempty,max=50"`
}
```
- **查询条件**:
  - 支持按{Field1}、{Field2}筛选
  - 支持按状态筛选
  - 支持时间范围查询
  - 支持关键词搜索

### 2.4 {Entity2}相关请求DTO
- **{Entity2}CreateReq**: [类似上述结构]
- **{Entity2}UpdateReq**: [类似上述结构]
- **{Entity2}ListReq**: [类似上述结构]

## 3. 响应DTO汇总

### 3.1 {Entity1}Resp - 单个响应
- **文件位置**: `model/dto/{biz_module}/response/{entity1}_resp.go`
- **结构定义**:
```go
type {Entity1}Resp struct {
    ID          int64  `json:"id"`
    {Field1}    {type} `json:"{jsonField1}"`
    {Field2}    {type} `json:"{jsonField2}"`
    {Field3}    {type} `json:"{jsonField3}"`
    Status      int32  `json:"status"`
    StatusText  string `json:"statusText"`
    CreatedAt   int64  `json:"createdAt"`
    UpdatedAt   int64  `json:"updatedAt"`
}
```
- **字段说明**:
  - 所有字段使用小驼峰命名
  - 时间字段统一为毫秒时间戳
  - 状态字段包含数值和文本描述

### 3.2 {Entity1}ListResp - 列表响应
- **文件位置**: `model/dto/{biz_module}/response/{entity1}_list_resp.go`
- **结构定义**:
```go
type {Entity1}ListResp struct {
    PageResp
    List []*{Entity1}Resp `json:"list"`
}
```
- **分页信息**:
  - 继承PageResp分页结构
  - List字段包含具体数据列表

### 3.3 {Entity2}相关响应DTO
- **{Entity2}Resp**: [类似上述结构]
- **{Entity2}ListResp**: [类似上述结构]

## 4. 转换器实现汇总

### 4.1 {Entity1}Converter
- **文件位置**: `model/dto/{biz_module}/converter/{entity1}_converter.go`
- **转换方法**:

#### 4.1.1 请求转领域实体
```go
// CreateReqToEntity 创建请求转实体
func (c *{Entity1}Converter) CreateReqToEntity(req *{Entity1}CreateReq) *entity.{Entity1} {
    if req == nil {
        return nil
    }
    return &entity.{Entity1}{
        {Field1}: req.{Field1},
        {Field2}: req.{Field2},
        {Field3}: req.{Field3},
        Status:   req.{Field4},
        CreatedAt: time.Now().UnixMilli(),
        UpdatedAt: time.Now().UnixMilli(),
    }
}

// UpdateReqToEntity 更新请求转实体
func (c *{Entity1}Converter) UpdateReqToEntity(req *{Entity1}UpdateReq, existing *entity.{Entity1}) *entity.{Entity1} {
    if req == nil || existing == nil {
        return existing
    }
    
    updated := *existing
    if req.{Field1} != nil {
        updated.{Field1} = *req.{Field1}
    }
    if req.{Field2} != nil {
        updated.{Field2} = *req.{Field2}
    }
    // ... 其他字段更新
    updated.UpdatedAt = time.Now().UnixMilli()
    
    return &updated
}
```

#### 4.1.2 领域实体转响应
```go
// EntityToResp 实体转响应
func (c *{Entity1}Converter) EntityToResp(entity *entity.{Entity1}) *{Entity1}Resp {
    if entity == nil {
        return nil
    }
    return &{Entity1}Resp{
        ID:         entity.ID,
        {Field1}:   entity.{Field1},
        {Field2}:   entity.{Field2},
        {Field3}:   entity.{Field3},
        Status:     entity.Status,
        StatusText: c.getStatusText(entity.Status),
        CreatedAt:  entity.CreatedAt,
        UpdatedAt:  entity.UpdatedAt,
    }
}

// EntitiesToListResp 实体列表转列表响应
func (c *{Entity1}Converter) EntitiesToListResp(entities []*entity.{Entity1}, total int64, pageReq *PageReq) *{Entity1}ListResp {
    list := make([]*{Entity1}Resp, 0, len(entities))
    for _, entity := range entities {
        if resp := c.EntityToResp(entity); resp != nil {
            list = append(list, resp)
        }
    }
    
    return &{Entity1}ListResp{
        PageResp: PageResp{
            Page:     pageReq.Page,
            PageSize: pageReq.PageSize,
            Total:    total,
            Pages:    (total + int64(pageReq.PageSize) - 1) / int64(pageReq.PageSize),
        },
        List: list,
    }
}
```

#### 4.1.3 辅助方法
```go
// getStatusText 获取状态文本
func (c *{Entity1}Converter) getStatusText(status int32) string {
    switch status {
    case consts.{Entity1}Status{State1}:
        return "{state1_text}"
    case consts.{Entity1}Status{State2}:
        return "{state2_text}"
    case consts.{Entity1}Status{State3}:
        return "{state3_text}"
    default:
        return "未知状态"
    }
}
```

### 4.2 {Entity2}Converter
- **文件位置**: `model/dto/{biz_module}/converter/{entity2}_converter.go`
- **转换方法**: [类似上述结构]

### 4.3 {Entity3}Converter
- **文件位置**: `model/dto/{biz_module}/converter/{entity3}_converter.go`
- **转换方法**: [类似上述结构]

## 5. 公共DTO结构汇总

### 5.1 分页请求结构
- **文件位置**: `model/dto/common/page_req.go`
```go
type PageReq struct {
    Page     int `json:"page" form:"page" binding:"required,min=1" validate:"min=1"`
    PageSize int `json:"pageSize" form:"pageSize" binding:"required,min=1,max=100" validate:"min=1,max=100"`
}
```

### 5.2 分页响应结构
- **文件位置**: `model/dto/common/page_resp.go`
```go
type PageResp struct {
    Page     int   `json:"page"`
    PageSize int   `json:"pageSize"`
    Total    int64 `json:"total"`
    Pages    int64 `json:"pages"`
}
```

### 5.3 基础响应结构
- **文件位置**: `model/dto/common/base_resp.go`
```go
type BaseResp struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}
```

## 6. 验证规则汇总

### 6.1 字段验证标签
- **binding标签**: Gin框架验证
  - `required`: 必填字段
  - `omitempty`: 可选字段
  - `min/max`: 数值范围
  - `email`: 邮箱格式

- **validate标签**: 自定义验证
  - `oneof`: 枚举值验证
  - `min/max`: 长度或数值验证
  - `email`: 邮箱格式验证

### 6.2 业务验证规则
- **{Entity1}验证**:
  - {Field1}: {validation_rule_1}
  - {Field2}: {validation_rule_2}
  - {Field3}: {validation_rule_3}

- **{Entity2}验证**:
  - [类似上述结构]

### 6.3 通用验证规则
- 分页参数: page≥1, pageSize∈[1,100]
- 时间范围: startTime ≤ endTime
- 关键词搜索: 长度≤50字符
- ID字段: 必须大于0

## 7. JSON序列化规范

### 7.1 命名规范
- ✅ 所有JSON字段使用小驼峰命名
- ✅ 时间字段统一为毫秒时间戳
- ✅ 布尔字段使用is前缀
- ✅ 状态字段提供数值和文本两种形式

### 7.2 数据类型规范
- ✅ 整数统一使用int64
- ✅ 时间使用int64毫秒时间戳
- ✅ 金额使用int64分为单位
- ✅ 可选字段使用指针类型

### 7.3 响应格式规范
- ✅ 统一使用BaseResp包装
- ✅ 列表响应包含分页信息
- ✅ 错误响应包含错误码和消息
- ✅ 成功响应code=0

## 8. 性能优化特性

### 8.1 转换器优化
- ✅ 批量转换方法实现
- ✅ 空值检查避免panic
- ✅ 内存预分配优化
- ✅ 避免不必要的内存拷贝

### 8.2 序列化优化
- ✅ 使用json标签优化序列化
- ✅ omitempty减少传输数据
- ✅ 合理的结构体字段顺序
- ✅ 避免深层嵌套结构

### 8.3 验证优化
- ✅ 快速失败验证策略
- ✅ 缓存验证结果
- ✅ 批量验证支持
- ✅ 自定义验证器复用

## 9. 测试覆盖情况

### 9.1 转换器测试
- **{Entity1}Converter测试覆盖率**: {converter1_coverage}%
- **{Entity2}Converter测试覆盖率**: {converter2_coverage}%
- **{Entity3}Converter测试覆盖率**: {converter3_coverage}%

### 9.2 测试场景
- ✅ 正常转换场景测试
- ✅ 空值处理测试
- ✅ 边界值测试
- ✅ 错误输入测试
- ✅ 性能基准测试

### 9.3 验证测试
- ✅ 请求参数验证测试
- ✅ 业务规则验证测试
- ✅ 边界条件验证测试
- ✅ 错误消息验证测试

## 10. 下一Phase输入资源

### 10.1 可用请求DTO
- `{Entity1}CreateReq` - 支持完整字段验证
- `{Entity1}UpdateReq` - 支持部分字段更新
- `{Entity1}ListReq` - 支持多条件查询
- `{Entity2}CreateReq` - [类似功能]
- `{Entity2}UpdateReq` - [类似功能]
- `{Entity2}ListReq` - [类似功能]

### 10.2 可用响应DTO
- `{Entity1}Resp` - 完整实体响应
- `{Entity1}ListResp` - 分页列表响应
- `{Entity2}Resp` - [类似功能]
- `{Entity2}ListResp` - [类似功能]

### 10.3 可用转换器
- `{Entity1}Converter` - 完整转换功能
- `{Entity2}Converter` - 完整转换功能
- `{Entity3}Converter` - 完整转换功能

### 10.4 验证规则
- 所有请求DTO包含完整验证规则
- 支持Gin binding验证
- 支持自定义validate验证
- 错误消息本地化支持

## 11. Service层开发指导

### 11.1 DTO使用建议
- Service方法接收请求DTO作为参数
- Service方法返回响应DTO
- 使用Converter进行DTO与Entity转换
- 不在Service层直接操作Entity

### 11.2 转换器使用建议
- 在Service层注入Converter依赖
- 统一使用Converter进行类型转换
- 避免在Controller层进行转换
- 保持转换逻辑的一致性

### 11.3 验证处理建议
- 在Controller层进行参数验证
- Service层可以进行业务逻辑验证
- 使用统一的错误响应格式
- 提供清晰的错误消息

## 12. 注意事项

### 12.1 数据一致性
- 确保DTO字段与Entity字段对应
- 保持JSON命名的一致性
- 统一时间格式处理
- 统一数据类型使用

### 12.2 向后兼容性
- 新增字段使用omitempty标签
- 避免删除已有字段
- 保持API响应格式稳定
- 版本化管理重大变更

### 12.3 安全考虑
- 敏感字段不在响应中返回
- 输入参数进行安全验证
- 防止SQL注入和XSS攻击
- 合理设置字段长度限制

## 13. 验收确认

- ✅ 所有请求DTO定义完成
- ✅ 所有响应DTO定义完成
- ✅ 所有转换器实现完成
- ✅ 验证规则配置完成
- ✅ 单元测试全部通过
- ✅ 性能测试通过
- ✅ 代码评审通过
- ✅ 文档更新完成

**Phase 3 DTO层开发完成，可以进入Phase 4 Service层开发。**
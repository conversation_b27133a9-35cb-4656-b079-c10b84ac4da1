# PRD - AI 课中 - 练习组件 1.0 **版本管理**

<table>
<tr>
<td>版本号<br/></td><td>日期<br/></td><td>变更人<br/></td><td>变更类型<br/></td><td>变更详情<br/></td></tr>
<tr>
<td>V1.0<br/></td><td>2025.04.25<br/></td><td>张博<br/></td><td>新建<br/></td><td>- 新建文档<br/></td></tr>
</table>

**关联需求**

<table>
<tr>
<td>关联需求名称<br/></td><td>所属 PM<br/></td><td>需求进度<br/></td><td>文档链接<br/></td></tr>
<tr>
<td>AI 课中 - V1.0<br/></td><td>ou_b2709be118f67c0f1ae49e028241afd2<br/></td><td>待开发<br/></td><td>[PRD - AI课中 - V1.0](https://wcng60ba718p.feishu.cn/wiki/XtfswiCCQiKHdgkev4Qc1Nqined)<br/></td></tr>
<tr>
<td>题型支持<br/></td><td>ou_4075bb29f2fdec7724d8181104831d94<br/></td><td>UI 中<br/></td><td>[PRD -  题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)<br/></td></tr>
</table>

**设计稿**
视觉稿：待补充

# 一、背景和目标

## 需求背景

为了优化 AI 课中整体学习体验，提升学生作答流畅度和情感反馈体验，本次练习组件在 Demo V1.0 与 V1.1 基础上进行进一步升级。
目标是通过引入更友好的转场动画、作答即时反馈机制，提升课程满意度与学习效果。

## 项目收益

- 优化课中作答体验。
- 通过即时反馈与情感激励，增强学生参与感。

## 覆盖用户

- 使用新 AI 课版本的全量学生用户

## 方案简述

本次方案主要聚焦在练习组件的交互体验升级，包括：

- 进入练习转场动效优化
- 题目间即时反馈机制
- 支持勾画与加入错题本功能复用

## 未来会做什么

1. 引入互动讲题模块，基于题目分布讲解和互动，进一步提升课中练习体验与教学效果。

# **二、名词说明**

暂无

# 三、业务流程

> 整体课程结构和 demo 阶段类似，V1.0 只支持 「文档组件」、「练习组件」、「答疑组件」
> 一节课由不同的可配置组件构成。组件之间不同的组合方式构成了不同的课程类型。
> 答疑组件在课中不能单独配置，需要挂载在「文档组件」/「练习组件」下

<table>
<tr>
<td>单节课程结构<br/></td><td>组件类型<br/></td><td>说明<br/></td></tr>
<tr>
<td rowspan="3">![in_table_IhpkwURCUh57uYbMqBhckYbNnld](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_IhpkwURCUh57uYbMqBhckYbNnld.png)<br/></td><td>文档组件<br/></td><td>课程内容为JS文档形式，支持学生在上课过程中自由浏览，支持点击/长按进行提问<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>支持各类题型（一期仅支持单选），可按策略推送题目<br/></td></tr>
<tr>
<td>答疑组件<br/></td><td>基于大模型能力和学生进行实时对话，解答学生问题<br/></td></tr>
</table>

![in_table_IhpkwURCUh57uYbMqBhckYbNnld](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_IhpkwURCUh57uYbMqBhckYbNnld.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】
该图片展示了“单节课内容”的结构，由三个主要层级构成：顶部的课程流程、中间的支撑组件/页面，以及底部的辅助功能。

**1. 课程核心流程**
单节课内容按以下顺序展开：
```mermaid
flowchart LR
    A[课程开场页] --> B[课程引入]
    B --> C[知识点1]
    C --> D[知识点2]
    D --> E[练习1]
    E --> F[知识点3]
    F --> G[知识点4]
    G --> H[练习2]
    H --> I[课程总结]
    I --> J[学习报告]
```

**2. 支撑组件与辅助功能对应关系**
每个课程流程节点都由特定的支撑组件或页面承载，部分组件还关联了答疑辅助功能：

*   **课程开场页**
    *   支撑：功能页面
*   **课程引入**
    *   支撑：文档组件
    *   辅助：答疑组件
*   **知识点1**
    *   支撑：文档组件
    *   辅助：答疑组件
*   **知识点2**
    *   支撑：文档组件
    *   辅助：答疑组件
*   **练习1**
    *   支撑：练习组件
    *   辅助：答疑组件
*   **知识点3**
    *   支撑：文档组件
    *   辅助：答疑组件
*   **知识点4**
    *   支撑：文档组件
    *   辅助：答疑组件
*   **练习2**
    *   支撑：练习组件
    *   辅助：答疑组件
*   **课程总结**
    *   支撑：文档组件
    *   辅助：答疑组件
*   **学习报告**
    *   支撑：功能页面

【============== 图片解析 END ==============】



# 四、需求概览

<table>
<tr>
<td>模块<br/></td><td>需求描述<br/></td><td>优先级<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>增加进入转场和题目间转场和反馈。<br/></td><td>P0<br/></td></tr>
</table>

# 五、详细产品方案

## 练习组件

嗯 我现在想着是 进入练习时 老师在但是内容 侧滑进来有个题的转场。
然后老师说完 题和老师滑走，然后进入题目。

<table>
<tr>
<td>模块/功能<br/></td><td>需求描述<br/></td><td>原型图<br/></td></tr>
<tr>
<td>进入/离开转场<br/></td><td>**进入练习提示：**<br/>- 练习组件最顶部，增加“即将进入练习” tip ，预告即将进入练习环节。<br/>- 判断：当练习组件前有其他组件时，展示该提示。<br/><br/>**进入练习转场：**<br/>- 首次进入练习（所有题目未作答时）：- 时机：用户完成课程后，进入练习组件时。- 转场：- 转场动效，翻页。- IP~~动效~~ + 文案。- 文案：开始练习 + 课程名称。- 展示时间： 2 s。<br/>- 再次进入练习- 时机：用户退出课程后再次进入课程时进度在练习组件时，- 转场：- IP 动效 + 文案。- 文案：继续练习 + 课程名称。- 展示时间： 2 s。<br/><br/>**离开练习转场**：<br/>- 时机：用户完成练习后（点击最后一题的继续按钮）。<br/>- 转场：- 转场动效，翻页。<br/></td><td>![in_table_XwKpbrJtaoPYEIxbkDbccUBKnYd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XwKpbrJtaoPYEIxbkDbccUBKnYd.png)<br/><br/>翻页动效 （进入和退出）<br/>![in_table_L4WcbI0zAoClQpx9hBmcJNjXnkd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_L4WcbI0zAoClQpx9hBmcJNjXnkd.png)<br/></td></tr>
<tr>
<td>练习环节<br/></td><td>**页面框架**<br/>- **顶部导航栏：**- 退出按钮：逻辑同[课程框架](https://wcng60ba718p.feishu.cn/wiki/XtfswiCCQiKHdgkev4Qc1Nqined#share-LFnSdUOGPoNj6JxMIkwcQ5vEnHh)。- 作答计时：复用题型组件。- 每题独立正计时（格式 00:00，上限 59:59）。- 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时（例一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。- 作答进度：- 进度展示：- 分母：预估学生作答数量。- 分子：依据推题策略，- 如果学生答对，进度涨。- 如果学生答错，但没有相似题，进度涨。- 如果学生答错，但有相似题需要再练一道，进度不变。- 连对触发效果：- **同巩固练习，**[PRD -  巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-RaX7dZ4RTo1goOxLPVjc5k4vnsd)**。**<br/>- **题型组件：**- 题型组件：各题型在练习中的展示和交互，见[PRD -  题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)。- 题目推荐：见课中题目推荐策略ou_b2709be118f67c0f1ae49e028241afd2，见[PRD - 课中练习推题策略 - V1.0](https://wcng60ba718p.feishu.cn/wiki/VMEawv89PiINjUkaLxncnSYWnqb?source_type=message&from=message&disposable_login_token=eyJ1c2VyX2lkIjoiNzQ1MzA0MDU3OTczMjQ1NTQyNiIsImRldmljZV9sb2dpbl9pZCI6Ijc0NjkzNjk2MDgxOTgzNTY5OTQiLCJ0aW1lc3RhbXAiOjE3NDU3MzMyNDUsInVuaXQiOiJldV9uYyIsInB3ZF9sZXNzX2xvZ2luX2F1dGgiOiIxIiwidmVyc2lvbiI6InYzIiwidGVuYW50X2JyYW5kIjoiZmVpc2h1IiwicGtnX2JyYW5kIjoi6aOe5LmmIn0=.84d78b821498de18a6f5eba14341bd8e413fef02d2cd2cf9739cfb49f4e1b36f)。<br/>- **勾画组件：同巩固练习，**[PRD -  巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-RaX7dZ4RTo1goOxLPVjc5k4vnsd)**。**<br/>- **错题本组件：同巩固练习，**[PRD -  巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-WdgqdEfPKo1X4BxYtJ3c6FXgnhh)**。**<br/>- **答疑组件：**- **入口：**- 右上角常驻展示「问一问」.- 长按题目内容，唤起问一问。**（本期不做）**- 可触发时机：- 题目进入解析状态前屏蔽问一问入口。- 题目进入解析状态才能选中的唤起问一问。- **交互：**- 点击问一问，进入答疑组件。<br/></td><td>![in_table_A0MHb8kW1o0HTPx3oDjcmw8Unfg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_A0MHb8kW1o0HTPx3oDjcmw8Unfg.png)<br/><br/>![in_table_GhmPbn3b9ovENsxSfWZctrAEnGb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GhmPbn3b9ovENsxSfWZctrAEnGb.png)<br/><br/></td></tr>
<tr>
<td>题目间转场<br/></td><td>- **逻辑和展示同巩固练习，**[PRD -  巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-Sf2FdTzLqoUffPxG0hccJShLnKb)**。**<br/><br/></td><td>![in_table_GFWobx4zHo13YlxhwhlccN3GnRe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GFWobx4zHo13YlxhwhlccN3GnRe.png)<br/>![in_table_BosmbvrKeo8580x9RqycMEZ7nYb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BosmbvrKeo8580x9RqycMEZ7nYb.png)<br/></td></tr>
</table>

![in_table_XwKpbrJtaoPYEIxbkDbccUBKnYd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XwKpbrJtaoPYEIxbkDbccUBKnYd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片展示了一个在线教育课程的学习界面，主要分为左右两部分：左侧为课程内容展示区，右侧为课程目录及进度导航区。

**一、左侧内容展示区 (主题: 等差数列的表示方法)**

*   **顶部标题**:
    *   图标: 书本堆叠图标
    *   文字: "等差数列的表示方法"
*   **内容模块1**: "1 表示方法选择"
    *   说明文字: "数列有多种表示方法，但用图像或表格表示等差数列较复杂，所以考虑用递推公式或通项公式表示。" (图片中此段文字下方有带数字12, 36, 6的小圆圈标记，推测为注释或标记点，OCR文本未包含这些数字标记。)
*   **内容模块2**: "2 递推公式"
    *   引言文字: "因为等差数列从第二项起，每一项与前一项的差是常数 \(d\)，"
    *   递推公式展示:
        *   "所以递推公式为"
            $$
            a_{n+1} - a_n = d
            $$
        *   "也可写成"
            $$
            a_n - a_{n-1} = d \quad (n \geq 2)
            $$
        *   "也可写成" (图片中可见此行及下方公式，与上一条公式视觉上相同，OCR文本未显式列出此第三条公式)
            $$
            a_n - a_{n-1} = d \quad (n \geq 2)
            $$
*   **底部按钮**:
    *   文字: "即将进入练习"

**二、右侧课程目录与进度导航区**

此区域以垂直时间线形式展示课程的各个节点及其状态。

*   **节点1**: "课程引入"
    *   状态图标: 完成/已学习标记 (橙色勾选图标)
*   **节点2**: "复数的分类"
    *   状态图标: 完成/已学习标记 (橙色勾选图标)
*   **节点3**: "复数相等的条件"
    *   状态图标: 当前学习标记 (橙色实心圆点)
    *   进度显示: "00:32 / 02:44"
*   **节点4**: "随堂练习1"
    *   状态图标: 编辑/练习标记 (铅笔图标)
    *   附加状态: 锁定 (右侧有灰色锁形图标)
*   **节点5**: "标题字数多折行展示效果如图"
    *   状态图标: 未学习/普通节点标记 (灰色虚线圆点)
    *   附加状态: 锁定 (右侧有灰色锁形图标)
*   **节点6**: "随堂练习2"
    *   状态图标: 编辑/练习标记 (铅笔图标)
    *   附加状态: 锁定 (右侧有灰色锁形图标)
*   **节点7**: "复数的分类" (与节点2文本重复)
    *   状态图标: 未学习/普通节点标记 (灰色虚线圆点)
    *   附加状态: 锁定 (右侧有灰色锁形图标)
*   **节点8**: "完成"
    *   状态图标: 目标/完成标记 (旗帜图标)

【============== 图片解析 END ==============】

![in_table_L4WcbI0zAoClQpx9hBmcJNjXnkd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_L4WcbI0zAoClQpx9hBmcJNjXnkd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

本文档包含多张UI界面截图，展示了在线教育产品中“练习”模块的不同界面和交互状态。

**一、 概念学习与练习引导界面 (进入练习)**

该界面用于概念讲解，并引导用户进入练习环节。

*   **顶部导航栏:**
    *   左侧返回箭头按钮。
    *   右侧“问”图标按钮和“三”(列表/菜单)图标按钮。
*   **内容区域 - 数学公式及解释:**
    *   **移项得:**
        $$
        a_n = a_1 + (n-1)d
        $$
        条件：$n \ge 2$，$n=1$ 时也成立。
    *   **5 基本量:**
        *   文本说明: 等差数列的首项 $a_1$ 和公差 $d$ 是基本量，知道它们就能求出数列任意一项。
*   **互动与引导:**
    *   **插画:** 包含三个卡通学生形象。
        *   学生A (男) 对话框: "下一步?"
        *   学生B (女) 对话框: "加油哦! 还差一步~"
    *   **按钮:** "即将进入练习"
*   **讲师形象:** 右下角有讲师人物图片。

**二、 练习答题界面 (退出练习)**

该界面为用户进行题目练习的界面。

*   **顶部状态栏:** 显示时间 "7:35 Mon Jun 3", 网络信号, 电量 "100%"。
*   **导航栏:**
    *   左侧返回箭头 "<"。
    *   标题: "用时 01:24"。
*   **题目区域:**
    *   **题型与来源:** "单选 (2024春・浙江期中)"
    *   **题干:** "在四面体ABCD中, BCD为等边三角形∠ADB=pai/2, 二面角B-AD-C的大小为a, 则a的取值范围是 ( )" (注: pai/2 通常表示 $\pi/2$)
    *   **几何图形:** 四面体ABCD的示意图。
    *   **选项 (状态一: 未作答，有卡通形象引导):**
        *   卡通小鹿形象，对话框: "让我们进入练习空间几何~"
        *   A (0, pai/6]
        *   B (0, pai/6]
        *   C (0, pai/6]
        *   D (0, pai/6]
    *   **选项 (状态二: 已作答或展示答案解析):**
        *   A (0, pai/6] (选择占比 20.9%)
        *   B (0, pai/6] (已选，正确，选择占比 10.8% ✔️)
        *   C (0, pai/6] (选择占比 50.2%)
        *   D (0, pai/6] (选择占比 18.1%)
*   **答案与解析区域 (仅状态二可见):**
    *   **正确答案:** B
    *   **题目解析:**  (显示“题目解析题目解析题目解析题解析...”占位符文本)
*   **底部操作栏:**
    *   按钮: "加入错题本"
    *   按钮: "继续"

**三、 等差数列实例引入界面**

该界面通过实例向用户介绍等差数列。

*   **顶部导航栏:**
    *   右侧“问”图标按钮和“三”(列表/菜单)图标按钮。
*   **内容区域 - 标题:** "等差数列的实例引入"
*   **内容区域 - 分点阐述:**
    *   **1 实例列举:**
        *   2022年9月每个星期五的日期: 2, 9, 16, 23, 30构成数列。
        *   男装不同型号对应的尺码: 36, 38, 40, 42, 44, 46, 48构成数列。
        *   负奇数从大到小排列: -1, -3, -5, -7...构成数列。
    *   **2 特殊数列研究的必要性:**
        *   文本说明: 学习函数时, 需先掌握基础知识, 再研究特殊函数以加深理解。对于数列而言, 同样存在值得研究的特殊数列。
    *   **3 规律探索**
*   **讲师形象:** 右下角有讲师人物图片。

【============== 图片解析 END ==============】

![in_table_A0MHb8kW1o0HTPx3oDjcmw8Unfg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_A0MHb8kW1o0HTPx3oDjcmw8Unfg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线答题界面的截图，主要包含以下元素：

1.  **顶部状态与导航栏 (Header & Navigation):**
    *   左上角：返回按钮 (向左箭头图标)。
    *   顶部中央：计时器，显示“用时 01:24”。
    *   计时器下方：一个水平的进度条，大部分为橙色，小部分为灰色。
    *   右上角：“课程进度”按钮，附带一个时钟和勾选的图标。

2.  **题目内容区域 (Question Content Area):**
    *   题型及来源信息： "单选 (2024春·浙江期中)"。
    *   题目描述文本：
        "在四面体ABCD中，BCD为等边三角形$\angle ADB=\frac{\pi}{2}$，二面角B-AD-C的大小为$\alpha$，则$\alpha$的取值范围是（ ）"
    *   几何图形：一个四面体ABCD的线框图，其中BD为虚线。
        *   图形右下角：一个放大镜图标（表示可缩放/查看大图）。
    *   辅助功能按钮（位于题目描述区域左下角）：“勾画”。

3.  **选项区域 (Answer Options Area):**
    *   选项A: $(0, \frac{\pi}{6}]$
    *   选项B: $(0, \frac{\pi}{6}]$
    *   选项C: $(0, \frac{\pi}{6}]$
    *   选项D: $(0, \frac{\pi}{6}]$

4.  **底部操作栏 (Action Bar):**
    *   左侧按钮：“不确定”。
    *   右侧按钮：“提交”（橙色背景）。

【============== 图片解析 END ==============】

![in_table_GhmPbn3b9ovENsxSfWZctrAEnGb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GhmPbn3b9ovENsxSfWZctrAEnGb.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线教育产品中的习题作答与解析界面。

**1. 页面整体布局与元素**

*   **顶部状态栏 (System Status Bar)**
    *   时间显示: `7:35 Mon Jun 3`
    *   系统图标: Wi-Fi, 信号强度, 电池电量 (`100%`)
*   **应用导航栏 (App Navigation Bar)**
    *   返回按钮: `<` 图标
    *   用时显示: `用时 01:24`
    *   答题进度提示: `连对5题` (配有进度条)
    *   辅助功能按钮:
        *   `问一问`
        *   `课程进度`
*   **题目区域 (Question Area)**
    *   题型与来源: `单选 (2024春·浙江期中)`
    *   题干描述:
        *   文字: "在四面体ABCD中，BCD为等边三角形ADB=pai/2，二面角B-AD-C的大小为a，则a的取值范围是（）"
        *   几何图形: 四面体ABCD的示意图。
*   **选项区域 (Options Area)**
    *   选项A: `(0, pai/6]`，选择比例 `20.9%`
    *   选项B: `(0, pai/6]`，选择比例 `10.8%` (用户已选，绿色高亮，带绿色对勾及星形标记)
    *   选项C: `(0, pai/6]`，选择比例 `50.2%`
    *   选项D: `(0, pai/6]`，选择比例 `18.1%`
*   **答案与解析区域 (Answer and Explanation Area)**
    *   正确答案提示: `正确答案：B`
    *   题目解析标题: `题目解析`
    *   题目解析内容: "题目解析题目解析题目解析题析题品领长照 解析题目解析题目解析题目" (实际内容为占位符或重复文本)
*   **底部操作按钮区域 (Action Buttons Area)**
    *   `加入错题本` 按钮
    *   `继续` 按钮

**2. 元素层级关系**

```
页面
├── 顶部状态栏
│   ├── 时间
│   └── 系统图标 (Wi-Fi, 信号, 电池)
├── 应用导航栏
│   ├── 返回按钮
│   ├── 用时
│   ├── 答题进度
│   ├── 问一问按钮
│   └── 课程进度按钮
├── 题目区域
│   ├── 题型与来源
│   └── 题干
│       ├── 文字描述
│       └── 几何图形
├── 选项区域
│   ├── 选项A (内容, 选择比例)
│   ├── 选项B (内容, 选择比例, 用户选择标记, 正确标记)
│   ├── 选项C (内容, 选择比例)
│   └── 选项D (内容, 选择比例)
├── 答案与解析区域
│   ├── 正确答案
│   └── 题目解析
│       ├── 标题
│       └── 内容
└── 底部操作按钮区域
    ├── 加入错题本按钮
    └── 继续按钮
```

**3. 图片内关键数学公式**

根据题干及选项内容，关键数学表达式如下：

*   角度关系:
    $$
    \angle ADB = \pi/2
    $$
*   取值范围 (以选项A为例，其余选项格式相同):
    $$
    a \in (0, \pi/6]
    $$

【============== 图片解析 END ==============】

![in_table_GFWobx4zHo13YlxhwhlccN3GnRe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GFWobx4zHo13YlxhwhlccN3GnRe.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片为在线教育产品中的单选题答题结果反馈界面。

**1. 页面整体结构**
*   **顶部导航栏**
    *   左侧：返回按钮（`<` 图标）。
    *   中间：用时信息 “用时 01:24”。
    *   右侧：进度条（橙色，有分段标记）。
*   **题目内容区域**
    *   **题型及来源**：
        *   “单选”
        *   “(2024春·浙江期中)”
    *   **题干**：
        *   文字描述：“在四面体ABCD中，BCD为等边三角形，ADB=$\pi/2$，二面角B-AD-C的大小为a，则a的取值范围是（ ）”
        *   配图：一个四面体的几何图形，标注了顶点B、C。图形右下角有放大镜图标。
*   **选项区域** (垂直排列)
    *   **选项 A**：
        *   内容：“(0, $\pi/6$]”
        *   选择比例：“20.9%”
    *   **选项 B**：
        *   内容：“(0, $\pi/6$]”
        *   状态：已选择，回答正确（绿色背景高亮，绿色对勾图标，星星闪烁动画提示）。
        *   选择比例：“10.8%”
    *   **选项 C**：
        *   内容：“(0, $\pi/6$]”
        *   选择比例：“50.2%”
    *   **选项 D**：
        *   内容：“(0, $\pi/6$]”
        *   选择比例：“18.1%”
*   **答题反馈区域**
    *   卡通形象（鹿，竖大拇指）。
    *   反馈文字（气泡框内）：“太棒了！同学巩固得很牢固”。
*   **答案及解析区域**
    *   正确答案标识：“B”
    *   标题：“题目解析”
    *   解析内容：占位文本，如“题目解析题目解析题目解析题目解析题解析日领长 解析题目解析题目解析题目”。
*   **底部操作按钮区域**
    *   左侧按钮：“加入错题本”（白色背景，黑色文字）。
    *   右侧按钮：“继续”（绿色背景，白色文字）。

**2. 关键元素关联**
*   计时器与进度条共同展示用户答题的进度和耗时。
*   题干、配图、选项共同构成题目本身。
*   选项B的绿色高亮、对勾图标、星星动画以及下方的反馈文字和卡通形象，共同构成了对用户正确答题的积极反馈。
*   正确答案标识“B”与选项B内容对应。
*   “题目解析”区域为用户提供解题思路和方法。
*   “加入错题本”和“继续”按钮为用户提供后续操作路径。

【============== 图片解析 END ==============】

![in_table_BosmbvrKeo8580x9RqycMEZ7nYb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BosmbvrKeo8580x9RqycMEZ7nYb.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片为在线教育产品中的一道数学题目练习界面截图，展示了用户答题后的反馈。

**1. 界面顶部信息区:**
    *   **日期与用时:**
        *   日期: "Mon Jun 3"
        *   用时: "01:24"
    *   **答题进度/状态:** "连对5题" (Indicates a streak of 5 correct answers)

**2. 题目内容区:**
    *   **题目类型与来源:** "单选 (2024春·浙江期中)" (Single-choice question from Zhejiang Midterm, Spring 2024)
    *   **题干:**
        *   文字描述: "在四面体ABCD中, BCD为等边三角形 ADB=pai/2, 二面角B-AD-C的大小为a, 则a的取值范围是 ( )"
        *   Markdown 数学公式:
            $$
            \text{在四面体ABCD中, BCD为等边三角形 } \angle ADB = \pi/2 \text{, 二面角B-AD-C的大小为a, 则a的取值范围是 ( )}
            $$
    *   **几何图形示意图:**
        *   部分显示的四面体ABCD，标注了顶点B、C。
    *   **选项区:**
        *   **A.** (0, pai/6] (选择此项的用户占比 20.9%)
            *   Markdown 수학 공식: $(0, \pi/6]$
        *   **B.** (0, pai/6] (绿色高亮，带✓标记，表示用户选择正确，选择此项的用户占比 10.8%)
            *   Markdown 수학 공식: $(0, \pi/6]$
        *   (其他选项部分可见，其中一个选项选择占比为 50.2%)

**3. 答题反馈弹窗 (居中叠加):**
    *   **祝贺信息:** "太棒了!"
    *   **成就描述:** "连对5题! 实力派666"
    *   **装饰性元素:** 卡通小鹿竖起大拇指的形象。

**4. 题目解析区 (位于题目内容下方):**
    *   **标题:** "题目解析"
    *   **内容:** 显示为重复的占位文字 "题目解析题目解析题目解析..."
    *   **交互按钮:** "继续" 按钮

**元素间关联:**
*   顶部的 "连对5题" 与弹窗中的 "连对5题! 实力派666" 相互呼应，是基于用户答题表现（正确回答了当前题目并达成连对）触发的即时反馈。
*   题目内容区（题干、图形、选项）构成一个完整的单选题。
*   选项B被标记为正确答案。
*   "题目解析" 区预期提供该题的解题思路和答案说明，当前为占位内容。
*   "继续" 按钮用于导航至下一内容或环节。

【============== 图片解析 END ==============】



# 七、数据需求

## 埋点需求

<table>
<tr>
<td>模块<br/></td><td>页面<br/></td><td>动作<br/></td><td>埋点<br/></td><td>备注<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>练习页面<br/></td><td>加载题目<br/></td><td>exercise_question_load<br/></td><td>每道题加载完成<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>练习页面<br/></td><td>提交答案<br/></td><td>exercise_answer_submit<br/></td><td>记录作答时间、答案内容、是否正确<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>练习页面<br/></td><td>点击退出按钮<br/></td><td>exercise_exit_confirm_click<br/></td><td>退出时是否保存进度<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>练习页面<br/></td><td>勾画操作<br/></td><td>drawing_action<br/></td><td>包括勾画/橡皮擦/清除/撤销/恢复<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>练习页面<br/></td><td>错题收藏/取消<br/></td><td>mistakebook_toggle_click<br/></td><td>收藏或取消收藏错题动作<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>作答后反馈页<br/></td><td>展示作答反馈<br/></td><td>answer_feedback_show<br/></td><td>正确/错误/连胜类型<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>难度变化提示页<br/></td><td>展示难度调整提示<br/></td><td>difficulty_change_feedback_show<br/></td><td>上升/下降类型<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>作答过程中<br/></td><td>展示不认真作答提示<br/></td><td>inattentive_feedback_show<br/></td><td>作答过快/异常行为触发<br/></td></tr>
</table>

## 数据存储需求

<table>
<tr>
<td>模块<br/></td><td>数据项<br/></td><td>说明<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>学生选择的答题选项<br/></td><td>包括选择内容、是否确定提交<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>每题作答时长<br/></td><td>单题独立正计时，精确到秒<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>每题作答正确/错误标识<br/></td><td>用于后续练习效果统计<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>连续正确次数<br/></td><td>记录连胜链条<br/></td></tr>
<tr>
<td>练习组件<br/></td><td>是否触发难度上升/下降<br/></td><td>标记作答后难度变化情况<br/></td></tr>
</table>
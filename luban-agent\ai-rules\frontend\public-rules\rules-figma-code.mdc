---
description: 
globs: 
alwaysApply: false
---
以下是针对 Figma-to-Code 转换任务的标准化规则（Rules），涵盖组件拆分、代码规范、协作流程等关键环节，适用于 React + TypeScript + Tailwind  技术栈：


---

### Figma-to-Code Rules  
#### 1. 组件拆分原则  
- 原子化设计：  
- 按 原子（Button/Input）→ 分子（Card/Modal）→ 模板（Page Section） 层级拆分。  
- 示例：一个 LoginForm 应由 Input、Button、Checkbox 等原子组件组合而成。  

#### 2. 代码规范  
- 文件结构：  
src/
├── components/
│   ├── homepage/
│   │      ├── HomePageButton.tsx
│   │      ├── HomePageInput.tsx
│   │      └── ...
│   ├── aboutpage/
│   │   │   ├── AboutPageNavbar.tsx
│   │   │   ├── AboutPageCard.tsx
│   │   │   └── ...
│   │   │   └── ...
│   ├── common/
│       ├── Header.tsx
│       ├── Footer.tsx
│       └── ...
│       
├── pages/
│   ├── HomePage.tsx
│   ├── AboutPage.tsx
│   └── ...
├── styles/
│   └── globals.css
├── types/                    # 全局类型定义
├── utils/                    # 工具函数
└── lib/                      # 第三方集成/SDK封装
- React + TS：  
- 每个组件需定义清晰的 Props 接口，禁止 any 类型。  
- 示例：  
interface ButtonProps {
  variant: 'primary' | 'secondary';
  onClick: () => void;
  children: React.ReactNode;
}
- Tailwind CSS：  
- 禁用 @apply，直接使用工具类（如 className="px-4 py-2 bg-blue-500"）。  
- 颜色/间距需严格对照 Figma 设计稿的 Style Guide。  

#### 3. 协作流程  
- Figma 标注：  
- 开发前需在 Figma 中标记：  
  - 组件边界（用 Frames 划分）。  
  - 动态数据区域（如 {userName} 文本需通过 props 传入）。  
- 代码审查：  
  - 提交 PR 时需附上 Figma 链接 和 截图对比。  
- 审查重点：  
  - 组件拆分合理性（是否过度耦合）。  
  - 类型安全（避免 as 强制类型断言）。  

#### 4. 异常处理  
- 模糊设计：  
- 如 Figma 缺失状态（如 hover、disabled），优先联系设计师确认。  
- 临时方案：在代码中注释 // TODO: 需补充 hover 样式（Figma 未提供）。  
- 性能优化：  
- 避免重复渲染：对高频更新组件用 React.memo。  
- 图标优先使用 SVG 而非图片。  
- 新增处理：
- 如果在创建的过程中需要创建新的文件夹和文件，请和我确认过后再进行创建，确认完后则立即进行创建。
- 在创建完所有任务之后，请检查所有你创建的文件是否有报错，如果有错误请解决。
- 如果需要添加新配置，请直接添加。
- 如果需要下载新依赖，请直接下载。
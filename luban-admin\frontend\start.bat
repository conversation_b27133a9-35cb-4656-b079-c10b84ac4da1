@echo off
echo 鲁班前端启动脚本 - Windows 版本
echo ==========================================
echo.
echo 请选择启动模式:
echo 1. 开发环境 - 本地API (localhost:8000)
echo 2. 开发环境 - 远程API (*************:8000)
echo 3. 测试环境
echo 4. 自定义端口 (3001)
echo 5. 默认开发环境
echo.
set /p choice=请输入选项 (1-5): 

REM 快速检查关键环境
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

where npm >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误: npm 未安装，请先安装 npm
    pause
    exit /b 1
)

echo 环境检查通过，清理缓存...

REM 清理 Next.js 缓存
if exist ".next" (
    echo 清理 .next 目录...
    rmdir /s /q ".next" 2>nul
)

REM 清理 node_modules 缓存
if exist "node_modules\.cache" (
    echo 清理 node_modules\.cache 目录...
    rmdir /s /q "node_modules\.cache" 2>nul
)

echo 缓存清理完成，启动开发服务器...

if "%choice%"=="1" (
    echo 使用本地API启动...
    set NEXT_PUBLIC_USE_REMOTE_API=false
    set NEXT_PUBLIC_API_HOST=http://localhost:8000
    npm run dev
) else if "%choice%"=="2" (
    echo 使用远程API启动...
    set NEXT_PUBLIC_USE_REMOTE_API=true
    set NEXT_PUBLIC_API_HOST=http://*************:8000
    npm run dev
) else if "%choice%"=="3" (
    echo 使用测试环境配置启动...
    set NODE_ENV=test
    npm run dev
) else if "%choice%"=="4" (
    echo 在3001端口启动...
    set PORT=3001
    npm run dev
) else (
    echo 使用默认配置启动...
    npm run dev
)

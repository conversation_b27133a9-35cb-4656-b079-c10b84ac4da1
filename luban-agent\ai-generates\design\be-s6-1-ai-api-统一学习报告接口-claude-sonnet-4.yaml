openapi: 3.0.3
info:
  title: 统一学习报告接口 API
  description: 提供学生学习报告查询和学习任务管理的统一接口服务，支持AI课中学习、巩固练习、课程任务、作业任务、自学等多种学习场景
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>

servers:
  - url: https://api.example.com
    description: 生产环境
  - url: https://api-staging.example.com
    description: 测试环境

security:
  - BearerAuth: []

paths:
  /api/v1/study_reports:
    get:
      tags:
        - 学习报告
      summary: 获取学习报告列表
      description: 获取学生的学习报告列表，支持历史报告查询和多维度筛选，包含分页功能
      operationId: getStudyReportsList
      parameters:
        - name: studentId
          in: query
          description: 学生ID，从Token中获取，用于权限验证
          required: false
          schema:
            type: string
            example: "student_789"
        - name: reportType
          in: query
          description: 报告类型筛选
          required: false
          schema:
            type: string
            enum: [course_study, exercise_practice, course_task, homework_task, self_study]
            example: "course_task"
        - name: courseId
          in: query
          description: 课程ID筛选
          required: false
          schema:
            type: string
            example: "course_101"
        - name: taskId
          in: query
          description: 任务ID筛选
          required: false
          schema:
            type: string
            example: "task_789"
        - name: startDate
          in: query
          description: 开始日期筛选（按完成时间），格式：YYYY-MM-DD
          required: false
          schema:
            type: string
            format: date
            example: "2025-06-01"
        - name: endDate
          in: query
          description: 结束日期筛选（按完成时间），格式：YYYY-MM-DD
          required: false
          schema:
            type: string
            format: date
            example: "2025-06-06"
        - name: dateRange
          in: query
          description: 预设时间范围
          required: false
          schema:
            type: string
            enum: [today, week, month, quarter]
            example: "month"
        - name: status
          in: query
          description: 完成状态筛选
          required: false
          schema:
            type: string
            enum: [completed, all]
            example: "completed"
        - name: page
          in: query
          description: 页码，从1开始
          required: false
          schema:
            type: integer
            format: int64
            minimum: 1
            default: 1
            example: 1
        - name: pageSize
          in: query
          description: 每页数量，最大100
          required: false
          schema:
            type: integer
            format: int64
            minimum: 1
            maximum: 100
            default: 20
            example: 20
        - name: sortBy
          in: query
          description: 排序字段
          required: false
          schema:
            type: string
            enum: [generatedAt, completionTime, masteryChange]
            default: "completionTime"
            example: "completionTime"
        - name: sortType
          in: query
          description: 排序方式
          required: false
          schema:
            type: string
            enum: [asc, desc]
            default: "desc"
            example: "desc"
      responses:
        '200':
          description: 获取学习报告列表成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudyReportsListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未认证或认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/study_reports/detail:
    get:
      tags:
        - 学习报告
      summary: 获取学习报告详情
      description: 获取单个学习报告的详细内容，包含完整的学习统计、掌握度分析、IP反馈等信息
      operationId: getStudyReportDetail
      parameters:
        - name: reportId
          in: query
          description: 报告ID，用于查看历史报告详情
          required: false
          schema:
            type: string
            example: "report_456"
        - name: sessionId
          in: query
          description: 学习会话ID，用于获取特定会话的报告
          required: false
          schema:
            type: string
            example: "session_123456"
        - name: taskId
          in: query
          description: 任务ID，用于获取特定任务的报告
          required: false
          schema:
            type: string
            example: "task_789"
        - name: studentId
          in: query
          description: 学生ID，从Token中获取，用于权限验证
          required: false
          schema:
            type: string
            example: "student_789"
      responses:
        '200':
          description: 获取学习报告详情成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudyReportDetailResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未认证或认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: 报告不存在
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/v1/study_tasks:
    get:
      tags:
        - 学习任务
      summary: 获取学习任务列表
      description: 获取学生的所有学习任务列表，包括课程任务、作业任务、自学记录，支持多维度筛选和分页
      operationId: getStudyTasksList
      parameters:
        - name: studentId
          in: query
          description: 学生ID，从Token中获取
          required: true
          schema:
            type: string
            example: "student_789"
        - name: taskType
          in: query
          description: 任务类型筛选
          required: false
          schema:
            type: string
            enum: [course_task, homework_task, self_study]
            example: "course_task"
        - name: status
          in: query
          description: 完成状态筛选
          required: false
          schema:
            type: string
            enum: [pending, completed, overdue]
            example: "completed"
        - name: startDate
          in: query
          description: 开始日期筛选，格式：YYYY-MM-DD
          required: false
          schema:
            type: string
            format: date
            example: "2025-06-01"
        - name: endDate
          in: query
          description: 结束日期筛选，格式：YYYY-MM-DD
          required: false
          schema:
            type: string
            format: date
            example: "2025-06-06"
        - name: dateRange
          in: query
          description: 预设时间范围
          required: false
          schema:
            type: string
            enum: [today, week, month, quarter]
            example: "month"
        - name: courseId
          in: query
          description: 课程ID筛选
          required: false
          schema:
            type: string
            example: "course_101"
        - name: page
          in: query
          description: 页码，从1开始
          required: false
          schema:
            type: integer
            format: int64
            minimum: 1
            default: 1
            example: 1
        - name: pageSize
          in: query
          description: 每页数量，最大100
          required: false
          schema:
            type: integer
            format: int64
            minimum: 1
            maximum: 100
            default: 20
            example: 20
        - name: sortBy
          in: query
          description: 排序字段
          required: false
          schema:
            type: string
            enum: [createdAt, deadline, completionTime]
            default: "deadline"
            example: "deadline"
        - name: sortType
          in: query
          description: 排序方式
          required: false
          schema:
            type: string
            enum: [asc, desc]
            default: "desc"
            example: "asc"
      responses:
        '200':
          description: 获取学习任务列表成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudyTasksListResponse'
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: 未认证或认证失败
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: 权限不足
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT Token认证，格式：Bearer <token>

  schemas:
    # 通用响应结构
    ErrorResponse:
      type: object
      required:
        - code
        - message
      properties:
        code:
          type: integer
          format: int64
          description: 业务错误码，0表示成功，非0表示错误
          example: 400001
        message:
          type: string
          description: 用户可读的错误信息
          example: "参数错误"
        detail:
          type: string
          description: 错误的详细信息
          example: "studentId参数不能为空"
        data:
          type: object
          nullable: true
          description: 错误时通常为空
        pageInfo:
          type: object
          nullable: true
          description: 错误时通常为空

    PageInfo:
      type: object
      required:
        - currentPage
        - pageSize
        - totalCount
        - totalPages
      properties:
        currentPage:
          type: integer
          format: int64
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          format: int64
          description: 每页数量
          example: 20
        totalCount:
          type: integer
          format: int64
          description: 总记录数
          example: 45
        totalPages:
          type: integer
          format: int64
          description: 总页数
          example: 3

    # 学习报告列表响应
    StudyReportsListResponse:
      type: object
      required:
        - code
        - message
        - data
        - pageInfo
      properties:
        code:
          type: integer
          format: int64
          description: 业务错误码，0表示成功
          example: 0
        message:
          type: string
          description: 响应消息
          example: "获取成功"
        data:
          type: array
          items:
            $ref: '#/components/schemas/StudyReportSummary'
        pageInfo:
          $ref: '#/components/schemas/PageInfo'

    StudyReportSummary:
      type: object
      required:
        - reportId
        - reportType
        - courseId
        - courseName
        - generatedAt
        - studyDuration
        - masteryChange
        - accuracyRate
        - summary
      properties:
        reportId:
          type: string
          description: 报告ID
          example: "report_123456"
        reportType:
          type: string
          description: 报告类型
          enum: [course_study, exercise_practice, course_task, homework_task, self_study]
          example: "course_task"
        taskId:
          type: string
          description: 任务ID（如果是任务相关报告）
          example: "task_789"
        taskName:
          type: string
          description: 任务名称（如果是任务相关报告）
          example: "AI基础概念学习"
        courseId:
          type: string
          description: 课程ID
          example: "course_101"
        courseName:
          type: string
          description: 课程名称
          example: "AI基础课程"
        generatedAt:
          type: integer
          format: int64
          description: 报告生成时间（UTC秒数）
          example: 1717646400
        completionTime:
          type: integer
          format: int64
          description: 学习完成时间（UTC秒数）
          example: 1717646400
        studyDuration:
          type: integer
          format: int64
          description: 学习时长（秒）
          example: 1800
        masteryChange:
          type: number
          format: double
          description: 掌握度变化
          example: 12.7
        accuracyRate:
          type: number
          format: double
          description: 正确率
          example: 75.0
        summary:
          type: string
          description: 报告摘要
          example: "完成AI基础课程学习，掌握度提升12.7%"

    # 学习报告详情响应
    StudyReportDetailResponse:
      type: object
      required:
        - code
        - message
        - data
      properties:
        code:
          type: integer
          format: int64
          description: 业务错误码，0表示成功
          example: 0
        message:
          type: string
          description: 响应消息
          example: "获取成功"
        data:
          $ref: '#/components/schemas/StudyReportDetail'

    StudyReportDetail:
      type: object
      required:
        - reportId
        - reportType
        - sessionId
        - studentId
        - courseId
        - courseName
        - generatedAt
        - studyStatistics
        - masteryAnalysis
        - performanceDetails
        - ipFeedback
        - recommendations
      properties:
        reportId:
          type: string
          description: 报告ID
          example: "report_123456"
        reportType:
          type: string
          description: 报告类型
          enum: [course_study, exercise_practice, course_task, homework_task, self_study]
          example: "course_task"
        sessionId:
          type: string
          description: 学习会话ID
          example: "session_123456"
        taskId:
          type: string
          description: 任务ID（如果是任务相关报告）
          example: "task_789"
        studentId:
          type: string
          description: 学生ID
          example: "student_789"
        courseId:
          type: string
          description: 课程ID
          example: "course_101"
        courseName:
          type: string
          description: 课程名称
          example: "AI基础课程"
        taskName:
          type: string
          description: 任务名称（如果是任务相关报告）
          example: "AI基础概念学习"
        taskType:
          type: string
          description: 任务类型（如果是任务相关报告）
          enum: [course_task, homework_task, self_study]
          example: "course_task"
        deadline:
          type: integer
          format: int64
          description: 截止时间（UTC秒数，如果有截止时间）
          example: 1717689599
        completionTime:
          type: integer
          format: int64
          description: 完成时间（UTC秒数）
          example: 1717646400
        generatedAt:
          type: integer
          format: int64
          description: 报告生成时间（UTC秒数）
          example: 1717646400
        studyStatistics:
          $ref: '#/components/schemas/StudyStatistics'
        masteryAnalysis:
          $ref: '#/components/schemas/MasteryAnalysis'
        performanceDetails:
          $ref: '#/components/schemas/PerformanceDetails'
        ipFeedback:
          $ref: '#/components/schemas/IpFeedback'
        recommendations:
          $ref: '#/components/schemas/Recommendations'

    StudyStatistics:
      type: object
      required:
        - totalDuration
        - studyStartTime
        - studyEndTime
      properties:
        totalDuration:
          type: integer
          format: int64
          description: 总学习时长（秒）
          example: 1800
        studyStartTime:
          type: integer
          format: int64
          description: 学习开始时间（UTC秒数）
          example: 1717644600
        studyEndTime:
          type: integer
          format: int64
          description: 学习结束时间（UTC秒数）
          example: 1717646400
        componentCount:
          type: integer
          format: int64
          description: 组件总数
          example: 5
        completedComponents:
          type: integer
          format: int64
          description: 已完成组件数
          example: 5
        interactionCount:
          type: integer
          format: int64
          description: 交互次数
          example: 23
        modeSwitches:
          type: integer
          format: int64
          description: 模式切换次数
          example: 3

    MasteryAnalysis:
      type: object
      required:
        - previousMastery
        - currentMastery
        - masteryChange
        - masteryLevel
        - knowledgePoints
      properties:
        previousMastery:
          type: number
          format: double
          description: 之前掌握度
          example: 65.5
        currentMastery:
          type: number
          format: double
          description: 当前掌握度
          example: 78.2
        masteryChange:
          type: number
          format: double
          description: 掌握度变化
          example: 12.7
        masteryLevel:
          type: string
          description: 掌握度等级
          example: "良好"
        knowledgePoints:
          type: array
          items:
            $ref: '#/components/schemas/KnowledgePoint'

    KnowledgePoint:
      type: object
      required:
        - pointName
        - previousScore
        - currentScore
        - improvement
      properties:
        pointName:
          type: string
          description: 知识点名称
          example: "AI基础概念"
        previousScore:
          type: number
          format: double
          description: 之前得分
          example: 60.0
        currentScore:
          type: number
          format: double
          description: 当前得分
          example: 75.0
        improvement:
          type: number
          format: double
          description: 提升幅度
          example: 15.0

    PerformanceDetails:
      type: object
      required:
        - totalQuestions
        - correctAnswers
        - accuracyRate
        - averageAnswerTime
        - questionDetails
      properties:
        totalQuestions:
          type: integer
          format: int64
          description: 总题目数
          example: 8
        correctAnswers:
          type: integer
          format: int64
          description: 正确答案数
          example: 6
        accuracyRate:
          type: number
          format: double
          description: 正确率
          example: 75.0
        averageAnswerTime:
          type: number
          format: double
          description: 平均答题时间（秒）
          example: 45.5
        questionDetails:
          type: array
          items:
            $ref: '#/components/schemas/QuestionDetail'

    QuestionDetail:
      type: object
      required:
        - questionId
        - isCorrect
        - answerTime
        - difficulty
      properties:
        questionId:
          type: string
          description: 题目ID
          example: "q_001"
        isCorrect:
          type: boolean
          description: 是否正确
          example: true
        answerTime:
          type: integer
          format: int64
          description: 答题时间（秒）
          example: 42
        difficulty:
          type: string
          description: 难度等级
          enum: [easy, medium, hard]
          example: "medium"

    IpFeedback:
      type: object
      required:
        - feedbackType
        - feedbackMessage
        - feedbackAnimation
        - encouragement
      properties:
        feedbackType:
          type: string
          description: 反馈类型
          enum: [celebration, encouragement, guidance]
          example: "celebration"
        feedbackMessage:
          type: string
          description: 反馈消息
          example: "太棒了！你在AI基础概念方面有了很大进步！"
        feedbackAnimation:
          type: string
          description: 反馈动画
          example: "celebration_star"
        encouragement:
          type: string
          description: 鼓励话语
          example: "继续保持这样的学习状态，你会越来越棒的！"

    Recommendations:
      type: object
      required:
        - nextActions
        - practiceSuggestions
      properties:
        nextActions:
          type: array
          items:
            $ref: '#/components/schemas/NextAction'
        practiceSuggestions:
          type: array
          items:
            $ref: '#/components/schemas/PracticeSuggestion'

    NextAction:
      type: object
      required:
        - actionType
        - title
        - description
      properties:
        actionType:
          type: string
          description: 行动类型
          enum: [continue_course, next_chapter, review_content, practice_more]
          example: "continue_course"
        title:
          type: string
          description: 行动标题
          example: "继续下一课程"
        description:
          type: string
          description: 行动描述
          example: "你已经掌握了基础概念，可以进入进阶课程了"

    PracticeSuggestion:
      type: object
      required:
        - suggestionType
        - content
      properties:
        suggestionType:
          type: string
          description: 建议类型
          enum: [review_weak_points, strengthen_practice, expand_knowledge]
          example: "review_weak_points"
        content:
          type: string
          description: 建议内容
          example: "建议复习一下机器学习基础概念"

    # 学习任务列表响应
    StudyTasksListResponse:
      type: object
      required:
        - code
        - message
        - data
        - pageInfo
      properties:
        code:
          type: integer
          format: int64
          description: 业务错误码，0表示成功
          example: 0
        message:
          type: string
          description: 响应消息
          example: "获取成功"
        data:
          type: array
          items:
            $ref: '#/components/schemas/StudyTask'
        pageInfo:
          $ref: '#/components/schemas/PageInfo'

    StudyTask:
      type: object
      required:
        - taskId
        - taskName
        - taskType
        - courseId
        - courseName
        - createdAt
        - status
        - progress
      properties:
        taskId:
          type: string
          description: 任务ID
          example: "task_123456"
        taskName:
          type: string
          description: 任务名称
          example: "AI基础概念学习"
        taskType:
          type: string
          description: 任务类型
          enum: [course_task, homework_task, self_study]
          example: "course_task"
        courseId:
          type: string
          description: 课程ID
          example: "course_101"
        courseName:
          type: string
          description: 课程名称
          example: "AI基础课程"
        chapterName:
          type: string
          description: 章节名称（如果有）
          example: "第一章 AI概述"
        createdAt:
          type: integer
          format: int64
          description: 创建时间（UTC秒数）
          example: 1717574400
        deadline:
          type: integer
          format: int64
          description: 截止时间（UTC秒数，自学任务可能为空）
          example: 1717689599
        status:
          type: string
          description: 任务状态
          enum: [pending, completed, overdue]
          example: "completed"
        completionTime:
          type: integer
          format: int64
          description: 完成时间（UTC秒数，未完成时为空）
          example: 1717646400
        progress:
          type: integer
          format: int64
          description: 进度百分比（0-100）
          example: 100
        estimatedDuration:
          type: integer
          format: int64
          description: 预估时长（秒）
          example: 1800
        actualDuration:
          type: integer
          format: int64
          description: 实际时长（秒）
          example: 1650
        masteryImprovement:
          type: number
          format: double
          description: 掌握度提升（完成后才有值）
          example: 12.7
        accuracyRate:
          type: number
          format: double
          description: 正确率（完成后才有值）
          example: 85.0

tags:
  - name: 学习报告
    description: 学习报告相关接口，包括报告列表查询和详情获取
  - name: 学习任务
    description: 学习任务相关接口，包括任务列表查询和管理
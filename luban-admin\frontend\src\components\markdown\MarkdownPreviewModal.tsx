// @ts-nocheck
'use client';

import { useRef, useEffect } from 'react';
import MarkdownPreview from './MarkdownPreview';

interface MarkdownPreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  downloadUrl: string;
  title?: string;
}

const MarkdownPreviewModal: React.FC<MarkdownPreviewModalProps> = ({
  isOpen,
  onClose,
  downloadUrl,
  title = '文档预览'
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  
  // 添加调试日志
  useEffect(() => {
    if (isOpen) {
      console.log('MarkdownPreviewModal opened with downloadUrl:', downloadUrl);
    }
  }, [isOpen, downloadUrl]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        event.preventDefault();
        event.stopPropagation();
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4 overflow-y-auto">
      <div 
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-5xl max-h-[90vh] flex flex-col"
      >
        <div className="p-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-white z-10">
          <h3 className="text-lg font-medium">{title}</h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        <div className="p-6 overflow-auto flex-grow">
          {/* 添加iframe作为备用显示方式 */}
          <div className="flex flex-col h-full">
            <MarkdownPreview downloadUrl={downloadUrl} />
            
            {/* 如果MarkdownPreview加载失败，显示iframe */}
            <div className="mt-4 border-t pt-4">
              <p className="text-sm text-gray-500 mb-2">如果上方内容加载失败，您可以直接在浏览器中打开链接查看：</p>
              <a
                href={downloadUrl}
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-800 hover:underline"
              >
                {downloadUrl}
              </a>
            </div>
          </div>
        </div>
        
        <div className="p-4 border-t border-gray-200 flex justify-end sticky bottom-0 bg-white">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors mr-2"
          >
            关闭
          </button>
          <a
            href={downloadUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            下载处理结果
          </a>
        </div>
      </div>
    </div>
  );
};

export default MarkdownPreviewModal;
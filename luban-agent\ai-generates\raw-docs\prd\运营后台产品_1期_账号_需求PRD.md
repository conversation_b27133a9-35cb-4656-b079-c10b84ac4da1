运营后台产品_1期_账号_需求PRD 

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0.0 | 02-06 | 袁全 | 新建 | 新建文档 |
| V5.0.0 | 02-28 | 袁全 | 补图 | 完成UI图补图 |
|  | 3-7 | 袁全 | 补图 |  |
|  | 3-13 | 袁全 | 修改了运营账号的入口 | ![in_table_image_OfHebVqH9o8Z7txQvVzcjsZAnkd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524281188.png) |
|  | 3-24 | 袁全 | 修改后端存储方式、前端文案变更 | 重置密码 |
|  | 3-25 | 袁全 | 学生账号扩充：手机号字段作为非必填 | ![in_table_image_I4tXbZqfIoyfncx94aEcR00Gnxd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524281704.png)

下一期扩展：转学、升学后绑定学校数据 |

校级、年级、班级 学生列表 「操作」+「查看密码」

点击后：可查看密码明文，并可复制

![image_Lby4bO4uToIEoBxz9lCc4ptrnAf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524279798.png)

通过“运营后台账户管理”功能维护：可登录运营后台的人员名单

输入：姓名 & 企业feishu对应手机号 创建账号

![image_P2X8b31KboZboyxQez1ckuojneY](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524280593.png)

![analyze_in_table_image_OfHebVqH9o8Z7txQvVzcjsZAnkd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524281188.png)

![analyze_in_table_image_I4tXbZqfIoyfncx94aEcR00Gnxd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524281704.png)

**关联需求**

| 关联需求名称 | 所属PM | 需求进度 | 文档链接 |
| --- | --- | --- | --- |
| 框架&教务需求 | 袁全 | 完成细评，开发中 |  |

**设计稿**

https://mastergo.com/file/149512448061157?fileOpenFrom=project&page_id=0%3A3



### 一、背景和目标

#### 1.1、需求背景

账号部分是整个运营后台产品的最核心模块。

- 业务层面：
- 承载了学生&老师账号信息的创建
- 还将在下一期，结合付费管理流程审批，记录流水和到期预警
- 技术层面：
- 基于中的P0部分，账号模块也是整个系统底层最基本的数据关系
|  | 账号管理 | 合作校管理 | 数据看板 |
| --- | --- | --- | --- |
| P0核心功能 | 学生管理教师管理 |  |  |
| 未来功能 |  | 试用/付费 流程 | 试用校到期数据预警 |

#### 1.2、项目收益

完成运营后台P0核心功能，确保在6月上线的版本可以跑通整个业务MVP流程



#### 1.3、覆盖用户

目标用户：负责管理各学校：学生端、教师端账号的状态创建 和 账号状态的变更



#### 1.3、方案简述

1. 可管理 校内的：班级 及 学生、教师关系的组织管理系统
1. 班级管理
1. 可批量上传学生名单，生成学生账号；支持转班
1. 可批量上传教师名单，生成教师账号；支持设置为离职
1. 可变更账号状态的 账号系统
1. 学生账号
1. 老师账号




### 二、名词说明

- 行政班：
- 本期学生的关系都挂载在当前学校的行政班下，学生 VS 当前行政班 是一一对应的关系
- 一个行政班 包含：班主任 & 多学科的学科教师，每个学科可配置多位老师
- 账号状态：
- 老师账号 & 学生账号 有5种状态：尚未启用 -> 试用 -> 已付费（-> 未付费）  -> 停用
- 尚未启用状态：默认“创建账号”后的状态，此时账号创建完成，但还不可以登入系统查看功能
- 试用状态：由“试用流程”，账号创建完成 + 试用期内，则账号可登入系统、使用试用期功能
- 已付费状态：由“付费流程”，账号创建完成 + 付费期内，则账号可以登入系统、使用付费期功能
- 未付费状态：账号创建完成 + 不在试用期、付费期内，则账号（？）不可以登入系统、当时可以在当前登录状态内使用功能
- 停用状态：由“停用流程”，账号创建完成 + 确认不使用，则账号被踢登录下线，且不可以再登入系统，直至解除
![board_JWPzw5EerhtZfGbmbfXcVUVvnEe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524276965.png)

                                                                                     



### 三、业务流程

**角色 & 核心页面关系图**

学校管理员获得学校的权限，设置了教务“学期、教材、课表”后，需创建账号，按如下流程进行配置

1. 完成班级创建
1. 批量上传学生名单，生成学生账号
1. 批量上传教师名单，生成教师账号
1. 根据合作要求，圈选账号、申请变更账号的状态：试用、付费、停用等流程
![board_Epz5wdreMhBUDjbeEs0cSR4Jn4d](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524277707.png)



**本期：P0核心功能 vs 学校账号生命周期**

![board_BiZJwRkBlhd8RobNRipcwXVrnVh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524278578.png)

附：运营平台--全景图，可见[新产品_运营后台](https://fqspoay1wny.feishu.cn/docx/VGJ8d3FYioel9bxM4jjcMQdBn2d#share-Te8idvHcCoYPhSxMiFEcBc7snle)

**各模块核心页面 及 属性、操作**

![board_VdBAwP3Qjhax3fbJWYocAtEynmc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524279187.png)



### 四、需求概览

| 序号 | 需求名称 | 内容概述 | 优先级 |
| --- | --- | --- | --- |
| 1 | 账号管理 |  | P0 |
| 2 | 用户登录 |  | p0 |

- 学生管理
- 学生账号的增删改查
- 学生管理的页面功能
- 教师管理
- 学生端/教师端登录
- 运营后台端登录
### 五、详细产品方案

#### 5.1 账号管理

##### 学生账号

###### 1.组织关系管理

页面由左侧边 / 右侧主体页面两部分构成

左侧：按学校名称 - 年级 -班级 ，分级展示数据；并展示班级创建、编辑入口，支持在弹窗中操作

右侧：展示该数据范围下的学生列表

| 所属页面 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| xx学校 > 账号管理> 学生管理 | 校级查询 | 在页面左侧，按学校名称 - 年级 -班级 ，分级展示数据在页面右侧，当前所选学年内，该校范围内的全部学生 | ![in_table_image_QIV5bsmOioYLkixJodHcRB1qnSg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524283958.png) |  |
|  | 年级查询&班级创建 | 在页面左侧，按学校名称 - 年级 -班级 ，分级展示数据在页面右侧：当前所选学年内，显示该年级范围内的全部学生 | ![in_table_image_K78hbnBq7o8CO9xoM8zcaoYMn80](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524285151.png) |  |
|  | 班级查询&班级编辑 | 在页面左侧，按学校名称 - 年级 -班级 ，分级展示数据在页面右侧：当前所选学年内，显示该班级范围内的全部学生 | ![in_table_image_V5mvbq43ooUD5AxUmtdc25Qinj4](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524286464.png) |  |

![analyze_in_table_image_GIyjbzDrnoPhejxfM8KcUfE3nwg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524282297.png)

![analyze_in_table_image_PwQ2bpz1so5Ljbxl4YycGim9nic](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524282856.png)

![analyze_in_table_image_R8lRbp76NoATBbxbh0scXW86nKf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524283376.png)

![analyze_in_table_image_QIV5bsmOioYLkixJodHcRB1qnSg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524283958.png)

![analyze_in_table_image_UxkWb56OJo4547xNMCBcuERZnIe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524284555.png)

![analyze_in_table_image_K78hbnBq7o8CO9xoM8zcaoYMn80](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524285151.png)

![analyze_in_table_image_Xbw6berxio1w6AxFW6CcSBMcn1c](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524285954.png)

![analyze_in_table_image_V5mvbq43ooUD5AxUmtdc25Qinj4](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524286464.png)

###### 2.账号增删改查

| 所属页面 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| -- | 变更账号状态（P2） | 学生账号的状态，和学校、年级、班级的合作状态可能都不一致，读本人的账号状态即可包含以下5种： |  |  |
| xx学校 > 账号管理> 学生管理 | 创建单个账号 | 前置条件：完成了班级创建在行政班里，选择“单个添加”学生： | ![in_table_image_YLqFbsIqhodkR5x5usfcA6DEnAf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524286980.png) |  |
|  | 修改账号 | 转班编辑单个学生 | ![in_table_image_NgQdbxSOfoOrq3xaIaBcCaDVnAl](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524288688.png) |  |
|  | 创建批量账号 | 批量添加学生：点击后，弹出弹窗“批量添加学生” | ![in_table_image_PGbVbJMejo0JaoxP5Jrce903nNf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524289705.png) |  |
|  | 查询账号 | 查询学生查看学生列表： | ![in_table_image_VVSWbeV2xo4MwdxN9IMcJwnvnNh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524290263.png) |  |
|  | 查看密码重置密码 | 重置密码 | ![in_table_image_WH0pbaJTSohn6jxixbyctz4lnYg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524292449.png) |  |
|  | 删除账号 | 暂不支持前端页面的数据删除 |  |  |

- 尚未启用状态：默认“创建账号”后的状态，此时账号创建完成，但还不可以登入系统查看功能
- 试用状态：由“试用流程”，账号创建完成 + 试用期内，则账号可登入系统、使用试用期功能
- 关联：新试用流程、试用期延长流程
- 已付费状态：由“付费流程”，账号创建完成 + 付费期内，则账号可以登入系统、使用付费期功能
- 关联：新付费流程
- 未付费状态：账号创建完成 + 不在试用期、付费期内，则账号当前可以登入系统、当时可以在当前登录状态内使用功能
- 停用状态：由“停用流程”，账号创建完成 + 确认不使用，则账号被踢登录下线，且不可以再登入系统，直至解除
- 关联：停用流程、
![analyze_in_table_image_YLqFbsIqhodkR5x5usfcA6DEnAf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524286980.png)

![analyze_in_table_image_RDEzbNMePoTcoEx6O2Mc10b3noh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524287531.png)

![analyze_in_table_image_SYvIbVLZeo82CexKbhwc5uxMnvd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524288116.png)

![analyze_in_table_image_NgQdbxSOfoOrq3xaIaBcCaDVnAl](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524288688.png)

![analyze_in_table_image_PGbVbJMejo0JaoxP5Jrce903nNf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524289705.png)

![analyze_in_table_image_VVSWbeV2xo4MwdxN9IMcJwnvnNh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524290263.png)

![analyze_in_table_image_UR8xb9JVGoTksBxWWQpc5n0rnxh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524290817.png)

![analyze_in_table_image_NQhJbvRITol2aEx90Y7c0CSpnlf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524291616.png)

![analyze_in_table_image_WH0pbaJTSohn6jxixbyctz4lnYg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524292449.png)

##### 老师账号

| 所属页面 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| -- | 变更账号状态（P2） | 老师在xx学校的账号状态 在职状态下：和所在当前学校的账号状态一致包含以下5种：在xx学校的账号状态 在离职状态下： |  |  |
| xx学校 > 账号管理> 老师管理 | 创建单个账号 | 前置条件：完成了班级创建在行政班里，选择“单个添加”老师： | ![in_table_image_WXgMbrYtOo0kRlxnBmbcKdhsnnb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524293731.png) |  |
|  | 修改账号 | 编辑单个老师操作离职 |  |  |
|  | 创建批量账号 | 批量添加老师：点击后，弹出弹窗“批量添加教师” | ![in_table_image_DXoJbDiL4orz93x43sAcsO8rnnb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524295240.png) |  |
|  | 查询账号 | 查询老师查看教师列表： | ![in_table_image_CngDbbvBZoq3lexc81rcSdQLnNd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524295842.png) |  |
|  | 删除账号 | 暂不支持前端页面的数据删除 |  |  |

- 尚未启用状态：还不可以登入系统查看功能
- 试用中 & 未付费：可以登入系统，可以在当前登录状态内使用部分功能
- 付费使用：可以登入系统，可以在当前登录状态内使用付费功能
- 停止合作：账号被踢登录下线，且不可以再登入系统，直至解除
- 设置为停止合作：账号被踢登录下线，且不可以再登入系统，直至重新加入学校
![analyze_in_table_image_JtbgbGzwboQtVox6OHMc2smFnpf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524293188.png)

![analyze_in_table_image_WXgMbrYtOo0kRlxnBmbcKdhsnnb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524293731.png)

![analyze_in_table_image_DXoJbDiL4orz93x43sAcsO8rnnb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524295240.png)

![analyze_in_table_image_CngDbbvBZoq3lexc81rcSdQLnNd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524295842.png)



#### 5.2 学生端账号登录

| 端 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| pad | 使用学校下发的账号登录pad | 稍后，在C端文档中查看 |  | P0 |

#### 5.3 教师端登录

| 端 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| pad | 使用手机号&验证码 登录 | 稍后，在C端文档中查看 |  | P0 |
| web |  |  |  | P0 |
| 大屏（P2） | 用pad扫大屏二维码登录点击大屏，在pad弹窗确认登录 |  |  | P2 |

#### 5.4 运营后台登录

| 端 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| web | 登录 | 浏览器中输入网址：xxx.xxx.xxx手机号：输出11位手机号验证码：登录按钮： | P0 |  |
|  | 添加人员到运营后台 | 通过“运营后台账户管理”功能维护：可登录运营后台的人员名单输入：姓名 & 企业feishu对应手机号 & 角色  创建账号此处操作基本和 教师账号一致页面操作人：暂时指定为“袁全”，后续添加角色3月13日 由“角色管理”添加角色3：“运营账号管理员” | ![in_table_image_JfMubR1ToodJ67x4c6scBTdgn8g](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524297487.png) |  |

![analyze_in_table_image_JHOvbiqtboIDOSxw9gscwAwPnlf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524296864.png)

![analyze_in_table_image_JfMubR1ToodJ67x4c6scBTdgn8g](https://static.test.xiaoluxue.cn/demo41/prd_images/1746524297487.png)

#### 





### 六、数据需求（待确认）

#### 6.1 效果追踪

描述计划监控数据效果的方式及所需数据字段

#### 6.2 数据埋点

| 页面 | 模块 | 动作 | 埋点名称 | 图示（如需） |
| --- | --- | --- | --- | --- |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |

### 七、a/b实验需求

若有，则写清楚实验方案

**实验目的**

XXX

**实验对象**

限制条件：端/版本/用户

实验组策略：

对照组策略：

**实验及分流**

启动时间、启动时用户量、预计持续时间

**评价指标**

结果指标（标明预期提升值）、过程指标

**暂无**



### 附：评审记录

记录存档历次需求评审中的会议纪要，方便追踪

举例：

**20250204 - 初评纪要**

1. 会后尽快确定巩固类型本期题目类型范围@杨宇航
已确认，并补充进文档

1. 数据下发方式待服务端和客户端最终确认@服务端rd @客户端rd
确定服务端下发，客户端仅做兜底逻辑

1. ...


**20250321 - 二次评审纪要**

1. XX
1. 

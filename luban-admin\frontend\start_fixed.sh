#!/bin/bash

echo "🚀 鲁班前端启动脚本 - Ubuntu/Mac 版本"
echo "=========================================="

# 检查 Node.js 是否安装
echo "🔍 检查 Node.js 环境..."
if ! command -v node &> /dev/null; then
    echo "❌ 错误: Node.js 未安装，请先安装 Node.js"
    echo "   下载地址: https://nodejs.org/"
    exit 1
fi

# 检查 npm 是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: npm 未安装，请先安装 npm"
    exit 1
fi

# 显示 Node.js 和 npm 版本
NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)
echo "✅ Node.js 版本: $NODE_VERSION"
echo "✅ npm 版本: $NPM_VERSION"

# 检查 node_modules 目录是否存在（支持软链接）
echo ""
echo "🔍 检查依赖包..."
if [ -e "node_modules" ]; then
    if [ -L "node_modules" ]; then
        echo "✅ node_modules 目录存在 (软链接)"
        LINK_TARGET=$(readlink "node_modules")
        echo "   链接目标: $LINK_TARGET"
    else
        echo "✅ node_modules 目录存在"
    fi
    
    # 检查关键依赖包
    if [ -d "node_modules/next" ]; then
        echo "✅ Next.js 依赖包已安装"
    else
        echo "⚠️  警告: Next.js 依赖包未找到，可能需要重新安装依赖"
    fi
    
    if [ -d "node_modules/react" ]; then
        echo "✅ React 依赖包已安装"
    else
        echo "⚠️  警告: React 依赖包未找到，可能需要重新安装依赖"
    fi
else
    echo "❌ node_modules 目录不存在，正在安装依赖包..."
    echo "📦 运行 npm install..."
    npm install
    
    if [ $? -eq 0 ]; then
        echo "✅ 依赖包安装完成"
    else
        echo "❌ 依赖包安装失败，请检查网络连接或 package.json 文件"
        exit 1
    fi
fi

echo ""
echo "🧹 正在清理 Next.js 缓存..."

# 删除 .next 目录
if [ -d ".next" ]; then
    echo "🗑️  删除 .next 目录..."
    rm -rf .next
    echo "✅ .next 目录已删除"
else
    echo "ℹ️  .next 目录不存在，跳过删除"
fi

# 删除 node_modules/.cache 目录
if [ -d "node_modules/.cache" ]; then
    echo "🗑️  删除 node_modules/.cache 目录..."
    rm -rf node_modules/.cache
    echo "✅ node_modules/.cache 目录已删除"
else
    echo "ℹ️  node_modules/.cache 目录不存在，跳过删除"
fi

echo "✅ 缓存清理完成！"

echo ""
echo "🚀 正在启动开发服务器..."
echo "=========================================="
npm run dev
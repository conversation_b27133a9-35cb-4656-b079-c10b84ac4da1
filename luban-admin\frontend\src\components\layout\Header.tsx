// @ts-nocheck
'use client';
import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import Logo from './Logo';
import TemplateModal from '../template/TemplateModal';

const Header = () => {
  const pathname = usePathname();
  
  // 检测是否在工作流相关页面
  const isWorkflowPage = pathname?.includes('/workflow');
  
  // 添加模板中心弹窗状态
  const [isTemplateModalOpen, setIsTemplateModalOpen] = useState(false);
  
  // 显示模板中心弹窗
  const handleTemplateClick = (e) => {
    e.preventDefault();
    setIsTemplateModalOpen(true);
  };

  return (
    <header className="fixed w-full top-0 z-50">
      {/* 主导航栏 */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-16">
            {/* 左侧Logo和名称 */}
            <div className="flex items-center">
              <Logo />
            </div>

            {/* 中间导航 */}
            <nav className="flex space-x-16">
              <Link href="/" className={`font-medium ${pathname === '/' ? 'text-green-500' : 'text-gray-600 hover:text-green-500'}`}>
                首页
              </Link>
              
              <Link
                href="/workflow/format-prd"
                className={`font-medium relative ${isWorkflowPage ? 'text-green-500' : 'text-gray-600 hover:text-green-500'}`}
              >
                工作流
                {isWorkflowPage && (
                  <span className="absolute -bottom-1 left-0 w-full h-0.5 bg-green-500"></span>
                )}
              </Link>
              
              <button
                onClick={handleTemplateClick}
                className="text-gray-600 hover:text-green-500 font-medium"
              >
                模板中心
              </button>
            </nav>

            {/* 右侧用户菜单 */}
            <div className="flex items-center">
              <button className="p-1 rounded-full text-gray-500 hover:text-blue-500 focus:outline-none">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
              </button>
              <div className="ml-4 relative flex items-center">
                <button className="flex items-center focus:outline-none">
                  <div className="w-8 h-8 bg-gray-200 rounded-full overflow-hidden">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-full w-full text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <span className="ml-2 text-sm font-medium text-gray-700">GIL</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 工作流子导航栏 - 仅在工作流页面显示 */}
      {isWorkflowPage && (
        <div className="bg-gray-50 border-b border-gray-200">
          <div className="container mx-auto px-4">
            <div className="flex items-center justify-center py-2">
              <Link
                href="/workflow/format-prd"
                className={`px-4 py-2 mx-2 rounded-md font-medium transition-colors duration-200 ${
                  pathname?.includes('/workflow/format-prd') || pathname === '/workflow/new'
                    ? 'bg-green-500 text-white'
                    : 'text-gray-600 hover:bg-green-100 hover:text-green-700'
                }`}
              >
                生成格式化PRD
              </Link>
              <Link
                href="/workflow/ai-prd"
                className={`px-4 py-2 mx-2 rounded-md font-medium transition-colors duration-200 ${
                  pathname?.includes('/workflow/ai-prd')
                    ? 'bg-green-500 text-white'
                    : 'text-gray-600 hover:bg-green-100 hover:text-green-700'
                }`}
              >
                生成AI-PRD
              </Link>
              <Link
                href="/workflow/ai-td"
                className={`px-4 py-2 mx-2 rounded-md font-medium transition-colors duration-200 ${
                  pathname?.includes('/workflow/ai-td')
                    ? 'bg-green-500 text-white'
                    : 'text-gray-600 hover:bg-green-100 hover:text-green-700'
                }`}
              >
                生成AI-TD
              </Link>
              <Link
                href="/workflow/ai-de"
                className={`px-4 py-2 mx-2 rounded-md font-medium transition-colors duration-200 ${
                  pathname?.includes('/workflow/ai-de')
                    ? 'bg-green-500 text-white'
                    : 'text-gray-600 hover:bg-green-100 hover:text-green-700'
                }`}
              >
                生成AI-DE
              </Link>
              <Link
                href="/workflow/ai-dd"
                className={`px-4 py-2 mx-2 rounded-md font-medium transition-colors duration-200 ${
                  pathname?.includes('/workflow/ai-dd')
                    ? 'bg-green-500 text-white'
                    : 'text-gray-600 hover:bg-green-100 hover:text-green-700'
                }`}
              >
                生成AI-DD
              </Link>
              <Link
                href="/workflow/ai-ad"
                className={`px-4 py-2 mx-2 rounded-md font-medium transition-colors duration-200 ${
                  pathname?.includes('/workflow/ai-ad')
                    ? 'bg-green-500 text-white'
                    : 'text-gray-600 hover:bg-green-100 hover:text-green-700'
                }`}
              >
                生成AI-AD
              </Link>
              <Link
                href="/workflow/ai-api"
                className={`px-4 py-2 mx-2 rounded-md font-medium transition-colors duration-200 ${
                  pathname?.includes('/workflow/ai-api')
                    ? 'bg-green-500 text-white'
                    : 'text-gray-600 hover:bg-green-100 hover:text-green-700'
                }`}
              >
                生成AI-API
              </Link>
            </div>
          </div>
        </div>
      )}

      {/* 模板中心弹窗 */}
      <TemplateModal
        isOpen={isTemplateModalOpen}
        onClose={() => setIsTemplateModalOpen(false)}
      />
    </header>
  );
};

export default Header;
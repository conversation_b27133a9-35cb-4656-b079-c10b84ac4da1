# Luban Design 目录结构说明

本文档描述了Luban Design项目的完整目录结构。

## 完整项目目录结构树

luban-design/
├─ ai-templates/                                         # AI模板管理根目录
│  ├─ output/                                            # 输出文档目录
│  │  ├─ fe-task/                                       # 前端任务输出目录
│  │  │  ├─ svg/                                        # SVG图形文件目录
│  │  │  │  ├─ card.svg                                 # 卡片组件图形
│  │  │  │  ├─ home.svg                                 # 首页布局图形
│  │  │  │  ├─ navbar.svg                               # 导航栏图形
│  │  │  │  ├─ sidebar-stack.svg                        # 侧边栏堆叠图形
│  │  │  │  └─ top-nav.svg                              # 顶部导航图形
│  │  │  ├─ .DS_Store                                   # 系统文件
│  │  │  ├─ 01-frame-home.md                            # 首页框架文档
│  │  │  ├─ 02-nav-navbar.md                            # 导航栏组件文档
│  │  │  ├─ 03-sidebar-knowledgelist.md                 # 侧边栏知识列表文档
│  │  │  ├─ 04-card-course.md                           # 课程卡片组件文档
│  │  │  ├─ 05-nav-topinfo.md                           # 顶部信息导航文档
│  │  │  └─ aiclass_tasktree.md                         # AI课程任务树文档
│  │  ├─ project-progress.md                            # 项目进度文档
│  │  ├─ tmp-output.md                                  # 临时文档输出
│  │  └─ tmp-prd-output.md                              # 临时PRD文档输出
│  ├─ prompts/                                           # AI提示词目录
│  │  ├─ be-dev/                                        # 后端开发提示词
│  │  │  ├─ prompt-gen-domain-code.md                   # 领域代码生成提示词
│  │  │  ├─ prompt-gen-interface-code.mdc               # 接口代码生成提示词
│  │  │  └─ prompt-gen-tdd-code.md                      # TDD代码生成提示词
│  │  ├─ design/                                        # 设计相关提示词
│  │  │  ├─ be-s0-workflow.md                           # 后端工作流说明
│  │  │  ├─ be-s1-gen-ai-prd.md                         # 后端PRD生成提示词
│  │  │  ├─ be-s2-gen-ai-td.md                          # 后端技术设计提示词
│  │  │  ├─ be-s3-gen-ai-dd.md                          # 后端详细设计提示词
│  │  │  ├─ be-s4-gen-ai-ad.md                          # 后端架构设计提示词
│  │  │  ├─ fe-gen-ai-td.md                             # 前端技术设计提示词
│  │  │  ├─ prompt-gen-ai-checklist.md                  # AI生成检查清单提示词
│  │  │  ├─ prompt-gen-ai-issue.md                      # AI生成问题提示词
│  │  │  ├─ prompt-gen-ai-plan.md                       # AI生成计划提示词
│  │  │  ├─ prompt-gen-ai-task.md                       # AI生成任务提示词
│  │  │  └─ prompt-gen-ai-user-story.md                 # AI生成用户故事提示词
│  │  ├─ fe-dev/                                        # 前端开发提示词
│  │  │  ├─ gen-detail-modify.md                        # 详细修改提示词
│  │  │  ├─ gen-task-code.md                            # 任务代码生成提示词
│  │  │  └─ gen-tasktree.md                             # 任务树生成提示词
│  │  └─ test/                                          # 测试相关提示词
│  │     ├─ gen-testcase-common.mdc                     # 通用测试用例生成提示词
│  │     ├─ prompt-gen-e2e-test.md                      # 端到端测试生成提示词
│  │     ├─ prompt-gen-testcase.md                      # 测试用例生成提示词
│  │     ├─ prompt-gen-testscript.md                    # 测试脚本生成提示词
│  │     └─ prompt-gen-unit-test.md                     # 单元测试生成提示词
│  └─ templates/                                         # 文档模板目录
│     ├─ ad/                                            # 架构设计模板目录
│     │  ├─ ai-ad-template-v1.0.md                      # 架构设计模板V1.0
│     │  └─ ai-ad-template-v2.0.md                      # 架构设计模板V2.0
│     ├─ common/                                        # 通用模板目录
│     │  ├─ ai-common-rules-template-v1.0.md            # 通用规则模板
│     │  └─ ai-mer-section-v1.0.md                      # 时序图模板
│     ├─ dd/                                            # 详细设计模板目录
│     │  ├─ ai-dd-template-v1.0.md                      # 详细设计模板V1.0
│     │  └─ ai-dd-template-v2.0.md                      # 详细设计模板V2.0
│     ├─ prd/                                           # PRD文档模板目录
│     │  ├─ ai-prd-v1.8.md                              # PRD模板V1.8
│     │  ├─ ai-prd-v2.1.md                              # PRD模板V2.1
│     │  ├─ ai-prd-v2.2.md                              # PRD模板V2.2
│     │  ├─ tech-prd-v1.md                              # 技术PRD模板V1
│     │  ├─ tech-prd-v2.md                              # 技术PRD模板V2
│     │  └─ user-story.mdc                              # 用户故事模板
│     ├─ td/                                            # 技术设计模板目录
│     │  ├─ ai-td-v1.0.md                               # 技术设计模板V1.0
│     │  └─ ai-td-v2.4.md                               # 技术设计模板V2.4
│     └─ testcase/                                      # 测试用例模板目录
│        ├─ ai-testcase-template-v1.0.md                # 测试用例模板V1.0
│        └─ ai-testcase-template-v2.0.md                # 测试用例模板V2.0
├─ ai-doc-format/         # AI文档格式化与处理相关代码和工具
│  ├─ golang/             # Go语言相关文档格式化处理
│  ├─ python/             # Python相关文档格式化处理
│  ├─ raw-docs/           # 原始文档存放目录
│  ├─ tools/              # 文档处理工具
│  └─ README.md           # 说明文档
├─ luban-admin/           # Luban管理后台相关代码
│  ├─ backend/            # 后端服务代码
│  ├─ frontend/           # 前端管理界面代码
│  ├─ docker-compose.yml  # 后台服务编排配置
│  └─ README.md           # 说明文档
├─ 目录说明.md            # 目录结构说明文档
└─ README.md              # 项目说明文档

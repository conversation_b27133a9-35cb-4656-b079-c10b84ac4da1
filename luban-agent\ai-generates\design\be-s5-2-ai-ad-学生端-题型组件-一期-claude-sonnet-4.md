# 接口使用文档 - 学生端题型组件一期 - V1.0

* **最后更新日期**: 2025-05-30
* **设计负责人**: claude-sonnet-4
* **评审人**: 待填写
* **状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-05-30 | claude-sonnet-4 | 初始草稿     |

## 1. 服务概述与设计目标

### 1.1 服务背景与范围
本服务提供学生端题型组件的完整答题功能，支持单选题、多选题、填空题、解答题等核心题型的答题流程。包括智能批改、实时交互、多媒体作答、勾画工具、异常行为监控等功能，旨在提升学生答题体验60%，题型覆盖率达到80%。

### 1.2 设计目标回顾
1. 提供符合项目API设计规范的接口定义，确保可直接用于前端开发和测试。
2. 清晰定义每个接口的请求、响应、参数、数据模型和错误码。
3. 确保接口设计满足产品需求文档 (PRD) 中定义的6个核心用户场景和业务流程。
4. 遵循团队的API设计规范和最佳实践，支持1000并发用户，P95响应时间<2秒。
5. 接口定义包含完整的中文描述信息，便于理解和维护。

## 2. 通用设计规范与约定

### 2.1 数据格式
* 请求体 (Request Body): `application/json`
* 响应体 (Response Body): `application/json`
* 日期时间格式: ISO 8601 (`YYYY-MM-DDTHH:mm:ss.sssZ`)

### 2.2 认证与授权
* 所有接口需要JWT认证，在请求头中传递 `Authorization: Bearer <token>`
* 学生身份验证通过用户中心服务完成
* 答题接口具有防刷题保护机制

### 2.3 版本控制
* 通过 URL 路径进行版本控制，如 `/api/v1/exercises/...`
* 路径中不包含任何参数，所有请求参数都放在 query param 或 request body 中

### 2.4 错误处理约定
* **统一响应结构**:
  ```json
  {
    "code": 0,                // 0表示成功，对应HTTP状态码200；其他情况下，code为7位长度，前3位为http状态码，后4位为业务自定义错误码
    "message": "Success",     // 成功或错误信息
    "data": { ... },          // 实际业务数据对象或数组
    "pageInfo": { ... }       // 分页信息，仅在分页查询时返回
  }
  ```
* **错误响应示例**:
  ```json
  {
    "code": 4000001,          // 400 + 0001 业务错误码
    "message": "Invalid input parameters.",
    "details": [
      {
        "field": "questionId",
        "issue": "must not be empty"
      }
    ]
  }
  ```
* **HTTP 状态码**:
  * `200 OK`: 请求成功，包括所有成功的操作
  * `400 Bad Request`: 请求无效 (例如参数错误、格式错误)
  * `401 Unauthorized`: 未认证或认证失败
  * `403 Forbidden`: 已认证，但无权限访问
  * `404 Not Found`: 请求的资源不存在
  * `429 Too Many Requests`: 请求过于频繁，触发速率限制
  * `500 Internal Server Error`: 服务器内部错误

### 2.6 分页与排序约定
* **分页参数**: 分页通常使用 page 和 pageSize 查询参数
* **排序参数**: 排序使用 sortBy 和 order 参数

## 3. 接口列表

| 接口路径            | 请求方式 | 接口名称         | 接口描述               |
| -------------------| -------- | -------- | ---------------- |
| /api/v1/exercises/sessions | GET/POST/PUT | 会话管理 | 获取题目信息并创建会话、查询、更新会话状态 |
| /api/v1/exercises/answers | GET/POST/PUT | 答题管理 | 提交答案、自评、查询结果，支持所有题型 |
| /api/v1/exercises/resources | GET/POST/PUT/DELETE | 资源管理 | 媒体文件、勾画数据、学习支持等统一管理 |

## 4. 场景说明

### 4.1 单选题作答场景

#### 4.1.1 单选题作答流程
1. 学生进入题目页面，系统自动创建答题会话并开始计时，同时加载题目内容和选项
2. 学生选择选项并提交答案，系统进行自动批改
3. 系统返回批改结果和解析内容
4. 如果答错且支持二次作答，学生可以申请重新作答
5. 中途退出会暂停计时，再次进入会继续计时

#### 4.1.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant ExerciseAPI as 题型组件API
    participant ContentAPI as 内容平台API
    participant GradingAPI as 批改服务API

    Student->>Frontend: 进入单选题页面
    Frontend->>ExerciseAPI: POST /api/v1/exercises/sessions
    Note over Frontend,ExerciseAPI: Body: {questionId: 123, studentId: 456}
    ExerciseAPI->>ContentAPI: 获取题目详情
    ContentAPI->>ExerciseAPI: 返回题目内容和选项
    ExerciseAPI->>Frontend: 返回题目信息和会话ID，开始计时
    Frontend->>Student: 展示题目和选项，显示计时器

    Student->>Frontend: 选择选项A并提交
    Frontend->>ExerciseAPI: POST /api/v1/exercises/answers
    Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", optionId: "A", answerDuration: 30}
    ExerciseAPI->>GradingAPI: 请求自动批改
    GradingAPI->>ExerciseAPI: 返回批改结果
    ExerciseAPI->>Frontend: 返回提交状态和批改结果

    Frontend->>ExerciseAPI: GET /api/v1/exercises/answers?sessionId=abc123&includeResults=true
    ExerciseAPI->>Frontend: 返回详细结果和解析
    Frontend->>Student: 展示答题结果

    alt 答错且支持二次作答
        Student->>Frontend: 申请重新作答
        Frontend->>ExerciseAPI: PUT /api/v1/exercises/sessions
        Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", action: "retry"}
        ExerciseAPI->>Frontend: 重置会话状态
        Frontend->>Student: 允许重新选择
    end
```

### 4.2 多选题作答场景

#### 4.2.1 多选题作答流程
1. 学生进入多选题页面，加载题目和选项
2. 学生选择多个选项，如果仅选择1个选项，系统弹窗确认
3. 确认后提交答案，系统进行自动批改
4. 返回批改结果，支持二次作答

#### 4.2.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant ExerciseAPI as 题型组件API

    Student->>Frontend: 选择多个选项
    Frontend->>Frontend: 检查选项数量
    
    alt 仅选择1个选项
        Frontend->>ExerciseAPI: POST /api/v1/exercises/answers
        Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", optionIds: ["A"], needConfirm: true}
        ExerciseAPI->>Frontend: 返回确认提示
        Frontend->>Student: 显示"您只选择了1个选项，确认提交吗？"
        Student->>Frontend: 确认提交
    end

    Frontend->>ExerciseAPI: POST /api/v1/exercises/answers
    Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", optionIds: ["A", "C"], answerDuration: 45, confirmed: true}
    ExerciseAPI->>Frontend: 返回批改结果
    Frontend->>Student: 展示结果
```

### 4.3 英语填空题自动批改场景

#### 4.3.1 英语填空题流程
1. 学生阅读英语题目，识别填空位置（标记1、2、3...）
2. 在各填空处输入英语单词或短语
3. 提交答案，系统调用英语自动批改算法
4. 返回每个空的批改结果和标准答案

#### 4.3.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant ExerciseAPI as 题型组件API
    participant EnglishGrading as 英语自动批改服务

    Student->>Frontend: 在填空处输入答案
    Frontend->>ExerciseAPI: POST /api/v1/exercises/answers
    Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", answers: ["apple", "running", "beautiful"], answerDuration: 120}
    
    ExerciseAPI->>EnglishGrading: 调用英语自动批改算法
    EnglishGrading->>ExerciseAPI: 返回每个空的批改结果
    ExerciseAPI->>Frontend: 返回详细批改结果
    Frontend->>Student: 展示每个空的对错和标准答案
```

### 4.4 其他学科填空题自评场景

#### 4.4.1 填空题自评流程
1. 学生完成填空题作答并提交
2. 系统返回标准答案供学生对比
3. 学生进行自评选择（我答对了/部分答对/我答错了）
4. 系统记录自评时长，检测异常行为

#### 4.4.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant ExerciseAPI as 题型组件API
    participant AnomalyAPI as 异常检测服务

    Student->>Frontend: 提交填空题答案
    Frontend->>ExerciseAPI: POST /api/v1/exercises/answers
    Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", answers: ["答案1", "答案2"], answerDuration: 90}
    ExerciseAPI->>Frontend: 返回标准答案对比界面
    Frontend->>Student: 展示标准答案，要求自评

    Student->>Frontend: 选择"我答对了"（耗时1秒）
    Frontend->>ExerciseAPI: POST /api/v1/exercises/answers
    Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", type: "self_evaluation", evaluationResult: 1, evaluationDuration: 1}
    ExerciseAPI->>AnomalyAPI: 内部调用异常检测
    Note over ExerciseAPI,AnomalyAPI: 检测参数: {studentId: 456, evaluationDuration: 1, questionId: 123}
    
    alt 检测到异常（连续3题<2秒）
        AnomalyAPI->>ExerciseAPI: 返回异常检测结果
        ExerciseAPI->>Frontend: 触发异常提示
        Frontend->>Student: 显示"认真核对哦"（2秒）
    else 正常行为
        AnomalyAPI->>ExerciseAPI: 返回正常状态
    end
    
    ExerciseAPI->>Frontend: 返回自评完成状态
    Frontend->>Student: 显示最终结果
```

### 4.5 解答题多媒体作答场景

#### 4.5.1 解答题作答流程
1. 学生阅读解答题题目
2. 选择作答方式：拍照上传或键盘输入
3. 如选择拍照，可上传最多4张图片
4. 提交答案后查看标准答案进行自评

#### 4.5.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant ExerciseAPI as 题型组件API
    participant MediaAPI as 多媒体服务

    Student->>Frontend: 选择拍照作答
    Frontend->>Student: 调用相机拍照
    Student->>Frontend: 拍摄答题图片（最多4张）
    
    loop 上传每张图片
        Frontend->>ExerciseAPI: POST /api/v1/exercises/resources
        Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", type: "media", file: <image_data>}
        ExerciseAPI->>Frontend: 返回图片URL
    end

    Student->>Frontend: 完成作答，提交答案
    Frontend->>ExerciseAPI: POST /api/v1/exercises/answers
    Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", textContent: "解答过程", imageUrls: ["url1", "url2"], answerDuration: 300}
    
    ExerciseAPI->>Frontend: 返回标准答案
    Frontend->>Student: 展示标准答案和自评选项
    
    Student->>Frontend: 完成自评
    Frontend->>ExerciseAPI: POST /api/v1/exercises/answers
    Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", type: "self_evaluation", evaluationResult: 2}
    ExerciseAPI->>Frontend: 返回最终结果
    Frontend->>Student: 显示完成状态
```

### 4.6 勾画工具辅助答题场景

#### 4.6.1 勾画工具使用流程
1. 学生在任意题型中点击勾画按钮
2. 选择勾画工具（画笔、橡皮擦）和样式（颜色、粗细）
3. 在题目上进行标记和勾画
4. 系统实时保存勾画数据，支持撤回和清空

#### 4.6.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant ExerciseAPI as 题型组件API

    Student->>Frontend: 点击勾画按钮
    Frontend->>ExerciseAPI: GET /api/v1/exercises/resources?sessionId=abc123&type=drawings
    ExerciseAPI->>Frontend: 返回已有勾画数据
    Frontend->>Student: 加载并显示历史勾画

    Student->>Frontend: 选择画笔工具，设置蓝色、中等粗细
    Student->>Frontend: 在题目上勾画
    Frontend->>ExerciseAPI: POST /api/v1/exercises/resources
    Note over Frontend,ExerciseAPI: Body: {sessionId: "abc123", type: "drawings", drawingContent: {...}, toolType: 1, color: "blue", thickness: 2}
    ExerciseAPI->>Frontend: 返回保存状态

    alt 撤回操作
        Student->>Frontend: 点击撤回
        Frontend->>ExerciseAPI: DELETE /api/v1/exercises/resources?sessionId=abc123&type=drawings&drawingId=draw001
        ExerciseAPI->>Frontend: 返回删除状态
    end

    alt 一键清空
        Student->>Frontend: 点击清空
        Frontend->>ExerciseAPI: DELETE /api/v1/exercises/resources?sessionId=abc123&type=drawings&clearAll=true
        ExerciseAPI->>Frontend: 批量删除所有勾画
    end

    Student->>Frontend: 完成勾画，继续答题
    Frontend->>Student: 保存勾画状态，返回答题界面
```

## 5. 检查清单

### 5.1 PRD核心需求场景与API覆盖性检查

| PRD核心需求点/用户场景 (ID)                     | 涉及的主要API接口 (路径与方法)                                  | 关键输入数据实体/参数 (示例)                        | 关键输出数据实体/参数 (示例)                           | 场景支持度(✅/⚠️/❌)(API是否完整支持场景流程?) | 数据流正确性(✅/⚠️/❌)(输入输出数据是否符合场景逻辑?) | 备注/待办事项                                   |
| :---------------------------------------------- | :-------------------------------------------------------------- | :---------------------------------------------------- | :------------------------------------------------------- | :----------------------------------------------------- | :--------------------------------------------------------- | :---------------------------------------------- |
| `学生进入题目页面并作答` | `1. POST /api/v1/exercises/sessions` <br/> `2. POST /api/v1/exercises/answers` <br/> `3. GET /api/v1/exercises/answers` | `1. questionId, studentId` <br/> `2. sessionId, optionId, answerDuration` <br/> `3. sessionId(query), includeResults=true` | `1. 题目内容, 选项列表, sessionId, 开始计时` <br/> `2. 批改结果, 正确答案` <br/> `3. 详细结果, 解析内容` | ✅ | ✅ | 进入页面即创建会话并开始计时 |
| `学生进行多选题作答` | `POST /api/v1/exercises/answers` | `sessionId, optionIds[], answerDuration, needConfirm, confirmed` | `批改结果, 正确答案, 确认提示` | ✅ | ✅ | 确认逻辑集成在答案接口中 |
| `学生进行英语填空题作答（自动批改）` | `POST /api/v1/exercises/answers` | `sessionId, answers[], answerDuration` | `自动批改结果, 每个空的对错, 标准答案` | ✅ | ✅ | 依赖英语自动批改算法 |
| `学生进行其他学科填空题自评` | `1. POST /api/v1/exercises/answers` <br/> `2. POST /api/v1/exercises/answers` | `1. sessionId, answers[]` <br/> `2. sessionId, type=self_evaluation, evaluationResult, evaluationDuration` | `1. 标准答案对比界面` <br/> `2. 自评状态, 最终结果, 异常检测结果` | ✅ | ✅ | 答案提交和自评统一在同一接口 |
| `学生进行解答题作答` | `1. POST /api/v1/exercises/resources` <br/> `2. POST /api/v1/exercises/answers` <br/> `3. POST /api/v1/exercises/answers` | `1. type=media, 图片文件, sessionId` <br/> `2. sessionId, textContent, imageUrls[]` <br/> `3. sessionId, type=self_evaluation, evaluationResult` | `1. 文件URL, 上传状态` <br/> `2. 标准答案, 自评界面` <br/> `3. 自评完成状态` | ✅ | ✅ | 支持最多4张图片上传 |
| `学生使用勾画工具辅助答题` | `/api/v1/exercises/resources` (GET/POST/DELETE) | `sessionId, type=drawings, drawingContent, toolType, color, thickness` | `勾画数据列表, 保存状态, 删除状态` | ✅ | ✅ | 统一资源管理接口 |
| `每题独立正计时功能` | `/api/v1/exercises/sessions` (POST/PUT) | `sessionId, startTime, pauseState, action` | `累计时长, 格式化时间显示` | ✅ | ✅ | 上限59:59，支持暂停继续和重试 |
| `答疑chat功能` | `POST /api/v1/exercises/resources` | `sessionId, type=ai_chat, questionContent` | `AI回答, 推荐问题列表` | ✅ | ✅ | 集成在统一资源管理接口中 |
| `错题本功能` | `POST /api/v1/exercises/resources` | `sessionId, type=bookmark, questionId` | `收藏状态` | ✅ | ✅ | 集成在统一资源管理接口中 |

### 5.2 API接口数据实体与数据库支持检查

| API接口 (路径与方法)                     | 操作类型 (CRUD) | 主要数据实体 (模型名称)      | 关键数据属性 (字段名示例)                                 | 依赖的数据库表/视图 (示例)        | 数据库操作 (读/写/更新/删) | 数据可获得性/可行性(✅/⚠️/❌)(DB能否提供?性能是否满足?) | 数据一致性保障 (简述策略)                               | 备注/待办事项                                                                 |
| :--------------------------------------- | :-------------- | :--------------------------- | :---------------------------------------------------------- | :-------------------------------- | :--------------------------- | :--------------------------------------------------------- | :-------------------------------------------------------- | :---------------------------------------------------------------------------- |
| `/api/v1/exercises/sessions` (GET/POST/PUT) | CRUD | `ExerciseSession, Question` | `sessionId, studentId, questionId, startTime, currentState, action, content, options, questionType` | `tbl_component_states, tbl_timing_records, questions(内容平台)` | 读/写/更新 | ✅ | `事务保证原子性，内容平台集成` | `统一会话管理，集成题目信息获取，进入页面即创建会话并开始计时` |
| `/api/v1/exercises/answers` (GET/POST/PUT) | CRUD | `AnswerRecord, SelfEvaluation` | `sessionId, answerContent, submissionTime, gradingResult, confirmed, type, evaluationResult, evaluationDuration` | `tbl_answer_records, tbl_self_evaluation, tbl_behavior_anomalies` | 读/写/更新 | ✅ | `事务保证一致性，异步异常检测` | `统一答题管理，包含答案提交、自评、异常检测等功能` |
| `/api/v1/exercises/resources` (GET/POST/PUT/DELETE) | CRUD | `MediaResource, DrawingData, SupportRecord` | `sessionId, type, fileUrl, drawingContent, toolType, color, thickness, content, result` | `media_resources, tbl_drawing_data, tbl_support_records` | 读/写/更新/删 | ✅ | `按类型分别处理，文件存储原子性，实时保存` | `统一资源管理，包含媒体文件、勾画数据、学习支持等` |

### 5.3 API通用设计规范符合性检查

| 检查项                                                                 | 状态 (✅/⚠️/❌/N/A) | 备注/说明                                                                                                |
| :--------------------------------------------------------------------- | :------------------- | :------------------------------------------------------------------------------------------------------- |
| 1. **命名规范**: 接口路径、参数、数据实体命名是否清晰、一致，并遵循团队约定？        | ✅ | 使用项目API规范，资源名词复数形式，路径使用snake_case，参数使用camelCase |
| 2. **HTTP方法**: GET, POST, PUT, DELETE, PATCH等HTTP方法使用是否语义恰当？        | ✅ | GET用于查询，POST用于创建和复杂操作，DELETE用于删除，符合项目规范 |
| 3. **状态码**: HTTP状态码是否准确反映操作结果 (成功、客户端错误、服务端错误)？        | ✅ | 200成功，400参数错误，401认证失败，404资源不存在，500服务器错误 |
| 4. **错误处理**: 错误响应格式是否统一 (参考2.4)，错误信息是否对用户友好且包含足够调试信息？ | ✅ | 统一错误响应格式，包含错误码、消息和详情 |
| 5. **认证与授权**: 需要保护的接口是否都正确实施了认证和授权机制 (参考2.2)？           | ✅ | 所有接口需要JWT认证，具有防刷题保护机制 |
| 6. **数据格式与校验**: 请求和响应体结构是否定义清晰，数据类型是否准确？输入数据是否有必要的校验？ | ✅ | JSON格式，包含必填项、长度限制、格式限制等校验 |
| 7. **分页与排序**: 对于返回列表数据的接口，是否按约定提供了分页和排序功能 (参考2.6)？    | N/A | 当前接口主要为单条记录操作，暂无列表查询需求 |
| 8. **幂等性**: 对于创建和更新操作，是否考虑了幂等性设计？ (特别是PUT, DELETE)         | ⚠️ | 会话创建和答案提交需要考虑重复提交的幂等性处理 |
| 9. **安全性**: 是否有潜在的安全风险，如敏感信息泄露、SQL注入、XSS等？             | ✅ | 参数过滤和转义，JWT认证，文件上传安全检查 |
| 10. **性能考量**: 接口响应时间是否满足性能要求？大数据量查询是否有优化措施？             | ✅ | P95响应时间<2秒，支持1000并发，缓存优化 |
| 11. **文档一致性**: 本文档描述是否与OpenAPI规范文件及实际实现保持一致？ | ✅ | 基于接口分析文档，与设计文档保持一致 |
| 12. **可测试性**: 接口是否易于进行单元测试、集成测试和端到端测试？                    | ✅ | 接口设计清晰，支持Mock测试和自动化测试 |
| 13. **向后兼容性**: 未来接口变更是否考虑了向后兼容性策略？                            | N/A (新设计) | 对于v1版本可标记为N/A，但后续版本迭代需重点关注 |

## 6. 附录 (Appendix)

### 6.1 引用文档
* **产品需求文档**: `be-s1-2-ai-prd-学生端-题型组件-一期-claude-sonnet-4.md`
* **技术架构设计**: `be-s2-1-ai-td-学生端-题型组件-一期-claude-sonnet-4.md`
* **数据实体设计**: `be-s3-1-ai-de-学生端-题型组件-一期-claude-sonnet-4.md`
* **数据库设计**: `be-s4-2-ai-dd-学生端-题型组件-一期-claude-sonnet-4.md`
* **接口分析**: `be-s5-1-ai-ada-学生端-题型组件-一期-claude-sonnet-4.md`

### 6.2 核心行业标准
* **RESTful API设计规范**: 遵循REST架构风格，使用标准HTTP方法和状态码
* **JSON数据格式**: 遵循RFC 7159 JSON数据交换格式标准
* **JWT认证标准**: 遵循RFC 7519 JSON Web Token标准
* **ISO 8601日期时间格式**: 国际标准化组织日期和时间表示法

### 6.3 关键设计决策
1. **统一接口设计**: 不同题型共用核心接口，通过参数区分，提高代码复用性
2. **异步处理策略**: 学习记录和统计数据采用异步处理，提升响应性能
3. **事件驱动架构**: 通过事件机制实现服务间松耦合，支持扩展性
4. **多媒体支持**: 解答题支持图片上传，最多4张，满足复杂作答需求
5. **异常行为监控**: 实时检测自评异常，连续3题<2秒触发提示

### 6.4 性能优化建议
1. **缓存策略**: 题目信息使用Redis缓存，TTL 1小时
2. **数据库优化**: 为高频查询建立复合索引，使用连接池管理
3. **异步处理**: 学习记录和统计数据异步更新，避免阻塞主流程
4. **文件处理**: 图片上传使用CDN加速，支持压缩和格式转换
5. **监控告警**: 接口响应时间、错误率、异常行为实时监控

### 6.5 后续迭代规划
1. **二期功能**: 母子题支持、特殊题型扩展（完形填空、阅读理解、七选五）
2. **三期功能**: AI辅助批改、多模态交互、学习分析平台
3. **性能优化**: 事件溯源架构、微服务网格、边缘计算支持

---

**文档完成时间**: 2025-05-30
**撰写模型**: claude-sonnet-4
**文档状态**: 草稿，待技术评审

--- API使用文档产出完毕，等待您的命令，指挥官
# 产品需求文档 - 学生端巩固练习 v1.0

## 1. 需求背景与目标

### 1.1 需求目标
- 通过巩固练习功能的实现，可以提升学生在AI课后的知识掌握程度和学习效果
- 该需求上线后，学生练习完成率预计可以提升30%，知识点掌握度预计可以达到85%以上
- 通过再练一次机制，确保每个学生都能达到掌握知识的目标，提升整体学习效果

### 1.2 需求背景
巩固练习是产品的核心学习场景之一，是在AI学习后进行的巩固作用的练习，和AI课知识点关联度非常高。调研发现原练习存在缺乏有效的情感反馈、激励机制不足等问题，需要整体优化学习体验，提升学习效果。

### 1.3 覆盖用户
初中、高中学生

## 2. 功能范围

### 2.1 核心功能
- **巩固练习入口管理**: 支持练习状态展示和入口变化，满足学生快速进入练习场景，解决练习入口不明确的痛点
  - 优先级：高
  - 依赖关系：无

- **练习环节执行**: 包含题目展示、作答功能、进度管理和计时，满足学生完整的答题体验，解决练习流程不流畅的痛点
  - 优先级：高
  - 依赖关系：依赖于巩固练习入口管理

- **题目间转场反馈**: 提供作答反馈、难度调整提示、连胜激励等，满足学生获得及时反馈的需求，解决缺乏激励机制的痛点
  - 优先级：高
  - 依赖关系：依赖于练习环节执行

- **学习报告生成**: 展示练习结果、掌握度变化和学习建议，满足学生了解学习效果的需求，解决学习成果不可见的痛点
  - 优先级：高
  - 依赖关系：依赖于练习环节执行

### 2.2 辅助功能
- **勾画组件**: 支持题目标记和勾画功能，为练习环节提供辅助工具支持
- **错题本组件**: 自动和手动添加错题功能，为学习效果跟踪提供支持
- **再练一次环节**: 基于掌握度判断的重复练习机制，为学习效果保障提供支持

### 2.3 非本期功能
- **完整积分系统**: 当前仅展示积分获得，完整的积分兑换和使用机制建议迭代2再考虑
- **社交功能扩展**: 小组排名的详细展示和互动功能，建议迭代3再考虑

## 3. 计算规则与公式

### 3.1 掌握度计算规则
- **掌握度提升公式**: 掌握度变化 = f(答题正确率, 题目难度, 完成时间)
  - 参数说明：
    - 答题正确率: 本次练习中答对题目数/总题目数
    - 题目难度: 题目的难度系数(1-5级)
    - 完成时间: 相对于标准时间的比值
  - 规则：
    - 取值范围：掌握度为0-100%

### 3.2 进度计算规则
- **进度计算公式**: 进度 = 已完成有效题目数 / 预估总题目数
  - 参数说明：
    - 已完成有效题目数: 根据推题策略确定的有效完成数
    - 预估总题目数: 基于推题策略的预估值
  - 规则：
    - 答对题目或无相似题时进度增加
    - 答错但有相似题需要重练时进度不变

### 3.3 积分计算规则
- **积分获得公式**: 积分 = 基础分 × 难度系数 × 连胜加成
  - 参数说明：
    - 基础分: 每题的基础积分值
    - 难度系数: 题目难度对应的系数
    - 连胜加成: 连续答对的加成倍数
  - 规则：
    - 取值范围：单题积分1-50分

## 4. 用户场景与故事

### 场景1: 学生首次进入巩固练习
作为一个初中学生，我希望能够在完成AI课程后立即进行巩固练习，这样我就可以及时检验和巩固刚学到的知识点。目前的痛点是不知道从哪里开始练习，如果能够有清晰的练习入口和引导，对我的学习会有很大帮助。

**关键步骤：**
1. 在学科页面找到巩固练习卡片
2. 点击"进入练习"按钮
3. 观看"开始练习"转场动画
4. 开始第一道题目的作答

- 优先级：高
- 依赖关系：无

### 场景2: 学生中途退出后继续练习
作为一个高中学生，我希望能够在中途退出练习后还能继续之前的进度，这样我就可以灵活安排学习时间而不用担心进度丢失。目前的痛点是担心退出后要重新开始，如果能够保存进度并支持继续练习，对我的学习安排会有很大帮助。

**关键步骤：**
1. 在练习过程中点击退出按钮
2. 确认退出并保存进度
3. 稍后返回点击"继续练习"
4. 从上次的题目继续作答

- 优先级：中
- 依赖关系：依赖于场景1

### 场景3: 学生查看学习报告和再练一次
作为一个学生，我希望能够在完成练习后看到详细的学习报告，这样我就可以了解自己的学习效果和需要改进的地方。目前的痛点是不知道自己哪些知识点掌握得不好，如果能够有清晰的报告和针对性的再练建议，对我的学习提升会有很大帮助。

**关键步骤：**
1. 完成所有练习题目
2. 查看学习报告页面
3. 了解掌握度变化和答题详情
4. 根据建议选择"再练一次"

- 优先级：中
- 依赖关系：依赖于场景1

## 5. 业务流程图

```mermaid
graph TD
    A[开始练习] --> B{练习状态}
    B -->|首次进入| C[进入练习转场]
    B -->|继续练习| D[继续练习转场]
    C --> E[题组转场]
    D --> E
    E --> F[当前题目]
    F --> G[用户作答]
    G --> H{作答结果}
    H -->|正确| I[正确反馈]
    H -->|错误| J[错误反馈]
    I --> K[连胜检查]
    J --> L[难度调整]
    K --> M{是否连胜}
    M -->|是| N[连胜反馈]
    M -->|否| O[下一题检查]
    L --> O
    N --> O
    O --> P{是否最后一题}
    P -->|否| F
    P -->|是| Q[练习报告]
    Q --> R{是否需要再练}
    R -->|是| S[再练一次]
    R -->|否| T[完成练习]
    S --> C
    F --> U[中途退出]
    U --> V[保存进度]
    V --> W[继续练习入口]
    W --> D
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：题目加载、作答提交、转场动画的P95响应时间应不超过2秒
- 并发用户数：系统需能支持至少1000名学生同时在线进行巩固练习，且性能无明显下降
- 数据处理能力：系统需能处理每秒500次作答提交请求
- 数据量：系统需支持千万级学生练习数据的存储和查询，关键查询场景（如学习报告生成）响应时间应在3秒内

### 6.2 安全需求
- 用户认证与授权：所有练习相关接口必须经过学生身份认证和权限校验，确保学生只能访问自己的练习数据
- 数据保护：学生的练习记录、答题数据、学习报告等敏感信息在存储和传输过程中必须加密
- 操作审计：对学生的关键学习操作（如练习开始、完成、退出）必须记录详细的操作日志，包含学生ID、操作时间、练习内容等关键信息
- 防护要求：系统应具备对恶意刷题、异常提交等行为的检测和防护能力

## 7. 验收标准

### 7.1 功能验收
- **巩固练习入口管理**：
  - 使用场景：当学生完成AI课程后在学科页面查看巩固练习时
  - 期望结果：系统应正确展示练习状态（未完成/已完成），显示预估题目数量，提供正确的操作按钮（进入练习/继续练习/看报告/再练一次）

- **练习环节执行**：
  - 使用场景：当学生在练习过程中进行答题操作时
  - 期望结果：系统应正确展示题目内容，支持各种题型的作答，准确记录答题时间，实时更新进度条

- **题目间转场反馈**：
  - 使用场景：当学生完成一道题目后
  - 期望结果：系统应根据作答结果展示相应的反馈动画和文案，在连胜或难度变化时给出特殊提示

- **学习报告生成**：
  - 使用场景：当学生完成所有练习题目后
  - 期望结果：系统应生成包含掌握度变化、答题统计、错题分析的完整报告，并提供查看详情功能

### 7.2 性能验收
- 响应时间：
  - 测试场景：模拟100个学生同时进行练习，包括题目加载、作答提交、报告生成等操作
  - 验收标准：P95响应时间不超过2秒

- 并发用户数：
  - 测试场景：逐步增加并发学生数至1000人，持续进行练习操作30分钟
  - 验收标准：系统保持稳定运行，无错误率飙升，核心操作平均响应时间不超过3秒

### 7.3 安全验收
- 用户认证与授权：
  - 测试场景：未登录学生尝试访问练习功能
  - 验收标准：系统应拒绝访问，并跳转到登录页面
  - 测试场景：已登录学生访问自己的练习数据
  - 验收标准：学生可以正常访问和操作自己的练习内容

- 数据保护：
  - 验证点：通过数据库检查和网络抓包验证敏感数据加密情况
  - 验收标准：学生练习数据在存储和传输层面符合加密标准

- 操作审计：
  - 测试场景：学生进行练习开始、答题、完成等操作
  - 验收标准：所有关键操作被准确记录，包含学生ID、时间戳、操作内容等必要字段

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：练习功能的操作界面应简洁直观，学生在无额外培训的情况下，3步内可完成主要练习操作
- 容错处理：当发生网络中断或误操作时，系统应能给出清晰的错误提示和引导，自动保存学生进度，避免数据丢失
- 兼容性：练习功能需在主流移动设备和浏览器得到良好支持，包括iOS最新版、Android主流版本，保证界面和功能正常

### 8.2 维护性需求
- 配置管理需求：关键练习参数（如题目数量阈值、掌握度计算权重、积分奖励规则）应支持运营人员通过管理后台进行调整，调整后可实时生效
- 监控与告警需求：练习核心流程（如练习完成率异常下降、系统响应时间超标、题目加载失败率突增）发生异常时，系统应触发告警通知技术和产品团队
- 日志需求：学生关键学习操作（如练习开始、题目作答、练习完成、错题收藏）必须被记录，日志应包含学生ID、操作时间、练习内容、设备信息等上下文，支持学习行为分析和问题排查

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 产品应提供至少2种用户反馈渠道：应用内反馈入口、客服反馈系统
- 应有机制每周收集、整理和分析来自各渠道的学生反馈，识别练习功能的问题和改进机会

### 9.2 迭代计划（高阶产品方向）
- 产品计划以每双周为周期进行迭代，持续优化练习体验和学习效果
- 下一个迭代重点可能包括：积分系统完善、社交功能增强、个性化练习推荐算法优化

## 10. 需求检查清单

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 巩固练习入口状态展示和交互 | 2.1核心功能-巩固练习入口管理 | ✅ | ✅ | ✅ | ✅ | ✅ | 包含未完成/已完成状态 |
| 进入练习转场设计 | 4.用户场景-场景1关键步骤 | ✅ | ✅ | ✅ | ✅ | ✅ | 首次进入和继续练习转场 |
| 练习环节页面框架和作答功能 | 2.1核心功能-练习环节执行 | ✅ | ✅ | ✅ | ✅ | ✅ | 包含题型组件、计时、进度 |
| 题目间转场反馈机制 | 2.1核心功能-题目间转场反馈 | ✅ | ✅ | ✅ | ✅ | ✅ | 正确/错误/连胜/难度反馈 |
| 学习报告展示和交互 | 2.1核心功能-学习报告生成 | ✅ | ✅ | ✅ | ✅ | ✅ | 掌握度变化、答题详情 |
| 再练一次环节 | 2.2辅助功能-再练一次环节 | ✅ | ✅ | ✅ | ✅ | ✅ | 基于掌握度判断触发 |
| 勾画组件功能 | 2.2辅助功能-勾画组件 | ✅ | ✅ | ✅ | ✅ | ✅ | 颜色、粗细、橡皮擦等 |
| 错题本组件功能 | 2.2辅助功能-错题本组件 | ✅ | ✅ | ✅ | ✅ | ✅ | 自动添加和手动添加 |
| 中途退出和继续练习 | 4.用户场景-场景2 | ✅ | ✅ | ✅ | ✅ | ✅ | 进度保存和恢复机制 |
| 题组转场机制 | 5.业务流程图-题组转场节点 | ✅ | ✅ | ✅ | ✅ | ✅ | 多题组时的转场展示 |
| 作答计时功能 | 2.1核心功能-练习环节执行 | ✅ | ✅ | ✅ | ✅ | ✅ | 每题独立计时 |
| 进度展示逻辑 | 3.2进度计算规则 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 推题策略细节需确认 |
| 不认真作答反馈 | 2.1核心功能-题目间转场反馈 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 判定规则需进一步明确 |
| 难度变化反馈 | 2.1核心功能-题目间转场反馈 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 难度调整算法需确认 |
| 连胜反馈机制 | 2.1核心功能-题目间转场反馈 | ✅ | ✅ | ✅ | ✅ | ✅ | 连续答对的激励反馈 |
| 小组排名反馈 | 2.3非本期功能-社交功能扩展 | ✅ | ✅ | ✅ | N/A | ✅ | 建议后续迭代实现 |
| 熔断机制 | 6.2安全需求-防护要求 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 具体熔断规则需确认 |
| 积分获得和展示 | 3.3积分计算规则 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 完整积分系统待后续迭代 |
| 掌握度计算和展示 | 3.1掌握度计算规则 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 具体算法参数需确认 |
| 推荐学习模块 | 4.用户场景-场景3关键步骤 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 推荐策略需进一步明确 |

## 11. 附录

### 11.1 原型图
- 练习入口原型图：参考原始PRD中的入口状态展示图
- 练习环节原型图：参考原始PRD中的答题界面设计图
- 学习报告原型图：参考原始PRD中的报告页面设计图

### 11.2 术语表
- **巩固练习**: 在AI课程学习后进行的知识巩固练习，与课程知识点高度关联
- **掌握度**: 学生对特定知识点的掌握程度，通过答题表现计算得出
- **再练一次**: 基于学生掌握度判断，推荐进行的额外练习环节
- **题组**: 按知识点或难度分组的题目集合
- **熔断机制**: 防止异常情况下系统过载的保护机制

### 11.3 参考资料
- 巩固练习相关策略文档
- 题型支持一期PRD文档
- 内容管理平台选题支持文档
- 教师端作业布置相关文档
# 产品需求文档：教师端作业报告 v1.1

## 1. 需求背景与目标

### 1.1 需求目标

- **业务层面**：为教师提供高效的工具来监控和分析学生在课程作业、资源学习等任务中的表现，包括完成进度、答题正确率、用时、错题分布以及学生提问情况。通过及时的数据反馈，赋能教师进行有针对性的教学干预（如鼓励表扬、提醒关注），从而提升教学质量和学生学习效果。
- **技术层面**：构建清晰、可扩展的任务、素材与学生行为数据之间的底层关联，为后续更多类型的学情分析和智能化教学辅助功能奠定基础。
- **项目层面**：完成教师端P0核心功能，确保在约定6月上线版本能够跑通业务MVP流程，并在9月完成全部核心功能。预计上线后，教师查看学情报告的频率提升30%，进行教学干预的及时性提升50%。

### 1.2 用户价值

- 对于 **合作校的学科教师**：
    - **解决了** 过去难以高效、全面掌握每个学生具体学情的痛点，尤其是在多班级授课场景下。
    - **带来了** 快速了解学生完成度、定位共性错题、识别需关注学生、进行及时表扬或提醒的能力，提升了教学工作的针对性和效率。
- 对于 **合作校的班主任**：
    - **解决了** 难以跨学科了解班级整体学风和个体学生学习状态的问题。
    - **带来了** 查看本班各科作业数据、进行班级整体学情分析的能力，为班级管理和学生辅导提供数据支持。
- 对于 **合作校的学科主任/年级主任/校长**：
    - **解决了** 缺乏有效工具从更高维度审视教学质量和学生学业水平的问题。
    - **带来了** 查看所辖范围内的教学数据报告，了解教学效果，为教学管理和决策提供依据。

## 2. 功能范围

### 2.1 核心功能

### **核心功能点1: 作业列表展示与管理**

**简洁描述该功能是什么:** 为教师提供一个集中展示其所负责或有权限查看的全部教学任务（课程、作业、资源、测验）的列表界面，并支持对这些任务进行筛选、搜索和基本管理操作。

**用户价值与场景:** 满足教师日常教学管理中快速概览已布置任务整体状态、查找特定任务进行后续操作（如查看报告、编辑等）的需求。解决任务繁多时难以快速找到目标任务、缺乏对任务状态直观了解的痛点。为用户带来提高教学任务管理效率、提供清晰任务概览和便捷查找途径的核心价值。

**功能详述与前端呈现:**

- **主要交互流程:**
    1. 教师进入作业相关模块，默认展示作业列表。
    2. 教师可以通过顶部的Tab页（全部、课程、作业、资源、测验）切换不同类型的内容列表。
    3. 教师可以使用筛选器（如学科、年级、班级、布置日期）组合筛选列表内容。
    4. 教师可以在搜索框中输入关键词，模糊匹配任务名称进行搜索。
    5. 列表以卡片形式展示每个任务的核心信息。
    6. 对于本人布置的任务，教师可以点击卡片上的操作菜单（如"..."标记）进行编辑、删除、复制等操作。
    7. 点击任务卡片，跳转到该任务的作业报告详情页。
- **界面布局与元素:**
    - **布局原则:** 移动端界面，采用上下滑动列表形式。顶部为类型切换Tab和筛选区域，下方为内容卡片列表。
    - **核心UI元素:**
        - **内容类型Tabs:** "全部", "课程", "作业", "资源", "测验"。选中状态高亮。
        - **筛选器:**
            - 学科筛选器：下拉选择或弹出选择，如"数学"。支持多选或单选。
            - 年级/班级筛选器：层级选择或下拉选择，如"全部年级" -> "高一" -> "高一3班"。
            - 日期范围筛选器：预设选项（如"最近一周"、"最近一月"、"全部日期"）及自定义日期区间选择（日历控件）。
        - **搜索框:** 带有占位符提示"搜索任务名称"，支持实时搜索或点击搜索按钮后搜索。提供清空按钮。
        - **任务卡片:**
            - **标题:** 显示任务名称，如"3.2.2函数的最值"。
            - **班级/范围:** 显示所属班级或适用范围，如"高一3班"。
            - **类型标识:** 明确标识任务类型（课程、作业、资源、测验）。
            - **状态标识:** 如"进行中"、"已到期"、"待批阅"、"已发布"。
            - **关键数据指标 (根据任务类型不同，P0期核心指标):**
                - 课程任务: 平均进度、课时数。
                - 作业/测验任务: 完成率、正确率、待关注题目数/共性错题数。
                - 资源任务: 学习人数、平均学习时长（或完成度）。
            - **时间信息:** 发布与到期时间，如"课程 3月16日08:30发布 - 3月20日16:00到期"。到期时间醒目提示，如使用不同颜色。
            - **操作菜单触发器:** 如"..."图标，点击后弹出操作选项（编辑、删除、复制，仅限本人布置且在特定状态下可用）。
- **用户操作与反馈:**
    - **切换Tab:** 点击Tab，列表内容异步加载并刷新，对应Tab高亮。加载过程中显示骨架屏或loading动画。
    - **使用筛选器:** 选择筛选条件后，列表内容异步加载并刷新。筛选器显示当前选中条件。若无结果，友好提示“暂无符合条件的任务”。
    - **搜索:** 输入关键词后，列表实时（或点击搜索按钮后）异步加载并刷新匹配结果。清空关键词后恢复原始列表或上次筛选结果。
    - **点击任务卡片:** 界面平滑过渡跳转到该任务的作业报告详情页。
    - **点击操作菜单:** 底部或侧边弹出菜单，选项根据权限和任务状态显示。点击具体操作（如删除）前，应有二次确认弹窗（例如：“确定要删除【任务名称】吗？删除后不可恢复。”）。操作成功或失败后，给出Toast提示，列表刷新。
    - **列表滚动加载:** 若任务数量过多，支持上拉加载更多或分页。
- **数据展示与更新:** 列表数据根据筛选和搜索条件动态更新。各项统计指标（完成率、正确率等）应为准实时更新（例如，后台每隔几分钟或用户下拉刷新时更新）。

**优先级：** 高 (P0)
**依赖关系：** 无。

### 用户场景与故事 (针对此核心功能点)

### 场景1：教师快速概览与筛选教学任务

作为一个 **学科老师**，我希望能够 **快速查看我布置和有权限查看的所有教学任务（课程、作业、资源、测验），并能通过类型、学科、班级、日期等条件进行筛选和搜索**，这样我就可以 **高效地管理我的教学任务，并快速定位到需要处理或查看报告的特定任务**。目前的痛点是 **任务多起来后，找一个特定的任务很费时，也无法直观了解任务的整体状态**，如果能够 **提供清晰的列表和便捷的筛选搜索功能**，对我的工作效率会有很大帮助。

**前端界面与交互关键步骤：**

1. **用户操作1:** 教师打开教师端应用，进入"作业"或类似模块。
    - **界面变化:** 默认显示"作业"类型的任务列表，卡片式展示，包含任务名称、班级、截止日期、完成率等关键信息。顶部有内容类型切换Tab（全部、课程、作业、资源、测验）和筛选区域（学科、班级、日期等）。
    - **即时反馈:** 列表加载动画（如骨架屏），加载完成后显示任务卡片。
2. **用户操作2:** 教师点击顶部的"课程"Tab。
    - **界面变化:** 任务列表内容更新为"课程"类型的任务，筛选条件保持或重置（根据设计）。"课程"Tab高亮。
    - **即时反馈:** 列表异步刷新，显示课程任务卡片。加载过程中有loading提示。
3. **用户操作3:** 教师点击"学科"筛选器，选择"数学"。
    - **界面变化:** 任务列表内容根据当前Tab类型（如"课程"）和"数学"学科进行过滤刷新。筛选器上显示已选"数学"。
    - **即时反馈:** 列表异步刷新。若无结果，提示“暂无数学相关的课程任务”。
4. **用户操作4:** 教师在搜索框输入"函数的最值"。
    - **界面变化:** 任务列表根据已有筛选条件和搜索关键词"函数的最值"进行模糊匹配任务名称后刷新。
    - **即时反馈:** 列表异步刷新，仅显示匹配的任务。若无结果，提示“未搜索到相关任务”。
5. **用户操作5:** 教师找到目标任务卡片，点击卡片。
    - **界面变化:** 页面跳转到该任务的"作业报告详情页"。
    - **即时反馈:** 页面切换动画。
6. **用户操作6:** 教师找到自己布置的某个作业卡片，点击卡片上的"..."操作菜单，选择“删除”。
    - **界面变化:** 弹出确认对话框：“确定删除此作业吗？删除后数据无法恢复。”
    - **即时反馈:** 用户点击“确定”后，显示loading，删除成功后toast提示“作业删除成功”，列表刷新。若删除失败，toast提示“删除失败，请重试”。

**场景细节补充：**

- **前置条件:** 教师已登录，并拥有相应班级和学科的教学任务数据。
- **期望结果:** 教师能够高效地找到并访问所需的教学任务及其报告，并能进行基本管理操作。
- **异常与边界情况:**
    - 网络请求失败时，应有友好提示（如“网络不给力，请稍后重试”）并允许用户手动刷新重试。
    - 筛选结果为空时，应明确提示"暂无符合条件的任务"。
    - 搜索结果过多时，支持分页或滚动加载。
    - 无任何任务时，列表区域应有空状态提示，如“您还没有布置任何任务，快去创建吧！”。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (核心功能点本身的流程图/状态图等):**

```mermaid
flowchart TD
    A[用户进入作业列表页] --> B{选择内容类型Tab};
    B -- 课程 --> C[请求课程数据];
    B -- 作业 --> D[请求作业数据];
    B -- 资源 --> E[请求资源数据];
    B -- 测验 --> F[请求测验数据];
    B -- 全部 --> G[请求全部类型数据];

    subgraph "数据请求与渲染流程"
      direction LR
      C --> K[向服务端发送请求<br>（类型、筛选、搜索）];
      D --> K;
      E --> K;
      F --> K;
      G --> K;
      K --> L[服务端返回任务列表数据];
      L --> M[前端渲染任务卡片列表];
    end

    M --> H{使用筛选器};
    H -- 学科/年级/日期等 --> I[组合筛选条件并触发C/D/E/F/G];
    H -- 无额外筛选 --> J;

    J{输入搜索关键词或不输入};
    J -- 输入关键词 --> I;
    J -- 不输入 --> N;

    N{点击任务卡片?};
    N -- 是 --> O[跳转到作业报告详情页];
    N -- 否 --> P{点击卡片操作菜单?};
    P -- 是 --> Q[显示操作选项（编辑/删除/复制）];
    Q -- 选择操作 --> R{二次确认？（如删除）};
    R -- 是 --> S[执行操作并刷新列表];
    R -- 否 --> P;
    P -- 否 --> N;
    %% 用户继续浏览或操作

```

### **核心功能点2: 班级整体作业报告查看 (学生报告Tab)**

**简洁描述该功能是什么:** 教师点击作业列表中的某个任务卡片后，进入该任务的作业报告详情页，默认展示"学生报告"Tab，提供班级整体的学情概览、学生表现分群（值得表扬/需要关注）、班级统计数据以及详细的学生列表。

**用户价值与场景:** 满足教师了解某次具体作业在班级内的整体完成情况，快速识别表现突出和需要帮助的学生，并查看每个学生的具体表现的需求。解决逐个分析学生数据耗时耗力、难以快速把握班级整体学情和个体差异的痛点。为用户提供一站式的班级作业数据分析视图，提升教师对学情的掌控能力，支持数据驱动的教学干预的核心价值。

**功能详述与前端呈现:**

- **主要交互流程:**
    1. 教师从作业列表点击特定作业卡片，进入作业报告详情页。
    2. 页面默认显示"学生报告"Tab。
    3. 教师可以查看作业基本信息（发布/截止时间、范围）、班级整体进度、待关注题目数。
    4. 教师可以查看系统推荐的"值得表扬"和"需要关注"学生列表，并执行"一键表扬"或"一键提醒"操作。
    5. 教师可以查看班级平均正确率和平均用时。
    6. 教师可以通过搜索框搜索学生姓名，查看学生列表。
    7. 教师可以点击学生列表中的"查看详情"操作，进入单个学生的作业报告。
    8. 教师可以点击"导出"按钮导出学生数据。
- **界面布局与元素:** (参照原始PRD中 `image_PswKbOca7ouLbDxJzFScXHognOc` 的分析)
    - **导航栏:** 返回按钮、作业标题、预警设置入口（若有）。标题应清晰指示当前作业。
    - **作业基础信息区:** 发布时间、截止时间、范围（如"全部（2节课）"）。
    - **班级整体概览区:**
        - 班级进度：以百分比和进度条形式展示。
        - 待关注题目数：明确数字，点击可跳转至“答题结果”Tab并筛选出这些题目。
    - **Tab切换区:** "学生报告"（默认选中，高亮）、"答题结果"、"学生提问"。
    - **快捷互动区:**
        - "值得表扬"学生列表：可横向滑动展示学生头像/姓名，附"一键表扬"按钮。若无学生，则提示“暂无值得表扬的学生”。
        - "需要关注"学生列表：可横向滑动展示学生头像/姓名，附"一键提醒"按钮。若无学生，则提示“暂无需要关注的学生”。
    - **班级统计数据区:** 清晰展示班级平均正确率、班级平均用时。
    - **学生列表管理区:**
        - 学生姓名搜索框：支持模糊搜索，实时筛选下方列表。
        - "导出"按钮：点击后触发数据导出流程。
    - **学生详细数据列表区:**
        - **表头:** 学生信息（姓名、头像）、分层标签、完成进度（百分比+进度条）、正确率（百分比）、答题难度（数值）、错题数/总题数、用时、操作。表头固定，列表可滚动。
        - **数据行:**
            - 学生姓名（可附头像）。
            - 分层标签（如"有进步"、"需关注"，可带颜色或图标区分，有tooltip解释标签含义）。
            - 完成进度（百分比及进度条）。
            - 正确率（百分比）。
            - 答题难度（如2.0，有tooltip解释含义）。
            - 错题/答题（如"1/16"）。
            - 用时（如"35分钟"）。
            - 操作："查看详情"链接或按钮。
        - **异常数据高亮:** 偏离均值较大数据（如正确率远低于平均值，用时过长/过短）进行特殊颜色标记（“飘色”），并有tooltip说明。
    - **分页控件:** 显示总数据条数（如"共49条数据"），若数据过多则提供翻页（上/下一页，页码选择）。
- **用户操作与反馈:**
    - **点击"一键表扬"/"一键提醒":** 按钮变为加载状态（如菊花图），操作成功后Toast提示"表扬已发送"或"提醒已发送"，按钮状态可变为"已表扬"/"已提醒"或恢复。对应学生可能收到系统通知。若操作失败，Toast提示失败原因。
    - **搜索学生:** 输入姓名，列表实时筛选显示匹配学生。若无匹配，列表提示“未找到该学生”。
    - **点击"导出":** 触发文件下载流程（如CSV、Excel），按钮变为“导出中...”，成功后提示“导出成功”。
    - **点击学生"查看详情":** 页面跳转至该学生的个人作业报告页面。
    - **切换Tab:** 页面内容平滑切换至对应Tab（答题结果、学生提问），URL可能相应变化。切换时有加载状态提示。
    - **列表排序:** 默认按UID排序，鼓励/关注学生置顶。表头各可排序列（如完成进度、正确率、用时）应有排序控件，点击可升/降序排列。
- **数据展示与更新:** 班级统计数据、学生列表数据均为动态加载。系统推荐的“值得表扬”和“需要关注”学生列表基于预设策略动态更新。

**优先级：** 高 (P0)
**依赖关系：** 依赖"核心功能点1：作业列表展示与管理"选择具体作业。

### 用户场景与故事 (针对此核心功能点)

### 场景1：教师查看班级整体作业报告并识别重点学生

作为一个 **班主任或学科老师**，我希望能够 **在进入某次作业的报告后，直观地看到班级整体的完成进度、平均正确率、平均用时，并快速识别出哪些学生表现"值得表扬"，哪些学生"需要关注"**，这样我就可以 **及时了解班级学情，并对不同表现的学生采取相应的激励或辅导措施**。目前的痛点是 **需要手动对比大量数据才能发现问题学生和优秀学生，效率较低**，如果能够 **系统自动汇总关键指标并推荐重点学生群体**，将极大提升我的工作效率。

**前端界面与交互关键步骤：**

1. **用户操作1:** 教师从作业列表点击进入某份作业的"作业报告详情页"，默认进入"学生报告"Tab。
    - **界面变化:** 页面顶部显示作业名称、发布/截止时间、班级整体进度（含进度条）、待关注题目数。中间区域显示"值得表扬"学生头像/姓名列表（可横向滑动）和"一键表扬"按钮，"需要关注"学生头像/姓名列表（可横向滑动）和"一键提醒"按钮。下方显示班级平均正确率、班级平均用时。再下方是详细的学生数据列表及搜索框。
    - **即时反馈:** 页面加载动画，数据显示后，进度条动态填充。
2. **用户操作2:** 教师查看"值得表扬"学生列表，并点击"一键表扬"按钮。
    - **界面变化:** "一键表扬"按钮变为"表扬中..."或显示加载动画。可能弹出二次确认框：“确定一键表扬这些学生吗？”
    - **即时反馈:** 操作成功后，Toast提示"表扬已发送"或类似信息。按钮恢复原状或变为"已表扬"状态。
3. **用户操作3:** 教师在学生列表中，通过姓名搜索框输入"李明"。
    - **界面变化:** 学生列表实时筛选，仅显示姓名包含"李明"的学生数据。列表上方显示“搜索结果 共X条”。
    - **即时反馈:** 列表内容动态变化。若无结果，提示“未找到学生 李明”。
4. **用户操作4:** 教师在学生列表中找到"李明"，其数据显示为"需关注"（例如：完成进度60%，正确率50%），点击该行右侧的"查看详情"按钮。
    - **界面变化:** 页面跳转到"李明"的个人作业报告详情页。
    - **即时反馈:** 页面切换动画。

**场景细节补充：**

- **前置条件:** 教师已进入特定作业的"学生报告"Tab。学生作业数据已统计完成。系统已根据策略生成推荐学生列表。
- **期望结果:** 教师能够快速掌握班级学情，并对重点学生进行初步干预或深入分析。
- **异常与边界情况:**
    - 若无学生符合"值得表扬"或"需要关注"的条件，对应区域应提示"暂无相关学生"或不显示该区域。
    - "一键表扬/提醒"操作时若网络失败，应提示操作失败并允许重试。按钮状态应恢复。
    - 学生列表数据量大时，应支持分页或虚拟滚动，确保性能。
    - 导出数据时，若数据量过大，应提示可能耗时较长，或提供异步导出方式。

**优先级：** 高
**依赖关系：** 依赖“核心功能点1：作业列表展示与管理”。

**Mermaid图示 (核心功能点本身的流程图/状态图等):**

```mermaid
flowchart TD
    A[进入作业报告详情页 - 学生报告Tab] --> B[显示作业基本信息];
    A --> C[显示班级整体概览（进度、待关注题目）];
    A --> D[显示'值得表扬'学生列表];
    D --> D1{点击'一键表扬'?};
    D1 -- 是 --> D_Confirm{确认表扬?};
    D_Confirm -- 是 --> D2[执行表扬操作<br>Toast提示结果];
    D_Confirm -- 否 --> E;
    D1 -- 否 --> E;

    A --> E[显示'需要关注'学生列表];
    E --> E1{点击'一键提醒'?};
    E1 -- 是 --> E_Confirm{确认提醒?};
    E_Confirm -- 是 --> E2[执行提醒操作<br>Toast提示结果];
    E_Confirm -- 否 --> F;
    E1 -- 否 --> F;

    A --> F[显示班级统计数据 （平均正确率、平均用时）];
    A --> G[显示学生列表管理区（搜索、导出）];
    G --> G0{搜索学生?};
    G0 -- 是 --> G2[根据姓名筛选学生列表];
    G0 -- 否 --> G1;
    G1{点击'导出'?};
    G1 -- 是 --> G3[执行导出操作];
    G1 -- 否 --> G4[显示学生详细数据列表];
    G2 --> G4;

    G4 --> H{点击某学生'查看详情'?};
    H -- 是 --> I[跳转到该学生个人作业报告];
    H -- 否 --> G4;
    %% 用户继续浏览或操作其他

    A --> X[切换到'答题结果'Tab];
    %% 页面内Tab切换
    A --> Y[切换到'学生提问'Tab];
    %% 页面内Tab切换

```

### **核心功能点3: 班级作业报告 - 答题结果Tab**

**简洁描述该功能是什么:** 在作业报告详情页，教师切换到"答题结果"Tab后，可以查看本次作业所有题目的整体作答情况，包括题目列表、每道题的正确率、错误人数、作答人数，并能筛选共性错题、按关键词搜索题目。

**用户价值与场景:** 满足教师希望了解班级在哪些题目上普遍存在困难（共性错题），哪些题目区分度较好，以便调整后续教学重点或进行针对性讲解的需求。解决难以快速从大量题目中定位到学生的薄弱点、缺乏对题目维度的整体认知的痛点。为用户帮助教师从题目维度分析学情，精准定位教学难点，优化教学内容和策略的核心价值。

**功能详述与前端呈现:** (参照原始PRD中 `image_Kde9bV1SroJspDxtJQeccHOCneg` 和 `image_QbOCbI3ssoaDfKxamzIcZbB8nWd` 的分析)

- **主要交互流程:**
    1. 在作业报告详情页，教师点击"答题结果"Tab。
    2. 页面展示作业的整体题目统计（总题数、共性错题数）和排序方式说明。
    3. 教师可以使用搜索框搜索题目关键词。
    4. 教师可以使用"只看共性错题"开关进行筛选。
    5. 教师可以按题型进行筛选。
    6. 题目列表展示每道题的题干预览、正确率、错误人数、作答人数。
    7. 教师可以点击题目的"查看"操作，进入该题目的详细作答分析页。
    8. 教师可以点击题目的"加入"操作（如加入错题本、教学资源库）。
    9. （可选）侧边栏可能提供题目面板，快速导航至不同题目，并用颜色区分正确率。
- **界面布局与元素:**
    - **整体统计区:** 显示"共XX道题，XX道共性错题"，并注明排序方式（如"已按照错误人数排序"）。共性错题数量可点击，效果同“只看共性错题”开关。
    - **题目筛选与搜索区:**
        - 搜索框："搜索题目关键词"。
        - 开关："只看共性错题" (toggle switch)。
        - 下拉筛选："全部题型"（或其他题型选项，如单选、多选、判断、填空、简答等）。
    - **题目列表区:**
        - **单个题目卡片:**
            - 题号（按当前排序规则）。
            - 题干预览（支持图文混排，过长可折叠，点击"展开"查看全部，"收起"恢复）。
            - 选项预览（根据题型动态布局，如单选题一行1/2/4个选项，支持图文）。
            - 答题统计：正确率（百分比）、XX人错误、XX人作答。错误人数可突出显示。
            - 操作按钮："加入"（如图标按钮，hover提示文字，如“加入错题本”）、"查看"（文字按钮）。
            - （可选）答案与解析入口：默认收起，点击展开显示简要答案，或提示在“查看”详情页中。
    - **侧边题目面板 (可选，P0期可不做):** 默认展开，可收起。列表展示题号，用颜色区分题目正确率（如红色-低，黄色-中，绿色-高）。点击题号可快速定位到题目列表中的对应题目，并高亮。
    - **分页控件:** 若题目数量多，则提供分页。
- **用户操作与反馈:**
    - **切换Tab至"答题结果":** 内容区域更新为题目列表及相关控件。显示加载动画。
    - **搜索/筛选题目:** 题目列表异步更新。输入关键词实时筛选，或点击搜索图标后筛选。筛选条件变化时，列表刷新，加载动画。无结果时提示“暂无符合条件的题目”。
    - **点击"查看":** 页面跳转到该题目的题目详情页。
    - **点击"加入":** 给出成功提示Toast（如“已加入错题本”），对应题目状态可能更新（如图标变化）。若操作失败，提示原因。
    - **展开/收起题干/解析:** 内容区域动态变化，平滑过渡。
    - **点击侧边题目面板题号:** 主列表滚动到对应题目，并可能有高亮提示。
- **数据展示与更新:**
    - 题目列表根据筛选条件动态加载。统计数据（正确率、错误人数等）基于学生首次作答数据。
    - 共性错题定义：P0阶段定义为"答题人数>10人，且任务中正确率＜60%"（此定义应清晰告知用户，或在筛选旁有tooltip解释）。P1期支持自定义阈值。
    - 正确率定义：对作答了的同学，答对小空数量 / 题目中的总小空之和 * 100%。
    - 已提交的课堂、测试、作业任务下，学生看到题、选“暂不作答”的记作已作答、答错。

**优先级：** 高 (P0)
**依赖关系：** 依赖"核心功能点1：作业列表展示与管理"选择具体作业，并进入作业报告。

### 用户场景与故事 (针对此核心功能点)

### 场景1：教师分析班级作业的题目作答情况

作为一个 **学科老师**，我希望能够 **在作业报告中切换到"答题结果"视图，查看所有题目的正确率、错误人数、作答人数等统计，并能快速筛选出"共性错题"或按关键词搜索特定题目**，这样我就可以 **准确了解学生在哪些知识点或题型上存在普遍困难，以便进行针对性的课堂讲解或调整教学策略**。目前的痛点是 **需要手动统计每道题的对错情况，耗时且易出错**，如果能够 **自动汇总题目维度的统计数据并高亮共性问题**，将非常有帮助。

**前端界面与交互关键步骤：**

1. **用户操作1:** 教师在某份作业的报告详情页，点击切换到"答题结果"Tab。
    - **界面变化:** 页面内容更新。顶部显示该作业"共XX道题，XX道共性错题"及排序方式。下方为题目搜索框、筛选条件（"只看共性错题"开关、"全部题型"下拉框）。再下方是题目列表，每条题目显示题干预览、正确率、错误人数、作答人数和"查看"、"加入"按钮。
    - **即时反馈:** Tab选中状态切换，内容区异步加载题目数据，显示骨架屏或loading。
2. **用户操作2:** 教师勾选"只看共性错题"开关。
    - **界面变化:** 题目列表刷新，只显示符合共性错题标准的题目。开关状态变为激活。
    - **即时反馈:** 列表异步更新。若无共性错题，提示“暂无共性错题”。
3. **用户操作3:** 教师在题目搜索框输入关键词"定义域"。
    - **界面变化:** 题目列表（在当前筛选条件下）根据关键词"定义域"模糊匹配题干、选项或解析后刷新。
    - **即时反馈:** 列表异步更新。若无匹配结果，提示“未搜索到相关题目”。
4. **用户操作4:** 教师找到一道错误率较高的题目，点击其右侧的"查看"按钮。
    - **界面变化:** 页面跳转到该题目的"题目详情页"，展示题目完整信息、答案解析、班级作答统计（如选项分布）等。
    - **即时反馈:** 页面切换动画。
5. **用户操作5:** 教师点击某题的"加入"按钮。
    - **界面变化:** "加入"按钮可能变为不可用或显示已加入状态。
    - **即时反馈:** Toast提示“已成功加入错题本”或类似信息。

**场景细节补充：**

- **前置条件:** 教师已进入特定作业的"答题结果"Tab。学生作业数据已统计完成。
- **期望结果:** 教师能够高效分析题目作答情况，定位教学难点，并能对感兴趣的题目进行标记或管理。
- **异常与边界情况:**
    - 若无题目符合"共性错题"条件，列表应提示"暂无共性错题"。
    - 搜索无结果时，应提示"未找到相关题目"。
    - 题目内容过长时，应能折叠和展开，保证界面整洁。
    - 网络请求失败，显示错误提示和重试按钮。

**优先级：** 高
**依赖关系：** 依赖作业报告主体功能。

**Mermaid图示 (核心功能点本身的流程图/状态图等):**

```mermaid
flowchart TD
    A[进入'答题结果'Tab] --> B[显示整体题目统计（总题数、共性错题数）与排序说明];
    A --> C[显示题目筛选与搜索控件];
    C --> C1{使用搜索框?};
    C1 -- 输入关键词 --> C2[根据关键词过滤题目列表];
    C1 -- 否 --> C3;
    C3{切换'只看共性错题'?};
    C3 -- 是 --> C4[筛选显示/隐藏共性错题];
    C3 -- 否 --> C5;
    C5{选择'题型筛选'?};
    C5 -- 选择题型 --> C6[根据题型过滤题目列表];
    C5 -- 否 --> D;

    C2 --> D;
    C4 --> D;
    C6 --> D;

    A --> D[显示题目列表（题干预览、统计数据、操作按钮）];
    D --> E{点击题目'查看'按钮?};
    E -- 是 --> F[跳转到该题目的题目详情页];
    E -- 否 --> G;
    G{点击题目'加入'按钮?};
    G -- 是 --> H[执行加入操作（如加入错题本）并反馈结果];
    G -- 否 --> I;
    I{点击题干'展开/收起'?};
    I -- 是 --> J[动态展开或收起题干内容];
    I -- 否 --> K;
    K{使用分页控件? （若有）};
    K -- 是 --> L[加载对应页码的题目列表];
    K -- 否 --> D;
    %% 用户继续浏览或操作

```

### **核心功能点4: 班级作业报告 - 学生提问Tab**

**简洁描述该功能是什么:** 在作业报告详情页，教师切换到"学生提问"Tab后，可以查看学生在本次AI课（或其他支持提问的任务类型）中针对不同课程内容（如幻灯片、知识点）提出的问题。

**用户价值与场景:** 满足教师希望了解学生在学习过程中主动提出的疑问，从而把握学生的困惑点，为答疑和后续教学提供参考的需求。解决学生提问分散，不易集中查看和管理；难以将问题与具体教学内容关联的痛点。为用户集中展示学生提问，帮助教师了解学生的学习难点和思考过程，促进师生互动和教学改进的核心价值。

**功能详述与前端呈现:** (参照原始PRD中 `image_CMoSbVPqcoznYTxrEPec4qDQnce` 和 `image_An8wbFUSLo9yJjxSrPicif6ynbg` 的分析)

- **主要交互流程:**
    1. 在作业报告详情页，教师点击"学生提问"Tab。
    2. 页面展示与当前任务（如AI课）相关的、有学生评论/提问的素材内容结构（如按课程幻灯片或知识点章节组织）。
    3. 内容结构列表清晰展示每个内容节点，并显示该节点下本班学生的评论/提问数统计。
    4. 教师可以点击某个内容节点，查看该节点下的具体提问列表。
    5. 教师可以查看提问详情（提问人、时间、内容），以及可能的AI回复或教师的已有回复。
    6. （可选P0+）教师可能可以进行回复或标记处理。
- **界面布局与元素:**
    - **内容筛选/概览区 (可能存在):** 显示当前筛选的班级、素材范围。顶部可能显示总提问数。
    - **内容结构列表区 (左侧或上方):**
        - 以树状结构或可展开列表形式展示课程的内容节点（如章节、幻灯片、知识点）。
        - **单个内容节点:**
            - 节点标题/描述 (如"引入部分"、"复数分类")。
            - 该节点下本班学生提问数统计 (如"4条讨论"、"10条提问")，数字可点击。
            - 点击节点后，右侧（或下方）联动展示该节点的提问列表。选中节点高亮。
    - **提问列表与详情区 (右侧或下方):**
        - 当选中某个内容节点后，展示该节点下的提问列表。若无提问，则提示“暂无学生提问”。
        - **单个提问卡片:**
            - 学生姓名/头像。
            - 提问时间。
            - 提问内容（过长可折叠展开）。
            - （若支持）教师回复、其他学生回复，清晰区分。
            - （可选P0+）操作按钮（如"回复"、"标记为已解决"、"查看上下文"——跳转到课程对应位置）。
    - **空状态提示:** 若整个任务均无学生提问，则在Tab内容区友好提示。
- **用户操作与反馈:**
    - **切换Tab至"学生提问":** 内容区域更新为提问相关界面。显示加载动画。
    - **点击内容节点:** 右侧（或下方）提问列表异步加载并更新，展示该节点下的提问。节点选中状态变化。加载时有提示。
    - **查看提问详情:** 若提问内容过长，点击可展开。若有回复，清晰展示。
    - **（可选P0+）进行回复/处理操作:** 若支持回复，点击回复按钮后出现输入框，提交后，界面更新，如显示新回复，或提问状态改变。操作成功有Toast提示。
- **数据展示与更新:** 提问列表和数量统计动态加载。新提问或回复应能准实时更新或通过下拉刷新获取。

**优先级：** 高 (P0)
**依赖关系：** 依赖"核心功能点1：作业列表展示与管理"选择具体作业，并进入作业报告。仅适用于支持提问功能的任务类型（如AI课）。

### 用户场景与故事 (针对此核心功能点)

### 场景1：教师查看学生在AI课中的提问

作为一个 **学科老师**，我希望能够 **在AI课的作业报告中，方便地查看学生针对课程不同环节提出的问题**，这样我就可以 **快速了解学生在哪些知识点上存在困惑，从而在后续的答疑或教学中更有针对性**。目前的痛点是 **学生的提问可能分散在各个地方，或者我需要进入每个学生的学习记录才能看到，很不方便**，如果能 **将所有提问按课程结构汇总展示**，将大大提高我的效率。

**前端界面与交互关键步骤：**

1. **用户操作1:** 教师在一份AI课的作业报告详情页，点击切换到"学生提问"Tab。
    - **界面变化:** 页面内容更新。左侧（或上部）展示该AI课的课程内容结构（如幻灯片1、知识点A、幻灯片2等），并标注每个结构节点下的学生提问数量。右侧（或下部）初始可能为空或显示欢迎信息。
    - **即时反馈:** Tab选中状态切换，异步加载课程结构和提问统计，显示loading。
2. **用户操作2:** 教师点击课程结构中的"知识点A (3条提问)"。
    - **界面变化:** 右侧（或下部）提问列表区刷新，展示"知识点A"下的3条学生提问。每条提问显示学生姓名、提问时间和内容。
    - **即时反馈:** "知识点A"在课程结构中高亮。右侧列表异步加载，显示loading后更新。
3. **用户操作3:** 教师看到一条提问内容较长，点击提问内容旁的"展开"按钮。
    - **界面变化:** 该条提问内容完全展示。
    - **即时反馈:** 内容平滑展开。
4. **用户操作4 (可选P0+):** 教师点击某条提问下的"回复"按钮。
    - **界面变化:** 该条提问下方出现回复输入框，教师可以输入回复内容。
    - **即时反馈:** 输入框获得焦点。
5. **用户操作5 (可选P0+):** 教师输入回复后，点击"提交"按钮。
    - **界面变化:** 输入框消失，教师的回复显示在该提问下方。
    - **即时反馈:** Toast提示“回复成功”。提问卡片状态可能更新。

**场景细节补充：**

- **前置条件:** 教师已进入特定AI课的"学生提问"Tab。学生已在该AI课中产生提问数据。
- **期望结果:** 教师能够高效地按课程结构浏览和理解学生的提问，并能进行必要的互动。
- **异常与边界情况:**
    - 若某个课程节点下无提问，点击后右侧应提示“该部分暂无学生提问”。
    - 若整个AI课均无学生提问，Tab内应有空状态提示，如“学生们还没有提问哦”。
    - 网络请求失败，显示错误提示和重试按钮。

**优先级：** 高
**依赖关系：** 依赖作业报告主体功能。仅适用于支持提问的任务类型。

**Mermaid图示 (核心功能点本身的流程图/状态图等):**

```mermaid
flowchart TD
    A[进入'学生提问'Tab] --> B[显示课程内容结构列表 及各节点提问数统计];
    B --> C{点击某个课程内容节点?};
    C -- 是 --> D[异步加载并展示该节点下的提问列表 （提问人、时间、内容、回复）];
    D --> E{查看具体提问详情（如展开长内容）?};
    E -- 是 --> F[展开/收起提问内容];
    E -- 否 --> G;
    G{进行回复或处理? （P0+ 可选）};
    G -- 是 --> H[显示回复输入框或处理选项];
    H --> I[用户提交操作];
    I --> J[更新界面（如显示新回复、更新状态）并反馈结果];
    J --> D;
    G -- 否 --> D;
    C -- 否 --> B;
    %% 用户继续浏览或选择其他节点

```

### **核心功能点5: 单个学生作业报告查看**

**简洁描述该功能是什么:** 教师从班级学生列表或其它入口进入某个特定学生的作业报告详情页，可以查看该生在本次作业中的详细答题情况（逐题对错、得分率）、提问记录等。

**用户价值与场景:** 满足教师需要对特定学生进行深入的学情分析，了解其知识掌握的薄弱点、学习习惯等，以便进行个性化辅导或与学生/家长沟通的需求。解决难以全面细致地了解单个学生的具体学习过程和问题的痛点。为用户提供精细化的个体学情数据，支持教师进行个性化教学和辅导的核心价值。

**功能详述与前端呈现:** (参照原始PRD中 `image_I4hibE2j6o3Kr4xlH1DcE0A3nag`, `image_FjLobW20loc0yVx64afcpujenMe`, `image_HauRbUdhqogkfyxqoVLcIIR2nPd` 等分析)

- **主要交互流程:**
    1. 教师通过入口（如班级报告的学生列表中的"查看详情"）进入学生个人作业报告。
    2. 页面顶部展示学生姓名和作业名称，以及作业的基本信息（发布/截止/完成时间、范围、进度）。
    3. 页面同样包含"答题结果"、"学生提问"等Tab页，逻辑与班级报告类似，但数据仅限该学生。
    4. 在"答题结果"Tab下，展示该生作业的整体统计（总题数、错题数），以及题目列表。
    5. 题目列表逐条显示题目、该生的作答状态（正确/错误/未作答，用符号或文字表示）、得分率（若适用）。
    6. 教师可点击"查看"进入题目详情，了解学生答案、标准答案、解析。
- **界面布局与元素:**
    - **导航栏:** 返回按钮、页面标题（格式："学生姓名 - 任务名称"）。
    - **学生与作业概览区:**
        - 学生头像/姓名。
        - 作业开始时间、要求完成时间。
        - 学生完成时间（若已完成）或当前进度（若未完成100%，显示百分比和进度条）。
        - 素材范围（如"全部（2节课）"，可点击查看更多）。
        - （根据PRD截图 `image_FjLobW20loc0yVx64afcpujenMe`）学生本次作业总得分/评价（如100%）、操作按钮（如"点赞"鼓励该生、"去处理"该生的问题或提问）。
    - **Tab切换区:** "答题结果"（默认选中）、"学生提问"（根据任务类型可能还有"学习报告"等）。
    - **"答题结果"Tab下 (学生个人版):**
        - **统计区:** "共XX道题，XX道错题"（针对该学生）。
        - **题目列表操作区:**
            - 搜索题目关键词：在当前学生作答的题目中搜索。
            - "只看错题"开关：筛选显示该学生自己的错题。
            - （可选）题型筛选。
        - **题目列表区:**
            - 题号。
            - 题干预览（同班级报告，支持图文混排、展开收起）。
            - 选项预览（若为选择题）。
            - **学生作答状态:**
                - 正确：✅ 标记，得分率100%。
                - 错误：❌ 标记，得分率0%（或具体小题得分）。
                - 部分正确：半对符号，具体得分率。
                - 未作答："!" 尚未作答，暂无结果。
            - 得分/得分率 (如100%，或 X/Y分)。
            - "查看"按钮：点击进入题目详情页（学生个人视角）。
    - **"学生提问"Tab下 (学生个人版):**
        - 逻辑类似班级报告的学生提问Tab，但只显示该学生在本次任务（如AI课）中提出的问题。
        - 按课程内容结构展示学生在该节点下的提问数量及内容。
        - 点击内容节点，展示该学生在该节点下的具体提问列表。
- **用户操作与反馈:**
    - **切换Tab:** 内容更新为该学生在该Tab下的数据。显示加载动画。
    - **题目列表操作 (搜索、筛选):** 题目列表根据条件异步更新。无结果时友好提示。
    - **点击题目"查看":** 跳转到该题的题目详情页，包含题目信息、该学生的作答答案、正确答案、题目解析。
    - **点击"点赞"按钮:** Toast提示“点赞成功”，按钮状态可能变化。
    - **点击"去处理"按钮:** 根据具体业务逻辑，可能跳转到学生提问详情页、与学生沟通的IM界面等。
- **数据展示与更新:** 所有数据均为该学生在本次作业中的个人数据，动态加载。

**优先级：** 高 (P0)
**依赖关系：** 依赖班级作业报告（核心功能点2）或其他学生入口。

### 用户场景与故事 (针对此核心功能点)

### 场景1：教师查看单个学生的详细作业报告

作为一个 **学科老师**，我希望能够 **查看特定学生（如"周雨彤"）在某份作业（如"3.2.2函数的最值"）中的详细作答情况，包括每道题的对错、得分，并能方便地查看其提问记录**，这样我就可以 **全面了解该学生的学习薄弱环节和疑问点，为个性化辅导提供依据**。目前的痛点是 **分散查看学生的不同维度信息较为繁琐**，如果能在一个报告中 **集中呈现学生的答题详情和提问记录**，将更为便捷。

**前端界面与交互关键步骤：**

1. **用户操作1:** 教师从班级作业报告的学生列表中，点击学生"周雨彤"所在行右侧的"查看详情"按钮。
    - **界面变化:** 页面跳转到"周雨彤 - 3.2.2函数的最值 作业报告"页面。顶部显示学生姓名、作业名、作业整体信息（发布/截止/完成时间、课程范围、完成进度80%）。下方有Tab切换（"答题结果"、"学生提问"）。默认显示"答题结果"Tab。
    - **即时反馈:** 页面加载动画，加载完成后显示学生个人报告。
2. **用户操作2:** 在"答题结果"Tab下，教师浏览题目列表，看到题目"ID:12395"，学生作答状态为"✅ 正确"，得分率"100%"；题目"ID:13035"，学生作答状态为"! 尚未作答，暂无结果"。
    - **界面变化:** 题目列表清晰展示了每道题学生个人的作答状态和结果。
    - **即时反馈:** 无特殊即时反馈，为静态信息展示。列表支持滚动。
3. **用户操作3:** 教师点击"学生提问"Tab。
    - **界面变化:** 内容区切换，展示该学生在此作业关联的课程内容中提出的问题列表。问题按课程结构（如幻灯片顺序）组织，并显示提问内容和时间。
    - **即时反馈:** Tab状态切换，异步加载并显示学生提问数据，有loading提示。若无提问，则提示“该学生暂无提问”。
4. **用户操作4:** 教师点击某条提问，查看提问详情及可能的AI回复或教师互动记录。
    - **界面变化:** 展开提问详情（若内容折叠）或跳转到提问详情页面（若设计如此）。
    - **即时反馈:** 加载并显示详细内容。

**场景细节补充：**

- **前置条件:** 教师已进入学生个人作业报告页面。学生已产生相应的作业数据和提问数据。
- **期望结果:** 教师能够全面了解单个学生在一次作业中的表现和疑问，为个性化辅导和沟通提供依据。
- **异常与边界情况:**
    - 若学生某Tab下无数据（如未提问、未作答任何题目），对应区域应提示"暂无相关内容"。
    - 题目或提问内容过长时，应支持展开查看全部。
    - 网络请求失败，显示错误提示和重试按钮。

**优先级：** 高
**依赖关系：** 依赖班级作业报告或其它学生入口。

**Mermaid图示 (核心功能点本身的流程图/状态图等):**

```mermaid
flowchart TD
    A[进入学生个人作业报告页] --> B[显示学生与作业基本信息（姓名、作业名、时间、进度等）];
    B --> C[默认显示'答题结果'Tab内容];
    C --> D[显示学生个人答题统计（总题数、错题数）];
    D --> E[显示学生个人题目列表（题干、作答状态✅❌、得分率）];
    E --> F{筛选/搜索题目? （如'只看错题'）};
    F -- 是 --> G[根据条件更新题目列表];
    F -- 否 --> H;
    G --> H;
    H{点击某题目'查看'按钮?};
    H -- 是 --> I[跳转到该题目的题目详情页（包含学生答案、标准答案、解析）];
    H -- 否 --> E; 
    %% 返回列表或继续操作

    B --> J[点击切换到'学生提问'Tab];
    J --> K[异步加载并显示学生个人提问记录（按课程结构组织）];
    K --> L{点击某内容节点或提问?};
    L -- 是 --> M[展示该节点下学生的具体提问内容或提问详情];
    L -- 否 --> K;

    B --> N{点击'点赞'或'去处理'等操作? （若有）};
    N -- 是 --> O[执行相应操作并反馈结果];
    N -- 否 --> B;

```

### **核心功能点6: 题目详情页查看**

**简洁描述该功能是什么:** 教师在班级报告的"答题结果"Tab或学生个人报告的"答题结果"Tab中，点击某道题的"查看"按钮后，进入该题目的详情页面。页面展示题目基本信息、答案解析，以及作答统计（班级视角）或学生个人作答情况（学生视角）。

**用户价值与场景:**

- **班级视角:** 教师想深入了解某道共性错题的错误选项分布、学生具体作答情况，以便准备讲解。
- **学生个人视角:** 教师想查看某个学生具体某道题的答案，以便进行针对性辅导。
解决仅看题目列表的统计数据不够深入，无法了解学生具体的思考和错误原因的痛点。为用户提供单题维度的深度分析，辅助教师进行精准的题目讲解和个体辅导的核心价值。

**功能详述与前端呈现:** (参照原始PRD中 `image_CLqrb4W3XoPbH4x4xlUcGgfMnuf` 的分析)

- **主要交互流程:**
    1. 教师从题目列表（班级报告或学生个人报告）点击某题的"查看"按钮。
    2. 进入题目详情页。页面根据来源（班级/学生）展示对应视角的内容。
    3. 页面展示题干、选项、题型、难度、知识点标签。
    4. 教师可展开查看答案与解析。
    5. **班级视角下:** 展示该题的整体作答统计（正答率、各选项选择人数及百分比、答对/错人数、平均用时），并可查看选择各选项的学生名单。
    6. **学生个人视角下:** 展示该题的题干、选项、答案解析，以及该学生选择的答案和对错情况、得分。
    7. （班级视角）可能提供"大屏讲解"快捷操作。
- **界面布局与元素:**
    - **导航栏:** 返回按钮、页面标题（如"题目详情"或题目编号）。
    - **快捷操作 (班级视角):** （可选）"大屏讲解"按钮。
    - **题目信息区:**
        - 题型（如单选题、多选题）、难度（如简单、中等、困难）、知识点标签（可多个，点击标签可进行相关操作，如查看同知识点题目）。
        - 题干内容（支持图文混排）。
        - 选项内容（A、B、C、D...，支持图文混排）。
    - **答案解析区:**
        - **正确答案:** 明确标识。
        - **解析内容:** 详细的文字、图片、公式解析。默认可能收起部分，提供"展开查看完整解析"功能。
    - **作答统计区 (班级视角):**
        - 正答率（百分比）。
        - 答对人数、答错人数。
        - 平均用时。
        - **选项分析:**
            - 每个选项的选择人数及百分比。
            - （可选）点击选项，可查看选择该选项的学生名单列表（可弹窗或新区域展示）。
    - **学生作答区 (学生个人视角):**
        - **学生选择的答案:** 明确标识。
        - **作答结果:** 正确/错误/部分正确。
        - **得分:** 该题的得分。
- **用户操作与反馈:**
    - **展开/收起答案解析:** 内容区动态变化，平滑过渡。
    - **点击"大屏讲解" (班级视角):** 可能将当前题目投屏或进入特殊讲解模式（具体交互待定）。
    - **查看选择某选项的学生列表 (班级视角):** 弹出学生名单浮层或在页面内展开列表。
    - **点击知识点标签:** （可选）弹出菜单，如“查看该知识点其他题目”、“加入知识点复习计划”等。
- **数据展示与更新:** 题目数据为静态加载，展示特定题目的信息。学生作答数据根据视角动态加载。

**优先级：** 高 (P0，因为是答题结果分析的延伸)
**依赖关系：** 依赖于班级（核心功能点3）或学生个人作业报告（核心功能点5）的"答题结果"Tab。

### 用户场景与故事 (针对此核心功能点)

### 场景1 (班级视角)：教师查看共性错题的详细作答情况

作为一个 **学科老师**，我希望能够 **在班级作业报告的“答题结果”中，点击一道共性错题的“查看”按钮，进入该题的详情页面，了解题目的完整内容、答案解析，以及班级整体在这道题上的正答率、平均用时、特别是各个选项都有哪些学生选了**，这样我就可以 **深入分析学生出错的原因，比如是不是某个错误选项迷惑性特别大，从而更有针对性地备课和讲解这道题目**。目前的痛点是 **只知道题目错了多少人还不够，我想知道错在哪里，学生是怎么思考的**，如果能 **看到详细的选项分布和选择对应选项的学生名单**，对我的教学非常有帮助。

**前端界面与交互关键步骤：**

1. **用户操作1:** 教师在班级作业报告的"答题结果"Tab下，找到一道标记为“共性错题”的题目，点击其右侧的"查看"按钮。
    - **界面变化:** 页面跳转到该题的"题目详情页"。页面顶部显示题目信息（题干、选项、题型、难度、知识点），下方显示答案解析（可展开），再下方显示班级作答统计（正答率、答对/错人数、平均用时、各选项选择人数及百分比）。
    - **即时反馈:** 页面加载动画，加载完成后显示题目详情。
2. **用户操作2:** 教师查看"选项分析"部分，发现选项C（错误选项）有较多学生选择。教师点击选项C旁边显示的“查看选择学生(X人)”链接。
    - **界面变化:** 弹出一个浮层（或在页面内展开一个区域），列表显示所有选择了选项C的学生姓名。
    - **即时反馈:** 浮层或列表区域平滑出现。列表支持滚动查看。
3. **用户操作3:** 教师查看完学生名单后，关闭浮层（或收起列表）。然后点击"答案解析"区域的"展开"按钮。
    - **界面变化:** 答案解析区域展开，显示完整的解析内容。
    - **即时反馈:** 解析内容平滑展开。
4. **用户操作4 (可选):** 教师点击"大屏讲解"按钮。
    - **界面变化:** 应用进入一个优化过的投屏讲解模式，可能隐藏部分教师操作界面，放大题目和解析内容。
    - **即时反馈:** 界面切换到讲解模式。

**场景细节补充：**

- **前置条件:** 教师已进入班级作业报告的“答题结果”Tab，并选择了某道题目的“查看”功能。
- **期望结果:** 教师能够全面、深入地了解一道题目在班级内的作答情况，为教学讲解和分析提供充分信息。
- **异常与边界情况:**
    - 若题目无学生作答，作答统计区应友好提示“暂无学生作答此题”。
    - 解析内容过长时，应能良好展示，支持滚动。

**优先级：** 高
**依赖关系：** 依赖核心功能点3。

### 场景2 (学生个人视角)：教师查看学生某道题的具体答案

作为一个 **学科老师**，我希望能够 **在查看某个学生（如“李明”）的个人作业报告时，点击他答错的一道题的“查看”按钮，进入该题的详情页面，不仅能看到题目信息和标准答案解析，还能清晰地看到“李明”这道题选了哪个答案，以及他的得分情况**，这样我就可以 **快速判断他是知识点没掌握，还是粗心选错，从而进行更精准的个性化辅导**。目前的痛点是 **只知道学生题目做错了，但不知道具体错法，辅导时缺乏针对性**，如果能 **直接看到学生的作答内容**，会方便很多。

**前端界面与交互关键步骤：**

1. **用户操作1:** 教师在学生"李明"的个人作业报告的"答题结果"Tab下，找到一道"李明"答错的题目，点击其右侧的"查看"按钮。
    - **界面变化:** 页面跳转到该题的"题目详情页"（学生个人视角）。页面顶部显示题目信息（题干、选项、题型、难度、知识点），下方显示"李明"的作答区（包含他选择的答案、作答结果“错误”、该题得分），再下方是标准答案与解析（可展开）。
    - **即时反馈:** 页面加载动画，加载完成后显示题目详情和学生作答。
2. **用户操作2:** 教师在"学生作答区"看到"李明选择：B"，作答结果为"❌ 错误"。
    - **界面变化:** 清晰展示学生的选项和对错。
    - **即时反馈:** 无特殊反馈。
3. **用户操作3:** 教师点击"答案解析"区域的"展开"按钮。
    - **界面变化:** 答案解析区域展开，显示完整的解析内容，其中正确答案（如A）会被高亮。
    - **即时反馈:** 解析内容平滑展开。

**场景细节补充：**

- **前置条件:** 教师已进入学生个人作业报告的“答题结果”Tab，并选择了某道题目的“查看”功能。
- **期望结果:** 教师能够清晰了解学生在特定题目上的作答情况和正确答案，为个性化辅导提供依据。
- **异常与边界情况:**
    - 若学生未作答该题，学生作答区应提示“该生未作答此题”。

**优先级：** 高
**依赖关系：** 依赖核心功能点5。

**Mermaid图示 (核心功能点本身的流程图/状态图等，区分视角):**

```mermaid
graph TD
    A[题目详情页] --> B[题目基本信息展示区];
    B --> B1[题干与选项 （图文）];
    B --> B2[题型/难度/知识点标签];
    A --> C[答案与解析区 （可展开/收起）];
    C --> C1[正确答案];
    C --> C2[详细解析内容];

    subgraph "班级视角下额外内容 (从班级报告进入)"
        direction LR
        A_Class[ ] -.- A;
        %% 使子图与父图关联，但不实际显示连线
        A_Class --> D_Class[作答统计区];
        D_Class --> D1_Class[整体正答率];
        D_Class --> D2_Class[答对/错学生人数];
        D_Class --> D3_Class[平均作答用时];
        D_Class --> D4_Class[各选项选择人数与百分比];
        D4_Class --> D5_Class[查看选择某选项学生名单 （可交互）];
        A_Class --> E_Class[快捷操作: '大屏讲解'按钮 （可选）];
    end

    subgraph "学生个人视角下额外内容 (从学生个人报告进入)"
        direction LR
        A_Student[ ] -.- A;
        %% 使子图与父图关联
        A_Student --> F_Student[学生作答信息区];
        F_Student --> F1_Student[学生选择的答案];
        F_Student --> F2_Student[作答结果 （正确/错误/部分正确）];
        F_Student --> F3_Student[学生该题得分];
    end

```

### **核心功能点7: 策略驱动的智能提醒与建议**

**简洁描述该功能是什么:** 系统根据预设策略（如鼓励进步、关注异常、共性错题），在作业报告的相关页面（如学生报告Tab、学生个人详情页、题目列表）自动识别并高亮表现特殊（优秀或需改进）的学生或题目，并向教师提供相应的干预建议或快捷操作。

**用户价值与场景:** 满足教师希望系统能辅助其快速发现需要表扬或需要特别辅导的学生，以及普遍性问题的需求。解决人工筛选效率低，容易遗漏；缺乏标准化的判断依据的痛点。为用户提升教学干预的及时性和精准性，减轻教师负担，促进个性化教学的核心价值。

**功能详述与前端呈现:** (此功能融入多个页面，此处概述逻辑和主要呈现方式)

- **主要逻辑与策略:**
    - **鼓励策略:** (原始PRD "策略" -> "鼓励"部分)
        - **触发条件 (示例):** 任务提前完成、比上次任务表现提升X%以上、高于同层同学表现Y%以上、达到个人/班级最多连对题数、达到个人最多提问数、开启提问等。具体条件和阈值由后台配置。
        - **前端呈现:**
            - 在"学生报告Tab"的"建议鼓励学生"区域展示命中鼓励标签的学生列表（如头像、姓名）。
            - 在学生列表中，对符合鼓励条件的学生显示特殊标签（如"有进步"、"连对王者"、"学霸"）和简要说明。
            - 进入学生个人详情页（参考`核心功能点5`），展示具体命中的鼓励标签（如"值得鼓励的进步"）、详细诊断文字、建议干预措施（如"班级课上表扬"、"线上点赞鼓励"），并提供快捷操作（"点赞鼓励"、"线下沟通记录"、"Push提醒"及预设文案）。
    - **关注策略:** (原始PRD "策略" -> "关注"部分)
        - **触发条件 (示例):** 班级进度50%仍未学习、逾期未完成/完成度低、与历史/同层/任务内课程表现偏离Z%以上等。具体条件和阈值由后台配置。
        - **前端呈现:**
            - 在"学生报告Tab"的"建议关注学生"区域展示命中关注标签的学生列表。
            - 在学生列表中，对符合关注条件的学生显示特殊标签（如"需关注"、"进度缓慢"）和简要说明。
            - 进入学生个人详情页（参考`核心功能点5`），展示具体命中的关注原因（如"需要关注的问题"）、详细诊断文字、建议干预措施（如"安排课后一对一辅导"），并提供快捷操作（"发布关注"、"线下沟通记录"、"Push提醒"及预设文案）。
    - **共性错题策略:** (原始PRD "策略" -> "共性错题"部分)
        - **触发条件:** P0默认"答题人数>10人，且任务中正确率＜60%"。P1期支持后台自定义阈值。
        - **前端呈现:**
            - 在任务卡片上（`核心功能点1`）可能显示"待关注题目X道"或"共性错题X道"。
            - 在作业报告详情页顶部概览区（`核心功能点2`）显示"待关注题目X道"。
            - 在"答题结果Tab"（`核心功能点3`）中，统计"XX道共性错题"，并提供"只看共性错题"筛选。符合条件的题目在列表中可能用特殊标记（如特定颜色背景、icon）高亮。
- **界面布局与元素:** 已在上述各功能点中结合描述。核心是智能生成的标签、推荐列表、诊断性文字描述和快捷操作按钮的动态展示。
- **用户操作与反馈:** 教师通过查看这些智能提醒，可以快速决策并执行干预操作。如点击"一键表扬"，系统后台处理并可能通知学生，前端给出操作反馈。
- **数据展示与更新:** 智能提醒和建议根据学生最新数据动态生成或更新。教师进行干预操作后，相关状态（如“已提醒”）应能更新。

**优先级：** 高 (P0)
**依赖关系：** 依赖于学生行为数据收集、作业数据统计以及预设的策略规则库。与核心功能点2、3、5紧密集成。

### 用户场景与故事 (针对此核心功能点)

### 场景1：教师通过智能推荐快速发现并鼓励进步学生

作为一个 **学科老师**，我希望 **系统能在我查看作业报告时，自动帮我找出那些有显著进步或者表现特别突出的学生，并给出表扬建议**，这样我就 **不用花很多时间去逐个对比数据，能更快地给学生正向反馈，激励他们**。目前的痛点是 **班级人数多，很难及时发现每个学生的闪光点，容易错过最佳的鼓励时机**，如果系统能 **智能推荐“值得表扬”的学生并提供一键操作**，那就太好了。

**前端界面与交互关键步骤：**

1. **用户操作1:** 教师进入某次作业的"学生报告"Tab (核心功能点2)。
    - **界面变化:** 在"快捷互动区"，系统基于鼓励策略，自动生成并展示了"值得表扬"学生列表（如周雨彤-“比上次进步20%”，刘明宇-“连对10题创新高”）。列表中包含学生姓名和简要的鼓励理由。
    - **即时反馈:** 列表动态加载。
2. **用户操作2:** 教师点击"值得表扬"学生列表旁的"一键表扬"按钮。
    - **界面变化:** 弹出确认框：“确定向这些学生发送表扬吗？”，按钮变为loading状态。
    - **即时反馈:** 点击确认后，系统执行表扬操作（如发送系统消息或Push），成功后Toast提示“表扬已发送”。
3. **用户操作3:** 教师点击"值得表扬"列表中的学生"周雨彤"。
    - **界面变化:** 页面跳转到"周雨彤"的个人作业报告详情页 (核心功能点5)。
    - **即时反馈:** 页面切换。在该学生的详情页中，顶部有明显的鼓励性标签和诊断文字，如“值得鼓励的进步：本次作业正确率较上次提升20%，继续加油！”，并提供“点赞鼓励”、“分享喜悦给家长”等快捷操作。

**场景细节补充：**
* **前置条件:** 系统已配置鼓励策略。学生作业数据已处理完毕。
* **期望结果:** 教师能够轻松发现并激励表现优秀的学生，提升教学互动效率。
* **异常与边界情况:**
    * 若无学生触发鼓励策略，则"值得表扬"区域提示“暂无特别值得表扬的学生”。
    * “一键表扬”操作失败时，应有友好提示。

**优先级：** 高
**依赖关系：** 依赖核心功能点2、5，以及后台策略配置。

**Mermaid图示 (核心功能点本身的流程图/状态图等 - 策略触发与展示流程):**
  ```mermaid
  flowchart TD
      A[学生作业数据/学习行为数据输入] --> B{数据分析与策略匹配引擎};
      
      subgraph "鼓励策略"
          B -- 匹配鼓励规则 (如进步、高分、连对) --> C[生成鼓励建议];
          C --> C1["在'学生报告Tab'(班级报告)展示<br>'值得表扬'学生列表与理由"];
          C1 --> C1a{点击'一键表扬'?};
          C1a -- 是 --> C1b[执行批量表扬操作];
          C --> C2["在'学生个人报告页'展示<br>具体鼓励标签、诊断文字、建议措施<br>和快捷操作 (如点赞、Push)"];
          C2 --> C2a{点击快捷操作?};
          C2a -- 是 --> C2b[执行单个学生干预操作];
      end

      subgraph "关注策略"
          B -- 匹配关注规则 (如进度慢、错误率高) --> D[生成关注建议];
          D --> D1["在'学生报告Tab'(班级报告)展示<br>'需要关注'学生列表与理由"];
          D1 --> D1a{点击'一键提醒'?};
          D1a -- 是 --> D1b[执行批量提醒操作];
          D --> D2["在'学生个人报告页'展示<br>具体关注原因、诊断文字、建议措施<br>和快捷操作 (如Push提醒、记录沟通)"];
          D2 --> D2a{点击快捷操作?};
          D2a -- 是 --> D2b[执行单个学生干预操作];
      end

      subgraph "共性错题策略"
          B -- 匹配共性错题规则 --> E[识别共性错题列表];
          E --> E1["在'任务卡片'/'作业报告概览'<br>提示共性错题数量"];
          E --> E2["在'答题结果Tab'高亮显示共性错题<br>并提供筛选功能"];
          E2 --> E2a{点击共性错题'查看'?};
          E2a -- 是 --> E2b[跳转到题目详情页分析];
      end
  ```

### 2.2 辅助功能
- 暂无明确定义的P0辅助功能，多数已融入核心功能描述中（如各列表的筛选、搜索，数据的导出等）。

### 2.3 非本期功能
- **提醒设置自定义 (P1):** 允许用户（教师）自定义鼓励、关注、共性错题的提醒规则和阈值参数。
- **大屏模式 (未来功能):** 针对测验、课堂互动等场景的特殊优化展示模式，方便投屏和集中讲解。
- **非选择题的主观题批改与智能辅助评分统计 (未来):** 当前P0期题目详情中，非选择题的作答统计主要针对无标准答案或选择题类型。未来将支持主观题在线批改、AI辅助评分及相关统计。
- **更丰富的干预手段 (未来):** 如直接布置针对性练习、发起小组讨论等。

## 3. 业务流程图 (可选，用于整体业务流)

```mermaid
graph LR
    subgraph "任务发起与学生执行"
        Teacher['学科老师'] -- 布置教学任务 --> TaskType['任务类型\n（课程/作业/测验/资源等）']
        TaskType -- 下发给 --> Student['学生']
        Student -- 接收并查看任务 --> Student_ViewTask['查看任务详情\n（如作业要求、课程内容）']
        Student_ViewTask -- 进行学习与作答 --> Student_Action['用户行为\n（看课、答题、看视频、提问等）']
        Student_Action -- 系统记录行为数据 --> BehaviorData['学生行为数据']
        BehaviorData -- 根据规则判断 --> AutoFinishStatus{'任务自动完成状态'}
    end

    subgraph "数据处理与报告生成"
        BehaviorData -- 聚合/分析 --> ProcessedData['处理后的学情数据']
        AutoFinishStatus -- 汇总 --> ProcessedData
        ProcessedData -- 生成 --> ReportData['各维度作业报告数据\n（班级、学生、题目、提问）']
    end

    subgraph "教师查阅报告与教学干预"
        ReportData -- 教师访问作业列表\n（核心功能点1） --> Teacher_AccessList['教师查看作业列表']
        Teacher_AccessList -- 选择特定作业 --> Teacher_ViewReport['教师查看作业报告详情']

        Teacher_ViewReport --> Tab_StudentReport['学生报告Tab\n（核心功能点2）']
        Tab_StudentReport --> Action_ClassIntervention['班级整体干预\n（一键表扬/提醒）']
        Tab_StudentReport -- 查看学生详情 --> Report_StudentDetail['单个学生作业报告\n（核心功能点5）']
        Report_StudentDetail --> Action_IndividualIntervention['个体学生干预\n（点赞、Push、记录沟通）']

        Teacher_ViewReport --> Tab_QuestionResult['答题结果Tab\n（核心功能点3）']
        Tab_QuestionResult -- 查看题目详情 --> Report_QuestionDetail['题目详情页\n（核心功能点6）']
        Report_QuestionDetail --> Action_QuestionAnalysis['教师分析题目\n（如准备讲解）']

        Teacher_ViewReport --> Tab_StudentQuestion['学生提问Tab\n（核心功能点4）']
        Tab_StudentQuestion --> Action_AnswerQuestion['教师查看/回复学生提问 （P0+）']

        ReportData -- 结合策略引擎\n（核心功能点7） --> SmartAlerts['智能提醒与建议']
        SmartAlerts --> Teacher_ViewReport
    end

    subgraph "多角色数据应用 （P0主要聚焦学科老师）"
        ReportData -- 按班级 （班主任） --> Report_ClassHead['班主任查看班级学情']
        ReportData -- 按年级/学科 （学科主任） --> Report_DeptHead['学科主任查看教学质量']
        ReportData -- 关键信息推送 （校长） --> Report_Principal['校长接收教学概况']
    end
```

## 4. 性能与安全需求

### 4.1 性能需求
- **响应时间：**
    - 作业列表加载：首次加载时间应在3秒内，筛选、搜索操作响应时间应在1.5秒内 (95th percentile)。
    - 作业报告详情页（各Tab）加载：首次加载时间应在3秒内，Tab切换响应时间应在1秒内。
    - 学生列表/题目列表滚动：应保持流畅，无明显卡顿。
    - 页面主要内容加载时间（LCP）应在 2.5 秒内。
    - 首次交互延迟（FID）或交互至下次绘制时间（INP）应小于 100 毫秒。
- **并发用户：** 系统需支持至少500名教师同时在线使用作业报告相关功能，核心功能（列表查看、报告详情查看）性能不显著衰减。
- **数据量：**
    - 教师端作业列表需支持流畅加载和管理至少1000个教学任务。
    - 单个班级学生列表（如50-100人）加载和操作（搜索、排序）应流畅。
    - 单个作业的题目列表（如50-100题）加载和操作应流畅。
- **前端性能感知：**
    - **列表加载**: 对于可能耗时较长的列表加载（如首次加载、复杂筛选），应使用骨架屏或合适的loading动画，明确告知用户加载状态。
    - **大数据量列表**: 学生列表、题目列表等采用虚拟滚动或分页加载技术，优化长列表性能。
    - **图片加载**: 任务卡片或报告中的图片（如学生头像、题目内图片）应进行懒加载和适当压缩，优化加载速度。
    - **实时更新**: 报告中的统计数据（如完成率）更新时，应有平滑过渡或明确提示，避免界面闪烁。

### 4.2 安全需求
- **权限控制：**
    - 严格按照教师的角色（学科老师、班主任、学科主任、年级主任、校长）和数据范围（本班、本年级、本校；本人布置的任务）控制作业列表和报告的查看权限。
    - 编辑、删除、复制任务等操作权限仅限任务布置者本人。
    - 学生个人敏感数据（如具体答案、个人提问）的访问应受严格控制。
- **数据加密：** 所有数据在前后端传输过程中必须使用 HTTPS 加密。前端避免在本地（如localStorage）存储明文敏感信息（如学生具体作答详情）。
- **操作审计：** 对教师的关键操作（如删除任务、一键表扬/提醒所有学生、导出敏感数据）需要记录操作日志，包括操作人、操作时间、IP地址、操作详情，以便追溯。
- **输入校验:** 前端需对用户输入（如搜索关键词）进行基础校验（如长度限制、特殊字符处理），防止常见的注入风险（如XSS），后端必须进行二次严格校验。
- **防刷与防爬：** 对核心数据接口（如获取学生列表、题目列表）应有适当的防刷机制。

## 5. 验收标准

### 5.1 功能验收
-   **核心功能点1：作业列表展示与管理**
    -   **用户场景：** 教师登录后进入作业列表。
    -   **界面与交互：**
        -   能正确显示教师权限范围内的任务列表，卡片信息（标题、班级、类型、状态、关键数据、时间）准确无误。
        -   内容类型Tab切换（全部、课程、作业、资源、测验）功能正常，列表内容正确刷新。
        -   筛选器（学科、年级/班级、日期）功能正常，列表能根据筛选条件正确刷新。
        -   搜索框功能正常，能按任务名称模糊匹配并刷新列表。
        -   本人布置的任务，操作菜单（编辑、删除、复制）按权限和状态正确显示并可用，操作后有二次确认（如删除）和结果反馈，列表状态更新。
    -   **期望结果：** 教师能高效管理和查找其教学任务。
-   **核心功能点2：班级整体作业报告查看 (学生报告Tab)**
    -   **用户场景：** 教师点击作业卡片进入报告详情，查看学生报告Tab。
    -   **界面与交互：**
        -   正确显示作业基础信息、班级整体概览（进度、待关注题目数）、快捷互动区（值得表扬/需要关注学生列表及一键操作按钮）、班级统计数据、学生列表管理区和详细数据列表。
        -   "一键表扬"/"一键提醒"功能可用，操作后有反馈。
        -   学生姓名搜索、数据导出功能可用。
        -   学生列表数据（分层、进度、正确率、错题、用时等）准确，"查看详情"可跳转。
        -   异常数据高亮、列表排序、分页功能正常。
    -   **期望结果：** 教师能全面了解班级整体学情，快速识别重点学生并进行初步干预。
-   **核心功能点3：班级作业报告 - 答题结果Tab**
    -   **用户场景：** 教师在作业报告详情页切换到答题结果Tab。
    -   **界面与交互：**
        -   正确显示整体题目统计（总题数、共性错题数、排序方式）。
        -   题目搜索、"只看共性错题"开关、题型筛选功能可用，题目列表正确刷新。
        -   题目列表卡片信息（题号、题干预览、选项预览、答题统计、操作按钮"加入"/"查看"）展示正确。
        -   题干/解析展开收起功能正常。
        -   点击"查看"能跳转到题目详情页。
    -   **期望结果：** 教师能从题目维度分析学情，定位教学难点。
-   **核心功能点4：班级作业报告 - 学生提问Tab**
    -   **用户场景：** 教师在AI课作业报告详情页切换到学生提问Tab。
    -   **界面与交互：**
        -   正确展示课程内容结构列表及各节点提问数统计。
        -   点击内容节点后，正确展示该节点下的提问列表（提问人、时间、内容）。
        -   （P0+）若支持回复，回复功能正常。
    -   **期望结果：** 教师能集中查看和管理学生在AI课中的提问。
-   **核心功能点5：单个学生作业报告查看**
    -   **用户场景：** 教师从班级报告点击学生"查看详情"进入个人报告。
    -   **界面与交互：**
        -   正确显示学生姓名、作业名称、作业概览信息。
        -   Tab切换（答题结果、学生提问）功能正常，内容仅限该学生。
        -   "答题结果"Tab下：正确统计该生总题数、错题数；题目列表正确显示该生每题作答状态（对错符号、得分率）、"查看"按钮可跳转。
        -   "学生提问"Tab下：正确显示该生个人提问记录。
    -   **期望结果：** 教师能深入了解单个学生的具体学习情况。
-   **核心功能点6：题目详情页查看**
    -   **用户场景：** 教师从班级或学生个人报告的题目列表点击"查看"。
    -   **界面与交互：**
        -   正确展示题目信息（题干、选项、题型、难度、标签）、答案解析（可展开收起）。
        -   班级视角下：正确显示作答统计（正答率、人数、用时、选项分析），可查看选择各选项学生名单。
        -   学生个人视角下：正确显示学生选择的答案、作答结果、得分。
    -   **期望结果：** 教师能深度分析单题的作答情况。
-   **核心功能点7：策略驱动的智能提醒与建议**
    -   **用户场景：** 教师在查看各类报告时，能看到系统基于策略生成的提醒和建议。
    -   **界面与交互：**
        -   鼓励、关注学生列表在学生报告Tab正确生成并展示。
        -   学生列表中的学生标签、学生个人详情页中的诊断文字和建议措施按策略正确展示。
        -   共性错题在任务卡片、报告概览、答题结果Tab中按策略正确提示和高亮。
        -   相关快捷操作功能可用。
    -   **期望结果：** 系统能有效辅助教师发现问题、及时干预。

### 5.2 性能验收
-   **响应时间：**
    -   **测试用例1:** 在普通4G网络条件下，用户打开作业列表页，首次内容加载时间应 ≤ 3秒。使用浏览器开发者工具测量，连续测试5次，95%的响应时间达标。
    -   **测试用例2:** 用户在作业报告详情页（学生报告Tab，50名学生数据）进行学生姓名搜索，列表刷新时间应 ≤ 1.5秒。
-   **并发用户：**
    -   **测试用例1:** 使用压测工具模拟500名教师用户同时访问作业列表和作业报告详情页（核心功能点1、2、3），持续运行30分钟，系统无5xx错误，核心功能平均响应时间不超过3秒，CPU/内存使用率在75%以内。
-   **前端性能感知验收：**
    -   **测试用例1 (大量数据加载):** 对于包含200条任务的作业列表，首次加载使用骨架屏，加载时间不超过4秒，后续滚动加载新数据无明显卡顿，浏览器内存占用增长平稳。
    -   **测试用例2 (Tab切换):** 在作业报告详情页，切换“学生报告”、“答题结果”、“学生提问”Tab时，内容切换动画流畅，新内容加载时间（若需请求数据）应 ≤ 1秒。

### 5.3 安全验收
-   **权限控制：**
    -   **测试用例1:** 使用学科老师A账号登录，应只能看到其任教班级的本学科作业。尝试访问其他班级或其他学科作业报告，应被拒绝或提示无权限。
    -   **测试用例2:** 使用班主任B账号登录，应能看到其负责班级的全部学科作业，但不能编辑或删除非本人布置的作业。
    -   **测试用例3:** 尝试通过直接修改URL参数等方式越权访问其他教师或班级的数据，应失败。
-   **数据加密：**
    -   **测试用例1:** 通过浏览器开发者工具查看所有与后端API的通信，确认均为HTTPS加密。
    -   **测试用例2:** 检查浏览器本地存储（localStorage, sessionStorage, cookies），确认没有明文存储用户凭证、学生个人答案等敏感信息。
-   **操作审计：**
    -   **测试用例1:** 教师A删除其布置的一项作业，后端操作日志中应正确记录该操作，包含操作人ID、操作时间、操作内容（删除作业ID）、IP地址。
-   **输入校验：**
    -   **测试用例1 (XSS):** 在作业列表的搜索框、学生报告的姓名搜索框中输入 ``<script>alert('xss')</script>``，提交后脚本未执行，特殊字符被转义或过滤，页面显示正常。
    -   **测试用例2 (SQL注入):** 在搜索框中尝试输入SQL注入payload，后端应能正确处理，不执行恶意SQL。

## 6. 其他需求

### 6.1 可用性与体验需求
-   **界面友好与直观性：**
    -   核心操作路径（如查看作业列表 -> 查看作业报告 -> 查看学生详情/题目详情）应简洁明了，符合教师用户使用习惯，主要路径操作步骤不宜过多。
    -   图标（如操作菜单、筛选、搜索、展开/收起、对错标记）、标签（如P0、进行中、已到期、有进步、需关注）、提示信息（如Toast、空状态提示、错误提示）清晰易懂，避免歧义。
    -   整体视觉风格（颜色、字体、间距）在教师端产品内保持一致性，重要信息（如错误率、截止时间、需关注项）和操作（如一键提醒、查看详情）有明确的视觉引导。
    -   移动端界面应充分考虑触摸操作的便捷性，按钮、链接、Tab等可点击区域大小适宜（建议不小于44x44 CSS像素）。
-   **容错处理：**
    -   用户进行敏感操作（如删除任务、一键提醒所有关注学生）前，应有二次确认弹窗，明确告知操作后果，并提供取消选项。
    -   网络请求失败或超时，应在界面对应区域给出友好提示（如“网络连接失败，请检查网络后重试”），并允许用户点击重试。
    -   无数据或空状态时（如作业列表为空、筛选结果为空、某Tab下无内容），界面应有明确、友好的图形化或文字提示，避免空白，并可酌情提供引导操作（如“您还没有布置作业，快去创建吧！”）。
    -   用户输入错误（如搜索框输入非法字符），应有即时校验提示或忽略非法字符。
-   **兼容性：**
    -   **浏览器：** 需支持最新版本的 Chrome, Firefox, Safari, Edge 浏览器，以及主流国产浏览器（如360极速模式、QQ浏览器Chromium内核）。保证核心功能和界面显示正常，无明显错位或样式问题。
    -   **响应式布局：** 产品主要面向移动端（App或H5）。若有PC端后台管理或查看需求，则：
        -   **移动端 (<768px):** 核心信息优先，单栏流式布局，导航简化（如汉堡菜单或底部Tab），操作按钮醒目易点。
        -   **(若适用) 平板端 (≥768px and <1200px):** 可考虑双栏布局或优化信息密度。
        -   **(若适用) 桌面端 (≥1200px):** 信息可更充分展示，多栏布局，利用更宽屏幕空间。
        -   核心信息和功能在不同尺寸下均易于访问和使用。
-   **可访问性 (A11y)：**
    -   核心功能应考虑键盘可操作性，所有交互元素（按钮、链接、表单控件、Tab）可通过Tab键聚焦并用Enter/Space键激活。
    -   图片等非文本内容需提供有意义的 `alt` 文本（如图标按钮的aria-label）。
    -   颜色对比度应符合 WCAG AA 级别标准，确保低视力用户可辨识重要信息。
    -   重要状态变更（如错误提示、加载完成、新消息）应有ARIA属性支持（如aria-live），方便屏幕阅读器用户感知。
    -   表单控件应有关联的`<label>`。
-   **交互一致性：**
    -   产品内相似功能或操作（如各处的筛选器、搜索框、列表分页、Tab切换、展开/收起、操作菜单）应使用统一的视觉样式、文案和交互模式。
    -   按钮样式（主按钮、次按钮、危险按钮）、弹窗风格（确认框、提示框）、Toast提示等应统一规范。

### 6.2 维护性需求
-   **配置管理：**
    -   共性错题的判断标准（如答题人数阈值、正确率阈值）应支持后台配置（P1需求明确，P0期可硬编码但预留接口）。
    -   鼓励与关注策略的触发条件和阈值参数（如进步百分比、偏离百分比）应支持后台配置。
    -   部分提示文案（如Push提醒模板）可考虑后台配置。
    -   分页大小（每页显示条数）可后台配置。
-   **监控告警：**
    -   关键接口（如获取作业列表、获取报告详情、提交操作）的调用成功率、平均耗时、错误率（4xx, 5xx）应纳入监控，并设置合理阈值，异常时自动告警通知相关开发和运维人员。
    -   前端JS错误应上报至监控系统（如Sentry），并按错误类型和发生频率进行监控告警。
    -   核心业务流程（如教师查看报告并进行干预）的转化漏斗和异常中断情况应有监控。
-   **日志管理：**
    -   前端应记录关键用户行为路径（如页面访问、Tab切换、重要按钮点击如"一键表扬"、"导出"），用于用户行为分析和问题排查。
    -   前端记录重要API请求的参数（脱敏后）、响应状态和耗时，辅助排查问题。
    -   JS错误日志应包含错误信息、堆栈、发生页面、用户环境（浏览器、OS）等详细信息。
    -   所有日志应有统一格式，便于采集和分析。日志保存周期至少30天。

## 7. 用户反馈和迭代计划

### 7.1 用户反馈机制
-   用户（教师）可以通过产品内的反馈入口（如“我的”-“意见反馈”）或通过运营支持渠道（如微信群、客服电话）提交使用过程中遇到的问题、操作体验的意见和功能改进建议。
-   前端应确保反馈入口易于发现和使用，提交过程便捷。反馈内容可支持文字、图片。
-   每周/每双周定期由产品和运营团队收集、整理和分析用户反馈，识别高频问题、痛点需求和改进机会，作为产品迭代规划的重要输入。

### 7.2 迭代计划
-   **迭代周期：** 初定每2-3周为一个迭代周期。
-   **P0阶段 (预计6月上线MVP):**
    - 目标：完成本文档描述的所有P0核心功能，跑通教师查看作业报告、分析学情、进行初步干预的MVP流程。
    - 重点：作业列表、班级整体报告（学生报告Tab、答题结果Tab、学生提问Tab）、单个学生报告、题目详情、基础的智能提醒策略。
-   **P0+阶段 (预计9月完成全部核心功能):**
    - 目标：完善P0功能体验，增强智能策略的丰富度和准确性，可能包括部分P1需求的预研。
    - 重点：优化现有P0功能交互细节、丰富鼓励与关注策略、细化共性错题分析。
-   **后续迭代 (P1及以后):**
    - 根据P0上线后的用户反馈、数据表现和业务发展需求，持续优化用户体验。
    - 规划并实现P1需求（如提醒设置自定义、更丰富的题目类型支持等）及未来功能（如大屏模式、主观题批改等）。
-   **优先级调整：** 每次迭代结束后，根据上个迭代的需求实现情况、数据表现和用户反馈，产品团队会重新评估Backlog中的需求优先级，并规划下一个迭代的内容。

## 8. 需求检查清单

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| -------------------------------- | ----------------------------------- | ------ | ------ | ------ | ------------------------- | --------------------------- | ----------- | ---- |
| **作业列表 (P0)** |  |  |  |  |  |  |  |  |
| 1. 数据范围：展示当前老师布置的、有权限看的全部作业 (取并集，区分职务) | 2.1 核心功能点1 (功能详述、权限部分见4.2) | [✅]    | [✅]    | [✅]    | [✅] (5.1, 5.3)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_GSs0bDnxPomvPlxUP2ac1IKinJe`等已融入描述 |
| 2. 筛选：全部类型、查看班级、学科、布置日期 | 2.1 核心功能点1 (功能详述-界面布局与元素-筛选器) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_NCQkbWefmoYZ6JxwYtzcQGepnMh`, `in_table_image_ZqNFb5BlvoUGZZx5WOjcCRJmnZL`等已融入描述 |
| 3. 搜索：按任务名称模糊匹配 | 2.1 核心功能点1 (功能详述-界面布局与元素-搜索框) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_Sj3wb50N3otvfKx11KxcWJCynnW`已融入描述 |
| 4. 卡片指标：区分课程、作业/测验、资源任务 | 2.1 核心功能点1 (功能详述-界面布局与元素-任务卡片-关键数据指标) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         |  |
| 5. 卡片操作：编辑、删除、复制 (仅本人布置) | 2.1 核心功能点1 (功能详述-界面布局与元素-任务卡片-操作菜单触发器) | [✅]    | [✅]    | [✅]    | [✅] (5.1, 5.3)      | [✅] (P0)                 | [✅]         |  |
| **作业报告 (P0) - 学生报告Tab** |  |  |  |  |  |  |  |  |
| 1. 页面名称返回、任务详情筛选、Tab切换 | 2.1 核心功能点2 (功能详述-界面布局与元素) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_Cd4kbLOXYo3T08xNlWccCmmenye`, `image_PswKbOca7ouLbDxJzFScXHognOc`等已融入描述 |
| 2. 建议鼓励/关注学生列表及操作 | 2.1 核心功能点2 (快捷互动区), 2.1 核心功能点7 (鼓励/关注策略) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_IH8ebP7lQoDQdlxZzN3cvmJonwh`, `in_table_image_MfM7bitX2ozpTCxMCtHcHPBonjE`等已融入描述 |
| 3. 班级进度/正确率/平均用时 | 2.1 核心功能点2 (班级整体概览区, 班级统计数据区) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         |  |
| 4. 学生列表及异常飘色、排序、操作 | 2.1 核心功能点2 (学生详细数据列表区, 用户操作与反馈) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_CFTebD2UVoAQkSx5g9bcc479n7e`已融入描述 |
| **作业报告 (P0) - 答题结果Tab** |  |  |  |  |  |  |  |  |
| 1. 数据展示逻辑(区分任务类型)、题目统计(共性错题解释) | 2.1 核心功能点3 (功能详述, 数据展示与更新) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`image_Kde9bV1SroJspDxtJQeccHOCneg`, `image_QbOCbI3ssoaDfKxamzIcZbB8nWd`等已融入描述 |
| 2. 搜索题目、题目信息展示(正确率等定义) | 2.1 核心功能点3 (题目筛选与搜索区, 题目列表区) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_PsxWbEgV3opGFoxTzVqcplajnGg`已融入描述 |
| 3. 侧边题目面板 (可选) | 2.1 核心功能点3 (界面布局与元素 - 侧边题目面板) | [✅]    | [✅]    | [✅]    | [⚠️] (P0期可选) | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_DCBObWZ44oqd9wx3ifxcPpHRnzg`已融入描述 |
| **作业报告 (P0) - 学生提问Tab** |  |  |  |  |  |  |  |  |
| 1. 展示有评论的素材(AI课)、按课程幻灯片顺序展示内容及评论数 | 2.1 核心功能点4 (功能详述-界面布局与元素) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`image_CMoSbVPqcoznYTxrEPec4qDQnce`, `image_An8wbFUSLo9yJjxSrPicif6ynbg`等已融入描述 |
| **xx学生的作业报告 (P0)** |  |  |  |  |  |  |  |  |
| 1. 来源入口、页面名称返回、答题概览(时间、进度、素材范围) | 2.1 核心功能点5 (功能详述-界面布局与元素) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`image_FjLobW20loc0yVx64afcpujenMe`, `image_OvxrbrH5zop670xgEdickNnBn6b`等已融入描述 |
| 2. 答题结果Tab(学生个人)：题目统计、题目信息展示(个人对错)、题目面板 | 2.1 核心功能点5 (功能详述-"答题结果"Tab下) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`image_I4hibE2j6o3Kr4xlH1DcE0A3nag`, `in_table_image_XT3tbg5KZoLgUixkle4csF4nn8u`等已融入描述 |
| 3. 学生提问Tab(学生个人)：展示个人评论的素材、按课程幻灯片顺序展示内容及个人评论数 | 2.1 核心功能点5 (功能详述-"学生提问"Tab下) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`in_table_image_Mn2obNTTAoNIktxmxcFcV5r7nvf`, `in_table_image_SqYxbKRGRoaiIGxQEhlcAyK9nZd`等已融入描述 |
| **题目详情页 (P0)** |  |  |  |  |  |  |  |  |
| 1. 班级视角：题目基本信息(题干选项、标签、答案解析)、作答统计(选择题/判断题) | 2.1 核心功能点6 (功能详述-班级视角下) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始图：`image_CLqrb4W3XoPbH4x4xlUcGgfMnuf`, `in_table_image_YOe7bcJuXofS8YxeykIcT3LEn0g`等已融入描述 |
| 2. 学生个人视角：题目基本信息、作答统计(仅本人数据) | 2.1 核心功能点6 (功能详述-学生个人视角下) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         |  |
| **策略说明 (P0)** |  |  |  |  |  |  |  |  |
| 1. 鼓励策略 (系统建议) | 2.1 核心功能点7 (鼓励策略) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始PRD"策略"章节已融入 |
| 2. 关注策略 (系统建议) | 2.1 核心功能点7 (关注策略) | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始PRD"策略"章节已融入 |
| 3. 共性错题策略 | 2.1 核心功能点7 (共性错题策略), 2.1 核心功能点3 | [✅]    | [✅]    | [✅]    | [✅] (5.1)      | [✅] (P0)                 | [✅]         | 原始PRD"策略"章节已融入 |

-   **完整性：** 原始需求是否都有对应的优化后需求点，无遗漏。
-   **正确性：** 优化后需求描述是否准确表达了原始需求的意图。
-   **一致性：** 优化后需求之间是否有冲突或重复。
-   **可验证性：** 优化后需求是否有明确的验收标准（对应5. 验收标准）。
-   **可跟踪性：** 优化后需求是否有明确的优先级和依赖关系。
-   **UI/UX明确性：** 优化后需求是否清晰描述了前端界面、交互和用户体验细节。

## 9. 附录

### 9.1 原型图/设计稿链接
-   UI图: [https://www.figma.com/design/d7RUo4uZ7uwRpb1Enf0Sng/%E9%93%B6%E6%B2%B3-%E6%95%99%E5%B8%88?node-id=11-4149&p=f&t=MkljbBY4IkwHZuo7-0](https://www.figma.com/design/d7RUo4uZ7uwRpb1Enf0Sng/%E9%93%B6%E6%B2%B3-%E6%95%99%E5%B8%88?node-id=11-4149&p=f&t=MkljbBY4IkwHZuo7-0)
-   (原始PRD中的图片已通过文字描述融入各功能点，此处不再罗列图片ID)

### 9.2 术语表
-   **共性错题:** 指在一定范围内（如班级、特定人数以上作答）答错比例较高的题目。P0阶段定义为"答题人数>10人，且任务中正确率＜60%"。
-   **学习分:** (原始PRD提及，学生端策略可能变化) 用于衡量学生学习效果或投入的综合性评分。本PRD中主要关注更直接的指标如正确率、完成度。
-   **飘色:** 在列表中，对偏离均值较大的异常数据进行特殊颜色标记（如红色警告、黄色关注），以引起教师注意。
-   **Push提醒:** 通过系统向学生端App或教师端App发送的通知消息。
-   **AI课:** 一种包含人机交互、智能反馈的在线课程形式，支持学生在学习过程中提问，并记录相关行为数据。
-   **P0/P1:** 项目阶段划分，P0为MVP核心功能，P1为后续迭代功能。

### 9.3 参考资料
-   原始需求文档：`documents/raw-docs/教师端_1期_作业报告_analyzed.md`
-   本文档遵循的工作指南：`documents/prompt/fe-gen-ai-prd.md`
-   本文档使用的模板：`documents/templates/fe-ai-prd-v1.1.md` (本文档已基于v2.2模板结构进行了适配，并命名为v1.1以对应当前产出轮次)
-   口径说明文档 (外部链接，来自原始PRD): `https://wcng60ba718p.feishu.cn/wiki/DXKawJOESiypQ3kt3vbc64DgnTc?sheet=e3d974`
-   教师端数据埋点汇总 (外部链接，来自原始PRD): `https://wcng60ba718p.feishu.cn/wiki/Pw1GwQgfEi7TsPk3dgrcysMDnR0?sheet=ceLy7M`

---
等待您的命令，指挥官

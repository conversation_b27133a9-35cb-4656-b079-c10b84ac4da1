Okay, I will transform the provided backend Go development workflow into a frontend development workflow, leveraging the frontend-specific documentation you've supplied. The core structure of the phases will be maintained, but the content, tools, and outputs will be adapted for a frontend environment centered around React, Next.js, TypeScript, and the MVVM pattern within your monorepo.

Here is the transformed frontend development workflow:

# AI 助推前端工程化开发工作流 (v1.0)

## 1\. 引言

### 1.1. 目的

本文档旨在定义一套结构化的工作流程，指导开发者如何有效利用 AI 助手（如 Cursor 内的 AI）与项目已定义的规范文档相结合，高效、高质量地完成基于 **Next.js/MVVM** 框架的前端项目工程化开发，特别关注 **schoolroom-monorepo** 的结构和规范。

### 1.2. 核心思想

本流程强调 **人机协作 (Human-AI Collaboration)**。AI 作为强大的辅助工具，负责执行明确指令下的代码生成（组件、Hooks、类型定义）、检查、文档编写等任务；
开发者则扮演**设计者（UI/UX、组件架构）、需求阐述者、评审者、测试执行者和最终决策者**的角色。
流程成功的关键在于**分阶段、上下文感知**地与 AI 交互，在正确的时间提供正确的输入（如设计稿、API 契约、组件需求）和指令。

### 1.3. 参考规范

本流程的执行依赖于项目内已定义的各类规范文档，开发者和 AI 在各阶段需参考：

  * **Monorepo 与全局规范:**
      * `ai-rules/frontend/project-rules/schoolRoom/monorepo&global.mdc`: Monorepo 项目结构 (`apps/*`, `packages/*`)、核心工具 (Turborepo, pnpm, Next.js, React, TypeScript, Tailwind CSS, Signals, SWR, Remotion)、代码质量 (ESLint, Prettier, Husky)、依赖管理、构建与开发命令。
  * **应用架构规范:**
      * `ai-rules/frontend/public-rules/application-architecture.mdc`: 应用、页面、业务视图 (Business View) 定义，**MVVM (Model-View-ViewModel) 模式详解**、分层目录结构、组件命名、数据 Mocking 策略 (Apifox, Next.js Route Handlers)、层级文档化 (`README.md`) 与复用策略。
  * **共享包文档:**
      * `ai-rules/frontend/project-rules/schoolRoom/docs/ui.readme.md`: `@repo/ui` 共享 UI 组件库 (基于 shadcn/ui) 的组件列表、Props、使用场景。
      * `ai-rules/frontend/project-rules/schoolRoom/docs/core.readme.md`: `@repo/core` 共享核心业务库的模块、业务组件、Hooks、服务、类型定义。
      * `ai-rules/frontend/project-rules/schoolRoom/docs/lib.readme.md`: `@repo/lib` 共享通用工具库的 Hooks、工具函数、类型定义。
  * **代码规范 (通过共享配置间接引用):**
      * `packages/config-eslint`: 共享 ESLint 配置。
      * `packages/config-typescript`: 共享 TypeScript 配置 (`@repo/config-typescript/base.json`)。
  * **项目进度与任务管理:**
      * `project-progress.md`: 作为模块或功能开发过程中的 Checklist 和状态跟踪。
  * **API 契约 (来自后端或 Apifox):**
      * 外部 API 文档 (例如由后端生成并维护在 Apifox 上的 OpenAPI 规范)。

## 2\. 开发工作流阶段

本工作流将开发过程划分为多个阶段，旨在通过人机协作逐步构建和验证前端应用。核心的层级构建顺序通常遵循（但不完全严格线性，共享包的开发可能并行）：**Model层定义 -\> ViewModel层实现 -\> View层实现（页面、业务视图、组件）-\> 路由配置**。下面的阶段划分将指导这一过程的执行。

### Phase 0: 准备与设计规划

  * **阶段核心:** 人类主导，AI 辅助。
  * **AI 角色:** 设计助理 / 规范检查员 / 信息检索员。
  * **目标:** 明确需求，设计核心用户流程、组件架构、页面结构和确认数据交互契约。
  * **流程:**
    1.  **需求理解与 UI/UX 设计和技术方案 (开发者):**
          * 深入理解 PRD 和 UI/UX 设计稿。
          * 编写初步技术设计文档 (核心页面/视图草稿、组件拆分方案、关键组件API设计、状态管理策略、API 依赖列表、路由规划)。
          * 参考 `application-architecture.mdc` 进行架构选型。
    2.  **AI 辅助完善设计 (可选):**
          * **输入:** 初步技术设计文档, UI 设计稿描述, 相关规范 (如 `monorepo&global.mdc`, `application-architecture.mdc`)。
          * **指令 (示例):** 请求 AI 检查组件拆分的合理性、建议状态管理方案、根据 UI 描述生成初步的组件 Prop 定义草稿、检索 `@repo/ui` 或 `@repo/core` 中是否有可复用的组件。
    3.  **最终设计确认 (开发者):**
          * 确认页面 (`app/.../page.tsx`) 和业务视图 (`app/views/...`) 的结构和职责。
          * 确认核心 Model 层接口 (`app/models/...` 或 `packages/core/models/...`)，特别是与 API 交互的数据结构和转换逻辑。
          * 确认核心 ViewModel 层接口 (`app/viewmodels/...` 或 `packages/core/viewmodels/...`)，明确其职责和暴露给 View 的状态与方法。
          * 确认关键组件的 API (Props) 和内部状态设计，优先考虑复用 `@repo/ui` 和 `@repo/core` 中的组件。
          * **[执行]** 根据需要，在 `apps/*` 或 `packages/*` 中创建相应的目录结构。

### Phase 1: Model 层构建与验证 (Model Layer Construction & Validation)

**目标:** 定义与 API 交互的数据结构 (TypeScript 类型/接口)，实现数据获取、缓存、转换逻辑 (Model Hooks/函数)，并完成单元测试。

**核心输入:**

  * API 契约文档 (如 Apifox OpenAPI 规范)。
  * Phase 0 确认的 Model 层设计和数据结构。
  * `ai-rules/frontend/public-rules/application-architecture.mdc` (Model 层职责、SWR 使用建议)。
  * `ai-rules/frontend/project-rules/schoolRoom/docs/lib.readme.md` (如 `Workspaceer` 工具的使用)。
  * `packages/config-typescript/base.json` (TypeScript 规范)。
  * `packages/config-eslint` (ESLint 规范)。

**输出:**

  * `app/{feature}/models/{entity}-model.ts` 或 `packages/core/models/{entity}-model.ts`: Model 定义 (包含类型、数据获取函数/SWR Hooks、转换逻辑)。
  * `app/{feature}/models/{entity}-model.test.ts` 或 `packages/core/models/{entity}-model.test.ts`: Model 的单元测试。
  * (可能) `app/api/.../route.ts` 中的 Mock API (如果采用 Next.js Route Handlers Mock)。
  * 更新对应 Model 目录下的 `README.md`。

**步骤:**

1.  **API 契约理解与数据结构定义 (API Contract & Type Definition):**

      * **输入:** API 契约文档, Phase 0 设计的实体关系和属性。
      * **人工:** 开发者理解 API 接口的请求参数、响应结构和业务含义。
      * **AI 辅助:**
          * **指令 (示例):** "根据这个 OpenAPI schema (pasted schema or link) 为 'User' 实体生成 TypeScript interface，包含 id (number), name (string), email (string), isActive (boolean)。同时，为 ViewModel 可能期望的 `TransformedUser` 生成 interface，其中 `displayName` 是 `name` 的大写，并添加 `initials` (string)。"
          * AI 根据 API 规范生成 TypeScript 类型/接口定义。
          * 产出:\*\* 初步的 `interface Entity` 和 `interface TransformedEntity` (如果需要转换)。
      * **人工评审:** 开发者评审 AI 生成的类型是否准确、完整，并根据 `application-architecture.mdc` 中 Model 层的规范进行调整。
      * 验证:\*\* 确认类型定义符合 API 响应和前端业务需求。

2.  **数据获取与转换逻辑实现 (Data Fetching & Transformation Logic):**

      * **输入:** 已定义的 TypeScript 类型, SWR 使用规范 (`monorepo&global.mdc`, `application-architecture.mdc`), `Workspaceer` 工具 (`@repo/lib`)。
      * **人工:** 开发者设计数据获取策略 (e.g., 使用 SWR Hook)，以及原始数据到 `TransformedEntity` 的转换逻辑。
      * **AI 辅助:**
          * **指令 (示例):** "为 `useUsers` Model Hook (在 `user-model.ts` 中) 生成使用 SWR 的代码。Fetcher 应调用 `/api/users` GET 请求 (使用 `@repo/lib/utils/fetcher` 的 `get` 方法)。数据获取后，将 `User[]` 转换为 `TransformedUser[]` (转换逻辑：displayName 为 name 大写，initials 为 name 前两个字母大写)。导出 `users` (转换后), `rawUsers` (原始), `isLoading`, `error`, `mutateUsers`。"
          * AI 根据指令生成包含 SWR 的 Model Hook 骨架和转换函数。
          * 产出:\*\* `app/{feature}/models/{entity}-model.ts` 中的核心逻辑。
      * **人工评审与完善:** 开发者评审 SWR 配置、fetcher 调用、错误处理、数据转换逻辑的准确性和健壮性。
      * 验证:\*\* 确保 Model 层能正确获取、缓存和转换数据。

3.  **数据 Mocking (如需前端 Mock):**

      * **输入:** API 契约，`application-architecture.mdc` 中的 Mocking 策略。
      * **人工/AI:** 如果选择 Next.js Route Handlers Mock，开发者或 AI 辅助在 `app/api/{entity}/route.ts` 中创建 Mock 接口。
          * **指令 (示例):** "为 `/api/users` 创建一个 GET Mock Route Handler，返回包含两个 mock User 对象的数组，模拟 500ms 延迟。"
          * 产出:\*\* Mock API 文件。
      * 验证:\*\* 确保 Mock API 返回的数据结构与定义的类型一致。

4.  **Model 层单元测试生成 (Model Unit Test Generation):**

      * **输入:** Model 实现代码 (`{entity}-model.ts`), TypeScript 类型定义。
      * **步骤:**
        1.  **(人工/AI)** 创建测试文件 `{entity}-model.test.ts`。
        2.  **(AI 生成)**
              * **指令 (示例):** "为 `user-model.ts` 中的 `useUsers` Hook 编写单元测试。需要 Mock `Workspaceer.get`。测试场景：1. 成功获取并正确转换用户数据。2. `Workspaceer.get` 返回错误。3. `Workspaceer.get` 返回空数据。断言 `users`, `isLoading`, `error` 的状态。"
              * AI 生成测试用例骨架，包括 Mock `Workspaceer`。
        3.  **(开发者评审、完善与执行)**
              * 评审测试场景覆盖，完善 Mock 实现和断言。
              * 执行 `pnpm test --filter={packageName}`。
              * 产出:\*\* 通过的单元测试。
      * 验证:\*\* Model 层逻辑在不同场景下表现符合预期，测试覆盖率达标。

5.  **文档更新 (`README.md`):**

      * **人工/AI:** 根据 `application-architecture.mdc` 的 5. 层级文档化与复用策略，更新对应 Model 目录下的 `README.md`，添加新 Model 的描述、使用方法等。

**完成标志:**

  * Model 层的数据结构、获取逻辑、转换逻辑已实现并通过评审。
  * Model 层单元测试已完成、通过，且覆盖率达标。
  * Mock API (如果需要) 已创建并可用。
  * 相关的 `README.md` 已更新。

### Phase 2: 核心逻辑/共享包构建与验证 (Core Logic / Shared Packages Construction & Validation)

**阶段说明:** 此阶段专注于构建和验证不直接属于特定业务视图的 MVVM 模式中的核心业务逻辑、可复用的工具函数、共享 UI 组件以及配置。这对应于 `packages/core`, `packages/lib`, `packages/ui` 等的开发。

**目标:** 设计、实现和测试共享包中的核心业务逻辑 (非视图模型部分)、工具函数、基础 UI 组件和配置。

**核心输入:**

  * Phase 0 阶段确定的共享功能需求。
  * `ai-rules/frontend/project-rules/schoolRoom/monorepo&global.mdc` (各 package 职责)。
  * `ai-rules/frontend/project-rules/schoolRoom/docs/core.readme.md` (如需在 `@repo/core` 中添加非 VM 的业务逻辑或服务)。
  * `ai-rules/frontend/project-rules/schoolRoom/docs/lib.readme.md` (如需在 `@repo/lib` 添加新 Hooks 或 utils)。
  * `ai-rules/frontend/project-rules/schoolRoom/docs/ui.readme.md` (如需在 `@repo/ui` 添加新基础组件)。
  * `packages/config-typescript/base.json`, `packages/config-eslint`。

**输出:**

  * `packages/{core|lib|ui}/src/...`: 实现的共享模块/组件/函数。
  * `packages/{core|lib|ui}/src/.../*.test.ts`: 对应的单元测试。
  * 更新对应包内的 `README.md` 和组件文档。

**步骤:**

1.  **共享模块/组件设计与评审 (Shared Module/Component Design & Review):**

      * **目标:** 确保设计的共享模块/组件具有良好的 API、高度可复用性，并符合规范。
      * **核心输入:** Phase 0 的设计输出, 对应 `packages/*` 的 `README.md` 和现有代码。
      * **步骤 (以 `@repo/ui` 组件为例):**
        1.  **(开发者/AI)** 参照 `ui.readme.md` 和 shadcn/ui 风格，设计新组件的 Props API、视觉表现和交互行为。
        2.  **(开发者/AI)** 评审设计的 API 是否清晰、通用，是否考虑了可访问性 (WCAG)。
        3.  **(开发者)** 最终确定组件的 API 和实现方案。
      * **关键检查点:** ✅ API 清晰通用 ✅ 符合包的定位和规范 ✅ 可测试性 ✅ 可访问性。
      * **输出物:** 定型的模块/组件设计方案。

2.  **共享模块/组件实现 (Shared Module/Component Implementation):**

      * **AI 辅助 (示例 - 为 `@repo/lib` 添加新 Hook):**
          * **指令:** "在 `@repo/lib/hooks` 中创建一个名为 `useDebounce` 的 React Hook。它接收 `value` 和 `delay` (毫秒) 作为参数，返回一个去抖后的 `debouncedValue`。请包含 TypeScript 类型定义和 JSDoc 注释。"
          * AI 生成 Hook 的初步代码。
      * **AI 辅助 (示例 - 为 `@repo/ui` 添加新组件):**
          * **指令:** "基于 shadcn/ui 的 `Button` 和 `Popover`，在 `@repo/ui/components` 中创建一个名为 `InfoButtonWithPopover` 的组件。它接收 `buttonText` (string), `popoverContent` (ReactNode) 和 `variant` (ButtonProps['variant']) 作为 Props。点击按钮时显示 Popover。确保导出并遵循命名规范。"
          * AI 生成组件的初步代码。
      * **(开发者评审与完善)** 开发者细化实现，处理边界情况，确保代码质量和性能。

3.  **共享模块/组件单元测试 (Shared Module/Component Unit Testing):**

      * **AI 辅助 (示例 - 为 `useDebounce` Hook):**
          * **指令:** "为 `useDebounce` Hook 编写单元测试。测试场景：1. 初始值正确返回。2. value 快速变化时，debouncedValue 只在 delay 后更新一次。3. delay 改变时行为正确。"
          * AI 生成测试用例骨架。
      * **(开发者评审、完善与执行)**
          * 执行 `pnpm test --filter={packageName}`。
      * **关键检查点:** ✅ 测试场景覆盖核心逻辑 ✅ 断言准确 ✅ 测试通过 ✅ 覆盖率达标。
      * **输出物:** 单元测试文件, 测试报告。

4.  **文档更新 (README.md 和组件文档):**

      * **人工/AI:** 根据 `ui.readme.md` 的格式要求，为 `@repo/ui` 中的新组件添加文档。更新 `@repo/lib` 或 `@repo/core` 的 `README.md`，列出新增的 Hook/工具函数/模块。
          * **指令:** "为 `InfoButtonWithPopover` 组件生成符合 `ui.readme.md` 格式的文档条目，包括基本说明、场景说明、输入信息 (Props)、输出信息 (渲染)。"

**完成标志:**

  * 共享模块/组件已实现并通过评审。
  * 单元测试已完成、通过，且覆盖率达标。
  * 相关文档 (`README.md`, 组件库文档) 已更新。

### Phase 3: ViewModel 层实现与验证 (ViewModel Layer Implementation & Validation)

**阶段说明:** 在 Model 层 (Phase 1) 和必要的共享核心逻辑 (Phase 2) 准备就绪后，本阶段聚焦于 ViewModel 层的构建。ViewModel 作为 Model 和 View 之间的桥梁，负责处理视图逻辑、管理视图状态，并从 Model 获取和转换数据以适应 View 的需求。

**目标:** 实现 ViewModel Hooks (`use[Feature]ViewModel`)，封装视图特有的业务逻辑、状态管理 (Signals, `useState`) 和与 Model 层的交互。

**核心输入:**

  * `app/{feature}/models/{entity}-model.ts` 或 `packages/core/models/{entity}-model.ts`: 已实现的 Model。
  * Phase 0 确认的 ViewModel 设计 (职责、暴露的状态和方法)。
  * `ai-rules/frontend/public-rules/application-architecture.mdc` (ViewModel 层职责、MVVM 模式详解、状态管理建议)。
  * `packages/config-typescript/base.json`, `packages/config-eslint`。

**输出:**

  * `app/{feature}/viewmodels/use-{feature}-viewmodel.ts` 或 `packages/core/viewmodels/use-{feature}-viewmodel.ts`: ViewModel Hook 实现。
  * `app/{feature}/viewmodels/use-{feature}-viewmodel.test.ts` 或 `packages/core/viewmodels/use-{feature}-viewmodel.test.ts`: ViewModel 的单元测试。
  * 更新对应 ViewModel 目录下的 `README.md`。

**步骤:**

1.  **DTO 定义与转换器实现 (如有必要):**

      * **目标:** 如果 Model 返回的数据结构与 ViewModel 内部状态或 View 层期望的数据结构差异较大，且转换逻辑复杂，则可能需要定义 DTO (Data Transfer Object) 并在 ViewModel 中进行转换。多数情况下，Model 层已完成主要转换，ViewModel 层可能只做轻量级适配。
      * **AI 增强指令 (如果需要):**
          * **指令 (示例):** "假设 `user-model.ts` 返回的 `TransformedUser` 包含 `initials`，但在 `UserListViewModel` 中我只需要 `id` 和 `displayName`。创建一个内部类型 `UserViewData` 并在 ViewModel 中进行映射。"
          * AI 可以在 ViewModel 内部或共享类型文件中生成此类辅助类型和映射逻辑。
      * **开发者评审与完善。**
      * **关键检查点:** ✅ 类型定义清晰 ✅ 转换逻辑正确（如果存在）。

2.  **ViewModel 实现 (ViewModel Implementation):**

      * **目标:** 实现具体的 ViewModel Hook，管理视图状态，调用 Model 获取数据，并暴露处理后的数据和操作方法给 View。
      * **核心输入:** Model Hook, ViewModel 设计, `application-architecture.mdc` (MVVM 示例)。
      * **步骤:**
        1.  **(人工)** 设计 ViewModel Hook 的结构 (`function useMyViewModel() { ... }`)，明确所需状态 (e.g., `useState`, `@preact/signals/safe-react` 的 `signal`) 和要从 Model 获取的数据。
        2.  **(AI 生成)**
              * **指令 (示例 - 参照 `application-architecture.mdc` 中的 `use-user-list-viewmodel.ts`):** "请在 `app/user/viewmodels/use-user-list-viewmodel.ts` 中实现 `useUserListViewModel` Hook。它应调用 `app/user/models/user-model.ts` 中的 `useUsers()` Hook。管理一个 `searchTerm` 状态 (string)。过滤从 `useUsers` 获取的 `users` (TransformedUser[])，只返回 `displayName` 包含 `searchTerm` 的用户。暴露 `filteredUsers`, `isLoading` (来自Model), `error` (来自Model), `searchTerm`, `setSearchTerm` 函数，以及一个调用 `mutateUsers` (来自Model) 的 `handleRefresh` 函数。"
              * AI 根据指令生成 ViewModel Hook 的初步实现。
        3.  **(开发者评审与完善)**
              * **业务逻辑**: 仔细核对 AI 实现是否与 UI/UX 需求和 ViewModel 设计完全一致。
              * **状态管理**: 确认状态（本地 `useState`/`signal`，或来自 Model）管理是否正确。
              * **Model 调用**: 确认是否正确调用了 Model Hook/函数。
              * **数据转换/适配**: 确认 Model 数据是否被正确适配以供 View 使用。
              * **暴露接口**: 确认暴露给 View 的数据和方法是否符合设计。
              * **代码规范**: 检查命名、注释、Hooks 规则等是否符合规范。
      * **关键检查点:** ✅ 视图逻辑准确 ✅ 状态管理正确 ✅ Model 调用无误 ✅ 接口清晰 ✅ 代码符合规范。
      * **输出物:** `use-{feature}-viewmodel.ts` 文件中 ViewModel Hook 的完整实现。

3.  **ViewModel 单元测试 (ViewModel Unit Testing):**

      * **目标:** 验证 ViewModel 逻辑的正确性，通过 Mock Model 层的依赖。
      * **核心输入:** ViewModel 实现代码, Model Hook 接口定义 (用于 Mock), Testing Library (e.g., `@testing-library/react` for hooks)。
      * **步骤:**
        1.  **(人工/AI)** 创建测试文件 `use-{feature}-viewmodel.test.ts`。
        2.  **(人工/AI)** Mock Model Hook (`user-model.ts`)。
        3.  **(AI 生成)**
              * **指令 (示例):** "为 `useUserListViewModel` Hook 编写单元测试。Mock `useUsers` Hook。测试场景：1. 初始状态下，`filteredUsers` 为空 (若mock `useUsers` 返回空)。2. `useUsers` 返回数据后，`filteredUsers` 正确显示。3. 设置 `searchTerm`后，`filteredUsers` 被正确过滤。4. 调用 `handleRefresh` 时，mock 的 `mutateUsers` 被调用。"
              * AI 生成测试用例骨架和 Mock 设置。
        4.  **(开发者评审、完善与执行)**
              * 评审测试场景，完善 Mock 返回值和断言。
              * 使用 `@testing-library/react` 的 `renderHook` 和 `act` 进行测试。
              * 执行 `pnpm test --filter={appName}`。
      * **关键检查点:** ✅ Mock 设置正确 ✅ 测试场景覆盖关键逻辑 ✅ 断言准确 ✅ 测试用例全部通过 ✅ 代码覆盖率达标。
      * **输出物:** `use-{feature}-viewmodel.test.ts`, 测试覆盖率报告。

4.  **文档更新 (`README.md`):**

      * **人工/AI:** 根据 `application-architecture.mdc` 的 5. 层级文档化与复用策略，更新对应 ViewModel 目录下的 `README.md`，添加新 ViewModel Hook 的描述、暴露接口、使用场景等。

**完成标志:**

  * ViewModel Hook 已实现并通过评审。
  * ViewModel 层的单元测试已完成、通过，且覆盖率达标。
  * 相关的 `README.md` 已更新。

### Phase 4: 视图层与路由实现与验证 (View Layer & Routing Implementation & Validation)

**阶段说明:** 在 ViewModel 层 (Phase 3) 实现并通过单元测试后，本阶段负责构建用户直接与之交互的视图层，包括页面 (Pages)、业务视图 (Business Views)、以及构成这些视图的各类 UI 组件（通用组件、业务组件、纯UI组件）。同时配置路由规则。

**目标:** 实现页面、业务视图和相关组件，处理用户交互，调用 ViewModel，并根据 ViewModel 返回的数据渲染 UI。配置 Next.js 路由。

**核心输入:**

  * UI/UX 设计稿和交互说明。
  * `app/{feature}/viewmodels/use-{feature}-viewmodel.ts`: 已实现的 ViewModel Hook。
  * `ai-rules/frontend/public-rules/application-architecture.mdc` (View 层职责、MVVM 模式、目录结构)。
  * `ai-rules/frontend/project-rules/schoolRoom/docs/ui.readme.md` (`@repo/ui` 组件库)。
  * `ai-rules/frontend/project-rules/schoolRoom/docs/core.readme.md` (可复用的业务组件)。
  * `ai-rules/frontend/project-rules/schoolRoom/monorepo&global.mdc` (Tailwind CSS, Next.js App Router 规范)。
  * `packages/config-eslint`。

**输出:**

  * `apps/{appName}/app/.../page.tsx`: Next.js 页面组件。
  * `apps/{appName}/app/views/{feature}/{feature}-view.tsx`: 业务视图组件。
  * `apps/{appName}/app/components/{feature}/...`: 特定于应用的组件。
  * (可能) `packages/ui/src/components/...` 或 `packages/core/src/...` (如果开发过程中识别出新的可共享组件)。
  * `apps/{appName}/app/.../*.test.tsx` 或 `apps/{appName}/app/views/.../*.test.tsx`: 组件的单元/集成测试。
  * 更新后的路由配置 (Next.js App Router 基于目录结构自动完成大部分)。
  * 更新对应 View/Component 目录下的 `README.md` (如果适用)。

**步骤:**

1.  **组件实现 (Component Implementation - Pages, Business Views, UI Components):**

      * **目标:** 实现用户界面，响应用户交互，并通过 ViewModel 更新或获取数据。
      * **核心输入:** UI 设计稿, ViewModel Hook, `@repo/ui` 组件文档, Tailwind CSS。
      * **步骤:**
        1.  **(人工)** 根据 UI 设计稿和 `application-architecture.mdc` 规划组件结构（页面、业务视图、子组件）。优先复用 `@repo/ui` 和 `@repo/core` 中的组件。
        2.  **(AI 生成 - 示例：业务视图 `UserListView`):**
              * **指令 (参照 `application-architecture.mdc` 中的 `user-list-view.tsx`):** "请在 `app/user/views/user-management/user-list-view.tsx` 中创建 `UserListView` 组件。它应使用 `app/user/viewmodels/use-user-list-viewmodel.ts` 中的 `useUserListViewModel` Hook。根据 ViewModel 返回的 `isLoading`, `error`, `users (filteredUsers)`, `searchTerm` 渲染界面。包含一个 `<input>` 用于搜索 (绑定 `searchTerm` 和 `setSearchTerm`)，一个刷新按钮 (调用 `handleRefresh`)，并使用 `@repo/ui/components/user-list` (假设存在，或一个通用的列表组件) 来展示用户列表。处理加载和错误状态的显示。"
              * AI 根据指令生成业务视图组件的初步实现，包括 JSX 结构和 ViewModel Hook 的调用。
        3.  **(开发者评审与完善)**
              * **UI/UX 准确性**: 确保组件渲染结果与设计稿一致，交互行为符合预期。使用 Tailwind CSS 实现样式。
              * **ViewModel 集成**: 确认正确调用 ViewModel Hook，并将返回的状态和方法绑定到 JSX。
              * **组件复用**: 检查是否最大化复用了 `@repo/ui` 和 `@repo/core` 的组件。
              * **Props 传递**: 确保子组件接收正确的 Props。
              * **可访问性**: 遵循基本的 WCAG 指南（例如，为表单元素添加标签，图像添加 alt 文本）。
              * **代码规范**: 检查命名、注释、组件结构等。
      * **关键检查点:** ✅ UI/UX 匹配设计 ✅ ViewModel 集成正确 ✅ 组件复用合理 ✅ Props 传递无误 ✅ 交互逻辑正确 ✅ 代码符合规范。
      * **输出物:** `.tsx` 文件中组件的完整实现。

2.  **组件单元/集成测试 (Component Unit/Integration Testing):**

      * **目标:** 验证组件的渲染、交互和与 ViewModel 的集成是否正确。
      * **核心输入:** 组件实现代码, ViewModel Hook (可能需要 Mock), Testing Library (`@testing-library/react`), Jest/Vitest。
      * **步骤:**
        1.  **(人工/AI)** 创建测试文件 `*.test.tsx`。
        2.  **(AI 生成)**
              * **指令 (示例 - 为 `UserListView`):** "为 `UserListView` 组件编写测试。Mock `useUserListViewModel` Hook。测试场景：1. 加载状态下显示 '加载中...'。2. 错误状态下显示错误信息。3. 成功加载用户数据后，正确渲染用户列表和搜索框。4. 用户在搜索框输入文本时，`setSearchTerm` 被调用。5. 点击刷新按钮时，`handleRefresh` 被调用。"
              * AI 生成测试用例骨架，包括 Mock ViewModel 和用户事件模拟。
        3.  **(开发者评审、完善与执行)**
              * **渲染断言**: 验证组件是否根据不同的 Props 和 ViewModel 状态正确渲染。
              * **交互测试**: 使用 `@testing-library/user-event` 模拟用户交互（点击、输入），并验证 UI 变化或 ViewModel 方法调用。
              * **Mock ViewModel**: 控制 ViewModel Hook 的返回值以测试不同场景。
              * 执行 `pnpm test --filter={appName}`。
      * **关键检查点:** ✅ 渲染正确 ✅ 交互符合预期 ✅ ViewModel Mock 有效 ✅ 测试用例通过。
      * **输出物:** `*.test.tsx`。

3.  **路由配置 (Routing Configuration with Next.js App Router):**

      * **目标:** 确保页面可以通过正确的 URL 访问，并应用必要的布局和中间件 (如果 Next.js App Router 支持的方式)。
      * **核心输入:** Phase 0 规划的路由, Next.js App Router 文档。
      * **步骤:**
        1.  **(开发者)** 根据 Next.js App Router 的约定，通过在 `app/` 目录下创建文件夹和 `page.tsx` (或 `route.ts` for API routes) 来定义路由。
        2.  **(开发者)** (可选) 创建 `layout.tsx` 来定义共享布局。
        3.  **(AI 辅助 - 咨询性):**
              * **指令 (示例):** "我需要在 `/dashboard/settings` 路径下创建一个新页面。在 Next.js App Router 中，我应该如何组织文件结构？这个页面需要应用根 `layout.tsx`。"
              * AI 可以提供关于 Next.js App Router 最佳实践的建议。
        4.  **(开发者评审与确认)**
              * **路径结构**: 确认文件和文件夹结构正确反映了期望的 URL 路径。
              * **页面组件**: 确认 `page.tsx` 正确导出了页面组件。
              * **布局**: 确认布局被正确应用。
      * **关键检查点:** ✅ 路由可访问 ✅ 页面正确渲染 ✅ 布局应用正确。
      * **输出物:** `app/` 目录下更新的文件结构。

4.  **文档更新 (`README.md` 和组件库文档):**

      * **人工/AI:** 如果在开发过程中创建了新的可复用 UI 组件 (并计划移至 `@repo/ui` 或 `@repo/core`)，则更新相应组件库的 `README.md`。对于应用内的业务视图或复杂组件，可在其目录或 `app/views/README.md` 中添加文档。

**完成标志:**

  * 所有相关的页面、业务视图和组件已实现并通过评审。
  * 组件的单元/集成测试已完成并通过。
  * 前端路由已正确配置，页面可访问。
  * 相关文档已更新。

### Phase 5: 整合、端到端测试与最终化 (Integration, E2E Testing & Finalization)

**阶段核心:** 开发者主导，AI 辅助。

**AI 角色:** 集成助手 / 检查员 / 测试用例建议者 / 脚本生成助手。

**目标:** 确保所有前端组件、模块（Model, ViewModel, View）、共享包和路由能正确协同工作，通过端到端 (E2E) 测试验证关键用户流程，执行最终的代码质量检查，并准备代码评审。

**核心输入:**

  * 已完成并通过单元/集成测试的各层代码。
  * 更新后的路由配置。
  * Monorepo 构建配置 (`turbo.json`, 各 `package.json` 脚本)。
  * E2E 测试环境 (可能需要 Mock 后端 API，例如通过 Apifox Mock URL 或前端 Mock Server)。
  * `ai-rules/frontend/project-rules/schoolRoom/monorepo&global.mdc` (构建、lint、格式化命令)。
  * 项目开发 Checklist (`project-progress.md`)。
  * 代码 Lint 工具配置 (`@repo/config-eslint`) 和 Prettier 配置。

**输出:**

  * 通过 Monorepo 构建的应用 (`turbo run build`)。
  * E2E 测试脚本 (`tests/e2e/{feature}.spec.ts` 或类似路径，使用 Playwright/Cypress)。
  * E2E 测试报告。
  * 通过 Lint 和格式化检查的代码。
  * 最终构建产物 (Next.js 应用的静态文件和 serverless functions)。
  * Code Review 请求。

**流程:**

1.  **Monorepo 依赖与构建检查 (Monorepo Dependencies & Build Check):**

      * **目标:** 确保所有内部包 (`workspace:*`) 依赖正确，并且整个 Monorepo (或特定应用) 可以成功构建。
      * **核心输入:** `package.json` 文件, `turbo.json`。
      * **步骤:**
        1.  **(开发者)** 仔细检查各 `package.json` 中的 `workspace:*` 依赖是否正确指向内部包的最新版本。
        2.  **(开发者)** 确认 Turborepo (`turbo.json`) 的流水线配置正确，能够正确构建和链接所有依赖。
        3.  **(AI 辅助 - 可选):**
              * **指令 (示例):** "我添加了对 `@repo/new-feature-lib` (workspace:\*) 的依赖到 `apps/stu`。请帮我检查 `turbo.json` 中是否需要更新构建流水线以确保 `new-feature-lib` 在 `stu` 构建前被构建。"
              * AI 可以帮助分析 `turbo.json` 配置。
        4.  **(开发者执行):**
              * 在项目根目录下运行 **`pnpm install`** (确保 pnpm-lock.yaml 更新)。
              * 运行 **`turbo run build --filter={appName}`** 或 **`turbo run build`** (构建所有)。
              * **检查输出**: 仔细查看构建输出。如果出现错误（如依赖找不到、类型冲突、构建失败），根据错误提示修改代码或配置并重新运行，直到成功。
      * **关键检查点:** ✅ `workspace:*` 依赖正确 ✅ Turborepo 流水线配置合理 ✅ `pnpm install` 完成 ✅ `turbo run build` 成功执行无错误。
      * **输出物:** 成功构建的应用/包。

2.  **端到端测试设计与实现 (E2E Test Design & Implementation):**

      * **目标:** 验证跨越多个页面、组件和用户交互的关键业务流程是否按预期工作，从用户视角确保应用功能正确。
      * **核心输入:** UI/UX 设计稿, PRD, API Mocking策略 (Apifox Mock URL 或前端 Mock Server)。
      * **步骤:**
        1.  **(开发者)** **设计测试场景**: 识别需要进行 E2E 测试的关键用户流程（例如：用户注册 -\> 登录 -\> 浏览课程 -\> 开始学习某一节）。明确每个场景的起始点、用户操作步骤、预期页面变化和最终结果。
        2.  **(开发者/AI)** **编写测试用例 (使用 Playwright/Cypress):**
              * 通常在 `apps/{appName}/tests/e2e/` 目录下创建测试文件 (如 `user-registration.spec.ts`)。
              * **测试环境准备**: 配置 E2E 测试框架以使用正确的应用 URL (本地开发服务器或特定测试部署)。配置 API Mocking (例如，拦截网络请求并返回 Apifox Mock 数据，或指向 Mock 服务器)。
              * **页面对象模型 (POM - 可选但推荐):** 为常交互的页面或组件创建 Page Objects，封装页面元素的选择器和交互方法，使测试脚本更易读和维护。
              * **执行用户操作**: 在测试用例中，使用 E2E 测试框架的 API 模拟用户操作（访问URL、点击按钮、填写表单、滚动等）。
              * **断言**: 验证结果。断言应包括：
                  * 页面 URL 是否正确。
                  * 页面上关键元素是否存在、可见、内容正确。
                  * 用户操作后 UI 是否按预期更新。
                  * (如果与 Mock API 交互) 验证某些 Mock API 调用是否发生或参数是否正确 (通过拦截或 Mock Server 日志)。
              * **AI 辅助 (建议性):**
                  * **指令 (示例):** "请为用户登录流程 (页面 `/login`) 设计 Playwright E2E 测试用例。流程：1. 访问 `/login`。 2. 输入用户名 'testuser' 和密码 'password' 到对应的输入框。 3. 点击登录按钮。 4. 验证页面跳转到 `/dashboard`。 5. 验证 `/dashboard` 页面包含文本 '欢迎, testuser'。请提供测试用例的 TypeScript 代码骨架，包括选择器建议和断言点。"
                  * AI 可以提供测试场景的结构、选择器建议和断言建议。
        3.  **(开发者执行与调试):**
              * **[执行]** 运行 E2E 测试 (例如 `pnpm test:e2e --filter={appName}` 或特定框架命令)。
              * 调试失败的测试用例，检查代码逻辑、选择器、Mock 数据或测试环境问题。
      * **关键检查点:** ✅ 关键用户流程被覆盖 ✅ 测试环境和 Mock 设置正确 ✅ 测试数据隔离/准备 ✅ 断言覆盖页面状态和关键内容 ✅ 测试用例通过。
      * **输出物:** E2E 测试脚本, 测试报告。

3.  **最终检查与准备评审 (Final Checks & Review Preparation):**

      * **目标:** 确保代码质量符合规范，项目可成功构建和运行，并准备好进行 Code Review。
      * **核心输入:** 最终的代码库。
      * **步骤:**
        1.  **(开发者执行) 代码格式化与 Lint**:
              * 运行 **`turbo run format`** (或 `pnpm format` 在根目录) 格式化代码。
              * 运行 **`turbo run lint`** (或 `pnpm lint` 在根目录)。
              * 运行 **`turbo run typecheck`** (或各应用/包的 `tsc --noEmit` 命令，或根目录的 `check-types` 脚本) 进行全局类型检查。
              * **修复所有错误**: 根据工具的报告修复所有格式化、Lint 和类型错误。
        2.  **(开发者执行) 构建项目 (再次确认):**
              * 运行 **`turbo run build`**。
              * 确认项目可以成功编译。
        3.  **(开发者执行) 本地运行与验证 (可选但推荐):**
              * 运行 **`turbo run dev --filter={appName}`** 启动开发服务器。
              * 手动测试已开发的功能，确保基本可用性。
        4.  **(开发者参考) 对照 Checklist**:
              * 打开 `project-progress.md`。
              * 逐项检查是否已完成所有开发步骤和规范要求（文档、测试、MVVM分层、代码规范、浏览器兼容性考虑等）。
        5.  **(开发者执行) 发起 Code Review**:
              * 将代码推送到版本控制系统（如 Git）。
              * 创建 Pull Request (或 Merge Request)。
              * 在 PR 描述中简要说明本次开发内容、关键改动，链接到相关的 UI 设计稿或 PRD，并邀请相关人员进行评审。提及对 `README.md` 等文档的更新。
      * **关键检查点:** ✅ 代码格式化 ✅ Lint 无错误 ✅ 类型检查无错误 ✅ 项目构建成功 ✅ Checklist 完成 ✅ Code Review 已发起。
      * **输出物:** 通过检查和构建的代码, Code Review 请求。

**完成标志:**

  * Monorepo 依赖正确且项目可成功构建。
  * 关键用户流程的 E2E 测试已完成并通过。
  * 代码通过所有质量检查（格式化、Lint、类型检查）。
  * 已对照 Checklist 进行最终检查。
  * 代码已提交并创建 Code Review 请求。

### Phase 6: 文档 (Documentation)

**阶段核心:** AI 辅助，开发者评审。

**AI 角色:** 文档助手 / 生成器 / 检查员。

**目标:** 生成、更新并完善项目相关的各类文档，**特别是组件库文档、模块 `README.md` 文件**，确保代码和系统的可理解性、可维护性和易用性。

**核心输入:**

  * 最终的、经过评审和测试的代码库。
  * 组件 Props 定义 (`.tsx` 文件中的 TypeScript 类型)。
  * 模块功能说明。
  * `ai-rules/frontend/public-rules/application-architecture.mdc` (层级文档化规范)。
  * `ai-rules/frontend/project-rules/schoolRoom/docs/ui.readme.md` (作为 `@repo/ui` 组件文档的模板和规范)。
  * `ai-rules/frontend/project-rules/schoolRoom/docs/core.readme.md`, `lib.readme.md` (作为对应包 `README.md` 的规范)。
  * 项目 `README.md` (根目录或 `apps/*` 下)。
  * (可选) Storybook 配置和现有 stories。

**输出:**

  * 更新后的**组件库文档** (例如 `packages/ui/components/{componentName}/README.md` 或 Storybook mdx 文件)。
  * 更新后的 Model、ViewModel、View (业务视图)、共享模块 (`@repo/core`, `@repo/lib`) 的 `README.md` 文件。
  * 更新后的项目级 `README.md`。
  * (可选) 更新或生成的 Storybook stories。
  * 记录 API Mocking 策略和使用的 Mock 数据结构 (如果前端负责 Mock)。

**流程:**

1.  **组件库/模块文档生成/更新 (Component Library/Module Documentation):**

      * **目标:** 确保共享组件 (`@repo/ui`, `@repo/core` 中的业务组件) 和模块 (`@repo/core`, `@repo/lib` 中的 Hooks/utils, `app/models`, `app/viewmodels`) 有清晰、准确的文档。
      * **核心输入:** 组件/模块代码 (Props 定义, JSDoc), `ui.readme.md` (格式规范), `application-architecture.mdc` (README 内容规范)。
      * **步骤:**
        1.  **(开发者/AI)** 检查组件/模块的 Props 定义、JSDoc 注释是否完整和准确。
        2.  **(AI 生成/开发者编写):**
              * **指令 (示例 - 为 `@repo/ui` 新组件生成 README 片段):** "请为 `packages/ui/src/components/NewAwesomeComponent.tsx` 生成符合 `ui.readme.md` 格式的文档。它接收 `title (string, required)`, `items (string[])`, `onItemClick ((item: string) => void)` Props。场景说明：用于展示一个带标题的可点击列表。"
              * AI 根据指令和代码 (如果可访问) 生成 Markdown 文档片段。
              * **(开发者)** 将生成的片段整合到对应组件的 `README.md` (或特定文档文件) 中，或更新 Storybook mdx 文件。
        3.  **(开发者评审与验证):**
              * 检查生成的文档是否准确描述了组件/模块的功能、Props/API、使用场景。
              * **关键验证**: 文档中的 Props 类型、名称、是否必需与代码一致。示例代码（如有）能够正确运行。
      * **关键检查点:** ✅ 文档与代码一致 ✅ Props/API 描述清晰 ✅ 使用场景明确 ✅ 遵循项目文档规范。
      * **输出物:** 最新的组件/模块文档。

2.  **API Mocking 文档 (API Mocking Documentation):**

      * **目标:** 记录前端使用的 API Mocking 策略和数据结构，方便团队协作和调试。
      * **核心输入:** 使用的 Mocking 工具 (Apifox, Next.js Route Handlers), Mock 数据文件/代码。
      * **步骤:**
        1.  **(开发者)** 确定 Mocking 策略（例如，哪些接口使用 Apifox Mock URL，哪些使用本地 Route Handler）。
        2.  **(开发者/AI)** 在项目文档的某个部分（例如 `docs/api-mocking.md` 或相关模块的 `README.md`）记录：
              * Mocking 工具的配置和启动方法。
              * 每个被 Mock 的 API 端点，其 Mock 数据的来源 (Apifox 链接, Route Handler 文件路径) 和主要结构示例。
              * **指令 (示例):** "为 `/api/users` 的 Next.js Route Handler Mock (`app/api/users/route.ts`) 撰写文档说明，指出它返回 `User[]` 结构，并提供一个简化的 User 对象示例。"
        3.  **(开发者评审与完善):** 确保 Mocking 文档清晰，能帮助其他开发者快速理解和使用 Mock 环境。
      * **关键检查点:** ✅ Mocking 策略清晰 ✅ Mock 数据源和结构有说明。
      * **输出物:** API Mocking 文档。

3.  **项目 README 及其他文档更新 (Project README & Other Docs Update):**

      * **目标:** 更新项目入口文档 (`README.md`) 和其他相关文档（如架构图的文字描述），反映最新的功能、开发设置、构建/运行命令或架构变更。
      * **核心输入:** 项目根 `README.md`, `apps/{appName}/README.md`, 相关代码变更, 新增功能说明。
      * **步骤:**
        1.  **(开发者/AI)** 识别需要更新的文档部分（如：项目简介、技术栈更新、开发环境设置、运行命令、新增功能模块介绍等）。
        2.  **(AI 生成/开发者修改):**
              * **指令 (示例):** "请更新项目根目录下的 `README.md` 文件。在 '技术栈' 部分，补充说明集成了 Remotion (vX.Y) 用于动态视频。在 '本地开发' 部分，更新 `turbo run dev --filter={appName}` 命令以启动特定应用。"
              * AI 根据指令生成 README 的更新内容。
        3.  **(开发者评审与完善):** 确保 README 内容清晰、准确、易于理解。检查其他相关文档是否也需要同步更新。
      * **关键检查点:** ✅ README 反映最新状态 ✅ 设置/命令说明清晰 ✅ 其他相关文档同步更新。
      * **输出物:** 更新后的 `README.md` 及其他相关文档。

**完成标志:**

  * **组件库和核心模块的文档**已生成/更新并与代码一致。
  * API Mocking 策略和数据已文档化。
  * 项目 README 及其他必要文档已更新。

-----

## 3\. 关键成功因素

  * **清晰的规范与示例:** AI 的输出质量高度依赖于输入规范的明确性和代码示例的质量。**优化 `application-architecture.mdc` 以及各 `packages/*` 下的 `README.md` 文件 (特别是 `ui.readme.md`, `core.readme.md`, `lib.readme.md`) 是重中之重。**
  * **高质量的 UI 设计稿和 API 契约:** 清晰的设计稿和准确的 API 文档是 AI 理解需求并生成有效前端代码的基础。
  * **结构化输入:** 每次与 AI 交互时，提供精准、最小化的上下文信息（如组件的 Props 定义、期望的交互行为、引用的规范文档）。
  * **迭代与人工评审:** 将 AI 生成的代码（组件、Hooks、测试、文档）视为初稿，必须经过开发者评审、测试和必要的修改。
  * **开发者主导:** 开发者始终负责需求理解、UI/UX 设计解读、整体架构设计、复杂交互逻辑实现、最终质量把控和代码可维护性责任。
  * **流程适应性:** 本工作流是一个指导框架，具体实践中可根据项目和团队情况灵活调整。
  * **熟悉 Monorepo 工具链:** 开发者需熟练使用 Turborepo, pnpm 等工具进行依赖管理、构建和脚本执行。

## 4\. AI行为约束规则 (前端适用)

### 4.1 基本原则

1.  **最小生成原则**
      * 仅生成符合 UI 设计稿、组件 API 定义或明确指令要求的代码。
      * 禁止生成与当前任务无关的额外工具函数、组件或状态。
      * 禁止随意修改项目已定义的 TypeScript 类型、共享组件 API 或 ViewModel 接口。
2.  **规范遵循原则**
      * 严格遵循项目定义的 MVVM 架构规范 (`application-architecture.mdc`)、目录结构、命名约定。
      * 严格遵循 `monorepo&global.mdc` 中定义的技术栈和编码风格（ESLint, Prettier, TypeScript 配置）。
      * 优先复用 `@repo/ui`, `@repo/core`, `@repo/lib` 中已有的组件和工具，除非明确指示创建新的。
      * 禁止引入未在项目中明确允许或定义的第三方依赖。
3.  **安全性原则 (前端侧重)**
      * 生成处理用户输入的代码时，应考虑基本的 XSS 防护（尽管现代框架有所缓解，但直接操作 DOM 时需注意）。
      * 不在前端代码中硬编码敏感信息（如 API Keys），应通过环境变量等安全方式注入。
      * 对于从 API 获取并展示的数据，避免直接渲染未经验证的 HTML 内容。

### 4.2 MVVM 分层约束规则

#### 4.2.1 Model 层约束 (`app/models` 或 `packages/core/models`)

  * 主要负责数据获取 (如使用 SWR 调用 `@repo/lib/fetcher`)、数据缓存和原始数据到应用友好型的转换。
  * 应导出清晰定义的 Hook (如 `useEntityData`) 或函数。
  * 类型定义 (interfaces/types) 必须明确，与 API 契约保持一致或有清晰的转换说明。
  * 禁止包含特定于 View 的逻辑或直接操作 DOM。
  * 错误处理应规范，例如将 fetcher 错误传递给 ViewModel。

#### 4.2.2 ViewModel 层约束 (`app/viewmodels` 或 `packages/core/viewmodels`)

  * 作为 Model 和 View 之间的桥梁，实现为自定义 React Hooks (如 `useFeatureViewModel`)。
  * 负责管理与特定视图相关的状态（使用 `useState`, Signals 等）、处理用户交互派发的命令、调用 Model 获取/操作数据，并将数据适配后暴露给 View。
  * 禁止直接进行数据获取（应调用 Model 层），禁止直接操作 DOM（应通过状态驱动 View 更新）。
  * 逻辑应可测试，不强依赖具体 View 实现。

#### 4.2.3 View 层约束 (Pages `app/.../page.tsx`, Business Views `app/views/...`, Components `app/components/`, `@repo/ui`, `@repo/core` 中的组件)

  * 主要负责 UI 渲染和响应用户输入。
  * 应从 ViewModel 获取数据和操作方法。
  * 组件应尽可能保持“纯粹”或受控，其行为由 Props 和 ViewModel 驱动。
  * 优先使用 `@repo/ui` 提供的基础组件和 Tailwind CSS 进行样式开发。
  * 复杂的、可复用的 UI 逻辑可以封装成更小的子组件。
  * 禁止包含复杂的业务逻辑（应在 ViewModel 中处理）或直接调用 Model。

### 4.3 代码生成限制

#### 4.3.1 禁止生成的代码类型 (除非明确指示并符合项目规范)

1.  与项目技术栈 (Next.js, React, Signals, SWR, Tailwind) 不符的 UI 组件或状态管理方案。
2.  全局 CSS 文件或大量组件级 CSS Modules (优先 Tailwind CSS，除非特定场景允许)。
3.  未使用 `@repo/lib/fetcher` 或 SWR 的自定义 HTTP 请求逻辑。
4.  不符合 `monorepo&global.mdc` 中定义的 Linting 和 Formatting 规则的代码。
5.  复杂的、未在 `packages/lib` 或 `packages/core` 中规划的通用工具函数。

#### 4.3.2 状态管理限制

  * 组件内部临时状态使用 `useState` 或 Signals。
  * 业务视图相关的、需要跨组件共享的状态由 ViewModel 管理。
  * 跨业务视图或全局状态，优先考虑 React Context (结合 ViewModel) 或 Signals。
  * 禁止随意引入新的全局状态管理库。

#### 4.3.3 数据获取限制

  * 所有与后端 API 的交互必须通过 Model 层封装的 Hooks 或函数进行。
  * View 和 ViewModel 层不应直接执行 `Workspace` API 调用。

#### 4.3.4 样式限制

  * 主要使用 Tailwind CSS 进行原子化样式开发。
  * 共享 UI 组件 (`@repo/ui`) 样式需符合其设计规范。
  * 避免使用内联样式 (style prop) 处理复杂的布局和样式，优先使用 Tailwind 类。

### 4.4 测试代码约束

  * 单元测试优先使用 Jest/Vitest 配合 `@testing-library/react` (用于组件和 Hooks)。
  * ViewModel 测试应 Mock Model 层依赖。
  * View (Component) 测试应 Mock ViewModel 依赖或通过 Props 传入测试数据。
  * 禁止在单元测试中进行真实的 API 调用 (必须 Mock)。
  * E2E 测试 (Playwright/Cypress) 脚本应遵循 POM (Page Object Model) 模式（推荐）。

### 4.5 错误处理约束

  * Model 层负责捕获和初步处理数据获取错误，并将其传递给 ViewModel。
  * ViewModel 层负责将 Model 错误转换为 View 可理解的状态 (如 `error`对象, `errorMessage` 字符串)。
  * View 层负责根据 ViewModel 提供的错误状态向用户展示友好的错误提示。
  * 禁止在控制台 `console.error` 后不进行任何用户层面的反馈。

### 4.6 日志记录约束

  * 仅在开发环境使用 `console.log` 进行调试。生产构建应移除或限制调试日志。
  * 关键错误或需要追踪的用户行为，可考虑接入第三方日志服务 (如果项目配置)。
  * 避免在日志中记录敏感用户信息或原始 API 响应 (可能包含敏感数据)。

### 4.7 性能相关约束

  * 合理使用 React `memo`, `useCallback`, `useMemo` 进行性能优化，但避免过度优化。
  * 对于长列表，考虑使用虚拟化方案 (如 `react-window` 或 `react-virtualized`，如果 `@repo/ui` 未提供)。
  * 图片资源优化 (使用 Next.js `Image` 组件, 合适的格式和尺寸)。
  * 关注 Next.js 的构建输出和 Core Web Vitals。

### 4.8 代码模板规范

各层（Model, ViewModel, View）的具体代码模板和实现示例请参考：

  * `ai-rules/frontend/public-rules/application-architecture.mdc` (MVVM 示例, 目录结构)
  * `ai-rules/frontend/project-rules/schoolRoom/docs/ui.readme.md` (UI 组件实现规范)
  * `ai-rules/frontend/project-rules/schoolRoom/docs/core.readme.md` (核心业务模块实现规范)
  * `ai-rules/frontend/project-rules/schoolRoom/docs/lib.readme.md` (工具库实现规范)

## 5\. MVVM 分层架构编码规则 (前端)

### 5.1 Model 层规范 (`app/models` 或 `packages/[packageName]/models`)

  * **职责**: 负责数据的获取、存储、管理和转换。定义数据结构，封装与API、本地存储的交互。
  * **位置**: 应用内特定功能: `apps/{appName}/app/models/`。跨应用复用: `packages/core/models/` 或 `packages/lib/models/` (如果非常通用)。
  * **文件组织** (示例 `apps/coolApp/app/models/user-model.ts`):
      * `{entity}-model.ts`: 包含特定实体的数据类型定义 (TypeScript Interfaces/Types)、使用 SWR 和 `Workspaceer` 的数据获取 Hooks (e.g., `useUsers`)、数据转换逻辑。
      * `{entity}-model.test.ts`: Model 的单元测试。
      * `README.md`: (在该目录下) 索引和描述该目录下的所有 Models。
  * **规范要求**:
    1.  **数据获取**: 使用 SWR 进行数据获取和缓存管理，配合 `@repo/lib/utils/fetcher`。
    2.  **类型定义**: 清晰定义从 API 获取的原始数据类型和供 ViewModel 使用的转换后数据类型。
    3.  **数据转换**: 负责将 API 返回的原始数据转换为 ViewModel 更易于消费的格式。
    4.  **错误处理**: 捕获数据获取过程中的错误，并将其暴露给 ViewModel。
    5.  **无业务逻辑**: 不应包含特定于视图的展示逻辑或复杂的业务流程编排（这些属于 ViewModel）。
    6.  **可测试性**: 逻辑应易于通过 Mock `Workspaceer` 等依赖进行单元测试。
    7.  参考 `application-architecture.mdc` 的 Model 层描述和示例。
    8.  更新 `app/models/README.md` 或对应 package 内 models 目录的 `README.md`。

### 5.2 ViewModel 层规范 (`app/viewmodels` 或 `packages/[packageName]/viewmodels`)

  * **职责**: 作为 Model 和 View 之间的桥梁，承载主要的视图业务逻辑和表现逻辑。从 Model 获取数据，处理数据以适应 View需求，管理视图状态，并响应 View 的用户操作指令。
  * **位置**: 应用内特定功能: `apps/{appName}/app/viewmodels/`。跨应用复用: `packages/core/viewmodels/`。
  * **文件组织** (示例 `apps/coolApp/app/viewmodels/use-user-list-viewmodel.ts`):
      * `use-{feature}-viewmodel.ts`: 实现为自定义 React Hook (e.g., `useUserListViewModel`)。
      * `use-{feature}-viewmodel.test.ts`: ViewModel Hook 的单元测试。
      * `README.md`: (在该目录下) 索引和描述该目录下的所有 ViewModels。
  * **规范要求**:
    1.  **React Hooks**: 实现为自定义 React Hooks。
    2.  **状态管理**: 使用 React `useState`, `useReducer`, 或 `@preact/signals/safe-react` 管理视图相关的状态 (加载状态、错误状态、用户输入、UI控制状态等)。
    3.  **调用 Model**: 调用 Model 层暴露的 Hooks/函数获取和操作数据。
    4.  **逻辑处理**: 包含特定于视图的业务逻辑，如数据筛选、排序、格式化。
    5.  **暴露接口**: 向 View 暴露经过处理的数据和可供调用的操作方法 (命令)。
    6.  **可测试性**: 通过 Mock Model 层的依赖来进行单元测试。
    7.  禁止直接进行数据获取 (应调用 Model) 或直接操作 DOM。
    8.  参考 `application-architecture.mdc` 的 ViewModel 层描述和示例。
    9.  更新 `app/viewmodels/README.md` 或对应 package 内 viewmodels 目录的 `README.md`。

### 5.3 View 层规范 (Pages, Business Views, Components)

  * **职责**: 负责界面的渲染和用户输入响应。是一系列 React 组件，专注于“如何展示数据”和“如何响应用户操作”。
  * **位置**:
      * **Pages**: `apps/{appName}/app/{route...}/page.tsx` (Next.js App Router)。
      * **Business Views**: `apps/{appName}/app/views/{feature}/{feature}-view.tsx`。
      * **应用内通用/纯 UI 组件**: `apps/{appName}/app/components/`。
      * **业务视图特定子组件**: `apps/{appName}/app/views/{feature}/components/`。
      * **共享 UI 组件库**: `packages/ui/src/components/`。
      * **共享核心业务组件**: `packages/core/src/{module}/components/` (如果组件与核心业务逻辑紧密耦合)。
  * **文件组织** (示例 `apps/coolApp/app/views/user-management/user-list-view.tsx`):
      * `{feature}-view.tsx` 或 `{component-name}.tsx`。
      * `{feature}-view.test.tsx` 或 `{component-name}.test.tsx`: 组件的单元或集成测试。
      * (可选) `README.md`: 对于复杂的 Business View 或可复用应用组件，可以在其目录下添加。
  * **规范要求**:
    1.  **React 组件**: 实现为函数式 React 组件。
    2.  **调用 ViewModel**: 从对应的 ViewModel Hook 获取数据和操作方法。
    3.  **渲染**: 根据 ViewModel 提供的数据渲染 UI。优先使用 `@repo/ui` 组件和 Tailwind CSS。
    4.  **用户交互**: 将用户的交互事件 (点击、输入) 通知给 ViewModel 进行处理。
    5.  **保持纯粹**: 尽可能保持 View 组件的无状态性或仅包含纯 UI 相关的瞬时状态 (如动画效果、下拉框的展开/收起)。大部分逻辑和状态应由 ViewModel 管理。
    6.  **Props**: 通过 Props 接收数据和回调函数。
    7.  **可测试性**: 使用 `@testing-library/react` 进行测试，可 Mock ViewModel 依赖。
    8.  禁止包含复杂业务逻辑或直接调用 Model。
    9.  参考 `application-architecture.mdc` 的 View 层描述和示例，以及 `@repo/ui` 的组件使用规范。
    10. 对于新创建的可复用 UI 组件，应遵循 `packages/ui/README.md` 的规范进行文档化。

### 5.4 路由层规范 (Next.js App Router)

  * **职责**: 定义应用的 URL 结构和页面映射。
  * **位置**: 主要通过 `apps/{appName}/app/` 目录下的文件和文件夹结构定义 (Next.js App Router 约定)。
      * `layout.tsx`: 定义共享布局。
      * `loading.tsx`: 定义加载 UI。
      * `error.tsx`: 定义错误 UI。
      * `template.tsx`: 定义模板。
      * `not-found.tsx`: 定义 404 页面。
  * **规范要求**:
    1.  **目录结构**: 遵循 Next.js App Router 的基于文件系统的路由约定。
    2.  **动态路由**: 使用 `[folderName]` 约定创建动态路由段。
    3.  **Route Groups**: 使用 `(folderName)` 对路由进行组织而不影响 URL 路径。
    4.  **Layouts**: 合理使用 `layout.tsx` 实现嵌套布局和共享 UI。
    5.  **Server Components & Client Components**: 根据组件需求和 Next.js 规范，合理使用 `"use server"` 和 `"use client"` 指令。通常 Pages 和 Business Views 可能是 Client Components (`"use client"`) 因为它们需要交互和 Hooks。
    6.  **API Routes (Route Handlers)**: 如需前端 Mock API 或简单后端逻辑，可在 `app/api/.../route.ts` 中创建。

### 5.5 共享包规范 (`packages/*`)

  * **`@repo/ui`**:
      * 职责: 提供原子化、可复用的纯 UI 组件，基于 shadcn/ui 和 Tailwind CSS。
      * 规范: 组件 API 设计应简洁、灵活。遵循 `packages/ui/README.md` 的组件分类和文档规范。每个组件应有清晰的 Props 定义和使用场景说明。
  * **`@repo/core`**:
      * 职责: 封装跨应用的共享核心业务逻辑、数据模型 (非 API 直接交互部分，若有)、特定业务场景的 Hooks (ViewModel)、和服务、以及与业务紧密耦合的复杂组件。
      * 规范: 模块化设计，职责清晰。遵循 `packages/core/README.md` 的模块分类和文档规范。
  * **`@repo/lib`**:
      * 职责: 提供通用的工具函数、非业务相关的 Hooks (如 `useScreen`, `useSocket`)、类型定义。
      * 规范: 函数/Hook 应具有普适性。遵循 `packages/lib/README.md` 的模块分类和文档规范。
  * **`@repo/config-eslint`, `@repo/config-typescript`**:
      * 职责: 提供统一的 ESLint 和 TypeScript 配置，确保整个 Monorepo 的代码风格和类型检查标准一致。
      * 规范: 配置应作为项目的基础，各应用和包通过 `extends` 方式继承。

### 5.6 跨层规范 (MVVM 前端)

1.  **依赖规则**:
      * View -\> ViewModel -\> Model
      * 组件可以依赖共享包 (`@repo/ui`, `@repo/core`, `@repo/lib`)。
      * ViewModel 可以依赖 Model，也可以依赖 `@repo/core` 或 `@repo/lib` 中的 Hooks/utils。
      * Model 可以依赖 `@repo/lib` 中的 `Workspaceer` 等。
      * 内层 (Model) 不能依赖外层 (ViewModel, View)。
      * 依赖注入主要通过 React Hooks 的组合和 Props 传递实现。
2.  **错误处理**:
      * Model: 捕获 API 请求错误，转换为可识别的错误对象或状态，传递给 ViewModel。
      * ViewModel: 接收 Model 的错误，管理错误状态 (e.g., `error` state)，可能进行错误日志记录，并将错误信息或状态暴露给 View。
      * View: 根据 ViewModel 提供的错误状态，向用户展示友好的错误提示 (e.g., Toast, Alert, inline message)。
3.  **日志记录**:
      * Model: 可记录关键数据获取的成功/失败 (DEBUG 级别)。
      * ViewModel: 可记录关键业务逻辑的执行情况或错误 (INFO/ERROR 级别)。
      * View: 通常不直接进行日志记录，除非是捕获到未预期的渲染错误 (通过 Error Boundaries)。
      * 优先使用浏览器开发工具进行调试，生产环境日志按需接入。
4.  **数据转换**:
      * API 原始数据 -\> Model 内部数据结构 (可选): 在 Model 内部 fetcher 回调中。
      * Model 内部数据结构 -\> ViewModel 期望的数据结构: 主要在 Model 层完成，ViewModel 可做轻量适配。
      * ViewModel 状态 -\> View Props: ViewModel 将状态直接或稍作转换后作为 Props 传给 View。
5.  **验证规则**:
      * 表单输入验证: 通常在 View 层配合 UI 组件库的验证功能，或在 ViewModel 中处理提交前的验证逻辑。
      * 业务规则验证: 主要在 ViewModel 中实现。
6.  **测试要求**:
      * Model: 单元测试 (Mock HTTP 请求)。
      * ViewModel: 单元测试 (Mock Model 依赖)。
      * View (Components): 单元/集成测试 (Mock ViewModel 依赖或传入 Props, 使用 `@testing-library/react`)。
      * Pages/Business Views: 集成测试，E2E 测试。
      * 共享包 (`@repo/ui`, `@repo/core`, `@repo/lib`): 单元测试各自模块。

### 5.7 性能优化规范 (前端)

1.  **组件渲染优化**:
      * 合理使用 `React.memo` 对组件进行记忆化。
      * 使用 `useCallback` 缓存事件处理函数，`useMemo` 缓存计算结果，防止不必要的重渲染。
      * 避免在渲染函数中创建新的对象或数组作为 Props (除非必要)。
2.  **列表性能**:
      * 为列表项提供稳定的 `key` Prop。
      * 对于非常长的列表，考虑使用虚拟化技术 (e.g., `react-window`, `tanstack-virtual`)。
3.  **状态管理优化**:
      * 避免将不必要的组件连接到高频更新的状态。
      * Signals 本身具有细粒度更新的优势。
      * 对于 Context，拆分不同职责的 Context 以减少不相关更新。
4.  **代码分割与懒加载**:
      * Next.js App Router 自动进行基于路由的代码分割。
      * 对于非首屏必需的大型组件或库，考虑使用 `next/dynamic`进行动态导入 (懒加载)。
5.  **图像优化**:
      * 使用 Next.js 内置的 `next/image` 组件进行图像优化 (自动 WebP, 响应式尺寸)。
6.  **Bundle 大小分析**:
      * 定期使用 `@next/bundle-analyzer` 分析构建产物大小，识别并优化过大的依赖或代码块。
7.  **SWR/数据获取**:
      * 合理配置 SWR 的 `revalidateOnFocus`, `dedupingInterval` 等选项，避免不必要的 API 请求。
8.  **Debounce/Throttle**:
      * 对高频触发的事件 (如搜索框输入、窗口 resize) 使用去抖 (debounce) 或节流 (throttle)。 (`@repo/lib` 可提供相应 Hook)。

### 5.8 监控规范 (前端)

1.  **基础性能监控 (Core Web Vitals)**:
      * Next.js 默认支持或可以集成 Vercel Analytics 等工具自动上报 LCP, FID, CLS。
      * 关注这些指标以提升用户体验。
2.  **错误监控**:
      * 集成 Sentry, LogRocket 或类似前端错误监控服务，捕获运行时错误。
      * 利用 React Error Boundaries 捕获组件树内的渲染错误，并上报。
3.  **业务监控指标 (通过分析工具或自定义事件)**:
      * 关键用户操作的转化率 (如注册、购买、完成课程)。
      * 特定功能的使用频率。
      * 用户在关键流程中的流失点。
4.  **API 请求监控**:
      * 监控 API 请求的成功率、响应时间、错误类型 (可结合后端日志)。

### 5.9 安全规范 (前端)

1.  **XSS (跨站脚本攻击) 防护**:
      * React 默认会对 JSX 中的动态内容进行转义。
      * 避免使用 `dangerouslySetInnerHTML`，除非内容绝对可信且必要。若使用，确保内容经过严格清理。
      * 对用户生成的内容进行展示时要特别小心。
2.  **CSRF (跨站请求伪造) 防护**:
      * 通常由后端实现 CSRF Token 机制，前端负责在状态变更请求 (POST, PUT, DELETE) 中携带。
3.  **API 数据安全**:
      * 不在 URL 中传递敏感信息。
      * 确保通过 HTTPS 与 API 通信。
      * 不在前端存储持久性的敏感信息 (如完整的用户凭证)。使用安全的 Token 机制 (如 HttpOnly, Secure cookies 或安全的 localStorage 存储，并配合短期 Access Token 和 Refresh Token 策略)。
4.  **依赖安全**:
      * 定期使用 `pnpm audit` 检查并更新存在已知漏洞的依赖。
5.  **内容安全策略 (CSP)**:
      * 考虑在 Next.js 中配置 CSP HTTP 头部，限制资源加载来源，减少 XSS 风险。
6.  **第三方脚本**:
      * 谨慎引入第三方脚本，确保其来源可靠，并了解其权限。

### 5.10 代码质量规范 (参考 `monorepo&global.mdc`)

1.  **命名规范 (TypeScript/React)**:
      * **文件名**: `kebab-case.ts` 或 `kebab-case.tsx` (e.g., `user-list-view.tsx`, `use-auth.ts`)。
      * **组件名**: `PascalCase` (e.g., `UserList`, `PrimaryButton`)。
      * **Hooks**: `useCamelCase` (e.g., `useAuth`, `useFormInput`)。
      * **Interfaces/Types**: `PascalCase` (e.g., `UserProfile`, `ButtonProps`)。
      * **Variables/Functions**: `camelCase` (e.g., `currentUser`, `WorkspaceData`)。
      * **Constants/Enums**: `UPPER_SNAKE_CASE` 或 `PascalCase` (for enum names)。
2.  **注释规范**:
      * 使用 JSDoc 为公共组件的 Props、Hooks 的参数和返回值、复杂的函数编写注释。
      * 对复杂或不直观的逻辑块添加行内或块注释。
      * `README.md` 文件作为模块/组件库的主要文档入口。
3.  **代码组织**:
      * 遵循 `application-architecture.mdc` 中定义的目录结构。
      * 保持文件和函数/组件的单一职责，避免过大文件。
      * 合理拆分复杂组件为更小的、可管理的子组件。
      * Monorepo 包的组织遵循 `packages/*` 结构。
4.  **ESLint & Prettier**:
      * 严格遵循 `@repo/config-eslint` 定义的规则。
      * 使用 Prettier (`prettier-plugin-tailwindcss`) 自动格式化代码。
      * Husky pre-commit 钩子会强制执行 lint 和 format。
5.  **TypeScript**:
      * 启用并遵循 `@repo/config-typescript/base.json` 中的 `strict` 模式。
      * 为所有变量、函数参数、返回值添加明确的类型，避免使用 `any` (除非绝对必要且有充分理由)。
      * 利用 Utility Types (如 `Partial`, `Pick`, `Omit`) 提高类型表达的灵活性和准确性。

### 5.11 错误处理规范 (前端示例)

1.  **错误状态定义 (ViewModel)**:

    ```typescript
    // In use-user-list-viewmodel.ts
    import { signal } from "@preact/signals/safe-react"; // or useState

    // ...
    const error = signal<Error | null>(null); // or useState<Error | null>(null)
    const isLoading = signal<boolean>(false);

    // In Model call
    try {
      isLoading.value = true;
      const data = await model.fetchUsers();
      // ... process data
      error.value = null;
    } catch (e) {
      error.value = e instanceof Error ? e : new Error('An unknown error occurred');
    } finally {
      isLoading.value = false;
    }
    // ...
    return { users, isLoading, error /* ... */ };
    ```

2.  **错误展示 (View)**:

    ```tsx
    // In user-list-view.tsx
    import { useUserListViewModel } from '../viewmodels/use-user-list-viewmodel';
    import { Alert, AlertDescription, AlertTitle } from '@repo/ui/components/alert'; // Assuming Alert component exists

    export default function UserListView() {
      const { users, isLoading, error } = useUserListViewModel();

      if (isLoading.value) { // Assuming signal usage
        return <div>Loading users...</div>;
      }

      if (error.value) {
        return (
          <Alert variant="destructive">
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              Failed to load users: {error.value.message}
            </AlertDescription>
          </Alert>
        );
      }

      return (
        {/* Render user list using `users.value` */}
      );
    }
    ```

3.  **Toast/Notification for non-critical errors (View, via ViewModel method)**:

    ```typescript
    // ViewModel
    // const { toast } = useToast(); // from @repo/ui
    // function handleErrorGracefully(message: string) { toast({ title: "Operation Failed", description: message, variant: "destructive" }) }

    // View (calling ViewModel method)
    // <Button onClick={async () => {
    //   const result = await viewModel.performSomeAction();
    //   if (!result.success) viewModel.handleErrorGracefully(result.errorMessage);
    // }}>Perform Action</Button>
    ```

### 5.12 测试规范 (前端示例)

1.  **ViewModel Hook 测试 (using `@testing-library/react` and Jest/Vitest)**:

    ```typescript
    // use-user-list-viewmodel.test.ts
    import { renderHook, act } from '@testing-library/react';
    import { useUserListViewModel } from './use-user-list-viewmodel';
    import * as UserModel from '../models/user-model'; // To mock

    jest.mock('../models/user-model'); // Mock the entire model module

    describe('useUserListViewModel', () => {
      const mockUseUsers = UserModel.useUsers as jest.Mock;

      beforeEach(() => {
        mockUseUsers.mockReturnValue({
          users: [],
          rawUsers: [],
          isLoading: false,
          error: null,
          mutateUsers: jest.fn(),
        });
      });

      it('should return initial state correctly', () => {
        const { result } = renderHook(() => useUserListViewModel());
        expect(result.current.users.value).toEqual([]);
        expect(result.current.isLoading.value).toBe(false);
        expect(result.current.searchTerm.value).toBe('');
      });

      it('should filter users based on searchTerm', async () => {
        mockUseUsers.mockReturnValue({
          users: [{ id: 1, displayName: 'Alice Smith', initials: 'AS' }],
          isLoading: false, error: null, mutateUsers: jest.fn(),
        });
        const { result } = renderHook(() => useUserListViewModel());

        await act(async () => {
          result.current.searchTerm.value = 'Alice';
        });
        // Need to wait for signals/effects to propagate if filtering is async or effect-based
        // For simplicity, assuming direct sync filtering in VM based on signal change
        expect(result.current.users.value.length).toBe(1); // or filteredUsers
        expect(result.current.users.value[0].displayName).toBe('Alice Smith');

        await act(async () => {
          result.current.searchTerm.value = 'NonExistent';
        });
        expect(result.current.users.value.length).toBe(0);
      });
    });
    ```

2.  **Component Test (using `@testing-library/react` and Jest/Vitest)**:

    ```tsx
    // user-list-view.test.tsx
    import { render, screen, fireEvent } from '@testing-library/react';
    import UserListView from './user-list-view';
    import * as UserListViewModel from '../viewmodels/use-user-list-viewmodel'; // To mock
    import { signal } from '@preact/signals/safe-react';

    jest.mock('../viewmodels/use-user-list-viewmodel');

    describe('UserListView', () => {
      const mockUseUserListViewModel = UserListViewModel.useUserListViewModel as jest.Mock;

      it('should display loading state', () => {
        mockUseUserListViewModel.mockReturnValue({
          users: signal([]),
          isLoading: signal(true),
          error: signal(null),
          searchTerm: signal(''),
          setSearchTerm: jest.fn(), // For signals, direct assignment is typical
          handleRefresh: jest.fn(),
        });
        render(<UserListView />);
        expect(screen.getByText(/Loading users.../i)).toBeInTheDocument();
      });

      it('should display user list when loaded', () => {
         const usersSignal = signal([{ id: 1, displayName: 'Test User', initials: 'TU' }]);
        mockUseUserListViewModel.mockReturnValue({
          users: usersSignal,
          isLoading: signal(false),
          error: signal(null),
          searchTerm: signal(''),
          setSearchTerm: jest.fn(),
          handleRefresh: jest.fn(),
        });
        render(<UserListView />);
        // Assuming UserList component renders displayNames
        expect(screen.getByText('Test User')).toBeInTheDocument();
      });

      it('should call setSearchTerm on input change', () => {
        const mockSetSearchTerm = jest.fn();
        const searchTermSignal = signal('');
        // For signals, the setter is typically direct assignment to .value
        // or if it's a function passed from VM, it's that function.
        // Let's assume ViewModel exposes a function for this if not direct signal mutation.
        // For this example, we'll assume the VM hook directly returns the signal
        // and the component mutates `searchTerm.value`.
        // A more robust test would mock the VM behavior more precisely.

        mockUseUserListViewModel.mockReturnValue({
          users: signal([]),
          isLoading: signal(false),
          error: signal(null),
          searchTerm: searchTermSignal, // Pass the signal
          // setSearchTerm: mockSetSearchTerm, // If VM exposed a setter function
          handleRefresh: jest.fn(),
        });
        render(<UserListView />);
        const inputElement = screen.getByPlaceholderText(/搜索用户.../i);
        fireEvent.change(inputElement, { target: { value: 'test' } });
        expect(searchTermSignal.value).toBe('test'); // Directly check signal value
      });
    });
    ```

### 5.13 (Was DDD分层架构实现细节) MVVM 架构实现细节示例

Refer to `ai-rules/frontend/public-rules/application-architecture.mdc` Section 3 (MVVM 模式详解) for detailed examples of:

  * Model Layer (`app/models/user-model.ts`)
  * ViewModel Layer (`app/viewmodels/use-user-list-viewmodel.ts`)
  * View Layer (Pure UI Component - `app/components/user-list.tsx`)
  * Business View (`app/views/user-management/user-list-view.tsx`)

These examples showcase the typical structure, interactions, and directory conventions for the MVVM pattern within the project. Developers and AI should use these as primary references when implementing new features.
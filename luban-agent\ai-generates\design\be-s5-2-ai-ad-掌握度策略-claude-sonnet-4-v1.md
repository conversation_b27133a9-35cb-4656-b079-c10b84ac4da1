# 接口使用文档 - 掌握度策略系统 - V1.0

* **最后更新日期**: 2025-06-05
* **设计负责人**: AI API使用文档撰写专家
* **评审人**: 待填写
* **状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-06-05 | AI API使用文档撰写专家 | 初始草稿     |

## 1. 服务概述与设计目标

### 1.1 服务背景与范围
掌握度策略系统是一个智能化学习评估平台，核心目标是通过精准的学科能力评估、实时的知识点掌握度计算和个性化的学习内容推荐，提升学生学习效率和个性化学习体验。系统支持1000+并发用户，P95响应时间<2秒，处理千万级学生数据和亿级答题记录。

主要功能模块包括：
- 学科能力评估与分层：基于学生考试成绩和学校信息进行用户能力分层
- 知识点掌握度实时计算：根据学生答题表现动态计算知识点掌握程度
- 外化掌握度展示：向学生展示课程掌握情况的可视化指标
- 薄弱知识点识别：自动标识需要重点学习的知识点
- 教师数据驱动教学：为教师提供班级掌握度统计和分析

### 1.2 设计目标回顾
1. 提供符合 OpenAPI 3.x 规范的接口定义，确保可直接导入 Apifox 等工具
2. 清晰定义每个接口的请求、响应、参数、数据模型和错误码
3. 确保接口设计满足产品需求文档 (PRD) 中定义的核心用户场景和业务流程
4. 遵循团队的API设计规范和最佳实践
5. 接口定义包含完整的中文描述信息，便于理解和维护

## 2. 通用设计规范与约定

### 2.1 数据格式
* 请求体 (Request Body): `application/json`
* 响应体 (Response Body): `application/json`
* 日期时间格式: ISO 8601 (`YYYY-MM-DDTHH:mm:ss.sssZ`)

### 2.2 认证与授权
* 所有需要认证的接口，在请求头中传递 `Authorization: Bearer <token>`
* 教师相关接口需要额外的角色权限验证
* 管理员接口需要最高级别权限验证

### 2.3 版本控制
* 通过 URL 路径进行版本控制，如 `/api/v1/mastery/...`
* 支持向后兼容，新版本通过 `/api/v2/mastery/...` 提供

### 2.4 错误处理约定
* **通用错误响应体示例**:
  ```json
  {
    "code": 0,                // 业务错误码，0表示成功，非0表示错误
    "message": "错误信息",     // 用户可读的错误信息
    "detail": "错误详情,可选",  // 错误的详细信息，可选
    "data": "返回数据",        // 可选，返回的数据，错误时通常为空
    "pageInfo": "分页信息,可选"  // 可选，分页信息，错误时通常为空
  }
  ```
* **常用 HTTP 状态码**:
  * `200 OK`: 请求成功
  * `400 Bad Request`: 请求无效 (例如参数错误、格式错误)
  * `401 Unauthorized`: 未认证或认证失败
  * `403 Forbidden`: 已认证，但无权限访问
  * `404 Not Found`: 请求的资源不存在
  * `500 Internal Server Error`: 服务器内部错误

### 2.6 分页与排序约定
* **分页参数**: 分页通常使用 page 和 pageSize 查询参数
* **排序参数**: 排序可能使用 sort_by 和 order 参数

## 3. 接口列表

| 接口路径            | 请求方式 | 接口名称         | 接口描述               |
| -------------------| -------- | -------- | ---------------- |
| /api/v1/mastery/user-tiering/initialize | POST | 用户分层初始化 | 新学生系统初始化时进行用户分层 |
| /api/v1/mastery/knowledge-mastery/batch-initialize | POST | 批量知识点掌握度初始化 | 批量初始化学生知识点掌握度 |
| /api/v1/mastery/external-mastery/initialize | POST | 外化掌握度初始化 | 初始化课程级别的掌握度展示 |
| /api/v1/mastery/answer-records | POST | 答题记录提交与掌握度更新 | 学生答题后实时更新掌握度 |
| /api/v1/mastery/knowledge-mastery | GET | 知识点掌握度查询 | 查询学生知识点掌握度信息 |
| /api/v1/mastery/external-mastery/{courseId} | PUT | 外化掌握度更新 | 更新课程的外化掌握度 |
| /api/v1/mastery/weak-points/analyze | POST | 薄弱知识点识别与推荐 | 分析并推荐薄弱知识点 |
| /api/v1/mastery/class-statistics | GET | 班级掌握度统计查询 | 教师查看班级掌握度分布 |
| /api/v1/mastery/student-report/{studentId} | GET | 学生个体掌握度报告 | 教师查看单个学生详细报告 |
| /api/v1/mastery/class-weak-points | GET | 班级薄弱知识点汇总 | 教师查看班级薄弱知识点统计 |
| /api/v1/mastery/personal-analysis | GET | 个人学习分析报告 | 学生查看个人学习分析 |
| /api/v1/mastery/personalized-recommendations | GET | 个性化学习推荐 | 基于掌握度的智能推荐 |
| /api/v1/mastery/calculation-config | GET | 掌握度计算参数配置查询 | 查询算法参数配置 |
| /api/v1/mastery/calculation-config/{configId} | PUT | 掌握度计算参数配置更新 | 更新算法参数配置 |

## 4. 场景说明

### 4.1 新学生系统初始化场景

#### 4.1.1 场景描述
新入学的学生首次使用系统时，需要根据其学校信息、成绩排名等数据计算用户分层，并初始化各知识点的掌握度，为后续个性化学习提供准确的起点。

#### 4.1.2 业务流程
1. 学生/教师录入基础信息（成绩排名、学校类型）
2. 系统计算校准系数和学科能力分层
3. 为每个知识点设置个性化初始掌握度
4. 生成初始化报告供学生确认

#### 4.1.3 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 新学生
    participant Frontend as 前端应用
    participant MasteryAPI as 掌握度策略API
    participant UserCenter as 用户中心服务
    participant ContentPlatform as 内容平台服务

    Student->>Frontend: 访问系统进行初始化
    Frontend->>UserCenter: 获取学生基本信息
    UserCenter-->>Frontend: 返回学生信息（学校ID、成绩等）
    Frontend->>UserCenter: 获取学校质量信息
    UserCenter-->>Frontend: 返回学校录取率等信息
    Frontend->>MasteryAPI: POST /api/v1/mastery/user-tiering/initialize
    Note over MasteryAPI: 计算校准系数和用户分层
    MasteryAPI-->>Frontend: 返回分层结果（S+/S/A/B/C）
    Frontend->>ContentPlatform: 获取知识点列表
    ContentPlatform-->>Frontend: 返回知识点结构
    Frontend->>MasteryAPI: POST /api/v1/mastery/knowledge-mastery/batch-initialize
    Note over MasteryAPI: 批量初始化知识点掌握度
    MasteryAPI-->>Frontend: 返回批量初始化结果
    Frontend->>MasteryAPI: POST /api/v1/mastery/external-mastery/initialize
    Note over MasteryAPI: 初始化外化掌握度
    MasteryAPI-->>Frontend: 返回外化掌握度初始化结果
    Frontend->>Student: 显示初始化完成结果
```

### 4.2 学生日常答题学习场景

#### 4.2.1 场景描述
学生在各种学习场景中答题后，系统需要实时计算掌握度增量，更新知识点掌握度，并判断是否需要更新外化掌握度，同时识别薄弱知识点进行推荐。

#### 4.2.2 业务流程
1. 学生在各种学习场景中答题（AI课、练习、作业）
2. 系统识别题目类型、难度、重复情况
3. 应用相应的计算规则更新掌握度
4. 实时展示掌握度变化和学习建议
5. 累积数据用于薄弱知识点识别

#### 4.2.3 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端应用
    participant MasteryAPI as 掌握度策略API
    participant ContentPlatform as 内容平台服务

    Student->>Frontend: 完成答题
    Frontend->>ContentPlatform: 获取题目信息（难度等级）
    ContentPlatform-->>Frontend: 返回题目元数据
    Frontend->>MasteryAPI: POST /api/v1/mastery/answer-records
    Note over MasteryAPI: 记录答题并计算掌握度增量
    MasteryAPI-->>Frontend: 返回掌握度更新结果
    alt 累计答题≥5题
        Frontend->>MasteryAPI: PUT /api/v1/mastery/external-mastery/{courseId}
        Note over MasteryAPI: 更新外化掌握度（只增不降）
        MasteryAPI-->>Frontend: 返回外化掌握度变化
    end
    Frontend->>MasteryAPI: POST /api/v1/mastery/weak-points/analyze
    Note over MasteryAPI: 分析薄弱知识点
    MasteryAPI-->>Frontend: 返回薄弱知识点和推荐
    Frontend->>Student: 展示掌握度变化和学习建议
```

### 4.3 教师数据驱动教学场景

#### 4.3.1 场景描述
教师需要查看班级学生的掌握度分布情况，识别薄弱知识点，为教学决策提供数据支持，包括个体学生分析和班级整体统计。

#### 4.3.2 业务流程
1. 教师选择查看的班级和时间范围
2. 系统生成掌握度分布报告和薄弱知识点统计
3. 教师分析数据识别教学问题
4. 根据数据调整教学计划和资源分配

#### 4.3.3 接口调用时序图
```mermaid
sequenceDiagram
    participant Teacher as 教师
    participant Frontend as 教师端应用
    participant MasteryAPI as 掌握度策略API
    participant TeacherService as 教师服务
    participant UserCenter as 用户中心服务

    Teacher->>Frontend: 选择班级查看掌握度统计
    Frontend->>TeacherService: 验证教师权限
    TeacherService-->>Frontend: 返回权限确认
    Frontend->>TeacherService: 获取班级学生列表
    TeacherService-->>Frontend: 返回学生ID列表
    Frontend->>MasteryAPI: GET /api/v1/mastery/class-statistics
    Note over MasteryAPI: 统计班级掌握度分布
    MasteryAPI-->>Frontend: 返回班级掌握度分布
    Frontend->>MasteryAPI: GET /api/v1/mastery/class-weak-points
    Note over MasteryAPI: 识别班级薄弱知识点
    MasteryAPI-->>Frontend: 返回薄弱知识点统计
    alt 查看个别学生详情
        Frontend->>MasteryAPI: GET /api/v1/mastery/student-report/{studentId}
        Note over MasteryAPI: 生成学生个体报告
        MasteryAPI-->>Frontend: 返回学生详细报告
    end
    Frontend->>Teacher: 展示班级掌握度分析结果
```

### 4.4 学生自主学习规划场景

#### 4.4.1 场景描述
有自主学习意识的学生查看个人学习数据，分析薄弱点，制定针对性的学习计划，跟踪学习效果和进度。

#### 4.4.2 业务流程
1. 学生进入个人学习分析页面
2. 查看各科目掌握度趋势和变化
3. 系统识别薄弱知识点并推荐学习内容
4. 学生制定个性化学习计划
5. 设置学习目标和提醒

#### 4.4.3 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端应用
    participant MasteryAPI as 掌握度策略API
    participant RecommendationSystem as 推荐系统

    Student->>Frontend: 进入个人学习分析页面
    Frontend->>MasteryAPI: GET /api/v1/mastery/personal-analysis
    Note over MasteryAPI: 分析个人掌握度数据
    MasteryAPI-->>Frontend: 返回学习分析报告
    Frontend->>MasteryAPI: GET /api/v1/mastery/knowledge-mastery
    Note over MasteryAPI: 查询详细掌握度数据
    MasteryAPI-->>Frontend: 返回知识点掌握度列表
    Frontend->>MasteryAPI: GET /api/v1/mastery/personalized-recommendations
    Note over MasteryAPI: 生成个性化推荐
    MasteryAPI->>RecommendationSystem: 调用推荐算法
    RecommendationSystem-->>MasteryAPI: 返回推荐内容
    MasteryAPI-->>Frontend: 返回个性化学习建议
    Frontend->>Student: 展示完整的学习分析和建议
```

## 5. 检查清单

### 5.1 PRD核心需求场景与API覆盖性检查

| PRD核心需求点/用户场景 (ID)                     | 涉及的主要API接口 (路径与方法)                                  | 关键输入数据实体/参数 (示例)                        | 关键输出数据实体/参数 (示例)                           | 场景支持度(✅/⚠️/❌) | 数据流正确性(✅/⚠️/❌) | 备注/待办事项                                   |
| :---------------------------------------------- | :-------------------------------------------------------------- | :---------------------------------------------------- | :------------------------------------------------------- | :----------------------------------------------------- | :--------------------------------------------------------- | :---------------------------------------------- |
| `新学生系统初始化（冷启动场景）`                            | `1. POST /api/v1/mastery/user-tiering/initialize` <br/> `2. POST /api/v1/mastery/knowledge-mastery/batch-initialize` <br/> `3. POST /api/v1/mastery/external-mastery/initialize`                                      | `用户ID, 学校ID, 学科ID, 成绩排名百分比, 知识点列表` | `分层等级, 校准系数, 批量初始化结果, 外化掌握度设置`    | ✅                                                     | ✅                                                         | 完整支持新用户冷启动流程                                                |
| `学生日常答题学习（核心使用场景）`                  | `1. POST /api/v1/mastery/answer-records` <br/> `2. GET /api/v1/mastery/knowledge-mastery` <br/> `3. PUT /api/v1/mastery/external-mastery/{courseId}` <br/> `4. POST /api/v1/mastery/weak-points/analyze` | `用户ID, 题目ID, 答题结果, 答题时间, 知识点ID`            | `掌握度增量, 更新后掌握度, 外化掌握度变化, 薄弱知识点推荐` | ✅ | ✅ | 核心高频场景，支持复杂掌握度计算逻辑 |
| `教师数据驱动教学（管理场景）`        | `1. GET /api/v1/mastery/class-statistics` <br/> `2. GET /api/v1/mastery/student-report/{studentId}` <br/> `3. GET /api/v1/mastery/class-weak-points`                                          | `教师ID, 班级ID, 课程ID, 学生ID, 时间范围` | `班级掌握度分布, 学生详细报告, 薄弱知识点统计`  | ✅ | ✅ | 支持教师多维度数据分析需求 |
| `学生自主学习规划（反思场景）`   | `1. GET /api/v1/mastery/personal-analysis` <br/> `2. GET /api/v1/mastery/personalized-recommendations`                                                                 | `用户ID, 时间范围, 分析维度, 推荐类型`                                                       | `掌握度趋势, 学习进步情况, 个性化推荐内容`                                                          | ✅ | ✅ | 支持学生自主学习和反思需求 |
| `掌握度计算参数管理`        | `1. GET /api/v1/mastery/calculation-config` <br/> `2. PUT /api/v1/mastery/calculation-config/{configId}`                                          | `参数类型, 学科ID, 配置ID, 新参数值` | `计算参数配置, 更新结果`  | ✅ | ✅ | 支持算法参数动态配置和优化 |

### 5.2 API接口数据实体与数据库支持检查

| API接口 (路径与方法)                     | 操作类型 (CRUD) | 主要数据实体 (模型名称)      | 关键数据属性 (字段名示例)                                 | 依赖的数据库表/视图 (示例)        | 数据库操作 (读/写/更新/删) | 数据可获得性/可行性(✅/⚠️/❌) | 数据一致性保障 (简述策略)                               | 备注/待办事项                                                                 |
| :--------------------------------------- | :-------------- | :--------------------------- | :---------------------------------------------------------- | :-------------------------------- | :--------------------------- | :--------------------------------------------------------- | :-------------------------------------------------------- | :---------------------------------------------------------------------------- |
| `POST /api/v1/mastery/user-tiering/initialize`               | Create          | `UserTiering`                       | `user_id, subject_id, school_id, tiering_level, calibration_coefficient_a, calibration_coefficient_b`  | `tbl_mastery_user_tiering`                           | 写                           | ✅                                                         | `事务保证原子性，唯一索引防重复`                                            | `需要复杂的校准系数计算逻辑`                                                              |
| `POST /api/v1/mastery/answer-records`             | Create            | `AnswerRecord`, `KnowledgeMastery`                       | `user_id, question_id, answer_result, mastery_increment, current_mastery`                       | `tbl_mastery_answer_record`, `tbl_mastery_knowledge_mastery`                           | 写/更新                           | ✅                                                         | `事务保证答题记录和掌握度更新的原子性`                                            | `高频接口，需要性能优化和并发控制`                                                        |
| `GET /api/v1/mastery/knowledge-mastery`             | Read          | `KnowledgeMastery`            | `user_id, knowledge_point_id, current_mastery, cumulative_answer_count`               | `tbl_mastery_knowledge_mastery`                           | 读                         | ✅                                                         | `读取操作，使用缓存提升性能`                                      | `支持批量查询，需要索引优化`                                                    |
| `PUT /api/v1/mastery/external-mastery/{courseId}`                   | Update            | `ExternalMastery`             | `user_id, course_id, external_mastery_value, last_updated_at`            | `tbl_mastery_external_mastery` | 更新                           | ✅                                                        | `乐观锁防止并发更新冲突，只增不降业务规则`                                            | `需要实现只增不降的业务逻辑`                                            |
| `GET /api/v1/mastery/class-statistics`        | Read                 | `ExternalMastery`, `WeakKnowledge`              | `user_id, course_id, external_mastery_value, weakness_degree`                                                             | `tbl_mastery_external_mastery`, `tbl_mastery_weak_knowledge`                                   | 读                              | ✅                                                            | `读取操作，支持缓存`                                                           | `需要聚合查询优化，考虑使用ClickHouse分析表`                                                                               |

### 5.3 API通用设计规范符合性检查

| 检查项                                                                 | 状态 (✅/⚠️/❌/N/A) | 备注/说明                                                                                                |
| :--------------------------------------------------------------------- | :------------------- | :------------------------------------------------------------------------------------------------------- |
| 1. **命名规范**: 接口路径、参数、数据实体命名是否清晰、一致，并遵循团队约定？        | ✅                      | 使用RESTful风格，路径清晰，参数命名一致                                                                 |
| 2. **HTTP方法**: GET, POST, PUT, DELETE, PATCH等HTTP方法使用是否语义恰当？        | ✅                      | GET用于查询，POST用于创建，PUT用于更新，语义正确                 |
| 3. **状态码**: HTTP状态码是否准确反映操作结果 (成功、客户端错误、服务端错误)？        | ✅                      | 200成功，400参数错误，401认证失败，403权限不足，404资源不存在，500服务器错误                                                          |
| 4. **错误处理**: 错误响应格式是否统一 (参考2.4)，错误信息是否对用户友好且包含足够调试信息？ | ✅                      | 统一错误响应格式，包含错误码、消息和详情                                                           |
| 5. **认证与授权**: 需要保护的接口是否都正确实施了认证和授权机制 (参考2.2)？           | ✅                      | 所有接口需要JWT认证，教师接口需要角色验证，管理员接口需要最高权限                                                                                   |
| 6. **数据格式与校验**: 请求和响应体结构是否定义清晰，数据类型是否准确？输入数据是否有必要的校验？ | ✅                      | JSON格式，数据类型明确，包含必要的参数校验                                                        |
| 7. **分页与排序**: 对于返回列表数据的接口，是否按约定提供了分页和排序功能 (参考2.6)？    | ✅                      | 统计查询接口支持分页和排序参数                                                                 |
| 8. **幂等性**: 对于创建和更新操作，是否考虑了幂等性设计？ (特别是PUT, DELETE)         | ✅                      | PUT操作设计为幂等，重复调用结果一致                              |
| 9. **安全性**: 是否有潜在的安全风险，如敏感信息泄露、SQL注入、XSS等？             | ✅                      | 参数校验和过滤，敏感数据加密，防止注入攻击                                             |
| 10. **性能考量**: 接口响应时间是否满足性能要求？大数据量查询是否有优化措施？             | ✅                      | 核心接口P95<2秒，使用Redis缓存，ClickHouse支持大数据分析                                                                   |
| 11. **文档一致性**: 本文档描述是否与OpenAPI规范文件及实际实现保持一致？ | ✅                      | 文档与接口分析文档保持一致                                                           |
| 12. **可测试性**: 接口是否易于进行单元测试、集成测试和端到端测试？                    | ✅                      | 接口设计支持自动化测试，参数明确，响应可预期                                                                                                          |
| 13. **向后兼容性**: 未来接口变更是否考虑了向后兼容性策略？                            |      N/A (新设计)    | V1版本新设计，后续版本需要考虑兼容性                                                         |

## 6. 附录 (Appendix)

### 6.1 引用文档
* 产品需求文档 (PRD): be-s1-2-ai-prd-掌握度策略-claude-sonnet-4.md
* 技术架构设计 (TD): be-s2-1-ai-td-掌握度策略-claude-sonnet-4.md
* 数据实体设计 (DE): be-s3-1-ai-de-掌握度策略-claude-sonnet-4.md
* 数据库设计 (DD): be-s4-2-ai-dd-掌握度策略-claude-sonnet-4.md
* 接口分析 (ADA): be-s5-1-ai-ada-掌握度策略-claude-sonnet-4.md

### 6.2 核心业务规则
* 外化掌握度遵循"只增不降"原则
* 掌握度计算基于复杂数学公式，需要高精度处理
* 薄弱知识点判断条件：外化掌握度/目标掌握度 ≤ 0.7 且 知识点掌握度 ≤ 0.5
* 用户分层等级：S+[0,5]、S(5,10]、A(10,50]、B(50,80]、C(80,100]

### 6.3 性能指标
* 支持1000+并发用户
* P95响应时间<2秒
* 掌握度更新处理能力：每秒500次请求
* 支持千万级学生数据和亿级答题记录

### 6.4 缓存策略
* 用户掌握度数据缓存1小时
* 班级统计数据缓存30分钟
* 计算参数配置缓存24小时
* 使用Redis集群支持高可用

### 6.5 异步处理
* 薄弱知识点分析采用异步处理
* 个人学习报告
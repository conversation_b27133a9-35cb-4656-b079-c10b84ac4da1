// @ts-nocheck
// @ts-nocheck
'use client';

import { useState, FormEvent, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import WorkflowSidebar from '@/components/workflow/WorkflowSidebar';
import { documentApi } from '@/lib/api';
import MarkdownPreviewModal from '@/components/markdown/MarkdownPreviewModal';
import WorkflowDebugPanel from '@/components/debug/WorkflowDebugPanel';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import axios from 'axios';
import { Terminal } from 'lucide-react';
import {
  Button,
  Input,
  Progress,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Box,
  useToast
} from '@chakra-ui/react';
import { CopyIcon } from '@chakra-ui/icons';
import { API_BASE_URL } from '@/app/configs/api';

export default function FormatPrdPage() {
  const [activeTab, setActiveTab] = useState('feishu');
  const [isLoading, setIsLoading] = useState(false);
  const [feishuUrl, setFeishuUrl] = useState('');
  const [flush, setFlush] = useState('no'); // 添加重新生成选项，默认为"否"
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState('');
  const [taskId, setTaskId] = useState('');
  const [logs, setLogs] = useState<string[]>([]);
  const [showLogs, setShowLogs] = useState(false);
  const [progress, setProgress] = useState(0);
  const [downloadUrl, setDownloadUrl] = useState('');
  const [retryCount, setRetryCount] = useState(0);
  const [maxRetries] = useState(5);
  const [retryTimerId, setRetryTimerId] = useState<NodeJS.Timeout | null>(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [isPreviewJustClosed, setIsPreviewJustClosed] = useState(false);
  const logEndRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const wsRef = useRef<WebSocket | null>(null);
  const toast = useToast();
  const [feishuUrlError, setFeishuUrlError] = useState('');
  const { workflowData, navigateToNextPage, getHiddenInputsProps } = useWorkflowData('format-prd');
  
  // 自动滚动到最新日志
  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);
  
  // WebSocket连接处理
  useEffect(() => {
    // 清理WebSocket连接
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
      
      // 清除重试定时器
      if (retryTimerId) {
        clearTimeout(retryTimerId);
        setRetryTimerId(null);
      }
    };
  }, [retryTimerId]);
  
  // 监听taskId变化，创建WebSocket连接
  useEffect(() => {
    if (taskId) {
      // 关闭已有的连接
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
      
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      let wsHost;
      try {
        const apiUrl = new URL(API_BASE_URL);
        wsHost = apiUrl.host;
      } catch (e) {
        console.error("无效的API_BASE_URL: ", API_BASE_URL);
        wsHost = 'localhost:8000'; // 回退到默认值
      }
      const wsUrl = `${protocol}//${wsHost}/api/ws/${taskId}`;
      
      console.log('正在连接WebSocket...', wsUrl);
      
      const socket = new WebSocket(wsUrl);
      wsRef.current = socket;
      
      socket.onopen = (event) => {
        console.log('WebSocket连接已建立', event);
        addLog('WebSocket连接已建立，开始接收实时日志...');
        try {
          socket.send(JSON.stringify({
            type: 'hello',
            taskId: taskId,
            timestamp: Date.now()
          }));
        } catch (e) {
          console.error('发送测试消息失败', e);
        }
      };
      
      socket.onmessage = (event) => {
        console.log('收到WebSocket消息:', event.data);
        
        // 尝试解析JSON消息
        try {
          const data = JSON.parse(event.data);
          
          // 处理不同类型的消息
          if (data.type === 'heartbeat') {
            // 心跳消息，不需要显示在日志中
            console.log('收到心跳消息:', data);
            return;
          } else if (data.type === 'log') {
            // 日志消息
            addLog(data.message || '');
            updateProgressFromLog(data.message || '');
          } else if (data.type === 'status') {
            // 状态消息
            const statusMessage = data.message === 'undefined' || data.message === undefined
              ? (data.status === 'running' ? '正在处理中...' : '')
              : data.message;
            
            addLog(`状态更新: ${data.status} - ${statusMessage}`);
            
            // 如果任务完成或失败，更新状态
            if (data.status === 'completed') {
              setProgress(100);
              setError(''); // 清除错误状态，因为任务已成功完成
              
              // 处理下载链接
              if (data.download_url) {
                const isDirectDownloadLink = data.download_url.startsWith('/download');
                const backendHost = API_BASE_URL.replace(/\/api$/, '');
                const fullDownloadUrl = data.download_url.startsWith('http')
                  ? data.download_url
                  : isDirectDownloadLink
                    ? `${backendHost}${data.download_url}`
                    : `${API_BASE_URL}${data.download_url.startsWith('/') ? '' : '/'}${data.download_url}`;
                
                setDownloadUrl(fullDownloadUrl);
                addLog(`处理完成！下载链接已生成`);
              }
              
              setIsLoading(false);
            } else if (data.status === 'failed' || data.status === 'not_found') {
              setError(statusMessage || '处理失败');
              addLog(`处理失败: ${statusMessage}`);
              setIsLoading(false);
            }
          } else if (data.type === 'task_update') {
            // 处理任务状态更新消息
            const task = data.task;
            if (task) {
              addLog(`任务状态更新: ${task.status}`);
              
              if (task.status === 'completed') {
                setProgress(100);
                setError(''); // 清除错误状态
                
                if (task.result && task.result.download_url) {
                  const isDirectDownloadLink = task.result.download_url.startsWith('/download');
                  const backendHost = API_BASE_URL.replace(/\/api$/, '');
                  const fullDownloadUrl = task.result.download_url.startsWith('http')
                    ? task.result.download_url
                    : isDirectDownloadLink
                      ? `${backendHost}${task.result.download_url}`
                      : `${API_BASE_URL}${task.result.download_url.startsWith('/') ? '' : '/'}${task.result.download_url}`;
                  
                  setDownloadUrl(fullDownloadUrl);
                  addLog(`处理完成！下载链接已生成`);
                } else {
                  addLog(`处理完成！但未找到下载链接`);
                }
                
                setIsLoading(false);
              } else if (task.status === 'failed') {
                setError(task.error || '处理失败');
                addLog(`处理失败: ${task.error || '未知错误'}`);
                setIsLoading(false);
              }
            }
          } else {
            // 其他类型的消息，直接显示
            addLog(event.data);
            updateProgressFromLog(event.data);
          }
        } catch (e) {
          // 不是JSON格式，直接显示原始消息
          addLog(event.data);
          updateProgressFromLog(event.data);
        }
      };
      
      socket.onclose = (event) => {
        console.log('WebSocket连接已关闭', event);
        addLog(`WebSocket连接已关闭. ${event.wasClean ? '正常关闭' : '异常关闭'} 代码: ${event.code}`);
        
        // 如果是异常关闭，并且任务仍在处理中，尝试重新获取任务状态
        if (!event.wasClean && isLoading) {
          console.log('WebSocket异常关闭，尝试重新获取任务状态');
          addLog('连接中断，尝试重新获取任务状态...');
          
          // 设置一个短暂的延迟，然后尝试获取任务状态
          setTimeout(() => {
            checkTaskStatus(taskId);
          }, 1000);
        }
      };
      
      socket.onerror = (error) => {
        console.error('WebSocket错误:', error);
        addLog(`WebSocket错误: ${error}`);
      };
      
      const pingInterval = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          try {
            socket.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
            console.log('发送心跳包');
          } catch (e) {
            console.error('发送心跳包失败', e);
          }
        }
      }, 30000);
      
      const checkStatusAndOutput = async () => {
        try {
          console.log('正在获取任务状态:', taskId);
          const taskStatus = await fetch(`/api/feishu/tasks/${taskId}`)
            .then(response => {
              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
              }
              return response.json();
            });
          
          console.log('任务状态:', taskStatus);
          
          // 优先使用API下载URL (如果存在)
          if (taskStatus.api_download_url) {
            console.log('发现API下载链接:', taskStatus.api_download_url);
            const apiDownloadUrl = `${API_BASE_URL.replace(/\/api$/, '')}${taskStatus.api_download_url}`;
            setDownloadUrl(apiDownloadUrl);
            console.log('构建的API下载链接:', apiDownloadUrl);
          }
          // 否则使用常规下载URL
          else if (taskStatus.download_url) {
            console.log('发现下载链接:', taskStatus.download_url);
            const isDirectDownloadLink = taskStatus.download_url.startsWith('/download');
            
            // 为直接下载链接使用固定的后端地址
            const backendHost = API_BASE_URL.replace(/\/api$/, '');
            
            const fullDownloadUrl = taskStatus.download_url.startsWith('http') 
              ? taskStatus.download_url 
              : isDirectDownloadLink
                ? `${backendHost}${taskStatus.download_url}`
                : `${API_BASE_URL}${taskStatus.download_url.startsWith('/') ? '' : '/'}${taskStatus.download_url}`;
            
            setDownloadUrl(fullDownloadUrl);
            console.log('构建的完整下载链接:', fullDownloadUrl);
          }
          
          if (taskStatus.status === 'completed' || taskStatus.status === 'failed' || taskStatus.status === 'not_found') {
            if (socket.readyState === WebSocket.OPEN) {
              socket.close();
            }
            if (taskStatus.status === 'completed') {
              // 清除错误状态，因为任务已成功完成
              setError('');
              // 确保下载链接被正确设置
              if (taskStatus.download_url) {
                const isDirectDownloadLink = taskStatus.download_url.startsWith('/download');
                const backendHost = API_BASE_URL.replace(/\/api$/, '');
                const fullDownloadUrl = taskStatus.download_url.startsWith('http')
                  ? taskStatus.download_url
                  : isDirectDownloadLink
                    ? `${backendHost}${taskStatus.download_url}`
                    : `${API_BASE_URL}${taskStatus.download_url.startsWith('/') ? '' : '/'}${taskStatus.download_url}`;
                
                setDownloadUrl(fullDownloadUrl);
                console.log('设置下载链接:', fullDownloadUrl);
                addLog(`处理完成！下载链接已生成`);
              } else {
                addLog(`处理完成！下载链接: ${taskStatus.download_url || '无'}`);
              }
              setIsLoading(false);
            } else if (taskStatus.status === 'not_found') {
              addLog(`任务不存在或已过期，请重新提交处理请求`);
              setError('任务不存在或已过期，请重新提交处理请求');
              setIsLoading(false);
            } else {
              addLog(`处理失败：${taskStatus.message}`);
              setError(taskStatus.message || '处理失败');
              setIsLoading(false);
            }
            return false;
          }
          
          addLog(`当前状态: ${taskStatus.status} - ${taskStatus.message}`);
          return true;
        } catch (error) {
          console.error('获取任务状态失败:', error);
          addLog(`获取任务状态失败: ${error.message || '未知错误'}`);
          setError(`获取任务状态失败: ${error.message || '未知错误'}`);
          setIsLoading(false);
          return false;
        }
      };
      
      // 声明在外部，以便清理函数可以访问
      let checkCount = 0;
      const maxChecks = 60; // 增加到60次，约3分钟
      let checkInterval: NodeJS.Timeout | null = null;
      
      // 初始检查
      checkStatusAndOutput().then(initialCheck => {
        if (!initialCheck) {
          // 如果初始检查就返回false，说明任务已完成或失败，不需要设置定时器
          return;
        }
        
        // 设置检查间隔
        checkInterval = setInterval(async () => {
          checkCount++;
          const shouldContinue = await checkStatusAndOutput();
          
          // 只有当任务确认完成或失败时才停止检查
          if (!shouldContinue) {
            if (checkInterval) {
              clearInterval(checkInterval);
              checkInterval = null;
            }
            clearInterval(pingInterval);
          }
          // 如果达到最大检查次数但任务仍在进行，只提示用户但继续检查
          else if (checkCount === maxChecks) {
            addLog(`已等待较长时间（约${Math.round(maxChecks * 3 / 60)}分钟），任务仍在后台处理中，请继续等待...`);
            
            // 减少检查频率，从3秒一次改为10秒一次，减轻服务器负担
            if (checkInterval) {
              clearInterval(checkInterval);
              checkInterval = setInterval(async () => {
                checkCount++;
                const stillContinue = await checkStatusAndOutput();
                if (!stillContinue) {
                  clearInterval(checkInterval);
                  checkInterval = null;
                  clearInterval(pingInterval);
                }
              }, 10000); // 10秒检查一次
            }
          }
        }, 3000);
      });
      
      return () => {
        if (checkInterval) {
          clearInterval(checkInterval);
          checkInterval = null;
        }
        clearInterval(pingInterval);
        if (socket.readyState === WebSocket.OPEN) {
          socket.close();
        }
        wsRef.current = null;
      };
    }
  }, [taskId, router]);
  
  const addLog = (message: string) => {
    setLogs((prevLogs) => [...prevLogs, message]);
  };
  
  const updateProgressFromLog = (logText: string) => {
    // 如果日志中包含"处理完成"，设置进度为100%
    if (logText.includes('处理完成') || logText.includes('生成文件')) {
      setProgress(100);
      return;
    }
    
    // 检测"文件可下载"消息，并处理下载链接
    if (logText.includes('文件可下载:') || logText.includes('处理完成，生成文件:')) {
      setProgress(100);
      setError(''); // 清除错误状态，因为处理已成功
      
      // 提取下载链接
      const downloadPathMatch = /文件可下载: (\/download\/[^\s]+)/.exec(logText);
      if (downloadPathMatch && downloadPathMatch[1]) {
        const downloadPath = downloadPathMatch[1];
        const backendHost = API_BASE_URL.replace(/\/api$/, '');
        const fullDownloadUrl = `${backendHost}${downloadPath}`;
        
        console.log('检测到下载链接:', fullDownloadUrl);
        
        // 设置下载链接
        setDownloadUrl(fullDownloadUrl);
        addLog(`处理完成！下载链接已生成`);
        
        // 清除之前的重试定时器
        if (retryTimerId) {
          clearTimeout(retryTimerId);
          setRetryTimerId(null);
        }
        
        // 重置重试计数
        setRetryCount(0);
        
        // 设置重试定时器，确保任务状态被正确更新
        const timerId = setTimeout(() => {
          checkTaskStatus(taskId);
        }, 1000);
        
        setRetryTimerId(timerId);
      }
      return;
    }
    
    // 检测处理成功的消息
    if (logText.includes('处理完成！') || logText.includes('处理成功')) {
      setProgress(100);
      setError(''); // 清除错误状态，因为处理已成功
      return;
    }
    
    // 检测错误信息
    if (logText.includes('命令执行失败') || logText.includes('退出码:')) {
      // 提取退出码信息
      const exitCodeMatch = logText.match(/退出码: (\d+)/);
      let errorMessage = '处理失败，请稍后重试';
      
      if (exitCodeMatch && exitCodeMatch[1]) {
        const exitCode = parseInt(exitCodeMatch[1]);
        if (exitCode === 3221225786) {
          errorMessage = '文档处理过程中出现内存错误，可能是由于文档过大或图片过多导致。请尝试分割文档或减少图片数量后重试。';
        }
      }
      
      setError(errorMessage);
      setIsLoading(false);
      return;
    }
    
    // 检测任务不存在或已过期的消息
    if (logText.includes('任务不存在或已过期')) {
      setError('任务不存在或已过期，请重新提交处理请求');
      setIsLoading(false);
      return;
    }
    
    // 如果进度为0且正在处理中，设置为50%
    if (progress === 0 && isLoading && (logText.includes('开始处理') || logText.includes('正在处理'))) {
      setProgress(50);
      return;
    }
    
    // 尝试从日志文本中解析进度信息
    const progressMatch = /进度: \[([\d]+)\/([\d]+)\]/.exec(logText);
    if (progressMatch) {
      const current = parseInt(progressMatch[1]);
      const total = parseInt(progressMatch[2]);
      const progressPercent = Math.round((current / total) * 100);
      
      // 更新进度条
      setProgress(progressPercent);
      
      // 每10%的进度或每5张图片添加一条进度提示
      if (current % 5 === 0 || progressPercent % 10 === 0 || current === total) {
        // 避免重复添加相同的进度日志
        const progressLog = `处理进度: ${progressPercent}% (${current}/${total})`;
        setLogs(prevLogs => {
          // 检查最后几条日志是否已经包含相同的进度信息
          const lastLogs = prevLogs.slice(-3);
          if (!lastLogs.some(log => log.includes(progressLog))) {
            return [...prevLogs, progressLog];
          }
          return prevLogs;
        });
      }
    }
  };
  
  // 添加检查任务状态的函数
  const checkTaskStatus = async (taskId: string) => {
    if (!taskId || retryCount >= maxRetries) {
      // 如果达到最大重试次数，显示友好的错误信息
      if (retryCount >= maxRetries) {
        setError('获取任务状态失败，可能是由于文档过大或处理时间过长导致。请尝试分割文档或减少图片数量后重试。');
      }
      return;
    }
    
    try {
      console.log(`正在重试获取任务状态 (${retryCount + 1}/${maxRetries}):`, taskId);
      const response = await fetch(`/api/feishu/tasks/${taskId}`);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const taskStatus = await response.json();
      console.log('重试获取任务状态结果:', taskStatus);
      
      // 处理任务状态
      if (taskStatus.status === 'completed' && taskStatus.download_url) {
        // 清除错误状态，因为任务已成功完成
        setError('');
        // 处理下载链接
        const isDirectDownloadLink = taskStatus.download_url.startsWith('/download');
        const backendHost = API_BASE_URL.replace(/\/api$/, '');
        const fullDownloadUrl = taskStatus.download_url.startsWith('http')
          ? taskStatus.download_url
          : isDirectDownloadLink
            ? `${backendHost}${taskStatus.download_url}`
            : `${API_BASE_URL}${taskStatus.download_url.startsWith('/') ? '' : '/'}${taskStatus.download_url}`;
        
        setDownloadUrl(fullDownloadUrl);
        console.log('重试设置下载链接:', fullDownloadUrl);
        setIsLoading(false);
        addLog(`处理完成！下载链接已生成`);
        setProgress(100);
      } else if (taskStatus.status === 'failed' || taskStatus.status === 'not_found') {
        // 处理失败情况
        let errorMessage = taskStatus.message || '处理失败';
        
        // 检查是否包含退出码信息
        const exitCodeMatch = errorMessage.match(/退出码: (\d+)/);
        if (exitCodeMatch && exitCodeMatch[1]) {
          const exitCode = parseInt(exitCodeMatch[1]);
          if (exitCode === 3221225786) {
            errorMessage = '文档处理过程中出现内存错误，可能是由于文档过大或图片过多导致。请尝试分割文档或减少图片数量后重试。';
          }
        }
        
        setError(errorMessage);
        addLog(`处理失败: ${taskStatus.message || '未知错误'}`);
        setIsLoading(false);
        
        // 不再继续重试
        return;
      }
      
      // 增加重试计数
      setRetryCount(prev => prev + 1);
      
      // 如果还需要继续重试
      if (retryCount < maxRetries - 1) {
        const timerId = setTimeout(() => {
          checkTaskStatus(taskId);
        }, 1000);
        
        setRetryTimerId(timerId);
      }
    } catch (error) {
      console.error('重试获取任务状态失败:', error);
      
      // 增加重试计数
      setRetryCount(prev => prev + 1);
      
      // 如果还需要继续重试
      if (retryCount < maxRetries - 1) {
        const timerId = setTimeout(() => {
          checkTaskStatus(taskId);
        }, 1000);
        
        setRetryTimerId(timerId);
      } else {
        // 达到最大重试次数，显示友好的错误信息
        setError('获取任务状态失败，可能是由于网络问题或服务器错误。请稍后重试。');
        setIsLoading(false);
      }
    }
  };
  
  // 验证飞书URL格式
  const validateFeishuUrl = (url: string): boolean => {
    if (!url) return false;
    
    try {
      // 检查是否包含.feishu.cn或larksuite.com域名
      const isFeishuDomain = url.includes('.feishu.cn') || url.includes('.larksuite.com');
      
      // 检查是否包含/wiki/路径（允许）
      const hasWikiPath = url.includes('/wiki/');
      
      // 检查是否包含/docx/路径（不允许）
      const hasDocxPath = url.includes('/docx/');
      
      if (!isFeishuDomain) {
        setFeishuUrlError('请输入有效的飞书文档链接，必须包含.feishu.cn或.larksuite.com域名');
        return false;
      }
      
      if (hasDocxPath) {
        setFeishuUrlError('不支持飞书文档（/docx/），请使用飞书知识库文档（/wiki/）');
        return false;
      }
      
      if (!hasWikiPath) {
        setFeishuUrlError('请使用飞书知识库文档链接，URL应包含/wiki/路径');
        return false;
      }
      
      setFeishuUrlError('');
      return true;
    } catch (error) {
      console.error('飞书URL验证错误:', error);
      setFeishuUrlError('URL格式无效，请检查');
      return false;
    }
  };
  
  const handleFormSubmit = async () => {
    // 不再需要阻止默认行为，因为现在是按钮点击事件
    // 也不再需要检查预览模态框是否刚刚关闭，因为只有手动点击按钮才会触发
    
    setIsLoading(true);
    setError('');
    setLogs([]);
    setShowLogs(false);
    setDownloadUrl('');
    setProgress(0);
    setRetryCount(0); // 重置重试计数
    
    // 清除之前的重试定时器
    if (retryTimerId) {
      clearTimeout(retryTimerId);
      setRetryTimerId(null);
    }
    
    try {
      let result;
      if (activeTab === 'feishu') {
        if (!feishuUrl) {
          throw new Error('请输入飞书文档链接');
        }
        
        // 添加飞书URL验证
        if (!validateFeishuUrl(feishuUrl)) {
          setIsLoading(false);
          throw new Error(feishuUrlError || '飞书文档链接格式不正确');
        }
        
        // 收集所有表单参数
        const formParams = {
          replace_images: true,
          flush: flush === 'yes', // 将flush值传递给后端
          // 传递工作流参数给后端
          workflow_params: workflowData,
          // 添加页面中的所有表单数据
          form_data: {
            url: feishuUrl, // format-prd接口需要url参数
            feishu_url: feishuUrl, // format-prd接口需要feishu_url参数
            flush: flush,
            name: name,
            description: description,
            active_tab: activeTab
          }
        };
        
        result = await documentApi.processFeishuDocumentV2(feishuUrl, formParams);
        setTaskId(result.task_id);
        setShowLogs(true);
        addLog(`开始处理飞书文档: ${feishuUrl}`);
        addLog(`任务ID: ${result.task_id}`);
        // 设置初始进度为50%
        setProgress(50);
      } else if (activeTab === 'upload') {
        if (!selectedFile) {
          throw new Error('请选择要上传的文件');
        }
        const formData = new FormData();
        formData.append('file', selectedFile);
        const uploadResult = await axios.post(`/api/documents/upload`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
        const fileId = uploadResult.data.file_id;
        result = await axios.post(`/api/documents/process/pdf/${fileId}`, {
          replace_images: true,
          output_format: 'markdown',
          name: name || `${selectedFile.name}-处理`,
          description: description || '由PDF文档自动生成',
          // 添加页面中的所有表单数据
          form_data: {
            file_name: selectedFile.name,
            flush: flush,
            name: name,
            description: description,
            active_tab: activeTab
          }
        }, { headers: { 'Content-Type': 'application/json' } });
        addLog(`文件上传和处理任务已提交，任务ID: ${result.data.task_id}`);
        setTaskId(result.data.task_id);
        setShowLogs(true);
        // 设置初始进度为50%
        setProgress(50);
      }
    } catch (error) {
      console.error('提交失败:', error);
      const errorMessage = error.response?.data?.detail || error.message || '提交失败，请重试';
      setError(errorMessage);
      setIsLoading(false);
      setShowLogs(false);
    }
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };
  
  const handleCopyLogs = () => {
    const logText = logs.join('\n');
    
    try {
      // 尝试使用现代Clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(logText)
          .then(() => {
            console.log('Logs copied to clipboard');
            toast({
              title: "日志已复制",
              description: "日志内容已成功复制到剪贴板。",
              status: "success",
              duration: 3000,
              isClosable: true,
            });
          })
          .catch(err => {
            console.error('使用Clipboard API复制失败: ', err);
            // 使用回退方法
            fallbackCopyTextToClipboard(logText);
          });
      } else {
        // 浏览器不支持Clipboard API，使用回退方法
        fallbackCopyTextToClipboard(logText);
      }
    } catch (err) {
      console.error('复制过程中出错: ', err);
      // 使用回退方法
      fallbackCopyTextToClipboard(logText);
    }
    
    // 回退复制方法
    function fallbackCopyTextToClipboard(text) {
      try {
        // 创建临时文本区域
        const textArea = document.createElement('textarea');
        textArea.value = text;
        
        // 避免滚动到底部
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        // 尝试复制文本
        const successful = document.execCommand('copy');
        
        // 移除临时元素
        document.body.removeChild(textArea);
        
        if (successful) {
          toast({
            title: "日志已复制",
            description: "日志内容已成功复制到剪贴板。",
            status: "success",
            duration: 3000,
            isClosable: true,
          });
        } else {
          throw new Error('复制命令执行失败');
        }
      } catch (err) {
        console.error('回退复制方法失败: ', err);
        toast({
          title: "复制失败",
          description: "无法将日志复制到剪贴板，请手动复制",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      }
    }
  };
  
  return (
    <MainLayout>
      <div className="flex justify-center min-h-screen">
        <div className="flex max-w-6xl w-full">
          {/* 左侧进度菜单 */}
          <WorkflowSidebar className="w-64 flex-shrink-0" />
          
          {/* 主内容区域 */}
          <div className="flex-1">
            <div className="p-6">
              <h1 className="text-3xl font-bold mb-8 border-none">生成格式化PRD</h1>
              <div className="flex border-b mb-6">
                <button
                  type="button"
                  className={`px-4 py-2 font-medium ${activeTab === 'feishu' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-green-600'}`}
                  onClick={() => setActiveTab('feishu')}
                >
                  飞书文档链接
                </button>
                <button
                  type="button"
                  className="px-4 py-2 font-medium text-gray-400 cursor-not-allowed"
                  onClick={() => {
                    toast({
                      title: "暂不支持",
                      description: "上传功能暂不支持，请使用飞书文档链接",
                      status: "warning",
                      duration: 3000,
                      isClosable: true,
                    });
                  }}
                >
                  上传PRD文档
                </button>
              </div>
              <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-1">格式化PRD文档处理</h2>
              <p className="text-gray-600 text-sm">通过PRD文档自动生成格式化的PRD文档</p>
            </div>
            {error && (
              <Alert status="error" mb={4} borderRadius="md" variant="subtle">
                <AlertIcon />
                <Box flex="1">
                  <AlertTitle fontWeight="semibold">发生错误:</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Box>
                <Button
                  colorScheme="red"
                  size="sm"
                  ml={2}
                  onClick={handleFormSubmit}
                  isDisabled={isLoading}
                >
                  重试
                </Button>
              </Alert>
            )}
            <form onSubmit={(e) => e.preventDefault()}>
              {activeTab === 'feishu' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="feishu-url">
                    *<span className="font-bold">飞书文档链接</span> <span className="text-red-400 font-normal">(必填)</span>
                  </label>
                  <Input
                    id="feishu-url"
                    name="url"
                    type="text"
                    value={feishuUrl}
                    onChange={(e) => {
                      setFeishuUrl(e.target.value);
                      if (feishuUrlError) validateFeishuUrl(e.target.value);
                    }}
                    placeholder="粘贴飞书文档分享链接"
                    required
                    isDisabled={isLoading}
                    width="100%"
                    px={3}
                    py={2}
                    borderColor={feishuUrlError ? "red.300" : "gray.300"}
                    borderRadius="md"
                    boxShadow="sm"
                    _focus={{ outline: "none", ring: "2px", ringColor: feishuUrlError ? "red.500" : "green.500", borderColor: feishuUrlError ? "red.500" : "green.500" }}
                  />
                  {feishuUrlError ? (
                    <p className="text-xs text-red-500 mt-1">{feishuUrlError}</p>
                  ) : (
                    <p className="text-xs text-gray-500 mt-1">
                      请确保文档已设置为可访问状态。仅支持飞书知识库文档(格式: https://组织或个人标识.feishu.cn/<span className="text-red-400 font-normal">wiki/</span>文档token)
                      <br />
                      示例URL：https://wcng60ba718p.feishu.cn/wiki/S3C9wkR7NinQM2kfcKfck9uwnLe
                    </p>
                  )}
                  
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      *<span className="font-bold">重新生成</span> <span className="text-red-400 font-normal">(必填)</span>
                    </label>
                    <div className="flex space-x-4">
                      <div className="flex items-center">
                        <input
                          id="flush-yes"
                          name="flush"
                          type="radio"
                          value="yes"
                          checked={flush === 'yes'}
                          onChange={() => setFlush('yes')}
                          disabled={isLoading}
                          className="h-4 w-4 text-green-600 border-gray-300 focus:ring-green-500"
                        />
                        <label htmlFor="flush-yes" className="ml-2 block text-sm text-gray-700">
                          是
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="flush-no"
                          name="flush"
                          type="radio"
                          value="no"
                          checked={flush === 'no'}
                          onChange={() => setFlush('no')}
                          disabled={isLoading}
                          className="h-4 w-4 text-green-600 border-gray-300 focus:ring-green-500"
                        />
                        <label htmlFor="flush-no" className="ml-2 block text-sm text-gray-700">
                          否
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}
              {activeTab === 'upload' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="file">
                    上传PRD文档
                  </label>
                  <div className="border border-gray-300 rounded-md p-3">
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                      <div className="mt-4 flex text-sm justify-center leading-6 text-gray-600">
                        <label
                          htmlFor="file-upload"
                          className={`relative cursor-pointer rounded-md bg-white font-semibold text-blue-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 hover:text-blue-500 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          <span>上传文件</span>
                          <input
                            id="file-upload"
                            name="file"
                            type="file"
                            accept=".pdf,.md,.docx"
                            className="sr-only"
                            onChange={handleFileChange}
                            required={activeTab === 'upload'}
                            disabled={isLoading}
                          />
                        </label>
                        <p className="pl-1">或拖拽到此处</p>
                      </div>
                      <p className="text-xs leading-5 text-gray-600 mt-2">支持 PDF, Markdown, DOCX (最大20MB)</p>
                      {selectedFile && (
                        <div className="mt-4 text-sm text-gray-700">
                          已选择: {selectedFile.name}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="name">
                  工作流名称 <span className="text-gray-400 font-normal">(选填)</span>
                </label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="为此次处理命名，便于后续查找"
                  isDisabled={isLoading}
                  width="100%"
                  px={3}
                  py={2}
                  borderColor="gray.300"
                  borderRadius="md"
                  boxShadow="sm"
                  _focus={{ outline: "none", ring: "2px", ringColor: "green.500", borderColor: "green.500" }}
                />
              </div>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="description">
                  描述 <span className="text-gray-400 font-normal">(选填)</span>
                </label>
                <Input
                  id="description"
                  name="description"
                  type="text"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="添加描述信息"
                  isDisabled={isLoading}
                  width="100%"
                  px={3}
                  py={2}
                  borderColor="gray.300"
                  borderRadius="md"
                  boxShadow="sm"
                  _focus={{ outline: "none", ring: "2px", ringColor: "green.500", borderColor: "green.500" }}
                />
              </div>
              {showLogs && (
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-md font-medium">处理进度</h3>
                    <Button
                      size="xs"
                      leftIcon={<CopyIcon />}
                      onClick={handleCopyLogs}
                      colorScheme="gray"
                      variant="outline"
                    >
                      复制日志
                    </Button>
                  </div>
                  <Progress 
                    value={progress} 
                    size="sm" 
                    colorScheme="green"
                    borderRadius="md" 
                    mb={2}
                  />
                  <p className="text-xs text-gray-500 mb-2">如果文档图片较多，处理时间{'>'}10分钟，请耐心等待。</p>
                  <div className="h-60 bg-gray-100 border border-gray-300 rounded-md p-3 overflow-y-auto font-mono text-sm">
                    {logs.map((log, index) => (
                      <div key={index} className="py-1 whitespace-pre-wrap break-all">
                        {log}
                      </div>
                    ))}
                    <div ref={logEndRef} />
                  </div>
                  {downloadUrl && (
                    <div className="mt-6 flex flex-wrap gap-2 items-center">
                      <p className="w-full text-green-600 font-semibold mb-2">✅ 处理完成！您可以：</p>
                      <a
                        href={downloadUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-green-600 hover:text-green-800 hover:underline flex items-center"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        下载结果
                      </a>
                      <button
                        onClick={() => setIsPreviewOpen(true)}
                        className="text-green-600 hover:text-green-800 hover:underline flex items-center"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        预览
                      </button>
                      <a
                        href={`/workflow/preview-md?url=${encodeURIComponent(downloadUrl)}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-purple-600 hover:text-purple-800 hover:underline flex items-center"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                        </svg>
                        新窗口预览
                      </a>
                      
                      <Button
                        size="sm"
                        leftIcon={<CopyIcon />}
                        onClick={() => {
                          try {
                            // 尝试使用现代Clipboard API
                            if (navigator.clipboard && navigator.clipboard.writeText) {
                              navigator.clipboard.writeText(downloadUrl)
                                .then(() => {
                                  toast({
                                    title: "链接已复制",
                                    description: "文档链接已成功复制到剪贴板",
                                    status: "success",
                                    duration: 3000,
                                    isClosable: true,
                                  });
                                })
                                .catch(err => {
                                  console.error('使用Clipboard API复制失败: ', err);
                                  // 使用回退方法
                                  fallbackCopyTextToClipboard(downloadUrl);
                                });
                            } else {
                              // 浏览器不支持Clipboard API，使用回退方法
                              fallbackCopyTextToClipboard(downloadUrl);
                            }
                          } catch (err) {
                            console.error('复制过程中出错: ', err);
                            // 使用回退方法
                            fallbackCopyTextToClipboard(downloadUrl);
                          }

                          // 回退复制方法
                          function fallbackCopyTextToClipboard(text) {
                            try {
                              // 创建临时文本区域
                              const textArea = document.createElement('textarea');
                              textArea.value = text;
                              
                              // 避免滚动到底部
                              textArea.style.top = '0';
                              textArea.style.left = '0';
                              textArea.style.position = 'fixed';
                              textArea.style.opacity = '0';
                              
                              document.body.appendChild(textArea);
                              textArea.focus();
                              textArea.select();
                              
                              // 尝试复制文本
                              const successful = document.execCommand('copy');
                              
                              // 移除临时元素
                              document.body.removeChild(textArea);
                              
                              if (successful) {
                                toast({
                                  title: "链接已复制",
                                  description: "文档链接已成功复制到剪贴板",
                                  status: "success",
                                  duration: 3000,
                                  isClosable: true,
                                });
                              } else {
                                throw new Error('复制命令执行失败');
                              }
                            } catch (err) {
                              console.error('回退复制方法失败: ', err);
                              toast({
                                title: "复制失败",
                                description: "无法将链接复制到剪贴板，请手动复制",
                                status: "error",
                                duration: 3000,
                                isClosable: true,
                              });
                            }
                          }
                        }}
                        colorScheme="gray"
                        variant="outline"
                      >
                        复制链接
                      </Button>
                      
                      <Button
                        onClick={() => {
                          // 使用GET方式传递参数
                          const params = new URLSearchParams();
                          params.set('feishu_url', feishuUrl);
                          params.set('format_prd_url', downloadUrl);
                          
                          // 保持debug参数
                          if (typeof window !== 'undefined') {
                            const currentParams = new URLSearchParams(window.location.search);
                            const debugParam = currentParams.get('debug');
                            if (debugParam) {
                              params.set('debug', debugParam);
                            }
                          }
                          
                          window.location.href = `/workflow/ai-prd?${params.toString()}`;
                        }}
                        colorScheme="green"
                        size="sm"
                        className="bg-green-600 hover:bg-green-700"
                      >
                        进行AI-PRD生成 →
                      </Button>
                    </div>
                  )}
                </div>
              )}
              <div className="flex justify-between items-center mt-8">
                <button
                    type="button"
                    onClick={() => {
                        // 确保清理所有定时器和连接
                        if (retryTimerId) {
                            clearTimeout(retryTimerId);
                            setRetryTimerId(null);
                        }
                        
                        if (wsRef.current) {
                            wsRef.current.close();
                            wsRef.current = null;
                        }
                        
                        // 重置所有状态
                        setActiveTab('feishu');
                        setIsLoading(false);
                        setFeishuUrl('');
                        setFlush('no'); // 重置重新生成选项为默认值"否"
                        setName('');
                        setDescription('');
                        setSelectedFile(null);
                        setError('');
                        setTaskId('');
                        setLogs([]);
                        setShowLogs(false);
                        setProgress(0);
                        setDownloadUrl('');
                        setRetryCount(0);
                    }}
                    className={`px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={isLoading}
                >
                  取消/重置
                </button>
                <button
                  type="button"
                  onClick={handleFormSubmit}
                  disabled={isLoading || (activeTab === 'feishu' && !feishuUrl) || (activeTab === 'upload' && !selectedFile)}
                  className={`px-6 py-2 rounded-md text-white transition-colors duration-150 ${isLoading || (activeTab === 'feishu' && !feishuUrl) || (activeTab === 'upload' && !selectedFile) ? 'bg-green-300 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700 focus:ring-4 focus:ring-green-300'}`}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      处理中...
                    </div>
                  ) : '开始处理'}
                </button>
              </div>
              </form>
              
              {/* Markdown 预览模态框 */}
              <MarkdownPreviewModal
                isOpen={isPreviewOpen}
                onClose={() => {
                  setIsPreviewOpen(false);
                  setIsPreviewJustClosed(true);
                  // 500ms后重置状态，防止长时间阻止表单提交
                  setTimeout(() => {
                    setIsPreviewJustClosed(false);
                  }, 500);
                }}
                downloadUrl={downloadUrl}
                title="Markdown 文档预览"
              />
            </div>
          </div>
        </div>
        
        </div>
        
        {/* 调试面板 - 通过URL参数debug=1控制显示 */}
        <WorkflowDebugPanel pageName="format-prd" />
      </div>
    </MainLayout>
  );
}
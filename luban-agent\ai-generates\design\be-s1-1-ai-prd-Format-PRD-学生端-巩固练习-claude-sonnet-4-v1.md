# 产品需求文档 - 学生端巩固练习

**文档版本：** V1.0  
**创建日期：** 2025-05-24  
**创建人：** AI产品经理  
**原始需求文档：** Format-PRD-学生端-巩固练习.md  

## 1. 需求背景与目标

### 1.1 需求目标
- 通过巩固练习功能的实现，可以提升学生在AI课后的知识掌握程度和学习效果
- 该需求上线后，学生巩固练习完成率预计可以提升30%，知识掌握度平均提升35%
- 通过再练一次机制，确保每位学生都能达到掌握知识的目标，提升整体学习效果

## 2. 功能范围

### 2.1 核心功能
- **巩固练习入口管理**: 提供练习状态展示和入口引导，满足学生快速进入练习场景，解决练习入口不明确的痛点
  - 优先级：高
  - 依赖关系：无

- **智能题目推荐**: 基于AI课知识点和学生实时表现动态推荐题目，满足个性化练习需求，解决题目难度不匹配的痛点
  - 优先级：高
  - 依赖关系：依赖于巩固练习推题策略

- **练习过程管理**: 提供完整的练习流程控制和进度管理，满足连续练习体验需求，解决练习中断和进度丢失的痛点
  - 优先级：高
  - 依赖关系：依赖于巩固练习入口管理

- **情感化反馈系统**: 提供差异化的作答反馈和激励机制，满足学习动力维持需求，解决练习过程枯燥的痛点
  - 优先级：高
  - 依赖关系：依赖于练习过程管理

- **再练一次机制**: 基于掌握度判断推荐再次练习，满足知识巩固需求，解决一次练习无法完全掌握的痛点
  - 优先级：高
  - 依赖关系：依赖于学习报告生成

### 2.2 辅助功能
- **勾画工具**: 为练习过程提供标记和勾画支持，提升答题体验
- **错题本管理**: 自动收集错题并支持手动添加，为后续复习提供支持
- **学习报告**: 展示练习结果和掌握度变化，为学习效果评估提供数据支持
- **熔断机制**: 防止过度练习，保护学生学习体验

### 2.3 非本期功能
- **社交排行榜**: 学生间的练习排名对比，建议迭代2再考虑
- **练习积分商城**: 积分兑换奖励机制，建议迭代3再考虑
- **语音答题**: 口语类题目的语音作答，建议迭代4再考虑

## 3. 计算规则与公式

### 3.1 进度计算规则
- **进度计算公式**: 当前进度 = 已完成题目数 / 预估总题目数 × 100%
  - 参数说明：
    - 已完成题目数：学生实际作答的题目数量
    - 预估总题目数：推题策略预估的练习题目总数
  - 规则：
    - 答对题目：进度增加
    - 答错但无相似题：进度增加
    - 答错且有相似题需再练：进度不变

### 3.2 掌握度变化规则
- **掌握度计算**: 基于巩固练习推题策略中的掌握度算法
  - 参数说明：
    - 正确率：本次练习的答题正确率
    - 题目难度：题目的难度系数
    - 知识点权重：不同知识点的重要性权重
  - 规则：
    - 取值范围：0-100%
    - 提升幅度：根据正确率和题目难度动态计算

### 3.3 再练一次触发规则
- **触发条件**: 基于学生掌握度情况判断
  - 参数说明：
    - 当前掌握度：练习完成后的知识点掌握程度
    - 目标掌握度：系统设定的掌握标准
  - 规则：
    - 当前掌握度 < 目标掌握度时触发再练一次推荐

## 4. 用户场景与故事

### 场景1：学生首次进入巩固练习
作为一个刚完成AI课学习的学生，我希望能够立即进行相关的巩固练习，这样我就可以及时检验和巩固刚学到的知识。目前的痛点是不知道从哪里开始练习，如果能够有明确的练习入口和引导，对我的学习会有很大帮助。

**关键步骤：**
1. 学生在学科页面查看巩固练习卡片
2. 点击【进入练习】按钮
3. 系统展示"开始练习"转场动画
4. 进入第一道练习题目

```mermaid
flowchart TD
    A[学生完成AI课学习] --> B[查看学科页面]
    B --> C{是否存在巩固练习}
    C -->|是| D[显示巩固练习卡片]
    C -->|否| E[不显示练习入口]
    D --> F[学生点击进入练习]
    F --> G[系统展示转场动画]
    G --> H[加载第一道题目]
    H --> I[开始练习]
```

- 优先级：高
- 依赖关系：无

### 场景2：学生在练习过程中遇到困难
作为一个正在进行巩固练习的学生，我希望在遇到困难时能够得到适当的帮助和鼓励，这样我就可以保持学习动力继续完成练习。目前的痛点是答错题目时容易产生挫败感，如果能够有友好的反馈和适当的难度调整，对我的学习体验会有很大改善。

**关键步骤：**
1. 学生作答题目
2. 系统判断答案正确性
3. 根据结果提供相应反馈
4. 必要时调整后续题目难度

```mermaid
flowchart TD
    A[学生作答题目] --> B{答案是否正确}
    B -->|正确| C[显示正确反馈]
    B -->|错误| D[显示错误反馈]
    C --> E{是否连续正确}
    E -->|是| F[显示连胜反馈]
    E -->|否| G[显示普通正确反馈]
    D --> H[显示安慰性反馈]
    F --> I{是否需要难度调整}
    G --> I
    H --> I
    I -->|需要上升| J[显示难度上升反馈]
    I -->|需要下降| K[显示难度下降反馈]
    I -->|无需调整| L[进入下一题]
    J --> L
    K --> L
```

- 优先级：高
- 依赖关系：依赖于场景1

### 场景3：学生完成练习查看报告
作为一个完成巩固练习的学生，我希望能够看到详细的学习报告和成果展示，这样我就可以了解自己的学习效果和进步情况。目前的痛点是不知道练习的具体效果，如果能够有清晰的数据展示和成就反馈，对我的学习成就感会有很大提升。

**关键步骤：**
1. 学生完成最后一道题目
2. 系统生成学习报告
3. 展示练习成果和掌握度变化
4. 提供再练一次或完成选择

```mermaid
flowchart TD
    A[学生完成最后一题] --> B[系统计算练习结果]
    B --> C[生成学习报告]
    C --> D[展示练习数据]
    D --> E[显示掌握度变化]
    E --> F{是否需要再练一次}
    F -->|是| G[强化显示再练一次按钮]
    F -->|否| H[显示普通完成选项]
    G --> I[学生选择再练一次或完成]
    H --> I
    I --> J{学生选择}
    J -->|再练一次| K[进入再练一次流程]
    J -->|完成| L[返回课程详情页]
```

- 优先级：高
- 依赖关系：依赖于场景2

### 场景4：学生进行再练一次
作为一个掌握度还未达标的学生，我希望能够进行针对性的再次练习，这样我就可以进一步巩固薄弱的知识点。目前的痛点是不知道哪些知识点还需要加强，如果能够有针对性的再练推荐，对我的学习效果会有很大帮助。

**关键步骤：**
1. 学生在报告页点击【再练一次】
2. 系统基于掌握度分析推荐题目
3. 进入再练一次练习流程
4. 完成后更新整体学习报告

```mermaid
flowchart TD
    A[学生点击再练一次] --> B[系统分析薄弱知识点]
    B --> C[从题库中筛选相关题目]
    C --> D[展示再练一次转场]
    D --> E[开始再练题目]
    E --> F[按照正常练习流程进行]
    F --> G[完成再练一次]
    G --> H[更新累计学习报告]
    H --> I{是否还需要继续再练}
    I -->|是| J[再次推荐再练一次]
    I -->|否| K[完成全部练习]
```

- 优先级：中
- 依赖关系：依赖于场景3

## 5. 业务流程图

```mermaid
flowchart TD
    A[学生完成AI课] --> B[查看巩固练习入口]
    B --> C{练习状态}
    C -->|未开始| D[显示进入练习]
    C -->|进行中| E[显示继续练习]
    C -->|已完成| F[显示看报告/再练一次]
    
    D --> G[点击进入练习]
    E --> G
    F --> H{用户选择}
    H -->|看报告| I[查看学习报告]
    H -->|再练一次| J[进入再练流程]
    
    G --> K[进入练习转场]
    K --> L{是否有题组}
    L -->|是| M[题组转场]
    L -->|否| N[直接进入题目]
    M --> N
    
    N --> O[展示题目]
    O --> P[学生作答]
    P --> Q[系统批改]
    Q --> R[作答反馈]
    R --> S{是否最后一题}
    S -->|否| T[题目间转场]
    T --> O
    S -->|是| U[生成学习报告]
    
    U --> V{是否需要再练}
    V -->|是| W[推荐再练一次]
    V -->|否| X[完成练习]
    W --> J
    J --> K
    
    I --> Y[查看详细数据]
    X --> Z[返回课程页面]
    Y --> Z
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：题目加载的响应时间不超过2秒
- 并发用户：系统需支持1000个并发用户同时进行巩固练习，保证性能不衰减
- 数据量：系统需支持100万条练习记录的存储和查询，响应时间不超过3秒

### 6.2 安全需求
- 权限控制：巩固练习功能需要学生身份认证才能访问
- 数据加密：学生作答数据需要使用AES256算法进行加密存储和传输
- 操作审计：对练习作答行为需要记录操作日志，包括用户ID、题目ID、作答内容、作答时间

## 7. 验收标准

### 7.1 功能验收
- **巩固练习入口**：
  - 使用场景：当学生完成AI课后查看学科页面时
  - 期望结果：系统应显示巩固练习卡片，包含预估题目数量和正确的入口状态

- **智能题目推荐**：
  - 使用场景：当学生在练习过程中作答题目时
  - 期望结果：系统应根据作答表现动态调整后续题目难度，确保练习效果

- **情感化反馈**：
  - 使用场景：当学生完成题目作答后
  - 期望结果：系统应根据作答结果提供相应的反馈动画和文案，包含IP角色和音效

- **再练一次机制**：
  - 使用场景：当学生完成巩固练习且掌握度未达标时
  - 期望结果：系统应在报告页强化显示再练一次按钮，并推荐针对性题目

### 7.2 性能验收
- 响应时间：
  - 给定正常网络条件，当学生点击进入练习，题目加载时间不超过2秒
  - 给定正常网络条件，当学生提交答案，反馈展示时间不超过1秒
- 并发用户：
  - 给定1000个并发用户，模拟同时进行巩固练习，系统运行30分钟，无异常，平均响应时间不超过3秒

### 7.3 安全验收
- 权限控制：
  - 给定未登录状态，当用户访问巩固练习功能，返回身份认证错误
  - 给定已登录学生，当访问巩固练习功能，可以正常使用
- 数据加密：
  - 给定学生作答数据，当查询数据库存储内容，数据是加密状态，且使用AES256算法
- 操作审计：
  - 给定学生作答行为，当执行作答操作，操作日志正常记录，包含用户ID、题目ID、作答内容、作答时间

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：巩固练习的操作界面简洁友好，3步内可完成主要操作
- 容错处理：网络异常发生时，系统可以自动保存进度，并给出友好提示
- 兼容性：需支持主流移动设备和浏览器，保证界面和功能正常

### 8.2 维护性需求
- 配置管理：题目推荐参数可由系统管理员在后台界面进行配置和调整
- 监控告警：练习异常中断发生时，系统自动告警，通知相关技术人员
- 日志管理：系统自动记录练习行为日志，日志保存30天，可供系统管理员查询和分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 用户可以通过练习页面反馈按钮和客服渠道提交反馈
- 每周定期收集和分析用户反馈，识别改进点

### 9.2 迭代计划
- 迭代周期：每2周
- 每次迭代结束后，评估需求实现情况和用户反馈，调整下一个迭代的优先级和计划

## 10. 需求检查清单

使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
| ----------- | --------------- | ------ | ------ | ------ | -------- | -------- | ---- |
| 巩固练习入口状态管理 | 巩固练习入口管理 | ✅ | ✅ | ✅ | ✅ | ✅ | 包含未完成、已完成状态展示 |
| 进入练习转场设计 | 练习过程管理 | ✅ | ✅ | ✅ | ✅ | ✅ | 首次进入和继续练习转场 |
| 练习环节页面框架 | 练习过程管理 | ✅ | ✅ | ✅ | ✅ | ✅ | 包含导航栏、进度条、题型组件 |
| 题目推荐策略 | 智能题目推荐 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 依赖外部推题策略文档 |
| 作答反馈机制 | 情感化反馈系统 | ✅ | ✅ | ✅ | ✅ | ✅ | 正确、错误、连续正确反馈 |
| 难度变化反馈 | 情感化反馈系统 | ✅ | ✅ | ✅ | ✅ | ✅ | 难度上升、下降提示 |
| 不认真作答反馈 | 情感化反馈系统 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 需要明确判定规则 |
| 勾画组件功能 | 勾画工具 | ✅ | ✅ | ✅ | ✅ | ✅ | 颜色、粗细、橡皮擦等工具 |
| 错题本自动添加 | 错题本管理 | ✅ | ✅ | ✅ | ✅ | ✅ | 答错自动添加，正确可手动添加 |
| 错因标签功能 | 错题本管理 | ✅ | ✅ | ✅ | ✅ | ✅ | 系统预设标签选择 |
| 学习报告展示 | 学习报告 | ✅ | ✅ | ✅ | ✅ | ✅ | 掌握度变化、学习时长等数据 |
| 题目详情查看 | 学习报告 | ✅ | ✅ | ✅ | ✅ | ✅ | 支持查看题目、答案、解析 |
| 再练一次触发规则 | 再练一次机制 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 基于掌握度策略判断 |
| 再练一次内容生成 | 再练一次机制 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 根据薄弱知识点推荐题目 |
| 熔断机制 | 熔断机制 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 防止过度练习的保护机制 |
| 题组转场功能 | 练习过程管理 | ✅ | ✅ | ✅ | ✅ | ✅ | 多题组时的转场展示 |
| 进度计算规则 | 计算规则与公式 | ✅ | ✅ | ✅ | ✅ | ✅ | 基于作答情况动态计算 |
| 中途退出保存 | 练习过程管理 | ✅ | ✅ | ✅ | ✅ | ✅ | 自动保存进度和状态 |

- 完整性：原始需求是否都有对应的优化后需求点，无遗漏
- 正确性：优化后需求描述是否准确表达了原始需求的意图
- 一致性：优化后需求之间是否有冲突或重复
- 可验证性：优化后需求是否有明确的验收标准
- 可跟踪性：优化后需求是否有明确的优先级和依赖关系

✅表示通过；❌表示未通过；⚠️表示描述正确，但细节不完整（如计算规则、阈值等，需要和PM进一步沟通）；N/A表示不适用。

## 11. 附录

### 11.1 原型图
参考原始需求文档中的Figma设计稿：[Figma链接](https://www.figma.com/design/COdZQZZOFHZHsdI1zWX8Tm/%E5%AD%A6%E7%94%9F%E7%AB%AF-1.0?node-id=1805-6218&p=f&m=dev)

### 11.2 术语表
- **巩固练习**: 在学生掌握新知识或技能后，借助有针对性的练习加深理解、强化记忆的学习活动
- **再练一次**: 基于学生掌握度情况推荐的额外练习环节，确保知识点充分掌握
- **掌握度**: 学生对特定知识点的掌握程度，用百分比表示
- **熔断机制**: 防止学生过度练习的保护机制，避免学习疲劳
- **题组**: 围绕特定知识点组织的题目集合
- **IP角色**: 产品中的卡通形象，用于情感化反馈

### 11.3 参考资料
- [巩固练习相关策略](https://wcng60ba718p.feishu.cn/docx/JYjedBulhoHiBDx1wGScCacHnrd)
- [PRD - 内容管理平台2.0 - 选题支持题组](https://wcng60ba718p.feishu.cn/wiki/C91Gwf402i2iU5kMs1vcPnICnDc)
- [PRD - 题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)
- [教师端_1期_备课 & 作业布置](https://wcng60ba718p.feishu.cn/wiki/G6OTwydxOi8gdmkYTKUcmQmHngd)

--- 等待您的命令，指挥官
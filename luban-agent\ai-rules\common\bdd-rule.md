---
description: TDD & BDD 和单元测试规范，适用于所有测试相关文件和实现文件。
globs: test-cases/**/*.tc.md, test-case/**/*.v*.tc.md
---
# Project TDD & BDD Rules

## 测试用例管理规范
- 文件组织
  - 存放目录：ai-generates/test-case/
  - 文件后缀：.tc.md
  - 按业务模块组织子目录
  - 命名格式：`[模块]-[功能]-[序号].tc.md`

- 版本管理
  - 版本文件命名：`[原文件名].v[版本号].us.md`
  - 每个版本文件需要包含：
    * 变更说明
    * 变更原因
    * 影响范围
    * 关联文件更新

## TDD 开发规范
- 遵循 TDD 的三个步骤：
  1. 先写测试（Red）
     - 在编写实现代码之前先写测试
     - 测试应该是失败的
     - 测试代码要简单清晰
     - 一次只测试一个功能点
  
  2. 实现代码（Green）
     - 快速实现功能让测试通过
     - 优先考虑最简单的实现
     - 不考虑代码质量和性能
     - 专注于满足当前测试用例
  
  3. 重构优化（Refactor）
     - 在测试通过的基础上重构代码
     - 改进代码设计和质量
     - 消除重复代码
     - 确保重构后测试仍然通过

## BDD 测试规范
- Feature 文件组织
  - 文件以 .feature 结尾
  - 按业务功能模块组织目录结构
  - 一个 feature 文件对应一个主要业务功能

- Gherkin 语法规范
  - 使用中文编写 Gherkin 语句
  - Feature 描述业务功能模块
  - Scenario 描述具体业务场景
  - 遵循 Given-When-Then 结构：
    - Given：准备测试前置条件
    - When：执行被测试的操作
    - Then：验证测试结果
    - And：补充连接同类步骤
    - But：补充对比步骤

- Steps 文件组织
  - 步骤定义文件以 .steps.ts 结尾
  - 与 feature 文件放在同一目录
  - 按功能模块拆分 steps 文件
  - 步骤实现要简单清晰

- Scenario 编写规范
  - 一个场景只测试一个业务流程
  - 场景描述要清晰明确
  - 避免场景之间的依赖
  - 合理使用 Background 共享前置条件
  - 合理使用 Scenario Outline 和 Examples
  - 严格遵循单场景开发流程：
    * 一次只定义和开发一个场景
    * 当前场景未完成前不开始新场景
    * 场景完成的定义：
      - 场景的步骤定义已实现
      - 相关的单元测试已编写并通过
      - 功能代码已实现并通过测试
      - 代码已完成必要的重构
      - 文档已更新
      - 变更已提交到代码库
    * 开发顺序：
      1. 定义单个场景
      2. 实现该场景的步骤定义
      3. 编写相关单元测试
      4. 实现功能代码
      5. 运行并确保所有测试通过
      6. 进行必要的重构
      7. 完成后再开始下一个场景

- Hooks 使用规范
  - Before/After hooks 处理环境准备和清理
  - BeforeAll/AfterAll 处理全局设置
  - 标签化管理不同场景的 hooks
  - 保持 hooks 轻量和独立

- 数据管理
  - 使用 Examples 表格管理测试数据
  - 使用 fixtures 存放测试数据文件
  - 避免在 feature 文件中硬编码数据
  - 数据准备和清理要完整

- 测试环境
  - 环境配置使用环境变量
  - 不同环境的配置通过 profiles 管理
  - 保持测试环境的独立性
  - 环境相关的设置要文档化  

## 单元测试规范
- 测试文件命名
  - 测试文件以 .test.ts 结尾
  - 测试文件与被测试文件同名
  - 测试文件与被测试文件在同一目录

- 测试结构组织
  - 使用 describe 描述测试单元
  - 使用 it/test 描述测试用例
  - 遵循 AAA 模式：
    - Arrange（准备）：准备测试数据和环境
    - Act（执行）：执行被测试的代码
    - Assert（断言）：验证测试结果

- 测试用例编写
  - 每个测试用例只测试一个功能点
  - 测试用例描述清晰明确
  - 使用有意义的测试数据
  - 避免测试用例之间的依赖
  - 合理使用 setup 和 teardown

- 测试覆盖率要求
  - 核心业务逻辑覆盖率 > 90%
  - 工具函数覆盖率 > 80%
  - 分支覆盖率 > 80%
  - UI 组件覆盖率 > 70%

- Mock 和 Stub 使用规范
  - 合理使用 Mock 隔离外部依赖
  - 使用 Stub 模拟数据和行为
  - Mock 对象要符合接口契约
  - 避免过度 Mock

- 异步测试规范
  - 正确使用 async/await
  - 处理 Promise 和回调
  - 测试超时设置合理
  - 清理异步资源

- 测试代码质量
  - 测试代码要如同产品代码一样整洁
  - 避免在测试中使用魔法数字
  - 提取重复的测试代码为辅助函数
  - 使用有意义的变量命名

## 最佳实践
- 先写测试再写实现
- 小步快走，频繁提交
- 及时重构，保持代码整洁
- 测试要快速、独立、可重复
- 定期回归测试
- 持续集成时运行所有测试
- 定期检查测试覆盖率报告

- BDD 相关最佳实践
  - 业务人员和开发人员共同评审 feature 文件
  - 保持 Gherkin 语言的业务性和可读性
  - 定期重构和优化测试场景
  - 场景要覆盖关键业务流程
  - 合理使用 tags 组织和管理场景
  - 持续集成时执行所有 BDD 测试
  - 定期检查场景覆盖率 
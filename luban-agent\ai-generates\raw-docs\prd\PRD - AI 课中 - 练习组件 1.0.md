PRD - AI 课中 - 练习组件 1.0 

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0 | 2025.04.25 | 张博 | 新建 |  |

- 新建文档
**关联需求**

| 关联需求名称 | 所属 PM | 需求进度 | 文档链接 |
| --- | --- | --- | --- |
| AI 课中 - V1.0 |  |  |  |
| 题型支持 |  |  |  |

**设计稿**

视觉稿：待补充



# 一、背景和目标

## 需求背景

为了优化 AI 课中整体学习体验，提升学生作答流畅度和情感反馈体验，本次练习组件在 Demo V1.0 与 V1.1 基础上进行进一步升级。

目标是通过引入更友好的转场动画、作答即时反馈机制，提升课程满意度与学习效果。

## 项目收益

- 优化课中作答体验。
- 通过即时反馈与情感激励，增强学生参与感。
## 覆盖用户

- 使用新 AI 课版本的全量学生用户
## 方案简述

本次方案主要聚焦在练习组件的交互体验升级，包括：

- 进入练习转场动效优化
- 题目间即时反馈机制
- 支持勾画与加入错题本功能复用


## 未来会做什么

1. 引入互动讲题模块，基于题目分布讲解和互动，进一步提升课中练习体验与教学效果。


# 二、名词说明

暂无



# 三、业务流程

整体课程结构和demo阶段类似，V1.0只支持 「文档组件」、「练习组件」、「答疑组件」

一节课由不同的可配置组件构成。组件之间不同的组合方式构成了不同的课程类型。

答疑组件在课中不能单独配置，需要挂载在「文档组件」/「练习组件」下

| 单节课程结构 | 组件类型 | 说明 |
| --- | --- | --- |
| ![in_table_board_IhpkwURCUh57uYbMqBhckYbNnld](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845253032.png) | 文档组件 | 课程内容为JS文档形式，支持学生在上课过程中自由浏览，支持点击/长按进行提问 |
|  | 练习组件 | 支持各类题型（一期仅支持单选），可按策略推送题目 |
|  | 答疑组件 | 基于大模型能力和学生进行实时对话，解答学生问题 |

![analyze_in_table_board_IhpkwURCUh57uYbMqBhckYbNnld](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845253032.png)

# 四、需求概览

| 模块 | 需求描述 | 优先级 |
| --- | --- | --- |
| 练习组件 | 增加进入转场和题目间转场和反馈。 | P0 |



# 五、详细产品方案

## 练习组件

嗯 我现在想着是 进入练习时 老师在但是内容 侧滑进来有个题的转场。

然后老师说完 题和老师滑走，然后进入题目。

| 模块/功能 | 需求描述 | 原型图 |
| --- | --- | --- |
| 进入练习 | 进入练习提示：进入练习转场： | ![in_table_image_E3xVbhOCBoczFFxa0Vic8qyFnLb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845254353.png) |
| 练习环节 | 页面框架 | ![in_table_image_K4wFb9b0Qof0G2xH6FvcBEGFnjx](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845254833.png) |
| 题目间转场 |  | ![in_table_image_BosmbvrKeo8580x9RqycMEZ7nYb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845255737.png) |

- 练习组件最顶部，增加“下拉进入练习” tip ，预告即将进入练习环节。
- 首次进入练习（所有题目未作答时）：
- 时机：用户完成课程后，进入练习组件时
- 转场：
- 转场动效，翻页。
- IP动效 + 文案。
- 文案：开始练习 + 课程名称。
- 展示时间： 2 s。
- 再次进入练习
- 时机：用户退出课程后再次进入课程时进度在练习组件时，
- 转场：
- IP动效 + 文案。
- 文案：继续练习 + 课程名称。
- 展示时间： 2 s。
![analyze_in_table_image_ZBTHbAACio3xtIxZgtQc7EpVnGh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845253755.png)

![analyze_in_table_image_E3xVbhOCBoczFFxa0Vic8qyFnLb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845254353.png)

- 顶部导航栏：
- 退出按钮：逻辑同课程框架。
- 作答计时：复用题型组件。
- 每题独立正计时（格式 00:00，上限 59:59）。
- 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时（例一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。
- 作答进度：
- 进度展示：
- 分母：预估学生作答数量。
- 分子：依据推题策略，
- 如果学生答对，进度涨。
- 如果学生答错，但没有相似题，进度涨。
- 如果学生答错，但有相似题需要再练一道，进度不变。
- 连对触发效果：
- 同巩固练习，。
- 答题组件：
- 题型组件：各题型在练习中的展示和交互，见。
- 题目推荐：见课中题目推荐策略，见。
- 勾画组件：同巩固练习，。
- 错题本组件：同巩固练习，。
- 答疑组件：
- 入口：右上角常驻展示「问一问」，题目进入解析状态前屏蔽问一问入口。
- 交互：
- 长按题目内容：选中文字/图片内容，唤起问一问。
- 用户可拖拽高亮条，选择文字。
- 点击问一问，进入答疑组件。
- 点击评论，唤起键盘，用户可输入内容进行发表。
![analyze_in_table_image_K4wFb9b0Qof0G2xH6FvcBEGFnjx](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845254833.png)

- 逻辑和展示同巩固练习，。
![analyze_in_table_image_GFWobx4zHo13YlxhwhlccN3GnRe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845255237.png)

![analyze_in_table_image_BosmbvrKeo8580x9RqycMEZ7nYb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746845255737.png)



# 七、数据需求

## 埋点需求

| 模块 | 页面 | 动作 | 埋点 | 备注 |
| --- | --- | --- | --- | --- |
| 练习组件 | 练习页面 | 加载题目 | exercise_question_load | 每道题加载完成 |
| 练习组件 | 练习页面 | 提交答案 | exercise_answer_submit | 记录作答时间、答案内容、是否正确 |
| 练习组件 | 练习页面 | 点击退出按钮 | exercise_exit_confirm_click | 退出时是否保存进度 |
| 练习组件 | 练习页面 | 勾画操作 | drawing_action | 包括勾画/橡皮擦/清除/撤销/恢复 |
| 练习组件 | 练习页面 | 错题收藏/取消 | mistakebook_toggle_click | 收藏或取消收藏错题动作 |
| 练习组件 | 作答后反馈页 | 展示作答反馈 | answer_feedback_show | 正确/错误/连胜类型 |
| 练习组件 | 难度变化提示页 | 展示难度调整提示 | difficulty_change_feedback_show | 上升/下降类型 |
| 练习组件 | 作答过程中 | 展示不认真作答提示 | inattentive_feedback_show | 作答过快/异常行为触发 |



## 数据存储需求

| 模块 | 数据项 | 说明 |
| --- | --- | --- |
| 练习组件 | 学生选择的答题选项 | 包括选择内容、是否确定提交 |
| 练习组件 | 每题作答时长 | 单题独立正计时，精确到秒 |
| 练习组件 | 每题作答正确/错误标识 | 用于后续练习效果统计 |
| 练习组件 | 连续正确次数 | 记录连胜链条 |
| 练习组件 | 是否触发难度上升/下降 | 标记作答后难度变化情况 |




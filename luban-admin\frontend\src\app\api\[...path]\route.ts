import { NextRequest, NextResponse } from 'next/server'

const API_HOST = process.env.NEXT_PUBLIC_API_HOST || 'http://localhost:3000'

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleApiRequest(request, params.path, 'GET')
}

export async function POST(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleApiRequest(request, params.path, 'POST')
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleApiRequest(request, params.path, 'PUT')
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleApiRequest(request, params.path, 'DELETE')
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return handleApiRequest(request, params.path, 'PATCH')
}

export async function OPTIONS(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Allow-Credentials': 'true',
    },
  })
}

async function handleApiRequest(
  request: NextRequest,
  pathSegments: string[],
  method: string
) {
  try {
    const apiPath = pathSegments.join('/')
    const url = new URL(request.url)
    const targetUrl = `${API_HOST}/api/${apiPath}${url.search}`

    console.log(`🔄 API代理: ${method} ${request.url} -> ${targetUrl}`)

    const headers: Record<string, string> = {}
    
    // 复制请求头
    request.headers.forEach((value, key) => {
      if (!key.toLowerCase().startsWith('host') && 
          !key.toLowerCase().startsWith('origin') &&
          !key.toLowerCase().startsWith('referer')) {
        headers[key] = value
      }
    })

    let body: any = undefined
    if (method !== 'GET' && method !== 'HEAD') {
      const contentType = request.headers.get('content-type')
      if (contentType?.includes('application/json')) {
        body = JSON.stringify(await request.json())
      } else if (contentType?.includes('application/x-www-form-urlencoded')) {
        body = await request.text()
      } else {
        body = await request.arrayBuffer()
      }
    }

    const response = await fetch(targetUrl, {
      method,
      headers,
      body,
    })

    const responseHeaders = new Headers()
    
    // 复制响应头
    response.headers.forEach((value, key) => {
      responseHeaders.set(key, value)
    })

    // 添加CORS头
    responseHeaders.set('Access-Control-Allow-Origin', '*')
    responseHeaders.set('Access-Control-Allow-Credentials', 'true')

    const responseBody = await response.arrayBuffer()

    return new NextResponse(responseBody, {
      status: response.status,
      statusText: response.statusText,
      headers: responseHeaders,
    })

  } catch (error) {
    console.error('❌ API代理错误:', error)
    return NextResponse.json(
      { error: 'API代理请求失败', details: error instanceof Error ? error.message : String(error) },
      { status: 500 }
    )
  }
}
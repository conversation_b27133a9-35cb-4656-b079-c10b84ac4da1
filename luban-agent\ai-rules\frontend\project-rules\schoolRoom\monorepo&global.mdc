---
description: 
globs: 
alwaysApply: false
---
---
description: 
globs: 
alwaysApply: true
---

# Schoolroom Monorepo: 前端开发规范与概览

本文档整合了 `schoolroom-monorepo` 项目的全局前端开发规范以及 Monore结构和概览信息。它适用于项目下的所有前端应用 (`apps/*`) 和`packages/*`)。

## 1. 项目结构与核心工具

*   **Monorepo 管理:** 使用 **@Turbo** (Turborepo) 管理各应用和包之间的依赖关系及脚本执行。
*   **包管理器:** *@pnpm** 作为主要包管理器，利用其 ace 特性高效管理项目依赖。
    *   内部包之间引用推荐使用 `workspace:*` 协议。
  在项目根目录执行 `pnpm inst所有依赖。
*   **代码版本控制it** 进行版本控制。
*   **Git Hooks:** 项目配置了 **@Husky**，用于在代码提交（pre-commit）前自动执行代码检查、格式化等预设任务，以保证代码质量。

## 2. 应用和包

Monorepo 包含以下主要的应用和共享包：

*   `apps/stu`: 学生端应用，基于 Next.js。
*   `app师端应用，基于 Next.js。
*   `apps/aipt`: AI 生产工具应用，基于 Next.js。
*   `packages/lib`: 共享工具库，包含通用的工具函数、Hooks 等，被各个应用共同使用。
*   `packages/ui`: 共享 UI 组件库，提供统一风格的 Reac用共同使用。
*   `packages/core`: 共享核心业务库，封装了跨应用的业务逻辑、数据模型和核心业务组件。
*   `packages/config-eslint`: 共享 ESLint 配置，包含了基础配置、N特定配置以及 Prettier 集成。
*   `packages/config-typescript`: 共享 TypeScript 配置，为整个 Monorepo 提供统一的类型检查基准。

## 3. 核心技术栈

*   **框架:** **@React** (v19)。
*   **应用框架:** **@Next.js** (v15)。
*   **语言:** **@TypeScript**。
    *   使用 `@repo/config-typescript/base.json` 作为共享的 TypeScript 基础配置，以确保整个项目的类型安全和一致性。
*   **样式方案:**
    *   主要使用 **@Tailwind CSS** 进行原子化样式开发，实现快速、高效的 UI 构建。
    *   通过 PostCSS 处理 Tailwind CSS。
    *   根据需要，可辅以 CSS Modules 或 CSS-in-JS 方案 (例如 `@emotion/css`) 进行更灵活的样式封装。
*   **状态管理:**
    *   推荐使用 **@Signals** ([Preact Signals](https://preactjs.com/guide/v10/signals/)) 进行组件级或局部模块的状态管理，因其简洁和高效的特性。
    *   对于跨模块或全局状态，可结合 React Context API。
*   **数据请求:**
    *   推荐使用 **@SWR** ([SWR by Vercel](https://swr.vercel.app/)) 进行客户端数据获取和缓存管理。
*   **动态视频与动画:**
    *   使用 **@Remotion** ([remotion.dev](https://www.remotion.dev/)) 创建和管理程序化的动态视频内容。
*   **共享组件库:**
    *   使用内部共享包 `@repo/ui`。该组件库可能基于 **@Radix UI** 作为底层无头组件库进行构建，并使用 Tailwind CSS 进行样式定制。
*   **共享业务逻辑与工具:**
    *   通过 `@repo/core` 包共享核心业务逻辑、特定业务场景的 Hooks 和组件。
    *   通过 `@repo/lib` 包共享通用的工具函数、非业务相关的 Hooks 等。

## 4. 代码质量与规范

*   **代码格式化:**
    *   使用 **@Prettier** 统一代码风格。
    *   配置了 `prettier-plugin-tailwindcss` 插件，以优化 Tailwind CSS 类名的排序和格式化。
    *   在根目录运行 `pnpm format` (或通过 `turbo run format`) 来格式化整个项目的代码。
*   **代码检查 (Linting):**
    *   使用 **@ESLint** 进行代码规范检查和潜在错误发现。
    *   基于 `@repo/config-eslint` 提供的共享配置，确保各应用和包遵循统一的 Linting 规则。
    *   在根目录运行 `pnpm lint` (或通过 `turbo run lint`) 来执行代码检查。
*   **类型检查:**
    *   利用 **@TypeScript** 自身的静态类型系统进行全面的类型检查。
    *   项目根目录通常包含一个 `check-types` 脚本 (例如 `tsc --noEmit -p tsconfig.json`) 用于全局类型检查。
    *   各应用和包的构建 (`build`) 或 Lint 脚本中也应包含类型检查步骤 (`tsc --noEmit`)。

## 5. 依赖管理

*   所有外部依赖和内部包的引用都在各个应用或包自身的 `package.json` 文件中声明。
*   **安装新依赖:** 在目标应用或包的目录下，使用 `pnpm add <package-name> [--save-dev]` 命令安装。
*   **更新依赖:** 推荐使用 `pnpm update <package-name>` 或 `pnpm up -iL` 进行交互式更新。
*   **内部包引用:** 强制使用 `workspace:*` 协议来引用 Monorepo内部的其他包，例如 `"@repo/ui": "workspace:*"`.

## 6. 构建与开发命令

*   **启动所有应用开发服务器:**
    *   在项目根目录运行 `pnpm dev` 或 `turbo run dev`。
*   **构建所有应用和包:**
    *   在项目根目录运行 `pnpm build` 或 `turbo run build`。
*   **针对特定应用或包执行脚本:**
    *   使用 Turborepo 的 `--filter` 标志: `turbo run <script-name> --filter=<app-or-package-name>` (例如 `turbo run dev --filter=stu`)。
    *   或者，直接进入特定应用或包的目录 (`cd apps/stu`)，然后运行 pnpm 脚本 (例如 `pnpm dev`)。

## 7. 测试

*   项目鼓励并包含测试实践（例如，`apps/stu` 项目中可能配置了 Jest 或其他测试框架）。
*   对于关键的业务逻辑、核心工具函数和重要的 UI 组件，应编写单元测试和/或集成测试，以确保其正确性和稳定性。

## 8. 应用架构规范

*   关于应用层面的详细架构设计规范、目录结构约定、MVVM 模式的推荐实践等，请参考文档：`./application-architecture.md` (请确保此文件存在于项目根目录或文档目录)。

## 9. 更多信息

*   若需查询更具体的内部文档或上下文信息，可尝试使用内部工具或命令，例如 (此为示例，具体请参照项目实际情况): `MCP:Context7 查询具体文档`。


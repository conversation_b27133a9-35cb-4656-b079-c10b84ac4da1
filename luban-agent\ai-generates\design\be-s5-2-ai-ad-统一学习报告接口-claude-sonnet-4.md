# 接口使用文档 - 统一学习报告接口 - V1.0

* **最后更新日期**: 2025-06-06
* **设计负责人**: claude-sonnet-4
* **评审人**: 待填写
* **状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-06-06 | claude-sonnet-4 | 初始草稿，合并AI课中和巩固练习的学习报告接口 |

## 1. 服务概述与设计目标

### 1.1 服务背景与范围
本服务提供统一的学习报告接口，支持学生在多种学习场景下的报告查看需求。涵盖老师布置的课程任务、课后作业以及学生自学等全场景的学习报告管理。

服务范围涵盖：
- **课程任务报告**：老师布置的AI课、巩固练习、拓展练习等课程学习任务完成后的报告
- **作业任务报告**：老师布置的课后作业完成后的学习报告
- **自学报告**：学生自主选择章节进行AI课、巩固练习、拓展练习等学习活动的报告
- **任务列表管理**：支持学生查看所有学习任务（包括自学）的列表和状态
- **历史报告查询**：支持按时间范围筛选和查看历史学习报告
- **多维度数据分析**：提供学习统计、掌握度变化、答题详情等丰富数据
- **个性化反馈**：包含IP角色反馈和学习建议推荐

### 1.2 设计目标回顾
1. 统一不同学习场景的报告接口，提供一致的调用体验
2. 支持多种报告类型：课中学习报告、巩固练习报告、综合学习报告
3. 提供丰富的学习数据：掌握度变化、学习统计、答题详情、时长分析等
4. 支持个性化反馈：IP角色反馈、再练推荐、学习建议
5. 确保高性能：P95响应时间<2秒，支持1000并发用户
6. 遵循RESTful设计规范和团队API标准

## 2. 通用设计规范与约定

### 2.1 数据格式
- 请求体 (Request Body): `application/json`
- 响应体 (Response Body): `application/json`
- 日期时间格式: ISO 8601 (`YYYY-MM-DDTHH:mm:ss.sssZ`)

### 2.2 认证与授权
- 采用Bearer Token (JWT)认证机制
- 所有接口在请求头中传递 `Authorization: Bearer <token>`
- Token包含用户ID、权限角色等信息，确保学生只能访问自己的学习报告

### 2.3 版本控制
- 通过 URL 路径进行版本控制，如 `/api/v1/...`
- 当前版本为 v1，后续版本迭代遵循语义化版本控制

### 2.4 错误处理约定
遵循统一的错误码和错误响应格式。

**通用错误响应体示例**:
```json
{
  "code": 0,                // 业务错误码，0表示成功，非0表示错误
  "message": "错误信息",     // 用户可读的错误信息
  "detail": "错误详情,可选",  // 错误的详细信息，可选
  "data": "返回数据",        // 可选，返回的数据，错误时通常为空
  "pageInfo": "分页信息,可选"  // 可选，分页信息，错误时通常为空
}
```

**常用 HTTP 状态码**:
- `200 OK`: 请求成功
- `400 Bad Request`: 请求无效 (例如参数错误、格式错误)
- `401 Unauthorized`: 未认证或认证失败
- `403 Forbidden`: 已认证，但无权限访问
- `404 Not Found`: 请求的资源不存在
- `500 Internal Server Error`: 服务器内部错误

### 2.5 分页与排序约定
**分页参数**:
- page: 页码，从1开始，默认值为1
- pageSize: 每页数量，默认值为20，最大值为100

**排序参数**:
- sortBy: 排序字段，默认为创建时间
- sortType: 排序方式 (asc 或 desc)，默认为desc

## 3. 接口列表

| 接口路径            | 请求方式 | 接口名称         | 接口描述               |
| -------------------| -------- | -------- | ---------------- |
| /api/v1/learning_reports | GET | 获取学习报告 | 统一的学习报告接口，支持单个报告查询、历史报告列表、任务报告等多种场景 |
| /api/v1/learning_tasks | GET | 获取学习任务列表 | 获取学生的所有学习任务（包括课程任务、作业任务、自学任务） |

## 4. 场景说明

### 4.1 老师布置课程学习任务场景

#### 4.1.1 课程任务学习流程
1. 老师在教师端布置课程学习任务（AI课、巩固练习、拓展练习等）
2. 学生收到任务通知，进入学习任务页面
3. 学生点击开始学习，系统创建学习会话并记录任务关联
4. 学生在学习过程中提交作答信息，系统实时记录学习进度
5. 学生完成学习任务，系统自动结束会话并生成学习报告
6. 前端调用学习报告接口，获取包含任务完成情况的报告
7. 系统更新任务状态为已完成，记录完成时间

#### 4.1.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant TaskService as 任务服务
    participant ReportService as 学习报告服务
    participant StudyService as 学习服务

    Student->>Frontend: 查看学习任务
    Frontend->>TaskService: GET /api/v1/learning_tasks
    TaskService-->>Frontend: 返回任务列表
    Frontend-->>Student: 展示任务列表

    Student->>Frontend: 点击开始学习任务
    Frontend->>StudyService: 创建学习会话（关联任务ID）
    StudyService-->>Frontend: 返回会话信息
    
    Note over Student,StudyService: 学习过程中提交作答信息
    
    Student->>Frontend: 完成学习任务
    Frontend->>StudyService: 结束学习会话
    StudyService->>TaskService: 更新任务完成状态
    
    Frontend->>ReportService: GET /api/v1/learning_reports
    Note over Frontend,ReportService: 携带会话ID，任务类型=course_task
    ReportService-->>Frontend: 返回任务学习报告
    Frontend-->>Student: 展示任务完成报告
```

### 4.2 老师布置课后作业场景

#### 4.2.1 作业任务学习流程
1. 老师在教师端布置课后作业，设置截止时间
2. 学生收到作业任务通知，进入作业页面
3. 学生点击开始作答，系统创建作业会话
4. 学生提交作业答案，系统记录作答详情
5. 学生完成作业提交，系统生成作业报告
6. 前端调用学习报告接口，获取作业完成报告
7. 系统更新作业状态并记录提交时间

#### 4.2.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant TaskService as 任务服务
    participant ReportService as 学习报告服务
    participant HomeworkService as 作业服务

    Student->>Frontend: 查看作业任务
    Frontend->>TaskService: GET /api/v1/learning_tasks
    Note over Frontend,TaskService: 筛选任务类型=homework
    TaskService-->>Frontend: 返回作业任务列表
    Frontend-->>Student: 展示作业列表

    Student->>Frontend: 点击开始作答
    Frontend->>HomeworkService: 创建作业会话
    HomeworkService-->>Frontend: 返回作业会话信息
    
    Note over Student,HomeworkService: 作答过程中提交答案
    
    Student->>Frontend: 提交作业
    Frontend->>HomeworkService: 完成作业提交
    HomeworkService->>TaskService: 更新作业完成状态
    
    Frontend->>ReportService: GET /api/v1/learning_reports
    Note over Frontend,ReportService: 携带会话ID，任务类型=homework_task
    ReportService-->>Frontend: 返回作业报告
    Frontend-->>Student: 展示作业完成报告
```

### 4.3 学生自学场景

#### 4.3.1 自学流程
1. 学生进入课程章节页面，浏览可学习的内容
2. 学生选择某个章节，点击AI课、巩固练习或拓展练习
3. 系统创建自学会话，无截止时间限制
4. 学生进行学习活动，系统记录学习过程
5. 学生完成自学内容，系统生成自学报告
6. 前端调用学习报告接口，获取自学成果报告
7. 系统将自学记录添加到学习历史中

#### 4.3.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant CourseService as 课程服务
    participant ReportService as 学习报告服务
    participant StudyService as 学习服务

    Student->>Frontend: 浏览课程章节
    Frontend->>CourseService: 获取章节内容
    CourseService-->>Frontend: 返回章节信息
    Frontend-->>Student: 展示章节学习选项

    Student->>Frontend: 选择学习类型（AI课/巩固练习/拓展练习）
    Frontend->>StudyService: 创建自学会话
    Note over Frontend,StudyService: 任务类型=self_study，无截止时间
    StudyService-->>Frontend: 返回自学会话信息
    
    Note over Student,StudyService: 自学过程中的学习活动
    
    Student->>Frontend: 完成自学内容
    Frontend->>StudyService: 结束自学会话
    
    Frontend->>ReportService: GET /api/v1/learning_reports
    Note over Frontend,ReportService: 携带会话ID，任务类型=self_study
    ReportService-->>Frontend: 返回自学报告
    Frontend-->>Student: 展示自学成果报告
```

### 4.4 学生查看任务列表场景

#### 4.4.1 任务列表查看流程
1. 学生进入学习中心或任务页面
2. 前端调用学习任务列表接口，获取所有任务
3. 系统返回包含课程任务、作业任务、自学记录的完整列表
4. 学生可以按任务类型、完成状态、时间范围进行筛选
5. 学生点击某个任务，查看任务详情或学习报告
6. 前端调用任务学习报告接口，展示完整报告

#### 4.4.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant TaskService as 任务服务
    participant ReportService as 学习报告服务

    Student->>Frontend: 进入学习任务页面
    Frontend->>TaskService: GET /api/v1/learning_tasks
    Note over Frontend,TaskService: 携带筛选条件：任务类型、状态、时间范围
    TaskService-->>Frontend: 返回任务列表
    Frontend-->>Student: 展示任务列表

    Student->>Frontend: 点击查看某个任务报告
    Frontend->>ReportService: GET /api/v1/learning_tasks/{task_id}/report
    ReportService->>TaskService: 查询任务详情
    TaskService-->>ReportService: 返回任务信息
    ReportService-->>Frontend: 返回完整任务报告
    Frontend-->>Student: 展示任务学习报告详情
```

### 4.5 AI课中学习完成后获取学习报告场景（原有场景保留）

#### 4.1.1 课中学习报告获取流程
1. 学生完成AI课中最后一个文档组件的学习
2. 前端调用结束学习会话接口，触发掌握度计算
3. 前端调用统一学习报告接口，指定报告类型为"课中学习"
4. 系统查询学习会话数据，包括学习时长、交互记录、掌握度变化等
5. 系统生成包含IP动效反馈、学习统计、课程完成庆祝的完整报告
6. 前端展示课中学习报告页面，包括掌握度变化动效和庆祝界面

#### 4.1.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant ReportService as 学习报告服务
    participant StudyService as 学习服务
    participant MasteryService as 掌握度计算服务

    Student->>Frontend: 完成最后一个文档组件
    Frontend->>StudyService: 结束学习会话 /api/v1/study/session/end
    StudyService->>MasteryService: 触发掌握度计算
    MasteryService-->>StudyService: 返回掌握度数据
    StudyService-->>Frontend: 返回会话结束状态

    Frontend->>ReportService: GET /api/v1/learning_reports
    Note over Frontend,ReportService: 携带会话ID，报告类型=course_study
    
    ReportService->>StudyService: 查询学习会话数据
    StudyService-->>ReportService: 返回学习统计数据
    
    ReportService->>MasteryService: 获取掌握度变化
    MasteryService-->>ReportService: 返回掌握度分析
    
    ReportService->>ReportService: 生成课中学习报告
    ReportService-->>Frontend: 返回完整学习报告
    
    Frontend-->>Student: 展示课中学习报告和庆祝页面
```

### 4.2 巩固练习完成后获取学习报告场景

#### 4.2.1 巩固练习报告获取流程
1. 学生完成巩固练习最后一道题目，系统检测到练习结束
2. 前端调用统一学习报告接口，指定报告类型为"巩固练习"
3. 系统汇总本次练习的作答记录，计算正确率、用时、掌握度变化等
4. 系统生成包含IP角色反馈、学习统计、再练推荐的完整报告
5. 前端展示巩固练习报告页面，包括掌握度变化动效
6. 如果系统推荐再练一次，学生可选择开始再练或完成练习

#### 4.2.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant ReportService as 学习报告服务
    participant ExerciseService as 巩固练习服务
    participant MasteryService as 掌握度计算服务

    Note over Student,MasteryService: 练习完成触发
    ExerciseService->>ExerciseService: 检测练习完成
    ExerciseService->>ExerciseService: 更新会话状态为已完成
    
    Frontend->>ReportService: GET /api/v1/learning_reports
    Note over Frontend,ReportService: 携带会话ID，报告类型=exercise_practice
    
    ReportService->>ExerciseService: 查询作答记录
    ExerciseService-->>ReportService: 返回作答统计
    
    ReportService->>MasteryService: 获取掌握度变化
    MasteryService-->>ReportService: 返回掌握度数据
    
    ReportService->>ReportService: 生成巩固练习报告
    ReportService->>ReportService: 判断是否推荐再练一次
    
    ReportService-->>Frontend: 返回完整学习报告
    Frontend-->>Student: 展示报告页面
    
    alt 推荐再练一次
        Student->>Frontend: 点击再练一次
        Frontend->>ExerciseService: GET /api/v1/exercise_sessions/enter
        Note over Frontend,ExerciseService: 创建新的再练会话
    else 完成练习
        Student->>Frontend: 点击完成
        Frontend-->>Student: 返回课程页面
    end
```

### 4.3 查看历史学习报告场景

#### 4.3.1 历史报告查看流程
1. 学生进入学习历史页面或个人中心
2. 前端调用历史学习报告列表接口，获取报告列表
3. 系统返回分页的历史报告记录，包括报告类型、时间、课程信息等
4. 学生可以筛选、排序查看不同类型的学习报告
5. 学生点击查看具体报告详情
6. 前端调用学习报告接口，获取历史报告的完整内容

#### 4.3.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant ReportService as 学习报告服务
    participant StudyService as 学习服务
    participant ExerciseService as 巩固练习服务

    Student->>Frontend: 进入学习历史页面
    Frontend->>ReportService: GET /api/v1/learning_reports/history
    Note over Frontend,ReportService: 携带学生ID、分页参数、筛选条件
    
    ReportService->>ReportService: 查询历史报告记录
    ReportService-->>Frontend: 返回历史报告列表
    Frontend-->>Student: 展示历史报告列表
    
    Student->>Frontend: 点击查看具体报告
    Frontend->>ReportService: GET /api/v1/learning_reports
    Note over Frontend,ReportService: 携带报告ID或会话ID
    
    alt 课中学习报告
        ReportService->>StudyService: 查询课中学习数据
        StudyService-->>ReportService: 返回学习数据
    else 巩固练习报告
        ReportService->>ExerciseService: 查询练习数据
        ExerciseService-->>ReportService: 返回练习数据
    end
    
    ReportService-->>Frontend: 返回完整历史报告
    Frontend-->>Student: 展示报告详情页面
```

### 4.4 获取学习报告摘要场景

#### 4.4.1 学习摘要获取流程
1. 学生进入个人学习中心或课程总览页面
2. 前端调用学习报告摘要接口，获取整体学习情况
3. 系统汇总学生在指定时间范围内的所有学习活动
4. 系统计算综合学习指标：总学习时长、课程完成数、练习完成率、整体掌握度等
5. 系统生成学习趋势分析和个性化学习建议
6. 前端展示学习摘要仪表板，包括图表和趋势分析

#### 4.4.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant ReportService as 学习报告服务
    participant StudyService as 学习服务
    participant ExerciseService as 巩固练习服务
    participant MasteryService as 掌握度计算服务

    Student->>Frontend: 进入学习中心
    Frontend->>ReportService: GET /api/v1/learning_reports/summary
    Note over Frontend,ReportService: 携带学生ID、时间范围参数
    
    ReportService->>StudyService: 查询课中学习统计
    StudyService-->>ReportService: 返回课中学习数据
    
    ReportService->>ExerciseService: 查询巩固练习统计
    ExerciseService-->>ReportService: 返回练习数据
    
    ReportService->>MasteryService: 获取整体掌握度趋势
    MasteryService-->>ReportService: 返回掌握度分析
    
    ReportService->>ReportService: 生成综合学习摘要
    ReportService->>ReportService: 计算学习趋势和建议
    
    ReportService-->>Frontend: 返回学习摘要报告
    Frontend-->>Student: 展示学习仪表板
```

## 5. 接口详细说明

### 5.1 获取学习报告接口

**接口路径**: `GET /api/v1/learning_reports`

**接口描述**: 统一的学习报告接口，通过query_type参数支持单个报告查询、历史报告列表、任务报告等多种场景

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| query_type | string | 是 | 查询类型：single(单个报告)、history(历史报告列表)、task_report(任务报告) | "single" |
| session_id | string | 否 | 学习会话ID，query_type=single时使用 | "session_123456" |
| task_id | string | 否 | 任务ID，query_type=task_report时使用 | "task_789" |
| report_id | string | 否 | 历史报告ID，query_type=single时使用 | "report_456" |
| report_type | string | 否 | 报告类型筛选：course_study(课中学习)、exercise_practice(巩固练习)、course_task(课程任务)、homework_task(作业任务)、self_study(自学) | "course_task" |
| student_id | string | 否 | 学生ID，从Token中获取，用于权限验证 | "student_789" |
| course_id | string | 否 | 课程ID筛选 | "course_101" |
| start_date | string | 否 | 开始日期筛选，query_type=history时使用 | "2025-06-01" |
| end_date | string | 否 | 结束日期筛选，query_type=history时使用 | "2025-06-06" |
| date_range | string | 否 | 预设时间范围：today(今天)、week(最近一周)、month(最近一月)、quarter(最近三月) | "month" |
| status | string | 否 | 完成状态筛选：completed(已完成)、all(全部) | "completed" |
| page | integer | 否 | 页码，query_type=history时使用，默认1 | 1 |
| page_size | integer | 否 | 每页数量，query_type=history时使用，默认20 | 20 |
| sort_by | string | 否 | 排序字段：generated_at(生成时间)、completion_time(完成时间)、mastery_change(掌握度变化) | "completion_time" |
| sort_type | string | 否 | 排序方式，默认desc | "desc" |

**响应数据结构**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "report_id": "report_123456",
    "report_type": "course_task",
    "session_id": "session_123456",
    "task_id": "task_789",
    "student_id": "student_789",
    "course_id": "course_101",
    "course_name": "AI基础课程",
    "task_name": "AI基础概念学习",
    "task_type": "course_task",
    "deadline": "2025-06-10T23:59:59.000Z",
    "completion_time": "2025-06-06T10:30:00.000Z",
    "generated_at": "2025-06-06T10:30:00.000Z",
    "study_statistics": {
      "total_duration": 1800,
      "study_start_time": "2025-06-06T09:00:00.000Z",
      "study_end_time": "2025-06-06T09:30:00.000Z",
      "component_count": 5,
      "completed_components": 5,
      "interaction_count": 23,
      "mode_switches": 3
    },
    "mastery_analysis": {
      "previous_mastery": 65.5,
      "current_mastery": 78.2,
      "mastery_change": 12.7,
      "mastery_level": "良好",
      "knowledge_points": [
        {
          "point_name": "AI基础概念",
          "previous_score": 60.0,
          "current_score": 75.0,
          "improvement": 15.0
        }
      ]
    },
    "performance_details": {
      "total_questions": 8,
      "correct_answers": 6,
      "accuracy_rate": 75.0,
      "average_answer_time": 45.5,
      "question_details": [
        {
          "question_id": "q_001",
          "is_correct": true,
          "answer_time": 42,
          "difficulty": "medium"
        }
      ]
    },
    "ip_feedback": {
      "feedback_type": "celebration",
      "feedback_message": "太棒了！你在AI基础概念方面有了很大进步！",
      "feedback_animation": "celebration_star",
      "encouragement": "继续保持这样的学习状态，你会越来越棒的！"
    },
    "recommendations": {
      "next_actions": [
        {
          "action_type": "continue_course",
          "title": "继续下一课程",
          "description": "你已经掌握了基础概念，可以进入进阶课程了"
        }
      ],
      "practice_suggestions": [
        {
          "suggestion_type": "review_weak_points",
          "content": "建议复习一下机器学习基础概念"
        }
      ]
    }
  }
}
```

### 5.2 获取历史学习报告列表接口

**接口路径**: `GET /api/v1/learning_reports/history`

**接口描述**: 查询学生的历史学习报告记录列表

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| student_id | string | 是 | 学生ID，从Token中获取 | "student_789" |
| report_type | string | 否 | 报告类型筛选：course_study(课中学习)、exercise_practice(巩固练习)、course_task(课程任务)、homework_task(作业任务)、self_study(自学) | "course_task" |
| task_type | string | 否 | 任务类型筛选，与report_type配合使用 | "course_task" |
| course_id | string | 否 | 课程ID筛选 | "course_101" |
| start_date | string | 否 | 开始日期筛选（按完成时间） | "2025-06-01" |
| end_date | string | 否 | 结束日期筛选（按完成时间） | "2025-06-06" |
| date_range | string | 否 | 预设时间范围：today(今天)、week(最近一周)、month(最近一月)、quarter(最近三月) | "month" |
| status | string | 否 | 完成状态筛选：completed(已完成)、all(全部) | "completed" |
| page | integer | 否 | 页码，默认1 | 1 |
| page_size | integer | 否 | 每页数量，默认20 | 20 |
| sort_by | string | 否 | 排序字段：generated_at(生成时间)、completion_time(完成时间)、mastery_change(掌握度变化) | "completion_time" |
| sort_type | string | 否 | 排序方式，默认desc | "desc" |

**响应数据结构**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": [
    {
      "report_id": "report_123456",
      "report_type": "course_study",
      "course_id": "course_101",
      "course_name": "AI基础课程",
      "generated_at": "2025-06-06T10:30:00.000Z",
      "study_duration": 1800,
      "mastery_change": 12.7,
      "accuracy_rate": 75.0,
      "summary": "完成AI基础课程学习，掌握度提升12.7%"
    }
  ],
  "page_info": {
    "current_page": 1,
    "page_size": 20,
    "total_count": 45,
    "total_pages": 3
  }
}
```

### 5.3 获取学习报告摘要接口

**接口路径**: `GET /api/v1/learning_reports/summary`

**接口描述**: 获取学生整体学习情况的汇总报告

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| student_id | string | 是 | 学生ID，从Token中获取 | "student_789" |
| time_range | string | 否 | 时间范围：week(最近一周)、month(最近一月)、quarter(最近三月) | "month" |
| start_date | string | 否 | 自定义开始日期 | "2025-05-01" |
| end_date | string | 否 | 自定义结束日期 | "2025-06-06" |

**响应数据结构**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "summary_period": {
      "start_date": "2025-05-01",
      "end_date": "2025-06-06",
      "total_days": 36
    },
    "overall_statistics": {
      "total_study_time": 7200,
      "study_days": 18,
      "completed_courses": 3,
      "completed_exercises": 12,
      "total_questions": 156,
      "correct_answers": 128,
      "overall_accuracy": 82.1
    },
    "mastery_trend": {
      "start_mastery": 45.2,
      "end_mastery": 78.5,
      "total_improvement": 33.3,
      "trend_data": [
        {
          "date": "2025-05-01",
          "mastery_score": 45.2
        },
        {
          "date": "2025-06-06",
          "mastery_score": 78.5
        }
      ]
    },
    "course_progress": [
      {
        "course_id": "course_101",
        "course_name": "AI基础课程",
        "completion_status": "completed",
        "completion_date": "2025-05-15",
        "mastery_improvement": 15.3
      }
    ],
    "learning_insights": {
      "strengths": ["概念理解能力强", "学习持续性好"],
      "improvement_areas": ["计算题准确率有待提高"],
      "recommendations": ["建议多做计算练习题", "保持当前学习节奏"]
    }
  }
}
```

## 6. 检查清单

### 6.1 PRD核心需求场景与API覆盖性检查

| PRD核心需求点/用户场景 (ID) | 涉及的主要API接口 (路径与方法) | 关键输入数据实体/参数 (示例) | 关键输出数据实体/参数 (示例) | 场景支持度(✅/⚠️/❌) | 数据流正确性(✅/⚠️/❌) | 备注/待办事项 |
| :------ | :------ | :------ | :------ | :------ | :------ | :------ |
| 老师布置课程学习任务 | `1. GET /api/v1/learning_tasks` <br/> `2. GET /api/v1/learning_reports` | `1. student_id, task_type=course_task` <br/> `2. task_id, report_type=course_task` | `1. 课程任务列表, 任务状态` <br/> `2. 任务学习报告, 完成情况` | ✅ | ✅ | 支持课程任务管理和报告生成，包含截止时间和完成状态 |
| 老师布置课后作业 | `1. GET /api/v1/learning_tasks` <br/> `2. GET /api/v1/learning_reports` | `1. student_id, task_type=homework_task` <br/> `2. task_id, report_type=homework_task` | `1. 作业任务列表, 截止时间` <br/> `2. 作业完成报告, 提交状态` | ✅ | ✅ | 支持作业任务管理，包含截止时间控制和提交状态跟踪 |
| 学生自学场景 | `1. GET /api/v1/learning_tasks` <br/> `2. GET /api/v1/learning_reports` | `1. student_id, task_type=self_study` <br/> `2. session_id, report_type=self_study` | `1. 自学记录列表, 学习进度` <br/> `2. 自学成果报告, 掌握度提升` | ✅ | ✅ | 支持自学记录管理，无截止时间限制，记录学习历史 |
| 学生查看任务列表 | `GET /api/v1/learning_tasks` | `student_id, 筛选条件(任务类型、状态、时间范围)` | `任务列表(任务名、截止时间、完成时间、完成状态)` | ✅ | ✅ | 支持多维度筛选，包含时间范围筛选和任务状态管理 |
| 学生查看任务报告详情 | `GET /api/v1/learning_tasks/{task_id}/report` | `task_id, student_id` | `完整任务报告(任务信息、学习统计、掌握度分析)` | ✅ | ✅ | 提供任务维度的完整学习报告，包含任务评价和推荐 |
| AI课中学习完成后获取学习报告 | `GET /api/v1/learning_reports` | `session_id, report_type=course_study` | `StudyReport (performance, mastery, ip_feedback)` | ✅ | ✅ | 统一接口支持课中学习报告，包含IP反馈和庆祝动效 |
| 巩固练习完成后获取学习报告 | `GET /api/v1/learning_reports` | `session_id, report_type=exercise_practice` | `LearningReport (mastery_change, accuracy_rate, recommendations)` | ✅ | ✅ | 统一接口支持巩固练习报告，包含再练推荐 |
| 查看历史学习报告（支持时间范围筛选） | `1. GET /api/v1/learning_reports/history` <br/> `2. GET /api/v1/learning_reports` | `1. student_id, date_range, 筛选条件` <br/> `2. report_id` | `1. 历史报告列表, 分页信息` <br/> `2. 完整历史报告详情` | ✅ | ✅ | 支持多维度筛选和时间范围筛选，包含预设时间范围选项 |
| 获取学习情况摘要 | `GET /api/v1/learning_reports/summary` | `student_id, time_range` | `综合学习统计, 掌握度趋势, 学习洞察` | ✅ | ✅ | 提供整体学习情况分析和趋势展示 |

### 6.2 API接口数据实体与数据库支持检查

| API接口 (路径与方法) | 操作类型 (CRUD) | 主要数据实体 (模型名称) | 关键数据属性 (字段名示例) | 依赖的数据库表/视图 (示例) | 数据库操作 (读/写/更新/删) | 数据可获得性/可行性(✅/⚠️/❌) | 数据一致性保障 (简述策略) | 备注/待办事项 |
| :------ | :------ | :------ | :------ | :------ | :------ | :------ | :------ | :------ |
| `GET /api/v1/learning_reports` | Read/Create | `LearningReport, StudySession, ExerciseSession, LearningTask` | `report_id, session_id, task_id, study_statistics, mastery_analysis, performance_details` | `tbl_learning_reports`, `tbl_study_sessions`, `tbl_exercise_sessions`, `tbl_learning_tasks` | 读/写 | ✅ | `报告生成和查看合并，缓存策略优化，任务关联` | `支持多种报告类型和任务类型，统一数据模型` |
| `GET /api/v1/learning_reports/history` | Read | `LearningReport, LearningTask` | `report_id, report_type, task_type, course_id, generated_at, completion_time, summary` | `tbl_learning_reports`, `tbl_learning_tasks` | 读 | ✅ | `读取一致性，分页优化，索引优化，时间范围筛选` | `支持多维度筛选、时间范围筛选和排序` |
| `GET /api/v1/learning_reports/summary` | Read | `LearningReport, StudySession, ExerciseSession, LearningTask` | `overall_statistics, mastery_trend, course_progress, task_statistics, learning_insights` | `tbl_learning_reports`, `tbl_study_sessions`, `tbl_exercise_sessions`, `tbl_learning_tasks` | 读 | ✅ | `数据聚合计算，缓存策略，定时更新，跨任务统计` | `需要跨多个数据源聚合计算，包含任务维度统计` |
| `GET /api/v1/learning_tasks` | Read | `LearningTask` | `task_id, task_name, task_type, course_id, deadline, status, completion_time, progress` | `tbl_learning_tasks`, `tbl_task_assignments` | 读 | ✅ | `读取一致性，分页优化，多维度筛选索引` | `支持任务列表查询，包含时间范围和状态筛选` |
| `GET /api/v1/learning_tasks/{task_id}/report` | Read | `LearningTask, LearningReport` | `task_info, learning_report, task_evaluation, completion_quality` | `tbl_learning_tasks`, `tbl_learning_reports`, `tbl_study_sessions` | 读 | ✅ | `任务报告关联查询，数据一致性保障` | `提供任务维度的完整学习报告，包含任务评价` |

### 6.3 API通用设计规范符合性检查

| 检查项 | 状态 (✅/⚠️/❌/N/A) | 备注/说明 |
| :------ | :------ | :------ |
| 1. 命名规范 | ✅ | 使用RESTful风格，路径清晰，参数命名一致 |
| 2. HTTP方法 | ✅ | GET用于查询，符合RESTful规范 |
| 3. 状态码 | ✅ | 200成功，400参数错误，401认证失败，404资源不存在，500服务器错误 |
| 4. 错误处理 | ✅ | 统一错误响应格式，包含业务错误码、用户友好信息和详细错误信息 |
| 5. 认证与授权 | ✅ | 所有接口都需要JWT Token认证，确保学生只能访问自己的数据 |
| 6. 数据格式与校验 | ✅ | 使用JSON格式，包含必填项、长度限制、格式限制等校验规则 |
| 7. 分页与排序 | ✅ | 历史报告接口支持分页参数，默认分页参数合理 |
| 8. 幂等性 | ✅ | GET操作天然幂等，报告生成具有幂等性设计 |
| 9. 安全性 | ✅ | 参数校验和转义，敏感信息加密，JWT Token认证保护 |
| 10. 性能考量 | ✅ | P95响应时间<2秒，支持1000并发，缓存机制，分页查询优化 |
| 11. 文档一致性 | ✅ | 基于原有接口分析文档合并设计，保持一致性 |
| 12. 可测试性 | ✅ | 接口设计清晰，依赖明确，便于各层级测试 |
| 13. 向后兼容性 | N/A (新设计) | 对于v1版本可标记为N/A，但后续版本迭代需重点关注 |

## 7. 接口合并说明

### 7.1 原有接口分析
**AI课中学习报告接口** (`/api/v1/study/report`):
- 主要用于课程框架+文档组件学习完成后的报告展示
- 包含学习时长、交互记录、掌握度变化等数据
- 重点关注课程学习过程的统计和反馈

**巩固练习报告接口** (`/api/v1/learning_reports`):
- 主要用于巩固练习完成后的学习成果报告
- 包含作答统计、正确率、掌握度变化、再练推荐等
- 重点关注练习效果和知识巩固情况

### 7.2 合并设计原则
1. **统一接口路径**: 采用 `/api/v1/learning_reports` 作为主接口，通过参数区分报告类型
2. **兼容原有数据**: 保持原有接口的数据结构和字段，确保向后兼容
3. **扩展功能支持**: 新增历史报告查询和学习摘要功能，提升用户体验
4. **统一响应格式**: 采用一致的响应数据结构，便于前端统一处理

### 7.3 合并优势
1. **减少接口维护成本**: 统一的学习报告接口，降低开发和维护复杂度
2. **提升用户体验**: 一致的报告展示风格，用户学习体验更加统一
3. **便于功能扩展**: 统一的数据模型便于后续新增报告类型和功能
4. **优化性能**: 统一的缓存策略和数据聚合逻辑，提升系统性能

### 7.4 迁移建议
1. **渐进式迁移**: 新接口上线后，原有接口保持一段时间兼容期
2. **数据迁移**: 将原有报告数据迁移到新的统一数据模型中
3. **前端适配**: 前端逐步切换到新接口，统一报告展示组件
4. **监控验证**: 密切监控新接口性能和数据准确性

## 8. 附录 (Appendix)

### 8.1 引用文档
- **AI课中学习服务文档**: `be-s4-2-ai-ad-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md`
- **巩固练习服务文档**: `be-s5-2-ai-ad-学生端-巩固练习-claude-sonnet-4.md`
- **API设计指令文档**: `ai-templates/prompts/be-design/be-s5-2-gen-ai-ad.md`

### 8.2 参考标准
- OpenAPI 3.0.3 规范
- RESTful API 设计最佳实践
- JWT Token 认证标准
- ISO 8601 日期时间格式标准

### 8.3 版本说明
本文档基于以下原有接口设计合并而成：
- AI课中学习报告接口 `/api/v1/study/report` (来源: AI课中-课程框架+文档组件V0.9)
- 巩固练习报告接口 `/api/v1/learning_reports` (来源: 学生端-巩固练习)

通过统一接口设计，为学生提供一致的学习报告体验，支持多种学习场景下的报告查看需求。

--- API使用文档产出完毕，等待您的命令，指挥官
### 5.4 获取学习任务列表接口

**接口路径**: `GET /api/v1/learning_tasks`

**接口描述**: 获取学生的所有学习任务列表，包括课程任务、作业任务、自学记录

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| student_id | string | 是 | 学生ID，从Token中获取 | "student_789" |
| task_type | string | 否 | 任务类型筛选：course_task(课程任务)、homework_task(作业任务)、self_study(自学) | "course_task" |
| status | string | 否 | 完成状态筛选：pending(待完成)、completed(已完成)、overdue(已逾期) | "completed" |
| start_date | string | 否 | 开始日期筛选 | "2025-06-01" |
| end_date | string | 否 | 结束日期筛选 | "2025-06-06" |
| course_id | string | 否 | 课程ID筛选 | "course_101" |
| page | integer | 否 | 页码，默认1 | 1 |
| page_size | integer | 否 | 每页数量，默认20 | 20 |
| sort_by | string | 否 | 排序字段：created_at(创建时间)、deadline(截止时间)、completion_time(完成时间) | "deadline" |
| sort_type | string | 否 | 排序方式，默认desc | "asc" |

**响应数据结构**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": [
    {
      "task_id": "task_123456",
      "task_name": "AI基础概念学习",
      "task_type": "course_task",
      "course_id": "course_101",
      "course_name": "AI基础课程",
      "chapter_name": "第一章 AI概述",
      "created_at": "2025-06-01T09:00:00.000Z",
      "deadline": "2025-06-10T23:59:59.000Z",
      "status": "completed",
      "completion_time": "2025-06-06T10:30:00.000Z",
      "progress": 100,
      "estimated_duration": 1800,
      "actual_duration": 1650,
      "mastery_improvement": 12.7,
      "accuracy_rate": 85.0
    },
    {
      "task_id": "task_789012",
      "task_name": "机器学习基础作业",
      "task_type": "homework_task",
      "course_id": "course_102",
      "course_name": "机器学习入门",
      "created_at": "2025-06-03T14:00:00.000Z",
      "deadline": "2025-06-08T23:59:59.000Z",
      "status": "pending",
      "completion_time": null,
      "progress": 60,
      "estimated_duration": 3600,
      "actual_duration": 2100,
      "mastery_improvement": null,
      "accuracy_rate": null
    },
    {
      "task_id": "self_345678",
      "task_name": "深度学习自学",
      "task_type": "self_study",
      "course_id": "course_103",
      "course_name": "深度学习进阶",
      "chapter_name": "第三章 神经网络",
      "created_at": "2025-06-05T16:30:00.000Z",
      "deadline": null,
      "status": "completed",
      "completion_time": "2025-06-05T18:00:00.000Z",
      "progress": 100,
      "estimated_duration": null,
      "actual_duration": 5400,
      "mastery_improvement": 8.3,
      "accuracy_rate": 92.0
    }
  ],
  "page_info": {
    "current_page": 1,
    "page_size": 20,
    "total_count": 25,
    "total_pages": 2
  }
}
```

### 5.5 获取任务学习报告接口

**接口路径**: `GET /api/v1/learning_tasks/{task_id}/report`

**接口描述**: 获取指定任务的完整学习报告

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| task_id | string | 是 | 任务ID，URL路径参数 | "task_123456" |
| student_id | string | 否 | 学生ID，从Token中获取，用于权限验证 | "student_789" |

**响应数据结构**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "task_info": {
      "task_id": "task_123456",
      "task_name": "AI基础概念学习",
      "task_type": "course_task",
      "course_id": "course_101",
      "course_name": "AI基础课程",
      "chapter_name": "第一章 AI概述",
      "created_at": "2025-06-01T09:00:00.000Z",
      "deadline": "2025-06-10T23:59:59.000Z",
      "completion_time": "2025-06-06T10:30:00.000Z",
      "status": "completed"
    },
    "learning_report": {
      "report_id": "report_123456",
      "session_id": "session_123456",
      "generated_at": "2025-06-06T10:30:00.000Z",
      "study_statistics": {
        "total_duration": 1650,
        "study_start_time": "2025-06-06T09:00:00.000Z",
        "study_end_time": "2025-06-06T10:27:30.000Z",
        "component_count": 5,
        "completed_components": 5,
        "interaction_count": 23,
        "mode_switches": 3
      },
      "mastery_analysis": {
        "previous_mastery": 65.5,
        "current_mastery": 78.2,
        "mastery_change": 12.7,
        "mastery_level": "良好",
        "knowledge_points": [
          {
            "point_name": "AI基础概念",
            "previous_score": 60.0,
            "current_score": 75.0,
            "improvement": 15.0
          }
        ]
      },
      "performance_details": {
        "total_questions": 8,
        "correct_answers": 7,
        "accuracy_rate": 87.5,
        "average_answer_time": 38.2,
        "question_details": [
          {
            "question_id": "q_001",
            "is_correct": true,
            "answer_time": 35,
            "difficulty": "medium"
          }
        ]
      },
      "ip_feedback": {
        "feedback_type": "celebration",
        "feedback_message": "恭喜你完成了AI基础概念的学习任务！",
        "feedback_animation": "task_complete_star",
        "encouragement": "你的学习进度很棒，继续保持！"
      },
      "task_evaluation": {
        "completion_quality": "优秀",
        "time_efficiency": "高效",
        "learning_effectiveness": "显著提升",
        "next_recommendations": [
          {
            "action_type": "next_chapter",
            "title": "继续学习第二章",
            "description": "你已经很好地掌握了基础概念，可以进入下一章节了"
          }
        ]
      }
    }
  }
}
```
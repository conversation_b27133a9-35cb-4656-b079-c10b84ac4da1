# 课中练习推题策略服务数据库设计：信息提取与分析报告

## 1. 业务目标和范围分析

### 1.1 核心业务目标
构建一个高效的课中练习推题策略服务，根据预设的推题规则和学生答题情况，在AI课堂教学过程中动态推荐题目，以提升学生专注度、巩固知识点、增强课堂互动感。

### 1.2 设计范围
- **包含**：课中练习推题策略相关的核心数据表结构，包括练习组件、题目、答题记录等
- **不包含**：UI/前端实现、详细的第三方服务集成、题目内容本身的存储（由题库服务提供）

### 1.3 关键业务指标
- 题目推荐响应时间：P99<100ms
- 单课堂并发支持：最大200名学生同时在线练习
- 系统扩展性：支持2年内用户增长5倍，峰值QPS达5k

## 2. 核心业务实体与属性识别

### 2.1 练习组件 (ExerciseComponent)
- **业务含义**：课程中的练习单元，包含一组有序练习题
- **核心属性**：
  - 组件ID
  - 课程ID (外部服务依赖：课程服务)
  - 组件名称
  - 组件在课程中的位置/顺序
  - 创建时间
  - 更新时间
  - 状态 (启用/禁用)

### 2.2 练习组件题目配置 (ComponentQuestionConfig)
- **业务含义**：定义练习组件中题目的顺序和题目的配置信息
- **核心属性**：
  - 配置ID
  - 组件ID
  - 题目ID/题组ID
  - 顺序号
  - 题目类型 (单题/相似题组)
  - 创建时间
  - 更新时间

### 2.3 相似题组 (SimilarQuestionGroup)
- **业务含义**：围绕相同知识点的一组题目集合
- **核心属性**：
  - 题组ID
  - 题组名称
  - 关联知识点
  - 创建时间
  - 更新时间

### 2.4 题组题目关系 (GroupQuestionRelation)
- **业务含义**：定义相似题组中包含的题目
- **核心属性**：
  - 关系ID
  - 题组ID
  - 题目ID
  - 创建时间

### 2.5 答题记录 (AnswerRecord)
- **业务含义**：记录学生对特定题目的作答情况
- **核心属性**：
  - 记录ID
  - 会话ID
  - 学生ID (外部服务依赖：用户中心)
  - 组件ID
  - 题目ID
  - 用户答案
  - 是否正确
  - 作答时间
  - 作答耗时
  - 是否为相似题推送
  - 原题ID (如果是相似题推送)

### 2.6 练习会话 (ExerciseSession)
- **业务含义**：学生在特定练习组件中的作答过程状态跟踪
- **核心属性**：
  - 会话ID
  - 学生ID
  - 课程ID
  - 组件ID
  - 当前题目ID/位置
  - 会话状态 (进行中/已完成/已过期)
  - 已完成题目数
  - 总题目数
  - 正确题目数
  - 开始时间
  - 最后活动时间
  - 完成时间

### 2.7 相似题推送记录 (SimilarQuestionPush)
- **业务含义**：记录针对错题推送的相似题情况，确保每道错题的相似题只推送一次
- **核心属性**：
  - 记录ID
  - 会话ID
  - 学生ID
  - 原题ID (错题)
  - 推送题目ID (相似题)
  - 推送时间
  - 是否已作答

### 2.8 题目基本信息 (本服务中的缓存表，实际由题库服务提供)
- **业务含义**：缓存题目基本信息，减少对题库服务的依赖
- **核心属性**：
  - 题目ID
  - 题目类型
  - 难度等级
  - 知识点标签
  - 相似题关系标记
  - 题目版本号
  - 缓存更新时间

## 3. 实体间关系分析

### 3.1 主要关系映射
1. **课程 -> 练习组件**：一对多，一个课程可以包含多个练习组件
2. **练习组件 -> 题目配置**：一对多，一个练习组件包含多个有序的题目或题组
3. **题目配置 -> 题目/题组**：多对一，多个配置可以引用同一个题目或题组
4. **题组 -> 题目**：多对多，通过题组题目关系表关联
5. **学生 -> 练习会话**：一对多，一个学生可以有多个练习会话
6. **练习会话 -> 答题记录**：一对多，一个会话包含多个答题记录
7. **答题记录 -> 相似题推送**：一对多，一个错误答题记录可能触发多个相似题推送

### 3.2 业务关系约束
1. 练习组件中的题目顺序必须明确且固定
2. 相似题组中至少包含2道题目
3. 对于一道错题，其相似题只推送一次
4. 练习会话的状态变更需遵循特定的生命周期规则

## 4. 数据特征与存储选型分析

### 4.1 数据特征分析
- **练习组件、题目配置、相似题组**：
  - 数据量较小
  - 更新频率低
  - 读取频率高
  - 需要事务支持来确保配置一致性

- **练习会话**：
  - 需要频繁更新
  - 并发读写较高
  - 需要事务保障状态一致性
  - 数据量大但有明确的生命周期

- **答题记录**：
  - 几乎只写入，极少更新
  - 数据量大
  - 查询模式明确：按会话ID、学生ID、题目ID查询

- **相似题推送记录**：
  - 写入频繁
  - 几乎不更新
  - 主要用于确保业务逻辑（相似题只推送一次）

### 4.2 存储选型初步判断

**PostgreSQL 存储**：
- 练习组件表 (exercise_component)
- 练习组件题目配置表 (component_question_config)
- 相似题组表 (similar_question_group)
- 题组题目关系表 (group_question_relation)
- 练习会话表 (exercise_session)
- 题目基本信息缓存表 (question_info_cache)
- 相似题推送记录表 (similar_question_push)

**ClickHouse 存储**：
- 答题记录表 (answer_record) - 日志性质数据，只增不改，适合分析

## 5. 配置信息识别与分类

### 5.1 需要存储在数据库中的结构化配置
- 练习组件及其题目顺序配置
- 相似题组及其包含的题目关系
- 题目基本信息缓存（难度、知识点等）

### 5.2 适合 Nacos 管理的配置
- 推题策略参数配置：
  - 相似题推送控制开关
  - 会话超时设置
  - 缓存策略参数（过期时间等）
  - 题目随机选择算法权重参数
- 系统参数配置：
  - 服务连接参数
  - 限流参数
  - 降级策略参数

## 6. 外部服务依赖及关联ID

### 6.1 题库服务
- **依赖项**：题目内容、答案、难度、知识点、相似题关系
- **交互方式**：gRPC接口调用
- **存储的关联ID**：
  - 题目ID（question_id）
  - 知识点ID（knowledge_point_id）

### 6.2 用户中心
- **依赖项**：用户身份认证、基本信息
- **交互方式**：gRPC接口调用，JWT验证
- **存储的关联ID**：
  - 学生ID（student_id）

### 6.3 教师服务
- **依赖项**：课程信息、班级学生名单
- **交互方式**：gRPC接口调用
- **存储的关联ID**：
  - 课程ID（course_id）
  - 班级ID（class_id）

## 7. 核心用户场景的数据表使用分析

### 7.1 场景一：学生顺序作答练习题并遇到错题
**涉及表**：
- exercise_session（创建/更新会话状态）
- component_question_config（获取题目顺序）
- answer_record（记录答题情况）
- similar_question_push（记录相似题推送情况）
- question_info_cache（获取题目信息）

**数据流**：
1. 读取 exercise_session 创建或恢复会话
2. 从 component_question_config 获取当前题目
3. 写入 answer_record 记录答题结果
4. 如果答错，查询是否有相似题并检查 similar_question_push 是否已推送
5. 若需推送相似题，写入 similar_question_push 记录
6. 更新 exercise_session 状态

### 7.2 场景二：学生遇到相似题组
**涉及表**：
- exercise_session（会话状态）
- component_question_config（题目配置）
- similar_question_group（相似题组信息）
- group_question_relation（题组包含的题目）
- answer_record（答题记录）

**数据流**：
1. 从 component_question_config 识别当前配置为相似题组
2. 通过 similar_question_group 和 group_question_relation 随机选择一题
3. 写入 answer_record 记录答题情况
4. 更新 exercise_session 状态

### 7.3 场景三：答题数据分析（教师查看班级练习情况）
**涉及表**：
- answer_record（分析学生答题情况）
- exercise_session（分析完成情况）
- component_question_config（关联题目信息）
- question_info_cache（题目难度等信息）

**数据流**：
1. 按班级/课程ID查询相关 exercise_session
2. 关联 answer_record 获取详细答题数据
3. 通过 component_question_config 和 question_info_cache 补充题目信息
4. 生成答题分析报表

## 8. 初步数据库表清单

### 8.1 PostgreSQL 数据库表

| 表名 | 核心功能/用途 | 预定数据库 | 粒度考量备注 |
|------|--------------|-----------|------------|
| exercise_component | 存储练习组件基本信息 | PostgreSQL | 一个组件作为一条记录，组件内题目通过关联表管理 |
| component_question_config | 存储组件题目配置和顺序 | PostgreSQL | 细粒度设计，每个题目/题组在组件中的配置为一条记录 |
| similar_question_group | 存储相似题组信息 | PostgreSQL | 一个题组作为一条记录，包含的题目通过关联表管理 |
| group_question_relation | 存储题组与题目的关系 | PostgreSQL | 每个题组包含的每道题目对应一条记录 |
| exercise_session | 存储学生练习会话状态 | PostgreSQL | 一个学生一个组件一次练习对应一条会话记录 |
| similar_question_push | 记录相似题推送情况 | PostgreSQL | 每次推送相似题记录一条，确保相似题只推送一次 |
| question_info_cache | 缓存题目基本信息 | PostgreSQL | 题目元数据的本地缓存，减少对题库服务依赖 |

### 8.2 ClickHouse 数据库表

| 表名 | 核心功能/用途 | 预定数据库 | 粒度考量备注 |
|------|--------------|-----------|------------|
| answer_record | 存储学生答题记录 | ClickHouse | 每次答题一条记录，日志性质数据，适合统计分析 |

## 9. 关键设计考量和不确定点

### 9.1 关键设计考量
1. **会话状态管理**：练习会话表需要高效支持状态更新和并发控制
2. **答题记录与相似题推送**：分离为两张表以优化各自的数据访问模式
3. **题目信息缓存**：本地缓存题目基本信息以减少对外部服务依赖

### 9.2 不确定点与待确认项
1. 答题记录是否需要支持更新操作（例如批改后修改正确性判断）
2. 题组内随机选题的算法是否需要考虑难度平衡或其他因素
3. 会话恢复机制的具体细节和数据持久化要求
4. 练习组件的生命周期管理（是否需要版本控制或历史记录）

--- 等待您的命令，指挥官 
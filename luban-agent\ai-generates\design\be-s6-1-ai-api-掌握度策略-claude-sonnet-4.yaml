openapi: 3.0.3
info:
  title: 掌握度策略系统 API
  description: |
    掌握度策略系统是一个智能化学习评估平台，核心目标是通过精准的掌握度计算和分析，为学生提供个性化的学习路径规划和学习效果反馈。
    系统提供学科能力评估与分层、知识点掌握度实时计算、外化掌握度展示、薄弱知识点识别等核心功能。
    本文档遵循 OpenAPI 3.0.3 规范。
  version: v1.0.0
  contact:
    name: 掌握度策略系统开发团队
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: 本地开发服务器
  - url: https://api.learning-platform.com
    description: 生产环境服务器

tags:
  - name: LearningRecord
    description: 学习记录相关接口
  - name: LearningTask
    description: 学习任务相关接口
  - name: LearningGoal
    description: 学习目标相关接口
  - name: LearningRecommendation
    description: 学习建议相关接口

components:
  schemas:
    # 通用响应结构
    GenericResponse:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码，0表示成功，非0表示错误
          example: 0
        message:
          type: string
          description: 用户可读的响应信息
          example: "操作成功"
        data:
          type: object
          description: 响应数据，具体结构根据接口而定
          nullable: true
        pageInfo:
          $ref: '#/components/schemas/PageInfo'
          nullable: true

    GenericError:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码
          example: 1001
        message:
          type: string
          description: 用户可读的错误信息
          example: "参数错误"
        detail:
          type: string
          description: 错误详情，可选
          nullable: true
          example: "字段 [userId] 不能为空"
        data:
          type: object
          description: 错误时通常为空
          nullable: true
        pageInfo:
          type: object
          description: 错误时通常为空
          nullable: true

    PageInfo:
      type: object
      description: 分页信息
      properties:
        page:
          type: integer
          description: 当前页码，从1开始
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          example: 20
        total:
          type: integer
          description: 总记录数
          example: 100
        totalPages:
          type: integer
          description: 总页数
          example: 5

    # 答题记录相关Schema
    SubmitAnswerRecordRequest:
      type: object
      description: 提交答题记录请求体
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 12345
        questionId:
          type: integer
          format: int64
          description: 题目ID
          example: 67890
        knowledgePointId:
          type: integer
          format: int64
          description: 知识点ID
          example: 111
        answerResult:
          type: string
          description: 答题结果
          enum: ["correct", "incorrect", "partial"]
          example: "correct"
        answerTime:
          type: integer
          description: 答题用时（秒）
          minimum: 0
          example: 45
        learningScenarioType:
          type: string
          description: 学习场景类型
          enum: ["ai_course", "practice", "homework", "exam"]
          example: "ai_course"
        extensionData:
          type: object
          description: 扩展数据，可选
          nullable: true
          properties:
            courseId:
              type: integer
              format: int64
              description: 课程ID
              example: 456
            lessonId:
              type: integer
              format: int64
              description: 课时ID
              example: 789
          additionalProperties: true
      required:
        - userId
        - questionId
        - knowledgePointId
        - answerResult
        - answerTime
        - learningScenarioType

    SubmitAnswerRecordResponse:
      type: object
      description: 提交答题记录响应数据
      properties:
        recordId:
          type: string
          description: 答题记录ID
          example: "rec_123456789"
        learningProgressUpdates:
          type: array
          description: 学习进度更新列表
          items:
            $ref: '#/components/schemas/LearningProgressUpdate'
        needsAttention:
          type: boolean
          description: 是否需要重点关注
          example: false
        recommendations:
          type: array
          description: 学习建议列表
          items:
            type: string
          example: []

    LearningProgressUpdate:
      type: object
      description: 学习进度更新信息
      properties:
        knowledgePointId:
          type: integer
          format: int64
          description: 知识点ID
          example: 111
        previousProgress:
          type: number
          format: double
          description: 之前的学习进度
          minimum: 0
          maximum: 1
          example: 0.65
        currentProgress:
          type: number
          format: double
          description: 当前学习进度
          minimum: 0
          maximum: 1
          example: 0.72
        progressIncrement:
          type: number
          format: double
          description: 进度增量
          example: 0.07

    # 学习任务相关Schema
    CompleteTaskRequest:
      type: object
      description: 完成学习任务请求体
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 12345
        taskId:
          type: string
          description: 任务ID
          example: "task_ai_course_001"
        courseId:
          type: integer
          format: int64
          description: 课程ID
          example: 456
        completionStatus:
          type: string
          description: 完成状态
          enum: ["completed", "partially_completed", "failed"]
          example: "completed"
      required:
        - userId
        - taskId
        - courseId
        - completionStatus

    CompleteTaskResponse:
      type: object
      description: 完成学习任务响应数据
      properties:
        taskId:
          type: string
          description: 任务ID
          example: "task_ai_course_001"
        learningProgress:
          type: number
          format: double
          description: 学习进度
          minimum: 0
          maximum: 1
          example: 0.85
        updateSource:
          type: string
          description: 更新来源
          enum: ["course_completion", "task_completion", "manual_update"]
          example: "course_completion"
        progressUpdated:
          type: boolean
          description: 进度是否已更新
          example: true

    # 学习目标相关Schema
    SetLearningGoalRequest:
      type: object
      description: 设置学习目标请求体
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 12345
        goalType:
          type: string
          description: 目标类型
          enum: ["high_school_target", "middle_school_ranking"]
          example: "high_school_target"
        targetProgress:
          type: number
          format: double
          description: 目标进度
          minimum: 0
          maximum: 1
          example: 0.85
        subjectIds:
          type: array
          description: 学科ID列表
          items:
            type: integer
            format: int64
          example: [1, 2, 3]
      required:
        - userId
        - goalType
        - targetProgress
        - subjectIds

    SetLearningGoalResponse:
      type: object
      description: 设置学习目标响应数据
      properties:
        goalId:
          type: string
          description: 目标ID
          example: "goal_123456"
        targetProgressList:
          type: array
          description: 目标进度列表
          items:
            $ref: '#/components/schemas/SubjectTargetProgress'
        calculatedCourseCount:
          type: integer
          description: 计算的课程数量
          example: 25

    SubjectTargetProgress:
      type: object
      description: 学科目标进度
      properties:
        subjectId:
          type: integer
          format: int64
          description: 学科ID
          example: 1
        subjectName:
          type: string
          description: 学科名称
          example: "数学"
        targetProgress:
          type: number
          format: double
          description: 目标进度
          minimum: 0
          maximum: 1
          example: 0.85
        currentProgress:
          type: number
          format: double
          description: 当前进度
          minimum: 0
          maximum: 1
          example: 0.65

    # 学习建议相关Schema
    LearningRecommendation:
      type: object
      description: 学习建议信息
      properties:
        knowledgePointId:
          type: integer
          format: int64
          description: 知识点ID
          example: 111
        knowledgePointName:
          type: string
          description: 知识点名称
          example: "二次函数图像"
        currentProgress:
          type: number
          format: double
          description: 当前进度
          minimum: 0
          maximum: 1
          example: 0.45
        targetProgress:
          type: number
          format: double
          description: 目标进度
          minimum: 0
          maximum: 1
          example: 0.70
        gapRatio:
          type: number
          format: double
          description: 差距比例
          minimum: 0
          maximum: 1
          example: 0.36
        recommendationPriority:
          type: integer
          description: 推荐优先级，数值越小优先级越高
          minimum: 1
          maximum: 10
          example: 1
        recommendedActions:
          type: array
          description: 推荐行动
          items:
            type: string
          example: ["巩固练习", "观看相关视频"]

    LearningRecommendationListResponse:
      type: object
      description: 学习建议列表响应数据
      properties:
        recommendations:
          type: array
          description: 学习建议列表
          items:
            $ref: '#/components/schemas/LearningRecommendation'

  parameters:
    userIdQueryParameter:
      name: userId
      in: query
      required: true
      description: 用户ID
      schema:
        type: integer
        format: int64
        example: 12345

    pageQueryParameter:
      name: page
      in: query
      description: 页码，从1开始
      required: false
      schema:
        type: integer
        default: 1
        minimum: 1
        example: 1

    pageSizeQueryParameter:
      name: pageSize
      in: query
      description: 每页数量
      required: false
      schema:
        type: integer
        default: 10
        minimum: 1
        maximum: 100
        example: 10

    sortByQueryParameter:
      name: sortBy
      in: query
      description: 排序字段
      required: false
      schema:
        type: string
        default: "priority"
        enum: ["priority", "currentProgress", "gapRatio"]
        example: "priority"

    orderQueryParameter:
      name: order
      in: query
      description: 排序方式
      required: false
      schema:
        type: string
        default: "asc"
        enum: ["asc", "desc"]
        example: "asc"

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "请输入JWT Token, 格式为: Bearer {token}"

  responses:
    UnauthorizedError:
      description: 未认证或认证令牌无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 1002
                message: "用户未认证"
                detail: "JWT token无效或已过期"
                data: null
                pageInfo: null

    ForbiddenError:
      description: 无权限访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 1003
                message: "权限不足"
                detail: "用户无权限执行此操作"
                data: null
                pageInfo: null

    NotFoundError:
      description: 请求的资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 1004
                message: "资源不存在"
                detail: "请求的资源未找到"
                data: null
                pageInfo: null

    BadRequestError:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 1001
                message: "参数错误"
                detail: "请求参数格式不正确或缺少必需参数"
                data: null
                pageInfo: null

    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 5000
                message: "系统内部错误"
                detail: "服务器处理请求时发生内部错误"
                data: null
                pageInfo: null

paths:
  /api/v1/learning/answer-record:
    post:
      tags:
        - LearningRecord
      summary: 提交答题记录（通用）
      description: |
        通用答题记录接口，支持AI课、练习、作业、考试等多种学习场景的答题数据收集和学习进度更新。
        系统会根据答题结果实时计算学习进度，并判断是否需要重点关注。
      operationId: submitAnswerRecord
      requestBody:
        description: 答题记录信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitAnswerRecordRequest'
            examples:
              ai_course_example:
                summary: AI课程答题示例
                value:
                  userId: 12345
                  questionId: 67890
                  knowledgePointId: 111
                  answerResult: "correct"
                  answerTime: 45
                  learningScenarioType: "ai_course"
                  extensionData:
                    courseId: 456
                    lessonId: 789
              practice_example:
                summary: 练习答题示例
                value:
                  userId: 12345
                  questionId: 67891
                  knowledgePointId: 112
                  answerResult: "incorrect"
                  answerTime: 120
                  learningScenarioType: "practice"
      responses:
        '200':
          description: 答题记录提交成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/SubmitAnswerRecordResponse'
              examples:
                success_example:
                  summary: 成功提交答题记录
                  value:
                    code: 0
                    message: "学习记录提交成功"
                    data:
                      recordId: "rec_123456789"
                      learningProgressUpdates:
                        - knowledgePointId: 111
                          previousProgress: 0.65
                          currentProgress: 0.72
                          progressIncrement: 0.07
                      needsAttention: false
                      recommendations: []
                    pageInfo: null
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  /api/v1/learning/task/complete:
    post:
      tags:
        - LearningTask
      summary: 完成学习任务
      description: |
        学生完成AI课程、练习或作业等学习任务后，系统更新学习成果。
        学习成果是向学生展示的学习进度指标，遵循"只增不降"的原则。
      operationId: completeTask
      requestBody:
        description: 学习任务完成信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CompleteTaskRequest'
            examples:
              course_completion:
                summary: 课程完成示例
                value:
                  userId: 12345
                  taskId: "task_ai_course_001"
                  courseId: 456
                  completionStatus: "completed"
      responses:
        '200':
          description: 学习任务完成成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/CompleteTaskResponse'
              examples:
                success_example:
                  summary: 成功完成学习任务
                  value:
                    code: 0
                    message: "学习任务完成"
                    data:
                      taskId: "task_ai_course_001"
                      learningProgress: 0.85
                      updateSource: "course_completion"
                      progressUpdated: true
                    pageInfo: null
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  /api/v1/learning/goal:
    post:
      tags:
        - LearningGoal
      summary: 设置学习目标
      description: |
        学生设定学习目标，支持高中院校目标和初中排名目标。
        注意：V1.0版本暂不实现此功能，为后续版本预留接口。
      operationId: setLearningGoal
      requestBody:
        description: 学习目标设置信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SetLearningGoalRequest'
            examples:
              high_school_target:
                summary: 高中目标设置示例
                value:
                  userId: 12345
                  goalType: "high_school_target"
                  targetProgress: 0.85
                  subjectIds: [1, 2, 3]
      responses:
        '200':
          description: 学习目标设置成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/SetLearningGoalResponse'
              examples:
                success_example:
                  summary: 成功设置学习目标
                  value:
                    code: 0
                    message: "学习目标设置成功"
                    data:
                      goalId: "goal_123456"
                      targetProgressList:
                        - subjectId: 1
                          subjectName: "数学"
                          targetProgress: 0.85
                          currentProgress: 0.65
                        - subjectId: 2
                          subjectName: "物理"
                          targetProgress: 0.85
                          currentProgress: 0.58
                      calculatedCourseCount: 25
                    pageInfo: null
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  /api/v1/learning/recommendations:
    get:
      tags:
        - LearningRecommendation
      summary: 获取学习建议
      description: |
        智能识别和获取用户的学习建议列表，支持个人查询和班级统计。
        系统根据学习进度分析，识别需要重点关注的知识点并按优先级排序。
        支持分页查询和多种排序方式。
      operationId: getLearningRecommendations
      parameters:
        - $ref: '#/components/parameters/userIdQueryParameter'
        - $ref: '#/components/parameters/pageQueryParameter'
        - $ref: '#/components/parameters/pageSizeQueryParameter'
        - $ref: '#/components/parameters/sortByQueryParameter'
        - $ref: '#/components/parameters/orderQueryParameter'
      responses:
        '200':
          description: 成功获取学习建议
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/LearningRecommendation'
              examples:
                success_example:
                  summary: 成功获取学习建议
                  value:
                    code: 0
                    message: "查询成功"
                    data:
                      - knowledgePointId: 111
                        knowledgePointName: "二次函数图像"
                        currentProgress: 0.45
                        targetProgress: 0.70
                        gapRatio: 0.36
                        recommendationPriority: 1
                        recommendedActions: ["巩固练习", "观看相关视频"]
                      - knowledgePointId: 222
                        knowledgePointName: "三角函数性质"
                        currentProgress: 0.38
                        targetProgress: 0.70
                        gapRatio: 0.46
                        recommendationPriority: 2
                        recommendedActions: ["基础概念复习", "巩固练习"]
                    pageInfo:
                      page: 1
                      pageSize: 10
                      total: 5
                      totalPages: 1
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []
<svg xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="-343.2513427734375 -148.87872314453125 1821.8824462890625 2519.456787109375" style="max-width: 3840px; background-color: white; max-height: 3840px;" class="flowchart" width="100%" id="my-svg"><style>#my-svg{font-family:arial,sans-serif;font-size:14px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#ffffff;}#my-svg .error-text{fill:#000000;stroke:#000000;}#my-svg .edge-thickness-normal{stroke-width:2px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#000000;stroke:#000000;}#my-svg .marker.cross{stroke:#000000;}#my-svg svg{font-family:arial,sans-serif;font-size:14px;}#my-svg p{margin:0;}#my-svg .label{font-family:arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#000000;}#my-svg .cluster-label span{color:#000000;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ffffff;stroke:#000000;stroke-width:2px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#000000!important;stroke-width:0;stroke:#000000;}#my-svg .arrowheadPath{fill:#000000;}#my-svg .edgePath .path{stroke:#000000;stroke-width:2px;}#my-svg .flowchart-link{stroke:#000000;fill:none;}#my-svg .edgeLabel{background-color:hsl(-120, 0%, 80%);text-align:center;}#my-svg .edgeLabel p{background-color:hsl(-120, 0%, 80%);}#my-svg .edgeLabel rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#my-svg .labelBkg{background-color:rgba(204, 204, 204, 0.5);}#my-svg .cluster rect{fill:#ffffff;stroke:hsl(0, 0%, 90%);stroke-width:2px;}#my-svg .cluster text{fill:#000000;}#my-svg .cluster span{color:#000000;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:arial,sans-serif;font-size:12px;background:#ffffff;border:1px solid hsl(0, 0%, 90%);border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:hsl(-120, 0%, 80%);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:hsl(-120, 0%, 80%);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#my-svg .node .neo-node{stroke:#000000;}#my-svg [data-look="neo"].node rect,#my-svg [data-look="neo"].cluster rect,#my-svg [data-look="neo"].node polygon{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node path{stroke:url(#my-svg-gradient);stroke-width:2;}#my-svg [data-look="neo"].node .outer-path{filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node .neo-line path{stroke:#000000;filter:none;}#my-svg [data-look="neo"].node circle{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node circle .state-start{fill:#000000;}#my-svg [data-look="neo"].statediagram-cluster rect{fill:#ffffff;stroke:url(#my-svg-gradient);stroke-width:2;}#my-svg [data-look="neo"].icon-shape .icon{fill:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].icon-shape .icon-neo path{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .process&gt;*{fill:#d4f1f9!important;stroke:#333!important;stroke-width:1px!important;}#my-svg .process span{fill:#d4f1f9!important;stroke:#333!important;stroke-width:1px!important;}#my-svg .decision&gt;*{fill:#ffe6cc!important;stroke:#333!important;stroke-width:1px!important;}#my-svg .decision span{fill:#ffe6cc!important;stroke:#333!important;stroke-width:1px!important;}#my-svg .document&gt;*{fill:#e6f7d9!important;stroke:#333!important;stroke-width:1px!important;}#my-svg .document span{fill:#e6f7d9!important;stroke:#333!important;stroke-width:1px!important;}#my-svg .workflow&gt;*{fill:#f5f5f5!important;stroke:#333!important;stroke-width:1px!important;stroke-dasharray:5 5!important;}#my-svg .workflow span{fill:#f5f5f5!important;stroke:#333!important;stroke-width:1px!important;stroke-dasharray:5 5!important;}</style><g><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="7.75" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="4" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="11.5" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd-margin"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="1" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart-margin"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="10.75" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="12.25" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="-2" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd-margin"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart-margin"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><g class="root"><g class="clusters"/><g class="nodes"><g transform="translate(1334.631103515625, 1608.226196289062)" data-look="neo" data-et="cluster" data-id="s2" id="s2" class="cluster"><rect height="337.614013671875" width="271.999755859375" y="-168.8070068359375" x="-135.9998779296875" style=""/><g transform="translate(-28, -168.8070068359375)" class="cluster-label"><foreignObject height="21" width="56"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>质量保障</p></span></div></foreignObject></g></g><g transform="translate(680.2724914550781, 1087.871276855469)" data-look="neo" data-et="cluster" data-id="subGraph2" id="subGraph2" class="cluster"><rect height="616" width="648.9607543945312" y="-308" x="-324.4803771972656" style=""/><g transform="translate(-55.609375, -308)" class="cluster-label"><foreignObject height="21" width="111.21875"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>任务执行 (MVVM)</p></span></div></foreignObject></g></g><g transform="translate(401.2431411743164, 460.5)" data-look="neo" data-et="cluster" data-id="subGraph1" id="subGraph1" class="cluster"><rect height="525" width="539.5251922607422" y="-262.5" x="-269.7625961303711" style=""/><g transform="translate(-90.609375, -262.5)" class="cluster-label"><foreignObject height="21" width="181.21875"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>设计与计划优化 (考虑兼容性)</p></span></div></foreignObject></g></g><g transform="translate(1312.159545898438, 285.6359252929688)" data-look="neo" data-et="cluster" data-id="s1" id="s1" class="cluster"><rect height="490.5" width="257.1484375" y="-245.25" x="-128.57421875" style=""/><g transform="translate(-70, -245.25)" class="cluster-label"><foreignObject height="21" width="140"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>原子化组件生成与测试</p></span></div></foreignObject></g></g><g transform="translate(1312.159545898438, 227.7719421386719)" data-look="neo" data-et="node" data-node="true" data-id="E_AtomicUI" id="flowchart-E_AtomicUI-0" class="node default process"><rect stroke="url(#gradient)" height="45" width="217.1484375" y="-22.5" x="-108.57421875" data-id="E_AtomicUI" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-92.5703125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="185.140625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 185.148px; text-align: center;"><span class="nodeLabel"><p>Figma-to-Code: 原子化UI组件</p></span></div></foreignObject></g></g><g transform="translate(1312.159606933594, 103.4999237060547)" data-look="neo" data-et="node" data-node="true" data-id="D_UI" id="flowchart-D_UI-1" class="node default process"><rect stroke="url(#gradient)" height="45.00003051757812" width="140.1170654296875" y="-22.50001525878906" x="-70.05853271484375" data-id="D_UI" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-54.0546875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="108.109375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 108.117px; text-align: center;"><span class="nodeLabel"><p>UI设计稿 (Figma)</p></span></div></foreignObject></g></g><g transform="translate(1312.159545898438, 322.7719421386719)" data-look="neo" data-et="node" data-node="true" data-id="E1_CompTest" id="flowchart-E1_CompTest-2" class="node default process"><rect stroke="url(#gradient)" height="45" width="193.820556640625" y="-22.5" x="-96.9102783203125" data-id="E1_CompTest" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-80.90625, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="161.8125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 161.821px; text-align: center;"><span class="nodeLabel"><p>组件测试 (Storybook/Jest)</p></span></div></foreignObject></g></g><g transform="translate(1312.159606933594, 467.7719421386719)" data-look="neo" data-et="node" data-node="true" data-id="E2_CompLib" id="flowchart-E2_CompLib-3" class="node default process"><rect stroke="url(#gradient)" height="45" width="129.2183837890625" y="-22.5" x="-64.60919189453125" data-id="E2_CompLib" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-48.6015625, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="97.203125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 97.2184px; text-align: center;"><span class="nodeLabel"><p>组件库 (可复用)</p></span></div></foreignObject></g></g><g transform="translate(428.9942626953125, 415)" data-look="neo" data-et="node" data-node="true" data-id="H_ReviewLoop" id="flowchart-H_ReviewLoop-4" class="node default decision"><polygon style="fill:#ffe6cc !important;stroke:#333 !important;stroke-width:1px !important" transform="translate(-71.99993896484375,71.99993896484375)" class="label-container" points="71.99993896484375,0 143.9998779296875,-71.99993896484375 71.99993896484375,-143.9998779296875 0,-71.99993896484375"/><g transform="translate(-41.9921875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="83.984375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 83.9999px; text-align: center;"><span class="nodeLabel"><p>技术架构审查</p></span></div></foreignObject></g></g><g transform="translate(322.2714920043945, 245.4999923706055)" data-look="neo" data-et="node" data-node="true" data-id="F_AITD" id="flowchart-F_AITD-5" class="node default process"><rect stroke="url(#gradient)" height="45.00001525878906" width="179.0000457763672" y="-22.50000762939453" x="-89.5000228881836" data-id="F_AITD" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-73.5, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="147"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 147px; text-align: center;"><span class="nodeLabel"><p>AI辅助技术架构 (AI-TD)</p></span></div></foreignObject></g></g><g transform="translate(535.7168121337891, 245.4999923706055)" data-look="neo" data-et="node" data-node="true" data-id="I_Legacy" id="flowchart-I_Legacy-6" class="node default document"><rect stroke="url(#gradient)" height="45.00001525878906" width="147.8902282714844" y="-22.50000762939453" x="-73.9451141357422" data-id="I_Legacy" style="fill:#e6f7d9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-57.9375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="115.875"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 115.89px; text-align: center;"><span class="nodeLabel"><p>既有系统/历史逻辑</p></span></div></foreignObject></g></g><g transform="translate(428.9942779541016, 580.5)" data-look="neo" data-et="node" data-node="true" data-id="J_RefinedTD" id="flowchart-J_RefinedTD-7" class="node default process"><rect stroke="url(#gradient)" height="45" width="114.4452819824219" y="-22.5" x="-57.22264099121095" data-id="J_RefinedTD" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-41.21875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="82.4375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 82.4453px; text-align: center;"><span class="nodeLabel"><p>优化版 AI-TD</p></span></div></foreignObject></g></g><g transform="translate(438.994140625, 675.5)" data-look="neo" data-et="node" data-node="true" data-id="G_Plan" id="flowchart-G_Plan-8" class="node default process"><rect stroke="url(#gradient)" height="45" width="213.265625" y="-22.5" x="-106.6328125" data-id="G_Plan" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-90.6328125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="181.265625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 181.266px; text-align: center; width: 181.266px;"><span class="nodeLabel"><p>AI辅助实施计划 (Action Plan)</p></span></div></foreignObject></g></g><g transform="translate(771.3836059570312, 947.3712463378906)" data-look="neo" data-et="node" data-node="true" data-id="L_TaskDev" id="flowchart-L_TaskDev-9" class="node default process"><rect stroke="url(#gradient)" height="45.00006103515625" width="144.7889404296875" y="-22.500030517578125" x="-72.39447021484375" data-id="L_TaskDev" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-56.390625, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="112.78125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 112.789px; text-align: center;"><span class="nodeLabel"><p>开始单个Task开发</p></span></div></foreignObject></g></g><g transform="translate(526.7530670166016, 827.3712768554688)" data-look="neo" data-et="node" data-node="true" data-id="K_Tasks" id="flowchart-K_Tasks-10" class="node default process"><rect stroke="url(#gradient)" height="45" width="136.9999694824219" y="-22.5" x="-68.49998474121095" data-id="K_Tasks" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-52.4921875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="104.984375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 105px; text-align: center;"><span class="nodeLabel"><p>任务分解 (Tasks)</p></span></div></foreignObject></g></g><g transform="translate(781.3837280273438, 1042.371276855469)" data-look="neo" data-et="node" data-node="true" data-id="M_VMDev" id="flowchart-M_VMDev-11" class="node default process"><rect stroke="url(#gradient)" height="45" width="183.4376220703125" y="-22.5" x="-91.71881103515625" data-id="M_VMDev" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-75.71875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="151.4375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 151.438px; text-align: center;"><span class="nodeLabel"><p>ViewModel开发 (含测试)</p></span></div></foreignObject></g></g><g transform="translate(793.1864624023438, 1147.871276855469)" data-look="neo" data-et="node" data-node="true" data-id="N_ModelDev" id="flowchart-N_ModelDev-12" class="node default process"><rect stroke="url(#gradient)" height="66" width="231.999755859375" y="-33" x="-115.9998779296875" data-id="N_ModelDev" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-99.9921875, -21)" style="" class="label"><rect/><foreignObject height="42" width="199.984375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>Model开发 (含测试, 对接API/Mock, 转换层)</p></span></div></foreignObject></g></g><g transform="translate(855.6865844726562, 1253.371276855469)" data-look="neo" data-et="node" data-node="true" data-id="O_ViewInt" id="flowchart-O_ViewInt-13" class="node default process"><rect stroke="url(#gradient)" height="45" width="224.265625" y="-22.5" x="-112.1328125" data-id="O_ViewInt" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-96.1328125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="192.265625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 192.266px; text-align: center; width: 192.266px;"><span class="nodeLabel"><p>View层集成 (UI组件 + VM绑定)</p></span></div></foreignObject></g></g><g transform="translate(855.6865539550781, 1348.371276855469)" data-look="neo" data-et="node" data-node="true" data-id="P_DevDone" id="flowchart-P_DevDone-14" class="node default process"><rect stroke="url(#gradient)" height="45" width="116.0000610351562" y="-22.5" x="-58.0000305175781" data-id="P_DevDone" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-42, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="84"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 84.0001px; text-align: center;"><span class="nodeLabel"><p>任务代码完成</p></span></div></foreignObject></g></g><g transform="translate(1334.631103515625, 1509.79052734375)" data-look="neo" data-et="node" data-node="true" data-id="Q_AICodeReview" id="flowchart-Q_AICodeReview-15" class="node default process"><rect stroke="url(#gradient)" height="87" width="231.999755859375" y="-43.5" x="-115.9998779296875" data-id="Q_AICodeReview" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-99.9921875, -31.5)" style="" class="label"><rect/><foreignObject height="63" width="199.984375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel"><p>AI辅助代码审查<br />(结合规范、用户故事、架构设计)</p></span></div></foreignObject></g></g><g transform="translate(1330.050048828125, 1632.661865234375)" data-look="neo" data-et="node" data-node="true" data-id="S_CodeFix" id="flowchart-S_CodeFix-16" class="node default process"><rect stroke="url(#gradient)" height="45" width="87.999755859375" y="-22.5" x="-43.9998779296875" data-id="S_CodeFix" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-27.9921875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="55.984375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 55.9998px; text-align: center;"><span class="nodeLabel"><p>代码修复</p></span></div></foreignObject></g></g><g transform="translate(1330.050048828125, 1727.661865234375)" data-look="neo" data-et="node" data-node="true" data-id="T_TaskApproved" id="flowchart-T_TaskApproved-17" class="node default process"><rect stroke="url(#gradient)" height="45" width="87.999755859375" y="-22.5" x="-43.9998779296875" data-id="T_TaskApproved" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-27.9921875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="55.984375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 55.9998px; text-align: center;"><span class="nodeLabel"><p>任务通过</p></span></div></foreignObject></g></g><g transform="translate(220.3647155761719, -118.3787291049957)" data-look="neo" data-et="node" data-node="true" data-id="A_PRD" id="flowchart-A_PRD-18" class="node default process"><rect stroke="url(#gradient)" height="45.00000047683716" width="158.7734375" y="-22.50000023841858" x="-79.38671875" data-id="A_PRD" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-63.3828125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="126.765625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 126.773px; text-align: center;"><span class="nodeLabel"><p>产品需求文档 (PRD)</p></span></div></foreignObject></g></g><g transform="translate(-1.104480415582657, 4.106576919555664)" data-look="neo" data-et="node" data-node="true" data-id="C_API_DOC" id="flowchart-C_API_DOC-19" class="node default process"><rect stroke="url(#gradient)" height="45" width="82.57009220123291" y="-22.5" x="-41.285046100616455" data-id="C_API_DOC" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-25.28125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="50.5625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 50.5701px; text-align: center;"><span class="nodeLabel"><p>API文档</p></span></div></foreignObject></g></g><g transform="translate(222.2968978881836, 125.5)" data-look="neo" data-et="node" data-node="true" data-id="C_AIPRD" id="flowchart-C_AIPRD-20" class="node default process"><rect stroke="url(#gradient)" height="45" width="163.4528350830078" y="-22.5" x="-81.7264175415039" data-id="C_AIPRD" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-65.71875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="131.4375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 131.453px; text-align: center;"><span class="nodeLabel"><p>AI增强PRD (AI-PRD)</p></span></div></foreignObject></g></g><g transform="translate(1207.740051269531, 1971.227966308594)" data-look="neo" data-et="node" data-node="true" data-id="U_PlanProgress" id="flowchart-U_PlanProgress-61" class="node default decision"><polygon style="fill:#ffe6cc !important;stroke:#333 !important;stroke-width:1px !important" transform="translate(-114.00006103515625,114.00006103515625)" class="label-container" points="114.00006103515625,0 228.0001220703125,-114.00006103515625 114.00006103515625,-228.0001220703125 0,-114.00006103515625"/><g transform="translate(-84, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="168"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 168px; text-align: center;"><span class="nodeLabel"><p>更新计划进度（任务状态）</p></span></div></foreignObject></g></g><g transform="translate(812.5780334472656, 2170.2890625)" data-look="neo" data-et="node" data-node="true" data-id="V_Next" id="flowchart-V_Next-63" class="node default decision"><polygon style="fill:#ffe6cc !important;stroke:#333 !important;stroke-width:1px !important" transform="translate(-76.2891540527344,76.2891540527344)" class="label-container" points="76.2891540527344,0 152.5783081054688,-76.2891540527344 76.2891540527344,-152.5783081054688 0,-76.2891540527344"/><g transform="translate(-46.2890625, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="92.578125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 92.5783px; text-align: center;"><span class="nodeLabel"><p>所有Task完成?</p></span></div></foreignObject></g></g><g transform="translate(812.5780334472656, 2340.078125)" data-look="neo" data-et="node" data-node="true" data-id="W_PlanDone" id="flowchart-W_PlanDone-67" class="node default process"><rect stroke="url(#gradient)" height="45" width="116.0000610351562" y="-22.5" x="-58.0000305175781" data-id="W_PlanDone" style="fill:#d4f1f9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-42, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="84"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 84.0001px; text-align: center;"><span class="nodeLabel"><p>实施计划完成</p></span></div></foreignObject></g></g><g transform="translate(945.301025390625, 469.9849243164062)" data-look="neo" data-et="node" data-node="true" data-id="X_ArchGuide" id="flowchart-X_ArchGuide-68" class="node default document"><rect height="45" width="165.9998779296875" y="-22.5" x="-82.99993896484375" ry="22.5" rx="22.5" style="fill:#e6f7d9 !important;stroke:#333 !important;stroke-width:1px !important" class="basic label-container"/><g transform="translate(-62.9921875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="125.984375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 126px; text-align: center;"><span class="nodeLabel"><p>前端MVVM架构规范</p></span></div></foreignObject></g></g><g transform="translate(-203.5301551818848, 126.5575394630432)" data-look="neo" data-et="node" data-node="true" data-id="Z_GEN_AI_PRD_WORKFLOW" id="flowchart-Z_GEN_AI_PRD_WORKFLOW-72" class="node default workflow"><rect height="45.00000047683716" width="165.2340087890625" y="-22.50000023841858" x="-82.61700439453125" ry="22.50000023841858" rx="22.50000023841858" style="fill:#f5f5f5 !important;stroke:#333 !important;stroke-width:1px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-62.609375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="135.21875"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 125.234px; text-align: center;"><span class="nodeLabel"><p>生成 AI-PRD 工作流</p></span></div></foreignObject></g></g><g transform="translate(-188.4710998535156, 244.6029815673828)" data-look="neo" data-et="node" data-node="true" data-id="X_GEN_AI_TD_WORKFLOW" id="flowchart-X_GEN_AI_TD_WORKFLOW-74" class="node default workflow"><rect height="45" width="136.4452819824219" y="-22.5" x="-68.22264099121095" ry="22.5" rx="22.5" style="fill:#f5f5f5 !important;stroke:#333 !important;stroke-width:1px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-48.21875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="106.4375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 96.4453px; text-align: center;"><span class="nodeLabel"><p>生成 TD 工作流</p></span></div></foreignObject></g></g><g transform="translate(-205.2586669921875, 667.5368041992188)" data-look="neo" data-et="node" data-node="true" data-id="X_GEN_PLAN_WORKFLOW" id="flowchart-X_GEN_PLAN_WORKFLOW-76" class="node default workflow"><rect height="45" width="154.3517456054688" y="-22.5" x="-77.1758728027344" ry="22.5" rx="22.5" style="fill:#f5f5f5 !important;stroke:#333 !important;stroke-width:1px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-57.171875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="124.34375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 114.352px; text-align: center;"><span class="nodeLabel"><p>生成 PLAN 工作流</p></span></div></foreignObject></g></g><g transform="translate(-201.6331939697266, 822.0883178710938)" data-look="neo" data-et="node" data-node="true" data-id="X_GEN_TASK_WORKFLOW" id="flowchart-X_GEN_TASK_WORKFLOW-78" class="node default workflow"><rect height="45" width="153.3125" y="-22.5" x="-76.65625" ry="22.5" rx="22.5" style="fill:#f5f5f5 !important;stroke:#333 !important;stroke-width:1px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-56.65625, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="113.3125"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 113.312px; text-align: center; width: 113.312px;"><span class="nodeLabel"><p>拆解 TASK 工作流</p></span></div></foreignObject></g></g><g transform="translate(-229.2906036376953, 1036.698516845703)" data-look="neo" data-et="node" data-node="true" data-id="X_GEN_VM_DEV_TEST_WORKFLOW" id="flowchart-X_GEN_VM_DEV_TEST_WORKFLOW-80" class="node default workflow"><rect height="45.00006103515625" width="211.9215087890625" y="-22.500030517578125" x="-105.96075439453125" ry="22.500030517578125" rx="22.500030517578125" style="fill:#f5f5f5 !important;stroke:#333 !important;stroke-width:1px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-85.953125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="171.90625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 171.922px; text-align: center;"><span class="nodeLabel"><p>viewmodel 开发/测试工作流</p></span></div></foreignObject></g></g><g transform="translate(-218.4024963378906, 1147.731689453125)" data-look="neo" data-et="node" data-node="true" data-id="X_GEN_M_DEV_TEST_WORKFLOW" id="flowchart-X_GEN_M_DEV_TEST_WORKFLOW-82" class="node default workflow"><rect height="45" width="183.9141845703125" y="-22.5" x="-91.95709228515625" ry="22.5" rx="22.5" style="fill:#f5f5f5 !important;stroke:#333 !important;stroke-width:1px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-71.953125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="153.90625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 143.914px; text-align: center;"><span class="nodeLabel"><p>model 开发/测试工作流</p></span></div></foreignObject></g></g><g transform="translate(-225.0622253417969, 1260.10302734375)" data-look="neo" data-et="node" data-node="true" data-id="X_GEN_MVVM_COMB_TEST_WORKFLOW" id="flowchart-X_GEN_MVVM_COMB_TEST_WORKFLOW-84" class="node default workflow"><rect height="45" width="187.7808837890625" y="-22.5" x="-93.89044189453125" ry="22.5" rx="22.5" style="fill:#f5f5f5 !important;stroke:#333 !important;stroke-width:1px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-73.8828125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="157.765625"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 147.781px; text-align: center;"><span class="nodeLabel"><p>MVVM 集成/测试工作流</p></span></div></foreignObject></g></g><g transform="translate(-188.6545562744141, 1506.411743164062)" data-look="neo" data-et="node" data-node="true" data-id="X_GEN_MVVM_REVIEW_WORKFLOW" id="flowchart-X_GEN_MVVM_REVIEW_WORKFLOW-86" class="node default workflow"><rect height="45" width="168.3515625" y="-22.5" x="-84.17578125" ry="22.5" rx="22.5" style="fill:#f5f5f5 !important;stroke:#333 !important;stroke-width:1px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-64.171875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="128.34375"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 128.352px; text-align: center;"><span class="nodeLabel"><p>MVVM AI审查工作流</p></span></div></foreignObject></g></g><g transform="translate(-192.9386749267578, 1970.448608398438)" data-look="neo" data-et="node" data-node="true" data-id="X_GEN_UPDATE_TASK_WORKFLOW" id="flowchart-X_GEN_UPDATE_TASK_WORKFLOW-88" class="node default workflow"><rect height="45" width="166.000244140625" y="-22.5" x="-83.0001220703125" ry="22.5" rx="22.5" style="fill:#f5f5f5 !important;stroke:#333 !important;stroke-width:1px !important;stroke-dasharray:5 5 !important" class="basic label-container"/><g transform="translate(-63, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="126"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 126px; text-align: center;"><span class="nodeLabel"><p>更新任务进度工作流</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTc5LjY4MTgxOTYwODA3OSwieSI6LTk1Ljg3ODcyODg2NjU3NzE1fSx7IngiOjE3OS42ODE4MTk2MDgwNzksInkiOi01Ny4xMzYwNzU5NzM1MTA3NH0seyJ4IjozOS41Nzg0MTUxMjE0MTg3OCwieSI6LTU3LjEzNjA3NTk3MzUxMDc0fSx7IngiOjM5LjU3ODQxNTEyMTQxODc4LCJ5IjotMTguMzkzNDIzMDgwNDQ0MzR9XQ==" data-id="L_A_PRD_C_API_DOC_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 199.26034545898438 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_PRD_C_API_DOC_0" d="M179.681819608079,-95.87872886657715L179.681819608079,-64.20714378537622Q179.681819608079,-57.13607597351074 172.61075179621352,-57.13607597351074L46.649482933284254,-57.13607597351074Q39.57841512141878,-57.13607597351074 39.57841512141878,-50.06500816164527L39.57841512141878,-22.39342308044434"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MjIwLjU0Mjk3NjcyNTIyOTQsInkiOi05NS44Nzg3Mjg4NjY1NzcxNX0seyJ4IjoyMjAuNTQyOTc2NzI1MjI5NCwieSI6My41NjA2MzU1NjY3MTE0MjZ9LHsieCI6MjIyLjExODYzNjc0MTAxNTEsInkiOjMuNTYwNjM1NTY2NzExNDI2fSx7IngiOjIyMi4xMTg2MzY3NDEwMTUxLCJ5IjoxMDN9XQ==" data-id="L_A_PRD_C_AIPRD_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link edge-animation-fast" id="L_A_PRD_C_AIPRD_0" d="M220.5429767252294,-95.87872886657715L220.5429767252294,2.7728055588185754Q220.5429767252294,3.560635566711426 221.33080673312224,3.560635566711426L221.33080673312224,3.560635566711426Q222.1186367410151,3.560635566711426 222.1186367410151,4.348465574604276L222.1186367410151,99"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6Mjk5Ljc1MTQzNDMyNjE3MTksInkiOi0xMDIuMjQ1NDYyMzY1MjY5NH0seyJ4IjoyOTkuNzUxNDM0MzI2MTcxOSwieSI6LTEwMi4yNDU0NjIzNjUyNjk0fSx7IngiOjc3MS45MjYyNTQyNzI0NjA5LCJ5IjotNS40OTE1NDgyMjI4NDg5NzZ9LHsieCI6MTI0Mi4xMDEwNzQyMTg3NSwieSI6ODkuMjYyMzY1OTE5NTcxNX1d" data-id="L_A_PRD_D_UI_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 948.6130981445312 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_PRD_D_UI_0" d="M299.7514343261719,-102.2454623652694L299.7514343261719,-102.2454623652694L538.9532515481017,-53.230328333272126Q771.9262542724609,-5.491548222848976 1005.0530815418823,41.49029435626888L1238.1799088113037,88.47213693538674"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTMxMi4xNTk1OTU4ODI5LCJ5IjoxMjUuOTk5OTM4OTY0ODQzOH0seyJ4IjoxMzEyLjE1OTU5NTg4MjksInkiOjEyNS45OTk5Mzg5NjQ4NDM4fSx7IngiOjEzMTMuMTU5NTc2NDE2MDEyLCJ5IjoxNjYuNjM1OTQwNTUxNzU3OH0seyJ4IjoxMzEyLjE1OTU1Njk0OTEyMywieSI6MjA1LjI3MTk0MjEzODY3MTl9XQ==" data-id="L_D_UI_E_AtomicUI_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 66.28990173339844 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D_UI_E_AtomicUI_0" d="M1312.1595958829,125.9999389648438L1312.1595958829,125.9999389648438L1312.7333806877652,149.31671311092268Q1313.159576416012,166.6359405517578 1312.7113155499505,183.95461094362423L1312.263054683889,201.27328133549062"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTMxMi4xNTk1NDU4OTg0MzgsInkiOjI1MC4yNzE5NDIxMzg2NzE5fSx7IngiOjEzMTIuMTU5NTQ1ODk4NDM4LCJ5IjoyNTAuMjcxOTQyMTM4NjcxOX0seyJ4IjoxMzEzLjE1OTU0NTg5ODQzOCwieSI6Mjc2LjI3MTk0MjEzODY3MTl9LHsieCI6MTMxMi4xNTk1NDU4OTg0MzgsInkiOjMwMC4yNzE5NDIxMzg2NzE5fV0=" data-id="L_E_AtomicUI_E1_CompTest_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 37.02938461303711 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E_AtomicUI_E1_CompTest_0" d="M1312.159545898438,250.2719421386719L1312.159545898438,250.2719421386719L1312.7748145060143,266.2689259356546Q1313.159545898438,276.2719421386719 1312.7428069878624,286.27367599248714L1312.3260680772867,296.27540984630247"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTMxMi4xNTk1NTUzNjk0MSwieSI6MzQ1LjI3MTk0MjEzODY3MTl9LHsieCI6MTMxMi4xNTk1NTUzNjk0MSwieSI6MzQ1LjI3MTk0MjEzODY3MTl9LHsieCI6MTMxMy4xNTk1NzY0MTYwMTYsInkiOjM5Ni4yNzE5NDIxMzg2NzE5fSx7IngiOjEzMTIuMTU5NTk3NDYyNjIxLCJ5Ijo0NDUuMjcxOTQyMTM4NjcxOX1d" data-id="L_E1_CompTest_E2_CompLib_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 87.01400756835938 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E1_CompTest_E2_CompLib_0" d="M1312.15955536941,345.2719421386719L1312.15955536941,345.2719421386719L1312.7183754418031,373.77116601878487Q1313.159576416016,396.2719421386719 1312.7003939101348,418.7723584842246L1312.2412114042536,441.2727748297773"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MjIyLjI5Njg3NSwieSI6MTQ4fSx7IngiOjIyMi4yOTY4NzUsInkiOjE3M30seyJ4IjoyMjIuMjk2ODc1LCJ5IjoxOTh9LHsieCI6Mjc0LjkxNTA5MDQ2MDUyNjMsInkiOjIyM31d" data-id="L_C_AIPRD_F_AITD_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link edge-animation-fast" id="L_C_AIPRD_F_AITD_0" d="M222.296875,148L222.296875,173L222.296875,188.64115251320413Q222.296875,198 230.75011614271796,202.01630930882646L271.3021493779221,221.28341713464485"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MzIyLjI3MTQ4NDM3NSwieSI6MjY4fSx7IngiOjMyMi4yNzE0ODQzNzUsInkiOjI5M30seyJ4IjozMjIuMjcxNDg0Mzc1LCJ5IjozMTh9LHsieCI6MzkxLjc3NjA0MTk1Mzc4MjYsInkiOjM4MS4yMTgwOTg2NzEyMTc0fV0=" data-id="L_F_AITD_H_ReviewLoop_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 129.5587158203125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_F_AITD_H_ReviewLoop_0" d="M322.271484375,268L322.271484375,293L322.271484375,305.63716108676914Q322.271484375,318 331.41713921667593,326.3184604051125L388.81696276303524,378.52665847257174"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTM4LjY1NjUwNDYwMDI2NywieSI6MjY4fSx7IngiOjUzOC42NTY1MDQ2MDAyNjcsInkiOjI5M30seyJ4Ijo1MzguNjU2NTA0NjAwMjY3LCJ5IjozMTh9LHsieCI6NDcwLjE1MTk0NzAyMTQ4NDQsInkiOjM4MS4yMTgwOTg2NzEyMTc0fV0=" data-id="L_I_Legacy_H_ReviewLoop_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 128.8336181640625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_I_Legacy_H_ReviewLoop_0" d="M538.656504600267,268L538.656504600267,293L538.656504600267,305.53535068908144Q538.656504600267,318 529.4963126326151,326.45330500809877L473.0915217304585,378.50536932047856"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6Mzg0LjkyODQ1NzA2NTc4MzMsInkiOjQ0MC40NTE4NjQwNDgyMDUyfSx7IngiOjIyOC43NzkzMjczOTI1NzgxLCJ5Ijo1MjIuNX0seyJ4IjozNzQuMjUzOTM2NzY3NTc4MSwieSI6NTY0LjEyNjI1MTQzMzMwNjd9XQ==" data-id="L_H_ReviewLoop_J_RefinedTD_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 310.6409912109375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_ReviewLoop_J_RefinedTD_0" d="M384.9284570657833,440.4518640482052L233.5478749624384,519.9943796348829Q228.7793273925781,522.5 233.95824161341955,523.9818997379299L370.4082747562742,563.0258498271584"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDcwLjc0MjY3NDAwNzk5NjUsInkiOjQ0Ni4yNTE0NjY2MTcwMDM1fSx7IngiOjU3My4xODk0NTMxMjUsInkiOjUyMi41fSx7IngiOjQ4NC45MzE5NzczNzA2ODk3LCJ5Ijo1NTh9XQ==" data-id="L_H_ReviewLoop_J_RefinedTD_2" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 206.21527099609375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_H_ReviewLoop_J_RefinedTD_2" d="M470.7426740079965,446.2514666170035L568.5907064344474,519.0772697435995Q573.189453125,522.5 567.8709024348198,524.6392924269328L488.6430207690725,556.5072987923501"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NDI4Ljk5NDE0MDYyNSwieSI6NjAzfSx7IngiOjQyOC45OTQxNDA2MjUsInkiOjYyOH0seyJ4Ijo0MzQuMjU3Mjk4NTE5NzM2OCwieSI6NjUzfV0=" data-id="L_J_RefinedTD_G_Plan_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link edge-animation-fast" id="L_J_RefinedTD_G_Plan_0" d="M428.994140625,603L428.994140625,617.2259948427976Q428.994140625,628 431.2136986102687,638.5429004300265L433.43325659553744,649.085800860053"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NDUxLjk5NTc4MTc4MjUzMjIsInkiOjY5OH0seyJ4Ijo0NTEuOTk1NzgxNzgyNTMyMiwieSI6NzUxLjQzNTYzODQyNzczNDR9LHsieCI6NTEzLjc1MTQyNTg1OTA2OTMsInkiOjc1MS40MzU2Mzg0Mjc3MzQ0fSx7IngiOjUxMy43NTE0MjU4NTkwNjkzLCJ5Ijo4MDQuODcxMjc2ODU1NDY4OH1d" data-id="L_G_Plan_K_Tasks_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link edge-animation-fast" id="L_G_Plan_K_Tasks_0" d="M451.9957817825322,698L451.9957817825322,744.3645706158688Q451.9957817825322,751.4356384277344 459.0668495943977,751.4356384277344L506.6803580472038,751.4356384277344Q513.7514258590693,751.4356384277344 513.7514258590693,758.5067062395999L513.7514258590693,800.8712768554688"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6NTcyLjYyMTMwNDczMjgyODIsInkiOjg0OS44NzEyNzY4NTU0Njg4fSx7IngiOjU3Mi42MjEzMDQ3MzI4MjgyLCJ5Ijo4ODcuMzcxMjQ2MzM3ODkwNn0seyJ4Ijo3MjUuNTE1MzA2MDI4MDI1NiwieSI6ODg3LjM3MTI0NjMzNzg5MDZ9LHsieCI6NzI1LjUxNTMwNjAyODAyNTYsInkiOjkyNC44NzEyMTU4MjAzMTI1fV0=" data-id="L_K_Tasks_L_TaskDev_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link edge-animation-fast" id="L_K_Tasks_L_TaskDev_0" d="M572.6213047328282,849.8712768554688L572.6213047328282,880.3001785260251Q572.6213047328282,887.3712463378906 579.6923725446936,887.3712463378906L718.4442382161601,887.3712463378906Q725.5153060280256,887.3712463378906 725.5153060280256,894.4423141497562L725.5153060280256,920.8712158203125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTI4Ni43ODk1NTM0MDA0OTIsInkiOjQ5MC4yNzE5NDIxMzg2NzE5fSx7IngiOjEyODYuNzg5NTUzNDAwNDkyLCJ5Ijo0OTAuMjcxOTQyMTM4NjcxOX0seyJ4IjoxMDQyLjc3MTYyMzY1MDQ4MSwieSI6NzA4LjU3MTU3ODk3OTQ5MjJ9LHsieCI6Nzk2Ljc1MzY5MzkwMDQ3MDYsInkiOjkyNC44NzEyMTU4MjAzMTI1fV0=" data-id="L_E2_CompLib_L_TaskDev_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 641.993896484375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_E2_CompLib_L_TaskDev_0" d="M1286.789553400492,490.2719421386719L1286.789553400492,490.2719421386719L1163.3530869556787,600.6988104652015Q1042.771623650481,708.5715789794922 921.2646795523588,815.4008166239512L799.7577354542366,922.2300542684102"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NzczLjc1MjA1ODM3MjYzMjQsInkiOjk2OS44NzEyNzY4NTU0Njg4fSx7IngiOjc3My43NTIwNTgzNzI2MzI0LCJ5Ijo5OTQuODcxMjc2ODU1NDY4OH0seyJ4Ijo3NzkuMDE1Mjc4ODI0MTU3NSwieSI6OTk0Ljg3MTI3Njg1NTQ2ODh9LHsieCI6Nzc5LjAxNTI3ODgyNDE1NzUsInkiOjEwMTkuODcxMjc2ODU1NDY5fV0=" data-id="L_L_TaskDev_M_VMDev_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 40.2803955078125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_L_TaskDev_M_VMDev_0" d="M773.7520583726324,969.8712768554688L773.7520583726324,992.2396666297062Q773.7520583726324,994.8712768554688 776.3836685983949,994.8712768554688L776.3836685983949,994.8712768554688Q779.0152788241575,994.8712768554688 779.0152788241575,997.5028870812313L779.0152788241575,1015.871276855469"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NzgzLjkwMDg5ODg2NTYxMzgsInkiOjEwNjQuODcxMjc2ODU1NDY5fSx7IngiOjc4My45MDA4OTg4NjU2MTM4LCJ5IjoxMDg5Ljg3MTI3Njg1NTQ2OX0seyJ4Ijo3ODkuNDk0NjExODM5NTQ3NiwieSI6MTA4OS44NzEyNzY4NTU0Njl9LHsieCI6Nzg5LjQ5NDYxMTgzOTU0NzYsInkiOjExMTQuODcxMjc2ODU1NDY5fV0=" data-id="L_M_VMDev_N_ModelDev_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 40.48631286621094 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_M_VMDev_N_ModelDev_0" d="M783.9008988656138,1064.871276855469L783.9008988656138,1087.0744203685022Q783.9008988656138,1089.871276855469 786.6977553525808,1089.871276855469L786.6977553525808,1089.871276855469Q789.4946118395476,1089.871276855469 789.4946118395476,1092.6681333424358L789.4946118395476,1110.871276855469"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6ODEyLjczNjI2MzYxODY1LCJ5IjoxMTgwLjg3MTI3Njg1NTQ2OX0seyJ4Ijo4MTIuNzM2MjYzNjE4NjUsInkiOjEyMDUuODcxMjc2ODU1NDY5fSx7IngiOjg0Mi4zNTcxNzQ1NTI0NDc0LCJ5IjoxMjA1Ljg3MTI3Njg1NTQ2OX0seyJ4Ijo4NDIuMzU3MTc0NTUyNDQ3NCwieSI6MTIzMC44NzEyNzY4NTU0Njl9XQ==" data-id="L_N_ModelDev_O_ViewInt_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 61.292640686035156 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_N_ModelDev_O_ViewInt_0" d="M812.73626361865,1180.871276855469L812.73626361865,1198.8002090436034Q812.73626361865,1205.871276855469 819.8073314305154,1205.871276855469L835.286106740582,1205.871276855469Q842.3571745524474,1205.871276855469 842.3571745524474,1212.9423446673345L842.3571745524474,1226.871276855469"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6ODU1LjY4NjU3NzI0NDgwODcsInkiOjEyNzUuODcxMjc2ODU1NDY5fSx7IngiOjg1NS42ODY1NzcyNDQ4MDg3LCJ5IjoxMzAwLjg3MTI3Njg1NTQ2OX0seyJ4Ijo4NTUuNjg2NTYxMTgyOTI1NiwieSI6MTMwMC44NzEyNzY4NTU0Njl9LHsieCI6ODU1LjY4NjU2MTE4MjkyNTYsInkiOjEzMjUuODcxMjc2ODU1NDY5fV0=" data-id="L_O_ViewInt_P_DevDone_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 37.00035095214844 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_O_ViewInt_P_DevDone_0" d="M855.6865772448087,1275.871276855469L855.6865772448087,1300.8712688245273Q855.6865772448087,1300.871276855469 855.6865692138672,1300.871276855469L855.6865692138672,1300.871276855469Q855.6865611829256,1300.871276855469 855.6865611829256,1300.8712848864106L855.6865611829256,1321.871276855469"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6OTEzLjY4NjU4NDQ3MjY1NjIsInkiOjEzNjcuOTE5MDk2MDQwNDU2fSx7IngiOjEwNjYuMTU4OTA1MDI5Mjk3LCJ5IjoxMzY3LjkxOTA5NjA0MDQ1Nn0seyJ4IjoxMDY2LjE1ODkwNTAyOTI5NywieSI6MTQ3MC42OTQ5NTA2ODYwMjl9LHsieCI6MTIxOC42MzEyMjU1ODU5MzgsInkiOjE0NzAuNjk0OTUwNjg2MDI5fV0=" data-id="L_P_DevDone_Q_AICodeReview_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link edge-animation-fast" id="L_P_DevDone_Q_AICodeReview_0" d="M913.6865844726562,1367.919096040456L1059.0878372174316,1367.919096040456Q1066.158905029297,1367.919096040456 1066.158905029297,1374.9901638523215L1066.158905029297,1463.6238828741634Q1066.158905029297,1470.694950686029 1073.2299728411626,1470.694950686029L1214.631225585938,1470.694950686029"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTMzMy4wMDkyNzc5MzUwODUsInkiOjE1NTMuMjkwNTI3MzQzNzV9LHsieCI6MTMzMy4wMDkyNzc5MzUwODUsInkiOjE1NTMuMjkwNTI3MzQzNzV9LHsieCI6MTMzMi45NDkxMDEwMzE3NDUsInkiOjE1ODIuNzI2MTk2Mjg5MDYyfSx7IngiOjEzMzAuODg4OTI0MTI4NDA1LCJ5IjoxNjEwLjE2MTg2NTIzNDM3NX1d" data-id="L_Q_AICodeReview_S_CodeFix_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 43.9382438659668 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Q_AICodeReview_S_CodeFix_0" d="M1333.009277935085,1553.29052734375L1333.009277935085,1553.29052734375L1332.973135328449,1570.9697654684978Q1332.949101031745,1582.726196289062 1332.0687733175075,1594.4496457132577L1331.18844560327,1606.1730951374536"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTMzMC4wNTAwNDg4MjgxMjUsInkiOjE2NTUuMTYxODY1MjM0Mzc1fSx7IngiOjEzMzAuMDUwMDQ4ODI4MTI1LCJ5IjoxNjU1LjE2MTg2NTIzNDM3NX0seyJ4IjoxMzMxLjA1MDA0ODgyODEyNSwieSI6MTY4MS4xNjE4NjUyMzQzNzV9LHsieCI6MTMzMC4wNTAwNDg4MjgxMjUsInkiOjE3MDUuMTYxODY1MjM0Mzc1fV0=" data-id="L_S_CodeFix_T_TaskApproved_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 37.02932357788086 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_S_CodeFix_T_TaskApproved_0" d="M1330.050048828125,1655.161865234375L1330.050048828125,1655.161865234375L1330.6653174357014,1671.1588490313577Q1331.050048828125,1681.161865234375 1330.6333099175495,1691.1635990881903L1330.2165710069737,1701.1653329420055"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTMxOC43NTEzNzA4MzkxNDgsInkiOjE3NTAuMTYxODY1MjM0Mzc1fSx7IngiOjEzMTguNzUxMzcwODM5MTQ4LCJ5IjoxNzUwLjE2MTg2NTIzNDM3NX0seyJ4IjoxMjgzLjU1MDQ1MDEyOTkzNCwieSI6MTgyMy45OTk2MjQzMjk1MDF9LHsieCI6MTI0Ni45ODg0MzIyMjE4OTksInkiOjE4OTYuNDc2Mjg2MjI1ODA2fV0=" data-id="L_T_TaskApproved_U_PlanProgress_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link edge-animation-fast" id="L_T_TaskApproved_U_PlanProgress_0" d="M1318.751370839148,1750.161865234375L1318.751370839148,1750.161865234375L1300.1562778911152,1789.1670948075016Q1283.550450129934,1823.999624329501 1266.1702425816509,1858.4523022241667L1248.7900350333678,1892.9049801188323"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6MTEzMi40MjkzMTEwMTM2OCwieSI6MjAwOS45MTcyODcwODc4OTh9LHsieCI6OTk4LjEyMDAxOTYyMTEzOTEsInkiOjIwMDkuOTE3Mjg3MDg3ODk4fSx7IngiOjk5OC4xMjAwMTk2MjExMzkxLCJ5IjoyMTQ1LjIzMjY5NDc4MTMzM30seyJ4Ijo4NjMuODEwNzI4MjI4NTk4OCwieSI6MjE0NS4yMzI2OTQ3ODEzMzN9XQ==" data-id="L_U_PlanProgress_V_Next_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link edge-animation-fast" id="L_U_PlanProgress_V_Next_0" d="M1132.42931101368,2009.917287087898L1005.1910874330047,2009.917287087898Q998.1200196211391,2009.917287087898 998.1200196211391,2016.9883548997636L998.1200196211391,2138.1616269694678Q998.1200196211391,2145.232694781333 991.0489518092736,2145.232694781333L867.8107282285988,2145.232694781333"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd)" data-points="W3sieCI6Nzk5LjY5MDIxMTAzMDA2MjYsInkiOjIxMDcuODg3ODIyNDE3MjAzfSx7IngiOjc5OS42OTAyMTEwMzAwNjI2LCJ5IjoxNDc4Ljg3OTU0OTYzNjMzNn0seyJ4Ijo1MzEuNTQxOTM5Mjg2Njk2MiwieSI6MTQ3OC44Nzk1NDk2MzYzMzZ9LHsieCI6NTMxLjU0MTkzOTI4NjY5NjIsInkiOjg0OS44NzEyNzY4NTU0Njg4fV0=" data-id="L_V_Next_K_Tasks_0" data-et="edge" data-edge="true" style=";" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link edge-animation-fast" id="L_V_Next_K_Tasks_0" d="M799.6902110300626,2107.887822417203L799.6902110300626,1485.9506174482015Q799.6902110300626,1478.879549636336 792.6191432181972,1478.879549636336L538.6130070985616,1478.879549636336Q531.5419392866962,1478.879549636336 531.5419392866962,1471.8084818244704L531.5419392866962,853.8712768554688"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6ODEzLjA3ODEyNSwieSI6MjI0Ny4wNzgxMjV9LHsieCI6ODEyLjU3ODEyNSwieSI6MjI4Mi4wNzgxMjV9LHsieCI6ODEyLjU3ODEyNSwieSI6MjMxNy41NzgxMjV9XQ==" data-id="L_V_Next_W_PlanDone_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 57.5030517578125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_V_Next_W_PlanDone_0" d="M813.078125,2247.078125L812.8031020443299,2266.3297318969076Q812.578125,2282.078125 812.578125,2297.828125L812.578125,2313.578125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin__AA00FF)" data-points="W3sieCI6OTM3LjEwNDAxMjI4NjY3NjYsInkiOjQ5Mi40ODQ5MjQzMTY0MDYyfSx7IngiOjkzNy4xMDQwMTIyODY2NzY2LCJ5Ijo3MDguNjc4MDcwMDY4MzU5NH0seyJ4Ijo3NzkuNTgwNjMwMTc4ODkwMywieSI6NzA4LjY3ODA3MDA2ODM1OTR9LHsieCI6Nzc5LjU4MDYzMDE3ODg5MDMsInkiOjkyNC44NzEyMTU4MjAzMTI1fV0=" data-id="L_X_ArchGuide_L_TaskDev_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;stroke:#AA00FF;fill:none;;;stroke:#AA00FF;fill:none" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_ArchGuide_L_TaskDev_0" d="M937.1040122866766,492.4849243164062L937.1040122866766,701.6070022564938Q937.1040122866766,708.6780700683594 930.0329444748111,708.6780700683594L786.6516979907558,708.6780700683594Q779.5806301788903,708.6780700683594 779.5806301788903,715.7491378802249L779.5806301788903,920.8712158203125"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin__AA00FF)" data-points="W3sieCI6OTUzLjcyNTYwNjYxMjY0MjMsInkiOjQ5Mi40ODQ5MjQzMTY0MDYyfSx7IngiOjk1My43MjU2MDY2MTI2NDIzLCJ5Ijo5NzkuMzg3NzI1ODMwMDc4MX0seyJ4IjoxMzE4LjM0MzU3OTgxOTcyNSwieSI6OTc5LjM4NzcyNTgzMDA3ODF9LHsieCI6MTMxOC4zNDM1Nzk4MTk3MjUsInkiOjE0NjYuMjkwNTI3MzQzNzV9XQ==" data-id="L_X_ArchGuide_Q_AICodeReview_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;stroke:#AA00FF;fill:none;;;stroke:#AA00FF;fill:none" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_ArchGuide_Q_AICodeReview_0" d="M953.7256066126423,492.4849243164062L953.7256066126423,972.3166580182126Q953.7256066126423,979.3877258300781 960.7966744245077,979.3877258300781L1311.2725120078594,979.3877258300781Q1318.343579819725,979.3877258300781 1318.343579819725,986.4587936419437L1318.343579819725,1462.29052734375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin__AA00FF)" data-points="W3sieCI6ODgyLjg1NTEyMTQyODY4NDcsInkiOjQ0Ny40ODQ5MjQzMTY0MDYyfSx7IngiOjg4Mi44NTUxMjE0Mjg2ODQ3LCJ5IjozNTcuNzQyNDYyMTU4MjAzMX0seyJ4IjozODQuNzE3NDE3MTQwNzU0MywieSI6MzU3Ljc0MjQ2MjE1ODIwMzF9LHsieCI6Mzg0LjcxNzQxNzE0MDc1NDMsInkiOjI2OH1d" data-id="L_X_ArchGuide_F_AITD_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;stroke:#AA00FF;fill:none;;;stroke:#AA00FF;fill:none" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_ArchGuide_F_AITD_0" d="M882.8551214286847,447.4849243164062L882.8551214286847,364.8135299700686Q882.8551214286847,357.7424621582031 875.7840536168192,357.7424621582031L391.7884849526198,357.7424621582031Q384.7174171407543,357.7424621582031 384.7174171407543,350.67139434633765L384.7174171407543,272"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6LTEyMC45MTMxNTA3ODczNTM1LCJ5IjoxMjYuMzUyMzYwNTI1MTcyM30seyJ4Ijo5LjgyODY2NDc3OTY2MzA4NiwieSI6MTI2LjM1MjM2MDUyNTE3MjN9LHsieCI6OS44Mjg2NjQ3Nzk2NjMwODYsInkiOjEyNS43MDI5NjcxNjk2NTN9LHsieCI6MTQwLjU3MDQ4MDM0NjY3OTcsInkiOjEyNS43MDI5NjcxNjk2NTN9XQ==" data-id="L_Z_GEN_AI_PRD_WORKFLOW_C_AIPRD_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Z_GEN_AI_PRD_WORKFLOW_C_AIPRD_0" d="M-120.9131507873535,126.3523605251723L9.503968101903432,126.3523605251723Q9.828664779663086,126.3523605251723 9.828664779663086,126.02766384741264L9.828664779663086,126.02766384741264Q9.828664779663086,125.702967169653 10.15336145742274,125.702967169653L136.5704803466797,125.702967169653"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6LTEyMC4yNDg0NTg4NjIzMDQ3LCJ5IjoyNDQuNzIyODAwMTM1NDIyOX0seyJ4IjotMTIwLjI0ODQ1ODg2MjMwNDcsInkiOjI0NC43MjI4MDAxMzU0MjI5fSx7IngiOjU3LjI2MTUwNTEyNjk1MzEyLCJ5IjoyNDYuMDMyODAyMzY5NjE4NX0seyJ4IjoyMzIuNzcxNDY5MTE2MjEwOSwieSI6MjQ1LjM0MjgwNDYwMzgxNDF9XQ==" data-id="L_X_GEN_AI_TD_WORKFLOW_F_AITD_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_GEN_AI_TD_WORKFLOW_F_AITD_0" d="M-120.2484588623047,244.7228001354229L-120.2484588623047,244.7228001354229L-28.491819883324204,245.39995312088584Q57.26150512695312,246.0328023696185 143.01650257721442,245.69566620194848L228.77150002747575,245.35853003427846"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6LTEyOC4wODI3OTQxODk0NTMxLCJ5Ijo2NjguNDkwNzI1NjcwNjYzMX0seyJ4IjotMTI4LjA4Mjc5NDE4OTQ1MzEsInkiOjY2OC40OTA3MjU2NzA2NjMxfSx7IngiOjEwMy4xMzkyNjY5Njc3NzM0LCJ5Ijo2NzIuMzM2MzUyOTI5MDYwNH0seyJ4IjozMzIuMzYxMzI4MTI1LCJ5Ijo2NzQuMTgxOTgwMTg3NDU3Nn1d" data-id="L_X_GEN_PLAN_WORKFLOW_G_Plan_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_GEN_PLAN_WORKFLOW_G_Plan_0" d="M-128.0827941894531,668.4907256706631L-128.0827941894531,668.4907256706631L-9.459906422485886,670.4636317523032Q103.1392669677734,672.3363529290604 215.75036237310678,673.243063680324L328.3614577784401,674.1497744315875"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6LTEyNC45NzY5NDM5Njk3MjY2LCJ5Ijo4MjIuNjQ0MzAyODU2NDQ3fSx7IngiOi0xMjQuOTc2OTQzOTY5NzI2NiwieSI6ODIyLjY0NDMwMjg1NjQ0N30seyJ4IjoxNjcuNjM4MDY5MTUyODMyLCJ5Ijo4MjUuNzU5Mzc1OTEwNDk5OH0seyJ4Ijo0NTguMjUzMDgyMjc1MzkwNiwieSI6ODI2Ljg3NDQ0ODk2NDU1MjZ9XQ==" data-id="L_X_GEN_TASK_WORKFLOW_K_Tasks_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_GEN_TASK_WORKFLOW_K_Tasks_0" d="M-124.9769439697266,822.644302856447L-124.9769439697266,822.644302856447L24.337612837642126,824.2338513472267Q167.638069152832,825.7593759104998 310.94559043607546,826.3092386092952L454.25311171931884,826.8591013080907"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6LTEyMy4zMjk4NDkyNDMxNjQxLCJ5IjoxMDM3LjI5MzI1ODMwODIxMX0seyJ4IjotMTIzLjMyOTg0OTI0MzE2NDEsInkiOjEwMzcuMjkzMjU4MzA4MjExfSx7IngiOjI4NC4xNjc1MzM4NzQ1MTE3LCJ5IjoxMDQwLjU3NDg2NTc3MjQzM30seyJ4Ijo2ODkuNjY0OTE2OTkyMTg3NSwieSI6MTA0MS44NTY0NzMyMzY2NTV9XQ==" data-id="L_X_GEN_VM_DEV_TEST_WORKFLOW_M_VMDev_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_GEN_VM_DEV_TEST_WORKFLOW_M_VMDev_0" d="M-123.3298492431641,1037.293258308211L-123.3298492431641,1037.293258308211L83.42433885894073,1038.9582655329054Q284.1675338745117,1040.574865772433 484.9162354225492,1041.209348373427L685.6649369705867,1041.8438309744213"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6LTEyNi40NDU0MDQwNTI3MzQ0LCJ5IjoxMTQ3Ljc0NDM3ODQ1MjQ3NH0seyJ4IjotMTI2LjQ0NTQwNDA1MjczNDQsInkiOjExNDcuNzQ0Mzc4NDUyNDc0fSx7IngiOjI3Ni4zNzA1OTAyMDk5NjA5LCJ5IjoxMTQ4Ljc5OTgyNDM0MzE5NH0seyJ4Ijo2NzcuMTg2NTg0NDcyNjU2MiwieSI6MTE0Ny44NTUyNzAyMzM5MTN9XQ==" data-id="L_X_GEN_M_DEV_TEST_WORKFLOW_N_ModelDev_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_GEN_M_DEV_TEST_WORKFLOW_N_ModelDev_0" d="M-126.4454040527344,1147.744378452474L-126.4454040527344,1147.744378452474L77.96271766262254,1148.2799622305936Q276.3705902099609,1148.799824343194 474.77859289474475,1148.3322604312425L673.1865955795287,1147.864696519291"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6LTEzMS4xNzE3ODM0NDcyNjU2LCJ5IjoxMjU5LjUxODIwNDA5NjgyM30seyJ4IjotMTMxLjE3MTc4MzQ0NzI2NTYsInkiOjEyNTkuNTE4MjA0MDk2ODIzfSx7IngiOjMwNy4xOTA5OTQyNjI2OTUzLCJ5IjoxMjU3Ljc5Mzk2NTk4OTI1NX0seyJ4Ijo3NDMuNTUzNzcxOTcyNjU2MiwieSI6MTI1NC4wNjk3Mjc4ODE2ODd9XQ==" data-id="L_X_GEN_MVVM_COMB_TEST_WORKFLOW_O_ViewInt_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_GEN_MVVM_COMB_TEST_WORKFLOW_O_ViewInt_0" d="M-131.1717834472656,1259.518204096823L-131.1717834472656,1259.518204096823L91.003331558453,1258.6443096438145Q307.1909942626953,1257.793965989255 523.3724559552978,1255.9489157720695L739.5539176479003,1254.1038655548844"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6LTEwNC40Nzg3NzUwMjQ0MTQxLCJ5IjoxNTA2LjU5ODQ1MjU5NDU0NH0seyJ4IjotMTA0LjQ3ODc3NTAyNDQxNDEsInkiOjE1MDYuNTk4NDUyNTk0NTQ0fSx7IngiOjU1OC4wNzYyMjUyODA3NjE3LCJ5IjoxNTA5LjA2NTg0MDkwMzkyNn0seyJ4IjoxMjE4LjYzMTIyNTU4NTkzOCwieSI6MTUwOS41MzMyMjkyMTMzMDd9XQ==" data-id="L_X_GEN_MVVM_REVIEW_WORKFLOW_Q_AICodeReview_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_GEN_MVVM_REVIEW_WORKFLOW_Q_AICodeReview_0" d="M-104.4787750244141,1506.598452594544L-104.4787750244141,1506.598452594544L229.8009187945637,1507.843327070192Q558.0762252807617,1509.065840903926 886.3537259340037,1509.2981199207638L1214.6312265872457,1509.5303989376016"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6LTEwOS45Mzg1NTI4NTY0NDUzLCJ5IjoxOTcwLjQ5NDc5MDg2NzMxNX0seyJ4Ijo0OTIuMTgyNDE2NjkzNDY5NSwieSI6MTk3MC40OTQ3OTA4NjczMTV9LHsieCI6NDkyLjE4MjQxNjY5MzQ2OTUsInkiOjE5NzEuNjY0NTcwMjk5NTg1fSx7IngiOjEwOTQuMzAzMzg2MjQzMzg0LCJ5IjoxOTcxLjY2NDU3MDI5OTU4NX1d" data-id="L_X_GEN_UPDATE_TASK_WORKFLOW_U_PlanProgress_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 2 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-dotted edge-thickness-normal edge-pattern-solid flowchart-link" id="L_X_GEN_UPDATE_TASK_WORKFLOW_U_PlanProgress_0" d="M-109.9385528564453,1970.494790867315L491.59752697733455,1970.494790867315Q492.1824166934695,1970.494790867315 492.1824166934695,1971.07968058345L492.1824166934695,1971.07968058345Q492.1824166934695,1971.664570299585 492.7673064096045,1971.664570299585L1090.303386243384,1971.664570299585"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_A_PRD_C_API_DOC_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_A_PRD_C_AIPRD_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_A_PRD_D_UI_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D_UI_E_AtomicUI_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_E_AtomicUI_E1_CompTest_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_E1_CompTest_E2_CompLib_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C_AIPRD_F_AITD_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_F_AITD_H_ReviewLoop_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_I_Legacy_H_ReviewLoop_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(239.88014, 516.66711)" class="edgeLabel"><g transform="translate(-28, -10.5)" data-id="L_H_ReviewLoop_J_RefinedTD_0" class="label"><foreignObject height="21" width="56"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"><p>迭代优化</p></span></div></foreignObject></g></g><g transform="translate(560.12249, 512.77459)" class="edgeLabel"><g transform="translate(-14, -10.5)" data-id="L_H_ReviewLoop_J_RefinedTD_2" class="label"><foreignObject height="21" width="28"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"><p>通过</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_J_RefinedTD_G_Plan_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_G_Plan_K_Tasks_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_K_Tasks_L_TaskDev_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_E2_CompLib_L_TaskDev_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_L_TaskDev_M_VMDev_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_M_VMDev_N_ModelDev_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_N_ModelDev_O_ViewInt_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_O_ViewInt_P_DevDone_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_P_DevDone_Q_AICodeReview_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Q_AICodeReview_S_CodeFix_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_S_CodeFix_T_TaskApproved_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_T_TaskApproved_U_PlanProgress_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_U_PlanProgress_V_Next_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(665.61608, 1478.87955)" class="edgeLabel"><g transform="translate(-7, -10.5)" data-id="L_V_Next_K_Tasks_0" class="label"><foreignObject height="21" width="14"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"><p>否</p></span></div></foreignObject></g></g><g transform="translate(812.57813, 2282.32634)" class="edgeLabel"><g transform="translate(-7, -10.5)" data-id="L_V_Next_W_PlanDone_0" class="label"><foreignObject height="21" width="14"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"><p>是</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_ArchGuide_L_TaskDev_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="stroke: rgb(170, 0, 255); display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";stroke:#AA00FF;color:none"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_ArchGuide_Q_AICodeReview_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="stroke: rgb(170, 0, 255); display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";stroke:#AA00FF;color:none"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_ArchGuide_F_AITD_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" class="labelBkg" style="stroke: rgb(170, 0, 255); display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="edgeLabel" style=";stroke:#AA00FF;color:none"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Z_GEN_AI_PRD_WORKFLOW_C_AIPRD_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_GEN_AI_TD_WORKFLOW_F_AITD_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_GEN_PLAN_WORKFLOW_G_Plan_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_GEN_TASK_WORKFLOW_K_Tasks_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_GEN_VM_DEV_TEST_WORKFLOW_M_VMDev_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_GEN_M_DEV_TEST_WORKFLOW_N_ModelDev_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_GEN_MVVM_COMB_TEST_WORKFLOW_O_ViewInt_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_GEN_MVVM_REVIEW_WORKFLOW_Q_AICodeReview_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_X_GEN_UPDATE_TASK_WORKFLOW_U_PlanProgress_0" class="label"><foreignObject height="0" width="0"><div xmlns="http://www.w3.org/1999/xhtml" style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg"><span class="edgeLabel"></span></div></foreignObject></g></g></g></g><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="11.5" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd-margin__AA00FF"><path fill="#AA00FF" stroke="#AA00FF" style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker></g><defs><filter width="130%" height="130%" id="drop-shadow"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="4" dx="4"/></filter></defs><defs><filter width="150%" height="150%" id="drop-shadow-small"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="2" dx="2"/></filter></defs><linearGradient y2="0%" x2="100%" y1="0%" x1="0%" gradientUnits="objectBoundingBox" id="my-svg-gradient"><stop stop-opacity="1" stop-color="#0042eb" offset="0%"/><stop stop-opacity="1" stop-color="#eb0042" offset="100%"/></linearGradient><script xmlns=""/></svg>
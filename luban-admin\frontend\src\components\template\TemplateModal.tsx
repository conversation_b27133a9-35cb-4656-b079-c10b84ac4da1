// @ts-nocheck
// @ts-nocheck
'use client';

import React, { useEffect, useRef, useState } from 'react';

interface TemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// 模拟模板数据
const MOCK_TEMPLATES = [
  { id: 1, name: '基础PRD文档', category: '产品', useCount: 1240 },
  { id: 2, name: '功能需求分析', category: '产品', useCount: 987 },
  { id: 3, name: '技术方案设计', category: '开发', useCount: 856 },
  { id: 4, name: 'API接口文档', category: '开发', useCount: 756 },
  { id: 5, name: '数据库设计', category: '开发', useCount: 654 },
  { id: 6, name: '用户故事地图', category: '产品', useCount: 543 }
];

const TemplateModal = ({ isOpen, onClose }: TemplateModalProps) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [activeCategory, setActiveCategory] = useState('全部');
  const [selectedTemplate, setSelectedTemplate] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // 点击外部关闭弹窗
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  // 过滤模板
  const filteredTemplates = MOCK_TEMPLATES.filter(template => {
    const matchesCategory = activeCategory === '全部' || template.category === activeCategory;
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  // 选择模板
  const handleSelectTemplate = (id: number) => {
    setSelectedTemplate(id === selectedTemplate ? null : id);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div 
        ref={modalRef}
        className="bg-white rounded-lg shadow-xl w-full max-w-4xl overflow-hidden"
      >
        <div className="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 className="text-lg font-medium">模板中心</h3>
          <button 
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        <div className="flex">
          {/* 左侧分类 */}
          <div className="w-48 border-r border-gray-200 p-4 bg-gray-50">
            <h4 className="font-medium text-gray-600 mb-3">模板分类</h4>
            <ul className="space-y-2">
              {['全部', '产品', '开发', '设计', '运营'].map((category) => (
                <li key={category}>
                  <button
                    className={`w-full text-left px-3 py-2 rounded-md ${
                      activeCategory === category 
                        ? 'bg-blue-100 text-blue-700 font-medium' 
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    onClick={() => setActiveCategory(category)}
                  >
                    {category}
                  </button>
                </li>
              ))}
            </ul>
          </div>
          
          {/* 右侧内容 */}
          <div className="flex-1 p-6 overflow-auto max-h-[70vh]">
            {/* 搜索框 */}
            <div className="mb-4">
              <div className="relative rounded-md shadow-sm">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="搜索模板"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>
            
            {/* 模板列表 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {filteredTemplates.length > 0 ? (
                filteredTemplates.map((template) => (
                  <div 
                    key={template.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedTemplate === template.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-blue-300'
                    }`}
                    onClick={() => handleSelectTemplate(template.id)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium text-gray-800">{template.name}</h4>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded mt-1 inline-block">
                          {template.category}
                        </span>
                      </div>
                      {selectedTemplate === template.id && (
                        <div className="bg-blue-500 rounded-full p-1">
                          <svg className="h-4 w-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                      )}
                    </div>
                    <div className="mt-2 text-sm text-gray-600">
                      已被使用 {template.useCount} 次
                    </div>
                    
                    {/* 流程图预览 */}
                    <div className="mt-3 bg-gray-50 rounded-md p-2 h-32 flex items-center justify-center border border-gray-200">
                      <div className="text-center">
                        <svg className="h-10 w-10 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                        </svg>
                        <div className="text-sm text-gray-500 mt-2">点击预览模板详情</div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="col-span-2 text-center py-8 text-gray-500">
                  没有找到匹配的模板
                </div>
              )}
            </div>
          </div>
        </div>
        
        <div className="p-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            取消
          </button>
          <button
            onClick={onClose}
            disabled={!selectedTemplate}
            className={`px-4 py-2 rounded-md text-white ${
              !selectedTemplate ? 'bg-blue-400 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            使用模板
          </button>
        </div>
      </div>
    </div>
  );
};

export default TemplateModal; 
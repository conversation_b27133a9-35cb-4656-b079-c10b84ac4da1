import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginRequest } from '@/types/user';
import { authApi } from '@/lib/api/auth';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => void;
  fetchUser: () => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      
      login: async (credentials: LoginRequest) => {
        try {
          set({ isLoading: true, error: null });
          
          // 登录获取token
          await authApi.login(credentials);
          
          // 获取用户信息
          const user = await authApi.getCurrentUser();
          
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false 
          });
        } catch (error) {
          console.error('Login error:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Login failed', 
            isLoading: false,
            isAuthenticated: false,
            user: null
          });
        }
      },
      
      logout: () => {
        authApi.logout();
        set({ 
          user: null, 
          isAuthenticated: false,
          error: null 
        });
      },
      
      fetchUser: async () => {
        try {
          set({ isLoading: true, error: null });
          
          if (!authApi.isAuthenticated()) {
            set({ isLoading: false, isAuthenticated: false });
            return;
          }
          
          const user = await authApi.getCurrentUser();
          
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false 
          });
        } catch (error) {
          console.error('Fetch user error:', error);
          
          // 如果获取失败，可能是token过期
          authApi.logout();
          
          set({ 
            error: error instanceof Error ? error.message : 'Authentication failed', 
            isLoading: false,
            isAuthenticated: false,
            user: null
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user,
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
); 
# 数据库设计文档 - 运营工具老师学生课程题目反馈系统 - V1.0

**模板版本**: v1.3
**最后更新日期**: 2025-05-30
**设计负责人**: AI数据库设计工程师
**评审人**: 待填写
**状态**: 草稿

## 📋 输出格式总体要求
* **IMPORTANT AI:** 本模板定义了所有输出的标准化格式，必须严格遵循：
  - 所有表格都有固定的列结构，不得随意增减
  - ER图必须使用Mermaid语法，按照标准格式绘制
  - CRUD分析必须使用标准7列表格格式
  - 检查清单必须逐项填写，使用✅/❌/☐/N/A标记
  - 所有DDL代码必须包含完整中文注释

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-05-30 | AI数据库设计工程师 | 初始草稿     |

## 1. 项目概述与设计目标

### 1.1 项目背景与范围
本项目是一个智能化的教育内容质量反馈平台，核心目标是建立完整的课程内容质量反馈闭环，通过用户反馈驱动课程质量提升。系统支持教师端和学生端用户快速提交课程问题，通过智能Agent自动分类审核，生产后台高效处理，并及时反馈处理结果给用户，同时提供积分激励机制提升用户参与积极性。

本次数据库设计覆盖反馈收集、智能审核、后台管理、消息通知、积分管理等核心业务模块的数据存储需求。

### 1.2 设计目标回顾
* 提供完整的数据库创建语句（如果适用）。
* 提供完整的、符合规范的表创建语句（DDL）。
* 清晰描述表间关系，并提供 ER 图。
* 详细说明核心用户场景/用户故事所涉及的表以及其 CRUD 操作方式。
* 阐述关键表的索引策略及其设计原因。
* 对核心表进行数据量预估和增长趋势分析。
* 明确数据生命周期管理/归档策略（如果适用）。
* 记录特殊设计考量与权衡。
* 总结设计如何体现对应数据库（PostgreSQL/ClickHouse）的关键规范。

## 2. 数据库环境与总体说明

### 2.1 目标数据库类型
* PostgreSQL 17（主要业务数据）
* ClickHouse 23.8.16.16（分析数据）

### 2.2 数据库创建语句 (如果适用)
* **PostgreSQL 示例**:
  ```sql
  -- CREATE DATABASE feedback_system
  -- WITH
  -- OWNER = feedback_admin
  -- ENCODING = 'UTF8'
  -- LC_COLLATE = 'zh_CN.UTF-8'
  -- LC_CTYPE = 'zh_CN.UTF-8'
  -- TABLESPACE = pg_default
  -- CONNECTION LIMIT = -1
  -- TEMPLATE = template0;
  -- COMMENT ON DATABASE feedback_system IS '运营工具老师学生课程题目反馈系统数据库';
  ```
* **ClickHouse 示例**:
  ```sql
  -- CREATE DATABASE IF NOT EXISTS feedback_analytics
  -- ENGINE = Atomic;
  -- COMMENT '反馈系统分析数据库';
  ```

### 2.3 数据库选型决策摘要

**技术选型决策表**：
| 实体/表名 | 数据库类型 | 选型依据 | 数据特征 | 查询模式 | 预估数据量 |
|---------|-----------|---------|---------|---------|-----------|
| tbl_feedback_records | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 中表(100万) |
| tbl_feedback_processing | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 状态更新+查询 | 中表(100万) |
| tbl_point_records | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 积分计算+查询 | 中表(50万) |
| tbl_notifications | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 消息推送+查询 | 大表(500万) |
| tbl_agent_audit_records | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 审核结果查询 | 中表(100万) |
| tbl_feedback_analytics | ClickHouse | 仅INSERT分析 | 时序数据 | 聚合分析 | 大表(千万级) |
| tbl_user_behavior_logs | ClickHouse | 仅INSERT分析 | 时序数据 | 行为分析 | 大表(千万级) |

## 3. 数据库表详细设计 - PostgreSQL

### 3.1 数据库表清单

#### PostgreSQL表清单
| 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 | 预定数据库类型 |
|------|------------------|-------------|---------|--------------|
| `tbl_feedback_records` | 反馈记录主表，存储用户提交的各类反馈信息 | 中表(100万) | 频繁CRUD操作，需要事务保证 | PostgreSQL |
| `tbl_feedback_processing` | 反馈处理记录表，存储生产后台的处理状态和结果 | 中表(100万) | 频繁状态更新，需要事务保证 | PostgreSQL |
| `tbl_point_records` | 积分记录表，存储用户积分获得和消费记录 | 中表(50万) | 积分计算需要事务保证 | PostgreSQL |
| `tbl_notifications` | 消息通知表，存储各类通知消息 | 大表(500万) | 频繁读写，需要事务保证 | PostgreSQL |
| `tbl_agent_audit_records` | Agent审核记录表，存储智能审核的结果 | 中表(100万) | 审核结果查询，需要事务保证 | PostgreSQL |

#### ClickHouse表清单  
| 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 | 预定数据库类型 |
|------|------------------|-------------|---------|--------------|
| `tbl_feedback_analytics` | 反馈分析数据表，存储反馈处理的统计分析数据 | 大表(千万级) | 仅INSERT分析查询，时序数据 | ClickHouse |
| `tbl_user_behavior_logs` | 用户行为日志表，存储用户操作行为数据 | 大表(千万级) | 仅INSERT分析查询，行为分析 | ClickHouse |

---

### 3.2 表名: `tbl_feedback_records` (反馈记录主表)

#### 3.2.1 表功能说明
存储用户提交的各类反馈信息，包括教师端的课程反馈、题目反馈、视频MV反馈、其他建议反馈，以及学生端的课程反馈、题目反馈、其他bug反馈。是整个反馈系统的核心数据表。支持反馈类型多选功能。

#### 3.2.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_feedback_records (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id BIGINT NOT NULL,                                    -- 提交反馈的用户ID（引用公共服务）
  user_type VARCHAR(20) NOT NULL DEFAULT 'teacher',           -- 用户类型：teacher/student
  feedback_types JSONB NOT NULL,                              -- 反馈类型（多选）：["课程反馈","题目反馈","视频MV反馈","其他建议反馈","其他bug反馈"]
  content_type VARCHAR(20) NOT NULL DEFAULT 'text',           -- 内容类型：text/non_text
  course_id BIGINT NULL,                                       -- 关联课程ID（引用公共服务）
  question_id BIGINT NULL,                                     -- 关联题目ID（引用公共服务）
  feedback_content TEXT NOT NULL,                             -- 反馈内容描述
  screenshot_urls TEXT NULL,                                   -- 截图信息（JSON格式存储多个URL）
  feedback_status VARCHAR(20) NOT NULL DEFAULT 'pending',     -- 反馈状态：pending/processing/accepted/rejected
  priority_level SMALLINT NOT NULL DEFAULT 1,                 -- 优先级：1-低，2-中，3-高
  source_platform VARCHAR(20) NOT NULL DEFAULT 'web',         -- 来源平台：web/mobile/app
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,                         -- 记录状态：0-禁用，1-启用
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 更新时间
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,                   -- 软删除时间
  created_by BIGINT NULL,                                      -- 创建人ID
  updated_by BIGINT NULL                                       -- 更新人ID
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_feedback_records IS '反馈记录主表 - 存储用户提交的各类反馈信息，支持教师端和学生端多种反馈类型多选';
COMMENT ON COLUMN tbl_feedback_records.id IS '主键ID';
COMMENT ON COLUMN tbl_feedback_records.user_id IS '提交反馈的用户ID（引用公共服务用户中心）';
COMMENT ON COLUMN tbl_feedback_records.user_type IS '用户类型 (teacher:教师, student:学生)';
COMMENT ON COLUMN tbl_feedback_records.feedback_types IS '反馈类型（多选JSON数组） (["课程反馈","题目反馈","视频MV反馈","其他建议反馈","其他bug反馈"])';
COMMENT ON COLUMN tbl_feedback_records.content_type IS '内容类型 (text:文字类内容, non_text:非文字类内容)';
COMMENT ON COLUMN tbl_feedback_records.course_id IS '关联课程ID（引用公共服务内容平台，可为空）';
COMMENT ON COLUMN tbl_feedback_records.question_id IS '关联题目ID（引用公共服务内容平台，可为空）';
COMMENT ON COLUMN tbl_feedback_records.feedback_content IS '用户提交的反馈内容描述';
COMMENT ON COLUMN tbl_feedback_records.screenshot_urls IS '截图信息（JSON格式存储多个图片URL）';
COMMENT ON COLUMN tbl_feedback_records.feedback_status IS '反馈处理状态 (pending:待处理, processing:处理中, accepted:已采纳, rejected:未采纳)';
COMMENT ON COLUMN tbl_feedback_records.priority_level IS '优先级等级 (1:低优先级, 2:中优先级, 3:高优先级)';
COMMENT ON COLUMN tbl_feedback_records.source_platform IS '反馈来源平台 (web:网页端, mobile:移动端, app:应用端)';
COMMENT ON COLUMN tbl_feedback_records.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_feedback_records.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_feedback_records.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_feedback_records.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_feedback_records.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_feedback_records.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE INDEX idx_feedback_records_user_id ON tbl_feedback_records(user_id) WHERE deleted_at IS NULL;
-- 用户反馈查询索引，支持按用户查询其提交的反馈列表

CREATE INDEX idx_feedback_records_status ON tbl_feedback_records(feedback_status) WHERE deleted_at IS NULL;
-- 反馈状态查询索引，支持后台按状态筛选反馈

CREATE INDEX idx_feedback_records_types_gin ON tbl_feedback_records USING gin (feedback_types) WHERE deleted_at IS NULL;
-- 反馈类型查询索引（GIN索引），支持按反馈类型多选查询和统计

CREATE INDEX idx_feedback_records_created_at ON tbl_feedback_records(created_at) WHERE deleted_at IS NULL;
-- 时间范围查询索引，支持按时间范围查询反馈

CREATE INDEX idx_feedback_records_course_id ON tbl_feedback_records(course_id) WHERE deleted_at IS NULL AND course_id IS NOT NULL;
-- 课程关联查询索引，支持查询特定课程的反馈

CREATE INDEX idx_feedback_records_question_id ON tbl_feedback_records(question_id) WHERE deleted_at IS NULL AND question_id IS NOT NULL;
-- 题目关联查询索引，支持查询特定题目的反馈

CREATE INDEX idx_feedback_records_user_status_time ON tbl_feedback_records(user_id, feedback_status, created_at) WHERE deleted_at IS NULL;
-- 复合索引，支持用户反馈列表按状态和时间排序查询
```

#### 3.2.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `idx_feedback_records_user_id` | user_id | btree | 用户反馈查询 | 查询用户提交的反馈列表 | 高效单用户查询 |
| `idx_feedback_records_status` | feedback_status | btree | 状态筛选查询 | 后台按状态筛选反馈 | 高效状态过滤 |
| `idx_feedback_records_types_gin` | feedback_types | gin | 类型多选查询 | 按反馈类型多选查询和统计 | 高效JSONB查询 |
| `idx_feedback_records_created_at` | created_at | btree | 时间范围查询 | 按时间范围查询反馈 | 高效时间范围查询 |
| `idx_feedback_records_course_id` | course_id | btree | 课程关联查询 | 查询特定课程反馈 | 高效课程过滤 |
| `idx_feedback_records_question_id` | question_id | btree | 题目关联查询 | 查询特定题目反馈 | 高效题目过滤 |
| `idx_feedback_records_user_status_time` | user_id,feedback_status,created_at | btree | 用户反馈列表查询 | 用户反馈列表分页排序 | 高效复合查询 |

---

### 3.3 表名: `tbl_feedback_processing` (反馈处理记录表)

#### 3.3.1 表功能说明
存储生产后台对反馈的处理状态和结果，记录处理人员、处理时间、处理结果、处理说明等信息，支持反馈处理流程的完整追踪。

#### 3.3.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_feedback_processing (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  feedback_id BIGINT NOT NULL,                                -- 关联反馈记录ID
  processor_id BIGINT NOT NULL,                               -- 处理人员ID
  processing_result BOOLEAN NULL,                             -- 处理结果：true-采纳，false-未采纳，null-处理中
  processing_reason TEXT NULL,                                -- 处理原因说明
  modification_content TEXT NULL,                             -- 对课程/题目的具体修改内容
  processing_stage VARCHAR(30) NOT NULL DEFAULT 'initial',    -- 处理阶段：initial/reviewing/modifying/completed
  estimated_completion_time TIMESTAMPTZ NULL,                 -- 预计完成时间
  actual_completion_time TIMESTAMPTZ NULL,                    -- 实际完成时间
  processing_notes TEXT NULL,                                 -- 处理备注
  feishu_record_id VARCHAR(100) NULL,                         -- 飞书多维表格记录ID
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,                         -- 记录状态：0-禁用，1-启用
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 更新时间
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,                   -- 软删除时间
  created_by BIGINT NULL,                                      -- 创建人ID
  updated_by BIGINT NULL                                       -- 更新人ID
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_feedback_processing IS '反馈处理记录表 - 存储生产后台对反馈的处理状态和结果，支持处理流程追踪';
COMMENT ON COLUMN tbl_feedback_processing.id IS '主键ID';
COMMENT ON COLUMN tbl_feedback_processing.feedback_id IS '关联的反馈记录ID';
COMMENT ON COLUMN tbl_feedback_processing.processor_id IS '处理反馈的生产人员ID';
COMMENT ON COLUMN tbl_feedback_processing.processing_result IS '处理结果 (true:采纳, false:未采纳, null:处理中)';
COMMENT ON COLUMN tbl_feedback_processing.processing_reason IS '采纳或未采纳的原因说明';
COMMENT ON COLUMN tbl_feedback_processing.modification_content IS '对课程/题目的具体修改内容描述';
COMMENT ON COLUMN tbl_feedback_processing.processing_stage IS '处理阶段 (initial:初始, reviewing:审核中, modifying:修改中, completed:已完成)';
COMMENT ON COLUMN tbl_feedback_processing.estimated_completion_time IS '预计完成处理的时间';
COMMENT ON COLUMN tbl_feedback_processing.actual_completion_time IS '实际完成处理的时间';
COMMENT ON COLUMN tbl_feedback_processing.processing_notes IS '处理过程中的备注信息';
COMMENT ON COLUMN tbl_feedback_processing.feishu_record_id IS '关联的飞书多维表格记录ID';
COMMENT ON COLUMN tbl_feedback_processing.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_feedback_processing.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_feedback_processing.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_feedback_processing.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_feedback_processing.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_feedback_processing.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE UNIQUE INDEX uk_feedback_processing_feedback_id ON tbl_feedback_processing(feedback_id) WHERE deleted_at IS NULL;
-- 反馈ID唯一索引，确保每个反馈只有一个处理记录

CREATE INDEX idx_feedback_processing_processor ON tbl_feedback_processing(processor_id) WHERE deleted_at IS NULL;
-- 处理人员查询索引，支持查询特定处理人员的工作记录

CREATE INDEX idx_feedback_processing_stage ON tbl_feedback_processing(processing_stage) WHERE deleted_at IS NULL;
-- 处理阶段查询索引，支持按处理阶段筛选任务

CREATE INDEX idx_feedback_processing_result ON tbl_feedback_processing(processing_result) WHERE deleted_at IS NULL;
-- 处理结果查询索引，支持按处理结果统计分析

CREATE INDEX idx_feedback_processing_completion_time ON tbl_feedback_processing(actual_completion_time) WHERE deleted_at IS NULL;
-- 完成时间查询索引，支持按完成时间范围查询
```

#### 3.3.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_feedback_processing_feedback_id` | feedback_id | btree unique | 反馈处理唯一性 | 确保每个反馈只有一个处理记录 | 高效唯一性约束 |
| `idx_feedback_processing_processor` | processor_id | btree | 处理人员查询 | 查询处理人员的工作记录 | 高效人员过滤 |
| `idx_feedback_processing_stage` | processing_stage | btree | 处理阶段查询 | 按处理阶段筛选任务 | 高效阶段过滤 |
| `idx_feedback_processing_result` | processing_result | btree | 处理结果查询 | 按处理结果统计分析 | 高效结果分组 |
| `idx_feedback_processing_completion_time` | actual_completion_time | btree | 完成时间查询 | 按完成时间范围查询 | 高效时间范围查询 |

---

### 3.4 表名: `tbl_point_records` (积分记录表)

#### 3.4.1 表功能说明
存储用户积分获得和消费记录，支持积分计算、积分下发、积分查询、积分历史管理等功能，为用户激励机制提供数据支持。

#### 3.4.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_point_records (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id BIGINT NOT NULL,                                    -- 获得积分的用户ID
  feedback_id BIGINT NOT NULL,                                -- 关联的反馈记录ID
  point_amount INTEGER NOT NULL,                              -- 积分数量（正数为获得，负数为消费）
  point_type VARCHAR(30) NOT NULL DEFAULT 'feedback_reward',  -- 积分类型：feedback_reward/bonus/penalty/consumption
  severity_level VARCHAR(20) NOT NULL DEFAULT 'normal',       -- 问题严重程度：minor/normal/serious
  calculation_rule TEXT NULL,                                 -- 积分计算规则说明
  distributor_id BIGINT NOT NULL,                             -- 下发积分的运营人员ID
  distribution_reason TEXT NULL,                              -- 积分下发原因
  current_balance INTEGER NOT NULL DEFAULT 0,                 -- 下发后的当前积分余额
  expiry_date TIMESTAMPTZ NULL,                               -- 积分过期时间（如果适用）
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,                         -- 记录状态：0-禁用，1-启用
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 更新时间
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,                   -- 软删除时间
  created_by BIGINT NULL,                                      -- 创建人ID
  updated_by BIGINT NULL                                       -- 更新人ID
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_point_records IS '积分记录表 - 存储用户积分获得和消费记录，支持积分激励机制';
COMMENT ON COLUMN tbl_point_records.id IS '主键ID';
COMMENT ON COLUMN tbl_point_records.user_id IS '获得积分的用户ID';
COMMENT ON COLUMN tbl_point_records.feedback_id IS '关联的反馈记录ID';
COMMENT ON COLUMN tbl_point_records.point_amount IS '积分数量 (正数为获得，负数为消费)';
COMMENT ON COLUMN tbl_point_records.point_type IS '积分类型 (feedback_reward:反馈奖励, bonus:额外奖励, penalty:扣分, consumption:消费)';
COMMENT ON COLUMN tbl_point_records.severity_level IS '问题严重程度 (minor:轻微, normal:一般, serious:严重)';
COMMENT ON COLUMN tbl_point_records.calculation_rule IS '积分计算规则的详细说明';
COMMENT ON COLUMN tbl_point_records.distributor_id IS '下发积分的运营人员ID';
COMMENT ON COLUMN tbl_point_records.distribution_reason IS '积分下发的具体原因';
COMMENT ON COLUMN tbl_point_records.current_balance IS '积分下发后用户的当前积分余额';
COMMENT ON COLUMN tbl_point_records.expiry_date IS '积分过期时间 (NULL表示永不过期)';
COMMENT ON COLUMN tbl_point_records.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_point_records.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_point_records.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_point_records.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_point_records.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_point_records.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE INDEX idx_point_records_user_id ON tbl_point_records(user_id) WHERE deleted_at IS NULL;
-- 用户积分查询索引，支持查询用户的积分记录

CREATE INDEX idx_point_records_feedback_id ON tbl_point_records(feedback_id) WHERE deleted_at IS NULL;
-- 反馈关联查询索引，支持查询特定反馈的积分记录

CREATE INDEX idx_point_records_type ON tbl_point_records(point_type) WHERE deleted_at IS NULL;
-- 积分类型查询索引，支持按积分类型统计分析

CREATE INDEX idx_point_records_user_time ON tbl_point_records(user_id, created_at) WHERE deleted_at IS NULL;
-- 用户积分历史查询索引，支持用户积分历史按时间排序

CREATE INDEX idx_point_records_distributor ON tbl_point_records(distributor_id) WHERE deleted_at IS NULL;
-- 分发人员查询索引，支持查询特定运营人员的积分分发记录
```

#### 3.4.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `idx_point_records_user_id` | user_id | btree | 用户积分查询 | 查询用户的积分记录 | 高效用户过滤 |
| `idx_point_records_feedback_id` | feedback_id | btree | 反馈关联查询 | 查询特定反馈的积分记录 | 高效反馈关联 |
| `idx_point_records_type` | point_type | btree | 积分类型查询 | 按积分类型统计分析 | 高效类型分组 |
| `idx_point_records_user_time` | user_id,created_at | btree | 用户积分历史查询 | 用户积分历史按时间排序 | 高效时序查询 |
| `idx_point_records_distributor` | distributor_id | btree | 分发人员查询 | 查询运营人员的积分分发记录 | 高效人员过滤 |

---

### 3.5 表名: `tbl_notifications
` (消息通知表)

#### 3.5.1 表功能说明
存储各类通知消息，包括反馈确认通知、处理结果通知、积分下发通知等，支持消息推送、消息查询、已读状态管理等功能。

#### 3.5.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_notifications (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id BIGINT NOT NULL,                                    -- 接收消息的用户ID
  feedback_id BIGINT NULL,                                    -- 关联的反馈记录ID（可为空）
  point_record_id BIGINT NULL,                                -- 关联的积分记录ID（可为空）
  notification_type VARCHAR(30) NOT NULL,                     -- 消息类型：feedback_confirm/processing_result/point_reward/system_notice
  notification_title VARCHAR(200) NOT NULL,                   -- 消息标题
  notification_content TEXT NOT NULL,                         -- 消息详细内容
  send_channel VARCHAR(20) NOT NULL DEFAULT 'app',            -- 发送渠道：app/email/sms/feishu
  send_status VARCHAR(20) NOT NULL DEFAULT 'pending',         -- 发送状态：pending/sent/failed/retry
  is_read BOOLEAN NOT NULL DEFAULT FALSE,                     -- 是否已读
  read_time TIMESTAMPTZ NULL,                                 -- 阅读时间
  send_time TIMESTAMPTZ NULL,                                 -- 实际发送时间
  retry_count INTEGER NOT NULL DEFAULT 0,                     -- 重试次数
  error_message TEXT NULL,                                    -- 发送失败错误信息
  template_id VARCHAR(50) NULL,                               -- 消息模板ID
  template_params JSONB NULL,                                 -- 模板参数（JSON格式）
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,                         -- 记录状态：0-禁用，1-启用
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 更新时间
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,                   -- 软删除时间
  created_by BIGINT NULL,                                      -- 创建人ID
  updated_by BIGINT NULL                                       -- 更新人ID
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_notifications IS '消息通知表 - 存储各类通知消息，支持多渠道消息推送和状态管理';
COMMENT ON COLUMN tbl_notifications.id IS '主键ID';
COMMENT ON COLUMN tbl_notifications.user_id IS '接收消息的用户ID';
COMMENT ON COLUMN tbl_notifications.feedback_id IS '关联的反馈记录ID（可为空）';
COMMENT ON COLUMN tbl_notifications.point_record_id IS '关联的积分记录ID（可为空）';
COMMENT ON COLUMN tbl_notifications.notification_type IS '消息类型 (feedback_confirm:反馈确认, processing_result:处理结果, point_reward:积分奖励, system_notice:系统通知)';
COMMENT ON COLUMN tbl_notifications.notification_title IS '消息标题';
COMMENT ON COLUMN tbl_notifications.notification_content IS '消息的详细内容';
COMMENT ON COLUMN tbl_notifications.send_channel IS '发送渠道 (app:应用内, email:邮件, sms:短信, feishu:飞书)';
COMMENT ON COLUMN tbl_notifications.send_status IS '发送状态 (pending:待发送, sent:已发送, failed:发送失败, retry:重试中)';
COMMENT ON COLUMN tbl_notifications.is_read IS '是否已读 (true:已读, false:未读)';
COMMENT ON COLUMN tbl_notifications.read_time IS '用户阅读消息的时间';
COMMENT ON COLUMN tbl_notifications.send_time IS '消息实际发送的时间';
COMMENT ON COLUMN tbl_notifications.retry_count IS '发送失败后的重试次数';
COMMENT ON COLUMN tbl_notifications.error_message IS '发送失败时的错误信息';
COMMENT ON COLUMN tbl_notifications.template_id IS '使用的消息模板ID';
COMMENT ON COLUMN tbl_notifications.template_params IS '消息模板参数（JSON格式存储）';
COMMENT ON COLUMN tbl_notifications.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_notifications.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_notifications.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_notifications.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_notifications.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_notifications.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE INDEX idx_notifications_user_id ON tbl_notifications(user_id) WHERE deleted_at IS NULL;
-- 用户消息查询索引，支持查询用户的消息列表

CREATE INDEX idx_notifications_user_read ON tbl_notifications(user_id, is_read) WHERE deleted_at IS NULL;
-- 用户未读消息查询索引，支持查询用户的未读消息

CREATE INDEX idx_notifications_feedback_id ON tbl_notifications(feedback_id) WHERE deleted_at IS NULL AND feedback_id IS NOT NULL;
-- 反馈关联查询索引，支持查询特定反馈的通知消息

CREATE INDEX idx_notifications_send_status ON tbl_notifications(send_status) WHERE deleted_at IS NULL;
-- 发送状态查询索引，支持查询待发送或失败的消息

CREATE INDEX idx_notifications_type ON tbl_notifications(notification_type) WHERE deleted_at IS NULL;
-- 消息类型查询索引，支持按消息类型统计分析

CREATE INDEX idx_notifications_user_time ON tbl_notifications(user_id, created_at) WHERE deleted_at IS NULL;
-- 用户消息时序查询索引，支持用户消息列表按时间排序

-- 为JSONB字段创建GIN索引
CREATE INDEX idx_notifications_template_params_gin ON tbl_notifications USING gin (template_params) WHERE deleted_at IS NULL;
-- 模板参数查询索引，支持基于模板参数的复杂查询
```

#### 3.5.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `idx_notifications_user_id` | user_id | btree | 用户消息查询 | 查询用户的消息列表 | 高效用户过滤 |
| `idx_notifications_user_read` | user_id,is_read | btree | 用户未读消息查询 | 查询用户的未读消息 | 高效未读过滤 |
| `idx_notifications_feedback_id` | feedback_id | btree | 反馈关联查询 | 查询特定反馈的通知消息 | 高效反馈关联 |
| `idx_notifications_send_status` | send_status | btree | 发送状态查询 | 查询待发送或失败的消息 | 高效状态过滤 |
| `idx_notifications_type` | notification_type | btree | 消息类型查询 | 按消息类型统计分析 | 高效类型分组 |
| `idx_notifications_user_time` | user_id,created_at | btree | 用户消息时序查询 | 用户消息列表按时间排序 | 高效时序查询 |
| `idx_notifications_template_params_gin` | template_params | gin | 模板参数查询 | 基于模板参数的复杂查询 | 高效JSONB查询 |

---

### 3.6 表名: `tbl_agent_audit_records` (Agent审核记录表)

#### 3.6.1 表功能说明
存储智能Agent对反馈的自动审核结果，记录审核类型、审核结果、置信度、审核时间、处理耗时等信息，支持审核质量分析和优化。

#### 3.6.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_agent_audit_records (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  feedback_id BIGINT NOT NULL,                                -- 关联的反馈记录ID
  workflow_type VARCHAR(30) NOT NULL,                         -- 使用的dify工作流类型：dify1_workflow/dify2_workflow/dify3_workflow
  audit_result VARCHAR(20) NOT NULL,                          -- 审核结果：valid_feedback/invalid_feedback/need_manual_review
  confidence_score NUMERIC(5,4) NULL,                         -- 置信度分数（0.0000-1.0000）
  audit_details TEXT NULL,                                    -- Agent的详细分析结果
  processing_time_ms INTEGER NOT NULL,                        -- Agent处理耗时（毫秒）
  input_tokens INTEGER NULL,                                  -- 输入token数量
  output_tokens INTEGER NULL,                                 -- 输出token数量
  model_version VARCHAR(50) NULL,                             -- 使用的模型版本
  audit_categories JSONB NULL,                                -- 审核分类结果（JSON格式）
  error_code VARCHAR(20) NULL,                                -- 错误代码（如果审核失败）
  error_message TEXT NULL,                                    -- 错误信息（如果审核失败）
  retry_count INTEGER NOT NULL DEFAULT 0,                     -- 重试次数
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,                         -- 记录状态：0-禁用，1-启用
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 创建时间
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 更新时间
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,                   -- 软删除时间
  created_by BIGINT NULL,                                      -- 创建人ID
  updated_by BIGINT NULL                                       -- 更新人ID
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_agent_audit_records IS 'Agent审核记录表 - 存储智能Agent对反馈的自动审核结果，支持审核质量分析';
COMMENT ON COLUMN tbl_agent_audit_records.id IS '主键ID';
COMMENT ON COLUMN tbl_agent_audit_records.feedback_id IS '关联的反馈记录ID';
COMMENT ON COLUMN tbl_agent_audit_records.workflow_type IS '使用的dify工作流类型 (dify1_workflow/dify2_workflow/dify3_workflow)';
COMMENT ON COLUMN tbl_agent_audit_records.audit_result IS '审核结果 (valid_feedback:有效反馈, invalid_feedback:无效反馈, need_manual_review:需要人工审核)';
COMMENT ON COLUMN tbl_agent_audit_records.confidence_score IS '审核结果的置信度分数 (0.0000-1.0000)';
COMMENT ON COLUMN tbl_agent_audit_records.audit_details IS 'Agent的详细分析结果和建议';
COMMENT ON COLUMN tbl_agent_audit_records.processing_time_ms IS 'Agent处理耗时（毫秒）';
COMMENT ON COLUMN tbl_agent_audit_records.input_tokens IS '输入的token数量';
COMMENT ON COLUMN tbl_agent_audit_records.output_tokens IS '输出的token数量';
COMMENT ON COLUMN tbl_agent_audit_records.model_version IS '使用的AI模型版本';
COMMENT ON COLUMN tbl_agent_audit_records.audit_categories IS '审核分类结果（JSON格式存储多个分类）';
COMMENT ON COLUMN tbl_agent_audit_records.error_code IS '审核失败时的错误代码';
COMMENT ON COLUMN tbl_agent_audit_records.error_message IS '审核失败时的详细错误信息';
COMMENT ON COLUMN tbl_agent_audit_records.retry_count IS '审核失败后的重试次数';
COMMENT ON COLUMN tbl_agent_audit_records.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_agent_audit_records.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_agent_audit_records.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_agent_audit_records.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_agent_audit_records.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_agent_audit_records.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE UNIQUE INDEX uk_agent_audit_records_feedback_id ON tbl_agent_audit_records(feedback_id) WHERE deleted_at IS NULL;
-- 反馈ID唯一索引，确保每个反馈只有一个审核记录

CREATE INDEX idx_agent_audit_records_workflow_type ON tbl_agent_audit_records(workflow_type) WHERE deleted_at IS NULL;
-- 工作流类型查询索引，支持按工作流类型统计分析

CREATE INDEX idx_agent_audit_records_result ON tbl_agent_audit_records(audit_result) WHERE deleted_at IS NULL;
-- 审核结果查询索引，支持按审核结果统计分析

CREATE INDEX idx_agent_audit_records_processing_time ON tbl_agent_audit_records(processing_time_ms) WHERE deleted_at IS NULL;
-- 处理时间查询索引，支持性能分析

CREATE INDEX idx_agent_audit_records_confidence ON tbl_agent_audit_records(confidence_score) WHERE deleted_at IS NULL AND confidence_score IS NOT NULL;
-- 置信度查询索引，支持按置信度范围查询

CREATE INDEX idx_agent_audit_records_created_at ON tbl_agent_audit_records(created_at) WHERE deleted_at IS NULL;
-- 时间查询索引，支持按时间范围统计审核数据

-- 为JSONB字段创建GIN索引
CREATE INDEX idx_agent_audit_records_categories_gin ON tbl_agent_audit_records USING gin (audit_categories) WHERE deleted_at IS NULL;
-- 审核分类查询索引，支持基于分类结果的复杂查询
```

#### 3.6.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_agent_audit_records_feedback_id` | feedback_id | btree unique | 反馈审核唯一性 | 确保每个反馈只有一个审核记录 | 高效唯一性约束 |
| `idx_agent_audit_records_workflow_type` | workflow_type | btree | 工作流类型查询 | 按工作流类型统计分析 | 高效类型分组 |
| `idx_agent_audit_records_result` | audit_result | btree | 审核结果查询 | 按审核结果统计分析 | 高效结果分组 |
| `idx_agent_audit_records_processing_time` | processing_time_ms | btree | 处理时间查询 | Agent性能分析 | 高效性能统计 |
| `idx_agent_audit_records_confidence` | confidence_score | btree | 置信度查询 | 按置信度范围查询 | 高效置信度过滤 |
| `idx_agent_audit_records_created_at` | created_at | btree | 时间查询 | 按时间范围统计审核数据 | 高效时间范围查询 |
| `idx_agent_audit_records_categories_gin` | audit_categories | gin | 审核分类查询 | 基于分类结果的复杂查询 | 高效JSONB查询 |

---

## 4. 数据库表详细设计 - ClickHouse

### 4.1 表名: `tbl_feedback_analytics` (反馈分析数据表)

#### 4.1.1 表功能说明
存储反馈处理的统计分析数据，支持反馈量趋势分析、处理效率分析、用户参与度分析等业务指标计算，为运营决策提供数据支持。

#### 4.1.2 表结构定义 (DDL)
```sql
CREATE TABLE default.tbl_feedback_analytics
(
    -- 时间维度字段
    event_date Date COMMENT '事件日期',
    event_time DateTime64(3) COMMENT '事件时间（毫秒精度）',
    
    -- 业务维度字段
    feedback_id UInt64 COMMENT '反馈记录ID',
    user_id UInt64 COMMENT '用户ID',
    user_type LowCardinality(String) COMMENT '用户类型 (teacher/student)',
    feedback_types Array(String) COMMENT '反馈类型数组（多选）',
    content_type LowCardinality(String) COMMENT '内容类型 (text/non_text)',
    feedback_status LowCardinality(String) COMMENT '反馈状态',
    priority_level UInt8 COMMENT '优先级等级',
    
    -- 关联维度字段
    course_id Nullable(UInt64) COMMENT '关联课程ID',
    question_id Nullable(UInt64) COMMENT '关联题目ID',
    processor_id Nullable(UInt64) COMMENT '处理人员ID',
    
    -- 处理过程字段
    processing_stage LowCardinality(String) COMMENT '处理阶段',
    processing_result Nullable(UInt8) COMMENT '处理结果 (1:采纳, 0:未采纳)',
    agent_audit_result LowCardinality(String) COMMENT 'Agent审核结果',
    agent_confidence_score Nullable(Float32) COMMENT 'Agent置信度分数',
    agent_processing_time_ms UInt32 COMMENT 'Agent处理耗时（毫秒）',
    
    -- 时间指标字段
    feedback_submit_time DateTime64(3) COMMENT '反馈提交时间',
    audit_complete_time Nullable(DateTime64(3)) COMMENT '审核完成时间',
    processing_complete_time Nullable(DateTime64(3)) COMMENT '处理完成时间',
    
    -- 计算指标字段
    audit_duration_ms Nullable(UInt32) COMMENT '审核耗时（毫秒）',
    processing_duration_hours Nullable(Float32) COMMENT '处理耗时（小时）',
    total_duration_hours Nullable(Float32) COMMENT '总耗时（小时）',
    
    -- 积分相关字段
    point_amount Nullable(Int32) COMMENT '获得积分数量',
    severity_level LowCardinality(String) COMMENT '问题严重程度',
    
    -- 来源平台字段
    source_platform LowCardinality(String) COMMENT '来源平台'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_type, event_time)
TTL event_date + INTERVAL 365 DAY DELETE
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE default.tbl_feedback_analytics IS '反馈分析数据表 - 存储反馈处理的统计分析数据，支持多维度业务指标分析';

-- 跳数索引
ALTER TABLE default.tbl_feedback_analytics ADD INDEX idx_user_id user_id TYPE bloom_filter GRANULARITY 1;
-- 用户ID布隆过滤器索引，支持快速用户过滤

ALTER TABLE default.tbl_feedback_analytics ADD INDEX idx_course_id course_id TYPE set(0) GRANULARITY 1;
-- 课程ID集合索引，支持快速课程过滤

ALTER TABLE default.tbl_feedback_analytics ADD INDEX idx_processing_time agent_processing_time_ms TYPE minmax GRANULARITY 1;
-- 处理时间最小最大值索引，支持性能分析查询

ALTER TABLE default.tbl_feedback_analytics ADD INDEX idx_feedback_types feedback_types TYPE bloom_filter GRANULARITY 1;
-- 反馈类型数组布隆过滤器索引，支持多选类型查询
```

**ClickHouse 特有说明**:
* **排序键 (ORDER BY)**: `(event_date, user_type, event_time)`
  * 设计原因: 按日期、用户类型、时间排序，支持时间范围查询和按用户类型的聚合分析
* **分区键 (PARTITION BY)**: `toYYYYMM(event_date)`
  * 设计原因: 按月分区，便于数据管理和历史数据清理，支持分区裁剪优化查询性能
* **TTL策略**: `event_date + INTERVAL 365 DAY DELETE`
  * 设计原因: 分析数据保留1年，自动清理历史数据，控制存储成本

#### 4.1.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `idx_user_id` | user_id | bloom_filter | 用户过滤查询 | 按用户ID过滤分析数据 | 高效用户过滤 |
| `idx_course_id` | course_id | set | 课程过滤查询 | 按课程ID过滤分析数据 | 高效课程过滤 |
| `idx_processing_time` | agent_processing_time_ms | minmax | 性能分析查询 | Agent处理时间范围查询 | 高效性能统计 |
| `idx_feedback_types` | feedback_types | bloom_filter | 反馈类型过滤查询 | 按反馈类型多选过滤分析数据 | 高效类型过滤 |

---

### 4.2 表名: `tbl_user_behavior_logs` (用户行为日志表)

#### 4.2.1 表功能说明
存储用户在反馈系统中的操作行为数据，包括页面访问、反馈提交、消息查看等行为，支持用户行为分析和系统使用情况统计。

#### 4.2.2 表结构定义 (DDL)
```sql
CREATE TABLE default.tbl_user_behavior_logs
(
    -- 时间维度字段
    event_date Date COMMENT '事件日期',
    event_time DateTime64(3) COMMENT '事件时间（毫秒精度）',
    
    -- 用户维度字段
    user_id UInt64 COMMENT '用户ID',
    user_type LowCardinality(String) COMMENT '用户类型 (teacher/student)',
    session_id String COMMENT '会话ID',
    
    -- 行为维度字段
    action_type LowCardinality(String) COMMENT '行为类型 (page_view/feedback_submit/message_read/point_check)',
    page_path String COMMENT '页面路径',
    action_target String COMMENT '操作目标',
    action_result LowCardinality(String) COMMENT '操作结果 (success/failed/cancelled)',
    
    -- 关联对象字段
    feedback_id Nullable(UInt64) COMMENT '关联反馈ID',
    notification_id Nullable(UInt64) COMMENT '关联通知ID',
    
    -- 设备和环境字段
    device_type LowCardinality(String) COMMENT '设备类型 (PC/Mobile/Tablet)',
    browser_type LowCardinality(String) COMMENT '浏览器类型',
    os_type LowCardinality(String) COMMENT '操作系统类型',
    ip_address IPv6 COMMENT '用户IP地址',
    user_agent String COMMENT '用户代理字符串',
    
    -- 性能指标字段
    page_load_time_ms Nullable(UInt32) COMMENT '页面加载时间（毫秒）',
    action_duration_ms Nullable(UInt32) COMMENT '操作耗时（毫秒）',
    
    -- 扩展属性字段
    event_properties Map(String, String) COMMENT '事件扩展属性'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, action_type, event_time)
TTL event_date + INTERVAL 180 DAY DELETE
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE default.tbl_user_behavior_logs IS '用户行为日志表 - 存储用户在反馈系统中的操作行为数据，支持用户行为分析';

-- 跳数索引
ALTER TABLE default.tbl_user_behavior_logs ADD INDEX idx_session_id session_id TYPE bloom_filter GRANULARITY 1;
-- 会话ID布隆过滤器索引，支持会话行为分析

ALTER TABLE default.tbl_user_behavior_logs ADD INDEX idx_ip_address ip_address TYPE set(0) GRANULARITY 1;
-- IP地址集合索引，支持IP行为分析

ALTER TABLE default.tbl_user_behavior_logs ADD INDEX idx_page_load_time page_load_time_ms TYPE minmax GRANULARITY 1;
-- 页面加载时间最小最大值索引，支持性能分析
```

**ClickHouse 特有说明**:
* **排序键 (ORDER BY)**: `(event_date, user_id, action_type, event_time)`
  * 设计原因: 按日期、用户ID、行为类型、时间排序，支持用户行为序列分析和按行为类型的聚合统计
* **分区键 (PARTITION BY)**: `toYYYYMM(event_date)`
  * 设计原因: 按月分区，便于数据管理和历史数据清理，支持分区裁剪优化查询性能
* **TTL策略**: `event_date + INTERVAL 180 DAY DELETE`
  * 设计原因: 行为日志数据保留6个月，平衡分析需求和存储成本

#### 4.2.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `idx_session_id` | session_id | bloom_filter | 会话行为分析 | 按会话ID分析用户行为序列 | 高效会话过滤 |
| `idx_ip_address` | ip_address | set | IP行为分析 | 按IP地址分析访问行为 | 高效IP过滤 |
| `idx_page_load_time` | page_load_time_ms | minmax | 性能分析 | 页面加载时间性能分析 | 高效性能统计 |

---

## 5. 表间关系与 ER 图

### 5.1 表间关系描述

**表间关系标准格式**：
| 主表           | 关系类型                       | 从表               | 外键字段 (从表)                           | 关联描述                                               | 一致性保障方式 |
| -------------- | ------------------------------ | ------------------ | ----------------------------------------- | ------------------------------------------------------ | ------------- |
| `tbl_feedback_records`   | 一对一 (1:1)                   | `tbl_feedback_processing`     | `feedback_id`                                 | 每个反馈记录对应一个处理记录                                 | 应用层校验 |
| `tbl_feedback_records` | 一对一 (1:1) | `tbl_agent_audit_records` | `feedback_id` | 每个反馈记录对应一个Agent审核记录 | 应用层校验 |
| `tbl_feedback_records` | 一对一 (1:1) | `tbl_point_records` | `feedback_id` | 每个被采纳的反馈记录对应一个积分记录 | 应用层校验 |
| `tbl_feedback_records` | 一对多 (1:N) | `tbl_notifications` | `feedback_id` | 一个反馈记录可以产生多个通知消息 | 应用层校验 |
| `tbl_point_records` |
一对多 (1:N) | `tbl_notifications` | `point_record_id` | 一个积分记录可以产生多个通知消息 | 应用层校验 |

### 5.2 ER 图 (使用 Mermaid 语法)

```mermaid
erDiagram
    %% PostgreSQL 核心业务表
    TBL_FEEDBACK_RECORDS {
        BIGSERIAL id PK
        BIGINT user_id
        VARCHAR user_type
        JSONB feedback_types
        VARCHAR content_type
        BIGINT course_id
        BIGINT question_id
        TEXT feedback_content
        TEXT screenshot_urls
        VARCHAR feedback_status
        SMALLINT priority_level
        VARCHAR source_platform
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    TBL_FEEDBACK_PROCESSING {
        BIGSERIAL id PK
        BIGINT feedback_id FK
        BIGINT processor_id
        BOOLEAN processing_result
        TEXT processing_reason
        TEXT modification_content
        VARCHAR processing_stage
        TIMESTAMPTZ estimated_completion_time
        TIMESTAMPTZ actual_completion_time
        TEXT processing_notes
        VARCHAR feishu_record_id
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    TBL_POINT_RECORDS {
        BIGSERIAL id PK
        BIGINT user_id FK
        BIGINT feedback_id FK
        INTEGER point_amount
        VARCHAR point_type
        VARCHAR severity_level
        TEXT calculation_rule
        BIGINT distributor_id
        TEXT distribution_reason
        INTEGER current_balance
        TIMESTAMPTZ expiry_date
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    TBL_NOTIFICATIONS {
        BIGSERIAL id PK
        BIGINT user_id FK
        BIGINT feedback_id FK
        BIGINT point_record_id FK
        VARCHAR notification_type
        VARCHAR notification_title
        TEXT notification_content
        VARCHAR send_channel
        VARCHAR send_status
        BOOLEAN is_read
        TIMESTAMPTZ read_time
        TIMESTAMPTZ send_time
        INTEGER retry_count
        TEXT error_message
        VARCHAR template_id
        JSONB template_params
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    TBL_AGENT_AUDIT_RECORDS {
        BIGSERIAL id PK
        BIGINT feedback_id FK
        VARCHAR workflow_type
        VARCHAR audit_result
        NUMERIC confidence_score
        TEXT audit_details
        INTEGER processing_time_ms
        INTEGER input_tokens
        INTEGER output_tokens
        VARCHAR model_version
        JSONB audit_categories
        VARCHAR error_code
        TEXT error_message
        INTEGER retry_count
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }

    %% ClickHouse 分析表
    TBL_FEEDBACK_ANALYTICS {
        Date event_date
        DateTime64 event_time
        UInt64 feedback_id
        UInt64 user_id
        LowCardinality_String user_type
        Array_String feedback_types
        LowCardinality_String content_type
        LowCardinality_String feedback_status
        UInt8 priority_level
        Nullable_UInt64 course_id
        Nullable_UInt64 question_id
        Nullable_UInt64 processor_id
        LowCardinality_String processing_stage
        Nullable_UInt8 processing_result
        LowCardinality_String agent_audit_result
        Nullable_Float32 agent_confidence_score
        UInt32 agent_processing_time_ms
        DateTime64 feedback_submit_time
        Nullable_DateTime64 audit_complete_time
        Nullable_DateTime64 processing_complete_time
        Nullable_UInt32 audit_duration_ms
        Nullable_Float32 processing_duration_hours
        Nullable_Float32 total_duration_hours
        Nullable_Int32 point_amount
        LowCardinality_String severity_level
        LowCardinality_String source_platform
    }
    
    TBL_USER_BEHAVIOR_LOGS {
        Date event_date
        DateTime64 event_time
        UInt64 user_id
        LowCardinality_String user_type
        String session_id
        LowCardinality_String action_type
        String page_path
        String action_target
        LowCardinality_String action_result
        Nullable_UInt64 feedback_id
        Nullable_UInt64 notification_id
        LowCardinality_String device_type
        LowCardinality_String browser_type
        LowCardinality_String os_type
        IPv6 ip_address
        String user_agent
        Nullable_UInt32 page_load_time_ms
        Nullable_UInt32 action_duration_ms
        Map_String_String event_properties
    }

    %% 表间关系（逻辑外键）
    TBL_FEEDBACK_RECORDS ||--|| TBL_FEEDBACK_PROCESSING : "processes"
    TBL_FEEDBACK_RECORDS ||--|| TBL_AGENT_AUDIT_RECORDS : "audited_by"
    TBL_FEEDBACK_RECORDS ||--|| TBL_POINT_RECORDS : "rewards"
    TBL_FEEDBACK_RECORDS ||--o{ TBL_NOTIFICATIONS : "generates"
    TBL_POINT_RECORDS ||--o{ TBL_NOTIFICATIONS : "notifies"
```

## 6. 核心用户场景/用户故事与数据操作分析

### 6.1 场景/故事: 教师提交课程反馈（支持多选反馈类型）

#### 6.1.1 场景描述
教师在使用课程时发现多种问题（如既有板书文字错误，又有字幕问题），通过教师端应用提交反馈时可以选择多种反馈类型，系统自动进行Agent审核，生产后台处理反馈并给予积分奖励，最终通知教师处理结果。

#### 6.1.2 涉及的主要数据表
* `tbl_feedback_records` (反馈记录主表)
* `tbl_agent_audit_records` (Agent审核记录表)
* `tbl_feedback_processing` (反馈处理记录表)
* `tbl_point_records` (积分记录表)
* `tbl_notifications` (消息通知表)

#### 6.1.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 教师提交多选反馈信息 | Create | tbl_feedback_records | - | 主键自增 | 应用层校验 | 低风险 |
| 查询多选反馈类型 | Read | tbl_feedback_records | feedback_types @> '["课程反馈"]' | idx_feedback_records_types_gin | JSONB查询一致性 | 低风险 |
| Agent自动审核反馈 | Create | tbl_agent_audit_records | feedback_id=123 | uk_agent_audit_records_feedback_id | 唯一约束 | 低风险 |
| 生产人员处理反馈 | Create | tbl_feedback_processing | feedback_id=123 | uk_feedback_processing_feedback_id | 唯一约束 | 低风险 |
| 更新反馈状态 | Update | tbl_feedback_records | id=123 | 主键 | 单行更新 | 低风险 |
| 下发积分奖励 | Create | tbl_point_records | feedback_id=123 | idx_point_records_feedback_id | 应用层校验 | 低风险 |
| 发送处理结果通知 | Create | tbl_notifications | user_id=456 | idx_notifications_user_id | 应用层校验 | 低风险 |
| 用户查看消息 | Read | tbl_notifications | user_id=456 AND is_read=false | idx_notifications_user_read | 读取一致性 | 低风险 |

#### 6.1.4 性能考量与索引支持
针对教师反馈提交场景，当前设计的索引能够有效支持：
- 反馈类型多选查询通过`idx_feedback_records_types_gin` GIN索引高效支持JSONB数组查询
- 反馈提交时的用户验证通过`idx_feedback_records_user_id`索引高效查询
- Agent审核记录的唯一性通过`uk_agent_audit_records_feedback_id`保证
- 处理记录的唯一性通过`uk_feedback_processing_feedback_id`保证
- 积分记录查询通过`idx_point_records_feedback_id`索引优化
- 消息通知查询通过`idx_notifications_user_read`索引优化

---

### 6.2 场景/故事: 学生提交题目反馈

#### 6.2.1 场景描述
学生在答题过程中发现题目错误，通过学生端应用提交题目反馈，可以选择多种问题类型（如题干错误、答案错误等），系统记录反馈信息并进行智能审核，生产后台处理后给予积分奖励并通知学生。

#### 6.2.2 涉及的主要数据表
* `tbl_feedback_records` (反馈记录主表)
* `tbl_agent_audit_records` (Agent审核记录表)
* `tbl_feedback_processing` (反馈处理记录表)
* `tbl_point_records` (积分记录表)
* `tbl_notifications` (消息通知表)

#### 6.2.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 学生提交题目反馈 | Create | tbl_feedback_records | - | 主键自增 | 应用层校验 | 低风险 |
| 验证题目存在性 | Read | tbl_feedback_records | question_id=789 | idx_feedback_records_question_id | 读取一致性 | 低风险 |
| Agent审核题目反馈 | Create | tbl_agent_audit_records | feedback_id=124 | uk_agent_audit_records_feedback_id | 唯一约束 | 低风险 |
| 生产人员处理 | Create | tbl_feedback_processing | feedback_id=124 | uk_feedback_processing_feedback_id | 唯一约束 | 低风险 |
| 积分计算和下发 | Create | tbl_point_records | user_id=789, feedback_id=124 | idx_point_records_user_id | 应用层事务 | 中风险 |
| 发送积分通知 | Create | tbl_notifications | user_id=789, point_record_id=456 | idx_notifications_user_id | 应用层校验 | 低风险 |

#### 6.2.4 性能考量与索引支持
学生题目反馈场景的索引支持：
- 题目关联查询通过`idx_feedback_records_question_id`索引优化
- 用户积分历史查询通过`idx_point_records_user_time`索引支持
- 消息通知的时序查询通过`idx_notifications_user_time`索引优化

---

### 6.3 场景/故事: 生产后台反馈管理

#### 6.3.1 场景描述
生产人员通过后台系统查看待处理反馈列表，按状态和类型筛选，查看Agent审核结果，进行人工处理并更新处理状态，系统自动触发积分下发和消息通知。支持按多选反馈类型进行筛选。

#### 6.3.2 涉及的主要数据表
* `tbl_feedback_records` (反馈记录主表)
* `tbl_agent_audit_records` (Agent审核记录表)
* `tbl_feedback_processing` (反馈处理记录表)
* `tbl_point_records` (积分记录表)
* `tbl_notifications` (消息通知表)

#### 6.3.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 查询待处理反馈列表 | Read | tbl_feedback_records | feedback_status='pending' | idx_feedback_records_status | 读取一致性 | 低风险 |
| 按多选类型筛选反馈 | Read | tbl_feedback_records | feedback_types @> '["课程反馈","题目反馈"]' | idx_feedback_records_types_gin | JSONB查询一致性 | 低风险 |
| 查看Agent审核结果 | Read | tbl_agent_audit_records | feedback_id=125 | uk_agent_audit_records_feedback_id | 读取一致性 | 低风险 |
| 创建处理记录 | Create | tbl_feedback_processing | feedback_id=125 | uk_feedback_processing_feedback_id | 唯一约束 | 低风险 |
| 更新处理状态 | Update | tbl_feedback_processing | feedback_id=125 | uk_feedback_processing_feedback_id | 单行更新 | 低风险 |
| 更新反馈状态 | Update | tbl_feedback_records | id=125 | 主键 | 单行更新 | 低风险 |
| 查询处理人员工作量 | Read | tbl_feedback_processing | processor_id=101 | idx_feedback_processing_processor | 读取一致性 | 低风险 |

#### 6.3.4 性能考量与索引支持
生产后台管理场景的索引支持：
- 反馈状态筛选通过`idx_feedback_records_status`索引高效过滤
- 反馈类型多选筛选通过`idx_feedback_records_types_gin` GIN索引支持复杂JSONB查询
- 处理人员工作量查询通过`idx_feedback_processing_processor`索引优化
- 处理阶段筛选通过`idx_feedback_processing_stage`索引支持

---

### 6.4 场景/故事: 用户查看反馈结果和积分

#### 6.4.1 场景描述
用户通过应用查看自己提交的反馈处理结果，查看获得的积分奖励，查看消息通知，了解反馈处理进度和积分变化情况。可以按反馈类型筛选查看。

#### 6.4.2 涉及的主要数据表
* `tbl_feedback_records` (反馈记录主表)
* `tbl_point_records` (积分记录表)
* `tbl_notifications` (消息通知表)

#### 6.4.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 查看个人反馈列表 | Read | tbl_feedback_records | user_id=456 | idx_feedback_records_user_id | 读取一致性 | 低风险 |
| 按状态筛选反馈 | Read | tbl_feedback_records | user_id=456 AND feedback_status='accepted' | idx_feedback_records_user_status_time | 读取一致性 | 低风险 |
| 按类型筛选反馈 | Read | tbl_feedback_records | user_id=456 AND feedback_types @> '["课程反馈"]' | idx_feedback_records_user_id + idx_feedback_records_types_gin | 读取一致性 | 低风险 |
| 查看积分记录 | Read | tbl_point_records | user_id=456 | idx_point_records_user_id | 读取一致性 | 低风险 |
| 查看积分历史 | Read | tbl_point_records | user_id=456 ORDER BY created_at | idx_point_records_user_time | 读取一致性 | 低风险 |
| 查看未读消息 | Read | tbl_notifications | user_id=456 AND is_read=false | idx_notifications_user_read | 读取一致性 | 低风险 |
| 标记消息已读 | Update | tbl_notifications | id=789 | 主键 | 单行更新 | 低风险 |
| 查看消息历史 | Read | tbl_notifications | user_id=456 ORDER BY created_at | idx_notifications_user_time | 读取一致性 | 低风险 |

#### 6.4.4 性能考量与索引支持
用户查看场景的索引支持：
- 个人反馈列表查询通过`idx_feedback_records_user_id`索引优化
- 反馈状态筛选通过`idx_feedback_records_user_status_time`复合索引高效支持
- 反馈类型筛选通过`idx_feedback_records_types_gin` GIN索引支持JSONB查询
- 积分历史查询通过`idx_point_records_user_time`索引优化
- 未读消息查询通过`idx_notifications_user_read`索引高效过滤
- 消息历史查询通过`idx_notifications_user_time`索引支持时序排序

---

## 7. 设计检查清单 (Design Checklist)

本检查清单分为两部分：第一部分关注数据库设计与需求、架构的对齐性；第二部分关注数据库设计方案本身的技术质量。
( ✅: 满足/通过, ❌: 不满足/未通过, N/A: 不适用 )

### 7.1 需求与架构对齐性检查

**标准化检查表格式**：每行必须对应一个具体的需求点或用户故事，确保可追溯性

| 需求点/用户故事 (来自PRD)        | 对应架构模块 (来自TD)           | 关联核心数据表 (本次设计) | ①需求覆盖完整性 | ②架构符合性 | ③一致性与无冲突 | ④业务场景满足度 | ⑤可追溯性与依赖清晰度 | 备注与待办事项                                                                                                                                  |
| -------------------------------- | ----------------------------- | --------------------- | --------------- | ----------- | --------------- | ------------- | ------------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| 教师端反馈系统（支持多选反馈类型）           | 反馈管理服务+智能审核服务 | `tbl_feedback_records`, `tbl_agent_audit_records` | ✅               | ✅           | ✅               | ✅             | ✅                   | 场景6.1已覆盖。支持教师端多种反馈类型多选，Agent智能审核，设计符合架构中微服务职责划分。feedback_types字段使用JSONB支持多选。                   |
| 学生端反馈系统               | 反馈管理服务+内容平台服务       | `tbl_feedback_records`, `tbl_agent_audit_records` | ✅               | ✅           | ✅               | ✅             | ✅                   | 场景6.2已覆盖。支持学生端题目反馈，与内容平台服务集成，架构设计满足。                                   |
| 生产后台反馈管理      | 生产后台服务+飞书平台集成               | `tbl_feedback_processing`, `tbl_feedback_records`      | ✅               | ✅           | ✅               | ✅             | ✅                   | 场景6.3已覆盖。支持后台管理功能，飞书表格集成通过feishu_record_id字段实现。支持按多选反馈类型筛选。                                                                                                                                               |
| 消息通知系统              | 消息通知服务           | `tbl_notifications`                   | ✅             | ✅         | ✅             | ✅           | ✅                 | 场景6.4已覆盖。支持多渠道消息通知，状态管理完整。                                                                                                                                           |
| 智能Agent审核 | 智能审核服务+Dify工作流平台 | `tbl_agent_audit_records` | ✅ | ✅ | ✅ | ✅ | ✅ | Agent审核记录表设计完整，支持多种工作流类型，置信度评估，性能监控。 |
| 积分激励机制 | 积分管理服务 | `tbl_point_records` | ✅ | ✅ | ✅ | ✅ | ✅ | 积分记录表支持积分计算、下发、查询，激励机制完整。 |

---

### 7.2 数据库设计质量检查

**(针对本次设计的每个核心表或整体设计方案进行评估)**

| 检查维度                               | 评估结果 (✅/❌/N/A) | 具体说明与改进建议 (如果存在问题)                                                                                                |
| -------------------------------------- | ------------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| **⑥ 设计规范符合性** | ✅                   | 严格遵循PostgreSQL和ClickHouse设计规范，命名规范统一，数据类型选择恰当，注释完整清晰。反馈类型字段已修改为JSONB支持多选。                                                                       |
|   - 命名规范                           | ✅                   | 所有表名使用tbl_前缀，字段名使用snake_case，索引名遵循规范格式。                                                                                                                                |
|   - 标准字段                           | ✅                   | 所有PostgreSQL表包含完整标准字段：id、created_at、updated_at、deleted_at、created_by、updated_by、status。                                                                                                                                |
|   - 数据类型选择                       | ✅                   | 使用TIMESTAMPTZ时间类型，JSONB存储JSON数据，NUMERIC存储精确数值，VARCHAR指定长度。feedback_types使用JSONB支持多选。                                                                                                                                |
|   - 注释质量                           | ✅                   | 所有表和字段都有清晰准确的中文注释，说明用途和取值含义。                                                                                                                                |
|   - 通用设计原则与反模式规避             | ✅                   | 遵循单一职责原则，避免巨石表，使用逻辑外键，避免过度规范化。                                                                                                                                |
| **⑦ 性能与可伸缩性考量** | ✅                   | 索引设计合理，分区策略恰当，表引擎选择最优，数据压缩策略有效。针对多选反馈类型设计了GIN索引。                                                            |
|   - 索引策略 (主键、排序键、跳数索引等)  | ✅                   | PostgreSQL表设计了完整的B-tree索引和GIN索引，ClickHouse表设计了合理的排序键和跳数索引。特别为feedback_types JSONB字段设计了GIN索引。                                                                                                                                |
|   - 分区/分片策略 (如果适用)             | ✅                   | ClickHouse表按月分区，支持分区裁剪，TTL策略合理。                                                                                                                                |
|   - 表引擎选择 (尤其 ClickHouse)        | ✅                   | ClickHouse使用MergeTree引擎，支持高效的列式存储和查询。                                                                                                                                |
|   - 数据压缩策略 (尤其 ClickHouse)      | ✅                   | ClickHouse表使用LowCardinality优化低基数字段，默认压缩策略合理。                                                                                                                                |
| **⑧ 可维护性与可扩展性** | ✅                   | 表结构清晰易懂，字段定义明确，支持业务扩展，设计具备良好的可扩展性。反馈类型多选设计支持未来扩展新的反馈类型。                                                                       |
| **⑨ 数据完整性保障** | ✅                   | 通过NOT NULL约束、UNIQUE索引、应用层校验保障数据完整性，逻辑外键设计支持一致性维护。                                                              |
| **⑩ (可选) 安全性考量** | ✅                   | 识别了用户敏感数据，通过user_id关联而非直接存储个人信息，遵循最小加密范围原则。                                                                       |
| **其他特定于数据库的检查点** (例如)      |                     |                                                                                                                                |
|   - PostgreSQL: `TIMESTAMPTZ` 使用        | ✅                   | 所有时间字段统一使用TIMESTAMPTZ类型，避免时区问题。                                                                                                                                |
|   - PostgreSQL: `JSONB` 使用合理性        | ✅                   | 合理使用JSONB存储反馈类型多选、模板参数、审核分类等半结构化数据，并创建GIN索引。                                                                                                                                |
|   - ClickHouse: `LowCardinality` 使用     | ✅                   | 合理使用LowCardinality优化用户类型、反馈类型等低基数字段。                                                                                                                                |
|   - ClickHouse: 物化视图设计合理性        | N/A                   | 当前设计未涉及物化视图，如需要可在后续迭代中添加。                                                                                                                                |

## 8. 参考资料与附录
* 产品需求文档 (PRD): [产品需求文档：运营工具_老师&学生课程题目反馈系统 v9.0.0]
* 技术架构方案 (TD): [运营工具老师学生课程题目反馈系统 - 服务端架构设计文档 v1.0]
* 数据实体设计 (DE): [PRD 数据提取（数据库设计导向）]
* PostgreSQL 设计规范与约束文件: [`rules-postgresql-code-v2.md`]
* ClickHouse 设计规范与约束文件: [`rules-clickhouse-code-v2.md`]
* 技术栈约束文件: [`rules-tech-stack.md`]

---

**数据库设计完成，已按模板要求完成质量检查，等待您的命令，指挥官**
---
description: 
globs: 
alwaysApply: false
---
# 前端 Vite 项目代码生成规则

> **核心原则：简洁优先，可维护性第一**

## 边界
- 遵循 ESLint 与 Prettier 规范
- 强制类型检查，使用 TypeScript 或 JSDoc 类型注解
- 禁止在生产代码中使用 `console.log`；统一使用项目日志机制
- 禁止在组件中直接调用 API；使用 hooks/service 层隔离
- 状态管理与 UI 表现分离
- 资源引用使用别名路径 `@/` 而非相对路径
- 全局样式与组件样式分离

## 约定
- 命名：变量/函数使用 `camelCase`；组件使用 `PascalCase`；常量使用 `UPPER_SNAKE`
- 文件命名：组件文件使用 `PascalCase.jsx/tsx`；工具/hooks 文件使用 `camelCase.js/ts`
- 导入顺序：React/框架 → 第三方库 → 内部组件/hooks → 样式；组间空行
- 函数组件：使用箭头函数，不使用函数声明
- 样式：优先使用 CSS Modules 或 styled-components
- 路由：集中配置，使用懒加载

## Vite 特有规范
- 静态资源引用：使用 `import` 语法而非 URL 字符串
- 环境变量：统一使用 `import.meta.env.VITE_*` 访问
- 路径别名：在 `vite.config.js` 中配置，组件引用使用 `@/components/*`
- 构建优化：预加载关键依赖，代码分割

## 组件结构
```javascript
// GoodComponent.tsx
import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';

// 第三方库
import { format } from 'date-fns';

// 内部组件/hooks
import useApiData from '@/hooks/useApiData';
import Button from '@/components/common/Button';

// 样式
import styles from './GoodComponent.module.css';

// 类型定义
interface Props {
  title: string;
  onAction: () => void;
}

const GoodComponent: React.FC<Props> = ({ title, onAction }) => {
  // 状态/hooks
  const [isActive, setIsActive] = useState(false);
  const { data, loading } = useApiData('/endpoint');
  const user = useSelector(state => state.user);

  // 副作用
  useEffect(() => {
    // 副作用逻辑
    return () => {
      // 清理逻辑
    };
  }, []);

  // 事件处理函数
  const handleClick = () => {
    setIsActive(!isActive);
    onAction();
  };

  // 渲染逻辑
  if (loading) return <div>Loading...</div>;

  return (
    <div className={styles.container}>
      <h2>{title}</h2>
      <p>{format(new Date(), 'yyyy-MM-dd')}</p>
      <Button onClick={handleClick}>
        {isActive ? 'Active' : 'Inactive'}
      </Button>
    </div>
  );
};

export default GoodComponent;
```

## API 请求规范
```javascript
// userService.ts
import { apiClient } from '@/utils/apiClient';
import type { User } from '@/types';

export const UserService = {
  async getUser(id: number): Promise<User> {
    try {
      const response = await apiClient.get(`/users/${id}`);
      return response.data;
    } catch (error) {
      // 统一错误处理
      throw error;
    }
  },
  
  async updateUser(id: number, data: Partial<User>): Promise<User> {
    try {
      const response = await apiClient.put(`/users/${id}`, data);
      return response.data;
    } catch (error) {
      // 统一错误处理
      throw error;
    }
  }
};
```

## 路由配置
```javascript
// router.ts
import { lazy } from 'react';
import { createBrowserRouter } from 'react-router-dom';

// 懒加载路由组件
const Home = lazy(() => import('@/pages/Home'));
const About = lazy(() => import('@/pages/About'));
const UserProfile = lazy(() => import('@/pages/UserProfile'));

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Home />,
  },
  {
    path: '/about',
    element: <About />,
  },
  {
    path: '/users/:id',
    element: <UserProfile />,
  }
]);
```

## 状态管理 (Redux)
```javascript
// userSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { UserService } from '@/services/userService';

export const fetchUser = createAsyncThunk(
  'user/fetchUser',
  async (userId: number) => {
    const response = await UserService.getUser(userId);
    return response;
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState: {
    data: null,
    loading: false,
    error: null,
  },
  reducers: {
    // 同步 reducers
    clearUser: (state) => {
      state.data = null;
    },
  },
  extraReducers: (builder) => {
    // 异步 reducers
    builder
      .addCase(fetchUser.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUser.fulfilled, (state, action) => {
        state.data = action.payload;
        state.loading = false;
      })
      .addCase(fetchUser.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      });
  },
});

export const { clearUser } = userSlice.actions;
export default userSlice.reducer;
```

## 测试规范
```javascript
// Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import Button from './Button';

describe('Button component', () => {
  test('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  test('calls onClick handler when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

## Vite 配置
```javascript
// vite.config.js
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
    },
  },
  build: {
    outDir: 'dist',
    minify: 'terser',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@mui/material', '@emotion/react'],
        },
      },
    },
  },
});
```
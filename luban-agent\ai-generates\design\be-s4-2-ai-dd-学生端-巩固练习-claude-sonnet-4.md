# 数据库设计文档 - 学生端巩固练习系统 - V1.0

**模板版本**: v1.3
**最后更新日期**: 2025-06-01
**设计负责人**: claude-sonnet-4
**评审人**: 待填写
**状态**: 草稿

## 📋 输出格式总体要求
* **IMPORTANT AI:** 本模板定义了所有输出的标准化格式，必须严格遵循：
  - 所有表格都有固定的列结构，不得随意增减
  - ER图必须使用Mermaid语法，按照标准格式绘制
  - CRUD分析必须使用标准7列表格格式
  - 检查清单必须逐项填写，使用✅/❌/☐/N/A标记
  - 所有DDL代码必须包含完整中文注释

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人          | 修订内容摘要                                                 |
| ------ | ---------- | --------------- | ------------------------------------------------------------ |
| V1.0   | 2025-06-01 | claude-sonnet-4 | 初始草稿                                                     |
| V1.1   | 2025-06-03 | claude-sonnet-4 | 重复功能表优化：合并tbl_study_sessions和tbl_retry_sessions，删除重复的tbl_study_reports定义，修正索引策略错误 |
| V1.2   | 2025-06-05 | claude-sonnet-4 | 表结构升级：扩展支持多种学习任务类型(AI课、巩固练习、作业、测试等)，添加tbl_task_questions表管理题目结构，优化字段设计 |

## 1. 项目概述与设计目标

### 1.1 项目背景与范围
本项目是学生端统一学习系统的核心数据库设计，旨在支持智能化的学生学习平台。系统支持多种学习任务类型，包括AI课、巩固练习、普通作业、测试任务、资源任务等，通过统一的会话管理和数据记录提升学生学习效果。核心功能包括多类型学习会话管理、智能推题、实时掌握度计算、作答处理与反馈、学习报告生成、再练一次推荐等。本次数据库设计覆盖多类型学习数据管理、学习行为记录、掌握度计算支撑和学习效果分析等核心数据存储需求。

### 1.2 设计目标回顾
- 提供完整的、符合规范的表创建语句（DDL）
- 清晰描述表间关系，并提供 ER 图
- 详细说明核心用户场景/用户故事所涉及的表以及其 CRUD 操作方式
- 阐述关键表的索引策略及其设计原因
- 对核心表进行数据量预估和增长趋势分析
- 明确数据生命周期管理/归档策略
- 记录特殊设计考量与权衡
- 总结设计如何体现对应数据库（PostgreSQL/ClickHouse）的关键规范

## 2. 数据库环境与总体说明

### 2.1 目标数据库类型
- PostgreSQL 17.x（用于核心业务数据）
- ClickHouse 23.7.16（用于日志和分析数据）

### 2.2 数据库创建语句 (如果适用)
使用现有数据库实例，目标数据库名称：
- PostgreSQL: `luban_exercise_db`
- ClickHouse: `luban_analytics_db`

### 2.3 数据库选型决策摘要

**技术选型决策表**：

| 实体/表名 | 数据库类型 | 选型依据 | 数据特征 | 查询模式 | 预估数据量 |
|---------|-----------|---------|---------|---------|-----------|
| tbl_study_sessions | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 大表(1000万) |
| tbl_session_knowledge_points | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 大表(5000万) |
| tbl_answer_records | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 大表(5000万) |
| tbl_study_reports | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 中表(500万) |
| tbl_wrong_questions | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 大表(2000万) |
| tbl_drawing_records | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 大表(1000万) |
| tbl_mastery_knowledge_point_mastery (引用) | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 大表(1000万) |
| tbl_mastery_externalized_mastery (引用) | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 中表(500万) |
| tbl_mastery_study_task (引用) | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 中表(100万) |
| tbl_answer_logs_history | ClickHouse | 仅INSERT分析 | 时序数据 | 聚合分析 | 大表(亿级) |
| tbl_study_behavior_logs | ClickHouse | 仅INSERT分析 | 时序数据 | 聚合分析 | 大表(亿级) |

## 3. 数据库表详细设计 - PostgreSQL

### 3.1 设计思路与架构说明

#### 3.1.1 多类型学习任务统一设计
基于用户补充信息，系统需要支持多种学习任务类型，每种类型有不同的题目组织结构：

**支持的学习类型**：
1. **AI课 (study_type=1)**：只有AI课有练习会话，题目结构为 AI课id → 组件列表 → 每个组件包含的题目
2. **巩固练习 (study_type=2)**：只有巩固练习有练习会话，题目结构为 巩固练习id → 题组列表 → 题目列表
3. **普通作业 (study_type=3)**：教师圈选固定题目，结构为 作业任务id → 题目列表，学生需全部作答
4. **测试任务 (study_type=4)**：测试类型任务，可能有时间限制和特殊规则
5. **资源任务 (study_type=5)**：资源学习类型任务
6. **其他 (study_type=99)**：预留扩展空间

**题目来源差异**：
- **推题服务动态获取 (question_source=1)**：AI课和巩固练习从推题服务获取题目
- **固定题目列表 (question_source=2)**：普通作业等使用教师预设的固定题目

#### 3.1.2 表结构设计原则
1. **统一会话管理**：`tbl_study_sessions` 统一管理所有类型的学习会话
2. **推题信息记录**：`tbl_answer_records` 记录题目来源信息，区分推题服务和固定题目
3. **统一作答记录**：`tbl_answer_records` 统一记录所有类型任务的作答数据
4. **掌握度数据分离**：`tbl_session_knowledge_points` 专门记录知识点掌握度变化
5. **扩展性设计**：通过枚举值和JSON字段支持未来新增的学习类型

#### 3.1.3 关键设计决策
- **字段简化**：移除 `created_by`、`updated_by`、`deleted_at` 等字段，因为学习会话绑定单一学生
- **表名调整**：从 `tbl_exercise_sessions` 改为 `tbl_study_sessions`，体现更广泛的适用范围
- **任务ID统一**：使用 `task_id` 字段统一标识不同类型的任务（AI课ID、巩固练习ID、作业ID等）
- **掌握度数据分离**：薄弱知识点判断由掌握度服务负责，本系统只记录掌握度变化情况和学习效果统计

#### 3.1.4 掌握度数据处理说明
- **掌握度服务职责**：判断薄弱知识点、计算掌握度变化
- **学习系统职责**：记录学习过程、统计掌握度提升的知识点数量、为再练一次提供数据支撑
- **数据流向**：学习完成后 → 掌握度服务计算 → 返回掌握度变化数据 → 学习系统记录统计信息

### 3.2 数据库表清单

#### PostgreSQL表清单
| 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 | 预定数据库类型 |
|------|------------------|-------------|---------|--------------|
| `tbl_study_sessions` | 统一的学习会话管理，支持AI课、巩固练习、作业、测试等多种学习任务类型的会话记录和状态管理 | 大表(1000万) | 频繁CRUD操作，需要事务支持 | PostgreSQL |
| `tbl_session_knowledge_points` | 学习会话知识点掌握度记录，存储每次学习涉及的知识点及掌握度变化情况 | 大表(5000万) | 频繁CRUD操作，复杂关联查询 | PostgreSQL |
| `tbl_answer_records` | 练习题目作答记录，支持掌握度计算 | 大表(5000万) | 频繁CRUD操作，复杂关联查询 | PostgreSQL |
| `tbl_study_reports` | 学习报告数据，展示学习成果 | 中表(500万) | 频繁CRUD操作，需要事务支持 | PostgreSQL |
| `tbl_task_reports` | 任务维度统计数据，支持教师查询指定学科时间段内的任务汇总报告 | 中表(100万) | 频繁CRUD操作，需要事务支持 | PostgreSQL |
| `tbl_wrong_questions` | 错题记录管理 | 大表(2000万) | 频繁CRUD操作，复杂关联查询 | PostgreSQL |
| `tbl_drawing_records` | 勾画记录数据 | 大表(1000万) | 频繁CRUD操作，需要事务支持 | PostgreSQL |
| `tbl_mastery_knowledge_point_mastery` (引用) | 学生知识点掌握度数据，来自掌握度策略系统 | 大表(1000万) | 频繁CRUD操作，掌握度计算核心 | PostgreSQL |
| `tbl_mastery_externalized_mastery` (引用) | 学生课程外化掌握度数据，来自掌握度策略系统 | 中表(500万) | 频繁CRUD操作，掌握度展示 | PostgreSQL |
| `tbl_mastery_study_task` (引用) | 学习任务信息，来自掌握度策略系统 | 中表(100万) | 频繁CRUD操作，任务管理 | PostgreSQL |

#### ClickHouse表清单  
| 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 | 预定数据库类型 |
|------|------------------|-------------|---------|--------------|
| `tbl_answer_logs_history` | 历史答题记录分析 | 大表(亿级) | 仅INSERT分析查询，时序数据 | ClickHouse |
| `tbl_study_behavior_logs` | 学习行为日志分析 | 大表(亿级) | 仅INSERT分析查询，时序数据 | ClickHouse |

---

### 3.3 表名: `tbl_study_sessions`

#### 3.3.1 表功能说明
存储统一学习会话的完整记录，支持多种学习任务类型的会话管理。该表是整个学习系统的核心，管理学生的学习生命周期，支持练习状态变化、进度跟踪、中途退出保存等功能。通过study_type字段区分不同的学习任务类型，通过session_type字段区分正常学习和再练一次，通过trigger_session_id字段关联再练会话与原始会话的关系。

#### 3.3.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_study_sessions (
  session_id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  student_id BIGINT NOT NULL,
  subject_id BIGINT NOT NULL,
  task_id VARCHAR(64) NOT NULL DEFAULT '',
  textbook_id BIGINT NOT NULL DEFAULT 0,
  knowledge_id BIGINT NOT NULL DEFAULT 0,
  study_type BIGINT NOT NULL,
  session_type BIGINT NOT NULL DEFAULT 1,
  session_status BIGINT NOT NULL DEFAULT 0,
  current_question_index BIGINT NOT NULL DEFAULT 0,
  total_questions BIGINT NOT NULL DEFAULT 0,
  start_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  end_time TIMESTAMPTZ NULL,
  session_config JSONB NULL,
  -- 再练一次相关字段
  trigger_session_id BIGINT NULL,
  -- 标准字段
  create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_study_sessions IS '统一学习会话表 - 存储各种学习任务的会话记录和状态管理';
COMMENT ON COLUMN tbl_study_sessions.session_id IS '会话主键ID';
COMMENT ON COLUMN tbl_study_sessions.student_id IS '学生ID，关联用户中心服务';
COMMENT ON COLUMN tbl_study_sessions.subject_id IS '科目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_study_sessions.task_id IS '任务ID，根据study_type对应不同的任务：AI课ID、巩固练习ID、作业任务ID等';
COMMENT ON COLUMN tbl_study_sessions.textbook_id IS '教材ID，AI课、巩固练习、拓展练习等绑定的教材ID，对应业务树根节点ID';
COMMENT ON COLUMN tbl_study_sessions.knowledge_id IS '知识点ID，AI课、巩固练习、拓展练习等直接关联的知识点ID';
COMMENT ON COLUMN tbl_study_sessions.study_type IS '学习类型 (1:AI课, 2:巩固练习, 3:拓展练习, 4:普通作业, 5:测试任务, 6:资源任务, 99:其他)';
COMMENT ON COLUMN tbl_study_sessions.session_type IS '会话类型 (1:正常学习, 2:再练一次)';
COMMENT ON COLUMN tbl_study_sessions.session_status IS '学习状态 (0:未开始, 1:学习中, 2:已完成, 3:已退出, 4:已暂停)';
COMMENT ON COLUMN tbl_study_sessions.current_question_index IS '当前题目序号，从0开始';
COMMENT ON COLUMN tbl_study_sessions.total_questions IS '本次学习的题目总数量';
COMMENT ON COLUMN tbl_study_sessions.start_time IS '学习开始时间';
COMMENT ON COLUMN tbl_study_sessions.end_time IS '学习结束时间，完成时填入';
COMMENT ON COLUMN tbl_study_sessions.session_config IS '会话配置信息，JSON格式，包含学习参数、推题配置等';
COMMENT ON COLUMN tbl_study_sessions.trigger_session_id IS '触发此再练会话的原始会话ID，仅再练一次时有值';
COMMENT ON COLUMN tbl_study_sessions.create_time IS '记录创建时间';
COMMENT ON COLUMN tbl_study_sessions.update_time IS '记录最后更新时间';

-- 索引定义
CREATE INDEX idx_study_sessions_student_knowledge ON tbl_study_sessions(student_id, knowledge_id);
-- 支持按科目和学习类型组合查询
CREATE INDEX idx_study_sessions_student_type ON tbl_study_sessions(student_id, study_type);
-- 支持按学生和学习类型组合查询
CREATE INDEX idx_study_sessions_student_task ON tbl_study_sessions(student_id, task_id);
-- 支持学生任务组合查询
CREATE INDEX idx_study_sessions_task_id ON tbl_study_sessions(task_id);
-- 支持按任务查询学习会话
CREATE INDEX idx_study_sessions_status ON tbl_study_sessions(session_status);
-- 支持按学习状态查询
CREATE INDEX idx_study_sessions_start_time ON tbl_study_sessions(start_time);
-- 支持按开始时间排序查询
CREATE INDEX idx_study_sessions_study_type ON tbl_study_sessions(study_type);
-- 支持按学习类型查询
CREATE INDEX idx_study_sessions_trigger_session ON tbl_study_sessions(trigger_session_id) WHERE trigger_session_id IS NOT NULL;
-- 支持查询再练会话关联的原始会话
CREATE INDEX idx_study_sessions_subject_type ON tbl_study_sessions(subject_id, study_type);
-- 支持按科目和学习类型组合查询
```

#### 3.3.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `idx_study_sessions_student_id` | student_id | btree | 学生学习历史查询 | 查询学生的所有学习会话 | 高效单列查询 |
| `idx_study_sessions_task_id` | task_id | btree | 任务学习统计查询 | 查询特定任务的学习情况 | 高效单列查询 |
| `idx_study_sessions_status` | session_status | btree | 学习状态筛选 | 查询进行中或已完成的学习 | 高效状态过滤 |
| `idx_study_sessions_start_time` | start_time | btree | 时间范围查询 | 按时间排序的学习列表 | 高效时间排序 |
| `idx_study_sessions_student_task` | student_id,task_id | btree | 学生任务组合查询 | 查询学生在特定任务的学习记录 | 高效复合查询 |
| `idx_study_sessions_study_type` | study_type | btree | 学习类型筛选 | 区分AI课、巩固练习、作业等 | 高效类型过滤 |
| `idx_study_sessions_trigger_session` | trigger_session_id | btree | 再练会话关联查询 | 查询原始会话触发的再练记录 | 高效关联查询 |
| `idx_study_sessions_student_type` | student_id,study_type | btree | 学生类型组合查询 | 查询学生的特定类型学习会话 | 高效复合查询 |
| `idx_study_sessions_subject_type` | subject_id,study_type | btree | 科目类型组合查询 | 查询特定科目的特定类型学习 | 高效复合查询 |
| `idx_exercise_sessions_type` | session_type | btree | 会话类型筛选 | 区分正常练习和再练一次 | 高效类型过滤 |
| `idx_exercise_sessions_trigger_session` | trigger_session_id | btree | 再练会话关联查询 | 查询原始会话触发的再练记录 | 高效关联查询 |
| `idx_exercise_sessions_student_type` | student_id,session_type | btree | 学生类型组合查询 | 查询学生的特定类型会话 | 高效复合查询 |

---

### 3.4 表名: `tbl_session_knowledge_points`

#### 3.4.1 表功能说明
存储每次学习会话的知识点掌握度变化汇总信息。每个学习会话只有一条记录，通过JSON字段存储该会话涉及的所有知识点及其掌握度变化情况。支持中途退出后继续学习时在同一条记录上更新数据。用于学习效果统计和再练一次的推荐依据。

#### 3.4.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_session_knowledge_points (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  session_id BIGINT NOT NULL,
  total_knowledge_points BIGINT NOT NULL DEFAULT 0,
  improved_points_count BIGINT NOT NULL DEFAULT 0,
  knowledge_points_data JSONB NOT NULL,
  -- 标准字段
  create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_session_knowledge_points IS '学习会话知识点掌握度汇总表 - 每个会话一条记录，存储所有知识点掌握度变化';
COMMENT ON COLUMN tbl_session_knowledge_points.id IS '主键ID';
COMMENT ON COLUMN tbl_session_knowledge_points.session_id IS '学习会话ID，关联tbl_study_sessions.session_id';
COMMENT ON COLUMN tbl_session_knowledge_points.total_knowledge_points IS '本次学习涉及的知识点总数';
COMMENT ON COLUMN tbl_session_knowledge_points.improved_points_count IS '掌握度有提升的知识点数量';
COMMENT ON COLUMN tbl_session_knowledge_points.knowledge_points_data IS '知识点详细数据，JSON格式存储所有知识点的掌握度变化信息';
COMMENT ON COLUMN tbl_session_knowledge_points.create_time IS '记录创建时间';
COMMENT ON COLUMN tbl_session_knowledge_points.update_time IS '记录最后更新时间';

-- 索引定义
CREATE UNIQUE INDEX uk_session_knowledge_points_session ON tbl_session_knowledge_points(session_id);
-- 确保每个会话只有一条记录
CREATE INDEX idx_session_knowledge_points_improved ON tbl_session_knowledge_points(improved_points_count);
-- 支持按掌握度提升数量查询
```

#### 3.4.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_session_knowledge_points_session` | session_id | btree unique | 会话唯一性约束 | 确保每个会话只有一条记录 | 高效唯一性约束 |
| `idx_session_knowledge_points_improved` | improved_points_count | btree | 掌握度提升查询 | 按掌握度提升数量统计分析 | 高效数值查询 |

#### 3.4.4 knowledge_points_data字段设计说明

**JSON结构示例**：
```json
{
  "knowledge_points": [
    {
      "knowledge_point_id": 2001,
      "mastery_before": 0.65,
      "mastery_after": 0.80,
      "mastery_change": 0.15,
      "question_count": 5,
      "correct_count": 4
    },
    {
      "knowledge_point_id": 2002,
      "mastery_before": 0.45,
      "mastery_after": 0.53,
      "mastery_change": 0.08,
      "question_count": 3,
      "correct_count": 2
    }
  ]
}
```

---

### 3.5 表名: `tbl_answer_records`

#### 3.5.1 表功能说明
存储学生在各种学习任务中对每道题目的作答情况和结果。该表是掌握度计算的核心数据源，记录详细的作答信息包括答案内容、正确性、作答时长、题目难度、推题来源等，支持实时掌握度计算和学习效果分析。同时记录题目的推题信息，区分是从推题服务获取还是固定题目。

#### 3.5.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_answer_records (
  answer_record_id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  session_id BIGINT NOT NULL DEFAULT 0,
  widget_index BIGINT NOT NULL DEFAULT 0,
  question_index BIGINT NOT NULL,
  question_id BIGINT NOT NULL,
  student_answer TEXT NULL,
  answer_result BIGINT NOT NULL,
  answer_duration BIGINT NOT NULL DEFAULT 0,
  question_difficulty NUMERIC(3,2) NOT NULL DEFAULT 1.0,
  correct_combo_count BIGINT NOT NULL DEFAULT 0,
  question_source BIGINT NOT NULL DEFAULT 1,
  answer_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  answer_metadata JSONB NULL,
  -- 标准字段
  create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_answer_records IS '练习题目作答记录表 - 存储学生作答情况，支持掌握度计算';
COMMENT ON COLUMN tbl_answer_records.answer_record_id IS '作答记录主键ID';
COMMENT ON COLUMN tbl_answer_records.session_id IS '所属学习会话ID，关联tbl_study_sessions.session_id，只有AI课和巩固练习有学习会话';
COMMENT ON COLUMN tbl_answer_records.widget_index IS '组件序号，只有AI课有，关联内容平台服务';
COMMENT ON COLUMN tbl_answer_records.question_id IS '题目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_answer_records.question_index IS '题目在本次练习中的序号，从0开始';
COMMENT ON COLUMN tbl_answer_records.student_answer IS '学生提交的答案内容';
COMMENT ON COLUMN tbl_answer_records.answer_result IS '作答结果 (1:正确, 2:错误, 3:部分正确)';
COMMENT ON COLUMN tbl_answer_records.answer_duration IS '学生作答该题的时间（秒）';
COMMENT ON COLUMN tbl_answer_records.question_difficulty IS '题目难度系数，用于掌握度计算';
COMMENT ON COLUMN tbl_answer_records.correct_combo_count IS '答题连续答对数';
COMMENT ON COLUMN tbl_answer_records.question_source IS '题目来源 (1:推题服务动态获取, 2:固定题目列表)';
COMMENT ON COLUMN tbl_answer_records.answer_time IS '提交答案的时间戳';
COMMENT ON COLUMN tbl_answer_records.answer_metadata IS '作答元数据，JSON格式';
COMMENT ON COLUMN tbl_answer_records.create_time IS '记录创建时间';
COMMENT ON COLUMN tbl_answer_records.update_time IS '记录最后更新时间';

-- 索引定义
CREATE INDEX idx_answer_records_session_id ON tbl_answer_records(session_id);
-- 支持按会话查询作答记录
CREATE INDEX idx_answer_records_question_id ON tbl_answer_records(question_id);
-- 支持按题目查询作答情况
CREATE INDEX idx_answer_records_session_index ON tbl_answer_records(session_id, question_index);
-- 支持按会话和题目序号查询
CREATE INDEX idx_answer_records_result ON tbl_answer_records(answer_result);
-- 支持按作答结果统计查询
```

#### 3.5.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `idx_answer_records_session_id` | session_id | btree | 会话作答记录查询 | 查询某次练习的所有作答记录 | 高效单列查询 |
| `idx_answer_records_question_id` | question_id | btree | 题目作答统计 | 查询特定题目的作答情况 | 高效单列查询 |
| `idx_answer_records_answer_time` | answer_time | btree | 时间序列查询 | 按时间排序的作答记录 | 高效时间排序 |
| `idx_answer_records_session_index` | session_id,question_index | btree | 会话题目定位 | 快速定位会话中的特定题目 | 高效复合查询 |
| `idx_answer_records_result` | answer_result | btree | 作答结果统计 | 统计正确率等指标 | 高效结果过滤 |

---

### 3.6 表名: `tbl_study_reports`

#### 3.6.1 表功能说明
存储学习报告数据，总结学生每次完整练习的学习报告，展示掌握度变化和学习成果。该表汇总练习结果，提供学习效果的可视化展示，包括正确率、学习时长、掌握度变化、IP角色反馈等信息。

#### 3.6.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_study_reports (
  study_report_id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  session_id BIGINT NOT NULL DEFAULT 0,
 	task_id BIGINT NOT NULL DEFAULT 0,
 	student_task_id BIGINT NOT NULL DEFAULT 0,
  student_id BIGINT NOT NULL,
  mastery_change NUMERIC(5,2) NOT NULL DEFAULT 0.00,
  study_duration BIGINT NOT NULL DEFAULT 0,
  question_count BIGINT NOT NULL DEFAULT 0,
  accuracy_rate NUMERIC(5,2) NOT NULL DEFAULT 0.00,
  ip_feedback TEXT NOT NULL,
  recommend_retry BOOLEAN NOT NULL DEFAULT FALSE,
  report_data JSONB NULL,
  generated_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  -- 标准字段
  create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_study_reports IS '学习报告表 - 存储练习完成后的学习成果报告';
COMMENT ON COLUMN tbl_study_reports.study_report_id IS '报告主键ID';
COMMENT ON COLUMN tbl_study_reports.session_id IS '关联的练习会话ID，关联tbl_study_sessions.id';
COMMENT ON COLUMN tbl_study_reports.task_id IS '教师布置的任务ID';
COMMENT ON COLUMN tbl_study_reports.student_task_id IS '学生任务ID，教师任务拆包后的子任务ID';
COMMENT ON COLUMN tbl_study_reports.student_id IS '学生ID，关联用户中心服务';
COMMENT ON COLUMN tbl_study_reports.mastery_change IS '本次练习前后的掌握度变化值';
COMMENT ON COLUMN tbl_study_reports.study_duration IS '本次练习的总学习时长（秒）';
COMMENT ON COLUMN tbl_study_reports.question_count IS '本次练习完成的题目数量';
COMMENT ON COLUMN tbl_study_reports.accuracy_rate IS '本次练习的正确率百分比';
COMMENT ON COLUMN tbl_study_reports.ip_feedback IS '基于正确率展示的IP角色反馈文案';
COMMENT ON COLUMN tbl_study_reports.recommend_retry IS '是否推荐学生再练一次';
COMMENT ON COLUMN tbl_study_reports.report_data IS '报告详细数据，JSON格式';
COMMENT ON COLUMN tbl_study_reports.generated_time IS '报告生成时间';
COMMENT ON COLUMN tbl_study_reports.create_time IS '记录创建时间';
COMMENT ON COLUMN tbl_study_reports.update_time IS '记录最后更新时间';

-- 索引定义
CREATE UNIQUE INDEX uk_study_reports_session ON tbl_study_reports(session_id);
-- 确保每个会话只有一个学习报告
CREATE INDEX idx_study_reports_student_id ON tbl_study_reports(student_id);
-- 支持按学生查询学习报告
CREATE INDEX idx_study_reports_generated_time ON tbl_study_reports(generated_time);
-- 支持按生成时间排序查询
CREATE INDEX idx_study_reports_accuracy ON tbl_study_reports(accuracy_rate);
-- 支持按正确率统计查询
```

#### 3.6.3 索引设计策略

**索引设计标准表格**：

| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_study_reports_session` | session_id | btree unique | 会话报告唯一性 | 确保每个会话只有一个报告 | 高效唯一性约束 |
| `idx_study_reports_student_id` | student_id | btree | 学生报告历史查询 | 查询学生的所有学习报告 | 高效单列查询 |
| `idx_study_reports_generated_time` | generated_time | btree | 时间排序查询 | 按时间排序的报告列表 | 高效时间排序 |
| `idx_study_reports_accuracy` | accuracy_rate | btree | 正确率统计 | 按正确率范围统计分析 | 高效数值范围查询 |

---

### 3.7 表名: `tbl_task_reports`

#### 3.7.1 表功能说明
存储任务维度的统计数据，支持教师查询指定学科指定时间段内的全部任务汇总报告。该表记录每个任务的完成进度、正确率和需要关注的题目数等统计信息，为教师提供任务级别的数据分析和学生学习情况监控。

#### 3.7.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_task_reports (
  task_report_id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  task_id VARCHAR(64) NOT NULL,
  subject_id BIGINT NOT NULL,
  study_type BIGINT NOT NULL,
  total_students BIGINT NOT NULL DEFAULT 0,
  completed_students BIGINT NOT NULL DEFAULT 0,
  completion_rate NUMERIC(5,2) NOT NULL DEFAULT 0.00,
  average_accuracy NUMERIC(5,2) NOT NULL DEFAULT 0.00,
  attention_questions_count BIGINT NOT NULL DEFAULT 0,
  total_questions BIGINT NOT NULL DEFAULT 0,
  total_answers BIGINT NOT NULL DEFAULT 0,
  correct_answers BIGINT NOT NULL DEFAULT 0,
  average_duration BIGINT NOT NULL DEFAULT 0,
  stat_date DATE NOT NULL,
  -- 标准字段
  status BOOLEAN NOT NULL DEFAULT true,
  create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_task_reports IS '任务统计表 - 存储任务维度的统计数据，支持教师查询任务汇总报告';
COMMENT ON COLUMN tbl_task_reports.task_report_id IS '统计记录主键ID';
COMMENT ON COLUMN tbl_task_reports.task_id IS '任务ID，根据study_type对应不同的任务：AI课ID、巩固练习ID、作业任务ID等';
COMMENT ON COLUMN tbl_task_reports.subject_id IS '科目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_task_reports.study_type IS '学习类型 (1:AI课, 2:巩固练习, 3:拓展练习, 4:普通作业, 5:测试任务, 6:资源任务, 99:其他)';
COMMENT ON COLUMN tbl_task_reports.total_students IS '参与该任务的学生总数';
COMMENT ON COLUMN tbl_task_reports.completed_students IS '完成该任务的学生数';
COMMENT ON COLUMN tbl_task_reports.completion_rate IS '任务完成率百分比';
COMMENT ON COLUMN tbl_task_reports.average_accuracy IS '任务平均正确率百分比';
COMMENT ON COLUMN tbl_task_reports.attention_questions_count IS '需要关注的题目数（生产报告时写入）';
COMMENT ON COLUMN tbl_task_reports.total_questions IS '任务总题目数';
COMMENT ON COLUMN tbl_task_reports.total_answers IS '总作答次数';
COMMENT ON COLUMN tbl_task_reports.correct_answers IS '正确作答次数';
COMMENT ON COLUMN tbl_task_reports.average_duration IS '平均完成时长（秒）';
COMMENT ON COLUMN tbl_task_reports.stat_date IS '统计日期';
COMMENT ON COLUMN tbl_task_reports.status IS '记录状态 (true:正常, false:删除)';
COMMENT ON COLUMN tbl_task_reports.create_time IS '记录创建时间';
COMMENT ON COLUMN tbl_task_reports.update_time IS '记录最后更新时间';

-- 索引定义
CREATE UNIQUE INDEX uk_task_statistics_task_date ON tbl_task_reports(task_id, stat_date);
-- 确保每个任务每天只有一条统计记录
CREATE INDEX idx_task_statistics_subject_date ON tbl_task_reports(subject_id, stat_date);
-- 支持按学科和日期查询任务统计
CREATE INDEX idx_task_statistics_study_type ON tbl_task_reports(study_type);
-- 支持按学习类型筛选
CREATE INDEX idx_task_statistics_completion_rate ON tbl_task_reports(completion_rate);
-- 支持按完成率排序
```

#### 3.7.3 索引设计策略

**索引设计标准表格**：

| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_task_statistics_task_date` | task_id, stat_date | btree unique | 任务日期唯一性 | 确保每个任务每天只有一条统计记录 | 高效唯一性约束 |
| `idx_task_statistics_subject_date` | subject_id, stat_date | btree | 学科日期查询 | 教师查询指定学科指定时间段的任务统计 | 高效复合查询 |
| `idx_task_statistics_study_type` | study_type | btree | 学习类型筛选 | 按任务类型筛选统计数据 | 高效单列查询 |
| `idx_task_statistics_completion_rate` | completion_rate | btree | 完成率排序 | 按完成率排序展示任务 | 高效数值排序 |

---

### 3.8 表名: `tbl_wrong_questions`

#### 3.8.1 表功能说明
存储错题记录，包括自动添加错题和手动添加的错题本记录。该表支持错题管理功能，记录学生的错误作答情况，支持错因分析和错题复习，是个性化学习的重要数据基础。

#### 3.8.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_wrong_questions (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  student_id BIGINT NOT NULL,
  question_id BIGINT NOT NULL,
  answer_record_id BIGINT NULL,
  add_method BIGINT NOT NULL,
  error_reason_tags TEXT NULL,
  wrong_metadata JSONB NULL,
  -- 标准字段
  status BOOLEAN NOT NULL DEFAULT true,
  create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_wrong_questions IS '错题记录表 - 存储自动和手动添加的错题记录';
COMMENT ON COLUMN tbl_wrong_questions.id IS '错题记录主键ID';
COMMENT ON COLUMN tbl_wrong_questions.student_id IS '学生ID，关联用户中心服务';
COMMENT ON COLUMN tbl_wrong_questions.question_id IS '题目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_wrong_questions.answer_record_id IS '关联的作答记录ID，手动添加时可为空';
COMMENT ON COLUMN tbl_wrong_questions.add_method IS '错题添加方式 (1:自动添加, 2:手动添加)';
COMMENT ON COLUMN tbl_wrong_questions.error_reason_tags IS '错因分类标签，多个标签用逗号分隔';
COMMENT ON COLUMN tbl_wrong_questions.wrong_metadata IS '错题元数据，JSON格式';
COMMENT ON COLUMN tbl_wrong_questions.status IS '记录状态 (false:禁用, true:启用)';
COMMENT ON COLUMN tbl_wrong_questions.create_time IS '记录创建时间';
COMMENT ON COLUMN tbl_wrong_questions.update_time IS '记录最后更新时间';

-- 索引定义
CREATE INDEX idx_wrong_questions_question_id ON tbl_wrong_questions(question_id);
-- 支持按题目查询错题情况
CREATE UNIQUE INDEX uk_wrong_questions_student_add_time ON tbl_wrong_questions(student_id, create_time);
-- 确保学生对同一题目只有一条错题记录
```

#### 3.8.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `idx_wrong_questions_student_id` | student_id | btree | 学生错题查询 | 查询学生的所有错题记录 | 高效单列查询 |
| `idx_wrong_questions_question_id` | question_id | btree | 题目错题统计 | 查询特定题目的错题情况 | 高效单列查询 |
| `idx_wrong_questions_add_method` | add_method | btree | 添加方式筛选 | 区分自动和手动添加的错题 | 高效方式过滤 |
| `idx_wrong_questions_add_time` | add_time | btree | 时间排序查询 | 按时间排序的错题列表 | 高效时间排序 |
| `uk_wrong_questions_student_question` | student_id,question_id | btree unique | 错题唯一性约束 | 确保学生对同一题目只有一条错题记录 | 高效唯一性约束 |

---

### 3.9 表名: `tbl_drawing_records`

#### 3.8.1 表功能说明
存储勾画记录数据，记录用户在作答环节时对题目进行勾画和标记的记录。该表支持学习过程中的交互行为记录，包括勾画工具类型、颜色、坐标数据等，为学习行为分析提供数据支撑。

#### 3.8.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_drawing_records (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  answer_record_id BIGINT NOT NULL,
  drawing_tool_type BIGINT NOT NULL,
  drawing_color VARCHAR(20) NOT NULL,
  coordinate_data JSONB NOT NULL,
  drawing_metadata JSONB NULL,
  -- 标准字段
  status BOOLEAN NOT NULL DEFAULT true,
  create_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  delete_time TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_drawing_records IS '勾画记录表 - 存储用户作答时的勾画和标记行为';
COMMENT ON COLUMN tbl_drawing_records.id IS '勾画记录主键ID';
COMMENT ON COLUMN tbl_drawing_records.answer_record_id IS '关联的作答记录ID，关联tbl_answer_records.id';
COMMENT ON COLUMN tbl_drawing_records.drawing_tool_type IS '勾画工具类型 (1:画笔, 2:荧光笔, 3:橡皮擦, 4:标记点)';
COMMENT ON COLUMN tbl_drawing_records.drawing_color IS '勾画使用的颜色，十六进制颜色代码';
COMMENT ON COLUMN tbl_drawing_records.coordinate_data IS '勾画的具体坐标和路径数据，JSON格式';
COMMENT ON COLUMN tbl_drawing_records.drawing_metadata IS '勾画元数据，JSON格式';
COMMENT ON COLUMN tbl_drawing_records.status IS '记录状态 (false:禁用, true:启用)';
COMMENT ON COLUMN tbl_drawing_records.create_time IS '记录创建时间';
COMMENT ON COLUMN tbl_drawing_records.update_time IS '记录最后更新时间';
COMMENT ON COLUMN tbl_drawing_records.delete_time IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE INDEX idx_drawing_records_answer_record ON tbl_drawing_records(answer_record_id) WHERE delete_time IS NULL;
-- 支持按作答记录查询勾画记录
CREATE INDEX idx_drawing_records_tool_type ON tbl_drawing_records(drawing_tool_type) WHERE delete_time IS NULL;
-- 支持按工具类型统计查询
CREATE INDEX idx_drawing_records_create_time ON tbl_drawing_records(create_time) WHERE delete_time IS NULL;
-- 支持按创建时间排序查询
```

#### 3.8.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `idx_drawing_records_answer_record` | answer_record_id | btree | 作答记录关联查询 | 查询某次作答的所有勾画记录 | 高效单列查询 |
| `idx_drawing_records_tool_type` | drawing_tool_type | btree | 工具类型统计 | 按工具类型统计使用情况 | 高效类型过滤 |
| `idx_drawing_records_create_time` | create_time | btree | 时间排序查询 | 按时间排序的勾画记录 | 高效时间排序 |

---

### 3.10 掌握度策略相关表集成说明

#### 3.10.1 集成背景
巩固练习系统需要与掌握度策略系统紧密集成，以实现智能推题和学习效果评估。以下表来自掌握度策略设计，在巩固练习系统中作为关联表使用：

#### 3.10.2 引用的掌握度策略表

**表引用清单**：
| 表名 | 来源系统 | 在巩固练习中的作用 | 关联方式 | 数据同步方式 |
|------|---------|------------------|---------|-------------|
| `tbl_mastery_knowledge_point_mastery` | 掌握度策略系统 | 提供知识点掌握度数据，支持智能推题 | 通过knowledge_point_id关联题目 | 实时读取，练习完成后更新 |
| `tbl_mastery_externalized_mastery` | 掌握度策略系统 | 提供课程掌握度展示数据 | 通过course_id关联练习会话 | 练习完成后更新外化掌握度 |
| `tbl_mastery_study_task` | 掌握度策略系统 | 定义学习任务类型和完成阈值 | 巩固练习作为task_type=20的任务 | 配置读取，任务完成状态更新 |

#### 3.9.3 集成关系说明

**掌握度计算流程**：

1. **练习开始时**：查询 `tbl_mastery_knowledge_point_mastery` 获取学生当前掌握度，用于智能推题
2. **答题过程中**：将答题结果写入 `tbl_answer_records`，为掌握度计算提供数据源
3. **练习完成后**：
   - 更新 `tbl_mastery_knowledge_point_mastery` 中相关知识点的掌握度
   - 更新 `tbl_mastery_externalized_mastery` 中课程的外化掌握度
   - 更新 `tbl_mastery_study_task` 中任务完成状态

**数据流向图**：
```mermaid
graph LR
    A[巩固练习开始] --> B[查询知识点掌握度]
    B --> C[智能推题算法]
    C --> D[学生答题]
    D --> E[记录答题结果]
    E --> F[计算掌握度变化]
    F --> G[更新知识点掌握度]
    F --> H[更新外化掌握度]
    F --> I[更新任务完成状态]
    
    B -.-> J[tbl_mastery_knowledge_point_mastery]
    E --> K[tbl_answer_records]
    G --> J
    H --> L[tbl_mastery_externalized_mastery]
    I --> M[tbl_mastery_study_task]
```

#### 3.9.4 API集成接口设计

**掌握度相关接口**：
| 接口名称 | 调用时机 | 目标表 | 操作类型 | 说明 |
|---------|---------|-------|---------|------|
| `getMasteryByKnowledgePoints` | 练习开始前 | `tbl_mastery_knowledge_point_mastery` | SELECT | 获取学生知识点掌握度用于推题 |
| `updateKnowledgePointMastery` | 答题后 | `tbl_mastery_knowledge_point_mastery` | UPDATE | 更新知识点掌握度 |
| `updateExternalizedMastery` | 练习完成后 | `tbl_mastery_externalized_mastery` | UPDATE | 更新课程外化掌握度 |
| `updateStudyTaskProgress` | 练习完成后 | `tbl_mastery_study_task` | UPDATE | 更新学习任务进度 |

---

## 4. 数据库表详细设计 - ClickHouse

### 4.1 表名: `tbl_answer_logs_history`

#### 4.1.1 表功能说明
存储历史答题记录详细日志，用于分析和掌握度计算。该表采用ClickHouse存储，支持高频写入的答题数据记录，为掌握度计算策略提供详细的答题行为数据，支持学习行为分析和个性化推荐算法。

#### 4.1.2 表结构定义 (DDL)
```sql
CREATE TABLE default.tbl_answer_logs_history
(
    -- 时间字段
    log_date Date MATERIALIZED toDate(answer_time) COMMENT '答题日期（用于分区）',
    answer_time DateTime64(3) COMMENT '答题提交时间（毫秒精度）',
    
    -- 业务字段
    session_id UInt64 COMMENT '学习会话ID',
    task_id String COMMENT '任务ID',
    student_id UInt64 COMMENT '学生ID',
    question_id UInt64 COMMENT '题目ID',
    student_answer String COMMENT '学生提交的答案',
    answer_result UInt8 COMMENT '答题结果 (1:正确, 2:错误, 3:部分正确)',
    answer_duration UInt32 COMMENT '答题用时（秒）',
    question_difficulty Float32 COMMENT '题目难度系数',
    correct_combo_count UInt64 COMMENT '答题连对数量',
    
    -- 扩展数据
    answer_metadata Map(String, String) COMMENT '答题元数据（设备信息、IP等）'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date) -- 按月分区
ORDER BY (log_date, student_id, answer_time, task_id, session_id) -- 排序键：日期、学生ID、答题时间、任务ID、会话ID
TTL log_date + INTERVAL 365 DAY DELETE -- 数据保留1年
SETTINGS index_granularity = 8192;

-- ClickHouse 特有说明
-- 排序键 (ORDER BY): (log_date, task_id, student_id, answer_time, session_id)
-- 设计原因: 支持按日期范围查询学生答题记录，按学生聚合分析，按时间序列分析答题行为
-- 分区键 (PARTITION BY): toYYYYMM(log_date)
-- 设计原因: 按月分区便于数据管理和查询优化，支持按时间范围的分区裁剪

-- 跳数索引
ALTER TABLE default.tbl_answer_logs_history ADD INDEX idx_question_id question_id TYPE bloom_filter GRANULARITY 1;
-- 支持快速筛选特定题目的答题记录
ALTER TABLE default.tbl_answer_logs_history ADD INDEX idx_task_id task_id TYPE bloom_filter GRANULARITY 1;
-- 支持快速筛选特定任务的答题记录
ALTER TABLE default.tbl_answer_logs_history ADD INDEX idx_session_id session_id TYPE bloom_filter GRANULARITY 1;
-- 支持快速筛选特定会话的答题记录
```

---

### 4.2 表名: `tbl_study_behavior_logs`

#### 4.2.1 表功能说明
存储学习行为日志，记录学生在练习过程中的各种操作行为，包括进入练习、退出练习、切换题目等操作。该表采用ClickHouse存储，支持高频写入的行为日志记录，为用户行为分析和学习模式研究提供数据支撑。

#### 4.2.2 表结构定义 (DDL)
```sql
CREATE TABLE default.tbl_study_behavior_logs
(
    -- 时间字段
    log_date Date MATERIALIZED toDate(behavior_time) COMMENT '行为日期（用于分区）',
    behavior_time DateTime64(3) COMMENT '行为发生时间（毫秒精度）',
    
    -- 业务字段
    session_id UInt64 COMMENT '学习会话ID',
    task_id String COMMENT '任务ID',
    student_id UInt64 COMMENT '学生ID',
    behavior_type LowCardinality(String) COMMENT '行为类型 (enter_exercise, exit_exercise, next_question, prev_question等)',
    behavior_target LowCardinality(String) COMMENT '行为目标 (session, question, report等)',
    behavior_details String COMMENT '行为详细内容描述',
    before_state String COMMENT '行为前状态',
    after_state String COMMENT '行为后状态',
    
    -- 技术字段
    client_ip IPv6 COMMENT '客户端IP地址',
    user_agent String COMMENT '用户代理信息',
    device_info Map(String, String) COMMENT '设备信息（设备类型、屏幕尺寸等）'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date) -- 按月分区
ORDER BY (log_date, student_id, behavior_time, behavior_type) -- 排序键：日期、学生ID、行为时间、行为类型
TTL log_date + INTERVAL 180 DAY DELETE -- 数据保留6个月
SETTINGS index_granularity = 8192;

-- ClickHouse 特有说明
-- 排序键 (ORDER BY): (log_date, student_id, behavior_time, behavior_type)
-- 设计原因: 支持按日期范围查询学生行为记录，按学生聚合分析行为模式，按行为类型统计分析
-- 分区键 (PARTITION BY): toYYYYMM(log_date)
-- 设计原因: 按月分区便于数据管理，行为日志数据量大但查询通常集中在近期数据

-- 跳数索引
ALTER TABLE default.tbl_study_behavior_logs ADD INDEX idx_behavior_type behavior_type TYPE set(0) GRANULARITY 1;
-- 支持快速筛选特定行为类型
ALTER TABLE default.tbl_study_behavior_logs ADD INDEX idx_session_id session_id TYPE bloom_filter GRANULARITY 1;
-- 支持快速定位特定会话的行为记录
```

---

## 5. 表间关系与 ER 图

### 5.1 表间关系描述

**表间关系标准格式**：
| 主表           | 关系类型                       | 从表               | 外键字段 (从表)                           | 关联描述                                               | 一致性保障方式 |
| -------------- | ------------------------------ | ------------------ | ----------------------------------------- | ------------------------------------------------------ | ------------- |
| `tbl_study_sessions`   | 一对多 (1:N)                   | `tbl_answer_records`     | `session_id`                                 | 一个练习会话包含多个作答记录                                 | 应用层校验 |
| `tbl_study_sessions` | 一对一 (1:1) | `tbl_study_reports` | `session_id` | 一个练习会话生成一个学习报告 | 应用层校验 |
| `tbl_study_sessions` | 一对多 (1:N) | `tbl_study_sessions` | `trigger_session_id` | 一个原始练习会话可能触发多个再练会话（自关联） | 应用层校验 |
| `tbl_answer_records` | 一对多 (1:N) | `tbl_drawing_records` | `answer_record_id` | 一个作答记录可包含多个勾画记录 | 应用层校验 |
| `tbl_answer_records` | 一对一 (1:1) | `tbl_wrong_questions` | `answer_record_id` | 错误作答记录可生成错题记录 | 应用层校验 |
| `tbl_mastery_knowledge_point_mastery` | 多对多 (M:N) | `tbl_answer_records` | `question_id` (通过知识点关联) | 答题记录影响知识点掌握度计算 | 应用层校验 |
| `tbl_mastery_externalized_mastery` | 一对多 (1:N) | `tbl_study_sessions` | `course_id` | 课程外化掌握度关联练习会话 | 应用层校验 |
| `tbl_mastery_study_task` | 一对多 (1:N) | `tbl_study_sessions` | `course_id` (task_type=20) | 学习任务定义巩固练习配置 | 应用层校验 |

### 5.2 ER 图 (使用 Mermaid 语法)
注意：本ER图中的外键关系（FK）表示的是表
间的逻辑关联和参照完整性期望，实际建表语句中不使用数据库层面的物理外键约束，数据一致性由应用层保障。

```mermaid
erDiagram
    %% PostgreSQL 核心业务表
    EXERCISE_SESSIONS {
        BIGSERIAL id PK
        BIGINT student_id FK
        BIGINT course_id FK
        BIGINT session_type
        BIGINT session_status
        BIGINT current_question_index
        BIGINT total_questions
        TIMESTAMPTZ start_time
        TIMESTAMPTZ end_time
        JSONB session_config
        BIGINT trigger_session_id FK
        BIGINT weak_points_count
        BIGINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    ANSWER_RECORDS {
        BIGSERIAL id PK
        BIGINT session_id FK
        BIGINT question_id FK
        BIGINT question_index
        TEXT student_answer
        BIGINT answer_result
        BIGINT answer_duration
        NUMERIC question_difficulty
        BOOLEAN is_continuous_correct
        TIMESTAMPTZ answer_time
        JSONB answer_metadata
        BIGINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    LEARNING_REPORTS {
        BIGSERIAL id PK
        BIGINT session_id FK
        BIGINT student_id FK
        NUMERIC mastery_change
        BIGINT study_duration
        BIGINT question_count
        NUMERIC accuracy_rate
        TEXT ip_feedback
        BOOLEAN recommend_retry
        JSONB report_data
        TIMESTAMPTZ generated_time
        BIGINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    WRONG_QUESTIONS {
        BIGSERIAL id PK
        BIGINT student_id FK
        BIGINT question_id FK
        BIGINT answer_record_id FK
        BIGINT add_method
        TEXT error_reason_tags
        TIMESTAMPTZ add_time
        JSONB wrong_metadata
        BIGINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    DRAWING_RECORDS {
        BIGSERIAL id PK
        BIGINT answer_record_id FK
        BIGINT drawing_tool_type
        VARCHAR drawing_color
        JSONB coordinate_data
        JSONB drawing_metadata
        TIMESTAMPTZ create_time
        BIGINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    %% ClickHouse 分析表
    ANSWER_LOGS_HISTORY {
        Date log_date
        DateTime64 answer_time
        UInt64 session_id
        UInt64 student_id
        UInt64 question_id
        UInt64 course_id
        String student_answer
        UInt8 answer_result
        UInt32 answer_duration
        Float32 question_difficulty
    }
    
    SESSION_BEHAVIOR_LOGS {
        Date log_date
        DateTime64 behavior_time
        UInt64 session_id
        UInt64 student_id
        String behavior_type
        String behavior_target
        String behavior_details
    }

    %% 掌握度策略相关表（引用）
    MASTERY_KNOWLEDGE_POINT {
        BIGSERIAL id PK
        VARCHAR user_id FK
        VARCHAR knowledge_point_id FK
        NUMERIC mastery_value
        BIGINT answer_count
        BOOLEAN is_weak_point
        TIMESTAMPTZ last_updated_at
    }
    
    MASTERY_EXTERNALIZED {
        BIGSERIAL id PK
        VARCHAR user_id FK
        VARCHAR course_id FK
        NUMERIC externalized_mastery_value
        BIGINT cumulative_answer_count
        TIMESTAMPTZ last_updated_at
    }
    
    MASTERY_LEARNING_TASK {
        BIGSERIAL id PK
        VARCHAR task_name
        BIGINT task_type
        VARCHAR course_id FK
        BIGINT completion_threshold
    }

    %% 关系定义
    EXERCISE_SESSIONS ||--o{ ANSWER_RECORDS : "contains"
    EXERCISE_SESSIONS ||--|| LEARNING_REPORTS : "generates"
    EXERCISE_SESSIONS ||--o{ EXERCISE_SESSIONS : "triggers_retry"
    ANSWER_RECORDS ||--o{ DRAWING_RECORDS : "includes"
    ANSWER_RECORDS ||--|| WRONG_QUESTIONS : "creates"
    EXERCISE_SESSIONS ||--o{ ANSWER_LOGS_HISTORY : "logs_to"
    EXERCISE_SESSIONS ||--o{ SESSION_BEHAVIOR_LOGS : "logs_to"
    
    %% 掌握度策略集成关系
    MASTERY_KNOWLEDGE_POINT ||--o{ ANSWER_RECORDS : "influences_mastery"
    MASTERY_EXTERNALIZED ||--o{ EXERCISE_SESSIONS : "tracks_course_mastery"
    MASTERY_LEARNING_TASK ||--o{ EXERCISE_SESSIONS : "defines_task_config"
```

## 6. 核心用户场景/用户故事与数据操作分析

### 6.1 场景/故事: 学生首次进入学习任务

#### 6.1.1 场景描述
学生通过客户端进入各种学习任务（AI课、巩固练习、作业等），系统验证用户身份后，获取任务信息并初始化学习会话。学生开始第一道题目的作答，系统记录学习开始状态和初始进度。

#### 6.1.2 涉及的主要数据表
- `tbl_study_sessions` - 创建学习会话记录
- `tbl_study_behavior_logs` - 记录进入学习的行为日志

#### 6.1.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 创建练习会话 | Create | tbl_study_sessions | - | 主键自增 | 应用层校验 | 低风险 |
| 记录进入行为 | Create | tbl_study_behavior_logs | - | 排序键优化 | 异步写入 | 低风险 |

**步骤 1: 学生请求进入学习任务**
- **创建 (Create)**:
  - 在 `tbl_study_sessions` 表中插入一条新的学习会话记录
  - 涉及字段：`student_id`, `task_id`, `study_type`, `session_status`, `start_time`, `session_config`
  - 数据来源/前置条件：用户认证信息、任务ID和学习类型
  - 成功标准/后置影响：返回新创建的会话ID，状态设为"学习中"

**步骤 2: 记录用户行为日志**
- **创建 (Create)**:
  - 在 `tbl_study_behavior_logs` 表中插入行为记录
  - 涉及字段：`session_id`, `student_id`, `behavior_type`, `behavior_time`
  - 数据来源/前置条件：会话信息和行为类型
  - 成功标准/后置影响：异步记录用户进入学习的行为

#### 6.1.4 性能考量与索引支持
- 学习会话创建通过主键自增保证高效插入
- 行为日志通过ClickHouse的高性能写入能力支持高并发场景
- 学生ID索引支持快速查询用户的学习历史
- 学习类型索引支持按任务类型快速筛选

---

### 6.2 场景/故事: 学生在各种学习任务中作答题目并实时更新掌握度

#### 6.2.1 场景描述
学生在各种学习任务（AI课、巩固练习、作业等）中提交题目答案，系统判断答案正确性，记录作答详情和推题信息，并实时更新掌握度。如果学生进行勾画操作，同时记录勾画数据。系统根据作答结果决定是否推荐下一题，并记录知识点掌握度变化情况。

#### 6.2.2 涉及的主要数据表
- `tbl_answer_records` - 记录作答详情和推题信息
- `tbl_drawing_records` - 记录勾画数据（如有）
- `tbl_wrong_questions` - 记录错题（如果作答错误）
- `tbl_session_knowledge_points` - 记录知识点掌握度变化
- `tbl_answer_logs_history` - 异步记录答题日志
- `tbl_mastery_knowledge_point_mastery` - 更新知识点掌握度（引用掌握度策略）
- `tbl_mastery_externalized_mastery` - 更新外化掌握度（引用掌握度策略）

#### 6.2.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 记录作答结果 | Create | tbl_answer_records | - | 主键自增 | 应用层校验 | 低风险 |
| 记录勾画数据 | Create | tbl_drawing_records | - | 主键自增 | 应用层校验 | 低风险 |
| 记录错题信息 | Create | tbl_wrong_questions | student_id,question_id | 唯一索引 | 应用层校验 | 低风险 |
| 更新会话进度 | Update | tbl_study_sessions | session_id=xxx | 主键 | 单行更新 | 低风险 |
| 查询知识点掌握度 | Read | tbl_mastery_knowledge_point_mastery | user_id,knowledge_point_id | 唯一索引 | 应用层校验 | 低风险 |
| 更新知识点掌握度 | Update | tbl_mastery_knowledge_point_mastery | user_id,knowledge_point_id | 唯一索引 | 应用层校验 | 中风险 |
| 更新外化掌握度 | Update | tbl_mastery_externalized_mastery | user_id,course_id | 唯一索引 | 应用层校验 | 中风险 |
| 记录掌握度变化 | Create | tbl_session_knowledge_points | session_id,knowledge_point_id | 唯一索引 | 应用层校验 | 低风险 |
| 异步记录日志 | Create | tbl_answer_logs_history | - | 排序键优化 | 异步写入 | 低风险 |

**步骤 1: 学生提交答案**
- **创建 (Create)**:
  - 在 `tbl_answer_records` 表中插入作答记录
  - 涉及字段：`session_id`, `task_id`, `question_id`, `student_answer`, `answer_result`, `answer_duration`, `question_source`
  - 数据来源/前置条件：会话ID、任务ID、题目ID、学生答案、系统判题结果、题目来源信息
  - 成功标准/后置影响：记录完整的作答信息和推题信息，用于掌握度计算

**步骤 2: 记录勾画数据（如有）**
- **创建 (Create)**:
  - 在 `tbl_drawing_records` 表中插入勾画记录
  - 涉及字段：`answer_record_id`, `drawing_tool_type`, `coordinate_data`
  - 数据来源/前置条件：作答记录ID和勾画操作数据
  - 成功标准/后置影响：保存学生的勾画行为数据

**步骤 3: 处理错题记录（如果作答错误）**
- **创建 (Create)**:
  - 在 `tbl_wrong_questions` 表中插入错题记录
  - 涉及字段：`student_id`, `question_id`, `answer_record_id`, `add_method`
  - 数据来源/前置条件：学生ID、题目ID和作答记录ID
  - 成功标准/后置影响：自动添加错题到错题本

**步骤 4: 更新练习进度**
- **更新 (Update)**:
  - 更新 `tbl_study_sessions` 表中的当前题目序号
  - 更新条件：`id = session_id`
  - 更新字段：`current_question_index`, `updated_at`
  - 影响：推进练习进度到下一题

**步骤 5: 查询当前知识点掌握度**
- **读取 (Read)**:
  - 从 `tbl_mastery_knowledge_point_mastery` 表中查询相关知识点掌握度
  - 查询条件：`user_id = {学生ID} AND knowledge_point_id IN ({题目关联的知识点}) AND deleted_at IS NULL`
  - 涉及字段：`mastery_value`, `answer_count`, `is_weak_point`
  - 目的：获取当前掌握度用于计算更新值

**步骤 6: 更新知识点掌握度**
- **更新 (Update)**:
  - 更新 `tbl_mastery_knowledge_point_mastery` 表中的掌握度数据
  - 更新条件：`user_id = {学生ID} AND knowledge_point_id = {知识点ID}`
  - 更新字段：`mastery_value`, `answer_count`, `is_weak_point`, `last_updated_at`
  - 影响：根据答题结果实时调整知识点掌握度

**步骤 7: 更新外化掌握度（如达到阈值）**
- **更新 (Update)**:
  - 更新 `tbl_mastery_externalized_mastery` 表中的课程掌握度
  - 更新条件：`user_id = {学生ID} AND course_id = {课程ID}`
  - 更新字段：`externalized_mastery_value`, `cumulative_answer_count`, `last_updated_at`
  - 影响：更新向学生展示的课程掌握程度

**步骤 8: 记录知识点掌握度变化**
- **创建/更新 (Create/Update)**:
  - 在 `tbl_session_knowledge_points` 表中记录知识点掌握度变化
  - 涉及字段：`session_id`, `knowledge_point_id`, `mastery_before`, `mastery_after`, `mastery_change`
  - 数据来源/前置条件：掌握度服务返回的知识点掌握度变化数据
  - 成功标准/后置影响：记录本次学习的掌握度提升情况，用于学习效果统计

**步骤 9: 异步记录答题日志**
- **创建 (Create)**:
  - 在 `tbl_answer_logs_history` 表中插入答题日志
  - 涉及字段：`session_id`, `student_id`, `question_id`, `answer_result`, `answer_time`
  - 数据来源/前置条件：作答记录的详细信息
  - 成功标准/后置影响：为后续数据分析提供原始数据

#### 6.2.4 性能考量与索引支持
- 作答记录插入通过主键自增保证高效写入
- 错题记录通过唯一索引避免重复插入
- 会话进度更新通过主键索引实现高效单行更新
- 掌握度查询和更新通过用户-知识点唯一索引保证高效操作
- 外化掌握度更新通过用户-课程唯一索引实现快速更新
- ClickHouse日志表通过排序键优化支持高频写入

---

### 6.3 场景/故事: 练习完成后生成学习报告

#### 6.3.1 场景描述
学生完成所有题目后，系统自动生成学习报告，汇总本次练习的表现数据，包括掌握度变化、正确率、学习时长等。系统根据掌握度情况判断是否推荐再练一次，并展示个性化的IP反馈。

#### 6.3.2 涉及的主要数据表
- `tbl_study_sessions` - 更新会话完成状态，创建再练会话（如需要）
- `tbl_answer_records` - 查询作答记录进行统计
- `tbl_study_reports` - 生成学习报告

#### 6.3.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 查询作答记录 | Read | tbl_answer_records | session_id=xxx | session_id索引 | 读取一致性 | 低风险 |
| 更新会话状态 | Update | tbl_study_sessions | id=session_id | 主键 | 单行更新 | 低风险 |
| 生成学习报告 | Create | tbl_study_reports | - | 主键自增 | 应用层校验 | 低风险 |
| 创建再练会话 | Create | tbl_study_sessions | - | 主键自增 | 应用层校验 | 低风险 |

**步骤 1: 查询和统计作答记录**
- **读取 (Read)**:
  - 从 `tbl_answer_records` 表中查询本次练习的所有作答记录
  - 查询条件：`session_id = {会话ID} AND deleted_at IS NULL`
  - 涉及字段：`answer_result`, `answer_duration`, `question_difficulty`
  - 目的：统计正确率、总用时、题目数量等指标

**步骤 2: 更新练习会话状态**
- **更新 (Update)**:
  - 更新 `tbl_study_sessions` 表中的会话状态
  - 更新条件：`id = {会话ID}`
  - 更新字段：`session_status = 2`, `end_time = CURRENT_TIMESTAMP`
  - 影响：标记练习会话为已完成状态

**步骤 3: 生成学习报告**
- **创建 (Create)**:
  - 在 `tbl_study_reports` 表中插入学习报告记录
  - 涉及字段：`session_id`, `student_id`, `mastery_change`, `accuracy_rate`, `study_duration`
  - 数据来源/前置条件：统计的作答数据和掌握度计算结果
  - 成功标准/后置影响：生成完整的学习成果报告

**步骤 4: 判断并创建再练会话（如需要）**
- **创建 (Create)**:
  - 在 `tbl_study_sessions` 表中插入再练会话记录
  - 涉及字段：`student_id`, `course_id`, `session_type=2`, `trigger_session_id`, `weak_points_count`
  - 数据来源/前置条件：掌握度分析结果和薄弱知识点统计
  - 成功标准/后置影响：为学生提供个性化的再练机会

#### 6.3.4 性能考量与索引支持
- 作答记录查询通过session_id索引实现高效聚合统计
- 会话状态更新通过主键索引保证快速更新
- 学习报告生成通过唯一索引确保每个会话只有一个报告
- 再练会话创建通过trigger_session_id索引支持关联查询

---

### 6.4 场景/故事: 学生查看错题本和学习历史

#### 6.4.1 场景描述
学生通过客户端查看自己的错题本，浏览历史练习记录和学习报告。系统需要快速检索学生的错题记录、练习历史和学习成果，支持按时间、课程等维度进行筛选和排序。

#### 6.4.2 涉及的主要数据表
- `tbl_wrong_questions` - 查询错题记录
- `tbl_study_sessions` - 查询练习历史
- `tbl_study_reports` - 查询学习报告
- `tbl_answer_records` - 查询详细作答记录

#### 6.4.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 查询错题记录 | Read | tbl_wrong_questions | student_id=xxx | student_id索引 | 读取一致性 | 低风险 |
| 查询练习历史 | Read | tbl_study_sessions | student_id=xxx | student_id索引 | 读取一致性 | 低风险 |
| 查询学习报告 | Read | tbl_study_reports | student_id=xxx | student_id索引 | 读取一致性 | 低风险 |
| 查询作答详情 | Read | tbl_answer_records | session_id=xxx | session_id索引 | 读取一致性 | 低风险 |

**步骤 1: 查询学生错题记录**
- **读取 (Read)**:
  - 从 `tbl_wrong_questions` 表中查询学生的错题记录
  - 查询条件：`student_id = {学生ID} AND deleted_at IS NULL`
  - 涉及字段：`question_id`, `add_time`, `error_reason_tags`, `add_method`
  - 目的：展示学生的错题本内容，支持错题复习

**步骤 2: 查询练习历史记录**
- **读取 (Read)**:
  - 从 `tbl_study_sessions` 表中查询学生的练习历史
  - 查询条件：`student_id = {学生ID} AND deleted_at IS NULL ORDER BY start_time DESC`
  - 涉及字段：`course_id`, `session_status`, `start_time`, `end_time`
  - 目的：展示学生的练习历史列表，支持按时间排序

**步骤 3: 查询学习报告列表**
- **读取 (Read)**:
  - 从 `tbl_study_reports` 表中查询学生的学习报告
  - 查询条件：`student_id = {学生ID} AND deleted_at IS NULL ORDER BY generated_time DESC`
  - 涉及字段：`accuracy_rate`, `mastery_change`, `study_duration`, `generated_time`
  - 目的：展示学生的学习成果趋势，支持学习效果分析

**步骤 4: 查询特定练习的作答详情**
- **读取 (Read)**:
  - 从 `tbl_answer_records` 表中查询特定练习的作答详情
  - 查询条件：`session_id = {会话ID} AND deleted_at IS NULL ORDER BY question_index`
  - 涉及字段：`question_id`, `answer_result`, `answer_duration`, `answer_time`
  - 目的：展示练习过程的详细作答情况

#### 6.4.4 性能考量与索引支持
- 错题查询通过student_id索引实现高效检索
- 练习历史查询通过student_id索引和时间排序优化
- 学习报告查询通过student_id索引支持快速获取
- 作答详情查询通过session_id索引和题目序号排序优化

---

### 6.5 场景/故事: 教师查询任务统计报告

#### 6.5.1 场景描述
教师通过管理端查询指定学科指定时间段内的全部任务汇总报告，获取任务完成进度、正确率和需要关注的题目数等统计信息。教师也可以查询具体任务的详细报告数据，包括每个学生的完成进度、正确率、答题难度、答题数、错题数、答题用时等详细信息。

#### 6.5.2 涉及的主要数据表
- `tbl_task_reports` - 查询任务维度统计数据
- `tbl_study_reports` - 查询具体任务的学生详细数据
- `tbl_study_sessions` - 查询任务完成情况
- `tbl_answer_records` - 查询作答详情统计

#### 6.5.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 查询任务汇总报告 | Read | tbl_task_reports | subject_id,stat_date | 复合索引 | 读取一致性 | 低风险 |
| 查询具体任务详情 | Read | tbl_study_reports | task_id | 外键索引 | 读取一致性 | 低风险 |
| 统计任务完成情况 | Read | tbl_study_sessions | task_id,session_status | 复合索引 | 读取一致性 | 中风险 |
| 统计作答详情 | Read | tbl_answer_records | task_id | 外键索引 | 读取一致性 | 中风险 |

**步骤 1: 查询指定学科时间段的任务汇总报告**
- **读取 (Read)**:
  - 从 `tbl_task_reports` 表中查询任务统计数据
  - 查询条件：`subject_id = {学科ID} AND stat_date BETWEEN {开始日期} AND {结束日期} ORDER BY stat_date DESC, completion_rate DESC`
  - 涉及字段：`task_id`, `completion_rate`, `average_accuracy`, `attention_questions_count`, `total_students`, `completed_students`
  - 数据来源/前置条件：学科ID和时间范围参数
  - 成功标准/后置影响：返回分页的任务汇总列表，支持按完成率排序

**步骤 2: 查询具体任务的学生详细数据**
- **读取 (Read)**:
  - 从 `tbl_study_reports` 表中查询任务的学生报告数据
  - 查询条件：`task_id = {任务ID} AND deleted_at IS NULL ORDER BY accuracy_rate DESC`
  - 涉及字段：`student_id`, `accuracy_rate`, `study_duration`, `question_count`, `mastery_change`
  - 数据来源/前置条件：任务ID参数
  - 成功标准/后置影响：返回该任务所有学生的详细学习数据

**步骤 3: 统计任务完成情况（实时计算）**
- **读取 (Read)**:
  - 从 `tbl_study_sessions` 表中统计任务完成情况
  - 查询条件：`task_id = {任务ID} GROUP BY session_status`
  - 涉及字段：`session_status`, `COUNT(*) as session_count`
  - 数据来源/前置条件：任务ID参数
  - 成功标准/后置影响：返回任务的完成状态统计

**步骤 4: 统计作答详情（实时计算）**
- **读取 (Read)**:
  - 从 `tbl_answer_records` 表中统计作答详情
  - 查询条件：`task_id = {任务ID} GROUP BY student_id`
  - 涉及字段：`student_id`, `COUNT(*) as answer_count`, `SUM(CASE WHEN answer_result=1 THEN 1 ELSE 0 END) as correct_count`, `AVG(answer_duration) as avg_duration`
  - 数据来源/前置条件：任务ID参数
  - 成功标准/后置影响：返回每个学生的作答统计数据

#### 6.5.4 性能考量与索引支持
- 任务统计查询通过subject_id和stat_date复合索引实现高效检索
- 学习报告查询通过task_id索引支持快速获取
- 会话统计查询通过task_id索引和状态分组优化
- 作答统计查询通过task_id索引和学生分组优化

---

## 7. 设计检查清单 (Design Checklist)

### 7.1 需求与架构对齐性检查

**标准化检查表格式**：每行必须对应一个具体的需求点或用户故事，确保可追溯性

| 需求点/用户故事 (来自PRD)        | 对应架构模块 (来自TD)           | 关联核心数据表 (本次设计) | ①需求覆盖完整性 | ②架构符合性 | ③一致性与无冲突 | ④业务场景满足度 | ⑤可追溯性与依赖清晰度 | 备注与待办事项                                                                                                                                  |
| -------------------------------- | ----------------------------- | --------------------- | --------------- | ----------- | --------------- | ------------- | ------------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| 学生进入巩固练习并开始作答           | 巩固练习服务+练习会话管理模块 | `tbl_study_sessions`, `tbl_answer_records` | ✅               | ✅           | ✅               | ✅             | ✅                   | 场景6.1已覆盖。`tbl_study_sessions` 管理会话状态，`tbl_answer_records` 记录作答详情。设计符合架构中的会话管理和作答处理要求。                   |
| 实时掌握度计算和更新               | 掌握度计算服务+推题策略服务       | `tbl_answer_records`, `tbl_mastery_knowledge_point_mastery` | ✅               | ✅           | ✅               | ✅             | ✅                   | 场景6.2已覆盖。集成掌握度策略系统，通过引用表实现知识点掌握度实时计算和更新。                                   |
| 智能推题基于掌握度策略 | 推题策略服务+掌握度计算服务 | `tbl_mastery_knowledge_point_mastery`, `tbl_mastery_study_task` | ✅ | ✅ | ✅ | ✅ | ✅ | 通过引用掌握度策略表，支持基于学生掌握度的智能推题算法。 |
| 外化掌握度展示和更新 | 掌握度计算服务+学习报告服务 | `tbl_mastery_externalized_mastery`, `tbl_study_reports` | ✅ | ✅ | ✅ | ✅ | ✅ | 练习完成后更新外化掌握度，为学生提供直观的学习成果展示。 |
| 学习报告生成和展示 | 学习报告服务+数据统计模块 | `tbl_study_reports`, `tbl_study_sessions` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景6.3已覆盖。`tbl_study_reports` 存储报告数据，支持掌握度变化展示和IP反馈。 |
| 再练一次推荐机制 | 推题策略服务+掌握度计算服务 | `tbl_study_sessions`, `tbl_study_reports` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景6.3已覆盖。通过 `tbl_study_sessions` 的session_type字段统一管理再练会话，基于掌握度判断推荐。 |
| 错题记录和管理 | 练习数据管理服务+错题管理模块 | `tbl_wrong_questions`, `tbl_answer_records` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景6.2和6.4已覆盖。支持自动和手动添加错题，错题本查询功能完整。 |
| 勾画记录和交互行为跟踪 | 练习数据管理服务+行为记录模块 | `tbl_drawing_records`, `tbl_study_behavior_logs` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景6.2已覆盖。`tbl_drawing_records` 记录勾画数据，`tbl_study_behavior_logs` 记录行为日志。 |
| 中途退出保存和继续练习 | 巩固练习服务+会话状态管理 | `tbl_study_sessions`, `tbl_study_behavior_logs` | ✅ | ✅ | ✅ | ✅ | ✅ | 通过会话状态管理支持中途退出保存，行为日志记录退出和继续操作。 |
| 学习历史查询和统计分析 | 学习报告服务+数据分析模块 | `tbl_study_sessions`, `tbl_study_reports`, `tbl_answer_logs_history` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景6.4已覆盖。支持练习历史、学习报告查询，ClickHouse表支持深度分析。 |

### 7.2 数据库设计质量检查

**(针对本次设计的每个核心表或整体设计方案进行评估)**

| 检查维度                               | 评估结果 (✅/❌/N/A) | 具体说明与改进建议 (如果存在问题)                                                                                                |
| -------------------------------------- | ------------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| **⑥ 设计规范符合性** | ✅                   | 严格遵循PostgreSQL和ClickHouse设计规范，表名使用tbl_前缀，字段命名规范，包含完整中文注释                                                                       |
|   - 命名规范                           | ✅                   | 表名使用tbl_前缀，字段名使用下划线命名法，符合规范要求                                                                                                                                |
|   - 标准字段                           | ✅                   | 所有PostgreSQL表包含标准字段：status, created_at, updated_at, deleted_at, created_by, updated_by                                                                                                                                |
|   - 数据类型选择                       | ✅                   | 合理选择数据类型：BIGSERIAL主键、TIMESTAMPTZ时间、JSONB灵活数据、NUMERIC精确数值                                                                                                                                |
|   - 注释质量                           | ✅                   | 所有表和字段都有详细的中文注释，说明用途和取值范围                                                                                                                                |
|   - 通用设计原则与反模式规避             | ✅                   | 避免物理外键约束，使用逻辑外键；合理设计索引；避免过度规范化                                                                                                                                |
| **⑦ 性能与可伸缩性考量** | ✅                   | 索引设计合理，分区策略适当，表引擎选择最优，支持高并发和大数据量                                                            |
|   - 索引策略 (主键、排序键、跳数索引等)  | ✅                   | PostgreSQL表设计了合理的B-tree索引，ClickHouse表使用排序键和跳数索引优化查询                                                                                                                                |
|   - 分区/分片策略 (如果适用)             | ✅                   | ClickHouse表按月分区，支持TTL自动清理，便于数据管理和查询优化                                                                                                                                |
|   - 表引擎选择 (尤其 ClickHouse)        | ✅                   | ClickHouse使用MergeTree引擎，适合时序数据的高频写入和分析查询                                                                                                                                |
|   - 数据压缩策略 (尤其 ClickHouse)      | ✅                   | ClickHouse使用LowCardinality优化枚举字段，Map类型优化元数据存储                                                                                                                                |
| **⑧ 可维护性与可扩展性** | ✅                   | 表结构清晰易懂，JSONB字段支持灵活扩展，软删除机制便于数据恢复                                                                       |
| **⑨ 数据完整性保障** | ✅                   | 使用NOT NULL、UNIQUE约束保障数据有效性，应用层保障逻辑外键完整性                                                                       |
| **⑩ (可选) 安全性考量** | ✅                   | 学生ID等敏感标识通过外部服务管理，答题内容等敏感数据可根据需要进行脱敏处理                                                                       |
| **其他特定于数据库的检查点** (例如)      |                     |                                                                                                                                |
|   - PostgreSQL: `TIMESTAMPTZ` 使用        | ✅                   | 所有时间字段使用TIMESTAMPTZ类型，支持时区处理                                                                                                                                |
|   - PostgreSQL: `JSONB` 使用合理性        | ✅                   | 合理使用JSONB存储配置信息、元数据等灵活数据，避免过度使用                                                                                                                                |
|   - ClickHouse: `LowCardinality` 使用     | ✅                   | 在行为类型等枚举字段使用LowCardinality优化存储和查询                                                                                                                                |
|   - ClickHouse: 物化视图设计合理性        | N/A                   | 当前设计未涉及物化视图，后续可根据分析
需求增加物化视图                                                                                                                                |

## 8. 参考资料与附录

- 产品需求文档 (PRD): [be-s3-1-ai-de-学生端-巩固练习-claude-sonnet-4.md]
- 技术架构方案 (TD): [be-s2-1-ai-td-学生端-巩固练习-claude-sonnet-4.md]
- PostgreSQL 设计规范与约束文件: [rules-postgresql-code-v2.md]
- ClickHouse 设计规范与约束文件: [rules-clickhouse-code-v2.md]
- 掌握度策略数据库设计参考: [be-s3-4-ai-dd-掌握度策略-V1.0-claude-3.6.md]
- AI课中数据库设计参考: [be-s3-4-ai-dd-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md]

---

**数据库设计完成，已按模板要求完成质量检查，等待您的命令，指挥官**
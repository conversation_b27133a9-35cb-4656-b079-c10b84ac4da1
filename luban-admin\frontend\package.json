{"name": "ai-doc-workflow-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:simple": "node server-simple.js", "dev:next": "next dev -H 0.0.0.0 -p 80", "build": "next build", "start": "next start", "lint": "next lint", "clean": "rimraf .next && rimraf node_modules/.cache", "clean:dev": "npm run clean && npm run dev", "add-base-ui": "npx shadcn-ui@latest add button && npx shadcn-ui@latest add input && npx shadcn-ui@latest add textarea && npx shadcn-ui@latest add progress && npx shadcn-ui@latest add alert"}, "dependencies": {"@chakra-ui/icons": "^2.1.1", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@hookform/resolvers": "^3.3.4", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.17.19", "axios": "^1.9.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "cmdk": "^0.2.0", "framer-motion": "^11.2.10", "lucide-react": "^0.307.0", "markdown-it": "^14.0.0", "next": "^14.1.0", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.49.3", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-syntax-highlighter": "^15.5.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@types/markdown-it": "^13.0.7", "@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "eslint": "^8.56.0", "eslint-config-next": "14.0.4", "postcss": "^8.4.33", "rimraf": "^5.0.5", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}
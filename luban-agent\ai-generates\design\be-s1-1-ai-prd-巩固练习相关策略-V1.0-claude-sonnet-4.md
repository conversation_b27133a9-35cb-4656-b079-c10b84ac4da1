# 产品需求文档 - 巩固练习相关策略 V1.0

## 1. 需求背景与目标

### 1.1 需求目标
- 通过巩固练习策略的实现，可以为不同能力水平的学生提供匹配的练习题，避免"题太难打击信心"或"题太简单浪费时间"的问题
- 该需求上线后，学习效率预计可以提升30%，知识掌握度预计可以达到目标掌握度的80%以上
- 为后续算法推荐提供结构化数据积累，便于未来迭代上线IRT、知识追踪等自动推荐机制
- 提升整体产品智能感与完成率，通过系统"主动推题、智能响应"增强学生对产品智能化的感知

## 2. 功能范围

### 2.1 核心功能
- **题目推荐策略**: 基于预期掌握度增量的智能推题算法，满足个性化学习场景，解决题目难度不匹配的痛点
  - 优先级：高
  - 依赖关系：依赖掌握度计算服务
- **预估题目数量计算**: 根据知识点难度、考频和用户掌握度动态计算练习题量，满足适量练习场景，解决练习量过多或过少的痛点
  - 优先级：高
  - 依赖关系：依赖题目推荐策略
- **再练一次功能**: 针对错题的二次练习机制，满足巩固薄弱知识点场景，解决一次练习无法充分掌握的痛点
  - 优先级：中
  - 依赖关系：依赖于题目推荐策略
- **熔断策略**: 基于用户答题表现的智能终止机制，满足避免挫败感场景，解决连续错题导致学习积极性下降的痛点
  - 优先级：中
  - 依赖关系：依赖专注度评分系统

### 2.2 辅助功能
- **专注度评分系统**: 基于作答时长和正确率的用户专注度判断，为熔断策略提供支持
- **题组管理**: 按典型题型组织题目的管理机制，为题目推荐策略提供支持
- **难度等级映射**: 将题目难度等级L1-L5映射为数值系数0.2-1.0，为推荐算法提供支持

### 2.3 非本期功能
- **基于IRT模型的推荐算法**: 需要大量用户答题数据支撑，建议迭代2再考虑
- **知识追踪算法**: 需要完善的知识图谱和学习路径数据，建议迭代3再考虑

## 3. 计算规则与公式

### 3.1 基础答题量计算
- **公式**: 基础答题量 = 10 × 难度系数 × 考频系数（四舍五入取整，范围[8,14]）
  - 参数说明：
    - 难度系数：知识点的难度系数
    - 考频系数：知识点在考试中的出现频率系数
  - 规则：
    - 取值范围：[8, 14]

### 3.2 动态调整系数计算
- **公式**: A = 1 + α × max(0, θ_tar - θ_cur) / θ_tar
  - 参数说明：
    - θ_tar：该知识点对应的目标掌握度
    - θ_cur：学生对该知识点的当前掌握度
    - α：经验调节系数，默认取0.6
  - 规则：
    - 取值范围：A ≥ 1

### 3.3 预期掌握度增量计算
- **公式**: P(答对) = sigmoid(4 × (θ - β))
  - 参数说明：
    - θ：学生的掌握度
    - β：题目难度系数
    - sigmoid函数：S(x) = 1/(1 + e^(-x))
  - 规则：
    - 取值范围：[0, 1]

- **公式**: 预期掌握度增量 = P(答对) × 答对掌握度增量 + P(答错) × 答错掌握度增量
  - 参数说明：
    - 答对掌握度增量 = 0.2 × (β - θ)
    - 答错掌握度增量 = -0.1 × (β - θ) × 0.5
  - 规则：
    - 取值范围：可为正值或负值

### 3.4 题目难度系数映射
- **规则**: 难度等级到系数的映射关系
  - L1 → 0.2
  - L2 → 0.4
  - L3 → 0.6
  - L4 → 0.8
  - L5 → 1.0

## 4. 用户场景与故事

### 场景1：学生首次进入巩固练习
作为一个刚完成AI课学习的学生，我希望能够获得适合我能力水平的练习题，这样我就可以有效巩固刚学到的知识点。目前的痛点是不知道自己应该做什么难度的题目，如果能够系统自动推荐合适的题目，对我的学习会有很大帮助。

**关键步骤：**
1. 学生完成AI课学习，进入巩固练习环节
2. 系统根据学生当前掌握度和知识点特性计算预估题目数量
3. 系统按题组顺序推荐首题（优先推荐必做题中预期掌握度增量最大的题目）

```mermaid
sequenceDiagram
    participant 学生
    participant 巩固练习服务
    participant 掌握度服务
    participant 题库服务

    学生->>巩固练习服务: 进入巩固练习
    巩固练习服务->>掌握度服务: 获取当前掌握度
    掌握度服务-->>巩固练习服务: 返回掌握度数据
    巩固练习服务->>巩固练习服务: 计算预估题目数量
    巩固练习服务->>题库服务: 获取题组信息
    题库服务-->>巩固练习服务: 返回题组和题目信息
    巩固练习服务->>巩固练习服务: 选择预期掌握度增量最大的必做题
    巩固练习服务-->>学生: 推送首题
```

- 优先级：高
- 依赖关系：无

### 场景2：学生在题组内继续答题
作为一个正在进行巩固练习的学生，我希望系统能够根据我的答题表现智能调整后续题目的难度，这样我就可以在合适的挑战度下持续学习。目前的痛点是题目难度跳跃太大或者重复性太高，如果能够平滑调整难度，对我的学习体验会有很大帮助。

**关键步骤：**
1. 学生完成当前题目作答
2. 系统更新学生掌握度并重新计算剩余题目的预期掌握度增量
3. 系统根据难度限制条件选择下一题（连续两题难度最多提升一个等级）

```mermaid
flowchart TD
    A[学生提交答案] --> B[更新掌握度]
    B --> C{当前题组是否已达预估答题量}
    C -->|是| D[进入下一题组]
    C -->|否| E{剩余题目预期掌握度增量是否≥0.01}
    E -->|否| F{本题组是否已作答2题}
    F -->|是| D
    F -->|否| G[选择预期掌握度增量最大的题目]
    E -->|是| G
    G --> H{检查难度限制条件}
    H --> I[推送下一题]
    D --> J[按顺序进入下一题组]
```

- 优先级：高
- 依赖关系：依赖于场景1

### 场景3：学生触发再练一次
作为一个完成巩固练习但掌握度仍不理想的学生，我希望能够针对错题进行重点练习，这样我就可以更好地掌握薄弱知识点。目前的痛点是不知道哪些题目需要重点练习，如果能够智能推荐错题相关的练习，对我的学习效果会有很大帮助。

**关键步骤：**
1. 学生完成巩固练习，系统判断是否需要引导再练一次
2. 学生选择再练一次，系统筛选有错题的题组
3. 系统优先推荐错题中预期掌握度增量最大的题目

```mermaid
flowchart TD
    A[完成巩固练习] --> B{当前掌握度 < 目标掌握度×70%}
    B -->|否| C[结束练习]
    B -->|是| D{正确率 < 70%}
    D -->|否| C
    D -->|是| E[显示再练一次引导]
    E --> F{用户选择再练一次}
    F -->|否| C
    F -->|是| G[筛选有错题的题组]
    G --> H{可用题目数量 ≥ 3}
    H -->|否| I[不显示再练一次入口]
    H -->|是| J[开始再练一次]
    J --> K[推荐错题中预期掌握度增量最大的题目]
```

- 优先级：中
- 依赖关系：依赖于场景1和场景2

### 场景4：触发熔断策略
作为一个在练习中遇到困难的学生，我希望系统能够在我连续答错或注意力不集中时及时停止练习，这样我就可以避免挫败感并保持学习积极性。目前的痛点是可能会在不合适的状态下继续答题，如果能够智能判断并适时停止，对我的学习心态会有很大帮助。

**关键步骤：**
1. 系统实时监控学生答题表现和专注度
2. 当触发熔断条件时（连续5题错误且无更低难度题目，或专注度分值为3），立即结束练习
3. 系统给出友好的结束提示和建议

```mermaid
stateDiagram-v2
    [*] --> 正常答题
    正常答题 --> 监控状态 : 每次答题后
    监控状态 --> 连续错误检查 : 答错题目
    监控状态 --> 专注度检查 : 更新专注度分值
    连续错误检查 --> 熔断触发 : 连续5题错误且无更低难度题
    专注度检查 --> 熔断触发 : 专注度分值=3
    监控状态 --> 正常答题 : 未触发熔断条件
    熔断触发 --> [*] : 结束练习
```

- 优先级：中
- 依赖关系：依赖于专注度评分系统

## 5. 业务流程图

```mermaid
flowchart TD
    A[开始巩固练习] --> B[计算预估题目数量]
    B --> C[按顺序进入题组]
    C --> D{是否为题组首题}
    D -->|是| E{题组内是否有必做题}
    D -->|否| F[选择预期掌握度增量最大的题目]
    E -->|是| G[在必做题中选择预期掌握度增量最大的题目]
    E -->|否| F
    G --> H[推送题目]
    F --> H
    H --> I[学生答题]
    I --> J[更新掌握度和专注度]
    J --> K{检查熔断条件}
    K -->|触发熔断| L[结束练习]
    K -->|未触发| M{当前题组是否已达预估答题量}
    M -->|是| N{是否有未作答题组}
    M -->|否| O{剩余题目预期掌握度增量≥0.01}
    O -->|是| F
    O -->|否| P{本题组是否已作答2题}
    P -->|是| N
    P -->|否| F
    N -->|是| C
    N -->|否| Q{是否需要再练一次}
    Q -->|是| R[开始再练一次流程]
    Q -->|否| L
    R --> S[筛选有错题的题组]
    S --> T[推荐错题中预期掌握度增量最大的题目]
    T --> I
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：题目推荐的响应时间不超过2秒
- 并发用户：系统需支持1000个并发用户同时进行巩固练习，保证性能不衰减
- 数据量：系统需支持100万条答题记录的存储和查询，响应时间不超过1秒

### 6.2 安全需求
- 权限控制：题目内容访问需要学生身份验证才能访问
- 数据加密：学生答题数据需要使用AES-256进行加密存储和传输
- 操作审计：对答题行为需要记录操作日志，包括用户ID、题目ID、答题时间、答案内容

## 7. 验收标准

### 7.1 功能验收
- **题目推荐策略**：
  - 使用场景：当学生进入巩固练习时
  - 期望结果：系统应根据预期掌握度增量推荐最合适的题目，必做题优先级高于普通题目
- **预估题目数量计算**：
  - 使用场景：当学生开始巩固练习时
  - 期望结果：系统应根据知识点难度、考频和用户掌握度计算出合理的题目数量（8-15题）
- **再练一次功能**：
  - 使用场景：当学生完成巩固练习且满足再练条件时
  - 期望结果：系统应显示再练一次入口，并优先推荐错题
- **熔断策略**：
  - 使用场景：当学生连续答错5题或专注度分值为3时
  - 期望结果：系统应立即结束练习并给出友好提示

### 7.2 性能验收
- 响应时间：
  - 给定正常网络条件，当执行题目推荐操作，响应时间不超过2秒
  - 给定正常网络条件，当查询答题记录，响应时间不超过1秒
- 并发用户：
  - 给定1000个并发用户，模拟巩固练习场景，系统运行30分钟，无异常，平均响应时间不超过3秒

### 7.3 安全验收
- 权限控制：
  - 给定未登录用户，当访问题目内容，返回权限错误
  - 给定已登录学生，当访问题目内容，可以正常使用
- 数据加密：
  - 给定答题数据存储，当查询数据库，数据是加密状态，且使用AES-256算法
- 操作审计：
  - 给定学生答题操作，当执行答题，操作日志正常记录，包含用户ID、题目ID、答题时间、答案内容

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：巩固练习的操作界面简洁友好，3步内可完成主要操作
- 容错处理：网络异常发生时，系统可以本地缓存答题数据，并给出友好提示
- 兼容性：需支持Chrome/Safari/移动端浏览器等主流环境，保证界面和功能正常

### 8.2 维护性需求
- 配置管理：难度系数、考频系数可由系统管理员在后台界面进行配置和调整
- 监控告警：系统异常发生时，系统自动告警，通知运维人员
- 日志管理：系统自动记录答题日志和推荐日志，日志保存30天，可供系统管理员查询和分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 用户可以通过练习结束页面的反馈按钮和客服系统提交反馈
- 每周定期收集和分析用户反馈，识别推荐算法和用户体验的改进点

### 9.2 迭代计划
- 迭代周期：每4周
- 每次迭代结束后，评估推荐效果和用户满意度，调整算法参数和策略优先级

## 10. 需求检查清单

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
|----------|------------|--------|--------|--------|----------|----------|------|
| 基础答题量计算公式：基础答题量=10*难度系数*考频系数 | 3.1 基础答题量计算 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 动态调整系数A计算公式 | 3.2 动态调整系数计算 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 预期掌握度增量计算方式 | 3.3 预期掌握度增量计算 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目难度等级L1-L5对应系数0.2-1.0 | 3.4 题目难度系数映射 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 巩固练习推题整体流程 | 2.1 题目推荐策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 必做题优先推荐策略 | 4.1 场景1：学生首次进入巩固练习 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 连续两题难度限制条件 | 4.2 场景2：学生在题组内继续答题 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次判断条件：掌握度<目标掌握度70%且正确率<70% | 4.3 场景3：学生触发再练一次 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次可用题目数量≥3题的条件 | 2.1 再练一次功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 熔断策略：连续5题错误且无更低难度题目 | 4.4 场景4：触发熔断策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 熔断策略：专注度分值等于3 | 2.1 熔断策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 专注度评分系统：初始10分，触发事件-1分 | 2.2 专注度评分系统 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 专注度判断事件：1s内作答且错误、连续3题错误等 | 2.2 专注度评分系统 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题组结构：每节课1-5个题组，每个题组10道题左右 | 2.2 题组管理 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 大题处理：计算是否超出预估答题数量 | 2.1 题目推荐策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 最小答题数量：题组数量*2 | 2.1 题目推荐策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 最大答题数量：不超过预估答题数 | 2.1 题目推荐策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |

## 11. 附录

### 11.1 原型图
[此处应附上巩固练习界面的原型图或界面设计图]

### 11.2 术语表
- **掌握度**: 学生对特定知识点的掌握程度，取值范围0-1
- **预期掌握度增量**: 学生完成某道题目后预期的掌握度提升量
- **题组**: 按典型题型组织的题目集合
- **必做题**: 教师标记为推荐的重要题目
- **熔断策略**: 基于学生表现自动终止练习的机制
- **专注度分值**: 评估学生答题专注程度的分数

### 11.3 参考资料
- PRD - 掌握度 - V1.0
- AI课程学习流程设计文档
- 题目难度分级标准文档 
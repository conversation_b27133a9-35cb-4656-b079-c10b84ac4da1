# 产品需求文档：课中练习推题策略 V1.0

## 1. 需求背景与目标

### 1.1 需求背景
在AI课堂教学过程中，为了提升学生的专注度和课堂互动感，避免单向知识输入，需在课程中合理穿插练习题组件。课中练习题主要用于巩固教师讲解内容，帮助学生即时检验对知识点的理解情况，同时通过适当的练习密度与反馈机制，增强课堂节奏感与参与感。因此，需要制定一套清晰可执行的课中推题策略，确保练习题推送符合教学节奏，兼顾知识巩固的目的。

### 1.2 项目收益
- **提升学习效果**：通过实时练习巩固课堂所学内容，帮助学生在知识点讲解后及时验证理解，降低遗忘率。
- **增强课堂互动与专注度**：通过答题互动打破单向讲授模式，提高学生参与感和课堂活跃度，延长课堂专注时长。
- **及时发现学习问题**：通过答错推送相似题，帮助学生暴露理解误区，实现即时纠偏，提高学习精准度。

### 1.3 覆盖用户
- 使用新AI课学习的全部用户。

## 2. 功能范围

### 2.1 核心功能

#### 2.1.1 课中练习题目推荐策略 (P0)
- **需求描述**：在AI课中的练习组件内，根据预设规则和学生答题情况动态推荐题目，以达到巩固知识、提升互动和即时反馈的效果。
- **主要功能点**：
    1.  **练习内容结构**：
        -   每节课可配置1-N个练习组件，这些组件在配课环节被插入到课中不同位置。
        -   每个练习组件包含1-N个题目。
        -   题目类型分为"单题"和"相似题组"。"相似题组"中至少包含2道相似题目。
        -   题目在练习组件内的推荐顺序由教研在后台配置时控制。
        -   每个题目关联知识点，并具有难度等级标签（课中练习题难度以L1、L2为主）。
    2.  **基本推题规则**：
        -   练习组件内的题目按后台配置的顺序依次推送给学生作答。
        -   当遇到"相似题组"时，从题组中随机选择一道题目进行推送。
        -   如果学生答对当前题目，则继续按顺序推出下一道题目（或下一个相似题组中的随机一题）。
        -   如果学生答错当前题目：
            -   系统检查当前题目是否存在未被作答过的相似题。若存在，则从这些相似题中随机推出一道。
            -   若不存在相似题（或相似题均已作答过），则按顺序继续推出下一道题目。
        -   针对一道错题所推送的相似题，只推送一次。无论学生是否答对这道相似题，完成后都继续按配置顺序推出后续的题目。

### 2.2 非本期功能 (未来可拓展)
1.  **区分错误的严重程度或错误类型进行推题**：例如，学生选择明显错误选项时，推两道不同角度的相似题；若因粗心导致小错误，则推一道相似题。
2.  **个性化动态调整题目数量**：例如，基于学生实时答题表现（如连续答对N题），允许系统跳过后续的部分简单题，以加快学习节奏。

## 3. 计算规则与公式
*本期推题策略主要基于逻辑判断，暂不涉及复杂的计算规则与公式。相关的题目属性（如难度、知识点）和学生答题数据（正确与否）是策略判断的输入。*

## 4. 用户场景与故事

### 场景1: 学生顺序作答练习题并遇到错题
小明正在学习AI课程的某一章节，进入了课中练习环节。他按顺序作答，前面几道题都答对了。接着遇到一道题目，他不小心答错了。系统判断他答错后，推送了一道与原题相似的题目。小明作答了这道相似题后（无论对错），系统继续推送了后续的题目。

**关键步骤：**
1.  学生进入练习组件，系统按配置顺序推送题目A。
2.  学生作答题目A正确。
3.  系统按配置顺序推送题目B。
4.  学生作答题目B错误。
5.  系统检查题目B是否有相似题。若有，则随机推送相似题B'。
6.  学生作答相似题B'。
7.  系统按配置顺序推送题目C（题目B后续的题目）。

**Mermaid图示 (活动图):**
```mermaid
stateDiagram-v2
    [*] --> "开始练习"
    "开始练习" --> "推送题目A"
    "推送题目A" --> "学生作答A"
    "学生作答A" --> "判断A结果"
    "判断A结果" --> "推送题目B" : 正确
    "推送题目B" --> "学生作答B"
    "学生作答B" --> "判断B结果"
    "判断B结果" --> "检查B有无相似题" : 错误
    "检查B有无相似题" --> "推送相似题Bprime" : 有
    "推送相似题Bprime" --> "学生作答Bprime"
    "学生作答Bprime" --> "推送题目C"
    "检查B有无相似题" --> "推送题目C" : 无
    "判断B结果" --> "推送题目C" : 正确
    "推送题目C" --> [*]
```

### 场景2: 学生遇到相似题组
小华在练习过程中，系统根据后台配置的顺序，推送到了一个"相似题组X"。该题组包含题目X1, X2, X3。系统从这三道题中随机选择了一道（例如X2）推送给小华作答。小华完成X2后，系统继续按顺序推送"相似题组X"之后的下一道题目或题组。

**关键步骤：**
1.  学生完成前序题目，系统按配置轮到"相似题组X"。
2.  系统从"相似题组X"（包含X1, X2, X3）中随机选择一道题目，如X2，推送给学生。
3.  学生作答题目X2。
4.  无论学生答对或答错X2（若答错，按错题逻辑处理相似题，但此处核心是题组只推其一）。
5.  系统按配置顺序推送"相似题组X"之后的下一题目/题组。

**Mermaid图示 (流程图):**
```mermaid
flowchart TD
    A[开始练习或上一题完成] --> B{下一项是单题还是相似题组?}
    B -- "单题" --> C[推送该单题]
    B -- "相似题组" --> D[从相似题组中随机选一题]
    D --> E[推送选中的题目]
    C --> F[学生作答]
    E --> F
    F --> G{答题结果处理}
    G -- "答对" --> H[继续下一项]
    G -- "答错" --> I{有无相似题（非题组内）?}
    I -- "有" --> J[推送错题的相似题]
    J --> H
    I -- "无" --> H
    H --> B
    %% 循环回下一项判断
    H --> End[练习结束]
```

## 5. 业务流程图

### 5.1 课中练习推题核心策略流程
*此流程图基于原始PRD图片 `board_R7zlwS1uOhjgUAbT5MZcdQNan05` 的解析进行绘制。*
```mermaid
flowchart TD
    A[开始课中推题] --> B{学生是否答对当前题目?};
    B -- "是" --> E{是否还有未推荐的题目?};
    B -- "否 (答错)" --> C{当前错题是否有未推荐的相似题?};
    C -- "是" --> D[从相似题中随机推一道];
    D --> E;
    C -- "否" --> E;
    E -- "是" --> A;
    E -- "否" --> F[练习组件内题目推荐结束];
```

### 5.2 练习内容层级结构示意
*此流程图基于原始PRD图片 `board_AFnaw4y5nhDgPybpTZXcJdthnTe` 的解析进行绘制。*
```mermaid
graph TD
    Level1[单节课] --> EC1[练习组件1];
    Level1 --> EC2[练习组件2];

    EC1 --> Q1_1[题目1 （单题）];
    EC1 --> Q1_2[题目2 （单题）];
    EC1 --> SG1_3[相似题组3];
    SG1_3 --> Q1_3_Variant1[题目3'];
    SG1_3 --> Q1_3_Variant2[题目3''];

    EC2 --> Q2_1[题目4 （单题）];
    EC2 --> SG2_2[相似题组5];
    %% 假设题目4后是相似题组5
    SG2_2 --> Q2_2_Variant1[题目5'];
    SG2_2 --> Q2_2_Variant2[题目5''];
    SG2_2 --> Q2_2_Variant3[题目5'''];
```

## 6. 性能与安全需求

### 6.1 性能需求
- **题目推荐响应时间**：从学生提交上一题答案到系统确定并开始加载下一题的逻辑处理时间应小于200毫秒。
- **题目数据加载**：练习组件中题目内容（文本、图片等）的加载时间需符合《AI课中-练习组件V1.0》PRD中定义的性能标准。

### 6.2 安全需求
- **防作弊**：防止通过恶意请求获取题目顺序或答案（如果适用）。
- **数据一致性**：确保教研配置的题目顺序、相似题关系等数据在推荐过程中被正确读取和使用。

## 7. 验收标准

### 7.1 功能验收

#### 7.1.1 题目推送顺序
- **使用场景**：学生进入一个包含多个单题和相似题组的练习组件。
- **期望结果**：
    - 系统严格按照后台配置的顺序推送题目或相似题组。
    - 当遇到相似题组时，从组内题目中随机选择一道进行推送，不会重复推送同一相似题组内的其他题目（除非是作为错题的相似题被再次推送）。

#### 7.1.2 答对题目后的逻辑
- **使用场景**：学生正确回答当前推送的题目。
- **期望结果**：系统继续按配置顺序推送下一道题目或下一个相似题组中的随机一题。

#### 7.1.3 答错题目后的逻辑 (无相似题)
- **使用场景**：学生错误回答当前推送的题目，且该题目没有配置相似题，或者其相似题均已作为错题相似题被推送过。
- **期望结果**：系统继续按配置顺序推送下一道题目或下一个相似题组中的随机一题。

#### 7.1.4 答错题目后的逻辑 (有相似题)
- **使用场景**：学生错误回答当前推送的题目，且该题目配置了未被推送过的相似题。
- **期望结果**：
    - 系统从该错题的相似题中随机选择一道进行推送。
    - 该相似题被推送后，无论学生答对与否，系统都继续按原配置顺序推送错题之后的下一道题目或相似题组。
    - 一道错题的相似题只会被触发推送一次。如果学生再次答错原题（例如，通过回顾功能），不再重复推送其相似题。

### 7.2 性能验收
*  **IMPORTANT AI (Chief Product Manager) INSTRUCTION:** 此节应该是对 6.1 节中每个需求点的详细验收标准，包括具体的测试用例和期望结果。
- **题目推荐响应时间**：
    - 给定学生提交答案的请求，在服务端完成答案判断后，推题逻辑模块在200毫秒内确定下一题的ID。

### 7.3 安全验收
*  **IMPORTANT AI (Chief Product Manager) INSTRUCTION:** 此节应该是对 6.2 节中每个需求点的详细验收标准，包括具体的测试用例和期望结果。
- **数据一致性**：
    - 后台配置A、B（相似题组含B1,B2）、C的顺序。用户首次进入，推A。用户答对A，推B1或B2。用户答对B1/B2，推C。流程符合预期。
    - 后台配置题目X，其相似题为X'。用户答错X，应推X'。若用户再错X'，不应再推其他相似题（除非X'本身也有相似题配置且当前在处理X'的错题逻辑），而是继续X的后续题目。

## 8. 其他需求

### 8.1 可用性需求
- **策略可理解性**：对于运营和教研人员，推题策略的配置方式应清晰易懂。
- **容错处理**：如果教研配置的题目数据存在逻辑问题（如相似题组为空、题目顺序断裂），系统应有合理的降级机制或在后台给出明确提示，避免影响前端用户体验。

### 8.2 维护性需求
- **配置管理**：题目顺序、相似题关系、题目难度等应支持在后台管理界面进行配置和调整。
- **监控告警**：当推题服务出现异常（如无法根据策略找到下一题），应有监控和告警机制。
- **日志管理**：详细记录每次推题的决策过程，包括输入的学生状态、当前题目、答题结果、以及最终推荐的下一题目ID和原因，便于问题排查和策略分析。

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 主要通过分析学生在练习组件中的整体表现数据（如各题正确率、完成时长、相似题的纠错成功率等）来间接评估推题策略的有效性。
- 教研和产品团队定期回顾推题策略的实际运行效果。

### 9.2 迭代计划
- **V1.0上线后**：重点监控推题逻辑的准确性和稳定性，收集练习完成数据，验证策略是否符合预期。
- **后续迭代**：根据数据分析结果和教学效果反馈，考虑引入2.2节中规划的"区分错误严重程度推题"和"个性化动态调整题目数量"等高级策略。

## 10. 需求检查清单
*  **IMPORTANT AI (Chief Product Manager) INSTRUCTION:** 本节必须保留，并且需要根据原始需求文档中的具体需求点进行填写。"原始需求"列，必须详细列出来自原始需求文档"详细产品方案"的所有具体功能点和需求描述。确保覆盖所有细化要求，而不是仅列出概览性条目，随后，将这些详细的原始需求点**严格映射**到新生成文档中对应的具体章节或功能点上，并进行比对评估。检查项必须全部完成，不得遗漏。

| 原始需求 (摘自PRD "课中练习推题策略 - V1.0" 第三节 "策略方案")                                                              | 对应新PRD需求点 (章节)                      | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
| --------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------- | ------ | ------ | ------ | -------- | -------- | ---- |
| 一个练习组件中的多个题目，需要按后台配置顺序依次作答。                                                                                      | 2.1.1 (核心功能 / 主要功能点 1 & 2)         | ✅      | ✅      | ✅      | ✅        | ✅        |      |
| 相似题题组中，可随机推出一题。                                                                                                      | 2.1.1 (核心功能 / 主要功能点 2)         | ✅      | ✅      | ✅      | ✅        | ✅        |      |
| 如果用户答对，则继续推出下一题。                                                                                                      | 2.1.1 (核心功能 / 主要功能点 2)         | ✅      | ✅      | ✅      | ✅        | ✅        |      |
| 如果用户答错，有相似题则推出相似题，没有相似题则继续推出下一题。                                                                              | 2.1.1 (核心功能 / 主要功能点 2)         | ✅      | ✅      | ✅      | ✅        | ✅        |      |
| 答错后的相似题只推出一次，无论用户是否回答正确都继续推出后续题目。                                                                                | 2.1.1 (核心功能 / 主要功能点 2)         | ✅      | ✅      | ✅      | ✅        | ✅        |      |
| 未来可拓展：区分错误的严重程度或错误类型 (推不同数量相似题)。                                                                                 | 2.2 (非本期功能 1)                          | ✅      | ✅      | ✅      | N/A      | ✅        |      |
| 未来可拓展：个性化推题 (基于实时答题表现调整题目数量，如连对跳题)。                                                                               | 2.2 (非本期功能 2)                          | ✅      | ✅      | ✅      | N/A      | ✅        |      |

- 完整性：原始需求是否都有对应的优化后需求点，无遗漏
- 正确性：优化后需求描述是否准确表达了原始需求的意图
- 一致性：优化后需求之间是否有冲突或重复
- 可验证性：优化后需求是否有明确的验收标准
- 可跟踪性：优化后需求是否有明确的优先级和依赖关系

✅表示通过；❌表示未通过；⚠️表示描述正确，但细节不完整（如计算规则、阈值等，需要和 PM 进一步沟通）；N/A表示不适用。每个需求点都需要填写。

## 11. 附录

### 11.1 原型图
- 课中练习内容层级结构图 (参考原始PRD图片 `board_AFnaw4y5nhDgPybpTZXcJdthnTe`)
- 课中练习推题策略流程图 (参考原始PRD图片 `board_R7zlwS1uOhjgUAbT5MZcdQNan05`)

### 11.2 术语表
- **练习组件**：在单节AI课中，用于组织和呈现一组练习题的功能模块。
- **单题**：独立的、不与其他题目直接关联作为变体的练习题目。
- **相似题组**：由一道母题和若干道围绕相同知识点或考点的变体题目构成的一组题目。
- **推题策略**：根据学生答题行为和预设规则，动态决定下一道应推送给学生的练习题目的机制。

### 11.3 参考资料
- 原始需求文档: `documents/raw-docs/PRD - 课中练习推题策略 - V1.0_analyzed.md`
- 《AI课中-练习组件V1.0》PRD (本文档中涉及的练习组件行为需参考此文档)

--- 等待您的命令，指挥官 
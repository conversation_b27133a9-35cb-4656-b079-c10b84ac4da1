---
description: 
globs: backend/app/infrastructure/repositories/**/*.py
alwaysApply: false
---
# 仓储层 (Repository) 代码生成规则

> **核心原则：简洁优先**

## 边界
- 仅与 `SQLModel` / 数据库会话交互；如需缓存可注入缓存服务  
- 不提交事务边界；由调用方 (`AppService`) 显式 `commit` / `rollback`  
- 返回领域实体或值对象，不泄露 ORM 实体  
- 封装底层异常为 `RepositoryError`  

## 约定
- 接口命名 `I<Entity>Repository`；实现去掉前缀 `I`  
- 查询方法命名 `find_by_*` / `exists_by_*`；Criteria 对象处理复杂检索
- 写操作可直接 `session.add()` + `commit()` 或 `session.merge()`；也可 `with session.begin(): …`  
- 读操作使用 `select(...)`；避免 N+1，必要时关联加载  
- 分页返回 `(items: list[Entity], total: int)`  

## 示例代码
```python
class UserRepository(IUserRepository):
    def save(self, user: User) -> User:
        try:
            self._session.add(UserMapper.to_model(user))
            self._session.commit()
            return user
        except Exception as exc:
            self._session.rollback()
            raise RepositoryError(str(exc)) from exc
```
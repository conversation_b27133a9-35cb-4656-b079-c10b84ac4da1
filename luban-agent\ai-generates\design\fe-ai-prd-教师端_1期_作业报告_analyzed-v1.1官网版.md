输入文件：`[documents/raw-docs/教师端_1期_作业报告_analyzed.md]`
输出文件：`[documents/ai-docs/ai-prd-教师端_1期_作业报告_analyzed-v1.1.md]`


# 产品需求文档：教师端作业报告 v1.1

## 1. 需求背景与目标

### 1.1 需求目标
- 通过该需求的实现，可以帮助教师高效把控学生学习情况，及时对学生进行鼓励和关注，从而提升教学质量和学生学习效果。 
- 该需求上线后，预计教师查看作业报告的效率提升50%，学生问题反馈处理及时率达到90%。

### 1.2 用户价值
- 对于 **学科老师**，解决了需要花费大量时间从原始数据中分析学情、定位学生问题的痛点，带来了高效、精准的学情概览和个体分析能力，使其能快速进行教学干预。 
- 对于 **班主任**，满足了其快速了解班级整体学习状况、识别共性问题和需要关注学生的核心诉求，实现了便捷的班级学情管理。 
- 对于 **学科主任/年级主任/校长**，提供了便捷的数据查看通道，辅助其进行教学质量监控和决策。 

## 2. 功能范围

### 2.1 核心功能

#### **[核心功能点1]: 作业列表展示与筛选**

**用户价值与场景:** 满足教师快速查找和定位不同维度（类型、班级、学科、日期）作业的需求，解决信息过载、查找效率低的问题，为教师带来便捷的作业管理体验。 

**功能详述与前端呈现:**
- **主要交互流程:**
    1. 教师进入作业管理模块，默认展示其有权限查看的作业列表。 
    2. 教师可通过顶部分类页签（全部、课程、作业、资源、测验）切换不同类型的任务列表。 
    3. 教师可使用筛选器（班级、学科、布置日期）组合筛选作业列表。 
    4. 教师可在搜索框输入关键词，模糊匹配任务名称进行搜索。 
    5. 列表以卡片形式展示作业核心信息。 
- **界面布局与元素:**
    - **顶部导航栏**: 固定显示，包含返回按钮（若适用）、页面标题（如“作业”）。
    - **分类页签**: 横向排列，选中状态高亮。例如：“课程”、“作业”、“资源”、“测验”。 
    - **筛选器区域**: 横向排列或下拉菜单形式，包含“班级”（如“高一3班”）、“学科”（如“数学”）、“布置日期”（如“最近一周”，点击后可选择预设范围或自定义日期）。 筛选器选中项应清晰显示。
    - **搜索框**: 位于筛选器区域附近，有明确的“搜索题目关键词”或类似占位符。 输入时应有清空按钮。
    - **作业卡片列表区**: 占据页面主要区域，纵向滚动。
        - **作业卡片**: 每个卡片清晰展示作业标题、所属班级、发布/到期时间、状态（如“到期”）、核心数据指标（如完成率、正确率、待关注项等，根据作业类型动态显示）。 卡片右侧（或下方）应有操作入口（如“...”更多操作）。
        - **卡片状态**:
            - **默认**: 清晰展示信息。
            - **悬停/点击**: 应有视觉反馈，如背景色变化或轻微放大。
    - **加载状态**: 列表加载时应有加载中提示（如骨架屏或Loading动画）。
    - **空状态**: 当筛选结果为空时，应有友好的空状态提示，如“暂无相关作业”。
- **用户操作与反馈:**
    - **切换页签**: 点击页签，列表内容立即刷新为对应类型作业，选中页签高亮。
    - **选择筛选条件**: 选择或修改筛选条件后，作业列表自动刷新。
    - **输入搜索关键词**: 输入过程中可实时筛选（可选）或输入完成后按回车/点击搜索按钮进行筛选。列表实时更新。 
    - **清空搜索**: 点击清空按钮，清除搜索关键词，列表恢复到搜索前状态。 
    - **滚动列表**: 支持平滑滚动加载更多作业（若有分页）。
    - **点击作业卡片**: 跳转至该作业的报告详情页。 
    - **点击卡片操作按钮**: 弹出操作菜单（如编辑、删除、复制任务）。 
- **数据展示与更新:**
    - 作业列表数据根据筛选条件动态从服务端获取并更新。
    - 作业卡片上的核心指标（完成率、正确率等）应为实时或准实时数据。 

**优先级：** P0 
**依赖关系：** 无

##### 用户场景与故事 (针对此核心功能点)

###### [场景1标题：学科老师快速查找并查看本周布置的数学作业]
作为一个 **学科老师**，我希望能够 **通过学科和日期快速筛选出我本周布置给高一3班的数学作业列表**，这样我就可以 **快速了解这些作业的整体情况并选择进入某个作业报告查看详情**。目前的痛点是 **作业太多，需要逐个翻找，效率低下**，如果能够 **提供便捷的多维度筛选和清晰的列表展示**，对我的工作会有很大帮助。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 老师进入“作业”模块。
    * **界面变化:** 默认展示有权限查看的全部作业列表，分页加载。顶部显示“全部类型”、“班级”、“学科”、“布置日期”等筛选器及搜索框。 
    * **即时反馈:** 列表加载完成后，显示第一页作业卡片。
2.  **[用户操作2]:** 老师点击“学科”筛选器，选择“数学”。
    * **界面变化:** “学科”筛选器显示“数学”，作业列表异步刷新，仅展示数学学科的作业。 
    * **即时反馈:** 列表上方可出现短暂的加载提示，刷新完成后列表内容更新。
3.  **[用户操作3]:** 老师点击“班级”筛选器，选择“高一3班”。
    * **界面变化:** “班级”筛选器显示“高一3班”，作业列表在当前“数学”学科基础上进一步异步刷新，仅展示高一3班的数学作业。 
    * **即时反馈:** 列表更新。
4.  **[用户操作4]:** 老师点击“布置日期”筛选器，选择“最近一周”。
    * **界面变化:** “布置日期”筛选器显示“最近一周”，作业列表在当前条件下再次异步刷新，仅展示最近一周布置给高一3班的数学作业。 
    * **即时反馈:** 列表更新。列表卡片上显示作业名称、班级、完成率、正确率、待关注项、发布与到期时间等。 
5.  **[用户操作5]:** 老师浏览列表，点击其中一个名为“3.2.2函数的最值”的作业卡片。
    * **界面变化:** 页面跳转至“3.2.2函数的最值”作业的报告详情页。 
    * **即时反馈:** 页面平滑过渡，加载作业报告详情。

**场景细节补充：**
* **前置条件:** 老师已登录系统，并拥有查看相关班级和学科作业的权限。系统中已存在符合条件的作业数据。
* **期望结果:** 老师成功筛选出目标作业列表，并顺利进入指定作业的报告详情页。
* **异常与边界情况:**
    * 筛选结果为空：列表区域应显示“暂无符合条件的作业”的提示。
    * 网络请求失败：应有友好的错误提示，如“列表加载失败，请稍后重试”。
    * 单个作业卡片信息不完整：应优雅显示，避免报错。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (核心功能点本身的流程图/状态图等):**
```mermaid
flowchart TD
    A[用户进入作业列表页] --> B{选择内容类型 Tab};
    B -- 作业 --> C[显示作业列表];
    C --> D{应用筛选条件};
    D -- 学科/班级/日期 --> E[向服务端请求筛选后的数据];
    E --> F[服务端返回数据];
    F --> G[前端渲染作业卡片列表];
    G --> H{用户点击作业卡片};
    H -- 是 --> I[跳转至作业报告详情页];
    D -- 搜索关键词 --> J[向服务端请求搜索结果];
    J --> F;
    G --> K{用户进行其他操作};
    K -- 编辑/删除/复制 --> L[执行相应操作];
````

#### **[核心功能点2]: 班级整体作业报告查看（学生报告Tab）**

**用户价值与场景:** 满足教师快速了解整个班级在某次作业中的宏观表现（如平均进度、正确率），并能便捷地识别出“值得表扬”和“需要关注”的学生群体，解决逐个分析学生数据耗时耗力的问题，为教师的整体教学调整和分层指导提供数据支持。

**功能详述与前端呈现:**

  - **主要交互流程:**
    1.  教师从作业列表点击某个作业卡片，进入该作业的报告详情页，默认展示“学生报告”Tab。
    2.  页面顶部展示作业的基本信息（名称、发布/截止时间、课程范围等）。
    3.  页面中部优先展示“值得表扬”和“需要关注”的学生名单（或人数统计和代表学生），并提供“一键表扬”、“一键提醒”等快捷操作。
    4.  接着展示班级整体的平均正确率和平均进度（完成率）。
    5.  页面下方以列表形式展示班级内所有学生的个体作业数据，包含学生信息、分层、完成进度、正确率、答题难度（若有）、错题数/答题数、用时等，并提供查看单个学生详情的操作。
    6.  教师可以通过搜索框搜索特定学生。
    7.  教师可以导出报告数据。
  - **界面布局与元素:**
      - **顶部作业信息区**: 显示作业标题、发布时间、截止时间、课程范围。 可能包含“预警设置”入口。
      - **Tab切换区**: “学生报告”（默认选中）、“答题结果”、“学生提问”三个Tab。
      - **快捷互动区 (学生报告Tab内)**:
          - **“值得表扬”学生组**: 标签“值得表扬”，展示若干学生姓名，及“一键表扬”按钮。按钮默认可用，点击后可变为禁用状态或显示操作成功反馈。
          - **“需要关注”学生组**: 标签“需要关注”，展示若干学生姓名，及“一键提醒”按钮。交互同上。
      - **班级统计数据区**: 清晰展示“班级平均正确率: XX%”、“班级平均用时: XX分钟”等。
      - **学生列表操作区**:
          - **搜索框**: “搜索学生姓名”占位符。
          - **导出按钮**: “导出”按钮，点击后触发数据导出流程。
      - **学生详细数据列表区**:
          - **表头**: “学生信息”、“分层”、“完成进度”、“正确率”、“答题难度”、“错题/答题数”、“用时”、“操作”。 表头固定，内容可滚动。
          - **数据行**: 每行展示一个学生的数据。对于偏离均值较大的数据，可进行飘色提醒。 “操作”列包含“查看详情”按钮。
              - **学生信息**: 头像（可选）、姓名。
              - **分层**: 如“有进步”、“需关注”等标签。
              - **完成进度/正确率**: 百分比显示，可配合进度条或颜色区分。
              - **“查看详情”按钮**: 默认可用。
      - **分页控件**: 若学生人数较多，列表底部应有分页控件，显示总条数。
  - **用户操作与反馈:**
      - **点击“一键表扬”/“一键提醒”**: 按钮显示加载状态，操作成功后Toast提示“表扬成功”/“提醒已发送”，按钮可能变为不可用或更新状态。
      - **搜索学生**: 输入姓名，列表实时刷新或按回车后刷新，展示匹配结果。
      - **点击“导出”**: 触发文件下载，按钮可显示“导出中...”状态。
      - **点击学生列表“查看详情”**: 跳转至该学生的个体作业报告详情页。
      - **滚动学生列表**: 若有分页，滚动到底部自动加载下一页或通过分页控件切换。
  - **数据展示与更新:**
      - 页面加载时异步获取作业报告的整体数据和学生列表数据。
      - “值得表扬”和“需要关注”的学生名单由后端算法生成或教师手动标记后展示。
      - 学生列表数据支持按特定规则排序（如学号、进度、正确率等，默认按uid）。

**优先级：** P0
**依赖关系：** 核心功能点1 (作业列表)

##### 用户场景与故事 (针对此核心功能点)

###### [场景1标题：老师查看班级作业报告，快速了解整体学情并关注重点学生]

作为一个 **学科老师**，我希望在查看某次作业的报告时，能够 **一眼看到班级的平均表现，并快速找到哪些学生做得好需要表扬，哪些学生做得差需要关注**，这样我就可以 **有针对性地进行后续的教学安排和个别辅导**。目前的痛点是 **需要自己从一堆数据里分析总结，效率低且容易遗漏**，如果能够 **系统自动汇总关键信息并高亮重点学生**，对我的教学帮助会非常大。

**前端界面与交互关键步骤：**

1.  **[用户操作1]:** 老师点击进入某次作业的报告详情页，默认显示“学生报告”Tab。
      * **界面变化:** 页面顶部展示作业名称、时间等信息。下方依次展示“值得表扬”（如周雨彤、刘明宇等6人）和“需要关注”（如李子涵）学生区块，每个区块有对应操作按钮（一键表扬/提醒）。再下方是班级平均正确率和平均用时。最下方是学生成绩列表。
      * **即时反馈:** 各区块数据加载完成后显示。
2.  **[用户操作2]:** 老师看到“值得表扬”学生中有周雨彤，点击“一键表扬”按钮。
      * **界面变化:** “一键表扬”按钮变为加载中状态，成功后按钮文本变为“已表扬”或禁用，并有Toast提示“表扬消息已发送”。
      * **即时反馈:** 操作成功，系统后台记录表扬行为或发送通知。
3.  **[用户操作3]:** 老师在学生列表中看到学生李子涵的完成进度为60%，正确率为60%，想查看其具体答题情况。点击李子涵所在行的“查看详情”按钮。
      * **界面变化:** 页面跳转至李子涵在该作业下的个体作业报告页面。
      * **即时反馈:** 页面平滑过渡。
4.  **[用户操作4]:** 老师想查找学生“张思远”的情况，在顶部的搜索框输入“张思远”。
      * **界面变化:** 学生列表异步刷新，仅显示姓名为“张思远”的学生信息。
      * **即时反馈:** 列表更新。

**场景细节补充：**

  * **前置条件:** 老师已登录，作业已发布且有学生作答数据。
  * **期望结果:** 老师高效获取了班级整体学情和重点学生信息，并能便捷地进行表扬、提醒或查看学生详情。
  * **异常与边界情况:**
      * 无学生数据：各统计数据显示为0或“-”，列表为空，提示“暂无学生作答数据”。
      * “一键”操作失败：Toast提示操作失败及原因。
      * 搜索无结果：列表为空，提示“未找到该学生”。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (核心功能点本身的流程图/状态图等):**

```mermaid
flowchart TD
    A[进入作业报告详情页（学生报告Tab）] --> B[加载并展示作业基本信息];
    B --> C[加载并展示“值得表扬”学生列表及操作按钮];
    C --> D[加载并展示“需要关注”学生列表及操作按钮];
    D --> E[展示班级平均正确率/用时];
    E --> F[展示学生详细数据列表];
    F --> G{用户操作};
    G -- 点击“一键表扬/提醒” --> H[发送请求，更新UI反馈];
    G -- 输入学生姓名搜索 --> I[筛选学生列表];
    G -- 点击“查看详情” --> J[跳转至学生个体报告页];
    G -- 点击“导出” --> K[执行导出操作];
```

#### **[核心功能点3]: 作业题目整体分析（答题结果Tab）**

**用户价值与场景:** 帮助教师从题目维度分析学生在某次作业中的整体答题表现，快速定位正确率低、错误人数多的共性错题，了解学生在哪些知识点上存在普遍困难，为课堂集中讲解和后续教学调整提供依据。

**功能详述与前端呈现:**

  - **主要交互流程:**
    1.  教师在作业报告详情页，点击切换到“答题结果”Tab。
    2.  页面顶部展示作业的总题数和共性错题数，并说明当前题目列表的排序方式（如按错误人数排序）。
    3.  提供题目关键词搜索框、“只看共性错题”开关和题型筛选器。
    4.  页面下方以列表形式展示每道题目的题干预览、正确率、错误人数、作答人数等信息，并提供“加入”（如加入错题本）和“查看”（题目详情）的操作。
    5.  （可选）侧边栏可展示当前任务下的全部题号面板，用颜色区分不同题目的答题正确率情况，点击题号可快速定位到题目列表中的对应题目。
  - **界面布局与元素:**
      - **顶部统计与筛选区**:
          - **统计信息**: “共XX道题，XX道共性错题 (已按照作答人数排序)”。
          - **搜索框**: “搜索题目关键词”。
          - **筛选控件**: “只看共性错题”开关（toggle）、“全部题型”下拉筛选器。
      - **题目列表区**:
          - **表头/卡片固定信息**: 题号、题干预览区域、统计数据区域（正确率、错误人数、作答人数）、操作区域。
          - **单条题目信息**:
              - **题号**: 清晰展示。
              - **题干预览**: 显示部分题干文字，过长时可有“展开查看更多”功能。 若有图片，按比例缩略展示。
              - **选项预览**: （可选）简略展示选项。
              - **答题统计**: “正确率: XX%”、“XX人错误”、“XX人作答”。
              - **操作按钮**: “加入”、“查看”。按钮默认可用。
      - **（可选）侧边题目面板**:
          - 固定或可展开/收起。
          - 列表展示题号，题号背景色根据正确率高低进行区分（如绿色-高，黄色-中，红色-低）。
          - 点击题号，主列表滚动定位到该题。
  - **用户操作与反馈:**
      - **切换到“答题结果”Tab**: 异步加载并显示题目分析数据。
      - **输入关键词搜索**: 题目列表实时或确认后刷新。
      - **切换“只看共性错题”开关**: 题目列表根据开关状态刷新。
      - **选择题型**: 题目列表根据所选体型刷新。
      - **点击题目“加入”**: 按钮可能显示加载状态，成功后Toast提示“加入成功”或按钮文本变为“已加入”。
      - **点击题目“查看”**: 跳转到该题目的详细分析页面（题目详情页）。
      - **（可选）点击侧边栏题号**: 主题目列表平滑滚动到对应题目位置，并可高亮显示。
  - **数据展示与更新:**
      - 题目列表数据根据筛选条件和排序规则从服务端获取。
      - 共性错题根据预设规则（如答题人数\>10人且正确率\<60%）或用户自定义阈值进行标记。
      - 题目正确率、错误人数、作答人数为统计数据。

**优先级：** P0
**依赖关系：** 核心功能点1 (作业列表)

##### 用户场景与故事 (针对此核心功能点)

###### [场景1标题：老师分析作业答题情况，找出共性错题进行备课]

作为一个 **学科老师**，我希望在查看作业报告时，能够 **快速筛选出班级中错误率高、多数学生都做错的题目（共性错题）**，并了解每道题的具体答题情况，这样我就可以 **为下一堂课准备针对性的讲解内容**。目前的痛点是 **手动统计错题费时费力，且不易发现共性问题**，如果能 **清晰展示题目维度的答题数据并高亮共性错题**，将极大提高我的备课效率。

**前端界面与交互关键步骤：**

1.  **[用户操作1]:** 老师在作业报告详情页，点击“答题结果”Tab。
      * **界面变化:** 页面内容切换到题目分析视图。顶部显示题目总数、共性错题数（如“共28道题，3道共性错题”）和排序方式。下方为题目列表，每条展示题干预览、正确率、错误人数、作答人数和操作按钮。
      * **即时反馈:** 题目列表数据加载完成后显示。
2.  **[用户操作2]:** 老师勾选/打开“只看共性错题”开关。
      * **界面变化:** 题目列表异步刷新，只显示被系统判定为“共性错题”的题目。
      * **即时反馈:** 列表内容更新。
3.  **[用户操作3]:** 老师浏览共性错题列表，发现一道题“A.a f(b) \> b f(a) ...”的正确率只有50%，有2人错误（假设共4人作答）。点击该题的“查看”按钮。
      * **界面变化:** 页面跳转至该题目的详情分析页面，展示题目完整内容、学生作答选项分布、答案解析等。
      * **即时反馈:** 页面平滑过渡。
4.  **[用户操作4]:** 老师回到题目列表，点击另一共性错题的“加入”按钮，打算将其加入错题本。
      * **界面变化:** “加入”按钮变为“处理中...”或显示loading动画，操作成功后按钮变为“已加入”或禁用，并有Toast提示“已成功加入错题本”。
      * **即时反馈:** 系统记录该操作。

**场景细节补充：**

  * **前置条件:** 老师已登录，作业报告已生成，有学生答题数据。
  * **期望结果:** 老师成功找到共性错题，并能查看错题详情或将其加入错题本，为后续教学活动做好准备。
  * **异常与边界情况:**
      * 无共性错题：当勾选“只看共性错题”时，列表为空，提示“暂无共性错题”。
      * 题目信息加载失败：应有错误提示。
      * “加入”操作失败：Toast提示失败原因。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (核心功能点本身的流程图/状态图等):**

```mermaid
flowchart TD
    A[进入作业报告详情页（答题结果Tab）] --> B[加载并展示作业题目统计信息];
    B --> C[展示题目列表（含题干、正确率、错误数、作答数等）];
    C --> D{用户操作};
    D -- 输入关键词搜索 --> E[根据关键词筛选题目列表];
    D -- 切换“只看共性错题” --> F[筛选共性错题列表];
    D -- 选择题型 --> G[根据题型筛选题目列表];
    D -- 点击题目“查看” --> H[跳转至题目详情页];
    D -- 点击题目“加入” --> I[执行加入操作，UI反馈];
    D -- 点击侧边栏题号（可选） --> J[主列表定位到该题];
```

#### **[核心功能点4]: 学生个体作业报告查看（从班级报告进入）**

**用户价值与场景:** 使教师能够深入了解单个学生在某次作业中的详细表现，包括其具体的答题对错情况、与AI助手的互动提问等，解决仅看总体分数无法了解学生具体学习过程和问题所在的痛点，为教师进行精准的个性化辅导和反馈提供全面依据。

**功能详述与前端呈现:**

  - **主要交互流程:**
    1.  教师在班级作业报告的“学生报告”Tab中，点击某个学生所在行的“查看详情”按钮。
    2.  页面跳转至该学生的个体作业报告页。页面顶部展示学生姓名和作业名称。
    3.  展示该生作业的整体信息：如完成进度、总得分/正确率、完成时间、发布/截止时间、课程范围。 可能会有对学生表现的整体评价或标签（如“值得鼓励的进步”），以及“点赞”、“去处理”等快捷互动按钮。
    4.  同样提供“学习报告”、“答题结果”、“学生提问”三个Tab。
    5.  **答题结果Tab（学生视角）**:
          - 显示该生在此作业中的总题数、错题数。
          - 题目列表展示方式与班级报告的“答题结果”Tab类似，但统计数据和对错状态仅针对此单个学生。 题目后显示该生作答结果（✅/❌/半对/\!尚未作答）。
          - 同样提供题目搜索、筛选（如“只看错题”）功能。
    6.  **学生提问Tab（学生视角）**:
          - 展示该生在该作业关联的课程内容中提出的问题列表。
          - 提问列表按课程幻灯片顺序或知识点组织，显示学生提问的内容、时间，以及AI助手或教师的解答。
          - 教师可以查看具体的问答对话详情。
  - **界面布局与元素:**
      - **顶部学生与作业信息区**: 学生头像（可选）、“学生姓名 + 作业名称”。 返回按钮。
      - **学生作业概览区**:
          - 完成进度（如“目前已完成作业进度80%”）。
          - 正确率/得分（如“100%”）。
          - 完成时间、发布时间、截止时间、课程范围。
          - 快捷操作按钮：“点赞”、“去处理”（如处理学生提问）。
      - **Tab切换区**: “学习报告”、“答题结果”（默认或根据来源高亮）、“学生提问”。
      - **答题结果Tab（学生个体）**:
          - **统计信息**: “共XX道题，XX道错题 (已按照已作答排序)”。
          - **题目列表**: 与班级报告类似，但作答状态是该学生的。题目前可加正确/错误/半对图标。
              - **操作**: “查看”（题目详情，包含学生答案和标准答案解析）。
      - **学生提问Tab（学生个体）**:
          - **内容组织**: 可按课程结构（章节/知识点/幻灯片）展示，每个节点旁显示该生的提问数。
          - **提问列表**: 显示学生提问的问题摘要、提问时间。点击可展开查看完整对话（学生提问、AI/教师回复）。
  - **用户操作与反馈:**
      - **点击“查看详情”进入**: 页面加载该学生的作业数据。
      - **切换Tab**: 异步加载对应Tab的内容。
      - **在“答题结果”Tab中点击题目“查看”**: 跳转或弹窗显示题目详情，包含该生的作答情况和标准答案解析。
      - **在“学生提问”Tab中点击提问项**: 展开或跳转到完整的问答对话记录。
  - **数据展示与更新:**
      - 所有数据均为该学生在此次作业中的个体数据。
      - 题目列表、提问列表等均为动态加载。

**优先级：** P0
**依赖关系：** 核心功能点2 (班级整体作业报告)

##### 用户场景与故事 (针对此核心功能点)

###### [场景1标题：老师查看某位“需要关注”学生的作业详情，了解其具体错题和学习疑问]

作为一个 **学科老师**，当我在班级报告中发现李子涵同学是“需要关注”的学生后，我希望能够 **点击进入他的个人作业报告，详细查看他具体错哪些题目，以及他在学习过程中问了什么问题**，这样我才能 **准确判断他的薄弱环节，并给出有针对性的辅导**。目前的痛点是 **只知道他表现不好，但具体问题不清晰**，如果能 **提供他详细的答题记录和提问记录**，对我的个性化辅导至关重要。

**前端界面与交互关键步骤：**

1.  **[用户操作1]:** 老师在“李子涵”的班级作业报告“学生报告”Tab中，点击其“查看详情”按钮。
      * **界面变化:** 页面跳转至“李子涵-3.2.2函数的最值”作业报告页。顶部显示“周雨彤 3.2.2函数的最值”（示例图显示周雨彤，此处应为李子涵）。下方为完成进度80%。Tab包含“答题结果”、“学生提问”。 (此处假设从周雨彤的报告示例图推断学生个人报告结构)
      * **即时反馈:** 页面加载李子涵的个体数据。
2.  **[用户操作2]:** 老师点击“答题结果”Tab。
      * **界面变化:** 显示李子涵的答题统计（如“共28道题，4道错题”）。题目列表展示每道题的题干、李子涵的作答状态（对/错/未作答）及“查看”按钮。
      * **即时反馈:** 列表加载。
3.  **[用户操作3]:** 老师在题目列表中找到一道标记为“❌”的错题，点击“查看”按钮。
      * **界面变化:** 跳转或弹窗显示该题目的完整内容、标准答案、解析，以及李子涵提交的答案。 (参考“题目详情页”的描述)
      * **即时反馈:** 题目详情加载。
4.  **[用户操作4]:** 老师返回学生个体报告，点击“学生提问”Tab。
      * **界面变化:** 页面展示李子涵在此作业关联课程中提出的问题列表，按课程内容结构组织。例如“例题1”下有“1个提问”。
      * **即时反馈:** 提问列表加载。
5.  **[用户操作5]:** 老师点击某个提问项“这道题的B选项为什么不对？”。
      * **界面变化:** 展开显示完整的对话记录，包含李子涵的提问和AI助手/老师的回复。
      * **即时反馈:** 对话记录加载。

**场景细节补充：**

  * **前置条件:** 老师已进入班级作业报告，并定位到需要查看详情的学生。
  * **期望结果:** 老师全面了解了李子涵的错题详情和学习疑问，为后续辅导做好了准备。
  * **异常与边界情况:**
      * 学生未作答任何题目：答题结果Tab列表为空，提示“该学生尚未作答”。
      * 学生无提问：学生提问Tab列表为空，提示“该学生在本次学习中未提问”。

**优先级：** 高
**依赖关系：** 核心功能点2

**Mermaid图示 (核心功能点本身的流程图/状态图等):**

```mermaid
flowchart TD
    A[从班级报告点击学生“查看详情”] --> B[进入学生个体作业报告页];
    B --> C[加载并展示学生作业概览（进度、得分等）];
    C --> D{选择Tab};
    D -- “答题结果”Tab --> E[加载学生答题列表（含对错状态）];
    E --> F{操作题目};
    F -- 点击“查看” --> G[显示题目详情（含学生答案与解析）];
    D -- “学生提问”Tab --> H[加载学生提问列表（按课程内容组织）];
    H --> I{操作提问};
    I -- 点击提问 --> J[展开问答对话详情];
```

### 2.2 辅助功能

  - **[辅助功能点1]:** 预警设置查看，为教师提供查看当前作业预警配置的途径。
      - **功能详述与前端呈现:** 在作业报告详情页（班级或个体）的顶部作业信息区，提供“预警设置”入口。点击后，跳转或弹窗显示预警设置详情，包含作业的发布时间、截止时间、关联课程范围、以及预警判断的完成时间基准。此页面为只读信息展示。
      - **Mermaid图示:**
    <!-- end list -->
    ```mermaid
    flowchart TD
        A[作业报告页面] --点击“预警设置”--> B[预警设置详情页/弹窗];
        B --> C[展示：发布时间];
        B --> D[展示：截止时间];
        B --> E[展示：课程范围];
        B --> F[展示：完成时间基准];
    ```
  - **[辅助功能点2]:** 题目详情查看（从答题结果Tab进入），为教师提供单个题目的详细作答分析。
      - **功能详述与前端呈现:** 在“答题结果”Tab的题目列表中，点击某题的“查看”按钮，进入题目详情页。页面展示题目的基本信息（题干、选项、标签、答案解析）。同时，展示该题的作答统计数据（如选择题选项分布、正答率、平均答题时长）。若从学生个体报告进入，则统计数据仅为该生数据或额外突出该生选项。 页面可提供“大屏讲解”等辅助教学操作。
      - **Mermaid图示:**
    <!-- end list -->
    ```mermaid
    flowchart TD
        A[答题结果Tab题目列表] --点击题目“查看”--> B[题目详情页];
        B --> C[展示：题干、选项、答案、解析];
        B --> D[展示：作答统计（全班或个体）];
        B --> E{操作};
        E -- 大屏讲解 --> F[启动大屏讲解模式];
    ```

### 2.3 非本期功能

  - **[功能点1]:** 用户自定义提醒策略（预警设置的编辑功能），P1。
  - **[功能点2]:** 用户自定义共性错题阈值，P1。
  - **[功能点3]:** 非选择题有分数的详细统计展示（目前仅无分数），未来。
  - **[功能点4]:** 测验报告的大屏模式，未来。

## 3\. 业务流程图

```mermaid
graph TD
    subgraph "教师端作业管理与报告"
        A[教师登录] --> B[进入作业模块];
        B --> C[作业列表展示与筛选];
        C -- 点击作业卡片 --> D[进入班级作业报告详情];
        D -- 切换Tab --> D1[学生报告Tab];
        D1 -- 查看学生个体数据 --> G[学生个体作业报告];
        G -- 切换Tab --> G1[个体-答题结果Tab];
        G1 -- 查看题目详情 --> H[题目详情页（学生作答情况）];
        G -- 切换Tab --> G2[个体-学生提问Tab];
        D -- 切换Tab --> D2[答题结果Tab（班级整体）];
        D2 -- 查看题目详情 --> F[题目详情页（班级作答统计）];
        D -- 切换Tab --> D3[学生提问Tab（班级整体）];
        C -- 布置/编辑/删除作业等操作 --> I[作业管理操作];
    end

    subgraph "学生端 (关联流程)"
        J[学生接收/完成作业] --> K[产生作答数据/提问数据];
    end

    K --> D; %% 学生数据汇入报告
```

## 4\. 性能与安全需求

### 4.1 性能需求

  - **响应时间：**
      - 作业列表加载、筛选、搜索响应时间应在2秒内 (95th percentile)。
      - 作业报告详情页（各Tab）数据加载时间应在3秒内 (95th percentile)。
      - 题目详情页加载时间应在2秒内 (95th percentile)。
      - 页面主要内容加载时间（LCP）应在 2.5 秒内。
      - 首次交互延迟（FID）或交互至下次绘制时间（INP）应小于 100毫秒。
  - **并发用户：** 系统需支持至少100名教师同时在线查看作业报告，核心功能性能不显著衰减。
  - **数据量：**
      - 作业列表需支持流畅加载和展示至少200条作业记录。
      - 班级学生列表需支持流畅加载和展示至少100名学生数据，滚动、筛选等操作响应时间不超过1秒。
      - 题目列表需支持流畅加载和展示至少100道题目数据。
  - **前端性能感知：**
      - **[大量数据加载]**: 学生列表、题目列表应采用分页加载或虚拟滚动技术。
      - **[复杂计算后的展示]**: 班级平均分、正确率等统计数据计算若在前端执行，应避免阻塞UI，可考虑Web Worker；若后端计算，前端等待时应有加载提示。
      - **[实时更新功能]**: 若有实时更新需求（如学生提交作业后报告数据刷新），更新频率与方式需明确，确保用户感知流畅且数据准确。

### 4.2 安全需求

  - **权限控制：**
      - 教师只能查看其任教班级或被授权查看的作业报告。
      - 编辑、删除作业等操作仅限作业布置者本人。
      - 敏感数据（如学生个人信息）的访问应有权限控制和审计。
  - **数据加密：** 学生姓名等敏感信息在前后端传输过程中必须使用 HTTPS。前端避免在本地（如localStorage）存储明文敏感信息。
  - **操作审计：** 对教师“一键表扬”、“一键提醒”、导出报告等关键操作需要记录操作日志，包括操作人、操作时间、IP、操作详情。
  - **输入校验:** 前端需对搜索框等输入进行基础校验（如长度），防止常见的注入风险（如XSS），后端必须进行二次严格校验。

## 5\. 验收标准

### 5.1 功能验收

  - **[核心功能点1：作业列表展示与筛选]：**
      - **用户场景：** 当教师进入作业模块，能够按班级、学科、日期成功筛选并搜索到目标作业。
      - **界面与交互：**
          - 筛选器功能正常，选择后列表按预期刷新。
          - 搜索功能正常，输入关键词后列表按预期展示匹配结果。
          - 作业卡片信息展示完整、准确（作业名称、班级、核心数据等）。
          - 点击作业卡片能正确跳转到对应的作业报告详情页。
      - **期望结果：** 教师能高效找到并访问到所需作业的报告。
  - **[核心功能点2：班级整体作业报告查看（学生报告Tab）]：**
      - **用户场景：** 教师查看某作业报告，能快速了解班级平均表现、重点学生群体，并能查看学生列表。
      - **界面与交互：**
          - “值得表扬”、“需要关注”学生按规则正确展示，相关操作按钮（一键表扬/提醒）功能正常。
          - 班级平均正确率、平均用时等统计数据展示准确。
          - 学生列表数据（姓名、进度、正确率、错题数、用时等）展示准确，排序、搜索、导出功能正常。
          - 点击学生“查看详情”能正确跳转。
      - **期望结果：** 教师能全面掌握班级学情，并能进行初步干预或深入分析。
  - **[核心功能点3：作业题目整体分析（答题结果Tab）]：**
      - **用户场景：** 教师查看作业的题目分析，能快速找到共性错题和各题目的答题情况。
      - **界面与交互：**
          - 总题数、共性错题数统计准确。
          - 题目搜索、“只看共性错题”、题型筛选功能正常。
          - 题目列表展示题干预览、正确率、错误人数、作答人数准确。
          - 题目“加入”、“查看”操作功能正常。
          - （可选）侧边题号面板功能正常。
      - **期望结果：** 教师能有效分析题目难度和学生掌握情况，为教学提供参考。
  - **[核心功能点4：学生个体作业报告查看]：**
      - **用户场景：** 教师查看单个学生的作业报告，能了解其详细的答题情况和提问记录。
      - **界面与交互：**
          - 学生作业概览信息（进度、得分等）准确。
          - “答题结果”Tab展示该生每道题的对错情况准确，题目详情可查看。
          - “学生提问”Tab按课程结构展示该生提问及解答，内容准确，交互流畅。
      - **期望结果：** 教师能深入了解学生个体学习问题，进行精准辅导。

### 5.2 性能验收

  - **响应时间：**
      - **测试用例1:** 模拟普通4G网络，教师打开包含50名学生的班级作业报告（学生报告Tab），页面主要数据在3秒内加载完成。
      - **测试用例2:** 在包含100道题的作业报告“答题结果”Tab中，进行关键词搜索，结果列表在2秒内展示。
  - **并发用户：**
      - **测试用例1:** 使用压测工具模拟100名教师同时访问作业报告模块，执行筛选、查看详情等操作，持续30分钟，系统无5xx错误，核心功能平均响应时间在设定阈值内，CPU/内存使用率在安全阈值内。
  - **前端性能感知验收：**
      - **测试用例1 (大量数据加载):** 对于包含100名学生的班级报告列表，滚动加载流畅，无明显卡顿。
      - **测试用例2 (复杂计算):** 若前端涉及统计计算，确保在普通配置的教师PC或移动设备上，计算过程不导致页面卡顿超过500ms。

### 5.3 安全验收

  - **权限控制：**
      - **测试用例1:** 使用A学科老师账号登录，尝试访问其未任教班级B的作业报告，期望结果：系统拒绝访问，提示无权限。
      - **测试用例2:** 使用班主任账号登录，尝试删除非本人布置的作业，期望结果：无删除按钮或操作失败，提示无权限。
  - **数据加密：**
      - **测试用例1:** 通过浏览器开发者工具查看请求，确认学生姓名等敏感信息在传输过程中已加密（HTTPS）。
      - **测试用例2:** 检查浏览器本地存储，确认没有明文存储学生ID、姓名等敏感信息。
  - **操作审计：**
      - **测试用例1:** 教师执行“一键表扬”操作，期望结果：后端操作日志中正确记录操作人、操作时间、被表扬学生等信息。
  - **输入校验：**
      - **测试用例1 (XSS):** 在作业列表搜索框、题目搜索框输入 `<script>alert('xss')</script>`，期望结果：提交后脚本未执行，页面显示正常或转义字符。
      - **测试用例2 (格式校验):** 若有日期筛选等，输入无效日期格式，期望结果：前端提示错误，无法提交。

## 6\. 其他需求

### 6.1 可用性与体验需求

  - **界面友好与直观性：**
      - 核心操作路径（如查看报告、筛选、定位问题学生/题目）应在3-5步内可完成。
      - 图标、标签、提示信息清晰易懂，符合教师用户通用认知。例如，“共性错题”的定义应有明确说明或鼠标悬浮提示。
      - 整体视觉风格（颜色、字体、布局）在教师端产品内保持一致性。重要操作（如“一键提醒”）有明确的视觉引导。
  - **容错处理：**
      - 当用户进行无效操作（如在日期筛选中输入非法字符）时，应提供清晰、友好的错误提示，并指引用户修正。
      - 当后端接口请求失败或超时，前端应有统一的错误处理机制（如toast提示“网络开小差了，请稍后重试”），避免页面崩溃或白屏。
  - **兼容性：**
      - **浏览器：** 需支持最新版本的 Chrome, Firefox, Safari, Edge 浏览器，保证核心功能和界面显示正常。
      - **响应式布局：**
          - **桌面端 (≥1200px):** 列表、筛选器等元素充分利用宽度，信息密度可较高。
          - **平板端 (≥768px):** 布局调整，保证操作便捷性，如筛选器可能变为抽屉式。
          - **移动端 (\<768px):** 针对小屏幕优化，采用单栏流式布局，导航和筛选器可能收纳于汉堡菜单或底部Tab，操作按钮点击区域符合移动端标准。核心信息（如作业名称、学生姓名、关键数据）优先突出展示。 （根据提供的图片多为移动端设计推断）
  - **可访问性 (A11y)：**
      - 核心功能应考虑键盘可操作性，所有交互元素可通过Tab键聚焦并用Enter/Space键激活。
      - 图片（如图表、学生头像）需提供有意义的 `alt` 文本。
      - 颜色对比度应符合 WCAG AA 级别标准。
      - 重要状态变更（如列表加载完成、操作成功/失败提示）应有ARIA属性支持。
  - **交互一致性：**
      - 产品内相似功能（如不同列表的搜索、筛选、分页）应使用统一的视觉样式和交互模式。
      - “查看详情”、“编辑”、“删除”等常用操作按钮在不同模块间应保持样式和位置的一致性。

### 6.2 维护性需求

  - **配置管理：**
      - “共性错题”的判断阈值（如答题人数、正确率）建议可在后台配置，无需修改代码。
      - “值得表扬”/“需要关注”学生的推荐策略（如连对题数、进步幅度等）的参数建议可在后台配置。
  - **监控告警：** 前端关键JS执行错误、API请求成功率骤降等应能通过监控系统（如Sentry）捕获并告警。
  - **日志管理：** 前端应记录关键用户行为路径（如查看报告、执行筛选、点击重要操作按钮）、重要API请求与响应（脱敏后）、JS错误详情，日志格式应规范，便于排查问题。

## 7\. 用户反馈和迭代计划

### 7.1 用户反馈机制

  - 用户（教师）可以通过产品内置的反馈入口（如“意见反馈”按钮/表单）或指定的沟通渠道（如微信群、邮件）提交产品使用问题、意见和建议。
  - 前端应确保反馈入口易于发现和使用。
  - 定期（如每双周）由产品/运营团队收集和分析用户反馈，识别高频问题和改进机会，纳入后续迭代规划。

### 7.2 迭代计划

  - **迭代周期：** P0核心功能上线后，根据用户反馈和业务需求，初定每2-4周为一个迭代周期。
  - **迭代流程：** 需求收集与分析 -\> 需求评审 -\> UI/UX设计 -\> 开发与联调 -\> 测试 -\> 发布上线 -\> 数据跟踪与用户反馈收集。
  - **优先级调整：** 根据P0上线后的数据表现、用户反馈和学校的实际需求，产品团队会动态调整Backlog中的需求优先级，并规划后续迭代内容。例如P1阶段的自定义提醒策略等。

## 8\. 需求检查清单

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| -------------------------------- | ----------------------------------- | ------ | ------ | ------ | ------------------------- | --------------------------- | ----------- | ---- |
| 1. 作业列表：展示当前老师布置的、有权限看的全部作业 | 2.1 核心功能点1 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 2. 作业报告：按学生报告、答题结果、学生提问tab展示作业报告 | 2.1 核心功能点2, 3, 4 及对应Tab切换 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 3. xx学生的作业报告：按答题结果、学生提问展示作业报告 | 2.1 核心功能点4 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 4. 策略说明：鼓励、关注、共性错题 | 2.1 核心功能点2 (鼓励/关注学生展示), 2.1 核心功能点3 (共性错题统计) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ | 具体策略参数见6.2配置管理 |
| 5. 提醒设置：支持用户自定义提醒策略 | 2.3 非本期功能 | ✅ | ✅ | ✅ | ⚠️ (P1功能，验收标准待细化) | ✅ (P1) | ⚠️ |  |
| 作业列表-数据范围：本班本学科/本班全部学科等，根据职务区分 | 4.2 安全需求 (权限控制) | ✅ | ✅ | ✅ | ✅ (5.3) | ✅ (P0) | ✅ |  |
| 作业列表-筛选：全部类型、查看班级、学科、布置日期 | 2.1 核心功能点1 (功能详述) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 作业列表-搜索：按任务名称模糊匹配 | 2.1 核心功能点1 (功能详述) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 作业列表-卡片指标：根据任务类型展示不同指标 | 2.1 核心功能点1 (界面布局与元素) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 作业列表-卡片操作：编辑、删除、复制（本人布置） | 2.1 核心功能点1 (用户操作与反馈), 4.2 安全需求 | ✅ | ✅ | ✅ | ✅ (5.1, 5.3) | ✅ (P0) | ✅ |  |
| 学生报告Tab-页面名称和返回 | 2.1 核心功能点2 (界面布局与元素) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 学生报告Tab-顶部任务详情和筛选 | 2.1 核心功能点2 (界面布局与元素) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 学生报告Tab-建议鼓励/关注学生展示及操作 | 2.1 核心功能点2 (快捷互动区) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 学生报告Tab-班级进度/正确率展示 | 2.1 核心功能点2 (班级统计数据区) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 学生报告Tab-任务学生列表及排序、操作 | 2.1 核心功能点2 (学生详细数据列表区) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 学生报告Tab-列表中异常数据飘色 | 2.1 核心功能点2 (学生详细数据列表区) | ✅ | ✅ | ✅ | ⚠️ (UI细节，验收时关注) | ✅ (P0) | ✅ |  |
| 答题结果Tab-数据展示逻辑（区分任务类型） | 2.1 核心功能点3 (功能详述) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 答题结果Tab-“共xx道题，xx道共性错题”解释 | 2.1 核心功能点3 (界面布局与元素) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 答题结果Tab-搜索题目关键词 | 2.1 核心功能点3 (界面布局与元素) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 答题结果Tab-题目信息展示（正确率、错人数、作答人数定义） | 2.1 核心功能点3 (界面布局与元素) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 答题结果Tab-侧边题目面板 | 2.1 核心功能点3 (界面布局与元素) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 学生提问Tab-展示任务下有评论的素材 | 2.1 核心功能点4 (学生个体作业报告-学生提问Tab) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 学生提问Tab-按课程幻灯片顺序展示及评论数统计 | 2.1 核心功能点4 (学生个体作业报告-学生提问Tab) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| xx学生的作业报告-来源入口 | 2.1 核心功能点4 (主要交互流程) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| xx学生的作业报告-顶部页面名称 | 2.1 核心功能点4 (界面布局与元素) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| xx学生的作业报告-答题概览（时间、进度、范围） | 2.1 核心功能点4 (界面布局与元素) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| xx学生的作业报告-答题结果Tab（错题数解释、题目信息、面板） | 2.1 核心功能点4 (答题结果Tab个体) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| xx学生的作业报告-学生提问Tab（评论素材、课程内容与评论统计） | 2.1 核心功能点4 (学生提问Tab个体) | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 题目详情页-展示题目基本信息（题干、选项、标签、答案解析） | 2.2 辅助功能点2 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 题目详情页-展示作答统计（区分选择题/非选择题） | 2.2 辅助功能点2 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 策略-鼓励（系统建议/自定义） | 2.1 核心功能点2 (快捷互动区), 6.2 维护性需求 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0/P1) | ✅ |  |
| 策略-关注（系统建议/自定义） | 2.1 核心功能点2 (快捷互动区), 6.2 维护性需求 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0/P1) | ✅ |  |
| 策略-共性错题（定义/自定义） | 2.1 核心功能点3 (数据展示), 6.2 维护性需求 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0/P1) | ✅ |  |

  - **完整性：** 原始需求是否都有对应的优化后需求点，无遗漏。
  - **正确性：** 优化后需求描述是否准确表达了原始需求的意图。
  - **一致性：** 优化后需求之间是否有冲突或重复。
  - **可验证性：** 优化后需求是否有明确的验收标准（对应5. 验收标准）。
  - **可跟踪性：** 优化后需求是否有明确的优先级和依赖关系。
  - **UI/UX明确性：** 优化后需求是否清晰描述了前端界面、交互和用户体验细节。

✅表示通过；❌表示未通过；⚠️表示描述正确，但UI/UX细节、验收标准或图示等仍需与PM进一步沟通完善；N/A表示不适用。每个需求点都需要填写。

## 9\. 附录

### 9.1 原型图/设计稿链接

  - UI图: [https://www.figma.com/design/d7RUo4uZ7uwRpb1Enf0Sng/%E9%93%B6%E6%B2%B3-%E6%95%99%E5%B8%88?node-id=11-4149\&p=f\&t=MkljbBY4IkwHZuo7-0](https://www.google.com/search?q=https://www.figma.com/design/d7RUo4uZ7uwRpb1Enf0Sng/%25E9%2593%25B6%25E6%25B2%25B3-%25E6%2595%2599%25E5%25B8%2588%3Fnode-id%3D11-4149%26p%3Df%26t%3DMkljbBY4IkwHZuo7-0)
  - (原始PRD中包含多张图片截图，已在各功能点描述中通过文字形式体现其UI和交互逻辑)

### 9.2 术语表

  - **P0/P1**: 需求优先级，P0为最高优先级。
  - **共性错题**: 根据特定规则（如答题人数\>10人，任务中正确率＜60%）筛选出的错误率较高的题目。
  - **学生分层**: 根据学生学习表现等数据，对学生进行的分类（如“有进步”、“需关注”、“C层”等）。
  - **学习分**: 一种衡量学生学习效果的量化指标（具体计算方式见学生端策略）。

### 9.3 参考资料

  - 原始需求文档：`[documents/raw-docs/教师端_1期_作业报告_analyzed.md]`
  - 关联需求：[教师端\_1期\_All-in-one](https://www.google.com/search?q=https://wcng60ba718p.feishu.cn/wiki/A3F1wlepNiF5ibkI2NAcf4K6nQc)
  - 口径说明：[https://wcng60ba718p.feishu.cn/wiki/DXKawJOESiypQ3kt3vbc64DgnTc?sheet=e3d974](https://www.google.com/search?q=https://wcng60ba718p.feishu.cn/wiki/DXKawJOESiypQ3kt3vbc64DgnTc%3Fsheet%3De3d974)
  - 教师端数据埋点汇总：[https://wcng60ba718p.feishu.cn/wiki/Pw1GwQgfEi7TsPk3dgrcysMDnR0?sheet=ceLy7M](https://www.google.com/search?q=https://wcng60ba718p.feishu.cn/wiki/Pw1GwQgfEi7TsPk3dgrcysMDnR0%3Fsheet%3DceLy7M)

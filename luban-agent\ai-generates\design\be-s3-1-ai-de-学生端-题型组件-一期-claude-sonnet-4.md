# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** 学生端题型组件一期
- **PRD版本号：** 一期
- **提取日期：** 2025-05-30

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 答题记录 | 学生在各种题型中的作答信息，包括选择、填空、解答等 | 2.1, 4.场景1-5 |
| 题型组件状态 | 题型组件的当前状态，如初始状态、作答状态、解析状态 | 5.业务流程图 |
| 计时记录 | 每题独立的计时信息，支持暂停和继续 | 3.1, 2.2 |
| 自评记录 | 学生对填空题和解答题的自我评价结果 | 3.2, 4.场景4-5 |
| 勾画数据 | 学生在题目上进行的标记和勾画内容 | 2.2, 4.场景6 |
| 选项统计 | 单选题和多选题的选项选择统计数据 | 3.3 |
| 异常行为记录 | 自评过程中的异常行为监控数据 | 3.2 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`答题记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 答题记录ID | 答题记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 推断 |
| 学生ID | 作答学生的标识符，关联用户中心 | 整数 | 非空，外键 |  | 外键 | 4.场景1-5 |
| 题目ID | 题目的标识符，关联内容平台 | 整数/字符串 | 非空，外键 |  | 外键 | 4.场景1-5 |
| 题型类型 | 题目类型：单选题、多选题、填空题、解答题 | 枚举/整数 | 取值范围：1-单选，2-多选，3-填空，4-解答 |  |  | 2.1 |
| 学科类型 | 题目所属学科，影响批改方式 | 整数 | 外键关联学科表 |  | 外键 | 2.1 |
| 答案内容 | 学生的作答内容 | JSON/文本 | 根据题型不同格式不同 |  |  | 4.场景1-5 |
| 批改结果 | 自动批改或自评的结果 | 枚举 | 正确/错误/部分正确 |  |  | 4.场景1-5 |
| 是否二次作答 | 标记是否进行了二次作答 | 布尔值 | true/false | false |  | 2.1 |
| 提交时间 | 答案提交的时间戳 | 时间戳 | 非空 |  |  | 4.场景1-5 |

**实体：`题型组件状态`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 状态记录ID | 状态记录的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 推断 |
| 学生ID | 学生标识符 | 整数 | 非空，外键 |  | 外键 | 5.业务流程图 |
| 题目ID | 题目标识符 | 整数/字符串 | 非空，外键 |  | 外键 | 5.业务流程图 |
| 当前状态 | 组件当前状态 | 枚举 | 初始状态/作答状态/解析状态 | 初始状态 |  | 5.业务流程图 |
| 进入时间 | 进入当前状态的时间 | 时间戳 | 非空 |  |  | 5.业务流程图 |

**实体：`计时记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 计时记录ID | 计时记录的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 推断 |
| 学生ID | 学生标识符 | 整数 | 非空，外键 |  | 外键 | 3.1 |
| 题目ID | 题目标识符 | 整数/字符串 | 非空，外键 |  | 外键 | 3.1 |
| 开始时间 | 用户进入题目时开始计时 | 时间戳 | 非空 |  |  | 3.1 |
| 结束时间 | 用户提交答案时停止计时 | 时间戳 | 可为空（未完成时） |  |  | 3.1 |
| 累计时长 | 累计答题时长（秒） | 整数 | 0-3599（上限59:59） | 0 |  | 3.1 |
| 是否暂停 | 当前是否处于暂停状态 | 布尔值 | true/false | false |  | 3.1 |

**实体：`自评记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 自评记录ID | 自评记录的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 推断 |
| 答题记录ID | 关联的答题记录 | 整数 | 非空，外键 |  | 外键 | 4.场景4-5 |
| 自评结果 | 学生的自评选择 | 枚举 | 我答对了/部分答对/我答错了 |  |  | 4.场景4-5 |
| 自评时长 | 自评操作耗时（秒） | 整数 | 大于0 |  |  | 3.2 |
| 自评时间 | 自评操作的时间戳 | 时间戳 | 非空 |  |  | 4.场景4-5 |

**实体：`勾画数据`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 勾画数据ID | 勾画数据的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 推断 |
| 学生ID | 学生标识符 | 整数 | 非空，外键 |  | 外键 | 4.场景6 |
| 题目ID | 题目标识符 | 整数/字符串 | 非空，外键 |  | 外键 | 4.场景6 |
| 勾画内容 | 勾画的具体数据（坐标、颜色、粗细等） | JSON | 包含画笔轨迹、颜色、粗细信息 |  |  | 4.场景6 |
| 工具类型 | 使用的勾画工具 | 枚举 | 画笔/橡皮擦 |  |  | 2.2, 4.场景6 |
| 颜色 | 勾画颜色 | 字符串 | 蓝色/红色/黑色 | 蓝色 |  | 4.场景6 |
| 粗细 | 勾画粗细 | 枚举 | 细/中等/粗 | 中等 |  | 4.场景6 |
| 创建时间 | 勾画创建时间 | 时间戳 | 非空 |  |  | 4.场景6 |

**实体：`选项统计`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 统计记录ID | 统计记录的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 推断 |
| 题目ID | 题目标识符 | 整数/字符串 | 非空，外键 |  | 外键 | 3.3 |
| 选项标识 | 选项的标识（A、B、C、D等） | 字符串 | 非空 |  |  | 3.3 |
| 选择次数 | 该选项被选择的次数 | 整数 | 大于等于0 | 0 |  | 3.3 |
| 总使用次数 | 题目总的使用次数 | 整数 | 大于等于0 | 0 |  | 3.3 |
| 选择占比 | 选项选择占比 | 小数 | 0-100%，保留一位小数 |  |  | 3.3 |
| 更新时间 | 统计数据更新时间 | 时间戳 | 非空 |  |  | 3.3 |

**实体：`异常行为记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 异常记录ID | 异常记录的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 推断 |
| 学生ID | 学生标识符 | 整数 | 非空，外键 |  | 外键 | 3.2 |
| 异常类型 | 异常行为类型 | 枚举 | 连续快速自评 |  |  | 3.2 |
| 触发时间 | 异常触发的时间戳 | 时间戳 | 非空 |  |  | 3.2 |
| 连续题目数 | 连续异常的题目数量 | 整数 | 大于等于3 |  |  | 3.2 |
| 平均时长 | 连续题目的平均自评时长 | 小数 | 小于2秒触发 |  |  | 3.2 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 (例如：用户 '拥有一个' 学校档案) | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 学生产生多条答题记录 | 学生（用户中心） | 答题记录 | 1:N | 答题记录.学生ID -> 学生.ID | 4.场景1-5 |
| 题目对应多条答题记录 | 题目（内容平台） | 答题记录 | 1:N | 答题记录.题目ID -> 题目.ID | 4.场景1-5 |
| 答题记录对应一条计时记录 | 答题记录 | 计时记录 | 1:1 | 计时记录.答题记录ID -> 答题记录.ID | 3.1 |
| 答题记录可能有自评记录 | 答题记录 | 自评记录 | 1:0..1 | 自评记录.答题记录ID -> 答题记录.ID | 4.场景4-5 |
| 学生在题目上产生勾画数据 | 学生（用户中心） | 勾画数据 | 1:N | 勾画数据.学生ID -> 学生.ID | 4.场景6 |
| 题目对应勾画数据 | 题目（内容平台） | 勾画数据 | 1:N | 勾画数据.题目ID -> 题目.ID | 4.场景6 |
| 题目有选项统计 | 题目（内容平台） | 选项统计 | 1:N | 选项统计.题目ID -> 题目.ID | 3.3 |
| 学生产生异常行为记录 | 学生（用户中心） | 异常行为记录 | 1:N | 异常行为记录.学生ID -> 学生.ID | 3.2 |
| 学生在题目上有组件状态 | 学生（用户中心） | 题型组件状态 | 1:N | 题型组件状态.学生ID -> 学生.ID | 5.业务流程图 |
| 题目对应组件状态 | 题目（内容平台） | 题型组件状态 | 1:N | 题型组件状态.题目ID -> 题目.ID | 5.业务流程图 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 计时公式（每题独立正计时，格式为MM:SS，上限59:59） | 开始时间、结束时间、暂停状态、累计时长 | 显示时长（MM:SS格式） | 3.1 |
| 自评异常监控（连续3题批改时长均小于2秒时触发提示） | 连续题目的自评时长、自评时间、学生ID | 异常提示触发标志 | 3.2 |
| 选项占比计算（题目使用次数大于20后展示选项占比） | 题目总使用次数、各选项选择次数 | 选项占比百分比 | 3.3 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 计时记录.开始时间 | 用户进入题目时开始计时 | 每次进入题目 | 3.1 |
| 计时记录.结束时间 | 用户提交答案时停止计时 | 每次提交答案 | 3.1 |
| 计时记录.是否暂停 | 用户未作答退出时暂停计时，下次进入继续计时 | 每次退出/进入 | 3.1 |
| 答题记录.答案内容 | 学生选择选项、输入答案、上传图片等作答行为 | 每次作答 | 4.场景1-5 |
| 答题记录.批改结果 | 自动批改服务返回结果或学生完成自评 | 每次批改/自评 | 4.场景1-5 |
| 自评记录.自评结果 | 学生进行自我评价选择 | 每次自评 | 4.场景4-5 |
| 异常行为记录 | 连续3题批改时长均小于2秒时触发 | 检测到异常时 | 3.2 |
| 选项统计.选择次数 | 学生选择选项时 | 每次选择 | 3.3 |
| 选项统计.选择占比 | 题目使用次数大于20后 | 每日更新 | 3.3 |
| 勾画数据 | 学生使用勾画工具进行标记 | 每次勾画操作 | 4.场景6 |
| 题型组件状态.当前状态 | 学生在答题流程中状态变化 | 每次状态转换 | 5.业务流程图 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 学生ID | 用户中心提供，学生登录认证后获取 | 4.场景1-5 |
| 题目ID | 内容平台提供，包括题目信息、难度、年份、地区等 | 4.场景1-5 |
| 学科类型 | 内容平台提供，题目所属学科信息 | 2.1 |
| 答案内容 | 学生通过界面输入（选择、键盘输入、拍照上传） | 4.场景1-5 |
| 勾画内容 | 学生通过勾画工具在界面上操作生成 | 4.场景6 |
| 标准答案 | 内容平台提供，用于自评对比 | 4.场景4-5 |
| 题目解析 | 内容平台提供，用于查看解析 | 4.场景1-5 |

---

## 7. 当前版本明确排除的数据需求 (例如PRD中的V1.0版本)

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 (源自PRD"非本期功能"等章节) | PRD中说明的排除原因 (若有) | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 母子题支持的复杂数据结构 | 复杂的母子题结构，建议二期再考虑 | 2.3 |
| 特殊题型的数据模型（完形填空、阅读理解、七选五等） | 特殊题型，建议三期再考虑 | 2.3 |
| 高级批改算法的相关数据存储 | 更复杂的自动批改算法，建议后续迭代优化 | 2.3 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别答题记录、计时记录、自评记录、勾画数据、选项统计等核心实体 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏（例如，本示例中的"题目信息"、"课程信息"）？                                                         | ✅ | 题目信息和学生信息通过公共服务提供，已在关系中体现 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已提取计时规则、自评规则、选项统计规则等相关属性 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | 属性定义与PRD描述一致 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确学生、题目与各记录实体的关系 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 已体现公共服务依赖关系 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 已列出计时、自评监控、选项统计的计算输入 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 已记录各种触发条件和数据来源 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 已列出母子题、特殊题型等排除项 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 与PRD术语保持一致 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 所有提取项都标注了PRD章节号 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 所有信息基于PRD原文，推断部分已标注 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 部分实体ID的具体数据类型需与技术架构确认 |

---

--- 等待您的命令，指挥官
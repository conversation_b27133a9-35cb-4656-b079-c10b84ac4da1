---
description: 
globs: backend/tests/domain/entities/**/*.py,backend/tests/domain/value_objects/**/*.py
alwaysApply: false
---
# Domain 层测试代码生成规则

**核心原则：简洁优先**

## 边界
- 纯 Python：不接触数据库、框架或网络  
- 聚焦业务不变量、值对象不可变性

## 约定
- 类名 `Test{Entity|VO}`；方法 `test_{规则或行为}`  
- 使用 `pytest.mark.parametrize` 覆盖边界值  
- 覆盖率目标 ≥ 90 %

## 示例片段
```python
"""
用户实体单元测试

测试用户实体的行为和方法，确保符合用户故事需求。
"""
class TestUsername:
    """用户名值对象测试"""
    
    def test_creation(self):
        """测试创建有效用户名"""
        pass
        
    def test_equality(self):
        """测试相等性"""
        pass
    
    def test_immutability(self):
        """测试不变性"""
        pass
    
    def test_serialization(self):
        """测试序列化与反序列化"""
        username = Username("serializable")
        
        # 序列化为持久化格式
        # 从持久化格式反序列化
        
    @pytest.mark.parametrize("invalid_name", ["", "a", "a" * 51])
    def test_validation(self, invalid_name):
        """测试验证规则"""
        with pytest.raises(ValidationError):
            Username(invalid_name)

class TestUser:
    """用户实体测试"""
    
    @pytest.fixture
    def test_user(self):
        """创建测试用户"""
        pass
    
    def test_activation(self, test_user):
        """测试用户激活"""
        # 确保用户处于非激活状态
        # 激活用户        
        # 验证状态和事件
        pass
        
class TestOrder:
    """订单聚合测试"""
    
    def test_add_item(self):
        """测试添加订单项"""
        # 创建订单        
        # 添加订单项        
        # 验证状态和事件
        pass
        
    def test_complex_aggregate_consistency(self):
        """测试复杂聚合内部一致性"""
        # 创建复杂订单
        order = create_test_order(
            customer_id=100, 
            shipping_address=Address(country="US", city="New York"),
            items=[
                {"product_id": 1, "quantity": 2, "unit_price": Money(100, "USD")},
                {"product_id": 2, "quantity": 1, "unit_price": Money(50, "USD")}
            ]
        )
        
        # 验证初始一致性
        # 执行取消操作        
        # 验证一致性不变量
        # 验证业务规则：已取消订单不能再修改
        
```


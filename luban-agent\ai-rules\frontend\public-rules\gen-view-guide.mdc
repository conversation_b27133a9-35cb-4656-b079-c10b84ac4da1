---
description: 
globs: 
alwaysApply: false
---
# View 层代码编写指南

## 📖 如何使用此指南

### 开发流程
1. **基于UI稿开发** - 直接参照UI设计稿，无需等待ViewModel实现
2. **Mock外部状态** - 使用规范化Mock确保组件可独立测试
3. **遵循状态边界** - 按照边界规则管理组件内部状态
4. **避免过度拆分** - 遵循"三次规则"，延迟组件拆分
5. **标识Mock数据** - 使用标准标识，便于后续ViewModel对接

### 检查要点
- ✅ 是否只处理UI相关逻辑？
- ❌ 是否绝对避免了直接调用API？
- ✅ 状态管理是否符合边界规则？
- ✅ Mock数据是否正确标识？

## 🎯 核心原则

1. **🚫 绝对禁止直接调用API** - View层与API隔离，所有数据通过ViewModel获取
2. **🎨 强制使用TailwindCSS** - 所有样式必须使用Tailwind类名，禁止内联样式和CSS模块
3. **📁 统一文件命名** - 所有文件使用kebab-case命名规范
4. **🧠 直觉优于教条** - 基于开发直觉的合理状态管理
5. **📏 状态边界清晰** - 布局样式状态可保留，业务状态移到ViewModel
6. **⏳ 延迟拆分** - 需要时再拆分组件，避免过度工程化
7. **🧪 独立可测** - 通过Mock确保组件可独立开发和测试
8. **🔄 事件委托** - 将业务处理委托给ViewModel层
9. **⚡ 性能优先** - 合理使用React优化API，避免不必要的重渲染

## 📁 文件组织与命名规范

### 文件命名规范
- [component-name]: 结合实际的 UI内容来定义 component-name
- [view-name]: 结合实际的 UI内容来定义 view-name
- **统一使用 kebab-case**：所有文件名使用小写字母和连字符
- 组件文件：`[component-name].tsx`、`[component-name].stories.tsx`、`[view-name]-view.tsx`、`[view-name]-view.stories.tsx`、`README.md`

### 标准目录结构
```
app/components/          # 纯 UI 公共组件，无业务逻辑
├── README.md
├── common/             # 按钮、输入框、弹窗等
│   ├── button/
│   │   ├── button.tsx
│   │   ├── button.stories.tsx
│   │   └── README.md
│   ├── input/
│   │   ├── input.tsx
│   │   ├── input.stories.tsx
│   │   └── README.md
│   └── modal/
│       ├── modal.tsx
│       ├── modal.stories.tsx
│       └── README.md
└── form/               # 表单基础组件
    ├── form-field/
    │   ├── form-field.tsx
    │   ├── form-field.stories.tsx
    │   └── README.md
    └── validation-message/
        ├── validation-message.tsx
        ├── validation-message.stories.tsx
        └── README.md

app/views/              # 业务 View 组件
├── README.md           # 所有组件聚合索引
├── common/             # 通用业务组件（基于 app/components）
│   ├── button/
│   │   ├── loading-button.tsx
│   │   ├── loading-button.stories.tsx
│   │   └── README.md
│   └── form/
│       ├── input-field.tsx
│       ├── input-field.stories.tsx
│       └── README.md
├── business/           # 具体业务组件（用户、课程、练习）
│   ├── user/
│   │   ├── user-card.tsx
│   │   ├── user-card.stories.tsx
│   │   └── README.md
│   ├── course/
│   │   ├── course-card.tsx
│   │   ├── course-card.stories.tsx
│   │   └── README.md
│   └── exercise/
│       ├── choice-question-view.tsx
│       ├── choice-question-view.stories.tsx
│       ├── fill-blank-view.tsx
│       ├── fill-blank-view.stories.tsx
│       └── README.md
└── layout/             # 布局组件
    ├── header/
    │   ├── header.tsx
    │   ├── header.stories.tsx
    │   └── README.md
    └── sidebar/
        ├── sidebar.tsx
        ├── sidebar.stories.tsx
        └── README.md
```

### 目录结构规范
- **分层清晰** - `app/components` 纯UI组件，`app/views` 业务组件
- **模块化组织** - 每个组件独立目录，包含组件、Story、README
- **必需文件** - 每个组件必须包含 `.tsx`、`.stories.tsx`、`README.md`
- **统一命名** - 使用kebab-case命名目录和文件
- **文档完整** - 每个层级都需要README文档和Storybook测试

### Props接口定义规范
```typescript
// ✅ Props接口直接定义在组件文件顶部，可导出使用
export interface ChoiceQuestionViewProps {
  // 必需属性
  questionData: QuestionData;
  onSubmit: (answer: Answer) => void;

  // 可选属性
  className?: string;
  disabled?: boolean;
}

export const ChoiceQuestionView: React.FC<ChoiceQuestionViewProps> = ({
  questionData,
  onSubmit,
  className,
  disabled = false
}) => {
  // 组件实现
};
```

## 🎨 样式规范

### 强制使用 TailwindCSS
- **必须使用 TailwindCSS** - 所有样式都通过 Tailwind 类名实现
- **禁止内联 style** - 即使UI稿中使用style属性，也必须转换为Tailwind类名
- **禁止CSS模块** - 不使用单独的CSS文件，所有样式通过className实现

```typescript
// ❌ 错误：使用内联样式
<div style={{ backgroundColor: '#f3f4f6', padding: '16px' }}>

// ❌ 错误：使用CSS模块
<div className={styles.container}>

// ✅ 正确：使用TailwindCSS
<div className="bg-gray-100 p-4">
```

### UI稿样式转换规范
```typescript
// UI稿中的样式 → TailwindCSS转换示例

// 背景色：background-color: #3b82f6 → bg-blue-500
// 内边距：padding: 16px → p-4
// 外边距：margin: 8px 16px → mx-4 my-2
// 圆角：border-radius: 8px → rounded-lg
// 阴影：box-shadow: 0 4px 6px rgba(0,0,0,0.1) → shadow-md
// 字体大小：font-size: 14px → text-sm
// 字体粗细：font-weight: 600 → font-semibold
// 颜色：color: #374151 → text-gray-700

// ✅ 完整示例
const Button = ({ children, variant = 'primary' }) => (
  <button className={`
    px-4 py-2 rounded-lg font-medium transition-all
    ${variant === 'primary'
      ? 'bg-blue-500 text-white hover:bg-blue-600'
      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
    }
  `}>
    {children}
  </button>
);
```

## 🎯 核心职责与禁止行为

### ✅ View层职责
| 职责 | 说明 | 示例 |
|------|------|------|
| **UI 渲染** | 声明式渲染界面元素 | JSX 组件、TailwindCSS样式 |
| **用户交互** | 捕获用户输入和操作 | 点击、输入、拖拽 |
| **状态转发** | 将用户操作转发给上层 | 调用 ViewModel 方法 |
| **UI 状态管理** | 管理纯UI相关的瞬时状态 | 动画、悬停、展开状态 |

### ❌ 严格禁止
| 禁止行为 | 说明 | 原因 |
|---------|------|------|
| **直接调用API** | 任何形式的网络请求 | 违反MVVM架构分层原则 |
| **直接访问Model层** | 跳过ViewModel直接操作数据 | 破坏数据流向和职责分离 |
| **业务逻辑处理** | 复杂的业务规则和计算 | 应由ViewModel层处理 |
| **使用内联样式** | style属性或CSS模块 | 必须使用TailwindCSS |

## 📏 状态管理边界规则

### ✅ 允许在View层的状态

#### 1. 布局和样式控制状态
```typescript
// ✅ 基于数据特征的布局控制
const [hasImages, setHasImages] = useState(false);
useEffect(() => {
  setHasImages(options.some(option => !!option.imageUrl));
}, [options]);

return (
  <div className={`${hasImages ? 'grid grid-cols-2 gap-3' : 'space-y-4'}`}>
    {/* 渲染选项 */}
  </div>
);
```

#### 2. 纯UI交互状态
```typescript
// ✅ 悬停、展开、动画状态
const [isExpanded, setIsExpanded] = useState(false);
const [isHovered, setIsHovered] = useState(false);

return (
  <button
    onClick={() => setIsExpanded(!isExpanded)}
    onMouseEnter={() => setIsHovered(true)}
    onMouseLeave={() => setIsHovered(false)}
    className={`transition-colors ${isHovered ? 'bg-gray-50' : 'bg-white'}`}
  >
    {title} {isExpanded ? '▼' : '▶'}
  </button>
);
```

#### 3. 临时表单输入状态
```typescript
// ✅ 表单提交前的临时输入
const [draftComment, setDraftComment] = useState('');

const handleSubmit = () => {
  if (draftComment.trim()) {
    onSubmit(draftComment); // 委托给ViewModel
    setDraftComment('');
  }
};

return (
  <div>
    <textarea
      value={draftComment}
      onChange={(e) => setDraftComment(e.target.value)}
      className="w-full px-3 py-2 border rounded-lg"
    />
    <button onClick={handleSubmit} disabled={!draftComment.trim()}>
      发布
    </button>
  </div>
);
```

#### 4. 临时UI控制状态
```typescript
// ✅ 弹窗、预览、菜单状态
const [previewImage, setPreviewImage] = useState<string | null>(null);
const [showTooltip, setShowTooltip] = useState(false);

return (
  <div>
    <img onClick={() => setPreviewImage(image.url)} />
    {previewImage && (
      <div className="fixed inset-0 bg-black bg-opacity-75" onClick={() => setPreviewImage(null)}>
        <img src={previewImage} alt="预览" />
      </div>
    )}
  </div>
);
```

### ❌ 禁止在View层的状态

#### 1. 业务数据状态
```typescript
// ❌ 用户信息 → 应在 ViewModel/Context
const UserProfile = () => {
  const [userInfo, setUserInfo] = useState(null); // ❌ 错误

  // ✅ 正确做法：从 ViewModel 获取
  const { userInfo } = useUserViewModel();

  return <div>{userInfo?.name}</div>;
};
```

#### 2. 请求状态和API调用
```typescript
// ❌ 加载状态 → 应在 ViewModel
const DataComponent = () => {
  const [isLoading, setIsLoading] = useState(false); // ❌ 错误

  // ❌ 绝对禁止：View层直接调用API
  useEffect(() => {
    fetch('/api/data').then(/* ... */); // ❌ 严重错误
  }, []);

  // ✅ 正确做法：从 ViewModel 获取
  const { data, isLoading, error } = useDataViewModel();

  if (isLoading) return <div>加载中...</div>;
  return <div>{/* 渲染数据 */}</div>;
};
```

#### 3. 业务逻辑相关状态
```typescript
// ❌ 用户选择、答题状态 → 应在 ViewModel
const QuestionDisplay = () => {
  const [selectedOptions, setSelectedOptions] = useState([]); // ❌ 错误
  const [isAnswerComplete, setIsAnswerComplete] = useState(false); // ❌ 错误

  // ✅ 正确做法：从 ViewModel 获取
  const { selectedOptions, isAnswerComplete } = useChoiceQuestionViewModel();

  return <div>{/* 渲染题目 */}</div>;
};
```

### 🎯 状态边界判断规则

#### 快速判断决策树
```
状态变量 → 是否影响业务逻辑？
    ├─ 是 → 移到 ViewModel
    └─ 否 → 是否只影响视觉呈现？
        ├─ 是 → 保留在 View 层
        └─ 否 → 重新评估必要性
```

#### 判断原则
1. **"移到ViewModel会让代码更复杂"** → 保留在View层
2. **"状态只影响视觉呈现"** → 保留在View层
3. **"状态需要在多个组件间共享"** → 移到ViewModel
4. **"状态影响业务流程"** → 移到ViewModel

## 🎭 View层独立开发与Mock规范

### Mock标识规范
- `TODO: [MOCK-VIEWMODEL]` - ViewModel状态和方法
- `TODO: [MOCK-CONTEXT]` - Context状态和方法
- `TODO: [MOCK-DATA]` - 静态数据

### 基本使用模式
```typescript
const ChoiceQuestionView = () => {
  // ==================== MOCK DATA START ====================
  // TODO: [MOCK-VIEWMODEL] 替换为真实的 useChoiceQuestionViewModel()
  // const { selectedOptions, selectOption } = useChoiceQuestionViewModel();
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const selectOption = (optionId: string) => { /* Mock逻辑 */ };

  // TODO: [MOCK-CONTEXT] 替换为真实的 useExerciseSessionContext()
  // const { questionState } = useExerciseSessionContext();
  const questionState = 'answering';

  // TODO: [MOCK-DATA] 替换为真实数据
  const mockData = { /* Mock数据 */ };
  // ==================== MOCK DATA END ====================

  return <div>{/* UI渲染 */}</div>;
};
```

### 清理检查
```bash
# 搜索所有 MOCK 标记
grep -r "TODO.*\[MOCK" src/
```

## 🧩 组件拆分规范

### 核心原则：延迟拆分，避免过度工程化

#### 拆分决策树
```
需要拆分组件？
├─ 是否在3个以上地方使用？ → 是：拆分 ✅
├─ 是否有独立的业务逻辑？ → 是：拆分 ✅
├─ 当前组件是否超过200行？ → 是：考虑拆分
└─ 否则：不拆分 ❌
```

### 拆分指导原则
1. **延迟拆分** - 需要时再拆，不要预先拆分
2. **三次规则** - 第三次使用时才考虑抽取
3. **实用主义** - 以解决实际问题为目标
4. **可读性优先** - 保持代码清晰比架构完美更重要

### 典型场景
```typescript
// ❌ 过度拆分：只在一处使用
const ChoiceOptionItem = ({ option, onClick }) => (
  <button onClick={() => onClick(option.id)}>{option.content}</button>
);

// ✅ 合理做法：保持在一个组件内
const ChoiceOptionsList = ({ options }) => (
  <div>
    {options.map(option => (
      <button key={option.id} onClick={() => handleClick(option.id)}>
        {option.content}
      </button>
    ))}
  </div>
);

// ✅ 合理拆分：真正的复用需求
const Button = ({ variant, children, onClick }) => (
  <button className={`btn btn-${variant}`} onClick={onClick}>
    {children}
  </button>
);
```

## ⚡ 性能优化规范

### 强制使用项目既有方案
- **虚拟滚动** - 使用`@tanstack/react-virtual`
- **防抖节流** - 使用`ahooks`的`useDebounce`、`useDebounceFn`
- **组件懒加载** - 使用Next.js `dynamic`
- **图片懒加载** - 使用`IntersectionObserver`

### 基本使用指南

#### 1. 虚拟滚动
```typescript
import { useVirtualizer } from '@tanstack/react-virtual';

const virtualizer = useVirtualizer({
  count: items.length,
  getScrollElement: () => containerRef.current,
  estimateSize: () => 120,  // 根据实际内容调整
  overscan: 5,              // 预渲染数量
});
```

#### 2. 防抖处理
```typescript
import { useDebounce, useDebounceFn } from 'ahooks';

// 防抖值
const debouncedValue = useDebounce(inputValue, { wait: 300 });

// 防抖函数
const { run: debouncedFn } = useDebounceFn(callback, { wait: 300 });
```


```

#### 4. 组件懒加载
```typescript
import dynamic from 'next/dynamic';

const LazyComponent = dynamic(() => import('./component'), {
  loading: () => <div>加载中...</div>,
  ssr: false
});
```

### React性能优化

#### 1. 组件记忆化
```typescript
// ✅ 对纯展示组件使用React.memo
export const ChoiceOptionItem = React.memo<ChoiceOptionItemProps>(({
  option, isSelected, onSelect
}) => (
  <button onClick={() => onSelect(option.id)}>
    {option.text}
  </button>
));
```

#### 2. 计算值缓存
```typescript
// ✅ 使用useMemo缓存复杂计算
const statistics = useMemo(() => {
  return questions.reduce(/* 复杂计算逻辑 */);
}, [questions, answers]);
```

#### 3. 回调函数优化
```typescript
// ✅ 使用useCallback缓存事件处理函数
const handleSelect = useCallback((id: string) => {
  onSelect(id);
}, [onSelect]);
```

### 性能优化最佳实践

#### 1. 优先使用项目既有方案
```typescript
// ✅ 推荐：使用项目中已有的性能优化方案
import { useVirtualizer } from '@tanstack/react-virtual';        // 虚拟滚动
import { useDebounce, useDebounceFn } from 'ahooks';             // 防抖节流
import dynamic from 'next/dynamic';                             // 懒加载

// ❌ 避免：重新实现已有的功能
```

#### 2. 参考项目中的实际使用
- **任务列表虚拟滚动** - 参考 `apps/tch/app/homework/_components/task-list.tsx`
- **搜索输入防抖** - 参考 `apps/tch/ui/searchInput.tsx`
- **设置防抖保存** - 参考 `apps/tch/app/homework/[id]/_context/homework-settings-context.tsx`
- **数据表格虚拟滚动** - 参考 `apps/tch/app/homework/_components/DataTable/index.tsx`





## 📚 文档化规范 (README.md)

### View层 README 位置
- `app/components/README.md` - 纯UI组件库文档索引
- `app/views/README.md` - 业务View组件文档索引
- `app/components/[category]/[component]/README.md` - 纯UI组件文档
- `app/views/[category]/[component]/README.md` - 业务组件文档

### 索引级 README 内容规范

#### app/components/README.md
```markdown
# 纯UI组件库

本目录包含无业务逻辑的纯UI组件，可在任何地方复用。

## 组件分类

### 基础组件 (common/)
- `Button` - 通用按钮组件
- `Input` - 输入框组件
- `Modal` - 弹窗组件

### 表单组件 (form/)
- `FormField` - 表单字段组件
- `ValidationMessage` - 验证消息组件

## 使用原则

- 纯UI组件，不包含业务逻辑
- 使用TailwindCSS样式
- 每个组件都有Storybook测试
- 遵循设计系统规范
```

#### app/views/README.md
```markdown
# 业务View组件库

本目录包含业务相关的View组件，基于app/components构建。

## 组件分类

### 通用业务组件 (common/)
- `LoadingButton` - 带加载状态的按钮
- `InputField` - 业务表单输入字段

### 具体业务组件 (business/)
- `UserCard` - 用户卡片组件
- `CourseCard` - 课程卡片组件
- `ChoiceQuestionView` - 选择题组件

### 布局组件 (layout/)
- `Header` - 页面头部组件
- `Sidebar` - 侧边栏组件

## 使用原则

- 基于app/components构建
- 包含业务逻辑和状态管理
- 使用Mock数据进行独立开发
- 遵循MVVM架构分层
```

### 组件级 README 内容规范

参考 `apps/stu/app/components/common/button/README.md` 格式：

```markdown
# [组件名] 组件

[组件简介和用途说明]

## 特性

- 特性1
- 特性2
- 特性3

## 使用方法

\`\`\`jsx
import { ComponentName } from '@/app/components/common/component-name';
// 或
import { ComponentName } from '@/app/views/business/component-name';

// 基本用法
<ComponentName>内容</ComponentName>

// 高级用法
<ComponentName
  prop1="value1"
  prop2={value2}
>
  内容
</ComponentName>
\`\`\`

## 属性

| 属性名 | 类型 | 默认值 | 描述 |
|--------|------|--------|------|
| `prop1` | `string` | `'default'` | 属性描述 |
| `prop2` | `boolean` | `false` | 属性描述 |

## Storybook

查看 Storybook 中的 `[ComponentName]` 故事以了解所有用法示例。

## 示例

[具体使用示例]

## 注意事项

- 注意事项1
- 注意事项2
```

### Storybook 测试规范

每个组件都必须包含 `.stories.tsx` 文件：

```typescript
// component-name.stories.tsx
import type { Meta, StoryObj } from '@storybook/react';
import { ComponentName } from './component-name';

const meta: Meta<typeof ComponentName> = {
  title: 'Components/Common/ComponentName', // 或 Views/Business/ComponentName
  component: ComponentName,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    // 属性控制配置
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// 基本用法
export const Default: Story = {
  args: {
    // 默认属性
  },
};

// 不同状态的故事
export const Loading: Story = {
  args: {
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
};
```

### 维护要求
- **新增组件时**：必须同时创建 `.tsx`、`.stories.tsx`、`README.md`
- **开发前检查**：查阅相应层级的 README.md 了解可复用组件
- **Code Review**：检查文档和Storybook更新情况
- **组件变更时**：及时更新属性表格、使用示例和Story
- **分层原则**：纯UI组件放在 `app/components`，业务组件放在 `app/views`

### 📋 README更新强制要求

#### 每次变化必须更新README
- **新增组件** → 更新 `app/components/README.md` 或 `app/views/README.md` 索引
- **删除组件** → 从相应索引README中移除组件条目
- **组件重命名** → 同步更新索引README中的组件名称和路径
- **组件移动** → 更新新旧位置的索引README
- **属性变更** → 更新组件级README中的属性表格
- **功能变更** → 更新组件级README中的特性列表和使用示例

#### 检查方式
```bash
# 检查是否有未更新的README
git diff --name-only | grep -E "\.(tsx|ts)$" | while read file; do
  if [[ $file == app/components/* ]]; then
    echo "检查 app/components/README.md 是否需要更新"
  elif [[ $file == app/views/* ]]; then
    echo "检查 app/views/README.md 是否需要更新"
  fi
done
```

## 📋 Code Review 检查清单

### View层核心检查
- [ ] **是否绝对避免了直接调用API？** ⚠️ 严格禁止
- [ ] 是否包含业务逻辑处理？
- [ ] 是否直接调用 Model 层？
- [ ] 状态是否符合边界判断规则？
- [ ] 事件处理是否正确委托给上层？
- [ ] 组件是否保持单一职责？

### 文件组织检查
- [ ] 文件名是否使用 kebab-case？
- [ ] Props接口是否定义在组件文件顶部并导出？
- [ ] 目录结构是否符合规范？

### 样式检查
- [ ] **是否完全使用 TailwindCSS？** ⚠️ 强制要求
- [ ] 是否避免了内联 style 属性？
- [ ] 是否避免了 CSS 模块？
- [ ] UI稿中的样式是否正确转换为 Tailwind 类名？

### 状态边界检查
- [ ] 布局样式控制状态是否合理保留在View层？
- [ ] 业务相关状态是否移到ViewModel？
- [ ] 是否遵循"直觉优于教条"原则？

### Mock数据检查
- [ ] 所有Mock数据是否有正确的标识类型？
- [ ] ViewModel状态Mock是否使用 `[MOCK-VIEWMODEL]` 标识？
- [ ] Context状态Mock是否使用 `[MOCK-CONTEXT]` 标识？
- [ ] 真实代码是否用注释形式保留？
- [ ] Mock数据是否集中在组件顶部？
- [ ] 是否使用了Mock数据区块标识？

### 组件拆分检查
- [ ] 是否遵循"三次规则"？
- [ ] 是否避免了过度拆分？
- [ ] 拆分的组件是否有真正的复用价值？
- [ ] 是否保持了相关逻辑的内聚性？

### 性能优化检查
- [ ] 大列表是否使用了@tanstack/react-virtual虚拟滚动？
- [ ] 防抖是否使用了ahooks的useDebounce或useDebounceFn？
- [ ] 滚动检测是否使用了项目的 hook？
- [ ] 懒加载是否使用了Next.js dynamic或IntersectionObserver？
- [ ] 是否对纯展示组件使用了React.memo？
- [ ] 是否使用useMemo缓存了复杂计算？
- [ ] 是否使用useCallback缓存了事件处理函数？
- [ ] 是否避免了不必要的状态更新？

### 文档维护检查
- [ ] **是否更新了相应的索引README？** ⚠️ 强制要求
  - [ ] `app/components/README.md` (纯UI组件变更时)
  - [ ] `app/views/README.md` (业务组件变更时)
- [ ] 组件级 README 是否包含完整的属性表格？
- [ ] 是否提供了使用示例和注意事项？
- [ ] 是否创建了对应的 Storybook 测试？
- [ ] 组件是否放在了正确的目录层级？
- [ ] 组件变更是否同步更新了相关文档？

### Storybook测试检查
- [ ] 是否创建了 `.stories.tsx` 文件？
- [ ] 是否包含了基本的 Default Story？
- [ ] 是否覆盖了主要的组件状态？
- [ ] Story 标题是否遵循分层命名规范？
- [ ] 是否配置了合适的 argTypes？

## 🎯 总结

### 核心要点
1. **绝对禁止直接调用API** - 这是最重要的架构原则
2. **强制使用TailwindCSS** - 所有样式必须使用Tailwind类名
3. **统一文件命名规范** - 使用kebab-case，Props接口导出
4. **强制更新README** - 每次组件变化都必须更新相应的索引README
5. **基于UI稿独立开发** - 通过Mock确保组件可测试
6. **状态边界清晰** - 布局样式状态可保留，业务状态移到ViewModel
7. **延迟拆分组件** - 避免过度工程化，需要时再拆分
8. **性能优化优先** - 优先使用项目既有的性能优化方案
9. **规范化Mock标识** - 便于后续ViewModel对接和清理

### 开发流程
```
UI设计稿 → Mock外部状态 → 实现View组件 → 编写测试/Story → 更新README → 对接ViewModel → 清理Mock
```

### README维护流程
```
组件变更 → 更新组件级README → 更新索引README → Code Review检查 → 合并代码
```

### 最佳实践
- 优先考虑开发直觉和代码可读性
- 保持组件职责单一和逻辑内聚
- 通过规范化Mock确保组件独立可测
- 遵循MVVM架构的分层原则

遵循这些规范，View层将成为清晰、可维护、高复用的UI展示层，完成代码编写后需要自检并输出自查清单。

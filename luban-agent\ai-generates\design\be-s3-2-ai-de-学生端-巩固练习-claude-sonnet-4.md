# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** be-s1-1-ai-prd-Format-PRD-学生端-巩固练习-claude-sonnet-4.md
- **PRD版本号：** V1.0
- **提取日期：** 2025-05-25

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 巩固练习 | 在学生掌握新知识或技能后，借助有针对性的练习加深理解、强化记忆的学习活动 | 2.1, 11.2 |
| 练习记录 | 学生进行巩固练习的完整记录，包含练习状态、进度、结果等信息 | 2.1, 4, 5 |
| 练习题目 | 巩固练习中推荐给学生的具体题目，基于AI课知识点和学生表现动态推荐 | 2.1, 3.2 |
| 学习报告 | 展示练习结果和掌握度变化的数据报告，为学习效果评估提供支持 | 2.2, 4, 7.1 |
| 错题记录 | 学生在练习过程中答错的题目记录，支持自动添加和手动添加 | 2.2, 4 |
| 再练一次记录 | 基于掌握度判断推荐的再次练习记录，确保知识点充分掌握 | 2.1, 3.3, 4 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`巩固练习`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 练习ID | 巩固练习的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 2.1 |
| 学生ID | 关联用户中心的学生标识 | 整数 | 非空, 外键 |  | 外键 | 2.1 |
| AI课ID | 关联的AI课程标识 | 整数 | 非空, 外键 |  | 外键 | 2.1 |
| 知识点ID | 关联的知识点标识 | 整数 | 非空, 外键 |  | 外键 | 2.1, 3.2 |
| 练习状态 | 练习的当前状态 | 枚举 | 未开始、进行中、已完成 | 未开始 |  | 2.1, 4 |
| 创建时间 | 练习创建的时间戳 | 时间戳 | 非空 |  |  | 2.1 |
| 完成时间 | 练习完成的时间戳 | 时间戳 | 可空 |  |  | 4 |

**实体：`练习记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 记录ID | 练习记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 4 |
| 练习ID | 关联的巩固练习标识 | 整数 | 非空, 外键 |  | 外键 | 4 |
| 已完成题目数 | 学生实际作答的题目数量 | 整数 | >=0 | 0 |  | 3.1 |
| 预估总题目数 | 推题策略预估的练习题目总数 | 整数 | >0 |  |  | 3.1 |
| 当前进度 | 当前进度 = 已完成题目数 / 预估总题目数 × 100% | 数值型(百分比) | 0-100 | 0 |  | 3.1 |
| 正确率 | 本次练习的答题正确率 | 数值型(百分比) | 0-100 |  |  | 3.2 |
| 学习时长 | 练习总耗时(秒) | 整数 | >=0 | 0 |  | 4, 7.1 |
| 开始时间 | 练习开始时间戳 | 时间戳 | 非空 |  |  | 4 |
| 最后更新时间 | 记录最后更新时间戳 | 时间戳 | 非空 |  |  | 4 |

**实体：`练习题目`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 练习题目ID | 练习题目记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 2.1 |
| 练习ID | 关联的巩固练习标识 | 整数 | 非空, 外键 |  | 外键 | 2.1 |
| 题目ID | 关联内容平台的题目标识 | 整数 | 非空, 外键 |  | 外键 | 2.1 |
| 题目序号 | 题目在练习中的顺序 | 整数 | >0 |  |  | 4 |
| 题目难度 | 题目的难度系数 | 数值型 | >0 |  |  | 3.2 |
| 作答状态 | 题目的作答状态 | 枚举 | 未作答、已作答、已跳过 | 未作答 |  | 4 |
| 学生答案 | 学生提交的答案内容 | 文本 | 可空 |  |  | 4 |
| 是否正确 | 答案是否正确 | 布尔值 | 可空 |  |  | 4 |
| 作答时间 | 学生作答耗时(秒) | 整数 | >=0 |  |  | 4 |
| 提交时间 | 答案提交时间戳 | 时间戳 | 可空 |  |  | 4 |

**实体：`学习报告`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 报告ID | 学习报告的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 4, 7.1 |
| 练习ID | 关联的巩固练习标识 | 整数 | 非空, 外键 |  | 外键 | 4 |
| 练习前掌握度 | 练习开始前的知识点掌握程度 | 数值型(百分比) | 0-100 |  |  | 3.2, 4 |
| 练习后掌握度 | 练习完成后的知识点掌握程度 | 数值型(百分比) | 0-100 |  |  | 3.2, 4 |
| 掌握度提升 | 掌握度变化幅度 | 数值型(百分比) | 可负数 |  |  | 4, 7.1 |
| 总题目数 | 练习总题目数量 | 整数 | >0 |  |  | 4, 7.1 |
| 正确题目数 | 答对的题目数量 | 整数 | >=0 |  |  | 4, 7.1 |
| 错误题目数 | 答错的题目数量 | 整数 | >=0 |  |  | 4, 7.1 |
| 总学习时长 | 练习总耗时(秒) | 整数 | >=0 |  |  | 4, 7.1 |
| 生成时间 | 报告生成时间戳 | 时间戳 | 非空 |  |  | 4 |

**实体：`错题记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 错题ID | 错题记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 2.2, 4 |
| 学生ID | 关联用户中心的学生标识 | 整数 | 非空, 外键 |  | 外键 | 2.2 |
| 题目ID | 关联内容平台的题目标识 | 整数 | 非空, 外键 |  | 外键 | 2.2 |
| 练习题目ID | 关联的练习题目标识(可空，手动添加时为空) | 整数 | 可空, 外键 |  | 外键 | 2.2, 4 |
| 添加方式 | 错题添加方式 | 枚举 | 自动添加、手动添加 |  |  | 2.2, 4 |
| 错因标签 | 系统预设的错误原因标签 | 文本数组 | 可空 |  |  | 2.2, 4 |
| 学生答案 | 学生的错误答案 | 文本 | 可空 |  |  | 4 |
| 添加时间 | 错题添加时间戳 | 时间戳 | 非空 |  |  | 2.2, 4 |

**实体：`再练一次记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 再练记录ID | 再练一次记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 2.1, 4 |
| 原练习ID | 关联的原始巩固练习标识 | 整数 | 非空, 外键 |  | 外键 | 3.3, 4 |
| 新练习ID | 再练一次生成的新练习标识 | 整数 | 非空, 外键 |  | 外键 | 4 |
| 触发掌握度 | 触发再练一次时的掌握度 | 数值型(百分比) | 0-100 |  |  | 3.3 |
| 目标掌握度 | 系统设定的掌握标准 | 数值型(百分比) | 0-100 |  |  | 3.3 |
| 薄弱知识点 | 需要加强练习的知识点列表 | 文本数组 | 非空 |  |  | 4 |
| 推荐原因 | 推荐再练一次的原因说明 | 文本 | 可空 |  |  | 4 |
| 创建时间 | 再练记录创建时间戳 | 时间戳 | 非空 |  |  | 4 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 (例如：用户 '拥有一个' 学校档案) | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 学生进行多次巩固练习 | 学生(用户中心) | 巩固练习 | 1:N | 巩固练习.学生ID -> 学生.ID | 2.1, 4 |
| AI课对应多个巩固练习 | AI课(内容平台) | 巩固练习 | 1:N | 巩固练习.AI课ID -> AI课.ID | 2.1 |
| 知识点对应多个巩固练习 | 知识点(内容平台) | 巩固练习 | 1:N | 巩固练习.知识点ID -> 知识点.ID | 2.1, 3.2 |
| 巩固练习包含一个练习记录 | 巩固练习 | 练习记录 | 1:1 | 练习记录.练习ID -> 巩固练习.ID | 4 |
| 巩固练习包含多个练习题目 | 巩固练习 | 练习题目 | 1:N | 练习题目.练习ID -> 巩固练习.ID | 2.1, 4 |
| 题目(内容平台)对应多个练习题目 | 题目(内容平台) | 练习题目 | 1:N | 练习题目.题目ID -> 题目.ID | 2.1 |
| 巩固练习生成一个学习报告 | 巩固练习 | 学习报告 | 1:1 | 学习报告.练习ID -> 巩固练习.ID | 4, 7.1 |
| 学生拥有多个错题记录 | 学生(用户中心) | 错题记录 | 1:N | 错题记录.学生ID -> 学生.ID | 2.2 |
| 题目(内容平台)对应多个错题记录 | 题目(内容平台) | 错题记录 | 1:N | 错题记录.题目ID -> 题目.ID | 2.2 |
| 练习题目可能生成错题记录 | 练习题目 | 错题记录 | 1:1(可选) | 错题记录.练习题目ID -> 练习题目.ID | 2.2, 4 |
| 原练习触发再练一次记录 | 巩固练习 | 再练一次记录 | 1:N | 再练一次记录.原练习ID -> 巩固练习.ID | 3.3, 4 |
| 再练一次记录关联新练习 | 再练一次记录 | 巩固练习 | 1:1 | 再练一次记录.新练习ID -> 巩固练习.ID | 4 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 进度计算公式 | 已完成题目数, 预估总题目数 | 当前进度 | 3.1 |
| 掌握度变化计算 | 正确率, 题目难度, 知识点权重, 当前掌握度 | 练习后掌握度 | 3.2 |
| 再练一次触发判断 | 当前掌握度, 目标掌握度 | 是否需要再练一次 | 3.3 |
| 学习报告统计 | 总题目数, 正确题目数, 错误题目数, 总学习时长, 练习前掌握度, 练习后掌握度 | 掌握度提升, 正确率 | 4, 7.1 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 练习记录.当前进度 | 学生每次完成题目作答后 | 实时/每次作答 | 3.1, 4 |
| 练习记录.正确率 | 学生每次完成题目作答后 | 实时/每次作答 | 3.2, 4 |
| 练习题目.作答状态 | 学生提交答案或跳过题目时 | 实时/每次操作 | 4 |
| 错题记录 | 学生答错题目时自动添加，或学生手动添加时 | 实时/每次错误或手动操作 | 2.2, 4 |
| 学习报告 | 学生完成最后一道题目后 | 每次练习完成 | 4, 7.1 |
| 掌握度相关属性 | 基于巩固练习推题策略中的掌握度算法更新 | 每次练习完成 | 3.2 |
| 再练一次记录 | 学生完成练习且掌握度未达标时 | 每次练习完成后判断 | 3.3, 4 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 学生ID | 用户中心服务提供，学生身份认证后获取 | 2.1, 6.2 |
| AI课ID | 内容平台服务提供，基于学生完成的AI课程 | 2.1 |
| 知识点ID | 内容平台服务提供，基于AI课关联的知识点 | 2.1, 3.2 |
| 题目ID | 内容平台服务提供，通过智能题目推荐策略获取 | 2.1 |
| 题目难度 | 内容平台服务提供，题目的难度系数 | 3.2 |
| 知识点权重 | 巩固练习推题策略提供，不同知识点的重要性权重 | 3.2 |
| 目标掌握度 | 系统配置，管理员在后台界面进行配置 | 3.3, 8.2 |
| 学生答案 | 用户输入，学生在练习过程中提交的答案 | 4 |
| 错因标签 | 系统预设，用户选择的错误原因标签 | 2.2, 4 |

---

## 7. 当前版本明确排除的数据需求 (例如PRD中的V1.0版本)

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 (源自PRD"非本期功能"等章节) | PRD中说明的排除原因 (若有) | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 社交排行榜相关数据 | 建议迭代2再考虑 | 2.3 |
| 练习积分商城相关数据 | 建议迭代3再考虑 | 2.3 |
| 语音答题相关数据 | 建议迭代4再考虑 | 2.3 |
| 学生间练习排名对比数据 | 社交功能，非本期范围 | 2.3 |
| 积分兑换奖励机制数据 | 激励系统，非本期范围 | 2.3 |
| 口语类题目的语音作答数据 | 语音处理，非本期范围 | 2.3 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别巩固练习、练习记录、练习题目、学习报告、错题记录、再练一次记录等核心实体 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏（例如，本示例中的"题目信息"、"课程信息"）？                                                         | ✅ | 题目信息、AI课信息等已在公共服务中定义，只保留关联关系 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已提取进度计算、掌握度变化、再练一次触发等相关属性 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | 属性描述与PRD原文保持一致 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确学生-练习、练习-题目、练习-报告等关系 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 掌握度计算依赖、再练一次触发依赖等已体现 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 进度计算、掌握度变化、再练一次触发等计算的输入项已列出 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 实时更新、每次作答更新、练习完成更新等频次已明确 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 社交排行榜、积分商城、语音答题等排除项已记录 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 使用PRD中的术语，如"巩固练习"、"再练一次"、"掌握度"等 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 所有提取项都标注了对应的PRD章节号 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 所有信息均来源于PRD原文，未添加主观内容 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 熔断机制的具体触发条件和阈值在PRD中未明确，需与PM确认 |

--- 
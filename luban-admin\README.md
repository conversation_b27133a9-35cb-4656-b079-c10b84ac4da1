# 项目名称：鲁班 Code AI Agent - 智能文档处理平台

## 项目用途：
**基于AI的智能文档分析与代码生成工具，专注于飞书文档和PDF文件的解析处理，自动生成PRD、技术设计文档、开发文档和API文档，提升开发团队的工作效率和代码质量。**

---

# 后端架构

## 技术栈
#### 核心框架
- **FastAPI** - 高性能异步Python Web框架
- **Uvicorn** - 基于asyncio的ASGI服务器
- **Pydantic** - 数据验证和序列化

#### 文档处理技术
- **PyMuPDF (1.25.5)** - PDF解析和处理
- **lark_oapi (1.4.14)** - 飞书API集成
- **EasyOCR (1.7.1)** - 图像OCR识别
- **Pillow** - 图像处理

#### AI模型集成
- **OpenAI API (1.76.0)** - GPT模型访问
- **Anthropic API (0.46.0)** - Claude模型访问
- **OpenRouter** - 多模型API聚合

#### 存储与安全
- **阿里云OSS (2.19.1)** - 对象存储服务
- **JWT** - 用户认证与授权
- **bcrypt** - 密码加密

## 系统架构
#### 分层架构设计
```
┌─────────────────────────────────────────────────────┐
│                      FastAPI                         │
└───────────────────────────┬─────────────────────────┘
                           │
┌───────────────────────────┴─────────────────────────┐
│                       API路由                        │
│    auth    documents    prompts    configs           │
└───────────────────────────┬─────────────────────────┘
                           │
┌───────────────────────────┴─────────────────────────┐
│                      服务层                          │
│ document_service  prompt_service  config_service     │
└───────────────────────────┬─────────────────────────┘
                           │
┌───────────────────────────┴─────────────────────────┐
│                      库层                            │
│ doc_processor    prompt    storage                   │
└───────────────────────────┬─────────────────────────┘
                           │
┌───────────────────────────┴─────────────────────────┐
│                 外部服务/资源                        │
│ 大模型API(OpenAI/Claude)  飞书API  阿里云OSS         │
└─────────────────────────────────────────────────────┘
```

## 目录结构
#### 模块化组织
```
backend/
├── app/                  # 应用主目录
│   ├── api/              # API路由定义
│   ├── core/             # 核心功能模块
│   ├── lib/              # 功能库
│   ├── services/         # 业务服务层
│   ├── schemas/          # 数据模式验证
│   ├── utils/            # 工具函数
│   └── main.py           # 应用入口
├── config/               # 配置文件目录
├── docs/                 # 文档目录
├── third-party/          # 第三方工具集成
├── outputs/              # 输出文件目录
└── requirements.txt      # 依赖管理
```

## 核心功能
#### 文档处理引擎
- **飞书文档解析** - 支持飞书在线文档的完整解析和转换
- **PDF文档处理** - 高精度PDF文本提取和图像识别
- **智能图像分析** - 使用大模型分析文档中的图片内容
- **Markdown转换** - 标准化文档格式输出

#### AI增强功能
- **PRD生成** - 基于原始文档自动生成产品需求文档（支持前端/后端）
- **技术设计文档** - 自动生成技术架构和设计方案（支持前端/后端）
- **开发文档** - 生成开发指南和实现文档
- **API文档** - 自动生成API接口文档

#### 异步任务系统
- **任务队列管理** - 处理长时间运行的文档处理任务
- **实时状态跟踪** - WebSocket实时任务进度推送
- **结果缓存** - 智能缓存处理结果

#### Prompt模板管理
- **模板分类管理** - 按功能分类组织Prompt模板
- **变量系统** - 支持动态变量替换和参数化
- **模板编辑** - 在线创建、编辑和测试Prompt模板

## 技术难点
#### 多模态文档处理
- **复杂文档结构解析** - 处理包含图片、表格、代码块的复杂文档
- **图像内容理解** - 结合OCR和大模型实现图像语义分析
- **格式保持** - 在转换过程中保持原文档的结构和格式

#### 大模型集成优化
- **多模型适配** - 统一接口适配不同AI模型API
- **Prompt工程** - 精细化Prompt模板管理和优化
- **错误处理与重试** - 网络异常和API限流的智能处理

#### 性能与并发
- **异步处理架构** - 基于asyncio的高并发处理能力
- **内存管理** - 大文件处理时的内存优化
- **任务调度** - 智能任务队列和资源分配

---

# 前端架构

## 技术栈
#### 核心框架
- **Next.js 14** - React全栈框架 (App Router)
- **React 18** - 用户界面库
- **TypeScript** - 类型安全的JavaScript

#### UI与样式
- **Tailwind CSS** - 原子化CSS框架
- **ShadCN UI** - 基于Radix UI的组件库
- **Framer Motion** - 动画库
- **Lucide React** - 图标库

#### 状态管理与数据
- **Zustand** - 轻量级状态管理
- **React Query (@tanstack/react-query)** - 数据获取与缓存
- **React Hook Form** - 表单处理
- **Zod** - 数据验证

#### 文档处理
- **React Markdown** - Markdown渲染
- **React Syntax Highlighter** - 代码高亮
- **Markdown-it** - Markdown解析

## 目录结构
#### 模块化组织
```
frontend/
├── public/               # 静态资源
├── src/                  # 源代码
│   ├── app/              # Next.js 应用路由
│   │   ├── workflow/     # 工作流相关页面
│   │   ├── api/          # API路由
│   │   └── configs/      # 配置页面
│   ├── components/       # UI组件
│   │   ├── layout/       # 布局组件
│   │   ├── workflow/     # 工作流组件
│   │   ├── markdown/     # Markdown组件
│   │   └── ui/           # 基础UI组件
│   ├── lib/              # 工具库
│   │   ├── api/          # API客户端
│   │   └── store/        # 状态管理
│   ├── types/            # 类型定义
│   └── styles/           # 样式文件
└── 配置文件
```

## 核心页面和功能
#### 主要页面
- **首页** - 产品介绍和快速入口
- **工作流管理** - 创建、编辑和管理文档处理工作流
- **文档处理** - 上传和处理飞书文档、PDF文件
- **PRD生成** - 产品需求文档自动生成（前端/后端分离）
- **技术设计** - 技术架构文档生成（前端/后端分离）
- **API文档** - 接口文档自动生成
- **格式化PRD** - 原始文档格式化和结构化处理

#### 核心功能模块
- **文档上传与预览** - 支持拖拽上传、实时预览
- **工作流编辑器** - 可视化工作流配置和编辑
- **Markdown编辑器** - 实时编辑和预览Markdown文档
- **任务监控** - 实时显示处理进度和状态
- **模板管理** - Prompt模板的创建和管理

#### 工作流步骤管理
- **步骤导航** - 清晰的步骤指示和进度跟踪
- **数据流转** - 步骤间的数据依赖和传递
- **状态持久化** - 防止页面刷新导致的数据丢失
- **错误恢复** - 步骤失败时的重试和恢复机制

#### 用户交互体验
- **响应式设计** - 适配桌面和移动设备
- **实时反馈** - WebSocket实时状态更新
- **错误处理** - 友好的错误提示和恢复机制
- **加载状态** - 优雅的加载动画和骨架屏

## 技术难点
#### 复杂状态管理
- **工作流状态同步** - 多步骤工作流的状态管理和数据流转
- **实时数据更新** - WebSocket与React Query的集成
- **离线状态处理** - 网络异常时的数据持久化

#### 性能优化
- **大文件处理** - 大型文档的分片上传和渲染优化
- **组件懒加载** - 基于路由的代码分割和动态导入
- **虚拟滚动** - 长列表的性能优化

#### 用户体验
- **Markdown渲染优化** - 大型文档的流畅渲染和编辑
- **拖拽交互** - 文件上传和工作流编辑的拖拽体验
- **响应式适配** - 复杂界面在不同设备上的适配

#### 类型安全
- **API类型定义** - 前后端接口的类型一致性
- **组件Props类型** - 严格的组件接口定义
- **状态类型管理** - Zustand store的类型安全

---

## 核心工作流程

### 文档处理流程
```
文件上传 -> 文档解析 -> 内容提取 -> 图片处理 -> Markdown生成 -> 结果保存
    │                                    │
    └─ 飞书文档授权                      └─ AI图片分析
```

### AI增强流程
```
原始文档 -> 格式化处理 -> PRD生成 -> 技术设计 -> 开发文档 -> API文档
    │           │           │         │          │         │
    └─ 结构化   └─ AI增强   └─ 前后端  └─ 架构图  └─ 代码片段 └─ OpenAPI
```

### 异步任务处理
```
任务创建 -> 任务排队 -> 任务执行 -> 结果处理
    │                      │         │
    └─ 状态跟踪            └─ 进度更新 └─ WebSocket推送
```

## 主要API接口

### 文档处理API
- `POST /api/documents/upload` - 上传PDF文档
- `POST /api/documents/process/pdf/{file_id}` - 处理PDF文件
- `POST /api/documents/process/feishu` - 处理飞书文档
- `POST /api/documents/process/ai-prd` - AI生成PRD文档
- `POST /api/documents/process/ai-td` - AI生成技术设计文档

### 任务管理API
- `GET /api/documents/tasks/{task_id}` - 获取任务状态
- `GET /api/documents/tasks` - 获取所有任务列表

### Prompt管理API
- `GET /api/prompts/categories` - 获取所有Prompt分类
- `GET /api/prompts/{category}/{name}` - 获取Prompt详情
- `POST /api/prompts/{category}/{name}` - 创建新Prompt
- `PUT /api/prompts/{category}/{name}` - 更新Prompt

## 部署与运行

### 环境要求
- **后端**: Python 3.8+, 足够磁盘空间
- **前端**: Node.js 16+, npm/yarn
- **可选**: 阿里云OSS账户（远程存储）

### 后端启动
```bash
cd backend
pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### 前端启动
```bash
cd frontend
npm install
npm run dev
```

### 配置说明
后端配置文件位于 `backend/config/default.yaml`，主要配置项：
- **LLM配置**: OpenAI、Claude、OpenRouter API密钥
- **飞书配置**: 应用ID和应用秘钥
- **OSS配置**: 阿里云存储配置
- **任务队列**: 并发数和超时设置

### 访问地址
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000/api
- API文档: http://localhost:8000/api/docs
- 下载文件: http://localhost:8000/download/
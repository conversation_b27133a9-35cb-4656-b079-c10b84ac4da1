// @ts-nocheck
'use client';

export const dynamic = 'force-dynamic';

import { useState, FormEvent, useEffect, useRef, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import WorkflowSidebar from '@/components/workflow/WorkflowSidebar';
import WorkflowPageWrapper from '@/components/workflow/WorkflowPageWrapper';
import { documentApi } from '@/lib/api';
import MarkdownPreviewModal from '@/components/markdown/MarkdownPreviewModal';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import WorkflowDebugPanel from '@/components/debug/WorkflowDebugPanel';
import axios from 'axios';
import { Terminal } from 'lucide-react';
import {
  Button,
  Input,
  Progress,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Box,
  useToast
} from '@chakra-ui/react';
import { CopyIcon } from '@chakra-ui/icons';
import { API_BASE_URL } from '@/app/configs/api';

export default function AiApiPage() {
  const [activeTab, setActiveTab] = useState('url');
  const [isLoading, setIsLoading] = useState(false);
  const [mdUrl, setMdUrl] = useState('');
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState('');
  const [taskId, setTaskId] = useState('');
  const [logs, setLogs] = useState<string[]>([]);
  const [showLogs, setShowLogs] = useState(false);
  const [progress, setProgress] = useState(0);
  const [downloadUrl, setDownloadUrl] = useState('');
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [urlError, setUrlError] = useState('');
  const [type, setType] = useState('be'); // 默认选择后端
  
  const logEndRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const wsRef = useRef<WebSocket | null>(null);
  const toast = useToast();
  const searchParams = useSearchParams();
  
  // 工作流数据管理
  const { workflowData, navigateToNextPage, getHiddenInputsData, getHiddenInputsProps } = useWorkflowData('ai-api');
  
  // 处理工作流参数 - 设置ai_ad_url到输入框
  useEffect(() => {
    if (workflowData.ai_ad_url) {
      setMdUrl(workflowData.ai_ad_url);
      setActiveTab('url');
      console.log('从工作流参数获取到ai_ad_url:', workflowData.ai_ad_url);
      console.log('接收到的工作流数据:', workflowData);
    }
  }, [workflowData]);
  
  // 自动滚动到最新日志
  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);
  
  // WebSocket连接处理
  useEffect(() => {
    // 清理WebSocket连接
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
    };
  }, []);
  
  // 监听taskId变化，创建WebSocket连接
  useEffect(() => {
    if (taskId) {
      console.log('taskId变化，创建WebSocket连接:', taskId);
      
      // 关闭已有的连接
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
      
      // WebSocket连接参数
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      let wsHost;
      
      try {
        if (API_BASE_URL.startsWith('http')) {
          const apiUrl = new URL(API_BASE_URL);
          wsHost = apiUrl.host;
        } else {
          wsHost = window.location.host;
        }
      } catch (e) {
        console.error("解析API_BASE_URL失败: ", API_BASE_URL, e);
        wsHost = window.location.host;
      }
      
      const wsUrl = `${protocol}//${wsHost}/api/ws/${taskId}`;
      console.log('正在连接WebSocket...', wsUrl);
      
      const socket = new WebSocket(wsUrl);
      wsRef.current = socket;
      
      socket.onopen = (event) => {
        console.log('WebSocket连接已建立', event);
        addLog('WebSocket连接已建立，开始接收实时日志...');
        
        try {
          socket.send(JSON.stringify({
            type: 'hello',
            taskId: taskId,
            timestamp: Date.now()
          }));
        } catch (e) {
          console.error('发送测试消息失败', e);
        }
      };
      
      socket.onmessage = (event) => {
        console.log('收到WebSocket消息:', event.data);
        
        try {
          const data = JSON.parse(event.data);
          
          if (data.type === 'heartbeat') {
            console.log('收到心跳消息:', data);
            return;
          } else if (data.type === 'log') {
            addLog(data.message || '');
            updateProgressFromLog(data.message || '');
          } else if (data.type === 'status') {
            const statusMessage = data.message === 'undefined' || data.message === undefined
              ? (data.status === 'running' ? '正在处理中...' : '')
              : data.message;
            
            addLog(`状态更新: ${data.status} - ${statusMessage}`);
            
            if (data.status === 'completed') {
              setProgress(100);
              
              if (data.download_url) {
                const isDirectDownloadLink = data.download_url.startsWith('/download');
                const backendHost = API_BASE_URL.replace(/\/api$/, '');
                const fullDownloadUrl = data.download_url.startsWith('http')
                  ? data.download_url
                  : isDirectDownloadLink
                    ? `${backendHost}${data.download_url}`
                    : `${API_BASE_URL}${data.download_url.startsWith('/') ? '' : '/'}${data.download_url}`;
                
                if (!downloadUrl) {
                  setDownloadUrl(fullDownloadUrl);
                  addLog(`处理完成！下载链接已生成`);
                }
              } else if (!downloadUrl) {
                addLog(`处理完成！但未找到下载链接`);
              }
              
              setIsLoading(false);
            } else if (data.status === 'failed') {
              setError(statusMessage || '处理失败');
              addLog(`处理失败: ${statusMessage}`);
              setIsLoading(false);
            }
          } else if (data.type === 'task_update') {
            // 处理任务状态更新消息
            const task = data.task;
            if (task) {
              addLog(`任务状态更新: ${task.status}`);
              
              if (task.status === 'completed') {
                setProgress(100);
                
                if (task.result && task.result.download_url) {
                  const isDirectDownloadLink = task.result.download_url.startsWith('/download');
                  const backendHost = API_BASE_URL.replace(/\/api$/, '');
                  const fullDownloadUrl = task.result.download_url.startsWith('http')
                    ? task.result.download_url
                    : isDirectDownloadLink
                      ? `${backendHost}${task.result.download_url}`
                      : `${API_BASE_URL}${task.result.download_url.startsWith('/') ? '' : '/'}${task.result.download_url}`;
                  
                  if (!downloadUrl) {
                    setDownloadUrl(fullDownloadUrl);
                    addLog(`处理完成！下载链接已生成`);
                  }
                } else if (!downloadUrl) {
                  addLog(`处理完成！但未找到下载链接`);
                }
                
                setIsLoading(false);
              } else if (task.status === 'failed') {
                setError(task.error || '处理失败');
                addLog(`处理失败: ${task.error || '未知错误'}`);
                setIsLoading(false);
              }
            }
          } else {
            addLog(event.data);
            updateProgressFromLog(event.data);
          }
        } catch (e) {
          addLog(event.data);
          updateProgressFromLog(event.data);
        }
      };

      socket.onclose = (event) => {
        console.log('WebSocket连接已关闭', event.code, event.reason);
        addLog(`WebSocket连接已关闭. 代码: ${event.code}`);
      };
      
      socket.onerror = (error) => {
        console.error('WebSocket错误:', error);
        addLog(`WebSocket错误: ${error}`);
      };
      
      const pingInterval = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          try {
            socket.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
          } catch (e) {
            console.error('发送心跳包失败', e);
          }
        }
      }, 30000);
      
      return () => {
        clearInterval(pingInterval);
        if (socket.readyState === WebSocket.OPEN) {
          socket.close();
        }
        wsRef.current = null;
      };
    }
  }, [taskId, router]);
  
  const addLog = (message: string) => {
    setLogs((prevLogs) => [...prevLogs, message]);
  };
  
  const updateProgressFromLog = (logText: string) => {
    if (logText.includes('处理完成') || logText.includes('生成文件')) {
      setProgress(100);
      return;
    }
    
    if (progress === 0 && isLoading && (logText.includes('开始处理') || logText.includes('正在处理'))) {
      setProgress(50);
      return;
    }
    
    const progressMatch = /进度: \[([\d]+)\/([\d]+)\]/.exec(logText);
    if (progressMatch) {
      const current = parseInt(progressMatch[1]);
      const total = parseInt(progressMatch[2]);
      const progressPercent = Math.round((current / total) * 100);
      setProgress(progressPercent);
    }
  };
  
  const validateUrl = (url: string): boolean => {
    if (!url) return false;
    
    try {
      new URL(url);
      setUrlError('');
      return true;
    } catch (error) {
      console.error('URL验证错误:', error);
      setUrlError('URL格式无效，请检查');
      return false;
    }
  };

  const handleFormSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setLogs([]);
    setShowLogs(false);
    setDownloadUrl('');
    setProgress(0);
    
    try {
      let result;
      if (activeTab === 'url') {
        if (!mdUrl) {
          throw new Error('请输入Markdown文档链接');
        }
        
        if (!validateUrl(mdUrl)) {
          setIsLoading(false);
          throw new Error(urlError || 'Markdown文档链接格式不正确');
        }
        
        result = await documentApi.processAiApi({
          url: mdUrl,
          replace_images: true,
          output_format: 'markdown',
          type: type,
          // 传递工作流参数给后端
          workflow_params: workflowData,
          // 添加页面中的所有表单数据
          form_data: {
            feishu_url: searchParams.get('feishu_url') || '', // ai-api接口需要feishu_url参数
            format_prd_url: searchParams.get('format_prd_url') || '', // ai-api接口需要format_prd_url参数
            ai_prd_url: searchParams.get('ai_prd_url') || '', // ai-api接口需要ai_prd_url参数
            ai_td_url: searchParams.get('ai_td_url') || '', // ai-api接口需要ai_td_url参数
            ai_de_url: searchParams.get('ai_de_url') || '', // ai-api接口需要ai_de_url参数
            ai_dd_url: searchParams.get('ai_dd_url') || '', // ai-api接口需要ai_dd_url参数
            ai_ad_url: searchParams.get('ai_ad_url') || mdUrl, // ai-api接口需要ai_ad_url参数
            md_url: mdUrl,
            name: name,
            description: description,
            type: type,
            active_tab: activeTab
          }
        });
        
        setTaskId(result.task_id);
        setShowLogs(true);
        addLog(`开始处理Markdown文档: ${mdUrl}`);
        addLog(`文档类型: 后端API`);
        addLog(`任务ID: ${result.task_id}`);
        setProgress(50);
      }
    } catch (error) {
      console.error('提交失败:', error);
      const errorMessage = error.response?.data?.detail || error.message || '提交失败，请重试';
      setError(errorMessage);
      setIsLoading(false);
      
      setShowLogs(true);
      addLog(`提交失败: ${errorMessage}`);
      addLog(`您可以尝试重新提交，或者检查网络连接和服务器状态`);
    }
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };
  
  const handleCopyLogs = () => {
    const logText = logs.join('\n');
    
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(logText)
          .then(() => {
            toast({
              title: "日志已复制",
              description: "日志内容已成功复制到剪贴板。",
              status: "success",
              duration: 3000,
              isClosable: true,
            });
          })
          .catch(err => {
            console.error('使用Clipboard API复制失败: ', err);
            fallbackCopyTextToClipboard(logText);
          });
      } else {
        fallbackCopyTextToClipboard(logText);
      }
    } catch (err) {
      console.error('复制过程中出错: ', err);
      fallbackCopyTextToClipboard(logText);
    }
    
    function fallbackCopyTextToClipboard(text) {
      try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
          toast({
            title: "日志已复制",
            description: "日志内容已成功复制到剪贴板。",
            status: "success",
            duration: 3000,
            isClosable: true,
          });
        } else {
          throw new Error('复制命令执行失败');
        }
      } catch (err) {
        console.error('回退复制方法失败: ', err);
        toast({
          title: "复制失败",
          description: "无法将日志复制到剪贴板，请手动复制",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      }
    }
  };
  
  return (
    <WorkflowPageWrapper>
      <MainLayout>
      <div className="flex justify-center min-h-screen">
        <div className="flex max-w-6xl w-full">
          {/* 左侧进度菜单 */}
          <WorkflowSidebar className="w-64 flex-shrink-0" />
          
          {/* 主内容区域 */}
          <div className="flex-1">
            <div className="p-6">
              <h1 className="text-3xl font-bold mb-8 border-none">生成AI-API</h1>
              <div className="max-w-3xl mx-auto">
          <div className="flex border-b mb-6">
            <button
              type="button"
              className={`px-4 py-2 font-medium ${activeTab === 'url' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-green-600'}`}
              onClick={() => setActiveTab('url')}
            >
              Markdown文档链接
            </button>
            <button
              type="button"
              className="px-4 py-2 font-medium text-gray-400 cursor-not-allowed"
              onClick={() => {
                toast({
                  title: "暂不支持",
                  description: "上传功能暂不支持，请使用Markdown文档链接",
                  status: "warning",
                  duration: 3000,
                  isClosable: true,
                });
              }}
            >
              上传Markdown文档
            </button>
          </div>
          <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-1">生成API接口文档</h2>
              <p className="text-gray-600 text-sm">通过Markdown文档自动生成API接口文档</p>
            </div>
            {error && (
              <Alert status="error" mb={4} borderRadius="md" variant="subtle">
                <AlertIcon />
                <Box flex="1">
                  <AlertTitle fontWeight="semibold">发生错误:</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Box>
              </Alert>
            )}
            <form onSubmit={handleFormSubmit}>
              {activeTab === 'url' && (
                <>
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="md-url">
                      *<span className="font-bold">Markdown文档链接</span> <span className="text-red-400 font-normal">(必填)</span>
                    </label>
                    <Input
                      id="md-url"
                      name="url"
                      type="text"
                      value={mdUrl}
                      onChange={(e) => {
                        setMdUrl(e.target.value);
                        if (urlError) validateUrl(e.target.value);
                      }}
                      placeholder="粘贴Markdown文档链接"
                      required
                      isDisabled={isLoading}
                      width="100%"
                      px={3}
                      py={2}
                      borderColor={urlError ? "red.300" : "gray.300"}
                      borderRadius="md"
                      boxShadow="sm"
                      _focus={{ outline: "none", ring: "2px", ringColor: urlError ? "red.500" : "green.500", borderColor: urlError ? "red.500" : "green.500" }}
                    />
                    {urlError ? (
                      <p className="text-xs text-red-500 mt-1">{urlError}</p>
                    ) : (
                      <p className="text-xs text-gray-500 mt-1">
                        请输入可访问的Markdown文档链接 <br />
                        示例：http://172.16.31.251:8000/download/be-ai-ad-S3C9wkR7NinQM2kfcKfck9uwnLe.md
                      </p>
                    )}
                  </div>
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *<span className="font-bold">API 类型</span> <span className="text-red-400 font-normal">(必填)</span>
                    </label>
                    <div className="flex space-x-4 mt-2">
                      <div className="flex items-center">
                        <input
                          id="api-type-be"
                          name="api-type"
                          type="radio"
                          value="be"
                          checked={type === 'be'}
                          onChange={() => setType('be')}
                          disabled={isLoading}
                          className="h-4 w-4 text-green-600 border-gray-300 focus:ring-green-500"
                        />
                        <label htmlFor="api-type-be" className="ml-2 block text-sm text-gray-700">
                          后端 API
                        </label>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      生成后端API接口文档
                    </p>
                  </div>
                </>
              )}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="name">
                  工作流名称 <span className="text-gray-400 font-normal">(选填)</span>
                </label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="为此次处理命名，便于后续查找"
                  isDisabled={isLoading}
                  width="100%"
                  px={3}
                  py={2}
                  borderColor="gray.300"
                  borderRadius="md"
                  boxShadow="sm"
                  _focus={{ outline: "none", ring: "2px", ringColor: "green.500", borderColor: "green.500" }}
                />
              </div>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="description">
                  描述 <span className="text-gray-400 font-normal">(选填)</span>
                </label>
                <Input
                  id="description"
                  name="description"
                  type="text"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="添加描述信息"
                  isDisabled={isLoading}
                  width="100%"
                  px={3}
                  py={2}
                  borderColor="gray.300"
                  borderRadius="md"
                  boxShadow="sm"
                  _focus={{ outline: "none", ring: "2px", ringColor: "green.500", borderColor: "green.500" }}
                />
              </div>
              {showLogs && (
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-md font-medium">处理进度</h3>
                    <Button 
                      size="xs" 
                      leftIcon={<CopyIcon />} 
                      onClick={handleCopyLogs}
                      colorScheme="gray"
                      variant="outline"
                    >
                      复制日志
                    </Button>
                  </div>
                  <Progress 
                    value={progress} 
                    size="sm" 
                    colorScheme="green"
                    borderRadius="md" 
                    mb={2}
                  />
                  <p className="text-xs text-gray-500 mb-2">AI处理可能需要一些时间，请耐心等待。</p>
                  <div className="h-60 bg-gray-100 border border-gray-300 rounded-md p-3 overflow-y-auto font-mono text-sm">
                    {logs.map((log, index) => (
                      <div key={index} className="py-1 whitespace-pre-wrap break-all">
                        {log}
                      </div>
                    ))}
                    <div ref={logEndRef} />
                  </div>
                  {downloadUrl && (
                    <div className="mt-2">
                      <div className="flex space-x-4 mb-4 items-center mt-6">
                        <a
                          href={downloadUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-green-600 hover:text-green-800 hover:underline flex items-center"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                          </svg>
                          下载结果
                        </a>
                        <button
                          type="button"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            console.log('预览文档，使用下载链接:', downloadUrl);
                            setIsPreviewOpen(true);
                          }}
                          className="text-green-600 hover:text-green-800 hover:underline flex items-center"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          预览
                        </button>
                        <a
                          href={`/workflow/preview-md?url=${encodeURIComponent(downloadUrl)}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-purple-600 hover:text-purple-800 hover:underline flex items-center"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                          </svg>
                          新窗口预览
                        </a>
                        
                        <Button
                          size="sm"
                          leftIcon={<CopyIcon />}
                          onClick={() => {
                            try {
                              if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(downloadUrl)
                                  .then(() => {
                                    toast({
                                      title: "链接已复制",
                                      description: "文档链接已成功复制到剪贴板",
                                      status: "success",
                                      duration: 3000,
                                      isClosable: true,
                                    });
                                  })
                                  .catch(err => {
                                    console.error('使用Clipboard API复制失败: ', err);
                                    fallbackCopyTextToClipboard(downloadUrl);
                                  });
                              } else {
                                fallbackCopyTextToClipboard(downloadUrl);
                              }
                            } catch (err) {
                              console.error('复制过程中出错: ', err);
                              fallbackCopyTextToClipboard(downloadUrl);
                            }

                            function fallbackCopyTextToClipboard(text) {
                              try {
                                const textArea = document.createElement('textarea');
                                textArea.value = text;
                                textArea.style.top = '0';
                                textArea.style.left = '0';
                                textArea.style.position = 'fixed';
                                textArea.style.opacity = '0';
                                
                                document.body.appendChild(textArea);
                                textArea.focus();
                                textArea.select();
                                
                                const successful = document.execCommand('copy');
                                document.body.removeChild(textArea);
                                
                                if (successful) {
                                  toast({
                                    title: "链接已复制",
                                    description: "文档链接已成功复制到剪贴板",
                                    status: "success",
                                    duration: 3000,
                                    isClosable: true,
                                  });
                                } else {
                                  throw new Error('复制命令执行失败');
                                }
                              } catch (err) {
                                console.error('回退复制方法失败: ', err);
                                toast({
                                  title: "复制失败",
                                  description: "无法将链接复制到剪贴板，请手动复制",
                                  status: "error",
                                  duration: 3000,
                                  isClosable: true,
                                });
                              }
                            }
                          }}
                          colorScheme="gray"
                          variant="outline"
                        >
                          复制链接
                        </Button>
                        
                        <Button
                          onClick={() => {
                            // 使用POST方式跳转，避免URL过长
                            navigateToNextPage('ai-done', downloadUrl, 'ai_api_url');
                          }}
                          colorScheme="green"
                          size="sm"
                          className="bg-green-600 hover:bg-green-700"
                        >
                          查看完整工作流 →
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div className="flex justify-between items-center mt-8">
                <button
                    type="button"
                    onClick={() => {
                        setActiveTab('url');
                        setIsLoading(false);
                        setMdUrl('');
                        setName('');
                        setDescription('');
                        setSelectedFile(null);
                        setError('');
                        setTaskId('');
                        setLogs([]);
                        setShowLogs(false);
                        setProgress(0);
                        setDownloadUrl('');
                        if (wsRef.current) {
                            wsRef.current.close();
                            wsRef.current = null;
                        }
                    }}
                    className={`px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={isLoading}
                >
                  取消/重置
                </button>
                <button
                  type="submit"
                  disabled={isLoading || (activeTab === 'url' && !mdUrl) || (activeTab === 'upload' && !selectedFile)}
                  className={`px-6 py-2 rounded-md text-white transition-colors duration-150 ${isLoading || (activeTab === 'url' && !mdUrl) || (activeTab === 'upload' && !selectedFile) ? 'bg-green-300 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700 focus:ring-4 focus:ring-green-300'}`}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      处理中...
                    </div>
                  ) : '开始处理'}
                </button>
              </div>
            </form>
          </div>
          
          <MarkdownPreviewModal
            isOpen={isPreviewOpen}
            onClose={() => setIsPreviewOpen(false)}
            downloadUrl={downloadUrl}
            title="AI-API 预览"
          />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 调试面板 - 通过URL参数debug=1控制显示 */}
      <WorkflowDebugPanel pageName="ai-api" />
    </MainLayout>
    </WorkflowPageWrapper>
  );
}
# 前端产品需求文档 - 学生端 - 题型组件 - 一期
文档说明：
  - 标注⚠️的是 AI 基于用户角度补充的点，可能并未在原始 PRD 中体现

## 1. 需求背景与目标

### 1.1 需求目标
通过该需求的实现，可以满足用户在日常学习过程中，对于练习、测验等场景下所需高频题型（单选题、多选题、填空题、解答题）的作答和学习需求，提升用户在不同学科和题型下的学习体验和效率。

### 1.2 用户价值
- 对于 **新初一、初二及新高一、高二用户**，解决了 **在不同学习场景下（如巩固练习）缺乏统一、规范、易用的在线题型作答和解析查看功能** 的痛点，带来了 **更便捷的在线练习体验、更清晰的题目解析反馈以及更个性化的学习辅助（如勾画、问一问）**。
- 对于 **新初一、初二及新高一、高二用户**，满足了 **对高频题型进行在线作答、获取即时反馈、查看详细解析、以及针对性巩固薄弱知识点** 的核心诉求，实现了 **提升学习效率、加深知识理解、及时发现并解决学习问题** 的用户收益。

## 2. 功能范围

### 2.1 核心功能
#### **[核心功能点1]:** 单选题作答与解析

##### **[核心功能点1.1]:** 单选题 - 初始状态展示
###### 界面布局与核心UI元素
- **布局参考：** [单选题初始状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZMHLbhLb3oLnPgx1baccZegqnRH.png) - 仅关注布局, [单选题初始状态-含图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XyfubkidNoqIObxbiiOcnuDdn3o.png) - 仅关注布局
- **布局原则：** 依据[单选题初始状态图片分析](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:229)，页面整体采用全屏展示。顶部栏包含返回按钮、计时器和答题进度条。内容区域左侧为题干区，展示题型标签、题目来源、题干文字及可能的几何图形（支持点击放大）；右侧为作答区，垂直排列选项A、B、C、D。底部操作栏包含勾画按钮、不确定按钮和提交按钮。
- **核心UI元素清单：**
    - 返回按钮
    - 计时器 (格式 00:00)
    - 答题进度条
    - 题型标签 (文字：“单选题”)
    - 题目来源标签 (例如：“2024 春•浙江期中”)
    - 题干内容区 (支持文字、图片)
    - 图片放大查看按钮 (若题干含图)
    - 选项A, B, C, D (包含选项标识和内容)
    - “勾画”按钮
    - “不确定”按钮
    - “提交”按钮 (置灰状态)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且没有作答时（即单选题没有选中选项）为初始状态。
    - **题型校验：**
        - 学科：语文、数学、英语、物理、化学、生物。
        - 录入题目作答方式为：单选题。
        - 非母子题。
    - **页面框架：**
        - 仅支持全屏展示。
        - **退出学习按钮：** 名称：“退出学习”。点击后退出至指定页面。
        - **作答计时：** 每题独立正计时（格式 00:00，上限 59:59）。用户进入题目开始计时。学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时。
    - **题板结构：** 左侧为题干区、右侧为作答区。
        - **题干区：**
            - **题目标签：** 题型标签：“单选题”。题目来源：数据源自题库中已录入题目。
            - **题干内容：** 数据源自题库中已录入题目。题干支持展示文字、图片（支持点开放大）。
            - **图片排版处理：** 参考[图片排版处理图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OQ9NbPQ5foqF1Gxc3Trc1FXMnfc.png)。
        - **作答区：**
            - **选项内容：** 数据来自题库中已录入题目。
            - **展示处理：** 超出一行后，折行展示。参考[选项折行展示图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LHNtbkAjxoyPGLxdKnWcqouWnRc.png)。含图选项处理参考[含图选项处理图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GOEWbMoOBo6fLExCJeScQRctnJf.png)。
    - **勾画组件：**
        - **入口：** 作答界面悬浮“勾画”按钮（常驻显示）。参考[勾画组件入口图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XKUNb9JLhoHRiRxX6OIc4vJDnVg.png)。
        - **交互：** 支持用户在作答环节时，点击“勾画”按钮唤起勾画组件，对题目进行勾画和标记。
        - **勾画工具：** 笔迹调整（颜色：蓝、红、黑；粗细：细、中、粗）、橡皮擦（粗细：细、中、粗）、撤回、恢复、一键清空。
        - **完成交互：** 点击“完成”，收起勾画组件，同时将勾画的内容自动保存并展示。
- **交互模式：**
    - 用户可查看题干和所有选项。
    - “提交”按钮初始状态为不可点击（置灰）。
    - 点击“不确定”按钮，按钮文字变为“放弃作答”。若此时再点击“放弃作答”，题目进入解析状态。若在“放弃作答”状态下点击任一选项，则“放弃作答”按钮回退至“不确定”，“提交”按钮变为可点击。
    - 点击“勾画”按钮可唤起或收起勾画工具栏。
- **反馈原则：**
    - 界面元素（如题干、选项）需清晰展示。
    - 按钮状态（可点击/不可点击）通过视觉样式明确区分。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点1.2]:** 单选题 - 作答状态交互
###### 界面布局与核心UI元素
- **布局参考：** [单选题作答状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AFLQbFyjFocz6PxFnrkcleXKnhd.png) - 仅关注布局
- **布局原则：** 依据[单选题作答状态图片分析](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:480)，整体布局与初始状态一致。主要变化在于选项区会显示用户的选择状态，底部“提交”按钮变为可点击状态。
- **核心UI元素清单：**
    - (同初始状态核心UI元素)
    - 选中状态的选项 (视觉上与其他选项区分)
    - “提交”按钮 (可点击状态)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且有作答时（即单选题选中唯一选项）为作答状态。
    - **批改方式：** 自动批改。
    - **作答方式：**
        - **选项：** 支持选中唯一一个选项，并展示选中状态。当有已选中选项时，点击其他选项，此时取消已选中，重新选中点击的选项。
    - **操作按钮交互：**
        - **不确定按钮：**
            - 按钮状态：可点击。
            - 交互：点击后按钮变更为“放弃作答”。点击“放弃作答”，题目进入解析状态。点击选项后，“放弃作答”按钮回退至“不确定”，“提交”按钮变为可点击。
        - **提交按钮：**
            - 按钮状态：可点击（当有选项被选中时）。
            - **第一次点击提交：**
                - 提交作答数据，校验作答结果。
                - **回答正确：** 弹出反馈弹窗，进入解析状态。
                - **回答错误：** 弹出反馈弹窗，停留在作答状态。仅展示“猜你想问”入口，不展示答案、解析。重置作答，取消选中状态，用户可重新作答，进行第二次提交。
            - **第二次点击提交：**
                - 再次提交作答数据，校验作答结果。
                - **回答正确：** 弹出反馈弹窗，进入解析状态。
                - **回答错误：** 弹出反馈弹窗，进入解析状态。
- **交互模式：**
    - 用户点击选项进行选择，界面实时更新选项的选中状态。
    - 选中选项后，“提交”按钮变为可点击。
    - 点击“提交”按钮后，根据作答次数和答案正误，系统给出相应反馈并转换状态。
- **反馈原则：**
    - 选项选中状态有清晰的视觉反馈。
    - 提交后，通过弹窗形式反馈作答结果（正确/错误）。
    - 首次答错后，界面元素（如答案、解析）的显隐会发生变化，引导用户二次作答或查看“猜你想问”。

**优先级：** P0
**依赖关系：** 核心功能点1.1

##### **[核心功能点1.3]:** 单选题 - 解析状态展示
###### 界面布局与核心UI元素
- **布局参考：** [单选题解析状态-答对](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VEZlblon3oPiDKxrcv1cUPwcnTe.png) - 仅关注布局, [单选题解析状态-答错](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_R8HwbvPb9o4741xn2gfcxSe6nO1.png) - 仅关注布局, [单选题解析状态-问一问](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_C1S1brNyUoAK20xiVlmcgg3InAf.png) - 仅关注布局
- **布局原则：** 依据[单选题解析状态图片分析(答对)](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:516)、[单选题解析状态图片分析(答错)](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:555)和[单选题解析状态图片分析(问一问)](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:597)，整体布局与作答状态相似。顶部计时器停止计时。作答区会展示选项占比、正确答案标识（对错颜色标记）。下方会展示题目解析内容。右上角出现“问一问”入口。底部操作按钮变为“继续”。
- **核心UI元素清单：**
    - (同作答状态核心UI元素，部分状态变化)
    - 选项占比标签 (例如：10.8%)
    - 正确/错误选项标记 (例如：绿色表示正确，红色表示错误)
    - “正确答案：X”文本
    - 题目解析内容区
    - “问一问”icon/按钮
    - “继续”按钮
    - (若触发“问一问”) 答疑组件弹窗，包含自由提问输入框、推荐问题、聊天记录区。

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：**
        - 当题目有作答（即单选题选中唯一选项）并点击“提交”正确或第二次“提交”后为解析状态。
        - 当题目没有作答，但点击了“不确定” - “放弃作答”后为解析状态。
    - **页面框架：**
        - **作答计时：** 停止计时，时间定格在提交时。
        - **反馈弹窗：** 具体样式参考“[巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f)”。
        - **选项占比：** 展示每个选项已作答用户的占比（格式 xx.x %）。数据取自该题目历史回答数据。冷启动策略：当题目使用次数大于20后，开始向用户展示占比数字。更新策略：每日更新使用次数大于20的题目的作答占比。
        - **答案：** 展示正确的选项文本。在选项上标记：正确选项标记为绿色；错误选项标记为红色。
        - **题目解析：** 内容数据源自题库中已录入题目，对应字段“题目解析”。
        - **问一问：**
            - **入口：** 右上角“问一问”icon，仅当题目进入解析状态后展示入口。
            - **交互：** 点击弹出答疑组件。
            - **展示：** 支持自由提问 + 模型生成的猜你想问问题（最多3个，可以为0）。由用户发起首轮对话。
    - **操作按钮：**
        - **继续按钮：**
            - **出现时机：** 题目进入解析状态。
            - **按钮状态：** 可点击。
            - **交互：** 点击后进入下一题/下一节内容。
- **交互模式：**
    - 用户可查看所有解析信息，包括选项占比、正确答案、题目解析。
    - 点击“问一问”icon可打开答疑弹窗，进行提问交互。
    - 点击“继续”按钮进入下一题目或环节。
- **反馈原则：**
    - 答案的对错状态通过颜色清晰标识。
    - 选项占以百分比形式直观展示。
    - “问一问”提供智能推荐和自由提问两种方式，满足不同用户需求。

**优先级：** P0
**依赖关系：** 核心功能点1.2

##### 用户场景与故事 (针对此核心功能点：单选题作答与解析)

###### [场景1：用户首次作答单选题并回答正确]
作为**初一学生**，我需要**在巩固练习中作答单选题，并能快速知道自己是否正确**以解决**知识点掌握不牢固**的痛点，从而**及时检验学习效果，增强学习信心**。

**前端界面与交互关键步骤：**
1.  **进入题目:** 用户进入单选题界面。
    *   **界面变化:** 展示题干、选项、“不确定”和置灰的“提交”按钮，计时器开始计时。
    *   **即时反馈:** 无特殊即时反馈。
2.  **选择答案:** 用户点击选项B。
    *   **界面变化:** 选项B高亮显示被选中状态，“提交”按钮变为可点击状态。
    *   **即时反馈:** 视觉上确认选项B被选中。
3.  **提交答案:** 用户点击“提交”按钮。
    *   **界面变化:** 弹出“回答正确”反馈弹窗，计时器停止。弹窗关闭后，界面显示选项占比，选项B标记为绿色，显示正确答案为B，展示题目解析内容，出现“问一问”入口和“继续”按钮。
    *   **即时反馈:** “回答正确”弹窗提示。

**场景细节补充：**
*   **前置条件:** 用户已登录并进入包含单选题的巩固练习环节。
*   **期望结果:** 用户顺利完成单选题作答，了解答案正确性，并可查看解析。
*   **异常与边界情况:**
    *   ⚠️网络请求失败：提交答案时若网络异常，应有友好提示“网络不给力，请稍后重试”，并允许用户在网络恢复后再次尝试提交。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (单选题核心作答流程):**
  ```mermaid
  flowchart TD
      A[用户进入单选题界面] --> B{查看题目与选项};
      B --> C[选择一个选项];
      C --> D{点击“提交”按钮};
      D -- 答案正确 --> E[弹出“回答正确”提示];
      E --> F[进入解析状态：显示答案、解析、“问一问”、“继续”按钮];
      D -- 答案错误 (首次) --> G[弹出“回答错误”提示];
      G --> H[停留在作答状态，允许二次作答，显示“猜你想问”];
      H --> I[用户再次选择选项];
      I --> J{点击“提交”按钮 (二次)};
      J -- 答案正确 --> E;
      J -- 答案错误 --> K[弹出“回答错误”提示];
      K --> F;
      B --> L[点击“不确定”按钮];
      L --> M{点击“放弃作答”按钮};
      M --> F;
      F --> N[点击“继续”按钮，进入下一题];
      F --> O[点击“问一问”，打开答疑弹窗];
  ```

###### [场景2：用户首次作答单选题答错，进行二次作答后答对]
作为**高一学生**，我需要**在单选题首次答错后有机会修正答案**以解决**对知识点理解不准确**的痛点，从而**通过再次思考加深理解并最终掌握**。

**前端界面与交互关键步骤：**
1.  **进入题目并选择错误答案:** 用户进入单选题界面，选择了错误选项A，并点击“提交”。
    *   **界面变化:** 弹出“回答错误”反馈弹窗。弹窗关闭后，界面停留在作答状态，选项A的选中状态被清除，显示“猜你想问”入口，但不显示答案和解析。
    *   **即时反馈:** “回答错误”弹窗提示。
2.  **进行二次作答:** 用户思考后，点击了正确选项C。
    *   **界面变化:** 选项C高亮显示被选中状态。
    *   **即时反馈:** 视觉上确认选项C被选中。
3.  **提交二次答案:** 用户再次点击“提交”按钮。
    *   **界面变化:** 弹出“回答正确”反馈弹窗，计时器停止。弹窗关闭后，界面显示选项占比，选项C标记为绿色，显示正确答案为C，展示题目解析内容，出现“问一问”入口和“继续”按钮。
    *   **即时反馈:** “回答正确”弹窗提示。

**场景细节补充：**
*   **前置条件:** 用户在单选题中首次作答错误。
*   **期望结果:** 用户通过二次作答机会答对题目，并能查看解析。
*   **异常与边界情况:**
    *   ⚠️用户在二次作答前点击“猜你想问”：应能正常弹出“猜你想问”交互界面。

**优先级：** 高
**依赖关系：** 核心功能点1.2

**Mermaid图示 (已包含在场景1的Mermaid图中)**

###### [场景3：用户不确定答案，选择放弃作答]
作为**初二学生**，我需要**在对单选题答案完全没有头绪时，可以选择放弃作答并直接查看答案解析**以解决**避免浪费时间在没有思路的题目上**的痛点，从而**快速学习解题方法和知识点**。

**前端界面与交互关键步骤：**
1.  **进入题目:** 用户进入单选题界面。
    *   **界面变化:** 展示题干、选项、“不确定”和置灰的“提交”按钮，计时器开始计时。
    *   **即时反馈:** 无。
2.  **点击不确定:** 用户点击“不确定”按钮。
    *   **界面变化:** “不确定”按钮文字变为“放弃作答”。
    *   **即时反馈:** 按钮文字变化。
3.  **点击放弃作答:** 用户点击“放弃作答”按钮。
    *   **界面变化:** 计时器停止。界面直接进入解析状态，显示选项占比，标记正确答案，展示题目解析内容，出现“问一问”入口和“继续”按钮。作答结果标记为“未作答”。
    *   **即时反馈:** 界面状态转换。

**场景细节补充：**
*   **前置条件:** 用户进入单选题界面，且未选择任何选项。
*   **期望结果:** 用户成功放弃作答，并能直接查看答案和解析。
*   **异常与边界情况:** 无。

**优先级：** 中
**依赖关系：** 核心功能点1.1, 1.3

**Mermaid图示 (已包含在场景1的Mermaid图中)**

###### [⚠️场景4：用户在查看解析时使用“问一问”功能]
作为**高二学生**，我需要**在查看单选题解析后，如果仍有疑问，可以使用“问一问”功能进行提问**以解决**解析内容无法完全解答我的个性化疑问**的痛点，从而**获得更精准的帮助，彻底理解题目**。

**前端界面与交互关键步骤：**
1.  **进入解析状态:** 用户已完成单选题作答（或放弃作答），界面处于解析状态。
    *   **界面变化:** 显示答案、解析、“问一问”入口和“继续”按钮。
    *   **即时反馈:** 无。
2.  **点击“问一问”:** 用户点击“问一问”icon。
    *   **界面变化:** 弹出答疑组件浮层，包含自由提问输入框和可能的推荐问题列表。
    *   **即时反馈:** 答疑组件弹出。
3.  **输入问题并发送/点击推荐问题:** 用户在输入框输入问题“为什么选项A是干扰项？”并发送，或者点击了一个推荐问题。
    *   **界面变化:** 聊天界面显示用户的问题，随后显示模型的回答。
    *   **即时反馈:** 聊天内容更新。
4.  **关闭“问一问”:** 用户点击答疑组件的关闭按钮。
    *   **界面变化:** 答疑组件关闭，返回到题目解析界面。
    *   **即时反馈:** 无。

**场景细节补充：**
*   **前置条件:** 用户处于单选题的解析状态。
*   **期望结果:** 用户能够通过“问一问”功能提问并获得解答。
*   **异常与边界情况:**
    *   ⚠️“问一问”服务异常：应提示用户“问一问服务暂时不可用，请稍后再试”。

**优先级：** 高
**依赖关系：** 核心功能点1.3

**Mermaid图示 (问一问交互流程):**
  ```mermaid
  sequenceDiagram
      participant User as 用户
      participant System as 系统
      participant AI_Assistant as AI助手

      User->>System: 进入单选题解析状态
      System-->>User: 显示答案、解析、"问一问"入口
      User->>System: 点击"问一问" icon
      System->>User: 弹出答疑组件 (含输入框/推荐问题)
      alt 用户自由提问
          User->>System: 输入问题并发送
          System->>AI_Assistant: 请求回答用户问题
          AI_Assistant-->>System: 返回答案
          System->>User: 在答疑组件中显示AI回答
      else 用户点击推荐问题
          User->>System: 点击推荐问题
          System->>AI_Assistant: 请求回答推荐问题
          AI_Assistant-->>System: 返回答案
          System->>User: 在答疑组件中显示AI回答
      end
      User->>System: 点击关闭答疑组件
      System->>User: 关闭答疑组件，返回解析界面
  ```
---
#### **[核心功能点2]:** 多选题作答与解析

##### **[核心功能点2.1]:** 多选题 - 初始状态展示
###### 界面布局与核心UI元素
- **布局参考：** [多选题初始状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OD1Zbr8o7oZVuwxZJSic0gvtnRh.png) - 仅关注布局
- **布局原则：** 依据[多选题初始状态图片分析](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:754)，布局与单选题初始状态基本一致。顶部栏包含返回按钮、计时器和答题进度条。内容区域左侧为题干区，展示题型标签（“多选”）、题目来源、题干文字及可能的图片；右侧为作答区，垂直排列选项。底部操作栏包含勾画按钮、“不确定”按钮和置灰的“提交”按钮。
- **核心UI元素清单：**
    - (同单选题初始状态核心UI元素，其中题型标签文字为“多选”)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且没有作答时（即多选题没有选中选项）为初始状态。
    - **题型校验：**
        - 学科：语文、数学、英语、物理、化学、生物。
        - 录入题目作答方式为：多选题。
        - 非母子题。
    - **页面框架：** (同单选题初始状态)
        - 仅支持全屏展示。
        - **退出学习按钮。**
        - **作答计时。**
    - **题板结构：** (同单选题初始状态)
        - 左侧为题干区、右侧为作答区。
        - **题干区：** 题目标签：“多选”。题目来源。题干内容（文字、图片，支持放大）。图片排版处理参考[图片排版处理图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WcCTbFpfIoG2suxgKFTcSibenIf.png)。
        - **作答区：** 选项内容。超出一行折行展示参考[选项折行展示图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OssBbq2HhojYHSxhgbZcH2L0nye.png)。含图选项处理参考[含图选项处理图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OSIIb8blyo9dXRxLIyTckAR0nsb.png)。
    - **勾画组件：** (同单选题初始状态) 参考[勾画组件入口图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SBribFrWVodELRxfTJvcZ6Tmnbh.png)。
- **交互模式：** (同单选题初始状态，但用户可以选择多个选项)
    - “提交”按钮初始状态为不可点击。
    - 点击“不确定”按钮，按钮文字变为“放弃作答”。
- **反馈原则：** (同单选题初始状态)

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点2.2]:** 多选题 - 作答状态交互
###### 界面布局与核心UI元素
- **布局参考：** [多选题作答状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_JjnbbhnpwogcHfxn8cucByuIn5d.png) - 仅关注布局
- **布局原则：** 依据[多选题作答状态图片分析](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:974)，整体布局与初始状态一致。选项区会显示用户的多项选择状态，底部“提交”按钮变为可点击状态。
- **核心UI元素清单：**
    - (同初始状态核心UI元素)
    - 选中状态的选项 (可多选，视觉上与其他选项区分)
    - “提交”按钮 (可点击状态)
    - (可能出现) 二次确认弹窗：“多选题答案有多个。操作按钮：我再想想，继续提交”

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且有作答时（即多选题选中至少一个选项）为作答状态。
    - **批改方式：** 自动批改。
    - **作答方式：**
        - **选项：** 支持选中一个或多个选项，并展示选中状态。当有已选中选项时，再次点击该选项，则取消选中状态。点击其他未选中选项，则选中点击的选项。
    - **操作按钮交互：**
        - **不确定按钮：** (同单选题作答状态)
        - **提交按钮：**
            - 按钮状态：可点击（当有选项被选中时）。
            - **特殊校验1 (首次提交和二次提交时，若选中选项仅为1个)：**
                - 弹窗二次确认。弹窗内容：“多选题答案有多个。”
                - 操作按钮：“我再想想”（点击后关闭弹窗），“继续提交”（点击后提交作答，进入相应反馈流程）。
            - **第一次点击提交 (或二次确认后继续提交)：**
                - 提交作答数据，校验作答结果。
                - **回答正确：** 反馈（IP + 积分反馈 + 文案），进入解析状态。
                - **回答错误 (包括部分正确、全错、多选了错误选项)：** 反馈（IP + 文案），停留在作答状态。仅展示“猜你想问”入口，不展示答案、解析。重置作答，取消选中状态，用户可重新作答。
            - **第二次点击提交 (或二次确认后继续提交)：**
                - 再次提交作答数据，校验作答结果。
                - **回答正确：** 弹出反馈弹窗，进入解析状态。
                - **回答错误 (包括部分正确、全错、多选了错误选项)：** 弹出反馈弹窗，进入解析状态。
- **交互模式：**
    - 用户点击选项进行选择或取消选择，界面实时更新选项的选中状态。
    - 选中至少一个选项后，“提交”按钮变为可点击。
    - 点击“提交”按钮，若只选一项，则触发二次确认弹窗。确认提交后，根据作答次数和答案正误，系统给出相应反馈并转换状态。
- **反馈原则：**
    - 选项选中状态有清晰的视觉反馈。
    - 提交时若只选一项，通过弹窗提示用户多选题特性。
    - 提交后，通过特定反馈（IP、文案、积分）和弹窗形式反馈作答结果。
    - 首次答错后，界面元素（如答案、解析）的显隐会发生变化，引导用户二次作答或查看“猜你想问”。

**优先级：** P0
**依赖关系：** 核心功能点2.1

##### **[核心功能点2.3]:** 多选题 - 解析状态展示
###### 界面布局与核心UI元素
- **布局参考：** [多选题解析状态-部分正确](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ExPobyMKgof0mVxHRglcLpCVnFf.png) - 仅关注布局, [多选题解析状态-全对](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XaGGbFPFcoRya1xzU0Ucr13PnOc.png) - 仅关注布局, [多选题解析状态-含漏选](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WZ5Obcouco1ajex01pocVqSPnRc.png) - 仅关注布局
- **布局原则：** 依据[多选题解析状态图片分析(部分正确)](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:1028)、[多选题解析状态图片分析(全对)](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:1095)和[多选题解析状态图片分析(含漏选)](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:1135)，整体布局与作答状态相似。顶部计时器停止计时。作答区会根据答案的正确情况（全对、部分正确-漏选、错误-全错/部分错/多选错误）以不同颜色和标记（如漏选标签）展示用户答案和正确答案。下方会展示题目解析内容。右上角出现“问一问”入口。底部操作按钮变为“继续”。
- **核心UI元素清单：**
    - (同作答状态核心UI元素，部分状态变化)
    - 正确选项标记 (绿色)
    - 错误选项标记 (红色)
    - 漏选标签 (针对未选中的正确选项)
    - “正确答案：X, Y, Z”文本
    - 题目解析内容区
    - “问一问”icon/按钮
    - “继续”按钮

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：**
        - 当题目有作答（即选中选项）并点击“提交”后为解析状态。
        - 当题目没有作答，但点击了“不确定” - “放弃作答”后为解析状态。
    - **页面框架：**
        - **作答计时：** 停止计时，时间定格在提交时。
        - **反馈弹窗：** 具体样式参考“[巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f)”。
        - **答案展示：**
            - **回答正确时：** 选中的正确选项颜色变更为绿色。
            - **回答部分正确（漏选）时：** 正确选项变更为绿色，未选中的正确选项展示漏选标签。
            - **回答错误时：**
                - **全错时：** 选错的选项颜色变更为红色，将正确选项变更为绿色。
                - **部分错时：** 选错的选项颜色变更为红色，未选中的正确选项变更为绿色并展示漏选标签。
                - **多选了错误选项时：** 正确选项变更为绿色，多选中的错误选项变更为红色。
            - **不确定 - 放弃作答时：** 正确选项变更为绿色。
        - **题目解析：** 内容数据源自题库中已录入题目，对应字段“题目解析”。
        - **问一问：** (同单选题解析状态)
    - **操作按钮：**
        - **继续按钮：** (同单选题解析状态)
- **交互模式：** (同单选题解析状态)
- **反馈原则：**
    - 答案的对错状态、漏选状态通过颜色和标签清晰标识。
    - “问一问”提供智能推荐和自由提问两种方式。

**优先级：** P0
**依赖关系：** 核心功能点2.2

##### 用户场景与故事 (针对此核心功能点：多选题作答与解析)

###### [场景1：用户作答多选题，部分正确（漏选）]
作为**高二学生**，我需要**在作答多选题时，如果漏选了部分正确答案，系统能明确指出哪些是漏选的**以解决**对知识点掌握不全面**的痛点，从而**准确了解自己的薄弱环节，进行针对性复习**。

**前端界面与交互关键步骤：**
1.  **进入题目并选择部分答案:** 用户进入多选题界面，正确答案为B,C,D，用户选择了B,C，并点击“提交”。
    *   **界面变化:** 弹出反馈（例如“部分正确”的IP和文案）。若为首次提交，则停留在作答状态，允许二次作答，显示“猜你想问”。若为二次提交或首次提交后直接进入解析，则计时器停止，界面显示选项B,C标记为绿色，选项D标记为绿色并带有“漏选”标签，展示题目解析内容，出现“问一问”入口和“继续”按钮。
    *   **即时反馈:** 相应的反馈提示。

**场景细节补充：**
*   **前置条件:** 用户已登录并进入包含多选题的练习环节。
*   **期望结果:** 用户了解自己漏选了哪些正确答案，并可查看解析。
*   **异常与边界情况:** 无。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (多选题核心作答流程):**
  ```mermaid
  flowchart TD
      A[用户进入多选题界面] --> B{查看题目与选项};
      B --> C[选择一个或多个选项];
      C --> D{点击“提交”按钮};
      D -- 选中选项仅为1个 --> Da[弹窗提示“多选题答案有多个”];
      Da -- 点击“我再想想” --> C;
      Da -- 点击“继续提交” --> Db{校验答案 (首次)};
      D -- 选中选项多于1个 --> Db;

      Db -- 答案全对 --> E[反馈正确(IP+积分+文案)];
      E --> F[进入解析状态：显示答案全对、解析、“问一问”、“继续”按钮];
      Db -- 答案部分正确/错误 (首次) --> G[反馈部分正确/错误(IP+文案)];
      G --> H[停留在作答状态，允许二次作答，显示“猜你想问”];
      H --> I[用户再次选择选项];
      I --> J{点击“提交”按钮 (二次)};
      J -- 选中选项仅为1个 --> Ja[弹窗提示“多选题答案有多个”];
      Ja -- 点击“我再想想” --> I;
      Ja -- 点击“继续提交” --> Jb{校验答案 (二次)};
      J -- 选中选项多于1个 --> Jb;

      Jb -- 答案全对 --> E;
      Jb -- 答案部分正确/错误 --> K[反馈部分正确/错误弹窗];
      K --> F; %% 无论二次对错均进入解析

      B --> L[点击“不确定”按钮];
      L --> M{点击“放弃作答”按钮};
      M --> F; %% 放弃作答直接进入解析，标记为未作答

      F --> N[点击“继续”按钮，进入下一题];
      F --> O[点击“问一问”，打开答疑弹窗];
  ```

###### [⚠️场景2：用户作答多选题时，选择了包含错误选项的答案]
作为**初三学生**，我需要**在多选题中如果选了错误选项，系统能明确标出错选的项和正确的项**以解决**概念混淆或理解偏差**的痛点，从而**清晰对比正误，纠正错误认知**。

**前端界面与交互关键步骤：**
1.  **进入题目并选择含错误项的答案:** 用户进入多选题，正确答案为A,B，用户选择了A,C (C为错误项)，并点击“提交”。
    *   **界面变化:** 弹出反馈。若为首次提交，则停留在作答状态。若为二次提交或直接进入解析，则计时器停止，界面显示选项A标记为绿色，选项C标记为红色，选项B标记为绿色并可能带“漏选”标签（如果B未被选），展示题目解析，出现“问一问”和“继续”按钮。
    *   **即时反馈:** 相应的反馈提示。

**场景细节补充：**
*   **前置条件:** 用户在多选题中作答，且答案中包含错误选项。
*   **期望结果:** 用户能清晰看到自己选错的选项以及所有正确答案。
*   **异常与边界情况:** 无。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (已包含在场景1的Mermaid图中)**
---
#### **[核心功能点3]:** 填空题（非英语，自评）作答与解析

##### **[核心功能点3.1]:** 填空题（非英语，自评） - 初始状态展示
###### 界面布局与核心UI元素
- **布局参考：** [填空题初始状态-键盘作答](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OX5hbeeyEoyqkbxDAj5c6MbInuc.png) - 仅关注布局, [填空题初始状态-拍照作答](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VjJhbuoEqoTsALxEl2Mc0MkGnEc.png) - 仅关注布局
- **布局原则：** 依据[填空题初始状态图片分析(键盘)](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:1283)和[填空题初始状态图片分析(拍照)](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:1320)，页面全屏展示。顶部包含返回、计时器。左侧为题干区，展示题型标签（“填空”）、题目来源、题干内容（可含图片和作答区标记①②...）。右侧为作答区，包含作答区选项卡（若多于一个空）、当前作答方式的输入区域（键盘输入提示或拍照上传入口）、“不确定”和置灰的“提交”按钮。
- **核心UI元素清单：**
    - 返回按钮
    - 计时器
    - 题型标签 (“填空”)
    - 题目来源标签
    - 题干内容区 (可含图片、作答区标记如①, ②)
    - (若多于一空) 作答区选项卡 (如 "1", "2", "3"...)
    - 作答方式切换按钮 (例如：“键盘作答”、“拍照作答”)
    - 当前作答方式的输入区域 (例如：“点击输入答案”提示文本框，或“拍照上传”按钮及提示“最多可以传X张”)
    - “不确定”按钮
    - “提交”按钮 (置灰)
    - “勾画”按钮 (参考[勾画组件入口图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AMqAbcFa3oYtQBxBQKNcVBeHnEh.png))

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且没有作答时为初始状态。
    - **题型校验：**
        - 录入学科：语文、数学、物理、化学、生物。
        - 录入题目作答方式为：填空题。
        - 非母子题。
    - **页面框架：** (同单选题) 全屏展示，退出学习，作答计时。
    - **题板结构：** 左侧题干区，右侧作答区。
        - **题干区：** 题型标签“填空”，题目来源，题干内容（文字、图片、作答区）。若题干存在多个作答区，作答区需标记顺序（从左到右从上到下标记1、2、3...）。点击题干上作答区标记，右侧作答区自动定位到选中位置。
        - **作答区：**
            - **填空区域：** 以输入框的形式插入题干中（此为视觉表现，实际输入在右侧作答区）。长度以答案长度作为作答区输入框长度。插入位置数据源自题库。
            - **作答区选项卡：** 对应题干中的作答区数量和顺序，只有一个空时，不展示选项卡。点击作答区选项卡中的选项，左侧题干区自动定位到选中位置。
    - **勾画组件：** (同单选题)
- **交互模式：**
    - 用户可查看题干，包含的填空位置有标记。
    - 若有多个填空，可通过点击题干中的标记或右侧的作答区选项卡切换当前作答的填空。
    - 用户可以选择作答方式（键盘输入/拍照上传）。
    - “提交”按钮初始置灰。
- **反馈原则：**
    - 当前活动的填空标记和对应的作答区选项卡应有高亮提示。
    - 作答方式切换有明确的视觉反馈。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点3.2]:** 填空题（非英语，自评） - 作答状态交互
###### 界面布局与核心UI元素
- **布局参考：** [填空题作答状态-键盘](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BnRUbC553og3KZxcBRvcxjsUnGg.png) - 仅关注布局, [填空题作答状态-键盘输入中](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_YxHrbOcU6oqZT0xqByEc7trInNe.png) - 仅关注布局, [填空题作答状态-多空切换](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VZF3bsNjQot6Pxxk2kvcRIF0nX7.png) - 仅关注布局, [填空题作答状态-文字过多](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Ne0Gb7W5voQfHrxhdO6cVe6Vnub.png) - 仅关注布局, [填空题作答状态-拍照上传入口](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RIdZbg5cRoilSkxc0SAcjh2kn8b.png) - 仅关注布局, [填空题作答状态-拍照上传示例](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GUhdbGw2HoYAktxdBOWcQMCQnVd.png) - 仅关注布局, [填空题作答状态-已上传图片](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EKFDbnuipoQDyNxtKK2cD2Hbnhg.png) - 仅关注布局
- **布局原则：** 依据相关图片分析，作答方式选择后，右侧作答区会显示对应作答方式的界面。键盘作答时，会调起虚拟键盘，并在键盘上方有输入内容展示区。拍照作答时，会显示已上传图片列表和继续上传的入口。题干中的填空处可能会显示已输入的内容预览。“提交”按钮变为可点击。
- **核心UI元素清单：**
    - (同初始状态核心UI元素)
    - **键盘作答模式下：**
        - 虚拟键盘
        - 键盘上方输入内容展示区
        - “确定”按钮（键盘上）
    - **拍照作答模式下：**
        - 已上传图片缩略图 (带删除按钮、可点击放大)
        - “拍照上传”按钮/入口 (若未达到数量上限)
        - 图片上传进度条/状态提示 (参考[上传状态图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G967bbzgsoddEJxIow8cNa44nBg.png))
        - 图片编辑/裁剪界面 (参考[图片编辑图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BkKIbv3lrofEY9xsFeqcjyBVnHf.png))
    - “提交”按钮 (可点击)
    - (可能出现) 提交确认弹窗 (参考[提交确认弹窗图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BKbZbJQxdoQ7GaxW7hqc96ymnVc.png))

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且有至少一道题目作答时为作答状态。
    - **作答方式切换：** 单题内仅可选择一种方式作答。切换时，若当前作答方式已有内容，切换后展示新的作答方式界面，原有内容不删除，方便用户切回。
        - 键盘作答切到拍照作答：题干上清空已作答内容预览。
        - 拍照作答切到键盘作答：不删除已上传图片。
    - **键盘作答（默认）：**
        - **入口：** 选择键盘作答。提示“点击输入答案”。
        - **交互：** 点击左侧题干作答区标记或右侧作答区“点击输入答案”，该区域选中并调起键盘。选项卡作答区序号选中，题干中对应序号选中。输入文字时，作答区不直接展示，在键盘上方新增区域展示输入文字（最多3行，超长换行）。点击键盘“确定”或点击键盘外其他区域（非其他填空区），键盘收起，保存输入。若点击的是其他填空区，键盘不收起，输入框内容更新为该填空区内容。
    - **拍照作答：**
        - **入口：** 选择拍照作答模式。最大上传数量3张。
        - **交互：** 点击拍照调起摄像头。拍摄界面可关闭，有16:9辅助线。拍摄后可调整框选、旋转图片。点击上传，显示上传中/上传失败（可重试）。已上传图片支持放大、删除。
        - **内容检测：** 接入大模型识别。异常内容（与题目无关）不保存图片，弹窗提示“请上传和题目相关的内容”（3秒后消失）。
    - **操作按钮交互：**
        - **不确定按钮：** (同单选题)
        - **提交按钮：**
            - 按钮状态：可点击。
            - **校验与交互：**
                - 若所有空都有内容（文字或图片），则提交作答数据，进入解析状态。
                - 若有空未作答：
                    - **全部未作答：** 弹窗提示“本题未作答，确认提交吗？”。操作按钮：“我再想想”（关闭弹窗），“继续提交”（进入解析状态）。
                    - **部分未作答：** 弹窗提示“还有填空未作答，确认提交吗？”。操作按钮同上。
- **交互模式：**
    - 用户在选定作答方式后，进行内容输入或图片上传。
    - 键盘输入时，焦点在哪个填空，输入内容就对应哪个填空。
    - 拍照上传时，图片列表会实时更新。
    - 点击“提交”时，根据作答完整性可能会有二次确认。
- **反馈原则：**
    - 当前作答的填空、选中的作答方式有清晰高亮。
    - 键盘输入时，有独立的输入预览区。
    - 图片上传有明确的进度和结果反馈。
    - 提交不完整答案时，通过弹窗进行用户确认。

**优先级：** P0
**依赖关系：** 核心功能点3.1

##### **[核心功能点3.3]:** 填空题（非英语，自评） - 解析状态展示
###### 界面布局与核心UI元素
- **布局参考：** [填空题解析状态-自评](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LrOQbLpKyo8y2Axm6a4cbZgYnmh.png) - 仅关注布局
- **布局原则：** 依据[填空题解析状态图片分析](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:1834)，左侧题干区展示用户答案（文字或图片）和标准答案。右侧为自评区，包含“核对答案”标题、提示语、作答区选项卡（显示各空自评结果）、自评按钮（我答对了、部分答对、我答错了）。下方为题目解析和“问一问”入口。底部为“继续”按钮。
- **核心UI元素清单：**
    - (同作答状态核心UI元素，部分状态变化)
    - 左侧题干区：用户答案展示（文字或图片）、标准答案展示
    - 右侧自评区：
        - “核对答案”标题
        - 提示语：“对待错误是进步的第一步，相信你会认真对待!”
        - 作答区选项卡 (显示各空自评结果：待自评、答对√、答错×、部分答对) 参考[自评结果选项卡图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G0CRbeT6YoeD0gxcg9ncZerynGk.png)
        - 自评按钮：“✔ 我答对了”，“✖ 部分答对”，“✖ 我答错了” (按钮样式参考[自评按钮状态图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G0CRbeT6YoeD0gxcg9ncZerynGk.png))
    - 题目解析内容区
    - “问一问”icon/按钮
    - “继续”按钮 (状态根据自评完成度变化)
    - (可能出现) 提示Toast：“认真核对哦～” (参考[提示Toast图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SD3zblHhDoNnHDxG6a0cFKXTnRd.png))

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当题目有作答并点击“提交”后，或没有作答但点击“不确定”-“放弃作答”后，进入解析状态。
    - **批改方式：** 自评。
    - **展示内容：**
        - **自评模块：**
            - 标题：“核对答案”。提示：“对待错误是进步的第一步，相信你会认真对待!”。
            - 自评题目减少掌握度和积分赋予。
            - **异常行为监控：** 连续3题，批改时长均小于2秒时，toast提示“认真核对哦”，展示时长2s。
            - **选项卡：** 对应题干作答区数量和顺序（一空时不展示）。点击选项卡定位到对应空的自评。自评后选项卡展示对应结果（符号+文字）。
            - **自评按钮交互：** 点击不同自评选项按钮，展示选中效果。单选逻辑。自评后自动进入下一个待自评题目；若是最后一题，则判断是否有前置未自评的，跳转到第一个未自评的。
            - 作答区选项卡展示自评结果（待自评、答对、答错、部分答对）。
            - 若用户有自评未完成，“提交”按钮（此处的提交指完成所有自评后的确认，或理解为“继续”按钮的启用条件）不可点击。
            - （所有空）提交自评后展示整体自评结果，仍可修改。
            - 提交后进行作答反馈（参考[巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f)）。
        - **题目解析：** 内容来自题库。
        - **问一问：** (同单选题)
    - **操作按钮：**
        - **继续按钮：** 出现时机：题目进入解析状态。按钮状态：可点击。交互：点击后进入下一题/下一节内容。
- **交互模式：**
    - 用户对照标准答案，逐个或通过选项卡选择填空进行自评。
    - 自评结果实时更新在选项卡和自评按钮上。
    - 所有填空自评完成后，“继续”按钮才可点击（或根据PRD，提交后按钮变为“继续”）。
- **反馈原则：**
    - 自评状态有清晰的视觉反馈。
    - 监控快速自评行为并给予提示。
    - “问一问”提供支持。

**优先级：** P0
**依赖关系：** 核心功能点3.2

##### 用户场景与故事 (针对此核心功能点：填空题（非英语，自评）作答与解析)

###### [场景1：用户使用键盘输入方式作答多空填空题，并进行自评]
作为**初中语文学生**，我需要**使用键盘为古诗词填空题的多个空档输入答案，并在提交后对照标准答案自己进行评价**以解决**手写不便和无法即时了解答案准确性**的痛点，从而**高效完成练习并准确评估自己的掌握程度**。

**前端界面与交互关键步骤：**
1.  **进入题目并选择键盘作答:** 用户进入一个含3个填空的语文填空题，默认为键盘作答方式。
    *   **界面变化:** 左侧题干显示3个填空标记，右侧作答区显示题号选项卡[1][2][3]，当前选中[1]，下方为“点击输入答案”区域。
    *   **即时反馈:** 无。
2.  **逐个输入答案:** 用户点击作答区选项卡[1]，调起键盘输入答案；然后点击选项卡[2]，输入答案；再点击选项卡[3]，输入答案。
    *   **界面变化:** 每次输入时，键盘上方显示输入内容，题干对应位置可能预览答案。切换选项卡时，输入焦点随之改变。
    *   **即时反馈:** 输入内容实时显示。
3.  **提交答案:** 用户完成所有填空后，点击“提交”按钮。
    *   **界面变化:** 进入解析状态。左侧题干显示用户答案和标准答案。右侧显示自评模块，选项卡[1][2][3]标记为“待自评”。
    *   **即时反馈:** 界面转换。
4.  **进行自评:** 用户对照答案，点击选项卡[1]，然后点击“我答对了”；点击选项卡[2]，点击“部分答对”；点击选项卡[3]，点击“我答错了”。
    *   **界面变化:** 选项卡[1]显示绿色√，选项卡[2]显示橙色符号，选项卡[3]显示红色×。自评按钮状态随点击改变。
    *   **即时反馈:** 自评结果实时反馈。
5.  **完成自评并继续:** 所有空自评完毕，用户点击“继续”按钮。
    *   **界面变化:** 进入下一题。
    *   **即时反馈:** 无。

**场景细节补充：**
*   **前置条件:** 用户已登录，进入填空题练习。
*   **期望结果:** 用户能顺利完成多空填空题的键盘作答和自评。
*   **异常与边界情况:**
    *   ⚠️用户未完成所有填空即提交：按PRD描述，弹窗提示“还有填空未作答，确认提交吗？”。
    *   ⚠️用户未完成所有自评即点击“继续”：若“继续”按钮与自评完成度挂钩，则按钮应为不可点击状态，或点击后提示先完成自评。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (填空题-非英语自评-核心流程):**
  ```mermaid
  flowchart TD
      A[用户进入填空题] --> B{选择作答方式};
      B -- 键盘输入 --> C[逐个填空输入内容];
      B -- 拍照上传 --> D[上传图片答案];
      C --> E{点击“提交”};
      D --> E;
      A --> F[点击“不确定”];
      F --> G{点击“放弃作答”};
      G --> H[进入解析状态(未作答)];
      E -- 作答不完整 --> Ea[弹窗确认提交];
      Ea -- 我再想想 --> B;
      Ea -- 继续提交 --> H;
      E -- 作答完整 --> H;

      H --> I[显示用户答案与标准答案];
      I --> J[用户逐个进行自评];
      J -- 所有空自评完成 --> K[显示整体自评结果];
      K --> L[点击“继续”进入下一题];
      H --> M[查看题目解析];
      H --> N[使用“问一问”];
  ```
---
#### **[核心功能点4]:** 英语填空题（自动批改）作答与解析

##### **[核心功能点4.1]:** 英语填空题 - 初始状态展示
###### 界面布局与核心UI元素
- **布局参考：** [英语填空题初始状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_MQbDb84rGocUN9xlXG3cq4ZVnDz.png) - 仅关注布局
- **布局原则：** 依据[英语填空题初始状态图片分析](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:2050)，与非英语填空题初始状态类似。左侧题干区，右侧作答区（包含选项卡和输入提示）。
- **核心UI元素清单：** (同非英语填空题初始状态，题型标签为“填空”，学科限定为英语)
    - “勾画”按钮 (参考[勾画组件入口图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_W1HJbyVIko755gx2bfXcO1dznjd.png))

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且没有作答时（即填空作答区没有输入内容）为初始状态。
    - **题型校验：**
        - 录入学科：英语。
        - 录入题目作答方式为：填空题。
        - 非母子题。
    - **页面框架、题板结构、勾画组件：** (同非英语填空题初始状态)
- **交互模式：** (同非英语填空题初始状态，作答方式限定为键盘输入)
- **反馈原则：** (同非英语填空题初始状态)

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点4.2]:** 英语填空题 - 作答状态交互
###### 界面布局与核心UI元素
- **布局参考：** [英语填空题作答状态-键盘输入](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OBFUbYqHAoKd5Yxjq7scW5ALn5f.png) - 仅关注布局, [英语填空题作答状态-输入内容展示](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FQz8bCpQzoaRrixqwWkccrZlnvc.png) - 仅关注布局
- **布局原则：** 依据相关图片分析，用户通过键盘输入答案，输入内容在键盘上方展示。
- **核心UI元素清单：**
    - (同初始状态核心UI元素)
    - 虚拟键盘
    - 键盘上方输入内容展示区
    - “提交”按钮 (可点击)
    - (可能出现) 提交确认弹窗

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且有作答时（即填空作答区输入了内容）为作答状态。
    - **批改方式：** 英语学科 - 自动批改。
    - **作答方式（键盘作答仅支持一种）：**
        - 支持通过键盘输入文字将内容填充到填空的对应位置。
        - 点击左侧题干作答区标记或右侧作答区“点击输入答案”，该区域选中并调起键盘。选项卡作答区序号选中，题干中对应序号选中，展示选中态。
        - 当对应作答区输入内容后，展示已输入状态。
        - 输入文字时，选项中不展示文字内容，键盘上方增加展示输入内容的区域。
        - 点击键盘“确定”或点击键盘区域外其他区域，收起键盘，取消选中状态，保存输入文字。若点击的是其他填空区，键盘不收起，输入框内容更新。
    - **操作按钮交互：**
        - **不确定按钮：** (同单选题)
        - **提交按钮：**
            - 按钮状态：可点击。
            - **校验与交互：** (同非英语填空题提交校验逻辑)
                - 有文字输入则提交。
                - 有空未输入则弹窗确认（“本题未作答，确认提交吗？”或“还有填空未作答，确认提交吗？”）。
- **交互模式：**
    - 用户通过键盘为每个填空输入答案。
    - 点击“提交”时，根据作答完整性可能会有二次确认。
- **反馈原则：**
    - 当前作答的填空有清晰高亮。
    - 键盘输入时，有独立的输入预览区。
    - 提交不完整答案时，通过弹窗进行用户确认。

**优先级：** P0
**依赖关系：** 核心功能点4.1

##### **[核心功能点4.3]:** 英语填空题 - 解析状态展示
###### 界面布局与核心UI元素
- **布局参考：** [英语填空题解析状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_T6xIbv5nAoy3xjxBYuocbx9LnUc.png) - 仅关注布局
- **布局原则：** 依据[英语填空题解析状态图片分析](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:2216)，左侧题干区展示用户答案和标准答案，并标记对错。右侧显示“正确答案”、“你的答案”、题目解析、“问一问”入口。底部为“继续”按钮。
- **核心UI元素清单：**
    - (同作答状态核心UI元素，部分状态变化)
    - 左侧题干区：用户答案（带对错标记）、标准答案
    - 右侧内容区：
        - “正确答案：XXX”
        - “你的答案：YYY”（若未作答则为“未作答”）
        - 题目解析内容区
        - “问一问”icon/按钮 (仅在回答错误后出现)
    - “继续”按钮
    - 反馈弹窗

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当题目有作答并点击“提交”后，或没有作答但点击“不确定”-“放弃作答”后，进入解析状态。
    - **批改方式：** 自动批改。
    - **展示内容：**
        - **反馈弹窗：** 具体样式参考“[巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f)”。
        - **答案：** 展示“正确答案：”和“你的答案：”。放弃作答时，对应作答区标记为不确定突出展示，你的答案为“未作答”。
        - **题目解析：** 内容来自题库。
        - **问一问：** (同单选题，仅在回答错误后出现)
    - **操作按钮：**
        - **继续按钮：** (同单选题)
- **交互模式：**
    - 用户可查看自动批改结果、标准答案和解析。
    - 若答错，可使用“问一问”。
- **反馈原则：**
    - 自动批改结果通过颜色和标记清晰展示。
    - 反馈弹窗提供即时结果反馈。

**优先级：** P0
**依赖关系：** 核心功能点4.2

##### 用户场景与故事 (针对此核心功能点：英语填空题作答与解析)

###### [场景1：用户作答英语填空题，部分空答对，部分空答错]
作为**高中英语学生**，我需要**在完成英语填空题后，系统能自动批改并明确指出哪些空正确、哪些空错误，并给出正确答案**以解决**无法即时获得准确反馈**的痛点，从而**快速订正错误，巩固记忆**。

**前端界面与交互关键步骤：**
1.  **进入题目并作答:** 用户进入一个含3个空的英语填空题，依次输入答案，其中第1、3空正确，第2空错误。
    *   **界面变化:** 用户输入时，键盘上方显示输入内容。
    *   **即时反馈:** 无。
2.  **提交答案:** 用户点击“提交”按钮。
    *   **界面变化:** 弹出反馈弹窗（如“部分正确”）。关闭弹窗后，进入解析状态。左侧题干中，第1、3空答案旁标记绿色√，第2空答案旁标记红色×，并显示正确答案。右侧显示“正确答案：[空1答案], [空2正确答案], [空3答案]”，“你的答案：[空1答案], [空2错误答案], [空3答案]”，题目解析，以及“问一问”入口。
    *   **即时反馈:** 反馈弹窗。

**场景细节补充：**
*   **前置条件:** 用户已登录，进入英语填空题练习。
*   **期望结果:** 用户能清晰了解各空的对错情况和正确答案。
*   **异常与边界情况:** 无。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (英语填空题-自动批改-核心流程):**
  ```mermaid
  flowchart TD
      A[用户进入英语填空题] --> B[键盘输入各空答案];
      B --> C{点击“提交”};
      A --> D[点击“不确定”];
      D --> E{点击“放弃作答”};
      E --> F[进入解析状态(未作答)];
      C -- 作答不完整 --> Ca[弹窗确认提交];
      Ca -- 我再想想 --> B;
      Ca -- 继续提交 --> F_auto_correct[自动批改];
      C -- 作答完整 --> F_auto_correct;

      F_auto_correct -- 全部正确 --> G[反馈正确弹窗];
      F_auto_correct -- 部分正确/全部错误 --> H[反馈部分正确/错误弹窗];
      
      G --> I[进入解析状态: 显示答案全对、解析];
      H --> J[进入解析状态: 显示各空对错、正确答案、解析、“问一问”入口];
      F --> K[进入解析状态: 显示未作答、正确答案、解析];

      I --> L[点击“继续”进入下一题];
      J --> L;
      K --> L;
      J --> M[使用“问一问”];
  ```
---
#### **[核心功能点5]:** 解答题作答与解析

##### **[核心功能点5.1]:** 解答题 - 初始状态展示
###### 界面布局与核心UI元素
- **布局参考：** [解答题初始状态-拍照作答默认](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EkLgbJkt5oeaKBxfb7Tcj4yenDf.png) - 仅关注布局, [解答题初始状态-键盘作答](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Q1UZb24j7oWaejxFENCc1XZmn9g.png) - 仅关注布局 (注意此图右侧为拍照上传入口，应理解为切换到键盘作答模式时的右侧布局)
- **布局原则：** 依据[解答题初始状态图片分析(拍照默认)](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:2358)和[解答题初始状态图片分析(键盘)](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:2398)，页面全屏。左侧题干区，展示题型标签（“解答”）、来源、题干内容（可含图）。右侧作答区，包含作答方式切换按钮（“拍照作答”默认选中，“键盘作答”）、当前作答方式的输入区域（拍照作答则为上传入口和示例，键盘作答则为文本输入框提示）、“不确定”和置灰的“提交”按钮。
- **核心UI元素清单：**
    - 返回按钮
    - 计时器
    - 题型标签 (“解答”)
    - 题目来源标签
    - 题干内容区 (可含图片)
    - 作答方式切换按钮 (“拍照作答”, “键盘作答”)
    - **拍照作答模式下：**
        - 作答示例图片 (可选)
        - “拍照上传”按钮/入口 (含提示“最多可以传X张”)
    - **键盘作答模式下：**
        - “点击输入答案”提示文本框
    - “不确定”按钮
    - “提交”按钮 (置灰)
    - “勾画”按钮 (参考[勾画组件入口图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GWmGbKabgoupdgxVeF0cSdg5nbh.png))

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且没有作答时为初始状态。
    - **题型校验：**
        - 录入学科：语文、数学、英语、物理、化学、生物。
        - 录入题目作答方式为：解答题。
        - 非母子题。
    - **页面框架、题板结构、勾画组件：** (同填空题)
    - **输入方式选择（单题内仅可选择一种方式作答）：**
        - 切换作答方式时，逻辑同非英语填空题。
        - **拍照作答（默认）：** 支持用户通过拍照作答。展示作答示例。展示拍照作答入口。
        - **键盘作答：** 支持用户通过键盘输入文本。提示“点击输入答案”。展示文本输入框和键盘。
- **交互模式：**
    - 用户可查看题干。
    - 用户可以选择作答方式（拍照上传/键盘输入），默认为拍照作答。
    - “提交”按钮初始置灰。
- **反馈原则：**
    - 选中的作答方式有高亮提示。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点5.2]:** 解答题 - 作答状态交互
###### 界面布局与核心UI元素
- **布局参考：** [解答题作答状态-已上传图片](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZupTbomF3oZI4jxuRhzcqQjcnPF.png) - 仅关注布局, [解答题作答状态-键盘输入中](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_HlTBbPcf6o4K4vxxGlscpUlenWh.png) - 仅关注布局
- **布局原则：** 依据相关图片分析，选择拍照作答后，右侧显示已上传图片列表和继续上传入口。选择键盘作答后，右侧为文本输入区域，可调起键盘。
- **核心UI元素清单：**
    - (同初始状态核心UI元素)
    - **拍照作答模式下：**
        - 已上传图片缩略图 (带删除按钮、可点击放大)
        - “拍照上传”按钮/入口 (若未达到数量上限4张)
        - 图片上传进度条/状态提示 (参考[上传状态图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_R6B1bPOmBoLL91xE84GcTmmMnfe.png))
        - 图片编辑/裁剪界面 (参考[图片编辑图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RkOBbwIHzoVq1axoMMXcUPWynZf.png)，注意辅助线为九宫格)
    - **键盘作答模式下：**
        - 文本输入框 (显示已输入内容)
        - 虚拟键盘
    - “提交”按钮 (可点击)
    - (可能出现) 提交确认弹窗

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当进入题目且有作答时（即拍照上传了图片或键盘输入了内容）为作答状态。
    - **批改方式：** 自评，核对答案。
    - **作答方式（单题内仅可选择一种方式作答）：**
        - **拍照作答（默认）：**
            - **入口：** 选择拍照作答。最大上传数量4张。
            - **交互：** 点击拍照调起摄像头。拍摄界面可关闭，有横竖九宫格辅助线。拍摄后可调整框选、旋转图片。点击上传，显示上传中/上传失败（可重试）。已上传图片支持放大、删除。
            - **内容检测：** 接入大模型识别。异常内容不保存，弹窗提示“请上传和题目相关的内容”（3秒后消失）。
        - **键盘作答：**
            - **入口：** 选择键盘作答。提示“点击输入答案”。
            - **交互：** 点击作答区调起键盘。输入文字时，作答区展示内容，超一行换行（最多XX行）。点击键盘外其他区域，键盘收起，保存输入。
    - **操作按钮交互：**
        - **不确定按钮：** (同单选题)
        - **提交按钮：**
            - 按钮状态：可点击。
            - **校验与交互：**
                - 当有上传图片（拍照）或有文字输入（键盘）时，提交作答数据，进入解析状态。
                - 当没有上传图片或没有文字输入时，弹窗提示“本题未作答，确认提交吗？”。操作按钮：“我再想想”（关闭弹窗），“继续提交”（进入解析状态）。
- **交互模式：**
    - 用户在选定作答方式后，进行内容输入或图片上传。
    - 点击“提交”时，若无内容则二次确认。
- **反馈原则：**
    - 图片上传有明确的进度和结果反馈。
    - 提交空答案时，通过弹窗进行用户确认。

**优先级：** P0
**依赖关系：** 核心功能点5.1

##### **[核心功能点5.3]:** 解答题 - 解析状态展示
###### 界面布局与核心UI元素
- **布局参考：** [解答题解析状态](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_TcU4bQiQEoeDBsxwGdUc72yPnrb.png) - 仅关注布局
- **布局原则：** 依据[解答题解析状态图片分析](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md:2628)，左侧题干区。右侧显示“正确答案”、“我的答案”（图片或文字）、自评模块（标题、副标题、自评选项按钮）、题目解析、“问一问”入口。底部为“继续”按钮。
- **核心UI元素清单：**
    - (同作答状态核心UI元素，部分状态变化)
    - 右侧内容区：
        - “正确答案：XXX” (文字或图片形式)
        - “我的答案：” (展示用户上传的图片列表或输入的文字)
        - **自评模块：**
            - 标题：“核对答案”
            - 副标题：“对待错误是进步的第一步，相信你会认真对待!”
            - 自评选项按钮：“✔ 我答对了”，“✖ 部分答对”，“✖ 我答错了” (参考[自评按钮状态图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GQA1bq8YEoUpTCxLeFbcRT45npd.png))
        - 题目解析内容区
        - “问一问”icon/按钮
    - “继续”按钮 (状态根据自评完成度变化)

###### 功能需求与交互原则
- **功能描述：**
    - **状态判断：** 当题目有作答并点击“提交”后，或没有作答但点击“不确定”-“放弃作答”后，进入解析状态。
    - **展示内容：**
        - **正确答案：** 数据来自题库。
        - **我的答案：** 已上传的图片（可放大）或已输入的文字。
        - **自评模块：**
            - 标题：“核对答案”。副标题：“对待错误是进步的第一步，相信你会认真对待!”。
            - **选项：** “✔ 我答对了”，“✖ 部分答对”，“✖ 我答错了”。
            - **交互：** 点击不同选项按钮展示选中效果。单选逻辑。提交自评后仍可修改。点击“继续”进入下一题/课。
        - **题目解析：** 内容来自题库。
        - **问一问：** (同单选题)
    - **操作按钮：**
        - **继续按钮：**
            - **出现时机：** 题目进入解析状态。
            - **按钮状态：** 自评已完成，可点击。自评未完成，不可点击，点击时未自评题目自评选项抖动提示。
            - **交互：** 点击后进入下一题/下一节内容。
- **交互模式：**
    - 用户对照标准答案，对自己的解答进行整体自评。
    - 自评结果实时更新。
    - 自评完成后，“继续”按钮才可点击。
- **反馈原则：**
    - 自评状态有清晰的视觉反馈。
    - 未完成自评时，对“继续”操作有明确的阻断和提示。

**优先级：** P0
**依赖关系：** 核心功能点5.2

##### 用户场景与故事 (针对此核心功能点：解答题作答与解析)

###### [场景1：用户使用拍照方式作答解答题，并进行自评]
作为**高中物理学生**，我需要**通过拍照上传手写的解答过程，并在提交后对照标准答案和解析进行自我评价**以解决**复杂公式和图表难以通过键盘输入以及希望得到全面解答参考**的痛点，从而**方便地提交详细解题步骤并准确评估解题质量**。

**前端界面与交互关键步骤：**
1.  **进入题目并选择拍照作答:** 用户进入物理计算题，选择“拍照作答”方式。
    *   **界面变化:** 右侧作答区显示“拍照上传”入口。
    *   **即时反馈:** 无。
2.  **拍照并上传答案:** 用户点击“拍照上传”，调用相机拍摄手写答案，调整后上传1张图片。
    *   **界面变化:** 右侧作答区显示已上传的图片缩略图。
    *   **即时反馈:** 图片上传成功提示。
3.  **提交答案:** 用户点击“提交”按钮。
    *   **界面变化:** 进入解析状态。右侧显示标准答案、用户上传的图片、“核对答案”自评模块。
    *   **即时反馈:** 界面转换。
4.  **进行自评:** 用户对照标准答案，认为自己“部分答对”，点击“部分答对”按钮。
    *   **界面变化:** “部分答对”按钮高亮选中。
    *   **即时反馈:** 自评结果视觉反馈。
5.  **查看解析并继续:** 用户查看题目解析，然后点击“继续”按钮。
    *   **界面变化:** 进入下一题。
    *   **即时反馈:** 无。

**场景细节补充：**
*   **前置条件:** 用户已登录，进入解答题练习。
*   **期望结果:** 用户能顺利通过拍照上传答案并完成自评。
*   **异常与边界情况:**
    *   ⚠️拍照上传图片内容不符合要求（如模糊、无关内容）：按PRD描述，大模型识别后弹窗提示“请上传和题目相关的内容”。
    *   ⚠️用户未完成自评点击“继续”：按钮不可点，或点击后抖动提示自评选项。

**优先级：** 高
**依赖关系：** 无

**Mermaid图示 (解答题-核心流程):**
  ```mermaid
  flowchart TD
      A[用户进入解答题] --> B{选择作答方式};
      B -- 拍照上传 --> C[拍摄/选择图片并上传];
      C -- 上传成功 --> D[显示已上传图片列表];
      B -- 键盘输入 --> E[在文本框输入解答内容];
      D --> F{点击“提交”};
      E --> F;
      A --> G[点击“不确定”];
      G --> H{点击“放弃作答”};
      H --> I[进入解析状态(未作答)];
      F -- 未作答 --> Fa[弹窗确认“本题未作答，确认提交吗？”];
      Fa -- 我再想想 --> B;
      Fa -- 继续提交 --> I;
      F -- 已作答 --> I;

      I --> J[显示标准答案、我的答案、自评模块];
      J --> K[用户进行自评(选择答对/部分/答错)];
      K -- 自评完成 --> L[“继续”按钮可点击];
      L --> M[点击“继续”进入下一题];
      I --> N[查看题目解析];
      I --> O[使用“问一问”];
  ```
---
### 2.2 辅助功能
- **[辅助功能点1]:** 勾画组件
    - **功能详述与前端呈现:**
        - **入口：** 所有题型（单选、多选、填空、解答）的作答界面均常驻悬浮“勾画”按钮。
        - **交互：** 点击“勾画”按钮唤起勾画工具栏。工具栏提供笔迹调整（颜色：蓝、红、黑；粗细：细、中、粗）、橡皮擦（粗细：细、中、粗）、撤回、恢复、一键清空功能。用户可在题目区域（主要是题干区）进行勾画和标记。
        - **保存与展示：** 点击勾画工具栏的“完成”按钮，收起工具栏，勾画内容自动保存并实时展示在题目上。再次进入该题时，勾画内容应保留。
        - **视觉参考：** [勾画组件示意图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XKUNb9JLhoHRiRxX6OIc4vJDnVg.png) (单选题)、[勾画组件示意图-填空](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AMqAbcFa3oYtQBxBQKNcVBeHnEh.png) (填空题)
    - **Mermaid图示 (勾画功能交互):**
      ```mermaid
      sequenceDiagram
          participant User as 用户
          participant Page as 答题页面
          participant Canvas as 勾画画布
          User->>Page: 点击“勾画”按钮
          Page->>Canvas: 唤起勾画工具栏和画布
          User->>Canvas: 选择颜色/粗细/工具
          User->>Canvas: 在题目区域勾画
          Canvas-->>Page: 实时显示勾画内容
          User->>Canvas: 点击“完成”
          Canvas->>Page: 保存勾画内容，收起工具栏
          Page-->>User: 勾画内容保留在题目上
      ```
- **[辅助功能点2]:** 问一问（答疑）组件
    - **功能详述与前端呈现:**
        - **入口：** 在单选题、多选题、填空题（英语自动批改答错后）、解答题的解析状态下，右上角展示“问一问”icon。
        - **交互：** 点击“问一问”icon，弹出居中或侧边栏形式的答疑组件。组件内支持用户自由输入文本进行提问，并可能展示由模型生成的“猜你想问”问题列表（最多3个，可为0）。用户发起首轮对话。
        - **展示：** 聊天气泡形式展示用户提问和AI助手的回答。
        - **视觉参考：** [问一问组件示意图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_C1S1brNyUoAK20xiVlmcgg3InAf.png)
    - **Mermaid图示 (已在单选题场景中提供)**

### 2.3 非本期功能
- **母子题支持：** 涉及复杂题干结构和关联子题的作答与计分逻辑，计划二期支持。
- **特殊题型支持：** 如完形填空、阅读理解（作为整体，非拆分为单选/多选）、七选五、作图题、连线题、证明题等，这些题型交互和批改逻辑更复杂，计划三期支持。

## 3. 业务流程图

```mermaid
graph TD
    A[用户进入练习/测验] --> B{选择题型};
    B -- 单选题 --> C_Single[单选题作答流程];
    B -- 多选题 --> C_Multi[多选题作答流程];
    B -- 填空题 --> C_FillIn[填空题作答流程];
    B -- 解答题 --> C_Essay[解答题作答流程];

    subgraph 单选题作答流程
        direction LR
        C_Single_Init[初始状态: 查看题目] --> C_Single_Answer[作答状态: 选择选项];
        C_Single_Answer --> C_Single_Submit{提交};
        C_Single_Submit -- 正确/二次错误 --> C_Single_Parse[解析状态: 查看答案/解析/问一问];
        C_Single_Submit -- 首次错误 --> C_Single_ReAnswer[作答状态: 重新作答];
        C_Single_ReAnswer --> C_Single_Submit;
        C_Single_Init --> C_Single_GiveUp{不确定?};
        C_Single_GiveUp -- 放弃作答 --> C_Single_Parse;
    end

    subgraph 多选题作答流程
        direction LR
        C_Multi_Init[初始状态: 查看题目] --> C_Multi_Answer[作答状态: 选择选项(可多选)];
        C_Multi_Answer --> C_Multi_Submit{提交};
        C_Multi_Submit -- 仅选一项 --> C_Multi_Confirm[弹窗确认];
        C_Multi_Confirm -- 继续提交 --> C_Multi_Judge;
        C_Multi_Confirm -- 再想想 --> C_Multi_Answer;
        C_Multi_Submit -- 选择多项 --> C_Multi_Judge;
        C_Multi_Judge -- 正确/二次错误 --> C_Multi_Parse[解析状态: 查看答案/解析/问一问];
        C_Multi_Judge -- 首次错误 --> C_Multi_ReAnswer[作答状态: 重新作答];
        C_Multi_ReAnswer --> C_Multi_Submit;
        C_Multi_Init --> C_Multi_GiveUp{不确定?};
        C_Multi_GiveUp -- 放弃作答 --> C_Multi_Parse;
    end
    
    subgraph 填空题作答流程
        direction LR
        C_FillIn_Init[初始状态: 查看题目] --> C_FillIn_Answer[作答状态: 输入内容];
        C_FillIn_Answer --> C_FillIn_Submit{提交};
        C_FillIn_Submit -- 未答完 --> C_FillIn_Confirm[弹窗确认];
        C_FillIn_Confirm -- 继续提交 --> C_FillIn_Judge;
        C_FillIn_Confirm -- 再想想 --> C_FillIn_Answer;
        C_FillIn_Submit -- 答完 --> C_FillIn_Judge;
        C_FillIn_Judge -- 英语 --> C_FillIn_AutoParse[自动批改后进入解析];
        C_FillIn_Judge -- 其他学科 --> C_FillIn_SelfParse[进入解析与自评];
        C_FillIn_Init --> C_FillIn_GiveUp{不确定?};
        C_FillIn_GiveUp -- 放弃作答 --> C_FillIn_Judge; %% 放弃也进入解析，标记未作答
    end

    subgraph 解答题作答流程
        direction LR
        C_Essay_Init[初始状态: 查看题目] --> C_Essay_ChooseMode{选择作答方式};
        C_Essay_ChooseMode -- 拍照 --> C_Essay_Upload[上传图片];
        C_Essay_ChooseMode -- 键盘 --> C_Essay_Type[输入文字];
        C_Essay_Upload --> C_Essay_Submit{提交};
        C_Essay_Type --> C_Essay_Submit;
        C_Essay_Submit -- 未作答 --> C_Essay_Confirm[弹窗确认];
        C_Essay_Confirm -- 继续提交 --> C_Essay_Parse[解析与自评状态];
        C_Essay_Confirm -- 再想想 --> C_Essay_ChooseMode;
        C_Essay_Submit -- 已作答 --> C_Essay_Parse;
        C_Essay_Init --> C_Essay_GiveUp{不确定?};
        C_Essay_GiveUp -- 放弃作答 --> C_Essay_Parse;
    end

    C_Single_Parse --> Z[下一题/结束];
    C_Multi_Parse --> Z;
    C_FillIn_AutoParse --> Z;
    C_FillIn_SelfParse -- 自评完成 --> Z;
    C_Essay_Parse -- 自评完成 --> Z;
```

## 4. 性能与安全需求

### 4.1 性能需求
-   **响应时间：**
    -   题目加载（包含题干、选项、图片等）响应时间不超过 2 秒 (95th percentile)。
    -   用户选择选项、输入文字等交互操作的界面反馈应在 200毫秒 内。
    -   提交答案后的结果反馈（无论自动批改还是进入自评）应在 3 秒内完成。
    -   勾画操作应流畅，无明显延迟。
-   **并发用户：** ⚠️系统需支持至少 500 并发用户同时进行不同题型的作答，核心功能性能不显著衰减。
-   **数据量：** ⚠️题干或解析中包含的图片，应进行适当压缩优化，单个图片大小建议不超过500KB，确保快速加载。
-   **前端性能感知：**
    -   **图片加载**: 对于题干或选项中的图片，应使用懒加载或占位符，避免一次性加载过多图片导致界面卡顿。
    -   **复杂题干渲染**: 对于包含复杂公式或大量内容的题干，确保渲染高效，不阻塞用户交互。
    -   **勾画保存**: 勾画内容保存应在后台异步进行，不影响用户继续操作。

### 4.2 安全需求
-   **权限控制：** ⚠️学生用户只能访问和作答分配给自己的练习或测验中的题目，不能查看或修改他人作答数据。
-   **数据加密：** 用户答案在提交至后端过程中必须使用 HTTPS。前端避免在本地（如localStorage）存储明文答案或个人敏感信息。
-   **操作审计：** ⚠️对学生提交答案、放弃作答等关键操作，后端应记录相关日志。
-   **输入校验:**
    -   对于填空题的文本输入，前端可进行基础的长度限制（例如，避免超长文本输入）。
    -   对于图片上传（解答题拍照），前端应校验文件类型和大小，后端必须进行二次严格校验和安全扫描。
    -   防止常见的客户端攻击，如XSS（尽管主要场景是内容展示，但若有用户输入展示的地方需注意）。

## 5. 验收标准

### 5.1 功能验收
-   **[核心功能点1：单选题]：**
    -   **用户场景：** 当用户在巩固练习中作答单选题，选择选项A并提交。
    -   **界面与交互：**
        -   选项A在点击后显示选中状态，“提交”按钮变为可点击。
        -   用户点击“提交”后，若A为正确答案，弹出“回答正确”提示，界面进入解析状态，显示正确答案A为绿色，展示解析内容。若A为错误答案（首次），弹出“回答错误”提示，界面停留在作答状态，允许重新选择。
    -   **期望结果：** 系统应正确判断答案，并根据对错和作答次数转换到对应状态，所有UI元素按设计稿显示。
-   **[核心功能点2：多选题]：**
    -   **用户场景：** 用户作答多选题，正确答案为A,B，用户选择A,C（C错误）并首次提交。
    -   **界面与交互：**
        -   选项A,C显示选中状态。
        -   提交后，弹出“回答错误”或“部分正确”提示，界面停留在作答状态，允许重新选择。
    -   **期望结果：** 系统反馈用户答案部分错误，并允许二次作答。
-   **[核心功能点3：填空题（非英语，自评）]：**
    -   **用户场景：** 用户通过键盘输入完成2个填空，提交后进入自评，将第1空评为“我答对了”，第2空评为“我答错了”。
    -   **界面与交互：**
        -   提交后，右侧显示自评模块，用户可为每个空选择自评结果。
        -   自评后，作答区选项卡对应显示“√”和“×”。
    -   **期望结果：** 用户能顺利完成自评，自评结果正确记录和显示。
-   **[核心功能点4：英语填空题（自动批改）]：**
    -   **用户场景：** 用户作答英语填空题，提交后系统自动批改。
    -   **界面与交互：**
        -   提交后，弹出反馈弹窗，界面进入解析状态，显示各空答案的对错标记和正确答案。
    -   **期望结果：** 系统自动批改结果准确，并清晰展示。
-   **[核心功能点5：解答题]：**
    -   **用户场景：** 用户通过拍照上传1张图片作为答案，提交后自评为“部分答对”。
    -   **界面与交互：**
        -   拍照上传功能正常，图片能成功上传并显示。
        -   提交后进入自评，用户能选择“部分答对”。
    -   **期望结果：** 用户能通过拍照完成作答并进行自评。
-   **[辅助功能点1：勾画组件]：**
    -   **用户场景：** 用户在作答任意题型时，点击“勾画”按钮，使用红色画笔在题干上划线，然后点击“完成”。
    -   **界面与交互：**
        -   勾画工具栏正常唤出和收起。
        -   勾画内容能实时显示并保存。
    -   **期望结果：** 勾画功能可用，内容能正确保存和展示。

### 5.2 性能验收
-   **响应时间：**
    -   **测试用例1:** 在普通4G网络下，用户从题目列表进入任一题型的答题界面，题目内容（含图片）在2秒内完全加载。
    -   **测试用例2:** 用户在单选题或多选题中点击选项，选项状态变化在200ms内响应。
    -   **测试用例3:** 用户提交英语填空题答案，自动批改结果及解析界面在3秒内展示。
-   **前端性能感知验收：**
    -   **测试用例1 (图片加载):** 解答题拍照上传一张2MB的图片，上传进度清晰可见，上传完成时间在可接受范围内（例如10秒内，具体视网络情况），上传期间界面不卡顿。

### 5.3 安全验收
-   **输入校验：**
    -   **测试用例1 (图片上传):** 尝试上传非图片文件（如.txt, .exe）到解答题拍照上传处，期望结果：前端应提示“不支持的文件类型”，无法上传。
    -   **测试用例2 (图片大小):** 尝试上传超过大小限制（如10MB）的图片，期望结果：前端应提示“图片大小超过限制”，无法上传。

## 6. 其他需求

### 6.1 可用性与体验需求
-   **界面友好与直观性：**
    -   各题型作答流程清晰，用户能快速理解如何操作。
    -   按钮、图标、提示信息易于理解，符合学生用户习惯。
    -   整体视觉风格（颜色、字体、布局）在不同题型间保持一致。
-   **容错处理：**
    -   图片上传失败时，提供明确的“上传失败，点击重试”提示。
    -   提交未完成的答案时（如填空题部分未填），通过弹窗与用户确认，避免误操作。
    -   ⚠️若后端服务异常（如获取题目失败、提交答案失败），前端应有统一的友好错误提示（如“网络开小差了，请稍后再试”），避免页面白屏或崩溃。
-   **兼容性：**
    -   **浏览器：** 需支持最新版本的 Chrome, Firefox, Safari, Edge 浏览器（桌面端和移动端）。
    -   **响应式布局：** 题目组件需适配不同尺寸的屏幕（特别是移动端和平板），确保在小屏幕上题目内容清晰可读，选项易于点选，操作按钮方便点击。
        -   **移动端 (<768px):** 题干和作答区可能需要上下布局或优化左右布局的比例，确保内容不拥挤。勾画工具栏在小屏上应易于操作。
-   **可访问性 (A11y)：**
    -   ⚠️核心交互元素（选项、提交按钮、不确定按钮、作答方式切换按钮）应支持键盘操作（Tab聚焦，Enter/Space激活）。
    -   ⚠️图片（题干图、选项图）需提供有意义的 `alt` 文本。
    -   ⚠️颜色对比度（如选项选中态、答案对错标记）应符合 WCAG AA 级别标准。
-   **交互一致性：**
    -   “提交”、“不确定”、“继续”等通用按钮在不同题型中的行为逻辑和视觉样式应保持一致。
    -   “勾画”、“问一问”等辅助功能的入口和基本交互模式应统一。

### 6.2 维护性需求
-   **配置管理：** ⚠️拍照上传的最大图片数量、大小限制应可配置。冷启动策略中题目使用次数阈值（如20次）应可配置。
-   **监控告警：** ⚠️前端应监控JS执行错误、API请求异常（如提交答案接口、获取题目解析接口），并通过Sentry等工具上报。
-   **日志管理：** ⚠️前端应记录关键用户行为（如进入题目、选择答案、提交答案、使用勾画、使用问一问）及API交互日志，辅助问题排查。

## 7. 用户反馈和迭代计划

### 7.1 用户反馈机制
-   ⚠️用户可以通过学生端App内的“意见反馈”入口提交使用问题和建议。
-   ⚠️定期（如每双周）由产品和运营团队收集分析用户关于题型组件的反馈，识别高频问题和体验痛点。

### 7.2 迭代计划
-   **一期：** 实现单选题、多选题、填空题（英语自动批改、其他学科自评）、解答题（拍照和键盘输入、自评）的核心作答与解析流程，以及勾画、问一问辅助功能。
-   **二期：** 根据一期上线后的数据和用户反馈，优化现有题型体验，并计划支持母子题。
-   **三期：** 进一步扩展题型库，支持完形填空、阅读理解等特殊题型。

## 8. 需求检查清单
使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| -------------------------------- | ----------------------------------- | ------ | ------ | ------ | ------------------------- | --------------------------- | ----------- | ---- |
| 单选题：初始状态需求说明 | 2.1 核心功能点1.1 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 单选题：作答状态需求说明 | 2.1 核心功能点1.2 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 单选题：解析状态需求说明 | 2.1 核心功能点1.3 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 多选题：初始状态需求说明 | 2.1 核心功能点2.1 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 多选题：作答状态需求说明 | 2.1 核心功能点2.2 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 多选题：解析状态需求说明 | 2.1 核心功能点2.3 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 填空题(非英语自评)：初始状态 | 2.1 核心功能点3.1 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 填空题(非英语自评)：作答状态 | 2.1 核心功能点3.2 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 填空题(非英语自评)：解析状态 | 2.1 核心功能点3.3 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 英语填空题(自动批改)：初始状态 | 2.1 核心功能点4.1 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 英语填空题(自动批改)：作答状态 | 2.1 核心功能点4.2 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 英语填空题(自动批改)：解析状态 | 2.1 核心功能点4.3 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 解答题：初始状态 | 2.1 核心功能点5.1 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 解答题：作答状态 | 2.1 核心功能点5.2 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 解答题：解析状态 | 2.1 核心功能点5.3 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 勾画组件需求 | 2.2 辅助功能点1 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 问一问需求 | 2.2 辅助功能点2 | ✅ | ✅ | ✅ | ✅ (5.1) | ✅ (P0) | ✅ |  |
| 需求背景、目标、用户、方案简述 | 1. 需求背景与目标 | ✅ | ✅ | ✅ | N/A | N/A | N/A |  |
| 未来要做的 | 2.3 非本期功能 | ✅ | ✅ | ✅ | N/A | N/A | N/A |  |
| 需求概览图及表格 | 2.1 核心功能 (概述) | ✅ | ✅ | ✅ | N/A | ✅ | N/A |  |
| 各题型流程图 | 2.1 各核心功能点下的Mermaid图示, 3. 业务流程图 | ✅ | ✅ | ✅ | N/A | N/A | ✅ |  |

-   **完整性：** 原始需求是否都有对应的优化后需求点，无遗漏。
-   **正确性：** 优化后需求描述是否准确表达了原始需求的意图。
-   **一致性：** 优化后需求之间是否有冲突或重复。
-   **可验证性：** 优化后需求是否有明确的验收标准（对应5. 验收标准）。
-   **可跟踪性：** 优化后需求是否有明确的优先级和依赖关系。
-   **UI/UX明确性：** 优化后需求是否清晰描述了前端界面、交互和用户体验细节。

✅表示通过；❌表示未通过；⚠️表示描述正确，但UI/UX细节、验收标准或图示等仍需与PM进一步沟通完善；N/A表示不适用。每个需求点都需要填写。

## 9. 附录

### 9.1 原型图/设计稿链接
-   交互稿：(原始PRD未提供链接)
-   视觉稿：[Figma: 学生端-题型组件](https://www.figma.com/design/j6wNSQxDFuZHscu1SVMuIA/AI%E8%AF%BE%E4%BD%93%E9%AA%8C%E7%89%88?node-id=2922-27245&t=tMaoye5c0E4lU71L-0)
-   动效：(原始PRD未提供链接)
-   (本AIPRD中引用的图片URL均来自原始PRD)

### 9.2 术语表
-   **自评：** 指学生在完成填空题（特指语文、数学、物理、化学、生物等学科）或解答题后，对照系统给出的标准答案和解析，自行判断自己答案的正确程度（如答对、部分答对、答错）的过程。
-   **自动批改：** 指系统根据预设的正确答案和评分规则，对学生的作答（特指英语填空题、单选题、多选题）进行自动判断对错并给出反馈的过程。
-   **勾画组件：** 指在答题界面提供的一组工具，允许用户在题目内容上进行划线、标记等操作，辅助思考和解题。
-   **问一问：** 指在题目解析状态下提供的答疑功能，用户可以通过自由提问或选择推荐问题的方式，与AI助手进行交互，以获得更深入的解答。

### 9.3 参考资料
-   原始需求文档：[`ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md`](ai-doc-format/tools/Format-PRD-学生端-题型组件-一期.md)
-   本AIPRD生成指南：[`ai-templates/prompts/fe-design/aiprd2.md`](ai-templates/prompts/fe-design/aiprd2.md)
-   本AIPRD模板：[`ai-templates/templates/prd/fe-ai-prd-template.md`](ai-templates/templates/prd/fe-ai-prd-template.md)
-   Mermaid规范：[`luban-agent/ai-rules/common/mermaid-rule.md`](luban-agent/ai-rules/common/mermaid-rule.md) (假设存在此文件)
-   Luban规范：[`luban-agent/ai-rules/common/luban-rule.md`](luban-agent/ai-rules/common/luban-rule.md) (假设存在此文件)
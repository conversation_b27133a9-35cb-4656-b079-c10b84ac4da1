openapi: 3.0.3
info:
  title: 学生端题型组件一期 API
  description: |
    提供学生端题型组件的完整答题功能，支持单选题、多选题、填空题、解答题等核心题型的答题流程。
    包括智能批改、实时交互、多媒体作答、勾画工具、异常行为监控等功能。
    本文档遵循 OpenAPI 3.0.3 规范。
  version: v1.0.0
  contact:
    name: 学生端题型组件开发团队
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api/v1
    description: 本地开发服务器
  - url: https://api.example.com/api/v1
    description: 生产环境服务器

tags:
  - name: SessionManagement
    description: 会话管理相关接口
  - name: AnswerProcessing
    description: 答题处理相关接口
  - name: ResourceManagement
    description: 资源管理相关接口

components:
  schemas:
    # 通用响应结构
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 业务状态码，0表示成功，其他为错误码
          example: 0
        message:
          type: string
          description: 响应消息
          example: "Success"
        data:
          type: object
          description: 实际业务数据
          nullable: true
        pageInfo:
          $ref: '#/components/schemas/PageInfo'

    PageInfo:
      type: object
      description: 分页信息
      properties:
        page:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          example: 20
        total:
          type: integer
          description: 总条目数
          example: 100

    GenericError:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码
          example: 4000001
        message:
          type: string
          description: 用户可读的错误信息
          example: '无效的参数'
        details:
          type: array
          description: 详细错误信息
          nullable: true
          items:
            type: object
            properties:
              field:
                type: string
                description: 错误字段名
                example: 'questionId'
              issue:
                type: string
                description: 错误描述
                example: 'must not be empty'

    # 题目相关Schema
    Question:
      type: object
      description: 题目信息
      properties:
        questionId:
          type: integer
          format: int64
          description: 题目唯一ID
          example: 123456
        questionType:
          type: integer
          description: 题型类型 (1:单选题, 2:多选题, 3:填空题, 4:解答题)
          example: 1
        subjectId:
          type: integer
          format: int64
          description: 学科ID
          example: 1001
        content:
          type: string
          description: 题目内容
          example: "下列哪个选项是正确的？"
        options:
          type: array
          description: 选项列表（选择题使用）
          items:
            $ref: '#/components/schemas/QuestionOption'
        standardAnswer:
          type: object
          description: 标准答案
          nullable: true
        difficulty:
          type: integer
          description: 难度等级
          example: 3
        supportSecondAttempt:
          type: boolean
          description: 是否支持二次作答
          example: true

    QuestionOption:
      type: object
      description: 题目选项
      properties:
        optionId:
          type: string
          description: 选项ID
          example: "A"
        content:
          type: string
          description: 选项内容
          example: "选项A的内容"
        isCorrect:
          type: boolean
          description: 是否为正确选项
          example: false

    # 会话相关Schema
    CreateSessionRequest:
      type: object
      description: 创建答题会话请求
      properties:
        questionId:
          type: integer
          format: int64
          description: 题目ID
          example: 123456
        studentId:
          type: integer
          format: int64
          description: 学生ID
          example: 789012
      required:
        - questionId
        - studentId

    SessionResponse:
      type: object
      description: 答题会话响应
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: "sess_abc123def456"
        questionId:
          type: integer
          format: int64
          description: 题目ID
          example: 123456
        studentId:
          type: integer
          format: int64
          description: 学生ID
          example: 789012
        currentState:
          type: integer
          description: 当前状态 (1:初始状态, 2:作答状态, 3:解析状态)
          example: 1
        startTime:
          type: integer
          format: int64
          description: 开始时间（UTC秒数）
          example: 1735545600
        accumulatedDuration:
          type: integer
          description: 累计答题时长（秒）
          example: 0
        isPaused:
          type: boolean
          description: 是否暂停
          example: false
        question:
          $ref: '#/components/schemas/Question'

    UpdateSessionRequest:
      type: object
      description: 更新会话状态请求
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: "sess_abc123def456"
        action:
          type: string
          description: 操作类型 (retry:重试, pause:暂停, resume:继续)
          enum: [retry, pause, resume]
          example: "retry"
      required:
        - sessionId
        - action

    # 答题相关Schema
    SubmitAnswerRequest:
      type: object
      description: 提交答案请求
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: "sess_abc123def456"
        type:
          type: string
          description: 操作类型 (submit:提交答案, self_evaluation:自评, confirm:确认)
          enum: [submit, self_evaluation, confirm]
          example: "submit"
        answerContent:
          type: object
          description: 答案内容，根据题型不同格式不同
          example: {"optionId": "A"}
        answerDuration:
          type: integer
          description: 答题用时（秒）
          example: 30
        evaluationResult:
          type: integer
          description: 自评结果 (1:我答对了, 2:部分答对, 3:我答错了)
          nullable: true
          example: 1
        evaluationDuration:
          type: integer
          description: 自评用时（秒）
          nullable: true
          example: 5
        needConfirm:
          type: boolean
          description: 是否需要确认（多选题仅选1个选项时）
          example: false
        confirmed:
          type: boolean
          description: 是否已确认
          example: true
      required:
        - sessionId
        - type

    AnswerResponse:
      type: object
      description: 答题响应
      properties:
        submissionStatus:
          type: string
          description: 提交状态
          example: "success"
        gradingResult:
          type: integer
          description: 批改结果 (0:未批改, 1:正确, 2:错误, 3:部分正确)
          example: 1
        standardAnswer:
          type: object
          description: 标准答案
          nullable: true
        needConfirmation:
          type: boolean
          description: 是否需要确认（多选题特殊逻辑）
          example: false
        confirmationMessage:
          type: string
          description: 确认提示信息
          nullable: true
          example: "您只选择了1个选项，确认提交吗？"
        anomalyDetected:
          type: boolean
          description: 是否检测到异常行为
          example: false
        anomalyMessage:
          type: string
          description: 异常提示信息
          nullable: true
          example: "认真核对哦"

    AnswerResultResponse:
      type: object
      description: 答题结果响应
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: "sess_abc123def456"
        gradingResult:
          type: integer
          description: 批改结果
          example: 1
        standardAnswer:
          type: object
          description: 标准答案
        explanation:
          type: string
          description: 题目解析
          nullable: true
          example: "这道题考查的是..."
        optionStatistics:
          type: array
          description: 选项统计（选择题）
          nullable: true
          items:
            $ref: '#/components/schemas/OptionStatistic'
        errorAnalysis:
          type: string
          description: 错误分析
          nullable: true

    OptionStatistic:
      type: object
      description: 选项统计
      properties:
        optionId:
          type: string
          description: 选项ID
          example: "A"
        selectionCount:
          type: integer
          description: 选择次数
          example: 45
        selectionPercentage:
          type: number
          format: float
          description: 选择占比
          example: 45.5

    # 资源相关Schema
    ResourceRequest:
      type: object
      description: 资源操作请求
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: "sess_abc123def456"
        type:
          type: string
          description: 资源类型 (media:多媒体, drawings:勾画, ai_chat:答疑, bookmark:收藏)
          enum: [media, drawings, ai_chat, bookmark]
          example: "drawings"
        action:
          type: string
          description: 操作类型 (upload:上传, save:保存, delete:删除, chat:聊天)
          enum: [upload, save, delete, chat]
          example: "save"
        content:
          type: object
          description: 资源内容，根据类型不同格式不同
          example: {"drawingContent": {"paths": [{"x": 100, "y": 200}]}}
        drawingId:
          type: string
          description: 勾画ID（删除勾画时使用）
          nullable: true
          example: "draw_001"
        clearAll:
          type: boolean
          description: 是否清空所有（删除勾画时使用）
          example: false
        questionContent:
          type: string
          description: 问题内容（AI答疑时使用）
          nullable: true
          example: "这道题的解题思路是什么？"
        bookmarkType:
          type: string
          description: 收藏类型（错题收藏时使用）
          nullable: true
          example: "wrong_answer"
      required:
        - sessionId
        - type
        - action

    ResourceResponse:
      type: object
      description: 资源操作响应
      properties:
        operationStatus:
          type: string
          description: 操作状态
          example: "success"
        resourceId:
          type: string
          description: 资源ID
          nullable: true
          example: "res_001"
        fileUrl:
          type: string
          description: 文件URL（文件上传时返回）
          nullable: true
          example: "https://cdn.example.com/uploads/file.jpg"
        drawingId:
          type: string
          description: 勾画ID（保存勾画时返回）
          nullable: true
          example: "draw_001"
        drawings:
          type: array
          description: 勾画数据列表（获取勾画时返回）
          nullable: true
          items:
            $ref: '#/components/schemas/DrawingData'
        aiAnswer:
          type: string
          description: AI回答（答疑时返回）
          nullable: true
          example: "这道题主要考查..."
        recommendedQuestions:
          type: array
          description: 推荐问题列表（答疑时返回）
          nullable: true
          items:
            type: string
          example: ["如何快速解决这类题？", "有什么解题技巧？"]
        bookmarkId:
          type: string
          description: 收藏ID（错题收藏时返回）
          nullable: true
          example: "bookmark_001"
        deletedCount:
          type: integer
          description: 删除数量（删除操作时返回）
          nullable: true
          example: 1

    DrawingData:
      type: object
      description: 勾画数据
      properties:
        drawingId:
          type: string
          description: 勾画ID
          example: "draw_001"
        drawingContent:
          type: object
          description: 勾画内容
        toolType:
          type: integer
          description: 工具类型 (1:画笔, 2:橡皮擦)
          example: 1
        color:
          type: string
          description: 颜色
          example: "blue"
        thickness:
          type: integer
          description: 粗细 (1:细, 2:中等, 3:粗)
          example: 2
        drawingOrder:
          type: integer
          description: 勾画顺序
          example: 1
        isErased:
          type: boolean
          description: 是否已擦除
          example: false

  parameters:
    sessionIdQuery:
      name: sessionId
      in: query
      required: true
      description: 会话ID
      schema:
        type: string
        example: "sess_abc123def456"

    questionIdQuery:
      name: questionId
      in: query
      required: true
      description: 题目ID
      schema:
        type: integer
        format: int64
        example: 123456

    studentIdQuery:
      name: studentId
      in: query
      required: true
      description: 学生ID
      schema:
        type: integer
        format: int64
        example: 789012

    includeResultsQuery:
      name: includeResults
      in: query
      required: false
      description: 是否包含详细结果
      schema:
        type: boolean
        default: false

    resourceTypeQuery:
      name: type
      in: query
      required: true
      description: 资源类型
      schema:
        type: string
        enum: [media, drawings, ai_chat, bookmark]
        example: "drawings"

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "请输入JWT Token, 格式为: Bearer {token}"

  responses:
    UnauthorizedError:
      description: 未认证或认证令牌无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4010001
                message: "需要认证或认证失败"

    NotFoundError:
      description: 请求的资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4040001
                message: "请求的资源不存在"

    ForbiddenError:
      description: 无权限访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4030001
                message: "无权限执行此操作"

    BadRequestError:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4000001
                message: "请求参数无效"

paths:
  /api/v1/exercises/sessions:
    get:
      tags:
        - SessionManagement
      summary: 获取题目信息和会话状态
      description: 根据题目ID获取题目详细信息，或根据会话ID获取会话状态
      operationId: getSessionInfo
      parameters:
        - name: questionId
          in: query
          required: false
          description: 题目ID（获取题目信息时使用）
          schema:
            type: integer
            format: int64
            example: 123456
        - name: sessionId
          in: query
          required: false
          description: 会话ID（获取会话状态时使用）
          schema:
            type: string
            example: "sess_abc123def456"
        - $ref: '#/components/parameters/studentIdQuery'
      responses:
        '200':
          description: 成功获取信息
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        oneOf:
                          - $ref: '#/components/schemas/Question'
                          - $ref: '#/components/schemas/SessionResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

    post:
      tags:
        - SessionManagement
      summary: 创建答题会话
      description: 为学生创建新的答题会话，初始化计时和状态管理
      operationId: createSession
      requestBody:
        description: 创建会话请求
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateSessionRequest'
            examples:
              default:
                value:
                  questionId: 123456
                  studentId: 789012
      responses:
        '200':
          description: 会话创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/SessionResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - BearerAuth: []

    put:
      tags:
        - SessionManagement
      summary: 更新会话状态
      description: 更新答题会话状态，支持重试、暂停、继续等操作
      operationId: updateSession
      requestBody:
        description: 更新会话请求
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSessionRequest'
            examples:
              retry:
                summary: 请求二次作答
                value:
                  sessionId: "sess_abc123def456"
                  action: "retry"
              pause:
                summary: 暂停计时
                value:
                  sessionId: "sess_abc123def456"
                  action: "pause"
              resume:
                summary: 继续计时
                value:
                  sessionId: "sess_abc123def456"
                  action: "resume"
      responses:
        '200':
          description: 会话状态更新成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/SessionResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /api/v1/exercises/answers:
    get:
      tags:
        - AnswerProcessing
      summary: 获取答题结果
      description: 获取答题的详细结果，包括批改结果、解析、统计等信息
      operationId: getAnswerResult
      parameters:
        - $ref: '#/components/parameters/sessionIdQuery'
        - $ref: '#/components/parameters/includeResultsQuery'
      responses:
        '200':
          description: 成功获取答题结果
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/AnswerResultResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

    post:
      tags:
        - AnswerProcessing
      summary: 提交答题答案或自评结果
      description: 统一的答题处理接口，支持答案提交、自评、多选题确认等操作
      operationId: submitAnswer
      requestBody:
        description: 答题请求
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitAnswerRequest'
            examples:
              singleChoice:
                summary: 单选题答案提交
                value:
                  sessionId: "sess_abc123def456"
                  type: "submit"
                  answerContent: {"optionId": "A"}
                  answerDuration: 30
              multipleChoice:
                summary: 多选题答案提交
                value:
                  sessionId: "sess_abc123def456"
                  type: "submit"
                  answerContent: {"optionIds": ["A", "C"]}
                  answerDuration: 45
              multipleChoiceConfirm:
                summary: 多选题选项确认
                value:
                  sessionId: "sess_abc123def456"
                  type: "confirm"
                  answerContent: {"optionIds": ["A"]}
                  confirmed: true
              fillInBlank:
                summary: 填空题答案提交
                value:
                  sessionId: "sess_abc123def456"
                  type: "submit"
                  answerContent: {"answers": ["apple", "running"]}
                  answerDuration: 120
              essay:
                summary: 解答题答案提交
                value:
                  sessionId: "sess_abc123def456"
                  type: "submit"
                  answerContent: {"textContent": "解答过程", "imageUrls": ["url1", "url2"]}
                  answerDuration: 300
              selfEvaluation:
                summary: 自评结果提交
                value:
                  sessionId: "sess_abc123def456"
                  type: "self_evaluation"
                  evaluationResult: 1
                  evaluationDuration: 5
      responses:
        '200':
          description: 答题处理成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/AnswerResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /api/v1/exercises/resources:
    get:
      tags:
        - ResourceManagement
      summary: 获取资源数据
      description: 获取指定类型的资源数据，如勾画数据列表等
      operationId: getResources
      parameters:
        - $ref: '#/components/parameters/sessionIdQuery'
        - $ref: '#/components/parameters/resourceTypeQuery'
      responses:
        '200':
          description: 成功获取资源数据
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ResourceResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

    post:
      tags:
        - ResourceManagement
      summary: 创建或处理资源
      description: 统一的资源处理接口，支持文件上传、勾画保存、AI答疑、错题收藏等操作
      operationId: processResources
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                sessionId:
                  type: string
                  description: 会话ID
                  examples:
                    - sess_abc123def456
                  example: sess_abc123def456
                type:
                  type: string
                  description: 资源类型
                  examples:
                    - media
                  example: media
                action:
                  type: string
                  description: 操作类型
                  examples:
                    - upload
                  example: upload
                imageBase64:
                  type: string
                  description: 上传图片的 base64 编码数据，直接拍照上传
                  example: ''
              required:
                - sessionId
                - type
                - action
                - imageBase64
      responses:
        '200':
          description: 资源处理成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ResourceResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '413':
          description: 文件过大
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                default:
                  value:
                    code: 4130001
                    message: "文件大小超出限制"
      security:
        - BearerAuth: []

    put:
      tags:
        - ResourceManagement
      summary: 更新资源数据
      description: 更新指定的资源数据
      operationId: updateResources
      requestBody:
        description: 资源更新请求
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ResourceRequest'
            examples:
              updateDrawing:
                summary: 更新勾画数据
                value:
                  sessionId: "sess_abc123def456"
                  type: "drawings"
                  action: "save"
                  drawingId: "draw_001"
                  content:
                    drawingContent: {"paths": [{"x": 150, "y": 250}]}
                    toolType: 1
                    color: "red"
                    thickness: 3
      responses:
        '200':
          description: 资源更新成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ResourceResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

    delete:
      tags:
        - ResourceManagement
      summary: 删除资源数据
      description: 删除指定的资源数据，如删除勾画、清空所有勾画等
      operationId: deleteResources
      parameters:
        - $ref: '#/components/parameters/sessionIdQuery'
        - $ref: '#/components/parameters/resourceTypeQuery'
        - name: drawingId
          in: query
          required: false
          description: 勾画ID（删除特定勾画时使用）
          schema:
            type: string
            example: "draw_001"
        - name: clearAll
          in: query
          required: false
          description: 是否清空所有（删除所有勾画时使用）
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: 资源删除成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ResourceResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

security:
  - BearerAuth: []
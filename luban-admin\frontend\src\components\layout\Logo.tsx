import Link from 'next/link';
import Image from 'next/image';

interface LogoProps {
  className?: string;
  textClassName?: string;
}

const Logo = ({ className = '', textClassName = '' }: LogoProps) => {
  return (
    <Link href="/" className={`flex items-center ${className}`}>
      <div className="h-8 mr-3 relative">
        <Image
          src="/images/luban-logo2.png"
          alt="鲁班 | Technical Design Agent Logo"
          width={120}
          height={32}
          className="object-contain"
          style={{ height: '32px', width: 'auto' }}
        />
      </div>
      <span className={`text-xl font-bold ${textClassName}`}>鲁班 | Technical Design Agent</span>
    </Link>
  );
};

export default Logo; 
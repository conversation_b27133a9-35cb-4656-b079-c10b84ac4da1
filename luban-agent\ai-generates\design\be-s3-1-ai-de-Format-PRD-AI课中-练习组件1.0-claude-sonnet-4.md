# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** AI课中练习组件1.0技术架构设计文档
- **PRD版本号：** V1.0
- **提取日期：** 2025-05-24

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 练习会话 | 管理学生的练习会话状态，包括进度跟踪、计时管理、状态恢复 | 2.1, 3.5.2 |
| 答题记录 | 处理学生答题请求，判断答案正确性，记录答题数据 | 2.1, 3.5.2 |
| 进度状态 | 实时计算和更新学生的练习进度 | 2.1, 3.5.2 |
| 即时反馈 | 根据答题结果生成个性化反馈内容，包括连胜检测 | 2.1, 1.5 |
| 转场状态 | 管理练习环节的进入和退出状态 | 2.1, 1.5 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`练习会话`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 会话ID | 练习会话的唯一标识符 | 字符串/UUID | 唯一, 非空 |  | 主键 | 3.5.2 |
| 学生ID | 参与练习的学生标识符 | 整数 | 非空, 外键关联用户中心 |  | 外键 | 3.5.2, 2.3 |
| 课程ID | 练习所属的课程标识符 | 整数 | 非空, 外键关联内容平台 |  | 外键 | 3.5.2, 2.3 |
| 创建时间 | 会话创建的时间戳 | 时间戳 | 非空 | 当前时间 |  | 3.5.2 |
| 当前进度 | 学生在练习中的当前进度 | 整数 | 非负数 | 0 |  | 3.5.2 |
| 计时状态 | 练习计时的状态信息 | JSON/文本 | 非空 | 初始计时状态 |  | 3.5.2 |
| 完成状态 | 练习会话是否完成 | 布尔值 | 非空 | false |  | 3.5.2 |

**实体：`答题记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 记录ID | 答题记录的唯一标识符 | 字符串/UUID | 唯一, 非空 |  | 主键 | 3.5.2 |
| 会话ID | 关联的练习会话标识符 | 字符串/UUID | 非空, 外键关联练习会话 |  | 外键 | 3.5.2 |
| 题目ID | 答题的题目标识符 | 整数 | 非空, 外键关联内容平台 |  | 外键 | 3.5.2, 2.3 |
| 学生答案 | 学生选择的答案内容 | 文本 | 非空 |  |  | 3.5.2 |
| 正确答案 | 题目的标准答案 | 文本 | 非空 |  |  | 3.5.2 |
| 答题时间 | 学生答题耗时（秒） | 整数 | 非负数 |  |  | 3.5.2 |
| 正确性标识 | 答案是否正确的标识 | 布尔值 | 非空 |  |  | 3.5.2 |
| 创建时间 | 答题记录创建时间 | 时间戳 | 非空 | 当前时间 |  | 3.5.2 |

**实体：`进度状态`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 学生ID | 学生的唯一标识符 | 整数 | 非空, 外键关联用户中心 |  | 主键组成部分 | 3.5.2, 2.3 |
| 课程ID | 课程的唯一标识符 | 整数 | 非空, 外键关联内容平台 |  | 主键组成部分 | 3.5.2, 2.3 |
| 已完成题目数 | 学生已完成的题目数量 | 整数 | 非负数 | 0 |  | 3.5.2 |
| 预估总题目数 | 预估学生需要完成的总题目数 | 整数 | 正数 |  |  | 3.5.2 |
| 连胜次数 | 学生连续答对的题目数 | 整数 | 非负数 | 0 |  | 3.5.2 |
| 当前难度 | 学生当前的题目难度等级 | 整数 | 正数 |  |  | 3.5.2 |
| 更新时间 | 进度状态最后更新时间 | 时间戳 | 非空 | 当前时间 |  | 3.5.2 |

**实体：`即时反馈`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 反馈ID | 即时反馈的唯一标识符 | 字符串/UUID | 唯一, 非空 |  | 主键 | 2.1 |
| 答题记录ID | 关联的答题记录标识符 | 字符串/UUID | 非空, 外键关联答题记录 |  | 外键 | 2.1 |
| 反馈类型 | 反馈的类型（正确/错误/连胜） | 枚举/字符串 | 非空, 限定值 |  |  | 2.1, 1.5 |
| 反馈内容 | 具体的反馈文案或内容 | 文本 | 非空 |  |  | 2.1 |
| 连胜状态 | 是否触发连胜效果 | 布尔值 | 非空 | false |  | 2.1 |
| 创建时间 | 反馈生成时间 | 时间戳 | 非空 | 当前时间 |  | 2.1 |

**实体：`转场状态`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 转场ID | 转场状态的唯一标识符 | 字符串/UUID | 唯一, 非空 |  | 主键 | 2.1 |
| 会话ID | 关联的练习会话标识符 | 字符串/UUID | 非空, 外键关联练习会话 |  | 外键 | 2.1 |
| 转场类型 | 转场的类型（进入/退出） | 枚举/字符串 | 非空, 限定值 |  |  | 2.1, 1.5 |
| 转场文案 | 转场时显示的文案内容 | 文本 | 非空 |  |  | 1.5 |
| 展示时长 | 转场动效展示时间（秒） | 整数 | 正数 | 2 |  | 1.5 |
| 创建时间 | 转场状态创建时间 | 时间戳 | 非空 | 当前时间 |  | 2.1 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 (例如：用户 '拥有一个' 学校档案) | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 练习会话包含多个答题记录 | 练习会话 | 答题记录 | 1:N | 答题记录表.会话ID -> 练习会话表.会话ID | 3.5.2 |
| 学生拥有多个练习会话 | 学生（用户中心） | 练习会话 | 1:N | 练习会话表.学生ID -> 用户中心.用户ID | 3.5.2, 2.3 |
| 课程包含多个练习会话 | 课程（内容平台） | 练习会话 | 1:N | 练习会话表.课程ID -> 内容平台.课程ID | 3.5.2, 2.3 |
| 学生在课程中有唯一的进度状态 | 学生（用户中心） | 进度状态 | 1:1 | 进度状态表.学生ID -> 用户中心.用户ID | 3.5.2, 2.3 |
| 课程对应多个学生的进度状态 | 课程（内容平台） | 进度状态 | 1:N | 进度状态表.课程ID -> 内容平台.课程ID | 3.5.2, 2.3 |
| 答题记录生成即时反馈 | 答题记录 | 即时反馈 | 1:1 | 即时反馈表.答题记录ID -> 答题记录表.记录ID | 2.1 |
| 练习会话产生转场状态 | 练习会话 | 转场状态 | 1:N | 转场状态表.会话ID -> 练习会话表.会话ID | 2.1 |
| 题目被多次答题 | 题目（内容平台） | 答题记录 | 1:N | 答题记录表.题目ID -> 内容平台.题目ID | 3.5.2, 2.3 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 练习进度计算 | 已完成题目数, 预估总题目数 | 当前进度百分比 | 2.1, 3.5.2 |
| 连胜状态检测 | 连续答对题目数, 答题正确性标识 | 连胜次数, 连胜状态 | 2.1, 3.5.2 |
| 答题时间统计 | 答题开始时间, 答题结束时间 | 答题耗时 | 3.5.2 |
| 即时反馈生成 | 答案正确性, 连胜次数, 答题时间 | 反馈类型, 反馈内容 | 2.1 |
| 转场文案生成 | 会话状态, 课程信息, 学生进度 | 转场文案, 转场类型 | 2.1, 1.5 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 练习会话.当前进度 | 学生每次完成答题后 | 实时/每次答题 | 2.1, 3.4.2 |
| 答题记录.* | 学生提交答题结果时 | 实时/每次提交 | 2.1, 3.4.2 |
| 进度状态.已完成题目数 | 学生完成一道题目后 | 实时/每次完成 | 2.1, 3.5.2 |
| 进度状态.连胜次数 | 学生答题正确或错误时 | 实时/每次答题 | 2.1, 3.5.2 |
| 即时反馈.* | 学生答题完成并判断正确性后 | 实时/每次答题 | 2.1, 3.4.2 |
| 转场状态.* | 学生进入或退出练习环节时 | 按需/状态变化时 | 2.1, 3.4.1, 3.4.3 |
| 练习会话.计时状态 | 学生暂停、继续或退出练习时 | 实时/状态变化时 | 3.5.2 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 学生ID | 用户中心服务提供，通过JWT Token认证获取 | 2.3, 5.1 |
| 课程ID | 内容平台服务提供，基于课程配置 | 2.3 |
| 题目ID | 内容平台服务提供，通过推题策略获取 | 2.3, 2.1 |
| 题目内容 | 内容平台服务提供，包括题干、选项、答案、难度等 | 2.3, 3.3.2 |
| 学生答案 | 学生客户端输入，通过答题提交接口 | 3.4.2 |
| 正确答案 | 内容平台服务提供，用于答案判断 | 2.3, 3.4.2 |
| 用户认证信息 | 用户中心服务提供，包括用户ID、角色、权限 | 2.3, 3.3.2 |

---

## 7. 当前版本明确排除的数据需求 (例如PRD中的V1.0版本)

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 (源自PRD"非本期功能"等章节) | PRD中说明的排除原因 (若有) | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 长按题目内容唤起问一问功能的数据存储 | 本期不做 | 原始PRD中提及 |
| 多学生协作练习的数据结构 | 未来演进方向，当前版本不支持 | 7 |
| 机器学习推题算法的训练数据存储 | 未来演进方向，当前使用基础推题策略 | 7 |
| 详细的用户行为轨迹数据 | 当前版本仅记录核心答题数据 | 推断 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别练习会话、答题记录、进度状态、即时反馈、转场状态等核心实体 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏（例如，本示例中的"题目信息"、"课程信息"）？                                                         | ⚠️ | 题目信息、课程信息、用户信息依赖外部服务，已在公共服务中定义 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已提取核心属性，包括标识符、状态、时间戳等 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | 属性定义基于技术架构文档中的领域对象定义 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确实体间的关联关系和外键约束 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 已体现与外部服务的依赖关系 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 已列出进度计算、连胜检测、反馈生成等核心计算的输入项 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 已记录实时更新机制和外部数据来源 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 已记录未来演进功能的数据需求排除项 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 术语与技术架构文档保持一致 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 已标注对应的章节号和引用位置 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 提取信息基于技术架构文档的明确描述 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 部分属性的具体约束条件需要在详细设计阶段进一步明确 |

---
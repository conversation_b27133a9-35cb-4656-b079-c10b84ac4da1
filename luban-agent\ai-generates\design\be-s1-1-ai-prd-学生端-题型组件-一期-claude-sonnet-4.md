# 产品需求文档 - 学生端题型组件一期

## 1. 需求背景与目标

### 1.1 需求目标
- 通过实现学生端题型组件，可以提升在线教学环节的核心功能体验，满足校内教学场景的基本需求
- 该需求上线后，学生答题体验预计可以提升60%，支持的题型覆盖率预计可以达到80%（覆盖高频题型）
- 分阶段逐步满足用户在日常学习过程中，对于练习、测验等场景下所需题型的要求

## 2. 功能范围

### 2.1 核心功能
- **单选题组件**: 支持单选题题型，实现完整展示和交互逻辑，满足选择类答题场景，解决基础选择题作答需求
  - 优先级：P0
  - 依赖关系：无
  - 支持二次作答功能
  
- **多选题组件**: 支持多选题题型，实现完整展示和交互逻辑，满足多项选择答题场景，解决复合选择题作答需求
  - 优先级：P0
  - 依赖关系：无
  - 支持二次作答功能
  
- **填空题组件（英语自动批改）**: 支持英语学科填空题的自动批改功能，满足英语填空练习场景，解决英语学科自动化批改需求
  - 优先级：P0
  - 依赖关系：无
  
- **填空题组件（其他学科自评）**: 支持非英语学科填空题的自评功能，满足其他学科填空练习场景，解决多学科填空题作答需求
  - 优先级：P0
  - 依赖关系：无
  
- **解答题组件**: 支持拍照作答、画板及文字输入等多种解答方式，满足主观题作答场景，解决复杂题型作答需求
  - 优先级：P0
  - 依赖关系：无

### 2.2 辅助功能
- **勾画组件**: 为所有题型提供勾画标记功能，支持颜色调整、粗细调整、橡皮擦等工具
- **计时功能**: 为每题提供独立正计时功能，支持暂停和继续计时
- **作答进度跟踪**: 实时显示作答进度和状态
- **答疑chat功能**: 提供智能答疑和自由提问功能
- **错题本功能**: 支持将错题加入错题本

### 2.3 非本期功能
- **母子题支持**: 复杂的母子题结构，建议二期再考虑
- **特殊题型**: 完形填空、阅读理解、七选五等特殊题型，建议三期再考虑
- **高级批改功能**: 更复杂的自动批改算法，建议后续迭代优化

## 3. 计算规则与公式

### 3.1 计时规则
- **计时公式**: 每题独立正计时，格式为MM:SS，上限59:59
  - 参数说明：
    - 开始时间：用户进入题目时开始计时
    - 结束时间：用户提交答案时停止计时
    - 暂停规则：用户未作答退出时暂停计时，下次进入继续计时

### 3.2 自评异常监控规则
- **异常行为检测**: 连续3题批改时长均小于2秒时触发提示
  - 参数说明：
    - 监控窗口：连续3题
    - 时长阈值：2秒
  - 规则：
    - 触发条件：连续3题批改时长 < 2秒
    - 提示内容："认真核对哦"
    - 展示时长：2秒

### 3.3 选项占比计算规则
- **占比显示规则**: 题目使用次数大于20后展示选项占比
  - 参数说明：
    - 冷启动阈值：20次使用
    - 更新频率：每日更新
  - 规则：
    - 取值范围：0-100%，保留一位小数

## 4. 用户场景与故事

### 场景1：学生进行单选题作答
作为一个学生用户，我希望能够在学习过程中完成单选题练习，这样我就可以巩固所学知识并获得即时反馈。目前的痛点是缺乏有效的在线答题工具，如果能够提供直观的单选题界面和智能批改功能，对我的学习会有很大帮助。

**关键步骤：**
1. 查看题目内容和选项
2. 选择认为正确的选项
3. 提交答案并查看结果反馈
4. 查看解析和进行答疑

- 优先级：高
- 依赖关系：无

### 场景2：学生进行多选题作答
作为一个学生用户，我希望能够完成多选题练习，这样我就可以训练复合思维能力。目前的痛点是多选题容易漏选或多选，如果能够提供选项确认提示和二次作答机会，对我的学习准确性会有很大帮助。

**关键步骤：**
1. 查看题目内容和多个选项
2. 选择多个认为正确的选项
3. 确认选择（系统提示是否确认提交）
4. 提交答案并查看详细反馈（正确/部分正确/错误）
5. 如果错误可进行二次作答

- 优先级：高
- 依赖关系：无

### 场景3：学生进行英语填空题作答（自动批改）
作为一个学生用户，我希望能够完成英语填空题并获得即时的自动批改结果，这样我就可以快速了解自己的英语掌握情况。目前的痛点是英语练习缺乏即时反馈，如果能够提供自动批改和标准答案对比，对我的英语学习效率会有很大帮助。

**关键步骤：**
1. 阅读英语题目并识别填空位置
2. 在填空处输入英语单词或短语
3. 提交答案获得自动批改结果
4. 查看正确答案和详细解析

- 优先级：高
- 依赖关系：依赖于自动批改算法

### 场景4：学生进行其他学科填空题自评
作为一个学生用户，我希望能够完成非英语学科的填空题练习并进行自我评价，这样我就可以培养自主学习能力。目前的痛点是缺乏自评机制，如果能够提供标准答案对比和自评选项，对我的学习自主性培养会有很大帮助。

**关键步骤：**
1. 阅读题目并在填空处输入答案
2. 提交答案查看标准答案
3. 对照标准答案进行自我评价（答对了/部分答对/答错了）
4. 查看题目解析和考察知识点

- 优先级：高
- 依赖关系：依赖于自评功能

### 场景5：学生进行解答题作答
作为一个学生用户，我希望能够通过多种方式完成解答题，这样我就可以灵活地表达解题思路。目前的痛点是解答题形式单一，如果能够支持拍照、手写、文字输入等多种作答方式，对我的学习表达会有很大帮助。

**关键步骤：**
1. 阅读解答题题目
2. 选择合适的作答方式（拍照上传/键盘输入）
3. 完成作答内容（支持多张图片上传或长文本输入）
4. 提交作答并进行自评
5. 查看标准答案和解析

- 优先级：高
- 依赖关系：依赖于多媒体上传功能和自评功能

### 场景6：学生使用勾画工具辅助答题
作为一个学生用户，我希望能够在答题过程中对题目进行标记和勾画，这样我就可以更好地理解题意和整理思路。目前的痛点是无法在题目上做标记，如果能够提供勾画工具，对我的解题思维会有很大帮助。

**关键步骤：**
1. 在任意题型中点击勾画按钮
2. 选择勾画工具（画笔、橡皮擦、颜色、粗细）
3. 在题目上进行标记和勾画
4. 保存勾画内容并继续答题

- 优先级：中
- 依赖关系：依赖于基础答题功能

## 5. 业务流程图

```mermaid
graph TD
    A[学生进入题目] --> B{题型判断}
    B -->|单选题| C[单选题组件]
    B -->|多选题| D[多选题组件]
    B -->|填空题| E{学科判断}
    B -->|解答题| F[解答题组件]
    
    E -->|英语| G[自动批改填空题]
    E -->|其他学科| H[自评填空题]
    
    C --> I[选择选项]
    D --> J[选择多个选项]
    G --> K[键盘输入答案]
    H --> L[键盘/拍照输入]
    F --> M[拍照/键盘输入]
    
    I --> N[提交答案]
    J --> N
    K --> N
    L --> N
    M --> N
    
    N --> O{批改方式}
    O -->|自动批改| P[显示批改结果]
    O -->|自评| Q[用户自评]
    
    P --> R[查看解析]
    Q --> R
    R --> S[答疑功能]
    S --> T[继续下一题]
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：题目加载、答案提交、结果反馈的P95响应时间应不超过2秒
- 并发用户数：系统需能支持至少1000个学生用户同时在线答题，且性能无明显下降
- 数据处理能力：系统需能处理每秒100次答题提交请求
- 数据量：系统需支持百万级题目数据和千万级答题记录的存储和高效查询，题目查询响应时间应在1秒内

### 6.2 安全需求
- 用户认证与授权：所有答题接口必须经过学生身份认证，确保答题数据的归属正确性
- 数据保护：学生答题数据和学习记录在存储和传输过程中必须加密，符合教育数据保护要求
- 操作审计：对所有答题行为、成绩修改、错题收藏等操作必须记录详细日志，包含学生ID、操作时间、题目ID、答题内容等关键信息
- 防护要求：系统应具备对恶意刷题、答案泄露等行为的基本防护能力

## 7. 验收标准

### 7.1 功能验收
- **单选题组件**：
  - 使用场景：当学生在练习场景下进行单选题作答时
  - 期望结果：系统应正确展示题目和选项，支持选项选择、答案提交、结果反馈和二次作答功能
  
- **多选题组件**：
  - 使用场景：当学生在练习场景下进行多选题作答时
  - 期望结果：系统应支持多个选项选择，提供选项确认提示，正确处理漏选、多选等情况
  
- **填空题组件**：
  - 使用场景：当学生进行填空题练习时
  - 期望结果：英语学科应支持自动批改，其他学科应支持自评功能，均能正确显示标准答案对比
  
- **解答题组件**：
  - 使用场景：当学生进行解答题作答时
  - 期望结果：系统应支持拍照上传和键盘输入两种作答方式，支持自评功能

### 7.2 性能验收
- 响应时间：
  - 测试场景：模拟100个并发学生同时进行答题操作
  - 验收标准：P95响应时间不超过2秒
  
- 并发用户数：
  - 测试场景：在标准硬件环境下，逐步增加并发用户至1000个，持续执行答题操作30分钟
  - 验收标准：系统应保持稳定运行，无错误率飙升，答题操作的平均响应时间不超过3秒

### 7.3 安全验收
- 用户认证与授权：
  - 测试场景：未登录用户尝试访问答题接口
  - 验收标准：系统应拒绝访问，并返回明确的认证失败提示
  
- 数据保护：
  - 验证点：通过数据库检查和网络抓包验证敏感数据加密情况
  - 验收标准：学生答题数据在存储和传输层面符合加密标准
  
- 操作审计：
  - 测试场景：执行一次完整的答题流程
  - 验收标准：答题日志被准确记录，包含学生ID、题目ID、答题时间、答题内容等必要字段

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：答题界面应简洁直观，学生在无额外培训的情况下，3步内可完成答题操作
- 容错处理：当发生网络中断或误操作时，系统应能自动保存答题进度，避免学生答题数据丢失
- 兼容性：产品核心功能需在Chrome最新版、Safari最新版、iOS最新版、Android主流版本得到良好支持

### 8.2 维护性需求
- 配置管理需求：题目难度系数、计时规则、自评提示文案等关键参数应支持运营人员通过管理后台进行调整
- 监控与告警需求：当答题失败率突增、系统响应时间异常、题目加载失败等情况发生时，应触发告警通知技术支持团队
- 日志需求：学生答题行为、系统异常、第三方服务调用等关键事件必须被记录，支持问题排查和学习行为分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 产品应提供应用内反馈入口、客服邮箱等用户反馈渠道
- 应有机制每周收集、整理和分析来自各渠道的学生和教师反馈，识别产品问题和改进机会

### 9.2 迭代计划（高阶产品方向）
- 产品计划以每双周为周期进行迭代，持续优化答题体验和增加新题型支持
- 下一个迭代重点可能包括：母子题支持、特殊题型扩展、智能推荐功能优化

## 10. 需求检查清单

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
|----------|------------|--------|--------|--------|----------|----------|------|
| 支持单选题题型，实现完整展示和交互逻辑 | 2.1核心功能-单选题组件 + 4.场景1 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 支持多选题题型，实现完整展示和交互逻辑 | 2.1核心功能-多选题组件 + 4.场景2 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 支持填空题题型（英语自动批改） | 2.1核心功能-填空题组件（英语自动批改） + 4.场景3 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 支持填空题题型（其他学科自评） | 2.1核心功能-填空题组件（其他学科自评） + 4.场景4 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 支持拍照作答、画板及文字输入等多种解答方式 | 2.1核心功能-解答题组件 + 4.场景5 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 单选、多选支持二次作答 | 2.1核心功能-单选题和多选题的二次作答功能 + 4.场景1&2 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 每题独立正计时（格式00:00，上限59:59） | 3.1计时规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 用户进入题目开始计时，提交后停止计时 | 3.1计时规则-开始和结束时间 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 学生未作答退出，暂停计时，下次进入后继续计时 | 3.1计时规则-暂停规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 勾画组件支持颜色调整、粗细调整、橡皮擦等 | 2.2辅助功能-勾画组件 + 4.场景6 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目匹配：以学科和题型作为校验条件 | 4.用户场景与故事中体现 + 5.业务流程图 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 流程梳理：初始状态、作答状态、解析状态三个阶段 | 5.业务流程图 + 各场景关键步骤 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 支持键盘输入完成单选题、多选题、填空题作答 | 各题型组件的输入方式 + 对应场景 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 解答题增加拍照上传作答内容功能 | 2.1核心功能-解答题组件 + 4.场景5 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 连续3题批改时长均小于2秒时提示"认真核对哦" | 3.2自评异常监控规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目使用次数大于20后展示选项占比 | 3.3选项占比计算规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 支持自由提问+模型生成的猜你想问问题 | 2.2辅助功能-答疑chat功能 + 各场景步骤4 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 多选题特殊校验：选中选项仅为1时弹窗二次确认 | 4.场景2-关键步骤3 + 7.1功能验收 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 填空题支持多个作答区，按顺序标记1、2、3... | 4.场景3&4中体现 + 2.1核心功能描述 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 解答题支持最多4张图片上传 | 4.场景5-关键步骤3 + 2.1核心功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 自评功能：我答对了/部分答对/我答错了三个选项 | 4.场景4&5中体现 + 3.2异常监控规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 二期支持母子题 | 2.3非本期功能-母子题支持 | ✅ | ✅ | ✅ | N/A | ✅ | |
| 三期支持完形填空、阅读理解、七选五等特殊题型 | 2.3非本期功能-特殊题型 | ✅ | ✅ | ✅ | N/A | ✅ | |

## 11. 附录

### 11.1 原型图
详细的UI原型图和交互设计已在原始需求文档中提供，包含各题型的初始状态、作答状态、解析状态的完整界面设计。

### 11.2 术语表
- **题型组件**: 支持特定题目类型的答题界面组件
- **自动批改**: 系统自动判断答案正误的功能
- **自评**: 学生对照标准答案进行自我评价的功能
- **二次作答**: 在首次作答错误后允许再次作答的功能
- **勾画组件**: 支持在题目上进行标记和涂鸦的工具
- **母子题**: 包含主题目和多个子题目的复合题型

### 11.3 参考资料
- Format-PRD-学生端-题型组件-一期.md（原始需求文档）
- 相关设计稿：Figma设计链接
- 关联需求：PRD - 学生端 - 巩固练习
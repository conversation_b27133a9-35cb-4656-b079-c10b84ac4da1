# AI课中练习组件1.0产品需求文档

**文档版本**: V1.0  
**创建日期**: 2025-05-24  
**最后更新**: 2025-05-24  
**文档状态**: 待评审  

## 1. 需求背景与目标

### 1.1 需求目标
- 通过优化AI课中练习组件的交互体验，提升学生作答流畅度和情感反馈体验，增强课程满意度与学习效果
- 该需求上线后，学生课中练习完成率预计可以提升15%，学生课程满意度预计可以达到85%以上

## 2. 功能范围

### 2.1 核心功能
- **进入练习转场动效**: 提供友好的练习环节进入提示和转场动画，包括首次进入和再次进入的不同场景处理，满足学生从课程内容到练习环节的平滑过渡需求，解决突兀进入练习导致的体验断层问题
  - 优先级：高
  - 依赖关系：无

- **练习环节核心交互**: 提供完整的练习答题界面，包括顶部导航、题型展示、作答计时、进度管理等核心功能，满足学生完整的练习作答需求，解决练习功能不完整的问题
  - 优先级：高
  - 依赖关系：无

- **题目间即时反馈机制**: 提供作答后的即时反馈展示，包括正确/错误反馈、连胜提示等情感激励，满足学生获得及时学习反馈的需求，解决作答后缺乏反馈导致的参与感不足问题
  - 优先级：高
  - 依赖关系：依赖于练习环节核心交互

### 2.2 辅助功能
- **勾画功能复用**: 复用巩固练习中的勾画组件，为练习环节提供辅助标记支持
- **错题本功能复用**: 复用巩固练习中的错题本组件，为练习环节提供错题收集支持
- **答疑组件集成**: 集成答疑组件，为练习环节提供实时问答支持

### 2.3 非本期功能
- **长按题目唤起问一问**: 通过长按题目内容唤起答疑功能，建议后续迭代再考虑
- **互动讲题模块**: 基于题目分布讲解和互动的功能，建议后续迭代再考虑

## 3. 计算规则与公式

### 3.1 作答进度计算规则
- **进度计算公式**: 当前进度 = 已完成题目数 / 预估学生作答数量
  - 参数说明：
    - 已完成题目数：根据推题策略动态计算
    - 预估学生作答数量：系统预设的该练习环节预期题目数量
  - 规则：
    - 学生答对时：进度增加
    - 学生答错但没有相似题时：进度增加  
    - 学生答错且有相似题需要再练时：进度不变

### 3.2 作答计时规则
- **计时公式**: 每题独立正计时，格式为MM:SS，上限59:59
  - 参数说明：
    - 计时起点：题目加载完成时开始
    - 计时终点：学生提交答案时结束
  - 规则：
    - 取值范围：00:00 - 59:59
    - 暂停条件：学生未作答退出时暂停计时
    - 恢复条件：下次进入时在上次计时基础上继续

## 4. 用户场景与故事

### 场景1：首次进入练习环节
作为一个正在上AI课的学生，我希望能够在完成课程内容学习后平滑地进入练习环节，这样我就可以保持学习的连贯性和专注度。目前的痛点是从课程内容直接跳转到练习会让我感到突兀，如果能够有友好的转场提示和动画，对我的学习体验会有很大帮助。

**关键步骤：**
1. 学生完成前置课程组件学习
2. 系统检测到即将进入练习组件，显示"即将进入练习"提示
3. 学生点击进入练习，触发转场动画（翻页效果）
4. 显示IP动效和"开始练习+课程名称"文案，持续2秒
5. 进入练习答题界面

```mermaid
sequenceDiagram
    participant Student as 学生
    participant CourseSystem as 课程系统
    participant ExerciseComponent as 练习组件
    
    Student->>CourseSystem: 完成前置课程内容
    CourseSystem->>CourseSystem: 检测下一组件为练习组件
    CourseSystem->>Student: 显示"即将进入练习"提示
    Student->>ExerciseComponent: 点击进入练习
    ExerciseComponent->>ExerciseComponent: 触发转场动画（翻页）
    ExerciseComponent->>Student: 显示IP动效+"开始练习+课程名称"
    Note over ExerciseComponent: 持续2秒
    ExerciseComponent->>Student: 进入练习答题界面
```

- 优先级：高
- 依赖关系：无

### 场景2：再次进入练习环节
作为一个曾经退出课程的学生，我希望能够在重新进入课程时快速恢复到练习环节的进度，这样我就可以继续之前未完成的练习。目前的痛点是不清楚自己的练习进度状态，如果能够有明确的继续练习提示，对我恢复学习会有很大帮助。

**关键步骤：**
1. 学生重新进入课程，系统检测到当前进度在练习组件
2. 系统显示IP动效和"继续练习+课程名称"文案，持续2秒
3. 进入练习答题界面，恢复之前的作答进度和计时状态

```mermaid
sequenceDiagram
    participant Student as 学生
    participant CourseSystem as 课程系统
    participant ExerciseComponent as 练习组件
    participant ProgressService as 进度服务
    
    Student->>CourseSystem: 重新进入课程
    CourseSystem->>ProgressService: 查询学习进度
    ProgressService->>CourseSystem: 返回当前进度在练习组件
    CourseSystem->>ExerciseComponent: 进入练习组件
    ExerciseComponent->>Student: 显示IP动效+"继续练习+课程名称"
    Note over ExerciseComponent: 持续2秒
    ExerciseComponent->>ProgressService: 获取练习进度和计时状态
    ProgressService->>ExerciseComponent: 返回进度数据
    ExerciseComponent->>Student: 恢复练习答题界面
```

- 优先级：高
- 依赖关系：依赖于场景1

### 场景3：练习作答与即时反馈
作为一个正在练习的学生，我希望能够在作答后立即获得反馈，这样我就可以及时了解自己的答题情况并保持学习动力。目前的痛点是作答后不知道对错，缺乏成就感，如果能够有即时的正确/错误反馈和连胜提示，对我的学习积极性会有很大帮助。

**关键步骤：**
1. 学生在练习界面查看题目并选择答案
2. 学生点击提交答案
3. 系统判断答案正确性并显示即时反馈
4. 如果连续答对，显示连胜提示和鼓励动画
5. 学生点击继续进入下一题或完成练习

```mermaid
sequenceDiagram
    participant Student as 学生
    participant ExerciseComponent as 练习组件
    participant AnswerService as 答题服务
    participant FeedbackService as 反馈服务
    
    Student->>ExerciseComponent: 查看题目
    Student->>ExerciseComponent: 选择答案
    Student->>ExerciseComponent: 点击提交
    ExerciseComponent->>AnswerService: 提交答案进行判断
    AnswerService->>AnswerService: 判断答案正确性
    AnswerService->>ExerciseComponent: 返回判断结果
    ExerciseComponent->>FeedbackService: 请求反馈内容
    FeedbackService->>FeedbackService: 检查连胜状态
    FeedbackService->>ExerciseComponent: 返回反馈内容
    ExerciseComponent->>Student: 显示答题反馈
    alt 连续答对
        ExerciseComponent->>Student: 显示连胜提示和鼓励动画
    end
    Student->>ExerciseComponent: 点击继续
```

- 优先级：高
- 依赖关系：依赖于场景1和场景2

## 5. 业务流程图

```mermaid
flowchart TD
    A[学生进入课程] --> B{检查学习进度}
    B -->|首次进入练习| C[显示'即将进入练习'提示]
    B -->|再次进入练习| D[显示'继续练习'转场]
    C --> E[触发进入转场动画]
    D --> E
    E --> F[进入练习答题界面]
    F --> G[加载题目]
    G --> H[学生查看题目]
    H --> I[学生选择答案]
    I --> J[学生提交答案]
    J --> K{判断答案}
    K -->|正确| L[显示正确反馈]
    K -->|错误| M[显示错误反馈]
    L --> N{检查连胜状态}
    M --> O{检查是否有相似题}
    N -->|达成连胜| P[显示连胜提示]
    N -->|未达成连胜| Q[更新进度]
    P --> Q
    O -->|有相似题| R[推送相似题目]
    O -->|无相似题| Q
    R --> G
    Q --> S{检查练习是否完成}
    S -->|未完成| T[加载下一题]
    S -->|已完成| U[触发离开转场]
    T --> G
    U --> V[返回课程流程]
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：练习组件加载的响应时间不超过2秒
- 并发用户：系统需支持1000个并发用户同时进行练习，保证性能不衰减
- 转场动画：转场动画播放流畅，帧率不低于30fps

### 6.2 安全需求
- 权限控制：练习功能需要学生身份验证才能访问
- 数据加密：学生答题数据需要使用AES-256算法进行加密存储和传输
- 操作审计：对学生答题行为需要记录操作日志，包括答题时间、答案内容、IP地址等关键字段

## 7. 验收标准

### 7.1 功能验收
- **进入练习转场动效**：
  - 使用场景：当学生首次进入练习组件时
  - 期望结果：系统应显示"即将进入练习"提示，播放翻页转场动画，显示IP动效和"开始练习+课程名称"文案2秒后进入答题界面
- **练习环节核心交互**：
  - 使用场景：当学生在练习界面进行答题时
  - 期望结果：系统应正确显示题目、选项、计时器、进度条，支持答案选择和提交功能
- **题目间即时反馈机制**：
  - 使用场景：当学生提交答案后
  - 期望结果：系统应立即显示正确/错误反馈，连续答对时显示连胜提示和鼓励动画

### 7.2 性能验收
- 响应时间：
  - 给定正常网络条件，当加载练习组件，响应时间不超过2秒
  - 给定正常网络条件，当提交答案，响应时间不超过1秒
- 并发用户：
  - 给定1000个并发用户，模拟同时进行练习，系统运行30分钟，无异常，平均响应时间不超过3秒

### 7.3 安全验收
- 权限控制：
  - 给定未登录用户，当访问练习功能，返回身份验证错误
  - 给定已登录学生用户，当访问练习功能，可以正常使用
- 数据加密：
  - 给定学生答题数据，当存储到数据库，数据是加密状态，且使用AES-256算法
- 操作审计：
  - 给定学生答题操作，当提交答案，操作日志正常记录，包含答题时间、答案内容、IP地址等关键字段

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：练习界面操作简洁友好，3步内可完成答题提交操作
- 容错处理：网络异常发生时，系统可以保存当前答题进度，并给出友好提示
- 兼容性：需支持Chrome/Safari/移动端浏览器等主流环境，保证界面和功能正常

### 8.2 维护性需求
- 配置管理：转场动画时长、反馈文案等参数可由系统管理员在后台界面进行配置和调整
- 监控告警：练习组件异常发生时，系统自动告警，通知相关技术人员
- 日志管理：系统自动记录练习相关操作日志，日志保存30天，可供系统管理员查询和分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 用户可以通过课程内反馈按钮和客服系统提交反馈
- 每周定期收集和分析用户反馈，识别改进点

### 9.2 迭代计划
- 迭代周期：每2周
- 每次迭代结束后，评估需求实现情况和用户反馈，调整下一个迭代的优先级和计划

## 10. 需求检查清单

使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
| ----------- | --------------- | ------ | ------ | ------ | -------- | -------- | ---- |
| 进入练习提示：练习组件最顶部，增加"即将进入练习" tip | 进入练习转场动效-练习提示 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 首次进入练习转场：翻页动效+IP动效+文案展示2s | 进入练习转场动效-首次进入 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再次进入练习转场：IP动效+继续练习文案展示2s | 进入练习转场动效-再次进入 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 离开练习转场：翻页动效 | 进入练习转场动效-离开转场 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 顶部导航栏：退出按钮、作答计时、作答进度 | 练习环节核心交互-顶部导航 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 每题独立正计时，格式00:00，上限59:59 | 作答计时规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 学生未作答退出，暂停计时，下次进入继续计时 | 作答计时规则-暂停恢复 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 作答进度：分母为预估学生作答数量，分子依据推题策略 | 作答进度计算规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 连对触发效果：同巩固练习 | 题目间即时反馈机制-连胜提示 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题型组件：各题型在练习中的展示和交互 | 练习环节核心交互-题型展示 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目推荐：见课中题目推荐策略 | 练习环节核心交互-题目推荐 | ✅ | ✅ | ✅ | N/A | ✅ | 依赖外部策略文档 |
| 勾画组件：同巩固练习 | 勾画功能复用 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 错题本组件：同巩固练习 | 错题本功能复用 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 答疑组件：右上角常驻展示"问一问" | 答疑组件集成-入口展示 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 长按题目内容，唤起问一问（本期不做） | 非本期功能-长按唤起问答 | ✅ | ✅ | ✅ | N/A | ✅ | |
| 题目进入解析状态前屏蔽问一问入口 | 答疑组件集成-触发时机 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目间转场：逻辑和展示同巩固练习 | 题目间即时反馈机制 | ✅ | ✅ | ✅ | ✅ | ✅ | |

## 11. 附录

### 11.1 原型图
参考原始需求文档中的UI设计图，包括：
- 进入练习转场界面设计
- 练习答题界面设计  
- 题目间反馈界面设计

### 11.2 术语表
- **IP动效**: 知识产权形象的动画效果，用于品牌展示和用户体验优化
- **转场动画**: 页面或组件之间切换时的过渡动画效果
- **连胜机制**: 连续答对题目时的奖励反馈机制
- **推题策略**: 根据学生答题情况智能推荐下一道题目的算法策略

### 11.3 参考资料
- [PRD - AI课中 - V1.0](https://wcng60ba718p.feishu.cn/wiki/XtfswiCCQiKHdgkev4Qc1Nqined)
- [PRD - 题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)
- [PRD - 巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f)
- [PRD - 课中练习推题策略 - V1.0](https://wcng60ba718p.feishu.cn/wiki/VMEawv89PiINjUkaLxncnSYWnqb)
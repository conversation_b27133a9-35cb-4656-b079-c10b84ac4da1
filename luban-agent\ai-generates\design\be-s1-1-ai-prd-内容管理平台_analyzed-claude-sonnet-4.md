# 内容管理平台产品需求文档

## 1. 需求背景与目标

### 1.1 需求目标
- 通过构建统一的内容管理平台，实现对教育资源（基础树、题目、试卷、课程等）的标准化管理，提升资源管理效率30%以上
- 该需求上线后，内容审核效率预计可以提升50%，内容质量保障率预计可以达到95%以上
- 建立完善的知识体系和业务体系，支持资源的精准分类与检索，满足多场景使用需求
- 提供高效的内容创作、题目录入及审核工具，确保内容的高质量输出，同时缩短审核周期40%

## 2. 功能范围

### 2.1 核心功能
- **内容管理平台框架**: 构建统一平台，实现对基础树、业务树、题目、试卷、课程等资源管理
  - 优先级：高
  - 依赖关系：无
- **题目管理**: 支持题目的查看、检索、录入、编辑、纠错等功能，包含按知识树和业务树查题
  - 优先级：高
  - 依赖关系：依赖于内容管理平台框架
- **资源录入**: 提供将Word文档下载题目和试卷录入平台并完成题干解析和题目标签标记等功能
  - 优先级：高
  - 依赖关系：依赖于内容管理平台框架
- **场景题库选题**: 实现可以按场景从题目管理中选题或录入新题，支持巩固练习、拓展练习等场景
  - 优先级：高
  - 依赖关系：依赖于题目管理
- **审核功能**: 支持对题目录入与选题的审核，包括状态流转、反馈与通知机制
  - 优先级：高
  - 依赖关系：依赖于题目管理和资源录入

### 2.2 辅助功能
- **试卷管理**: 支持试卷的查看、检索、录入、预览与下载功能
  - 优先级：中
  - 依赖关系：依赖于题目管理
- **基础数据管理**: 支持基础树与业务树的查看、筛选、搜索及树形展示
  - 优先级：中
  - 依赖关系：为核心功能提供基础数据支持
- **权限管理**: 根据用户权限展示相应的导航菜单和功能模块
  - 优先级：中
  - 依赖关系：为所有功能提供权限控制支持

### 2.3 非本期功能
- **高级数据分析**: 内容使用情况统计分析和智能推荐，建议迭代2再考虑
- **多语言支持**: 国际化功能，建议迭代3再考虑
- **移动端适配**: 移动设备专用界面，建议迭代2再考虑

## 3. 计算规则与公式

### 3.1 难度分级规则
- **难度等级**: 五档难度分级
  - 公式: 难度级别 ∈ {易, 较易, 中档, 较难, 难}
  - 参数说明：
    - 易: 基础概念题目，正确率>80%
    - 较易: 简单应用题目，正确率60%-80%
    - 中档: 综合应用题目，正确率40%-60%
    - 较难: 复杂分析题目，正确率20%-40%
    - 难: 高难度综合题目，正确率<20%
  - 规则：
    - 取值范围：每个场景题库各难度级别必须达到最低题目数量要求

### 3.2 选题进度计算规则
- **选题完成度计算**
  - 公式: 完成度 = 已选题目数量 / 最低题目要求数量 × 100%
  - 参数说明：
    - 已选题目数量: 当前难度级别下已选择的题目数量
    - 最低题目要求数量: 该难度级别要求的最少题目数量
  - 规则：
    - 取值范围：0% ≤ 完成度 ≤ 100%
    - 只有所有难度级别完成度达到100%才能提交上架

## 4. 用户场景与故事

### 场景1：内容录入人员批量导入题目
作为一个内容录入人员，我希望能够通过上传Word文档批量导入题目，这样我就可以快速扩充题库内容。目前的痛点是手动录入题目效率低下且容易出错，如果能够支持Word文档自动解析和批量导入，对我的工作效率会有很大帮助。

**关键步骤：**
1. 选择学段学科，进入题目录入模块
2. 上传包含题目的Word文档
3. 系统后台自动识别和解析题目内容
4. 确认解析结果并提交审核

```mermaid
sequenceDiagram
    participant U as 录入人员
    participant S as 内容管理系统
    participant AI as 大模型处理
    participant A as 审核人员
    
    U->>S: 上传Word文档
    S->>AI: 调用OCR+大模型处理
    AI->>S: 返回解析结果
    S->>U: 展示解析后的题目
    U->>S: 确认并提交
    S->>A: 推送审核任务
    A->>S: 完成审核
    S->>U: 通知审核结果
```

- 优先级：高
- 依赖关系：无

### 场景2：学科教研人员按知识点查找题目
作为一个学科教研人员，我希望能够按照知识树结构快速查找相关题目，这样我就可以为不同的教学场景组织合适的练习内容。目前的痛点是题目分散且难以按知识点精准定位，如果能够提供知识树导航和精准筛选功能，对我的教研工作会有很大帮助。

**关键步骤：**
1. 选择学段学科进入题目管理
2. 通过知识树或业务树导航定位知识点
3. 使用筛选条件（类型、难度、年份等）精确查找
4. 查看题目详情包括答案和解析

```mermaid
flowchart TD
    A[选择学段学科] --> B[选择查题方式]
    B --> C[按知识树查题]
    B --> D[按业务树查题]
    C --> E[选择知识点节点]
    D --> F[选择业务树节点]
    E --> G[应用筛选条件]
    F --> G
    G --> H[查看题目列表]
    H --> I[查看题目详情]
    I --> J[查看答案解析]
```

- 优先级：高
- 依赖关系：依赖于场景1的题目数据

### 场景3：教师创建场景题库
作为一个教师，我希望能够为特定的教学场景（如巩固练习、拓展练习）创建专门的题库，这样我就可以为学生提供针对性的练习内容。目前的痛点是无法按教学场景组织题目，如果能够支持场景化题库管理和选题功能，对我的教学工作会有很大帮助。

**关键步骤：**
1. 选择教材版本和业务树节点
2. 创建场景题库（巩固练习/拓展练习）
3. 按难度级别选择题目达到最低数量要求
4. 提交上架审核

```mermaid
stateDiagram-v2
    [*] --> 选择教材版本
    选择教材版本 --> 选择业务树节点
    选择业务树节点 --> 创建场景题库
    创建场景题库 --> 选题阶段
    选题阶段 --> 检查题目数量
    检查题目数量 --> 选题阶段 : 数量不足
    检查题目数量 --> 提交上架 : 数量满足
    提交上架 --> 审核中
    审核中 --> 审核通过 : 通过
    审核中 --> 修改选题 : 不通过
    修改选题 --> 选题阶段
    审核通过 --> [*]
```

- 优先级：高
- 依赖关系：依赖于场景1和场景2

### 场景4：审核人员审核题目内容
作为一个审核人员，我希望能够高效地审核录入的题目和选题任务，这样我就可以确保平台内容的质量。目前的痛点是审核任务分散且缺乏统一的审核标准，如果能够提供集中的审核工作台和标准化的审核流程，对我的审核工作会有很大帮助。

**关键步骤：**
1. 查看待审核任务列表
2. 进入具体审核任务查看详情
3. 逐一审核题目内容（题干、答案、解析、知识点）
4. 标记通过或不通过并填写原因
5. 提交审核结果

```mermaid
flowchart TD
    A[审核任务列表] --> B{选择任务类型}
    B --> C[题目录入审核]
    B --> D[选题任务审核]
    C --> E[查看Word解析结果]
    D --> F[查看选题详情]
    E --> G[逐题审核]
    F --> H[按难度审核题目]
    G --> I{审核结果}
    H --> I
    I --> J[标记通过]
    I --> K[标记不通过]
    K --> L[填写不通过原因]
    J --> M[提交审核结果]
    L --> M
    M --> N[通知相关人员]
```

- 优先级：高
- 依赖关系：依赖于场景1和场景3

### 场景5：管理员管理基础数据和业务树
作为一个系统管理员，我希望能够管理基础树和业务树的结构，这样我就可以为内容分类提供标准化的知识体系支撑。目前的痛点是知识点体系缺乏统一管理，如果能够提供树形结构的可视化管理功能，对系统的基础数据维护会有很大帮助。

**关键步骤：**
1. 进入基础数据管理模块
2. 选择基础树管理或业务树管理
3. 查看树形结构，支持展开/收起节点
4. 添加、编辑、删除树节点
5. 建立教材版本与业务树的映射关系

```mermaid
flowchart TD
    A[基础数据管理] --> B{选择管理类型}
    B --> C[基础树管理]
    B --> D[业务树管理]
    C --> E[查看知识点树结构]
    D --> F[查看业务分类树结构]
    E --> G[节点操作]
    F --> G
    G --> H[添加节点]
    G --> I[编辑节点]
    G --> J[删除节点]
    G --> K[移动节点]
    D --> L[教材版本映射]
    L --> M[建立映射关系]
```

- 优先级：中
- 依赖关系：为其他场景提供基础数据支撑

### 场景6：教师查看和管理试卷
作为一个教师，我希望能够查看、检索和管理试卷资源，这样我就可以为教学准备合适的测试材料。目前的痛点是试卷资源分散且难以按条件筛选，如果能够提供统一的试卷管理功能，对我的教学准备工作会有很大帮助。

**关键步骤：**
1. 进入试卷管理模块
2. 按学段学科筛选试卷
3. 使用搜索和排序功能定位试卷
4. 预览试卷内容
5. 下载试卷Word文件

```mermaid
flowchart TD
    A[试卷管理] --> B[选择学段学科]
    B --> C[应用筛选条件]
    C --> D[类型筛选]
    C --> E[年份筛选]
    C --> F[来源筛选]
    D --> G[试卷列表展示]
    E --> G
    F --> G
    G --> H[排序选择]
    H --> I[按更新时间排序]
    H --> J[按下载数量排序]
    I --> K[试卷操作]
    J --> K
    K --> L[预览试卷]
    K --> M[下载试卷]
```

- 优先级：中
- 依赖关系：依赖于题目管理功能

### 场景7：用户接收和处理系统通知
作为一个平台用户，我希望能够及时接收到系统通知（如审核结果、任务状态变更），这样我就可以及时了解工作进展并采取相应行动。目前的痛点是信息传达不及时，如果能够提供统一的消息通知机制，对我的工作协调会有很大帮助。

**关键步骤：**
1. 系统自动生成通知消息
2. 用户登录后查看通知列表
3. 点击查看具体通知内容
4. 根据通知内容采取相应行动
5. 标记通知为已读

```mermaid
sequenceDiagram
    participant S as 系统
    participant N as 通知服务
    participant U as 用户
    
    S->>N: 触发通知事件
    N->>N: 生成通知消息
    N->>U: 推送通知
    U->>N: 查看通知列表
    N->>U: 返回通知内容
    U->>N: 标记已读
    N->>S: 更新通知状态
```

- 优先级：中
- 依赖关系：依赖于各业务流程的状态变更

### 场景8：系统管理员进行权限配置
作为一个系统管理员，我希望能够为不同用户分配合适的权限，这样我就可以确保系统安全和功能的合理使用。目前的痛点是权限管理复杂且容易出错，如果能够提供简化的权限配置功能，对系统管理会有很大帮助。

**关键步骤：**
1. 进入用户权限管理模块
2. 查看用户列表和当前权限
3. 为用户分配查看权限或编辑权限
4. 按模块设置权限范围
5. 保存权限配置

```mermaid
flowchart TD
    A[权限管理] --> B[用户管理]
    B --> C[查看用户列表]
    C --> D[选择用户]
    D --> E[权限配置]
    E --> F[查看权限]
    E --> G[编辑权限]
    F --> H[模块权限设置]
    G --> H
    H --> I[题目管理权限]
    H --> J[基础数据管理权限]
    H --> K[审核权限]
    I --> L[保存配置]
    J --> L
    K --> L
```

- 优先级：低
- 依赖关系：为所有功能模块提供权限控制基础

## 5. 业务流程图

```mermaid
flowchart TD
    subgraph "内容生产流程"
        A[Word文档上传] --> B[OCR+大模型处理]
        B --> C[题目录入审核]
        C --> D{审核是否通过}
        D -->|是| E[数据入库]
        D -->|否| F[打回修改]
        F --> C
        E --> G[记录操作日志]
    end
    
    subgraph "场景题库管理流程"
        H[选择教材版本] --> I[选择业务树节点]
        I --> J[创建场景题库]
        J --> K[按难度选题]
        K --> L{题目数量是否满足}
        L -->|否| K
        L -->|是| M[提交上架]
        M --> N[选题审核]
        N --> O{审核是否通过}
        O -->|是| P[自动上架]
        O -->|否| Q[异常处理]
        Q --> K
    end
    
    subgraph "基础数据管理"
        R[基础树管理] --> S[知识点体系维护]
        T[业务树管理] --> U[教材版本映射]
        S --> V[支撑题目分类]
        U --> V
    end
    
    E --> K
    V --> I
    V --> C
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：题目查询功能的响应时间不超过2秒
- 并发用户：系统需支持100个并发用户，保证性能不衰减
- 数据量：系统需支持100万条题目记录的存储和查询，响应时间不超过3秒
- 文件处理：Word文档解析处理时间不超过30秒

### 6.2 安全需求
- 权限控制：审核功能需要审核员权限才能访问，编辑功能需要编辑权限才能访问
- 数据加密：用户密码和敏感配置信息需要使用AES-256进行加密存储和传输
- 操作审计：对题目录入、审核、上架等敏感操作需要记录操作日志，包括操作人、操作时间、操作内容、操作结果

## 7. 验收标准

### 7.1 功能验收
- **题目管理功能**：
  - 使用场景：当用户在高中数学学科下按知识树查找"复数的概念"相关题目时
  - 期望结果：系统应返回该知识点下的所有题目，支持按难度、类型、年份筛选，每页显示20条记录
- **资源录入功能**：
  - 使用场景：当用户上传包含10道题目的Word文档时
  - 期望结果：系统应自动解析出10道题目的题干、选项、答案、解析，准确率达到90%以上
- **场景题库功能**：
  - 使用场景：当用户为"复数的概念"创建巩固练习题库时
  - 期望结果：系统应支持按五档难度选题，每个难度至少选择规定数量的题目才能提交上架
- **审核功能**：
  - 使用场景：当审核员审核题目录入任务时
  - 期望结果：系统应展示所有待审核题目，支持逐题标记通过/不通过，并要求填写不通过原因

### 7.2 性能验收
- 响应时间：
  - 给定包含1000道题目的题库，当执行按知识点查询操作，响应时间不超过2秒
  - 给定10MB的Word文档，当执行OCR+大模型处理，处理时间不超过30秒
- 并发用户：
  - 给定100个并发用户同时进行题目查询操作，系统运行30分钟，无异常，平均响应时间不超过3秒

### 7.3 安全验收
- 权限控制：
  - 给定普通录入人员账号，当访问审核功能时，返回权限不足错误
  - 给定审核员账号，当访问审核功能时，可以正常使用所有审核功能
- 数据加密：
  - 给定用户密码存储，当查询数据库中的密码字段，数据是加密状态，且使用AES-256算法
- 操作审计：
  - 给定题目审核操作，当执行审核通过/不通过操作，操作日志正常记录，包含操作人、操作时间、题目ID、审核结果、审核意见

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：题目管理的操作界面简洁友好，3步内可完成主要查询和筛选操作
- 容错处理：Word文档解析失败时，系统可以提供详细的错误信息和重新上传选项，并给出友好提示
- 兼容性：需支持Chrome、Firefox、Safari等主流浏览器，保证界面和功能正常

### 8.2 维护性需求
- 配置管理：题目数量要求、难度级别定义等参数可由系统管理员在后台界面进行配置和调整
- 监控告警：系统异常、审核积压等情况发生时，系统自动告警，通知相关管理员
- 日志管理：系统自动记录用户操作、系统错误、性能指标等关键日志，日志保存90天，可供系统管理员查询和分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 用户可以通过系统内反馈功能和客服邮箱提交反馈
- 每周定期收集和分析用户反馈，识别改进点

### 9.2 迭代计划
- 迭代周期：每4周
- 每次迭代结束后，评估需求实现情况和用户反馈，调整下一个迭代的优先级和计划

## 10. 需求检查清单

使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
|----------|------------|--------|--------|--------|----------|----------|------|
| 内容管理平台框架构建 | 2.1核心功能-内容管理平台框架 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目管理（查看、检索、录入、编辑、纠错） | 2.1核心功能-题目管理 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 试卷管理（查看、检索、录入、预览、下载） | 2.2辅助功能-试卷管理 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 资源录入（Word文档解析和标签标记） | 2.1核心功能-资源录入 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 场景题库选题（AI课、巩固练习、拓展练习） | 2.1核心功能-场景题库选题 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 基础数据管理（基础树、业务树） | 2.2辅助功能-基础数据管理 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 审核功能（状态流转、反馈、通知） | 2.1核心功能-审核功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 按知识树查题功能 | 4.场景2-按知识点查找题目 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 按业务树查题功能 | 4.场景2-按知识点查找题目 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| Word文档上传和OCR+大模型处理 | 4.场景1-批量导入题目 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 五档难度分级（易、较易、中档、较难、难） | 3.1难度分级规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 教材版本字段和映射关系 | 4.场景5-基础数据管理 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 审核任务排序规则（待审核优先，时间倒序） | 4.场景4-审核流程 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 知识点校验不可为空 | 7.1功能验收-审核功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目字段管理（学段、学科、类型、难度等） | 2.1核心功能-题目管理 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 试卷字段管理（浏览量、下载量、题目数量） | 4.场景6-试卷管理 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 权限管理（查看权限、编辑权限） | 4.场景8-权限配置 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 审核不通过处理逻辑 | 4.场景4-审核流程 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 选题进度管理（按难度统计） | 3.2选题进度计算规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 上架条件校验（最少题目数量） | 4.场景3-创建场景题库 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 消息通知机制（审核结果、系统通知） | 4.场景7-系统通知 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 基础树和业务树的树形展示和管理 | 4.场景5-基础数据管理 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 试卷预览和下载功能 | 4.场景6-试卷管理 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 用户权限按模块分配 | 4.场景8-权限配置 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 审核撤回功能 | 4.场景4-审核流程 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 异常处理流程（审核不通过后的处理） | 4.场景4-审核流程 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 操作日志记录 | 6.2安全需求-操作审计 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目移出校验（不能低于最小数量） | 4.场景3-创建场景题库 | ✅ | ✅ | ✅ | ✅ | ✅ | |

## 11. 附录

### 11.1 原型图
原型图详见原始需求文档中的界面设计图，包括：
- 内容管理平台框架导航图
- 题目管理界面原型
- 资源录入流程图
- 场景题库选题界面
- 审核工作台界面

### 11.2 术语表
- **基础树**：以学科为单位，将所有知识点以树形结构进行组织的知识体系
- **业务树**：基础树的子集，根据实际使用场景（如教材版本、教学阶段）进行组织的分类体系
- **场景题库**：按照不同教学或练习场景（如AI课、巩固练习、拓展练习）组织的题库
- **OCR**：光学字符识别技术，用于从图像中识别文字
- **大模型处理**：使用人工智能大语言模型对文本内容进行理解和结构化处理

### 11.3 参考资料
- 原始需求文档：PRD - 内容管理平台_analyzed.md
- 产品需求文档模板：be-s1-1-ai-prd-template-v2.2.md

--- 等待您的命令，指挥官
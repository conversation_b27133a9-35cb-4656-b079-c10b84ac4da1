'use client';

import Link from 'next/link';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  // 简单打印错误信息到控制台
  console.error('发生错误:', error);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            出错了
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            {error.message || "发生了一个错误，请尝试刷新页面"}
          </p>
        </div>
        <div className="flex flex-col items-center justify-center space-y-4">
          <button
            onClick={() => reset()}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
          >
            重试
          </button>
          <Link
            href="/"
            className="text-blue-600 hover:text-blue-800 text-sm font-medium"
          >
            返回首页
          </Link>
        </div>
      </div>
    </div>
  );
} 
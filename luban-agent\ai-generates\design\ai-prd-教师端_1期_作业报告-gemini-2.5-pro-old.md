# AI 产品需求文档 - 教师端作业报告 - V1.0

## 1. 修订历史

| 版本  | 日期       | 修订人 | 修订说明 |
| :---- | :--------- | :----- | :------- |
| 1.0   | {生成日期} | Gemini | 初稿     |

## 2. 引言

### 2.1. 项目背景与目标

#### 2.1.1. 需求背景

*   **业务层面**：
    *   教师在授课后，核心关注点转变为把控学生学习情况，需要从学生粒度（完成度、正确率、错题数、用时）和答题粒度（共性错题、题目正确率、选项分布、用时）以及提问粒度（共性知识点、提问内容）进行分析。
    *   教师需要扮演"鼓励者"的角色，及时对学生的进步进行表扬（如系统推荐的进步、连续答对等），并对不当行为（如未完成作业、情况下降等）进行沟通关注。
*   **技术层面**：
    *   需要构建任务、素材和学生之间的底层数据关系，以支撑报告功能。 P0 核心功能聚焦于课程作业资源的"学生报告"、"答题结果"、"学生提问"三个 Tab。

#### 2.1.2. 项目目标与价值

*   完成教师端 P0 核心功能，确保 6 月上线版本能跑通业务 MVP 流程。
*   支撑 9 月完成全部核心功能的发布。
*   为教师提供有效的学情分析工具，提升教学效率和学生辅导的针对性。

#### 2.1.3. 目标用户

*   合作校的教师（包括学科老师、班主任、学科主任、年级主任、校长等，不同角色权限不同）。

### 2.2. 名词解释

*   *(原始文档此部分为空，暂无)*

### 2.3. 相关文档

*   原始需求文档：[documents/raw-docs/教师端_1期_作业报告.md]
*   口径说明：[https://wcng60ba718p.feishu.cn/wiki/DXKawJOESiypQ3kt3vbc64DgnTc?sheet=e3d974]
*   鼓励/关注文案策略：[https://wcng60ba718p.feishu.cn/wiki/R1N4waSckieoockdM2kcVHcVnvg?sheet=5e8ecf]
*   数据埋点汇总：[https://wcng60ba718p.feishu.cn/wiki/Pw1GwQgfEi7TsPk3dgrcysMDnR0?sheet=ceLy7M]

## 3. 产品概述

### 3.1. 产品定位与范围

#### 3.1.1. 产品定位

*   为教师提供一个便捷、全面的学生作业学情分析与干预工具，帮助教师实时掌握学生学习状况，识别问题并进行个性化指导。

#### 3.1.2. 核心功能范围 (P0)

*   **作业列表**：展示教师权限内的作业任务，支持筛选和搜索。
*   **作业报告**：按任务查看详细报告，包含：
    *   **学生报告 Tab**：班级整体统计、学生个体表现、建议鼓励/关注名单。
    *   **答题结果 Tab**：题目作答统计、共性错题分析。
    *   **学生提问 Tab**：学生提问汇总与查看。
*   **学生作业报告**：查看单个学生在特定任务中的详细答题结果和提问情况。
*   **题目详情**：查看单个题目的详细信息、解析和作答统计。
*   **策略应用**：系统根据预设规则推荐鼓励/关注学生和共性错题。

#### 3.1.3. 辅助功能范围 (P1/未来)

*   **提醒设置**：教师自定义鼓励、关注、共性错题的提醒阈值。
*   **测验报告**：支持测验类型任务的报告。
*   **大屏模式**：可能用于课堂集中讲解或展示。

### 3.2. 整体业务流程

```mermaid
graph TD
    A[教师布置任务【课程/作业/测验/资源】] --> B{学生接收任务};
    B --> C{学生完成任务'看课/答题/提问/看资源'};
    C --> D[系统记录学生行为数据看课/答题/提问/文件/视频];
    D --> E{生成作业报告数据'进度/正确率/用时/错题/提问等'};
    E --> F[教师查看作业报告'列表/详情'];
    F -- 查看学生报告 --> G[学生报告Tab'班级统计/个体列表/建议干预'];
    F -- 查看答题结果 --> H[答题结果Tab'题目统计/共性错题'];
    F -- 查看学生提问 --> I[学生提问Tab'提问汇总'];
    G -- 查看学生详情 --> J[学生个人报告'答题结果/提问'];
    H -- 查看题目详情 --> K[题目详情页'题目/解析/统计'];
    G -- 干预 --> L{教师干预'鼓励/关注/提醒'};
    H -- 干预 --> L;
    I -- 干预 --> L;

    subgraph 任务类型
        direction LR
        T1[课程]
        T2[作业]
        T3[测验（未来）]
        T4[资源]
    end

     subgraph P0报告维度
        direction LR
        R1[学生报告]
        R2[答题结果]
        R3[学生提问]
    end

    A --> T1 & T2 & T4;
    E --> R1 & R2 & R3;
```

*原始 PRD 图片解析: images/diagram.png*
```markdown
### 任务布置与接收
- **任务布置**：服务端需支持学科老师向学生布置课程、作业、测验、资源等任务，记录任务的相关信息，包括任务名称（如"3.2.2函数的最值"课程 ）、发布时间、截止时间、适用班级（如高一（3）班 ） 。
- **任务接收**：学生端接收任务，服务端记录学生收到任务的时间，以及任务类型（课程任务、作业任务、测验任务、资源任务 ）。对于课程任务，若学生提前自学课程可自动完成，服务端需记录该完成状态。 

### 学生行为数据记录
- **课程任务**：服务端记录学生查看课程（如xx课程1、xx课程2 ）的行为，包括看课开始时间、结束时间、课中答题情况（题目、答案、是否正确 ）、提问内容及时间。
- **作业任务**：记录学生查看作业任务卡、作业答题情况（答案、是否正确、答题时间 ）、提问内容及时间。
- **测验任务**：记录学生查看测验任务卡、测验答题情况（答案、是否正确、答题时间 ）。
- **资源任务**：记录学生查看资源任务卡、看视频（开始时间、结束时间 ）、看文件（开始时间、结束时间 ）、提问内容及时间。 

### 作业报告数据
- **学生报告Tab（按任务/素材）**
    - **按班级**：服务端提供班级整体的任务进度、正确率、平均用时数据。
    - **按个体**：提供每个学生的完成进度、正确率、答题难度、错题/答题数量、用时等数据，以及建议鼓励和建议关注的学生名单。
    - **操作功能**：支持教师进行鼓励、关注学生操作，并记录操作时间；支持发送push消息功能，记录消息发送状态。 
- **答题结果Tab**
    - **按班级**：提供班级共性错题（题目内容、答错人数 ）、作答分布（各题目作答人数、正确率分布 ）数据。
    - **按个体**：提供每个学生的错题列表（题目内容、学生答案、正确答案 ）。
    - **操作功能**：支持教师发送push消息、批改作业操作，并记录操作时间。 
- **学生提问Tab**
    - **按班级**：提供班级提问分布情况（按知识点分类的提问数量 ）。
    - **按个体**：提供每个学生的提问历史（提问内容、时间、回答情况 ）。
    - **操作功能**：支持教师发送push消息操作，并记录操作时间。 
- **资源学习情况**
    - **按班级**：提供班级资源学习的平均进度、平均用时数据。
    - **按个体**：提供每个学生的资源学习进度、用时数据。
    - **操作功能**：支持教师发送push消息操作，并记录操作时间。 
```

*原始 PRD 图片解析: images/diagram-1.png*
```markdown
### 作业核心页面交互图
1. **作业报告查看**
    - **课程信息**：需服务端提供课程名称（如"3.2.2函数的最值" ）、发布时间、截止时间、适用班级（如高一3班 ）、课程节数等信息。
    - **学生整体数据**：班级完成率、正确率、待关注题目数量；学生个体的分层（如S+、S、A、B、C ）、完成进度、正确率、答题难度、错题/答题数量、用时等数据。还需提供值得表扬和需要关注的学生名单。
    - **答题结果**：题目总数、错题数量、按作答排序的题目列表，每道题的题目内容、选项、作答状态（正确/错误/未作答 ）、正确率、答错人数、答对人数、平均答题时长等。 
    - **学生提问**：按课程知识点（如课程引言、函数的定义域与值域等 ）分类的提问数量，以及具体提问内容、提问时间、回答情况等。 
2. **页面跳转关联数据**
    - 从作业报告进入学生详情页面，服务端需提供该学生作业正确率、与班级或分层平均正确率对比数据，学习进度及与平均进度对比数据，同时提供针对该生的建议干预措施等文本内容。 
    - 从答题结果进入题目详情页面，服务端需提供题目详情（题型、难度、答题用时 ）、答案解析、作答统计（正确率、答对人数、答错人数、平均答题时长、各选项作答人数 ）等数据。 

### 账号关系
1. **学校管理端**
    - 需提供学校名称、学校ID等基础信息。
    - 支持创建、管理教师账号和学生账号，包括账号ID、账号类型（教师/学生 ）、所属班级（学生 ）等信息的增删改查功能。 
2. **教师端**
    - 教师账号关联的学校信息、教师ID、姓名、所授班级等信息。
    - 可获取所授班级学生的作业、答题、提问等相关数据，进行作业布置、批改，学生表扬、提醒，发送消息等操作，并记录操作日志。 
3. **学生端**
    - 学生账号关联的学校信息、学生ID、姓名、所属班级等信息。
    - 可接收作业任务，提交作业答案，进行提问，查看作业报告、答题结果、提问历史等数据。 

### 作业任务关系
1. **作业任务基本信息**
    - 作业任务的名称、任务内容（如课程任务、作业任务卡、测验任务卡、资源任务卡 ）、发布时间、截止时间、适用班级、学科等信息。 
2. **课程任务**
    - 课程名称、课程节数、提前自学课程可自动完成的规则。学生行为数据如看课记录（开始时间、结束时间、时长 ）、课中答题结果（题目、答案、得分 ）、提问内容及时间等。 
3. **作业任务卡**
    - 作业题目内容、题型、难度、答案解析。学生作业答题结果（答案、是否正确、答题时间 ）、提问内容及时间等数据。 
4. **测验任务卡**
    - 测验题目内容、题型、难度、答案解析。学生测验答题结果（答案、是否正确、答题时间 ）等数据。 
5. **资源任务卡**
    - 资源名称（如视频、文件 ）、资源类型、课时数。学生行为数据如看视频记录（开始时间、结束时间、时长 ）、看文件记录（打开时间、关闭时间 ）、提问内容及时间等。 
```

## 4. 用户场景与故事化解

### 4.1. 用户角色：学科老师/班主任 (具有 P0 布置权限)

#### 4.1.1. 场景：课后检查作业完成情况
*   **用户故事**：作为一名数学老师，我想快速了解我布置的《3.2.2函数的最值》作业，高一（3）班学生的整体完成进度和正确率，以及哪些学生因为表现不佳（如进度落后、正确率低）或表现优异（如进步明显）需要我特别关注。
*   **功能点**：作业列表、作业报告（学生报告 Tab）。
*   **验收标准**：
    1.  老师打开作业列表页面，能看到《3.2.2函数的最值》作业卡片。
    2.  卡片上直接显示该作业在高一（3）班的整体完成率和正确率。
    3.  点击该卡片，进入作业报告详情页，默认展示"学生报告"Tab。
    4.  页面顶部展示班级整体的平均进度（完成率）和平均正确率。
    5.  页面中部清晰展示系统推荐的"建议鼓励"和"建议关注"的学生名单。
    6.  页面下方展示高一（3）班所有学生的列表，包含姓名、进度、正确率、用时等关键指标，建议鼓励/关注的学生置顶显示。
    7.  列表中，与班级平均水平偏差较大的数据（如正确率远低于平均值）有高亮或特殊标记。
*   **流程图**：
    ```mermaid
    sequenceDiagram
        participant T as Teacher
        participant S as System
        T->>S: 打开作业列表页面
        S-->>T: 展示各作业卡片(含《3.2.2函数的最值》及其完成率/正确率)
        T->>S: 点击"3.2.2函数的最值"作业卡片
        S-->>T: 进入作业报告页，默认显示"学生报告"Tab
        S-->>T: 展示班级平均进度、平均正确率
        S-->>T: 展示"建议鼓励"、"建议关注"学生名单
        S-->>T: 展示学生列表(含进度、正确率等)，异常数据高亮，推荐学生置顶
    ```

#### 4.1.2. 场景：分析班级共性错题
*   **用户故事**：作为一名数学老师，我想知道高一（3）班在《3.2.2函数的最值》作业中，哪些题目错误率比较高（比如低于60%），以便我在后续的讲评课上重点讲解这些共性错题。
*   **功能点**：作业报告（答题结果 Tab）。
*   **验收标准**：
    1.  在《3.2.2函数的最值》作业报告页，老师切换到"答题结果" Tab。
    2.  页面顶部显示本次作业的总题数和识别出的"共性错题"数量。
    3.  提供"只看共性错题"的筛选开关。
    4.  题目列表默认按正确率从低到高排序（或其他有助于识别问题的排序）。
    5.  每道题目清晰展示其题干缩略、正确率、答错人数、作答总人数。
    6.  满足共性错题标准的题目有明显的"共性错题"标签。
    7.  老师可以点击任意题目查看该题的详细作答情况和解析。
*   **流程图**:
    ```mermaid
    sequenceDiagram
        participant T as Teacher
        participant S as System
        T->>S: 在作业报告页切换到"答题结果"Tab
        S-->>T: 显示题目列表及统计信息(总题数、共性错题数)
        S-->>T: 题目按正确率排序，满足条件的题目标记"共性错题"
        T->>S: (Optional) 点击"只看共性错题"开关
        S-->>T: (Optional) 列表仅显示共性错题
        T->>S: (Optional) 点击某共性错题
        S-->>T: (Optional) 显示该题目详情页(作答统计、解析)
    ```

#### 4.1.3. 场景：查看单个学生详细作答情况
*   **用户故事**：作为高一（3）班班主任，我注意到李子涵这次作业被系统标记为"建议关注"，我想详细了解他在《3.2.2函数的最值》作业中的具体表现，看看他哪些题目做错了，以及他是否在学习过程中提出了疑问。
*   **功能点**：学生报告 Tab -> 学生个人作业报告（答题结果 Tab, 学生提问 Tab）。
*   **验收标准**：
    1.  在学生报告 Tab 的"建议关注"列表或学生总列表中找到李子涵，点击"查看详情"。
    2.  进入标题为"李子涵 - 3.2.2函数的最值"的个人报告页面。
    3.  页面顶部显示李子涵本次作业的整体情况（如完成时间/进度、错题数）。
    4.  默认展示"答题结果" Tab，列出所有题目以及李子涵对每道题的作答状态（正确✅、错误❌、部分正确、未作答❓）。
    5.  提供"只看错题"的筛选开关，方便老师快速定位错题。
    6.  老师可以切换到"学生提问" Tab。
    7.  "学生提问" Tab 按课程结构展示李子涵在各环节提出的问题或评论。
*   **流程图**:
    ```mermaid
    sequenceDiagram
        participant T as Teacher
        participant S as System
        T->>S: 在"学生报告"Tab找到李子涵，点击"查看详情"
        S-->>T: 进入"李子涵-3.2.2函数的最值"报告页
        S-->>T: 显示李子涵答题概览(进度、错题数等)
        S-->>T: 默认显示"答题结果"Tab，展示其每题作答状态(✅/❌/❓)
        T->>S: (Optional) 点击"只看错题"开关
        S-->>T: (Optional) 列表仅显示李子涵的错题
        T->>S: 切换到"学生提问"Tab
        S-->>T: 显示李子涵的提问记录(按课程结构)
    ```

#### 4.1.4. 场景：鼓励表现进步的学生
*   **用户故事**：作为高一（3）班的数学老师，我看到系统在《3.2.2函数的最值》作业报告中提示周雨彤表现"有进步"，我想给她发送一条在线鼓励消息，肯定她的努力。
*   **功能点**：学生报告 Tab -> 建议鼓励学生 -> 鼓励学生抽屉页 -> Push 提醒。
*   **验收标准**：
    1.  在学生报告 Tab 的"建议鼓励"列表中能看到周雨彤。
    2.  点击周雨彤的姓名或头像，弹出"鼓励学生"详情抽屉。
    3.  抽屉页清晰展示周雨彤的具体进步数据（如学习分提升、连对突破等）和系统建议的干预措施。
    4.  提供"点赞鼓励"（可能仅为标记）、"线下沟通记录"、"Push提醒"等操作按钮。
    5.  点击"Push提醒"按钮，系统弹出预设的、包含周雨彤进步具体内容的鼓励消息文本。
    6.  老师可以编辑或直接发送该消息。
    7.  发送成功后，系统记录本次干预操作。
*   **流程图**:
    ```mermaid
    sequenceDiagram
        participant T as Teacher
        participant S as System
        T->>S: 查看"学生报告"Tab的"建议鼓励"列表
        S-->>T: 列表包含周雨彤
        T->>S: 点击周雨彤姓名/头像
        S-->>T: 弹出鼓励学生抽屉页(含进步详情、建议措施)
        T->>S: 点击"Push提醒"按钮
        S-->>T: 显示预设的、个性化的鼓励消息文本
        T->>S: (Optional) 编辑消息
        T->>S: 点击发送
        S->>周雨彤客户端: 发送Push消息
        S-->>T: 提示发送成功，记录干预操作
    ```

### 4.2. 用户角色：学科主任/年级主任/校长 (无布置权限)

#### 4.2.1. 场景：查看年级/学校整体学情
*   **用户故事**：作为高一年级主任，我想了解本年级所有班级近期数学作业的整体完成情况和正确率，以便掌握年级整体学情。
*   **功能点**：作业列表（数据范围权限）、作业报告（班级切换）。
*   **验收标准**：
    1.  年级主任登录后，在作业列表页面能看到本年级所有班级、所有学科的作业任务。
    2.  作业列表的筛选功能支持按班级、学科进行过滤。
    3.  点击某个作业任务卡片进入作业报告页。
    4.  作业报告页的顶部提供班级切换功能，可以查看该任务在不同班级的报告数据。
    5.  年级主任没有编辑、删除、复制作业的操作权限。
*   **流程图**:
    ```mermaid
    sequenceDiagram
        participant D as Director
        participant S as System
        D->>S: 打开作业列表页面
        S-->>T: 展示权限内(全年级)所有作业卡片
        D->>S: (Optional) 使用班级/学科筛选
        S-->>T: (Optional) 显示筛选后的作业列表
        D->>S: 点击某数学作业卡片
        S-->>T: 进入作业报告页(默认展示首个班级数据)
        S-->>T: 顶部显示班级切换控件
        D->>S: (Optional) 切换到其他班级
        S-->>T: (Optional) 刷新报告数据为所选班级的数据
        D->S: 尝试编辑/删除/复制 (操作按钮不显示或置灰)
    ```

*(注：需要根据原始需求补充更全面的用户场景，例如教师如何查看学生提问详情并回复等)*

## 5. 功能需求详述

### 5.1. 作业列表 (P0)

#### 5.1.1. 功能描述
为教师提供一个集中查看其负责或有权限查看的所有作业任务（包括课程、作业、资源、测验类型）的列表界面。支持按类型、班级、学科、布置日期进行筛选，并支持按任务名称关键词搜索。不同职务的教师拥有不同的数据查看范围和操作权限。

#### 5.1.2. 核心指标、筛选、搜索与操作
*   **数据范围与权限**：
    | 职务     | 数据报告查看权限                          | 操作权限 (编辑/删除/复制) |
    | -------- | ----------------------------------------- | ------------------------- |
    | 学科老师 | 本班本学科 任务数据                       | 仅本人布置的任务          |
    | 班主任   | 本班全部学科 任务数据                     | 仅本人布置的任务          |
    | 学科主任 | 本年级本学科 任务数据                     | 无                        |
    | 年级主任 | 本年级全部班级全部学科 任务数据           | 无                        |
    | 校长     | 本校全部班级全部学科 任务数据             | 无                        |
*   **卡片展示指标**：
    *   **课程/作业/测验任务**：任务名称、发布时间、截止时间、班级、完成率、正确率、待关注题目数量。
    *   **资源任务**：任务名称、发布时间、截止时间、班级、平均进度、课时数。
    *   **特殊标识**：如"到期"等状态标识。
*   **筛选**：
    *   **任务类型**：全部、课程、作业、资源、测验 (P0 阶段可能仅支持部分类型)。
    *   **班级**：根据用户权限展示可选班级列表。
    *   **学科**：根据用户权限和学校开设学科展示可选列表。
    *   **布置日期**：支持选择预设范围（如最近7天、最近两周、1个月内）或自定义起止日期。
*   **搜索**：
    *   输入框内输入关键词，实时模糊匹配任务名称。
    *   支持清空搜索关键词。
*   **卡片操作**：
    *   对于有权限的教师本人布置的任务，卡片上显示"..."操作菜单。
    *   点击菜单，展示"编辑任务"、"删除任务"、"复制任务"选项。

#### 5.1.3. 界面原型/说明
*原始 PRD 图片解析: images/布置任务-查看更多.png, images/布置任务-查看更多-1.png*
```markdown
### 顶部筛选与搜索栏
- **筛选功能**：提供学科、年级、时间范围筛选。学科当前值为"数学"；年级为"全部年级"；时间范围是"最近一周"。
- **搜索框**：支持用户输入关键词搜索相关教学内容。

### 教学内容卡片数据
卡片分为课程、作业、资源三类，以高一3班为例，具体数据如下：
- **课程**
    - 课程名称："3.2.2函数的最值"
    - 发布时间："3月16日8:30"
    - 到期时间："3月20日16:00"
    - 关联班级："高一3班"
    - 完成率："90%"
    - 正确率："73%"
    - 待关注题目数量："4题"
- **作业**
    - 作业名称："3月14日数学作业"
    - 发布时间："3月16日8:30"
    - 到期时间："3月20日16:00"
    - 关联班级："高一3班"
    - 完成率："90%"
    - 正确率："73%"
    - 待关注题目数量："4题"
- **资源**
    - 资源名称："3月18日数学资源"
    - 发布时间："3月16日8:30"
    - 到期时间："3月20日16:00"
    - 关联班级："高一3班"
    - 平均进度："22%"
    - 课时数："2节"
    - 部分资源有"到期"标识 

### 数据组合关系
- 同一课程下，可能关联多个作业、资源，通过课程名称、发布时间、到期时间以及关联班级进行组合关联。如"3.2.2函数的最值"课程，关联了相关作业和资源，且发布、到期时间相同，面向班级均为高一3班 。
- 每个课程、作业、资源卡片展示各自特定的统计数据（完成率、正确率等 ），便于用户快速了解对应教学内容的进展情况。
```
*原始 PRD 图片解析: images/image-11.png*
```markdown
### 导航栏信息
- **分类标签**：服务端需提供"全部""课程""作业""资源""测验"等分类标签数据，支持当前选中状态标识（如"全部"下方的视觉区分 ）。
- **搜索框**：服务端需支持搜索功能，接收用户输入关键词，进行相关教学内容的检索。
- **筛选按钮**：学科筛选按钮当前显示"数学" ，服务端需提供学科列表数据（如语文、数学、英语等 ）以供切换；年级筛选按钮显示"全部年级" ，需提供年级相关数据支持筛选；时间筛选按钮显示"最近一周" ，服务端需提供时间范围选项（如最近7天、最近两周、1个月内等 ）及日历视图数据（可选择具体日期范围 ）。
```
*原始 PRD 图片解析: images/布置任务-选择日期-时间段.png*
```markdown
### 分类与筛选信息
- **分类标签**：有"全部""课程""作业""资源""测验"，当前"全部"被选中，服务端需支持分类切换及对应内容展示。
- **时间筛选**：时间筛选器展开，显示日历视图，可选择日期范围（当前为2021年10月20日 - 2021年11月5日 ），并提供"最近7天""最近两周""1个月内"快捷选项 。服务端需支持日期选择操作，根据所选日期筛选并展示对应课程、作业、资源等数据。
- **年级筛选**：当前筛选为"二年级" ，服务端需提供年级筛选选项及对应数据筛选功能。

### 课程、作业、资源数据
- **3.2.2函数的最值（课程）**：3月16日8:30发布，3月20日16:00到期，适用于高一 - 3班 ，完成率90%，正确率73%，4题待关注。
- **3月18日数学资源**：3月16日8:30发布，3月20日16:00到期，针对高一 - 3班 ，平均进度22%，共2节课时 。
- **3月14日数学作业**：3月16日8:30发布，3月20日16:00到期，面向高一 - 3班 （图中未完整显示相关统计数据 ）。

### 操作功能
筛选器底部有"取消"和"确定"按钮，服务端需支持点击操作，"确定"用于确认所选日期范围并应用筛选，"取消"用于关闭筛选器不应用筛选。
```
*原始 PRD 图片解析: images/image-10.png, images/image-24.png*
```markdown
### 导航栏信息
- **分类标签**：服务端需提供"全部""课程""作业""资源""测验"等分类标签数据，支持当前选中状态标识（如"全部"下方的蓝色下划线 ）。
- **搜索框**：支持用户输入关键词（如"高三" ）进行搜索，服务端需根据输入内容匹配并返回相关结果，如"高三函数""高三英语""高三数学""高三语文""高三几何""高三语法"等。需支持搜索框的清空操作（如点击"×" ）及搜索触发操作（如点击放大镜图标或回车 ）。
- **筛选按钮**：学科筛选按钮当前显示"数学" ，服务端需提供学科列表数据（如语文、数学、英语等 ）以供切换；年级筛选按钮当前显示"全部年级" ，需提供年级相关数据支持筛选；时间筛选按钮当前显示"最近一周" ，服务端需提供时间范围选项（如最近7天、最近两周、1个月内等 ）及日历视图数据（可选择具体日期范围 ）。

### 内容展示区信息 (image-10.png)
- **课程卡片**：对于课程"3.2.2函数的最值" ，服务端需提供课程名称、发布时间（3月16日8:30 ）、到期时间（3月20日16:00 ）、适用班级（高一3班 ）、完成率（90% ）、正确率（73% ）、待关注题目数量（4题 ）等数据。
- **资源卡片**：对于资源"3月18日数学资源" ，服务端需提供资源名称、发布时间（3月16日8:30 ）、到期时间（3月20日16:00 ）、适用班级（高一3班 ）、平均进度（22% ）、课时数（2节 ）等数据。
```

#### 5.1.4. 数据处理规则与计算口径
*   **完成率**：(已提交或已完成任务的学生数 / 任务总学生数) * 100%。具体定义需参考 [口径说明](https://wcng60ba718p.feishu.cn/wiki/DXKawJOESiypQ3kt3vbc64DgnTc?sheet=e3d974)。
*   **正确率 (班级整体)**：班级所有学生在任务中首次作答的题目总正确数 / 班级所有学生在任务中首次作答的题目总数。具体计算方式（如按小空计还是按整题计）需参考 [口径说明](https://wcng60ba718p.feishu.cn/wiki/DXKawJOESiypQ3kt3vbc64DgnTc?sheet=e3d974)。
*   **平均进度 (资源任务)**：(所有学生已完成课时数之和 / (总学生数 * 总课时数)) * 100%。需确认具体计算方式。
*   **待关注题目数**：满足"共性错题"标准的题目数量（见 5.5.3）。
*   **数据权限控制**：后端接口需严格根据请求教师的职务和ID，返回其权限范围内的数据。

### 5.2. 作业报告 (P0)

#### 5.2.1. 功能描述
提供单个作业任务的详细数据报告，包含"学生报告"、"答题结果"、"学生提问"三个核心维度（Tab页），帮助教师全面、深入地了解本次任务的学情。

#### 5.2.2. 页面结构与通用元素
*   **入口**：从作业列表点击任务卡片进入。也可能从"上课"模块的学生学习卡片进入特定学生的报告页。
*   **页面标题**：居中显示任务名称。
*   **返回按钮**：左上角提供返回上一页的功能。
*   **顶部信息栏**：
    *   任务发布时间、任务要求完成时间。
    *   当前任务的班级（若任务涉及多班级，或用户有多班级权限，应支持切换）。
    *   当前任务的素材范围（若任务包含多个素材，应支持切换查看不同范围的数据）。
*   **Tab 导航栏**：清晰展示"学生报告"、"答题结果"、"学生提问"三个 Tab，并标示当前选中的 Tab。默认进入时展示"学生报告" Tab。
*原始 PRD 图片解析: images/image-12.png*
```markdown
### 课程基本信息
- **课程名称**：3.2.2函数的最值
- **发布时间**：2024-01-15 14:30
- **截止时间**：2024-01-18 22:00
- **班级**：高一（3）班，服务端需支持班级信息的展示与切换功能
- **课程范围**：全部（2节课），服务端需支持课程范围信息的展示与切换功能

### 统计信息
- **需关注学生数量**：5人
- **待关注题目数量**：4题 

### 其他功能
服务端需提供"预警设置"功能的相关配置及操作记录支持。
```
*原始 PRD 图片解析: images/image-13.png*
```markdown
### 每节课学习情况 (素材范围示例)
服务端需提供以下数据：
| 课程名称                   | 完成进度 | 平均用时 | 待关注题目数 | 正确率 | 需关注人数 | 操作     |
| -------------------------- | -------- | -------- | ------------ | ------ | ---------- | -------- |
| 3.2.2函数的最值 - AI课     | 90%      | 35分钟   | 2            | 88.5%  | 5          | 预览课程 |
| 3.2.2函数的最值 - 巩固练习 | 85%      | 10分钟   | 4            | 85.2%  | 5          | 预览课程 |

### 各班学习情况 (班级切换示例)
服务端需提供以下数据：
| 班级名称    | 完成进度 | 平均用时 | 待关注题目数 | 正确率 | 需关注人数 | 操作     |
| ----------- | -------- | -------- | ------------ | ------ | ---------- | -------- |
| 高一（3）班 | 90%      | 35分钟   | 12           | 88.5%  | 10         | 当前班级 |
| 高一（5）班 | 85%      | 42分钟   | 15           | 85.2%  | 15         | 切换班级 |

### 操作功能支持
- 对于"预览课程"操作，服务端需支持课程内容预览相关功能及数据调用。
- 对于"当前班级""切换班级"操作，服务端需支持班级切换逻辑及相关数据更新展示。
```

#### 5.2.3. 学生报告 Tab
*   **功能目标**：从学生维度展示班级整体学情概览和个体表现，突出显示需要教师干预（鼓励或关注）的学生群体。
*   **核心内容模块**：
    1.  **建议鼓励学生**：
        *   展示系统根据"鼓励策略"（见 5.5.1）筛选出的学生名单（头像、姓名）。
        *   点击学生姓名或头像，弹出抽屉页，展示该生具体的优秀表现数据、系统分析和建议干预措施。
        *   抽屉页提供干预操作：点赞鼓励（标记）、线下沟通记录、Push 提醒（可使用或编辑预设文案）。
        *   提供"一键点赞"所有建议鼓励学生的功能（可选）。
    2.  **建议关注学生**：
        *   展示系统根据"关注策略"（见 5.5.2）筛选出的学生名单（头像、姓名）。
        *   点击学生姓名或头像，弹出抽屉页，展示该生具体的异常表现数据、系统分析和建议干预措施。
        *   抽屉页提供干预操作：标记关注、线下沟通记录、Push 提醒（可使用或编辑预设文案）。
        *   提供"一键提醒"所有建议关注学生的功能（可选）。
    3.  **班级整体统计**：
        *   **班级进度**：显示当前班级、当前素材范围内的平均完成率（同任务卡片指标）。
        *   **班级正确率**：显示当前班级、当前素材范围内的平均正确率（同任务卡片指标）。
    4.  **任务学生列表**：
        *   **排序**：默认按学生 UID/姓名 排序，带有"建议鼓励"/"建议关注"标签的学生置顶显示。支持按其他列（如进度、正确率、用时）排序。
        *   **列表列**：学生头像、姓名、分层（若有）、完成进度（%）、正确率（%）、答题难度（若有）、错题数/答题总数、用时、特殊标识（"建议鼓励"/"建议关注"图标或文字）、操作（"查看详情"按钮）。
        *   **异常数据飘色/高亮**：对于与班级均值偏离较大的数据（如进度/正确率低于班级平均值 20% 或排名后 10% 的学生），进行视觉上的突出显示。
        *   **操作**：点击"查看详情"进入该学生的个人作业报告页面（见 5.3）。
        *   **其他操作**：可能包括列表导出、编辑表格列显示等。
*   **界面原型/说明**:
    *原始 PRD 图片解析: images/image-1.png, images/image-7.png, images/作业详情 (1).png*
    ```markdown
    ### 课程基本信息 (image-1.png)
    - **课程名称**：3.2.2函数的最值
    - **发布时间**：2024-01-15 14:30
    - **截止时间**：2024-01-18 22:00
    - **班级**：高一（3）班
    - **范围**：全部（2节课）
    - **班级进度**：85%
    - **待关注题目数量**：4题

    ### 班级整体统计信息 (image-1.png)
    - **值得表扬学生名单**：周雨彤、刘明宇等6人
    - **需要关注学生名单**：李子涵
    - **班级平均正确率**：78.5%
    - **班级平均用时**：45分钟

    ### 学生个体数据 (image-1.png, image-7.png, 作业详情 (1).png)
    服务端需提供以下学生个体维度的数据 (数据可能来自不同图片，需整合)：
    | 学生姓名 | 分层 | 特殊标识 | 学习分 | 完成进度 | 正确率 | 答题难度 | 错题/答题 | 用时   | 操作     |
    | -------- | ---- | -------- | ------ | -------- | ------ | -------- | --------- | ------ | -------- |
    | 周雨彤   | S+   | 有进步   | (未见) | 95%      | 95%    | 3.1      | 1/20      | 35分钟 | 查看详情 |
    | 刘明宇   | S    | 有进步   | (未见) | 80%      | 94%    | 2.9      | 1/16      | 42分钟 | 查看详情 |
    | 张思远   | A    | 有进步   | (未见) | 88%      | 83%    | 2.8      | 3/18      | 38分钟 | 查看详情 |
    | 李子涵   | B    | 需关注   | (未见) | 60%      | 60%    | 2.5      | 5/12      | 50分钟 | 查看详情 |
    | 王梓萱   | C    | 无       | (未见) | 85%      | 82%    | 2.3      | 3/17      | 40分钟 | 查看详情 |
    | 王维     | 有进步   | 有进步   | 20     | 45%      | 78%    | (未见)   | (未见)    | (未见) | 查看详情 |
    | 赵田     | 有进步   | 有进步   | 15     | 45%      | 78%    | (未见)   | (未见)    | (未见) | 查看详情 |
    | 王欧斐   | (未见) | 需关注   | 25     | 92%      | 97%    | (未见)   | (未见)    | (未见) | 查看详情 |
    ... (共49条)

    ### 其他功能 (image-1.png, image-7.png, 作业详情 (1).png)
    - **搜索功能**：服务端需支持通过学生姓名搜索学生信息。
    - **导出功能**：服务端应提供数据导出功能，允许将当前页面展示的学生相关数据导出。
    - **分页功能**：当前显示共49条数据，服务端需支持分页展示，当前为第1页，可切换至其他页。
    - **一键点赞/提醒**：对建议鼓励/关注学生的操作。
    - **编辑表格**：(可选) 自定义列表显示列。
    ```
    *原始 PRD 图片解析: images/image-14.png, images/image-15.png (鼓励策略说明)*
    ```markdown
    ### 连对王者 (image-14.png)
    - **展示指标**：连对题数 (新纪录 vs 班级历史最高)
    ### 自我突破 (image-14.png)
    - **展示指标**：学习分增幅 (本次 vs 上次任务平均单课学习分)
    ### 连对突破 (image-14.png)
    - **展示指标**：连对题数 (新纪录 vs 个人历史最高)
    ### 本层领跑 (image-14.png)
    - **展示指标**：学习分差值 (个人 vs 同层平均，需高于10%)
    ### 超前学霸 (image-14.png)
    - **展示指标**：任务完成时间 (早于发布时间完成)
    ### 探索达人 (image-14.png)
    - **展示指标**：提问次数 (新纪录 vs 个人历史记录)

    ### 建议干预措施 (image-15.png)
    服务端需根据不同推荐类型，提供包含学生姓名、具体表现数据的文本内容，并组合通用干预建议。
    ### 鼓励类Push默认文案 (image-15.png)
    服务端需根据不同推荐类型，提供包含学生姓名、具体表现数据的Push文案内容，并组合通用文案框架。
    ### 初始化的阈值 (image-15.png)
    服务端需根据不同推荐类型，按照阈值规则判断是否展示（如需历史数据对比的，需要至少2次任务/单课数据）。
    ```
    *原始 PRD 图片解析: images/image-17.png, images/image-18.png (关注策略说明)*
    ```markdown
    ### 进度未开始 (image-17.png)
    - **展示指标**：进度对比 (个人未开始 vs 班级平均达50%)
    ### 逾期未完成 (image-17.png)
    - **展示指标**：逾期天数
    ### 逾期完成 (image-17.png)
    - **展示指标**：逾期完成天数
    ### 偏离历史 (image-17.png)
    - **展示指标**：学习分对比 (本次 vs 上次任务下降20%)
    ### 偏离同层 (image-17.png)
    - **展示指标**：学习分对比 (个人 vs 同层平均低20%)
    ### 内容偏离 (image-17.png)
    - **展示指标**：学习分对比 (某课程显著偏离本次任务各课平均分20%以上)

    ### 建议干预措施 (image-18.png)
    服务端需根据不同异常类型，提供包含学生姓名、具体表现数据的文本内容，并组合通用干预建议。
    ### 关注类Push默认文案 (image-18.png)
    服务端需根据不同异常类型，提供包含学生姓名、具体表现数据的Push文案内容，并组合通用文案框架及跳转链接。
    ### 初始化的阈值 (image-18.png)
    服务端需根据不同异常类型，按照阈值规则判断是否展示（如需历史数据对比的，需要至少2次任务数据）。
    ```
    *原始 PRD 图片解析: images/image-16.png, images/image-8.png (鼓励学生抽屉页)*
    ```markdown
    ### 学生基本信息
    - **姓名**：王梓萱
    - **班级**：高一（3）班
    ### 学习情况数据
    - **作业正确率**：87%，C层平均75%
    - **学习进度**：85%，C层平均65%
    ### 建议干预措施文本
    系统生成的分析文本及建议（班级表扬、线上点赞、家校沟通）。
    ### 操作功能
    - **点赞鼓励**：记录操作。
    - **线下沟通**：记录操作及备注。
    - **Push提醒**：发送预设消息（如"子涵同学，看到你今天的任务完成得非常棒..." - 注意姓名可能需要替换）。
    ```
    *原始 PRD 图片解析: images/image-19.png, images/image-9.png (关注学生抽屉页)*
    ```markdown
    ### 学生基本信息
    - **姓名**：李子涵
    - **班级**：高一（3）班
    ### 学习情况数据
    - **作业正确率**：50%，班级平均78.5%
    - **学习进度**：60%，班级平均95%
    ### 建议干预措施文本
    系统生成的分析文本及建议（一对一辅导、家校沟通、个性化计划、定期跟进）。
    ### 操作功能
    - **发布关注/关注**：记录操作。
    - **线下沟通**：记录操作及备注。
    - **Push提醒**：发送预设消息（如"子涵同学，你今天的任务完成情况不太好..."）。
    ```
    *原始 PRD 图片解析: images/image-20.png (学生列表展示)*
    ```markdown
    ### 班级整体信息
    - **学生数据总数**：全班共49条数据。
    ### 学生个体数据 (部分)
    | 学生姓名 | 特殊标识 | 学习分 | 完成进度 | 正确率 | 操作     |
    | -------- | -------- | ------ | -------- | ------ | -------- |
    | 王维     | 有进步   | 20     | 45%      | 78%    | 查看详情 |
    | 赵田     | 有进步   | 15     | 45%      | 78%    | 查看详情 |
    | 刘天成   | 无       | 10     | 95%      | 80%    | 查看详情 |
    | 刘晓伟   | 无       | 5      | 65%      | 90%    | 查看详情 |
    | 王欧斐   | 需关注   | 25     | 92%      | 97%    | 查看详情 |
    ### 操作功能支持
    - **搜索功能**
    - **导出功能**
    - **编辑表格功能** (可选)
    - **查看详情功能**
    ```
*   **流程图**: 参考 4.1.1 和 4.1.4。

#### 5.2.4. 答题结果 Tab
*   **功能目标**：从题目维度展示班级整体的作答情况，帮助教师分析题目难度、区分度以及学生的知识点掌握情况，重点识别共性错题。
*   **适用范围**：适用于包含答题环节的任务类型（如课程、作业、测验）。资源任务无此 Tab。
*   **核心内容模块**：
    1.  **整体统计与筛选**：
        *   显示"共 XX 道题，XX 道共性错题"。共性错题数量根据策略（见 5.5.3）计算得出。
        *   **搜索框**：输入关键词，模糊匹配题干、选项、解析内容，实时筛选下方题目列表。支持清空。
        *   **筛选器**：提供"只看共性错题"开关。可能还包括按题型、难度、知识点等筛选（P0 阶段可能仅支持"共性错题"）。
    2.  **题目列表**：
        *   **排序**：默认按题目在任务中的顺序展示。支持按正确率（升序/降序）、作答人数（降序/升序）等方式排序。
        *   **题目信息卡片**：
            *   题号。
            *   题干缩略文本及主要选项（若空间允许）。
            *   **核心指标**：
                *   **正确率 (%)**：该题在当前班级&素材范围下的首答正确率。
                *   **XX 人答错**：该题在当前班级&素材范围下首答有错误的学生人数。
                *   **XX 人作答**：该题在当前班级&素材范围下有作答记录（包括选"暂不作答"）的学生人数。
            *   **共性错题标识**：若该题满足共性错题标准，则显示明显标识。
            *   **操作**：提供"查看"按钮/链接，点击进入该题目的详情页（见 5.4）。可能还有"加入讲评"（未来）等操作。
    3.  **题目面板 (侧边栏)**：
        *   默认展开，可收起。
        *   展示当前任务下的全部题号。
        *   用不同颜色块标识每道题的正确率区间（如：<60% 红色，60%-79% 橙色，80%-100% 绿色）。
        *   点击题号，下方题目列表滚动定位到对应题目。
*   **数据处理规则与计算口径**：
    *   **正确率 (题目)**：(答对该题的学生数 / 作答了该题的学生数) * 100%。需严格按照 [口径说明](https://wcng60ba718p.feishu.cn/wiki/DXKawJOESiypQ3kt3vbc64DgnTc?sheet=e3d974) 中关于"首答"、"小空"、"题目"的定义计算。
    *   **答错人数**：首次作答该题时，至少答错一个空的学生数量。
    *   **作答人数**：在任务中（已提交状态下）有该题作答记录的学生数量。学生看到了题、选了"暂不作答"也记为已作答、答错。未提交的作业中跳过的题目不算作答。
    *   **共性错题定义**：P0 标准为"答题人数 > 10人 且 正确率 < 60%"。
*   **界面原型/说明**:
    *原始 PRD 图片解析: images/image-2.png, images/作业详情 (2).png*
    ```markdown
    ### 课程基本信息 (image-2.png)
    - **课程名称**：3.2.2函数的最值
    - **发布时间**：2024-01-15 14:30
    - **截止时间**：2024-01-18 22:00
    - **班级**：高一（3）班
    - **课程范围**：全部（2节课）
    - **需关注学生数量**：5人
    - **待关注题目数量**：4题

    ### 答题结果信息 (image-2.png, 作业详情 (2).png)
    - **题目总数**：28道
    - **共性错题数量**：3道
    - **排序方式**：已按作答人数排序
    - **搜索功能**：支持搜索题目关键词
    - **筛选功能**：具备"只看共性错题"筛选切换，及其他筛选功能

    ### 题目详情及作答数据 (image-2.png, 作业详情 (2).png - 以题目1为例)
    - **题目内容**：已知集合\(A\)中只有两个元素\(1\)，\(2a\)，则实数\(a\)不能取... (或 若函数\(y = f(x)\)在\(R\)上可导...)
    - **正确率**：50%
    - **错误人数**：2人
    - **作答人数**：4人
    - **操作**：有"查看"按钮，"加入"按钮(作业详情 (2).png)。

    ### 分页信息 (image-2.png)
    - **数据总数**：共28条数据
    - **分页导航**：支持分页。
    ```
    *原始 PRD 图片解析: images/image-22.png, images/image-23.png (题目信息展示细节)*
    ```markdown
    ### 题目详情数据 (image-22.png - 题目1)
    - **题目内容**：已知集合\(A\)中只有两个元素\(1\)，\(2a\)...
    - **作答统计**：正确率50%，2人错误，4人作答
    - **操作**：查看按钮
    ### 题目详情数据 (image-22.png - 题目2)
    - **题目内容**：下列可以构成集合的是...
    - **作答统计**：正确率50%，2人错误，4人作答
    - **操作**：查看按钮
    ### 作答统计数据 (image-23.png)
    - **正确率**：56%
    - **错误人数**：15人
    - **作答人数**：35人
    - **标识**："共性错题"标识。
    ```
    *原始 PRD 图片解析: images/image-21.png (题目面板)*
    ```markdown
    ### 题目正确率数据
    服务端需提供每道题目的正确率数据，并根据区间进行颜色标识：
    | 题目序号 | 正确率 | 颜色标识      |
    | -------- | ------ | ------------- |
    | 1        | 98%    | 绿色(80-100%) |
    | 5        | 0%     | 红色(<60%)    |
    | ...      | ...    | ...           |
    ### 可视化标识
    - 正确率<60%，标识为红色。
    - 正确率在60%-79%，标识为橙色。
    - 正确率在80%-100%，标识为绿色。
    ### 题目面板功能支持
    支持点击题号定位到列表中的题目。
    ```
*   **流程图**: 参考 4.1.2。

#### 5.2.5. 学生提问 Tab
*   **功能目标**：汇集展示当前任务下（特定班级、特定素材范围）所有学生的提问/评论内容，帮助教师了解学生在学习过程中的疑问点和讨论情况。
*   **适用范围**：适用于包含学生提问或评论功能的任务类型（如 AI 课、带讨论区的资源等）。具体哪些任务类型支持需确认。
*   **核心内容模块**：
    1.  **内容结构列表 (左侧)**：
        *   按照任务内容的结构（如 AI 课的幻灯片/知识点顺序）展示。
        *   每个内容节点旁显示该节点下本班学生的总提问/评论数量。
        *   点击某个内容节点，右侧加载该节点的具体提问/评论列表。
    2.  **提问/评论列表 (右侧)**：
        *   展示所选内容节点下的所有提问/评论。
        *   每条内容包括：提问/评论者头像、姓名、内容、时间。
        *   若有回复（来自教师或其他学生），则嵌套展示回复内容。
        *   支持教师直接在列表中进行回复、点赞等操作（具体交互待定）。
*   **界面原型/说明**:
    *原始 PRD 图片解析: images/image-3.png, images/作业详情 (4).png*
    ```markdown
    ### 课程基本信息 (image-3.png)
    - ... (同上)

    ### 学生提问数据 (image-3.png - 按课程结构分类统计)
    服务端需按课程结构提供各部分的提问数据：
    | 课程部分                                            | 提问数量            |
    | --------------------------------------------------- | ------------------- |
    | 课程引言                                            | 2个                 |
    | 知识点1：函数的定义域与值域 - 知识点讲解            | 3个                 |
    | ...                                                 | ...                 |
    | 知识点3：最值应用 - 实际问题应用                    | 6个，标记"建议关注" |
    | ...                                                 | ...                 |
    | 巩固练习                                            | 8个                 |
    ```
    *原始 PRD 图片解析: images/image-34.png, images/image-33.png, images/作业详情 (4).png (提问展示布局)*
    ```markdown
    ### 课程信息 (image-34.png)
    - **课程标题**：1.2.1复数的概念
    - **章节内容**：(左侧列表)
        - 引入部分 (4条讨论)
        - 复数的概念引入背景 (6条讨论)
        ...

    ### 课堂讨论数据 (image-33.png, 作业详情 (4).png - 右侧列表)
    - **讨论总数**：全部讨论共6条。
    - **具体讨论**：
        - 万洁于3月12日09:52提问：这节内容没有听太懂...
        - 赵田于3月12日10:02发言：a + bi构成...
        - 张老师于3月12日10:05回复赵田：认可a + bi...
        ...
    - **操作功能**：支持在讨论区输入内容、@用户、回复等操作。
    ```

### 5.3. xx学生的作业报告 (P0)

#### 5.3.1. 功能描述
提供单个学生在特定作业任务中的详细学情报告，聚焦该生的答题结果和提问情况，方便教师进行个性化分析和辅导。

#### 5.3.2. 页面结构与通用元素
*   **入口**：主要从班级"学生报告 Tab"的学生列表点击"查看详情"进入。也可能从其他入口（如"上课"模块）进入。
*   **页面标题**：居中显示"{学生姓名} - {任务名称}"。
*   **返回按钮**：左上角提供返回上一页的功能。
*   **顶部信息栏 (学生个人视角)**：
    *   任务开始时间、要求完成时间。
    *   **若进度 100%**：显示该生完成时间。
    *   **若进度 < 100%**：显示该生当前进度（%）。
    *   当前任务的素材范围：默认展示当前所选素材，若包含多个素材，点击可弹出抽屉页切换查看不同素材下的数据。
*   **Tab 导航栏**：包含"答题结果"、"学生提问"两个 Tab。默认展示"答题结果" Tab。
*原始 PRD 图片解析: images/作业详情 (3).png (整体布局)*
```markdown
### 课程及学生信息
- **课程标题**：3.2.2函数的最值
- **关联学生**：王维
- **课程范围**：全部2节课
- **完成时间**：2024.01.15 23:12:00 (已完成)
- **发布时间**：2024.01.15 18:22:00
- **截止时间**：2024.01.15 18:22:00
```
*原始 PRD 图片解析: images/image-25.png (未完成状态顶部)*
```markdown
### 课程基本信息
- **课程名称**：3.2.2函数的最值
- **关联学生**：周雨彤
- **发布时间**：2024-01-14 18:30
- **截止时间**：2024-01-15 14:30
- **课程范围**：全部（2节课）
- **完成进度**：80%
```
*原始 PRD 图片解析: images/image-26.png (已完成状态顶部)*
```markdown
### 课程基本信息
- **课程名称**：3.2.2函数的最值
- **关联学生**：周雨彤
- **发布时间**：2024-01-14 18:30
- **截止时间**：2024-01-15 14:30
- **课程范围**：全部（2节课）
- **完成时间**：2024-01-15 14:30
```
*原始 PRD 图片解析: images/image-27.png (切换素材抽屉)*
```markdown
### 每节课学习情况
服务端需提供以下数据：
| 课程名称                   | 完成进度 | 平均用时 | 待关注题目数 | 正确率 | 操作     |
| -------------------------- | -------- | -------- | ------------ | ------ | -------- |
| 3.2.2函数的最值 - AI课     | 90%      | 35分钟   | 2            | 88.5%  | 预览课程 |
| 3.2.2函数的最值 - 巩固练习 | 85%      | 10分钟   | 4            | 85.2%  | 预览课程 |
### 操作功能支持
- 对于"预览课程"操作，服务端需支持课程内容预览相关功能及数据调用。
```

#### 5.3.3. 答题结果 Tab (学生个人视角)
*   **功能目标**：清晰展示该学生在任务中每一道题目的作答情况（对、错、未答）。
*   **核心内容模块**：
    1.  **整体统计与筛选**：
        *   显示"共 XX 道题，XX 道错题"。错题数量为该生在该任务下的错题总数。
        *   **搜索框**：同班级报告，搜索题目关键词。
        *   **筛选器**：提供"只看错题"开关。可能还包括按题型、知识点等筛选。
    2.  **题目列表**：
        *   **排序**：默认按题目在任务中的顺序展示。
        *   **题目信息卡片**：
            *   题号。
            *   题干缩略文本。
            *   **该生作答状态**：使用明确的图标或文字标识：
                *   正确：✅
                *   错误：❌
                *   部分正确：（半对符号）
                *   尚未作答："! 尚未作答，暂无结果"或❓
            *   **操作**：提供"查看"按钮/链接，点击进入该题目的详情页（见 5.4，此时详情页应包含该生的作答痕迹）。
    3.  **题目面板 (侧边栏)**：
        *   同班级报告的题目面板结构。
        *   颜色标识可能调整为反映该学生的作答对错情况（如：绿色✅，红色❌，灰色❓）。
*   **界面原型/说明**:
    *原始 PRD 图片解析: images/image-4.png (学生个人答题结果 Tab 示例)*
    ```markdown
    ### 课程基本信息
    - **学生姓名**：周雨彤
    - ... (同上)
    ### 答题结果信息
    - **题目总数**：28道
    - **错题数量**：4道
    - **排序方式**：已按作答排序
    - **搜索功能**：支持搜索题目关键词
    - **筛选功能**：具备"只看错题"筛选切换 ，及其他筛选功能
    ### 题目详情及作答数据
    - **题目1**
        - **题目内容**：已知集合\(A\)中只有两个元素\(1\)，\(2a\)...
        - **作答状态**：正确 (✅)
        - **操作**：查看
    - **题目2**
        - **题目内容**：下列可以构成集合的是...
        - **作答状态**：尚未作答，暂无结果 (❓)
        - **操作**：查看
    ### 题目正确率可视化 (题目面板)
    右侧需展示题目面板，可能用颜色标识该生作答对错。
    ### 分页信息
    支持分页。
    ```
    *原始 PRD 图片解析: images/image-28.png (个人错题统计与筛选)*
    ```markdown
    ### 题目统计信息
    - **题目总数**：共28道题
    - **错题数量**：4道错题
    - **排序方式**：已按作答排序
    ### 搜索功能
    支持搜索题目关键词。
    ### 筛选功能
    - **只看错题**：支持开关切换。
    - **筛选**：支持其他筛选条件。
    ```
    *原始 PRD 图片解析: images/作业详情 (3).png (另一原型图)*
    ```markdown
    ### 答题结果数据
    - **作业进度**：80% (显示在顶部)
    - **题目总数**：共28道题
    - **错题数量**：3道
    - **排序方式**：按照已作答排序
    ### 题目详情数据
    - **题目内容**: 若函数...
    - **题目属性**: 第2题标注有"2020年·江西省·期末，单选，简单"
    - **答题状态及结果**：
        - 第1题：尚无作答 (❓)
        - 第2题：正确 (✅)
        - 第3题：尚无作答 (❓)
    ### 操作功能支持
    - 搜索、筛选(只看错题)、查看、题目面板、点赞、去处理。
    ```
*   **流程图**: 参考 4.1.3。

#### 5.3.4. 学生提问 Tab (学生个人视角)
*   **功能目标**：集中展示该学生在当前任务下的所有提问/评论记录。
*   **核心内容模块**：
    1.  **内容结构列表 (左侧)**：
        *   同班级报告，按任务内容结构展示。
        *   每个内容节点旁显示**该学生**在该节点下的提问/评论数量。
    2.  **提问/评论列表 (右侧)**：
        *   展示所选内容节点下**该学生**的所有提问/评论。
        *   每条内容包括：提问/评论内容、时间。
        *   若有回复，嵌套展示。
*   **界面原型/说明**:
    *原始 PRD 图片解析: images/image-29.png (学生个人提问 Tab)*
    ```markdown
    ### 课程内容数据 (左侧)
    - **课程标题**：1.2.1复数的概念
    - **章节内容**：
        - 引入部分 (该生有 N 条讨论)
        - 复数的概念引入背景 (该生有 M 条讨论)
        ...
    ### 课堂讨论数据 (右侧 - 假设只展示该生的)
    - **讨论总数**：该生共发起 X 条讨论。
    - **具体讨论**：
        - (若万洁是当前学生) 万洁于3月12日09:52提问：这节内容没有听太懂...
        - (若王维是当前学生) 王维于3月12日15:02发言：a + bi...
    - **操作功能**：可能支持教师针对该生的提问进行回复。
    ```
    *原始 PRD 图片解析: images/image-30.png (默认2栏布局)*
    ```markdown
    (布局类似 image-29.png)
    ```
    *原始 PRD 图片解析: images/image-31.png (展开第3栏 - 提问详情)*
    ```markdown
    (布局类似 image-29.png, 右侧展示具体讨论内容)
    ```

### 5.4. 题目详情页 (P0)

#### 5.4.1. 功能描述
提供单个题目的详细信息、标准答案与解析，并根据入口来源展示班级整体或特定学生的作答统计数据。

#### 5.4.2. 页面结构与核心内容
*   **入口**：
    *   从班级"答题结果 Tab"进入，展示班级作答统计。
    *   从学生个人"答题结果 Tab"进入，展示该生作答情况及班级统计（可选对比）。
*   **核心内容模块**：
    1.  **题目信息区**：
        *   完整题干、选项（选择题）或作答区域（非选择题）。
        *   题目属性：题型（单选、多选、判断、填空等）、难度系数、知识点/能力标签、分值（若有）。
    2.  **答案解析区**：
        *   正确答案。
        *   详细的步骤解析、考点分析、方法总结等。
        *   支持展开/收起。
    3.  **作答统计区 (班级视角)**：
        *   **整体统计**：班级正确率、平均答题用时、答对人数、答错人数、未作答人数。
        *   **选项分布 (选择题)**：展示每个选项的选择人数及比例，标示正确选项。
        *   **得分分布 (非选择题 - 未来)**：展示不同得分段的人数分布。
        *   **作答学生名单**：(可选) 点击各选项/得分段可查看对应的学生名单。
    4.  **作答统计区 (学生个人视角)**：
        *   **个人作答**：清晰展示该学生的答案、作答用时、得分（若有）、对错判断。
        *   **(可选) 对比数据**：展示班级平均正确率、平均用时，供学生对比。
*   **操作**：
    *   **导航**：提供"上一题"、"下一题"按钮，方便在当前任务的题目间切换浏览。
    *   **其他**："大屏讲解"（未来）、"加入讲评"（未来）、"纠错"（未来）。
*   **界面原型/说明**:
    *原始 PRD 图片解析: images/image-5.png (班级视角 - 选择题)*
    ```markdown
    ### 题目基本信息
    - **题型**：单选题
    - **难度**：1.0
    - **答题用时**：12秒 (应为平均用时)
    - **题目内容**：已知集合\(A\)中只有两个元素\(1\)，\(2a\)...
    ### 答案解析相关
    支持"展开"查看详细解析。
    ### 作答统计信息 (班级)
    - **正确率**：50%
    - **答对人数**：2人
    - **答错人数**：2人
    - **平均答题时长**：12秒
    - **选项作答情况**：B(正确): 2人 (陈鹤明, 演示001); C: 1人; D: 1人。
    ### 功能按钮相关
    支持"上一题""下一题""大屏讲解"。
    ```
    *原始 PRD 图片解析: images/image-32.png (班级视角 - 选择题)*
    ```markdown
    ### 题目详情数据
    - **题目内容**：(2024·甘肃·一模) 已知函数\(f(x)=\frac{\sin x}{e^{x}}\)...
    - **题目属性**：单选题，难度0.0，答题用时8分2秒 (平均)。
    ### 答案解析
    支持展开/收起。
    ### 作答统计数据 (班级)
    - **正确率**：46%
    - **答对人数**：6人
    - **答错人数**：7人
    - **平均答题时长**：8分2秒
    - **各选项作答人数**：A(错): 2人; B(错): 5人; C(对): 6人; 未作答: 2人。
    ### 操作功能支持
    支持"收起""展开""题目面板"。
    ```
    *(注：原始文档未提供明确的学生个人视角题目详情页原型，需根据描述推断)*

### 5.5. 策略说明 (P0)

#### 5.5.1. 鼓励策略
*   **目标**：自动识别在任务中表现突出或有显著进步的学生，生成建议列表供教师参考和干预。
*   **系统建议触发条件 (满足其一即可)**：具体判断逻辑和阈值需严格参考 [鼓励和关注的文案策略](https://wcng60ba718p.feishu.cn/wiki/R1N4waSckieoockdM2kcVHcVnvg?sheet=5e8ecf) 及图片解析 `image-14.png`, `image-15.png`。核心判断点包括：
    *   **连对王者**：连对题数破班级记录。
    *   **自我突破**：学习分较上次任务大幅提升。
    *   **连对突破**：连对题数破个人记录。
    *   **本层领跑**：学习分显著高于同层平均水平 (如>10%)。
    *   **超前学霸**：在任务发布前已完成学习。
    *   **探索达人**：提问次数破个人记录。
*   **数据依赖**：需要学生历史任务数据（学习分、连对记录、提问次数）、班级/层级历史数据（最高连对、平均分）。部分策略需要至少2次任务或单课数据才能生效。
*   **展示与交互**：
    *   在"学生报告 Tab"生成"建议鼓励"学生名单。
    *   点击学生弹出抽屉页，展示具体符合的策略标签和数据，并提供预设的干预建议和 Push 文案。
    *   教师可执行点赞、记录沟通、发送 Push 等操作。

#### 5.5.2. 关注策略
*   **目标**：自动识别在任务中表现异常或有退步风险的学生，生成建议列表供教师及时关注和干预。
*   **系统建议触发条件 (满足其一即可)**：具体判断逻辑和阈值需严格参考 [鼓励和关注的文案策略](https://wcng60ba718p.feishu.cn/wiki/R1N4waSckieoockdM2kcVHcVnvg?sheet=5e8ecf) 及图片解析 `image-17.png`, `image-18.png`。核心判断点包括：
    *   **进度未开始**：班级平均进度 > 50%，个人进度仍为 0%。
    *   **逾期未完成**：超过截止时间仍未完成。
    *   **逾期完成**：超过截止时间才完成。
    *   **偏离历史**：学习分较上次同类任务下降显著 (如>20%)。
    *   **偏离同层**：学习分显著低于同层平均水平 (如<20%)。
    *   **内容偏离**：在特定知识点/课程部分的学习分显著低于本次任务整体平均水平 (如>20%)。
*   **数据依赖**：需要任务截止时间、学生实时进度、学习分数据、历史任务数据、同层学生数据。部分策略需要至少2次任务数据才能生效。
*   **展示与交互**：
    *   在"学生报告 Tab"生成"建议关注"学生名单。
    *   点击学生弹出抽屉页，展示具体符合的策略标签和数据，并提供预设的干预建议和 Push 文案（含对应功能的跳转链接）。
    *   教师可执行标记关注、记录沟通、发送 Push 等操作。

#### 5.5.3. 共性错题策略
*   **目标**：自动识别出班级内大部分学生都掌握不佳的题目。
*   **触发条件 (P0 标准)**：
    1.  作答了该题的学生人数 > 10 人。
    2.  该题在当前任务下的首次作答正确率 < 60%。
*   **展示与应用**：
    *   在作业列表卡片上显示"待关注题目"数量（即共性错题数）。
    *   在作业报告顶部显示"待关注题目"数量。
    *   在"答题结果 Tab"的题目列表中，为满足条件的题目添加"共性错题"标识。
    *   在"答题结果 Tab"提供"只看共性错题"的筛选功能。

### 5.6. 提醒设置 (P1)

#### 5.6.1. 功能描述
(优先级 P1) 提供一个设置界面，允许教师根据班级实际情况，调整系统用于生成"建议鼓励"、"建议关注"和"共性错题"的判断阈值，并选择开启或关闭某些规则的提醒。

#### 5.6.2. 核心内容 (P1 规划)
*   **鼓励策略阈值设置**：可调整学习分提升比例、连对题数、高于同层百分比等参数。
*   **关注策略阈值设置**：可调整进度滞后标准、逾期天数、学习分下降比例、低于同层百分比、内容偏离度等参数。
*   **共性错题阈值设置**：可调整作答人数下限和正确率上限。
*   **规则开关**：允许教师启用或禁用某一条具体的鼓励/关注建议规则。

## 6. 数据需求

### 6.1. 数据统计与分析 (效果追踪)
需要通过数据埋点，追踪以下核心指标以评估功能效果和用户行为：
*   **页面访问**：作业列表、作业报告（含各 Tab）、学生个人报告、题目详情页的 PV、UV、平均停留时长。
*   **核心功能使用率**：
    *   Tab 切换频率（学生报告 vs 答题结果 vs 学生提问）。
    *   筛选、搜索功能的使用次数和转化率。
    *   "查看详情"（学生/题目）按钮的点击率。
    *   "建议鼓励/关注"名单的点击率。
    *   干预操作（点赞、提醒、记录沟通）的执行次数。
    *   "只看共性错题"、"只看错题"开关的使用率。
    *   题目面板的使用率（点击题号）。
    *   数据导出功能的使用次数。

### 6.2. 数据埋点
*   需严格按照 [教师端数据埋点汇总](https://wcng60ba718p.feishu.cn/wiki/Pw1GwQgfEi7TsPk3dgrcysMDnR0?sheet=ceLy7M) 文档中定义的事件名称、属性及其取值进行埋点设计和实施。
*   关键事件应覆盖页面浏览、核心按钮点击、筛选/搜索操作、干预行为等。

## 7. 非功能性需求

### 7.1. 性能需求
*   **列表加载**：作业列表、学生列表、题目列表在数据量较大时（如 50+ 学生，30+ 题目）应在 3 秒内完成加载和渲染。采用分页或虚拟滚动技术优化长列表性能。
*   **页面切换**：作业报告内各 Tab 切换响应时间应 < 1 秒。
*   **数据计算**：涉及到的统计指标（如正确率、平均进度、策略判断）应尽可能通过后端异步计算或缓存优化，避免前端长时间等待。

### 7.2. 兼容性需求
*   **浏览器**：支持 Chrome、Firefox、Safari、Edge 的最新稳定版本。
*   **分辨率**：界面布局应能良好适配主流桌面显示器分辨率（建议 1366px 及以上）。

### 7.3. 可用性需求
*   **信息架构清晰**：页面布局、导航逻辑符合教师使用习惯，易于找到所需信息。
*   **数据可视化**：关键指标（正确率、进度）应采用清晰、直观的方式展示（如进度条、百分比、颜色区分等）。
*   **交互反馈及时**：用户的操作（点击、筛选、排序等）应有及时的视觉反馈。
*   **文案准确易懂**：界面上的文字、标签、提示信息应准确、简洁、易于理解。

## 8. 需求检查清单

| 序号 | 检查项                                                              | 原始需求对应章节/内容                             | 本文档对应章节 | 状态 |
| :--- | :------------------------------------------------------------------ | :------------------------------------------------ | :------------- | :--- |
| 1    | 作业列表能展示教师权限内的所有任务卡片？                          | 四、需求概览-1；五、作业列表；区分职务的数据    | 5.1            | ✅    |
| 2    | 作业列表卡片指标是否完整（完成率、正确率、进度等）？                | 五、作业列表-卡片指标                             | 5.1.2          | ✅    |
| 3    | 作业列表支持筛选（类型、班级、学科、日期）和搜索（名称）？        | 五、作业列表-筛选、搜索                           | 5.1.2          | ✅    |
| 4    | 作业报告包含学生报告、答题结果、学生提问三个Tab？                 | 四、需求概览-2；五、作业报告                      | 5.2            | ✅    |
| 5    | 学生报告Tab包含建议鼓励/关注、班级统计、学生列表？                | 五、作业报告-学生报告Tab                          | 5.2.3          | ✅    |
| 6    | 答题结果Tab包含整体统计、题目列表（含正确率等）、题目面板？         | 五、作业报告-答题结果Tab                          | 5.2.4          | ✅    |
| 7    | 学生提问Tab能按课程结构展示提问及数量？                           | 五、作业报告-学生提问Tab                          | 5.2.5          | ✅    |
| 8    | 支持查看单个学生的作业报告（含答题结果、学生提问）？              | 四、需求概览-3；五、xx学生的作业报告              | 5.3            | ✅    |
| 9    | 支持查看题目详情（含题目、解析、作答统计）？                      | 五、题目详情页                                    | 5.4            | ✅    |
| 10   | 鼓励策略是否按要求定义和展示？                                      | 四、需求概览-4；五、策略-鼓励                     | 5.5.1          | ✅    |
| 11   | 关注策略是否按要求定义和展示？                                      | 四、需求概览-4；五、策略-关注                     | 5.5.2          | ✅    |
| 12   | 共性错题策略是否按要求定义和展示？                                  | 四、需求概览-4；五、策略-共性错题                 | 5.5.3          | ✅    |
| 13   | 是否包含 P0 核心功能的业务流程图 (Mermaid)？                      | 三、业务流程 (diagram-1.png)                      | 3.2            | ✅    |
| 14   | 主要用户场景是否有流程图 (Mermaid)？                              | (根据要求生成)                                    | 4.1            | ✅    |
| 15   | 图片解析是否已整合入文档相应章节？                                | 七、图片解析                                      | 5.1.3, 5.2等   | ✅    |
| 16   | 数据处理规则（如正确率计算、作答定义等）是否明确并引用口径？        | 五、作业报告-答题结果Tab-题目信息展示；口径说明   | 5.1.4, 5.2.4   | ✅    |
| 17   | P1 需求（提醒设置）是否已列出但标记为 P1？                        | 四、需求概览-5；五、策略-自定义                   | 3.1.3, 5.6     | ✅    |
| 18   | 数据需求（效果追踪、埋点）是否已包含并引用文档？                    | 六、数据需求                                      | 6              | ✅    |
| 19   | 非功能需求（性能、兼容、可用）是否已考虑？                        | (根据经验补充)                                    | 7              | ✅    |

--- 等待您的命令，指挥官 
---
【Cursor开发中rules配置文件】Python优化版
---

### 通用原则
1. 默认情况下，所有回复都必须是中文，开头称呼用户为"帅哥："
2. 复杂需求拆解成小任务，分步实现
3. 代码实现遵循"第一性原理"、"KISS原则"和"SOLID原则"
4. 遵循PEP 8代码风格，使用4个空格缩进
5. 使用描述性的变量名和函数名，增强可读性
6. 代码修改遵循单一职责原则，不混合多个变更
7. 尽量复用已有代码，避免重复
8. 不引入不必要的依赖，最小化第三方库数量
9. 确保代码可读性与可维护性，必要时加简要注释
10. 如果有疑问，先询问再修改

### 代码质量规范
11. 类型注解与文档：
    - 函数参数和返回值添加类型注解
    - 主要函数和类添加docstring（Google或NumPy风格）
    - 使用mypy进行静态类型检查
12. 错误处理策略：
    - 使用异常而非返回特殊值表示错误
    - 异常捕获要具体，避免过于宽泛的except
    - 使用日志（logging/loguru）记录错误和调试信息
13. 代码优化：
    - 使用列表推导式、生成器表达式等Pythonic方式
    - 关注算法复杂度，优化性能关键路径
    - 大数据处理使用惰性求值（如生成器）
    - 使用适当的并行处理（concurrent.futures、multiprocessing）

### 深度学习与模型开发
14. 框架与库：
    - 使用PyTorch作为主要深度学习框架
    - 使用Transformers处理预训练模型和分词器
    - 使用Diffusers实现扩散模型
15. 模型实现：
    - 自定义模型继承nn.Module
    - 正确实现注意力机制和位置编码
    - 使用混合精度训练（torch.cuda.amp）优化性能
    - 使用DataParallel或DistributedDataParallel进行多GPU训练

### Web开发通用规范
16. 架构设计：
    - 遵循分层架构（路由、服务、数据访问层）
    - 服务设计为无状态，使用外部存储实现状态持久化
    - 使用环境变量管理配置，敏感信息不进入版本控制
17. API设计：
    - RESTful API设计，URI使用名词表示资源
    - 使用HTTP动词表示操作（GET、POST、PUT、DELETE等）
    - 返回JSON格式数据，统一响应结构
    - 实现适当的身份验证和授权机制
18. 性能优化：
    - 使用Redis/Memcached缓存频繁访问的数据
    - 使用异步处理I/O密集型任务
    - 数据库查询优化（索引使用、查询结构）
    - 使用消息队列处理后台任务

### 框架专用规范
19. Django开发：
    - 使用基于类的视图（CBV）处理复杂逻辑
    - 使用Django ORM，避免原生SQL
    - 使用Django REST框架（DRF）进行API开发
    - 使用select_related和prefetch_related优化查询
20. FastAPI开发：
    - 使用函数式编程，为所有函数签名添加类型提示
    - 使用Pydantic模型进行输入验证和序列化
    - 使用异步操作处理I/O密集型任务
    - 使用依赖注入系统管理状态和共享资源
21. Flask开发：
    - 使用应用工厂和蓝图组织代码
    - 使用Flask-SQLAlchemy进行ORM操作
    - 使用Marshmallow进行序列化和验证
    - 使用Gunicorn或uWSGI作为生产环境的WSGI服务器

### 数据处理与科学计算
22. 数据处理流程：
    - 数据获取：使用requests、Scrapy、Selenium
    - 数据清洗：使用pandas处理缺失值和异常值
    - 数据转换：使用pandas、numpy进行数据操作
    - 数据存储：使用SQLAlchemy、pymongo等
23. 数据科学规范：
    - 数据处理步骤明确分离，确保可重现
    - 使用scikit-learn Pipeline封装数据转换和模型训练
    - 模型训练、评估、预测功能分离
    - 使用交叉验证确保模型稳定性
24. 可视化最佳实践：
    - 使用matplotlib、seaborn进行静态可视化
    - 使用plotly、dash构建交互式仪表盘
    - 图表包含标题、坐标轴标签、图例
    - 大数据集使用适当抽样或聚合可视化

### 项目管理与部署
25. 版本控制：
    - commit信息描述修改点和原因
    - 更新相关文档（README、API文档等）
    - API变更提供兼容策略
26. 测试与质量控制：
    - 使用pytest进行自动化测试
    - 使用pylint、flake8检查代码质量
    - 重要操作前自动备份数据
27. 部署与监控：
    - 使用Docker容器化应用
    - 使用CI/CD自动化部署流程
    - 使用Prometheus和Grafana监控应用性能
    - 不同环境（开发、测试、生产）配置分离
    
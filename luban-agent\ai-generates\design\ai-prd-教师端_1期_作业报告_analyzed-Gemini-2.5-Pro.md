# 产品需求文档：教师端1期作业报告

## 1. 需求背景与目标

### 1.1 需求背景
- **业务层面**：教师在授课之余，核心工作之一是精准掌握学生的学习情况。这包括从学生个体和班级整体两个维度，对完成度/进度、正确率/得分、错题/答题数量以及用时等关键指标进行追踪。同时，教师需要及时了解学生在题目层面的共性错误、各题目的正确率、学生对各选项的选择分布以及用时情况。此外，对于学生在学习过程中产生的提问，教师也需要掌握其共性问题所在的知识点和具体的提问内容。
- 在学生的学习旅程中，教师扮演着"鼓励者"的角色，需要对学生的每一个进步予以表扬，并对学习行为中的不当之处进行及时沟通和引导。系统应能辅助教师识别值得表扬（如学习进步、连续答对题目）和需要关注（如未完成作业、学习情况明显下滑）的学生情况。
- **技术层面**：需要构建清晰、稳固的底层数据关系，联通各类任务（如课程作业、测验）、教学素材与学生行为数据。

### 1.2 项目收益
- 完成教师端P0核心功能，确保产品在预定6月上线的版本中能够顺利跑通业务MVP（最小可行产品）流程。
- 目标在9月完成所有核心功能的开发与上线。

### 1.3 覆盖用户
- **目标用户**：合作校的教师。

### 1.4 方案简述
1.  **作业列表核心信息展示**：采用"卡片式"设计，在作业列表中集中展示各项作业任务最核心的信息。
2.  **多维度作业报告详情**：用户点击作业卡片后，系统将提供从"学生报告"、"答题结果"、"学生提问"三个不同维度审视当次作业情况的详细报告。

## 2. 功能范围

### 2.1 核心功能
- **P0 核心功能 - 课程作业资源相关的报告**：
    - **学生报告Tab**：
        - **功能描述**：在此Tab下，教师可以查看班级内每位学生针对特定作业的详细学情数据，包括但不限于完成进度、正确率、错题数、用时、以及系统根据学生表现给出的"建议鼓励"或"建议关注"标签。
        - **用户场景**：教师需要快速了解班级整体学情，并能下钻到每个学生的具体表现，以便进行个性化辅导。
        - **解决痛点**：解决教师难以高效、全面掌握每个学生作业情况的痛点，辅助教师识别优秀学生和后进学生。
        - **优先级**：P0
        - **依赖关系**：依赖作业数据的采集与处理。
    - **答题结果Tab**：
        - **功能描述**：在此Tab下，教师可以从题目维度查看整个班级或特定学生群体在每道题目上的表现，包括题目的正确率、错误人数、作答人数、共性错题等。
        - **用户场景**：教师需要分析哪些题目是学生的普遍难点，哪些知识点掌握不牢固。
        - **解决痛点**：解决教师难以快速定位共性问题和教学难点的痛点，为调整教学重点提供数据支持。
        - **优先级**：P0
        - **依赖关系**：依赖作业数据的采集、处理及题目维度的统计分析。
    - **学生提问Tab**：
        - **功能描述**：在此Tab下，教师可以查看学生在完成作业过程中，针对不同知识点或题目提出的问题。系统会汇总展示提问内容及提问集中的环节。
        - **用户场景**：教师需要了解学生在学习过程中的具体困惑和疑问，以便答疑解惑。
        - **解决痛点**：解决教师难以全面收集和掌握学生具体学习疑问的痛点，促进师生互动和教学相长。
        - **优先级**：P0
        - **依赖关系**：依赖学生提问数据的采集与关联。
- **作业列表功能**：
    - **功能描述**：以列表形式展示教师负责或有权限查看的全部作业。卡片式设计，突出每份作业的核心信息，如完成率、正确率、待关注项等。
    - **用户场景**：教师需要一个统一的入口来管理和追踪所有布置的作业。
    - **解决痛点**：解决作业信息分散、难以统一概览和快速定位的问题。
    - **优先级**：P0
    - **依赖关系**：无。
- **个体学生作业报告**：
    - **功能描述**：从班级报告或学生列表进入，展示特定学生在某份作业上的详细"答题结果"和"学生提问"情况。
    - **用户场景**：教师在了解班级整体情况后，需要对特定学生进行深入分析。
    - **解决痛点**：支持对个体学生的精细化学情诊断。
    - **优先级**：P0
    - **依赖关系**：依赖班级作业报告功能。
- **策略说明功能集成**：
    - **功能描述**：在报告中融入"鼓励"、"关注"、"共性错题"等策略的提示和可视化，帮助教师理解数据背后的意义并引导其进行相应操作。
    - **用户场景**：教师需要系统辅助解读数据，并给出教学建议。
    - **解决痛点**：提升教师的数据解读能力和教学干预的有效性。
    - **优先级**：P0
    - **依赖关系**：依赖各项数据统计和分析功能。

### 2.2 辅助功能
- **数据筛选与搜索**：
    - **功能描述**：在作业列表和报告详情页（如答题结果Tab）提供基于类型、班级、学科、布置日期、题目关键词等的筛选和搜索功能。
    - **为核心功能提供支持**：帮助教师在大量数据中快速定位到所需信息，提升核心功能的使用效率。
- **数据导出**：
    - **功能描述**：在学生报告Tab中，支持将学生列表数据导出。
    - **为核心功能提供支持**：方便教师进行离线分析、存档或与其他工具结合使用。
- **预警/关注/鼓励的快捷操作**：
    - **功能描述**：在学生报告Tab中，为"值得表扬"和"需要关注"的学生群体提供"一键表扬"和"一键提醒"等快捷操作。在查看单个学生详情时，提供"点赞鼓励"、"线下沟通"、"Push提醒"等操作。
    - **为核心功能提供支持**：提升教师进行教学干预和学生管理的效率。

### 2.3 非本期功能
- **提醒设置自定义**：
    - **功能描述**：允许用户自定义提醒策略的触发条件和提醒方式。
    - **建议迭代**：P1
- **测验相关报告的大屏模式**：
    - **功能描述**：针对测验类型的任务，提供适配大屏展示的报告模式。
    - **建议迭代**：未来功能

## 3. 计算规则与公式

### 3.1 题目答题数据统计规则
- **正确率 (题目维度)**
    - **公式**: `(答对小空数量 / 题目中的总小空数量) * 100%`
    - **参数说明**：
        - `答对小空数量`：学生在首次作答该题目时，答对的最小知识单元（小空）的数量。
        - `题目中的总小空数量`：该题目包含的最小知识单元（小空）的总数。
    - **适用范围**：针对所有作答了该题目的学生，在计算其个人题目正确率时使用。
    - **统计原则**：
        1.  **首次作答优先**：若学生在当前任务素材中被推送了多次同一题目，仅采纳学生首次作答的数据进行统计。
        2.  **"暂不作答"的处理**：在已提交的课堂、测试、作业任务下，若学生看到题目后选择"暂不作答"，则该题记为"已作答"且"答错"。
        3.  **未提交作业的跳过作答**：对于未提交的作业，如果学生跳过某题先作答其他题目，则该跳过题目此时不算作"已作答"。
- **答错人数 (题目维度)**
    - **定义**: 在任务下的首次作答中，答错至少1个小空的学生数量。
    - **统计原则**: 同"正确率"的统计原则。
- **作答人数 (题目维度)**
    - **定义**: 在任务下，有作答记录（包括选择"暂不作答"的情况，依据上述原则）的学生数量。
    - **统计原则**: 同"正确率"的统计原则。

### 3.2 班级整体数据统计规则
- **班级平均进度 (班级报告Tab)**
    - **定义**: 指当前班级，在指定素材范围内，班级整体的平均完成率。具体计算方式参考任务卡片中的"完成率"指标。
    - **备注**: 原始文档提及"同任务卡片：完成率"，具体完成率计算方法需参照任务卡片模块的定义。若无明确定义，通常为 `(班级内已完成作业的学生数 / 班级总学生数) * 100%` 或 `(班级内所有学生作业完成度之和 / 班级总学生数)`。
- **班级正确率 (班级报告Tab)**
    - **定义**: 指当前班级，在指定素材范围内，班级整体的平均正确率。具体计算方式参考任务卡片中的"正确率"指标。
    - **备注**: 原始文档提及"同任务卡片：正确率"，具体正确率计算方法需参照任务卡片模块的定义。若无明确定义，通常为 `(班级内所有学生所有已作答题目的总正确率之和 / 班级内所有学生已作答题目的总数)` 或 `(班级内所有学生平均正确率之和 / 班级总学生数)`。

### 3.3 数据异常与高亮规则
- **列表中异常数据的飘色 (学生报告Tab)**
    - **规则**: 对于学生列表中，与班级均值偏离较大的数据进行特殊颜色高亮展示。
    - **示例阈值**：
        - 低于班级平均值的20%的数据。
        - 排在最后的10%的数据。
    - **适用指标**: 具体应用到哪些指标（如正确率、用时等）需进一步明确。

### 3.4 "建议鼓励"的推荐类型与规则

*IMPORTANT AI (Chief Product Manager) INSTRUCTION: 此处根据原始PRD图片 `in_table_image_AnvObATkyod5a5xurxecUjEQnKc` 中的表格进行整理。*

- **连对王者**
    - **展示指标**: 连对题数。
    - **可视化方式**: 新纪录 vs 班级该学科历史最高纪录。
    - **展示要求**: 学生本次作业连对题数 > 班级该学科历史最高纪录，则展示 "恭喜XX同学，连对XX题，打破班级纪录（原纪录XX题）！"
- **自我突破**
    - **展示指标**: 学习分增幅。
    - **可视化方式**: 本次任务平均单课学习分 vs 上次任务平均单课学习分。
    - **展示要求**: 基于个人历史记录计算。展示"XX同学本次任务学习分较上次提升XX%！"
- **连对突破**
    - **展示指标**: 连对题数。
    - **可视化方式**: 新纪录 vs 个人该学科历史最高纪录。
    - **展示要求**: 学生本次作业连对题数 > 个人该学科历史最高纪录，则展示"XX同学本次作业连对XX题，创造个人新纪录（原纪录XX题）！"
- **本层领跑**
    - **展示指标**: 与同层级学生平均学习分差值。
    - **可视化方式**: 本人平均单课学习分 vs 同层学生平均单课学习分。
    - **展示要求**: 假设同层学生学习分平均分为X，学生为Y，则展示"XX同学学习分Y，超过同层平均水平Z%！" （Z = (Y-X)/X * 100%）。（原始文档中"由于同层同学学习分平均分100"可能为示例或特定条件，此处按通用差值百分比理解）
- **超前学霸**
    - **展示指标**: 任务完成时间。
    - **可视化方式**: 任务布置时间 vs 本人完成时间。
    - **展示要求**: 学生完成时间早于任务布置时间（或截止时间前X%的时间内完成，需明确），展示"XX同学于X月X日X时完成任务，真是个早起的鸟儿有虫吃！" (文案可优化)
- **探索达人**
    - **展示指标**: 单课提问次数。
    - **可视化方式**: 新纪录 vs 个人历史单课最高提问次数。
    - **展示要求**: 学生本次作业关联课程中单课提问次数 > 个人历史单课最高提问次数，则展示"XX同学在本课中提问XX次，探索精神可嘉，创造个人记录！"

### 3.5 "建议关注"的异常行为规则

*IMPORTANT AI (Chief Product Manager) INSTRUCTION: 此处根据原始PRD图片 `in_table_image_Pg2lbdqDboNMPlx1r4ccsxwmn1P` 中的表格进行整理。*

- **未开始学习**
    - **展示指标**: 班级平均进度 vs 本人进度。
    - **可视化方式**: 文字提示。
    - **展示要求**: 当班级平均进度达到50%，学生本人进度仍为0%（未开始）时，提示"班级平均进度已达50%，该生仍未开始学习"。
- **逾期未完成**
    - **展示指标**: 逾期天数。
    - **可视化方式**: 文字提示。
    - **展示要求**: 学生作业超过截止日期仍未完成，展示"该生作业已逾期X天未完成"。
- **逾期完成**
    - **展示指标**: 逾期天数。
    - **可视化方式**: 文字提示。
    - **展示要求**: 学生作业在截止日期之后完成，展示"该生作业逾期X天后完成"。
- **学习表现偏离历史 (与自身过往比较)**
    - **展示指标**: 本次同类任务平均学习得分 vs 以往同类任务平均学习得分。
    - **可视化方式**: 文字提示与百分比差异。
    - **展示要求**: 学生本次任务（或单课）平均学习得分较以往同类任务平均学习得分下降X%（如20%），展示"该生本次任务表现较以往下降X%"。
- **学习表现偏离同层 (与同水平群体比较)**
    - **展示指标**: 本人单课学习得分平均分 vs 同层学生单课学习得分平均分。
    - **可视化方式**: 文字提示与百分比差异。
    - **展示要求**: 学生本人单课学习得分平均分低于同层学生平均分Y%（如15%），展示"该生表现低于同层平均水平Y%"。
- **学习内容偏离 (任务内部不同内容比较)**
    - **展示指标**: 本次任务某课程的课程得分 vs 本次任务各课程的平均分。
    - **可视化方式**: 文字提示与具体偏离课程。
    - **展示要求**: 学生在本次任务中，某课程Z的得分显著低于本次任务各课程的平均分（如低于平均分W%），展示"该生在课程Z上表现不佳，得分较任务平均水平低W%"。

## 4. 用户场景与故事

### 场景1：学科老师查看作业列表并进入特定作业报告

作为一个 **学科老师**，我希望能够 **方便地查看我布置的或我有权限查看的所有作业的列表，并能快速了解每份作业的核心状态（如完成率、正确率、到期情况），然后轻松进入某份具体作业的详细报告页面**，这样我就可以 **高效地追踪教学任务进展，及时发现需要关注的作业，并进一步分析学情**。

目前的痛点是 **如果作业信息分散，或者列表信息不直观，我很难快速掌握整体情况，需要花费大量时间逐个查找和判断**，如果能够 **在一个统一的列表中清晰展示作业核心指标，并能一键进入详细报告**，对我的日常教学管理工作会有很大帮助。

**关键步骤：**
1.  学科老师登录教师端系统，进入作业管理或相关功能模块。
2.  系统展示作业列表，以卡片形式呈现每份作业的核心信息（如作业名称、班级、发布时间、截止时间、完成率、正确率、待关注项等）。
3.  老师可以通过筛选（如按班级、学科、布置日期、作业状态）和搜索功能快速定位到目标作业。
4.  老师点击某份作业卡片。
5.  系统跳转到该份作业的详细报告页面，默认展示"学生报告"Tab的内容。

- **优先级**：P0
- **依赖关系**：依赖作业数据的采集和作业列表的构建。

```mermaid
flowchart TD
    A[学科老师登录系统] --> B[进入作业管理模块];
    B --> C{作业列表展示};
    C -- 卡片式呈现 --> D[作业A信息（完成率、正确率等）];
    C -- 卡片式呈现 --> E[作业B信息（完成率、正确率等）];
    C --> F[筛选与搜索功能];
    F -- 应用筛选/搜索 --> C;
    D -- 点击 --> G[进入作业A的详细报告页面];
    E -- 点击 --> H[进入作业B的详细报告页面];
    G --> I[默认展示'学生报告'Tab];
    H --> J[默认展示'学生报告'Tab];
    %% 用户通过列表快速概览并选择特定作业进行深入分析
```

### 场景2：教师查看班级整体学情（学生报告Tab）

作为一个 **教师**（学科老师/班主任），在打开某份作业的详细报告后，我希望能够 **首先在"学生报告"Tab中清晰地看到整个班级的整体学情概览，包括班级平均进度、平均正确率，以及系统自动识别出的"值得表扬"和"需要关注"的学生群体，并能快速对这些学生进行批量操作（如一键表扬/提醒）**，这样我就可以 **迅速把握班级整体学习状况，并高效处理重点学生群体**。

目前的痛点是 **手动分析全班每个学生的原始数据来找出优秀和落后的学生非常耗时，且容易遗漏**，如果能够 **系统自动汇总班级数据、智能推荐重点学生并提供快捷操作**，对我的教学管理和学生激励/辅导工作将有极大提升。

**关键步骤：**
1.  教师已进入某份作业的详细报告页面，当前默认或主动切换到"学生报告"Tab。
2.  系统在页面上部展示该作业的班级平均进度和班级平均正确率。
3.  系统展示"值得表扬"学生列表（如根据连对王者、自我突破等规则生成），并提供"一键表扬"按钮。
4.  系统展示"需要关注"学生列表（如根据未开始学习、逾期未完成、表现偏离历史/同层等规则生成），并提供"一键提醒"按钮。
5.  教师查看这些信息，对班级整体情况形成初步判断。
6.  教师可选择点击"一键表扬"或"一键提醒"对相应学生群体进行批量操作。
7.  系统执行批量操作（如发送预设的表扬/提醒消息）。

- **优先级**：P0
- **依赖关系**：依赖作业数据统计分析、学生分层与标签规则、"建议鼓励"与"建议关注"规则的计算。

```mermaid
sequenceDiagram
    participant T as 教师
    participant S as 系统

    T->>S: 进入作业报告的"学生报告"Tab
    S-->>T: 显示班级平均进度、平均正确率
    S-->>T: 显示"值得表扬"学生列表及"一键表扬"按钮
    %% "值得表扬"学生列表根据3.4节规则生成
    S-->>T: 显示"需要关注"学生列表及"一键提醒"按钮
    %% "需要关注"学生列表根据3.5节规则生成
    T->>T: 评估班级整体情况和重点学生群体
    alt 一键表扬
        T->>S: 点击"一键表扬"
        S->>S: 处理表扬逻辑（如发送Push）
        S-->>T: 反馈操作结果
    else 一键提醒
        T->>S: 点击"一键提醒"
        S->>S: 处理提醒逻辑（如发送Push）
        S-->>T: 反馈操作结果
    end
```

### 场景3：教师查看详细学生列表并对单个或多个异常学生进行干预（学生报告Tab）

作为一个 **教师**，在"学生报告"Tab了解了班级整体情况和系统推荐的重点学生群体后，我希望能够 **进一步查看包含所有学生的详细数据列表，并通过姓名搜索定位特定学生，同时系统应对列表中的异常数据（如远低于平均水平的正确率）进行高亮提示**，这样我就可以 **全面掌握每个学生的具体表现，并能针对性地对表现异常的单个或多个学生进行查看详情、或进一步的个性化干预（如点赞鼓励、线下沟通、Push提醒）**。

目前的痛点是 **系统推荐的群体可能仍不够细致，我需要一个方式来审视全班数据并快速找到那些未被系统高亮归类但表现仍需注意的学生，或者对已归类的学生进行更个性化的处理，而非仅仅批量操作**，如果能 **提供完整的学生列表、清晰的异常数据提示，并允许我对单个学生进行精细化操作**，将有助于我更全面、更灵活地管理学生。

**关键步骤：**
1.  教师在作业报告的"学生报告"Tab下，已查看了上方的班级概览和推荐学生群体。
2.  教师视线下移，查看页面中下部的"任务学生列表"。
3.  系统展示包含所有学生的列表，列出每位学生的学生信息、分层标签（如有）、完成进度、正确率、答题难度（如有）、错题数/答题数、用时等。
4.  系统根据"数据异常与高亮规则"（如低于班级平均值20%的数据）对列表中的异常数据进行飘色展示。
5.  教师可以通过"搜索学生姓名"框快速定位特定学生。
6.  教师发现某学生（如李子涵）的数据异常（如正确率60%，远低于班级平均）或带有"需关注"标签。
7.  教师点击该学生行末的"查看详情"操作，进入该生的个体作业报告页面。
8.  或者，教师直接在该列表判断后，决定对某个"值得鼓励"的学生进行"点赞鼓励"，或对"需要关注"的学生进行"Push提醒"或记录"线下沟通"（如果列表行提供此类快捷操作按钮，或者进入详情后操作）。原始PRD中，这类操作更多在学生详情页，但列表页也可能集成简易操作。

- **优先级**：P0
- **依赖关系**：依赖完整的学生作业数据、数据异常高亮规则、搜索功能。

```mermaid
flowchart TD
    A[教师在'学生报告'Tab] --> B[查看下方'任务学生列表'];
    B --> C[系统展示全班学生数据列表];
    C -- 包含 --> D[学生A：正常数据];
    C -- 包含 --> E[学生B：正确率60%（飘色高亮）];
    C -- 包含 --> F[学生C：'值得鼓励'标签];
    C --> G[搜索框：'搜索学生姓名'];
    G -- 输入'李子涵' --> H{定位到李子涵（学生B）};
    E --> I{教师关注到学生B的异常数据};
    I -- 点击'查看详情' --> J[进入学生B的个体作业报告页];
    F -- 教师判断 --> K[对学生C进行鼓励操作（如进入详情页后点赞）];
    %% 教师通过详细列表全面掌握个体情况，并对异常学生进行针对性处理
```

### 场景4：教师分析题目整体作答情况和共性错题（答题结果Tab）

作为一个 **教师**，在对学生整体学情有了大致了解后，我希望能切换到"答题结果"Tab，**从题目的维度去分析本次作业的整体答题情况，特别是快速识别出"共性错题"（如错误率较高的题目），并能通过关键词搜索或题型筛选来定位我想分析的题目**，这样我就可以 **找到教学中的普遍性难点和学生的知识薄弱点，为后续的课堂讲解和教学调整提供依据**。

目前的痛点是 **逐题分析学生的答案来找出共性问题非常困难，效率低下**，如果系统能够 **自动统计每道题的正确率、错误人数，并高亮"共性错题"，同时提供便捷的题目筛选和搜索功能**，将大大提升我分析教学效果的效率。

**关键步骤：**
1.  教师在作业报告页面，从"学生报告"Tab或其他Tab切换到"答题结果"Tab。
2.  系统在页面上部展示本次作业的"共XX道题，XX道共性错题"的统计信息。
3.  系统默认展示题目列表，并可能按错误人数对题目进行排序（或按"共性错题"优先展示）。
4.  教师可以使用"搜索题目关键词"功能查找特定题目。
5.  教师可以使用"只看共性错题"开关，筛选出共性错题列表。
6.  教师可以使用"全部题型"筛选器，按题型查看题目列表。
7.  对于列表中的每道题，系统展示其题干预览、正确率、错误人数、作答人数。
8.  教师浏览题目列表，重点关注正确率低、错误人数多的题目，特别是被标记为"共性错题"的题目。

- **优先级**：P0
- **依赖关系**：依赖题目维度的答题数据统计、共性错题识别规则。

```mermaid
sequenceDiagram
    participant T as 教师
    participant S as 系统

    T->>S: 切换到"答题结果"Tab
    S-->>T: 显示作业题目统计（总题数、共性错题数）
    S-->>T: 显示题目列表（默认排序，含题干、正确率、错误人数、作答人数）
    T->>S: （可选）输入关键词搜索题目
    S-->>T: 返回符合搜索条件的题目列表
    T->>S: （可选）打开"只看共性错题"开关
    S-->>T: 返回共性错题列表
    T->>S: （可选）选择特定"题型"进行筛选
    S-->>T: 返回符合题型筛选的题目列表
    T->>T: 浏览列表，分析题目作答情况，定位教学难点
    %% 教师通过题目维度的分析，找出教学中的薄弱环节
```

### 场景5：教师查看单个题目的详细作答数据（答题结果Tab）

作为一个 **教师**，当我在"答题结果"Tab中定位到一道需要重点分析的题目（如共性错题或某道特定题目）后，我希望能够 **点击"查看"进入该题的详情页面，了解其题型、难度、完整题干、选项、正确答案及解析，并能看到该题目的整体作答统计（正答率、各选项选择人数分布）以及具体哪些学生选了哪个选项，特别是选错的学生名单**，这样我就可以 **深入分析学生答错的原因，是概念不清、审题失误还是其他问题，为针对性讲解和辅导提供更细致的依据**。

目前的痛点是 **仅看整体正确率难以了解学生具体的思维误区，需要更详细的数据来支撑教学判断**，如果能够 **提供单题的多维度分析数据，包括学生选项分布和错误详情**，将极大帮助我理解学生的学习难点。

**关键步骤：**
1.  教师在"答题结果"Tab的题目列表中，找到一道目标题目。
2.  教师点击该题目对应的"查看"按钮。
3.  系统跳转到该题的"答题结果详情"页面。
4.  页面展示题目的完整信息：题型、难度、题干、各选项内容。
5.  页面展示该题的答案和详细解析（默认可能收起，点击可展开）。
6.  页面展示该题的整体作答统计：正答率、答对人数、答错人数、平均答题时长。
7.  页面展示各选项的选择情况：选择A选项的学生列表、选择B选项的学生列表等，并标示出正确选项。
8.  教师可以重点查看选择了错误选项的学生名单，分析其错误原因。
9.  页面可能提供"大屏讲解"等辅助功能。

- **优先级**：P0
- **依赖关系**：依赖题目列表功能、单题详细作答数据的统计与存储。

```mermaid
graph TD
    A[教师在'答题结果'Tab的题目列表中] --> B{定位到某题目};
    B -- 点击'查看' --> C[进入'答题结果详情'页面];
    C --> D[展示：题干、选项、题型、难度];
    C --> E[展示：答案与解析（可展开/收起）];
    C --> F[展示：整体作答统计（正答率、人数、用时）];
    C --> G[展示：各选项选择情况及学生名单];
    G -- 包含 --> H[选项A：张三、李四];
    G -- 包含 --> I[选项B（正确答案）：王五、赵六];
    G -- 包含 --> J[选项C：陈七];
    H --> K[教师分析选错学生原因];
    J --> K;
    C --> L[（可选）'大屏讲解'等辅助功能];
    %% 教师深入分析单题的作答细节，找出学生具体错误原因
```

### 场景6：教师了解学生疑问点（学生提问Tab）

作为一个 **教师**，我希望在作业报告中能够切换到"学生提问"Tab，**集中查看本次作业或相关课程内容中，学生们提出的所有问题，了解问题主要集中在哪些知识点或题目上，并能看到具体的提问内容和提问学生**，这样我就可以 **及时掌握学生的困惑点，有针对性地进行答疑，或者调整后续的教学内容与方法**。

目前的痛点是 **学生的疑问可能分散在各个地方，不易收集和统一处理，也难以判断哪些是共性问题**，如果能 **将学生提问集中展示并按课程内容结构或提问热度进行组织**，将方便我高效地进行答疑和教学反思。

**关键步骤：**
1.  教师在作业报告页面，从其他Tab切换到"学生提问"Tab。
2.  系统展示与本次作业关联的课程内容结构（如按幻灯片/知识点顺序）。
3.  在每个课程内容节点旁，系统展示本班学生在该节点下的提问数量统计。
4.  教师可以点击某个有提问的内容节点，查看具体的提问列表。
5.  提问列表展示提问学生、提问内容、提问时间等。
6.  教师浏览学生的提问，了解学生的疑问点和思考过程。

- **优先级**：P0
- **依赖关系**：依赖学生提问功能的实现、提问数据与课程/作业内容的关联。

```mermaid
sequenceDiagram
    participant T as 教师
    participant S as 系统

    T->>S: 切换到"学生提问"Tab
    S-->>T: 展示与作业关联的课程内容结构
    S-->>T: 在各内容节点旁显示学生提问数量统计
    T->>S: 点击某个有提问的内容节点（如"1.2.1 复数的概念 - 引入部分"）
    S-->>T: 展示该节点下的学生提问列表（提问人、内容、时间）
    T->>T: 浏览提问，分析学生疑问点
    %% 教师通过集中的学生提问，了解学生的学习困惑
```

### 场景7：教师查看特定学生的完整作业报告

作为一个 **教师**（学科老师/班主任），当我想深入了解某一个特定学生的作业完成情况时，例如从班级"学生报告"Tab的学生列表中点击某学生的"查看详情"，或者从其他入口（如学生个人中心、消息提醒等）进入，我希望能够 **看到该学生本次作业的完整报告，包括TA的答题概览（如完成时间、进度）、详细的"答题结果"（每道题的作答对错情况）以及TA提出的"学生提问"**，这样我就可以 **全面评估这位学生的学习状况，进行有针对性的辅导和沟通**。

目前的痛点是 **如果只能看到班级汇总数据或者学生的部分数据，很难对单个学生形成完整画像，不利于个性化指导**，如果能 **提供一个完整的、专属于某个学生的作业报告视图**，将使我的辅导更加精准有效。

**关键步骤：**
1.  教师通过任一入口（如班级作业报告的学生列表、学生个人课堂详情弹窗的学习任务卡片）选择查看特定学生的作业报告。
2.  系统跳转到该学生的个体作业报告页面，页面顶部清晰展示学生姓名和作业名称。
3.  页面展示该生本次作业的答题概览：任务开始与要求完成时间；若已完成，则显示本人完成时间；若未完成，显示本人进度；以及任务的素材范围。
4.  页面提供"答题结果"Tab和"学生提问"Tab（若有提问）。
5.  教师切换到"答题结果"Tab：
    a.  系统展示该生"共XX道题，XX道错题"的个人统计。
    b.  系统展示题目列表，并清晰标出该生在每道题上的作答结果（正确✅、错误❌、部分对、尚未作答）。
    c.  教师可以点击题目查看题目详情和学生答案（同场景5，但视角为单个学生）。
6.  教师切换到"学生提问"Tab（若有）：
    a.  系统展示该生在本次作业或关联课程中提出的问题列表。
    b.  教师可以查看提问详情。

- **优先级**：P0
- **依赖关系**：依赖班级作业报告功能、学生个体答题数据和提问数据的记录与调取。

```mermaid
flowchart TD
    A[教师通过入口选择查看特定学生X的作业报告] --> B[进入学生X的个体作业报告页面]
    B --> C[顶部显示：学生X + 作业名称]
    B --> D[展示：学生X的答题概览（完成时间/进度、素材范围）]
    B --> E["包含Tab：答题结果 - 学生提问"]
    subgraph E_Tab1 [答题结果Tab]
        direction LR
        F[显示：学生X的题目统计（共X题，错X题）] --> G[题目列表]
        G --> H[学生X的作答状态：✅/❌/半对/未作答]
        H --> I[题目详情及学生X答案]
    end
    subgraph E_Tab2 [学生提问Tab]
        direction LR
        J[显示：学生X的提问列表] --> K[提问详情]
    end
    E --> E_Tab1
    E --> E_Tab2
    C --> L[教师全面评估学生X的学情]
    D --> L
    E_Tab1 --> L
    E_Tab2 --> L
``` 

## 5. 页面与组件设计

*IMPORTANT AI (Chief Product Manager) INSTRUCTION: 此部分根据原始PRD中的图片和描述进行整理。图片均使用占位符表示。请产品经理和UI设计师根据占位符和描述进行具体设计。*

### 5.1 作业列表与核心信息展示

**设计理念**：采用"卡片式"设计，在作业列表中集中展示各项作业任务最核心的信息，方便教师快速概览和定位。

**页面主要构成**：
- 页面顶部：Tab切换（如"我布置的"、"我管理的"，对应不同权限范围的作业列表）。
- 筛选区域：支持按班级、学科、布置日期、作业状态（全部、未开始、进行中、已结束待批阅、已完成）进行筛选。
- 搜索框：支持按作业名称关键词搜索。
- 作业卡片列表区域。

**作业卡片设计 (对应原始PRD图片 `task_card_image_cn37bqEwdoU3tqxOubtcTkJgnmg`)**：

| 卡片元素             | 描述与说明                                                                 | 示例/备注                                    |
| -------------------- | -------------------------------------------------------------------------- | -------------------------------------------- |
| **作业类型与状态**   | 左上角标签，如"课程作业-已结束"、"测验-进行中"                               | 区分作业类型，高亮作业当前状态                 |
| **作业名称**         | 突出显示作业的标题                                                         | 如"第一单元综合练习"                         |
| **所属课程/教材**  | （可选）作业关联的课程或教材信息                                             |                                              |
| **班级信息**         | 作业所属的班级名称                                                         | 如"高一（3）班"                               |
| **核心数据指标**     | 重点展示：完成率、正确率                                                     | 如"完成率：80%"，"正确率：75%"              |
| **时间信息**         | 发布时间、截止时间                                                         | 清晰展示时间节点                             |
| **待关注人数**       | （可选，P1功能）若有需特别关注的学生（如严重逾期、极低正确率），可在此提示人数 | 如"3人需关注"                                |
| **操作按钮**         | "查看报告"按钮，引导进入详细作业报告页面                                     |                                              |
| **角标/提醒**      | （可选）如"新"、"已逾期"等角标                                              | 补充状态信息                                 |

![作业卡片示例](image_placeholder_task_card.png)

### 5.2 作业报告详情页 - 整体布局

**页面主要构成**：
- 页面顶部：面包屑导航，显示当前位置（如"作业管理 > 作业报告 > 第一单元综合练习"）。
- 报告头信息：显示作业名称、班级、时间等核心信息。
- Tab切换区域：固定在页面上部，支持在"学生报告"、"答题结果"、"学生提问"三个Tab间切换。
- 内容显示区域：根据当前选中的Tab展示对应的内容。

![作业报告整体布局](image_placeholder_report_layout.png)

### 5.3 "学生报告"Tab 页面设计

**此Tab的核心目标**：帮助教师快速了解班级整体学情，并能下钻到每个学生的具体表现，同时提供对"建议鼓励"和"建议关注"学生的快捷操作入口。

**页面主要构成**：
1.  **班级整体学情概览 (对应原始PRD图片 `overview_image_XzYob2iJvoMpHlxkHkCcLqAhnPe`)**
    - **班级进度**：展示当前班级、当前素材范围内，班级平均进度（同任务卡片：完成率）。
    - **班级正确率**：展示当前班级、当前素材范围内，班级正确率（同任务卡片：正确率）。
    - **可视化方式**：可以使用仪表盘、进度条或显著的数字展示。

    ![班级整体学情概览](image_placeholder_class_overview.png)

2.  **建议鼓励/关注模块 (对应原始PRD图片 `suggestion_image_MAaRb94hUoCrpmxikH3cgWDhnrd`)**
    - **"建议鼓励"区域**：
        - 标题："值得表扬的同学"。
        - 内容：以卡片或列表形式展示符合"建议鼓励"规则的学生，显示学生姓名、鼓励原因（如"连对王者：连对15题，打破班级记录！"）。
        - 操作："一键表扬"按钮（对所有推荐学生发送预设表扬信息）、单个学生表扬操作（如点赞图标）。
    - **"建议关注"区域**：
        - 标题："需要留意的同学"。
        - 内容：以卡片或列表形式展示符合"建议关注"规则的学生，显示学生姓名、关注原因（如"逾期未完成：已逾期3天"）。
        - 操作："一键提醒"按钮（对所有推荐学生发送预设提醒信息）、单个学生提醒操作。

    ![建议鼓励与关注模块](image_placeholder_suggestion_module.png)

3.  **任务学生列表 (对应原始PRD图片 `student_list_image_SAuJbQKlXoN8kQxEGincj8hYnUb` 和 `student_list_filter_image_DArwbmxyRoPorfxAE8hctDLHnWg`)**
    - **列表上方**：
        - 班级筛选：若教师管理多个班级，且报告支持合并查看，则可能需要班级筛选器。
        - 难度筛选："全部难度"、"偏易"、"适中"、"偏难"（基于学生作答难度分层）。
        - 关注类型筛选："全部关注类型"、"建议鼓励"、"建议关注"。
        - 导出数据按钮。
        - 搜索学生姓名框。
    - **列表表头**：学生信息（头像、姓名、学号）、分层（SABCD，P1）、完成进度、正确率、答题难度（基于学生作答难度分层，P1）、错题数/答题数、用时、操作（查看详情）。
    - **列表内容**：展示班级所有学生的数据，异常数据进行飘色展示（根据3.3节规则）。
    - **列表行操作**：
        - 点击学生姓名或"查看详情"按钮，跳转到该学生的个体作业报告页面。
        - （P1功能，可考虑）快速操作：如对"建议鼓励"学生直接点赞，对"建议关注"学生快速发送提醒。

    ![任务学生列表](image_placeholder_student_list.png)

### 5.4 "答题结果"Tab 页面设计

**此Tab的核心目标**：帮助教师从题目维度分析本次作业的整体答题情况，快速识别共性错题，并能深入查看单题的详细作答数据。

**页面主要构成**：
1.  **题目整体统计与筛选 (对应原始PRD图片 `question_stats_filter_image_TU0ObIzsVoPaurxpuAscLIglndh`)**
    - **统计信息**：页面顶部显示"共X道题，Y道共性错题"。
    - **筛选/操作区**：
        - "只看共性错题"开关。
        - "全部题型"下拉筛选（如：选择题、填空题、解答题等）。
        - "搜索题目关键词"输入框。
        - （P1功能）"按知识点/章节"筛选。

    ![题目整体统计与筛选](image_placeholder_question_stats_filter.png)

2.  **题目列表 (对应原始PRD图片 `question_list_image_MQSgbQTsio81kHxSfexcPqcFnBc`)**
    - **列表表头**：题号、题干预览（通常截取部分文字，并显示完整题型）、共性错题标签（若符合）、正确率、错误人数、作答人数、平均用时、操作（查看）。
    - **列表内容**：展示作业中的所有题目或筛选后的题目。默认可能按错误人数降序或题号升序排列。"共性错题"应有明显标识。
    - **列表行操作**：点击"查看"按钮，跳转到该题的"答题结果详情"页面。

    ![题目列表](image_placeholder_question_list.png)

3.  **单题作答详情页面 (通过点击题目列表中的"查看"进入，对应原始PRD图片 `single_question_detail_image_VkqfbxYdkouM5YxYnD5c1kP9n8b` 及选项分析相关图片)**
    - **页面布局**：
        - 左侧：题目信息区域。
        - 右侧：学生作答统计与名单区域。
        - （可选）顶部：题目导航（上一题/下一题）。
    - **左侧 - 题目信息**：
        - 题型、难度、所属知识点/章节。
        - 完整题干。
        - 选项信息（针对选择题）：清晰展示各选项内容。
        - 正确答案。
        - 详细解析（可展开/收起）。
        - "大屏讲解"按钮（将题目内容优化后投屏显示）。
    - **右侧 - 学生作答统计与名单**：
        - 整体统计：正确率、平均用时、作答人数。
        - 选项分析（针对选择题，对应原始PRD图片 `option_analysis_image_DAwHbfxUYoK4PfxkOhfc2hT5nRe`)：
            - 以柱状图或百分比形式展示各选项的选择人数及比例。
            - 正确选项进行高亮。
            - 点击每个选项，下方可联动展示选择该选项的学生名单。
        - 作答学生名单：
            - 可切换查看"全部学生"、"答对学生"、"答错学生"。
            - 列表显示学生姓名、所选答案（若为选择题）、提交时间、用时。
            - 点击学生姓名可跳转至该学生的个体作业报告中对应此题的作答情况（P1）。

    ![单题作答详情](image_placeholder_single_question_detail.png)

### 5.5 "学生提问"Tab 页面设计

**此Tab的核心目标**：集中展示学生在本次作业或关联课程中提出的问题，帮助教师了解学生的困惑点。

**页面主要构成 (对应原始PRD图片 `student_questions_tab_image_XwVsbC3kUoY2ARxU4VjcQROCnbf`)**：
1.  **内容结构与提问统计**：
    - 左侧或上部：以树状结构或列表形式展示与本次作业关联的课程内容结构（如按教材章节、课时、幻灯片组织）。
    - 每个内容节点旁显示该节点下的学生提问数量（如"1.2.1 复数的概念 (3条提问)"）。
    - 节点可展开/收起。
2.  **提问列表**：
    - 点击某个有提问的内容节点后，在右侧或下方区域展示该节点下的具体提问列表。
    - 列表表头：提问学生（头像、姓名）、提问内容预览、提问时间、状态（待回复/已回复，P1）。
    - 点击提问可查看完整提问内容及上下文（如截图、语音，P1），并进行回复操作（P1）。

    ![学生提问Tab](image_placeholder_student_questions_tab.png)

### 5.6 学生个体报告页面设计

**此页面的核心目标**：提供某个特定学生本次作业的完整、详细的学情数据，便于教师进行个性化辅导。

**页面主要构成 (对应原始PRD图片 `individual_student_report_overview_image_BMNfbxLkUo6oYpxuAhrcDWxUn4g` 及后续相关图片)**：
1.  **报告头信息**：
    - 清晰显示学生姓名、学号、所属班级。
    - 作业名称、作业起止时间。
2.  **学生作答概览**：
    - **完成情况**：如"已完成，用时35分钟"、"未完成，当前进度60%"、"逾期完成，逾期1天"。
    - **整体表现**：正确率、在班级中的排名（按正确率，P1）、与班级平均水平的对比（P1）。
    - **AI评价与建议**（P1，基于学生表现自动生成简要评语和建议）。

    ![学生个体报告概览](image_placeholder_individual_overview.png)

3.  **Tab切换区域**：支持"答题结果"、"学生提问"（若该生有提问）两个Tab。

4.  **"答题结果"Tab (个体视角)**
    - **统计信息**：显示该生"共X道题，答对Y道，答错Z道"。
    - **题目列表**：
        - 展示本次作业的所有题目。
        - 每题清晰标出该生的作答结果（正确✅、错误❌、部分正确、未作答）。
        - 显示该生答案、正确答案、该生用时。
        - 点击题目可展开查看完整题干、选项、解析（同班级报告的单题详情，但聚焦于此学生的作答）。

    ![学生个体报告答题结果](image_placeholder_individual_answers.png)

5.  **"学生提问"Tab (个体视角，若有)**
    - 列表展示该学生在本次作业或关联课程内容中提出的所有问题。
    - 显示提问内容、提问时间、教师回复状态（P1）。

    ![学生个体报告学生提问](image_placeholder_individual_questions.png)

## 6. 数据指标与统计看板

*IMPORTANT AI (Chief Product Manager) INSTRUCTION: 此部分定义了作业报告功能所需的核心数据指标及其在统计看板上的呈现方式。请数据产品经理和开发团队详细设计和实现。*

### 6.1 核心数据指标列表

| 序号 | 数据指标名称         | 定义                                                                 | 计算公式（或参见章节）                                  | 数据来源                                    | 备注/用途                                       |
| ---- | -------------------- | -------------------------------------------------------------------- | ------------------------------------------------------- | ------------------------------------------- | ----------------------------------------------- |
| 1    | **作业完成率**       | 在指定班级/学生群体中，完成指定作业的学生比例。                        | `(已完成作业学生数 / 总学生数) * 100%`                  | 学生作业提交状态、班级学生名单                | 衡量作业整体参与度、班级/个体进度。                 |
| 2    | **平均正确率 (班级)** | 班级内所有已作答学生，在指定作业中，题目答对的平均百分比。             | 参见 3.2 班级正确率                                   | 学生答题数据、题目信息                        | 反映班级整体对知识点的掌握程度。                  |
| 3    | **平均正确率 (学生)** |单个学生在指定作业中，题目答对的百分比。                               | `(学生答对题目数 / 学生已作答题目总数) * 100%`           | 该学生答题数据、题目信息                      | 评估个体学生学习效果。                            |
| 4    | **题目正确率**       | 针对某一特定题目，在作答了该题的学生中，首次答对该题的学生比例。         | 参见 3.1 题目正确率                                   | 学生对特定题目的首次作答数据                  | 识别题目难度、定位易错题、共性错题。              |
| 5    | **题目错误人数**     | 针对某一特定题目，在作答了该题的学生中，首次答错该题（至少一个空）的学生数。 | 直接计数                                                | 学生对特定题目的首次作答数据                  | 直观反映题目难度和学生掌握情况。                  |
| 6    | **平均用时 (作业)**  | 学生完成整个作业所花费的平均时间。                                     | `(所有完成作业学生的总用时 / 完成作业学生数)`           | 学生作业提交时间、开始作答时间（若有）        | 辅助判断作业整体难度、学生投入程度。              |
| 7    | **平均用时 (题目)**  | 学生解答某一特定题目所花费的平均时间。                                 | `(所有作答该题学生的总用时 / 作答该题学生数)`           | 学生对特定题目的作答时间记录                  | 辅助判断题目难度、学生解题效率。                  |
| 8    | **学生提问数**       | 学生在与作业相关的课程内容中提出的问题总数。                           | 直接计数                                                | 学生提问记录                                | 反映学生学习主动性、识别学生困惑点。              |
| 9    | **"建议鼓励"学生数** | 符合"建议鼓励"推荐规则的学生数量。                                     | 参见 3.4 "建议鼓励"的推荐类型与规则                     | 学生行为数据、历史数据、班级数据              | 快速定位表现优秀的学生。                          |
| 10   | **"建议关注"学生数** | 符合"建议关注"异常行为规则的学生数量。                                 | 参见 3.5 "建议关注"的异常行为规则                     | 学生行为数据、历史数据、班级数据              | 快速定位学习上需要帮助的学生。                    |
| 11   | **共性错题数量**     | 在一次作业中，错误率达到特定阈值（如>40%）的题目数量。                  | 根据题目正确率阈值判断后计数                              | 题目正确率数据                                | 帮助教师快速聚焦普遍性教学难点。                  |

### 6.2 统计看板设计（概念）

#### 6.2.1 教师个人看板（作业报告相关模块）

**目标用户**：学科教师、班主任
**核心目的**：快速了解所教班级/学科的作业完成情况、学情概况，及时发现问题。

**可能展示的图表与指标**：
1.  **近期作业概览 (列表/卡片形式)**
    -   指标：作业名称、班级、发布时间、截止时间、**作业完成率**、**平均正确率 (班级)**。
    -   交互：点击可跳转至对应作业的详细报告。
2.  **班级学情趋势 (针对某一选定班级和学科)**
    -   指标：**平均正确率 (班级)**、**作业完成率**随时间（如近7次作业/近1个月）的变化。
    -   图表类型：折线图。
    -   用途：追踪班级整体学习状态的动态变化。
3.  **待处理作业提醒**
    -   指标：显示"已逾期未完成学生数较多的作业"、"新发布待学生开始的作业"等。
    -   用途：提醒教师关注紧急或重要的作业情况。
4.  **"学生关注"概览 (针对某一选定班级)**
    -   指标：当前**"建议关注"学生数**、**"建议鼓励"学生数**。
    -   图表类型：数字卡片、饼图（展示两类学生占比）。
    -   用途：快速了解班级内需要重点关注的学生分布。

#### 6.2.2 作业详细报告页面内的统计图表

此部分已在"5. 页面与组件设计"中结合具体Tab描述，此处不再赘述，关键图表包括：
-   **学生报告Tab**：
    -   班级整体学情（进度、正确率）：仪表盘或数字卡片。
    -   建议鼓励/关注模块：列表或卡片展示，数量统计。
-   **答题结果Tab**：
    -   单题详情中的选项分析：柱状图（展示各选项选择人数及比例）。

#### 6.2.3 （P1）学科/年级组长看板（作业数据相关模块）

**目标用户**：学科组长、年级组长
**核心目的**：从更高维度监控学科/年级的整体学情，进行教学质量分析和管理。

**可能展示的图表与指标**：
1.  **多班级横向对比 (针对所管辖班级)**
    -   指标：各班级在同一次重要作业（如统一月考的作业部分）上的**平均正确率**、**作业完成率**。
    -   图表类型：柱状图或表格。
    -   用途：比较不同班级表现，发现教学差异。
2.  **共性错题分析 (针对学科/年级)**
    -   指标：在多次作业/重要作业中，出现频率最高的**共性错题**列表（题目名称、平均错误率）。
    -   图表类型：表格、词云图（展示高频出错知识点）。
    -   用途：识别学科/年级普遍存在的薄弱环节，指导集体备课和教研活动。
3.  **学情指标趋势 (针对学科/年级)**
    -   指标：所管辖范围内，整体的**平均正确率**、**作业完成率**随时间的变化。
    -   图表类型：折线图。
    -   用途：监控学科/年级整体教学质量的动态变化。

## 7. 权限管理

*IMPORTANT AI (Chief Product Manager) INSTRUCTION: 请根据实际组织架构和需求细化权限配置。*

**核心原则**：教师默认只能查看其任教班级和学科相关的作业报告。班主任可能拥有对其管理班级所有学科作业报告的查看权限。更高级别的角色（如学科组长、年级组长、校级管理员）拥有更广范围的数据查看和管理权限。

| 用户角色         | 功能模块/操作                            | 权限描述 (CRUD - 创建, 读取, 更新, 删除)                                 | 备注                                                                 |
| ---------------- | ---------------------------------------- | ----------------------------------------------------------------------- | -------------------------------------------------------------------- |
| **学科教师**     | 查看作业列表                             | 读取 (自己布置的、或任教班级本科目的作业)                               |                                                                      |
|                  | 查看作业报告 (班级整体)                  | 读取 (自己布置的、或任教班级本科目的作业报告)                           | 包括学生报告Tab、答题结果Tab、学生提问Tab                          |
|                  | 查看学生个体作业报告                     | 读取 (任教班级本科目下，所有学生的个体报告)                             |                                                                      |
|                  | 操作"一键表扬"/"一键提醒" (班级报告)   | 更新 (对任教班级本科目的学生群体)                                       | 触发消息发送等                                                       |
|                  | （P1）单个学生点赞/提醒等操作            | 更新 (对任教班级本科目的学生)                                           |                                                                      |
| **班主任**       | 查看作业列表                             | 读取 (自己班级的所有科目作业，或学校策略配置)                           | 权限范围可能大于普通学科教师                                           |
|                  | 查看作业报告 (班级整体)                  | 读取 (自己班级的所有科目作业报告)                                       |                                                                      |
|                  | 查看学生个体作业报告                     | 读取 (自己班级所有学生的个体报告，不限科目)                             |                                                                      |
|                  | 操作"一键表扬"/"一键提醒" (班级报告)   | 更新 (对自己班级的学生群体)                                             |                                                                      |
|                  | （P1）单个学生点赞/提醒等操作            | 更新 (对自己班级的学生)                                                 |                                                                      |
| **学科组长/主任** | 查看作业列表                             | 读取 (所管辖学科内所有教师布置的作业)                                   |                                                                      |
|                  | 查看作业报告 (班级整体)                  | 读取 (所管辖学科内所有班级的作业报告)                                   |                                                                      |
|                  | 查看学生个体作业报告                     | 读取 (所管辖学科内所有学生的个体报告)                                   |                                                                      |
|                  | 查看统计看板 (学科维度)                  | 读取 (学科相关的汇总数据、多班级对比、共性错题分析等)                   | 参见 6.2.3                                                           |
| **年级组长**     | 查看作业列表                             | 读取 (所管辖年级内所有班级、所有学科的作业)                             |                                                                      |
|                  | 查看作业报告 (班级整体)                  | 读取 (所管辖年级内所有班级的作业报告)                                   |                                                                      |
|                  | 查看学生个体作业报告                     | 读取 (所管辖年级内所有学生的个体报告)                                   |                                                                      |
|                  | 查看统计看板 (年级维度)                  | 读取 (年级相关的汇总数据、多班级/学科对比、共性错题分析等)              | 参见 6.2.3                                                           |
| **校级管理员**   | 查看作业列表                             | 读取 (全校所有作业)                                                     | 最高权限                                                             |
|                  | 查看作业报告 (班级整体)                  | 读取 (全校所有作业报告)                                                 |                                                                      |
|                  | 查看学生个体作业报告                     | 读取 (全校所有学生个体报告)                                             |                                                                      |
|                  | 查看统计看板 (校级/多维度)               | 读取 (全校范围的数据统计与分析)                                         |                                                                      |
|                  | （P1）配置管理                           | 创建, 更新, 删除 (如配置"共性错题"阈值、"建议关注"规则参数等)         | 系统层面的参数配置，需谨慎操作                                       |
| **学生**         | 查看自己的作业报告                       | 读取 (仅限查看自己已提交作业的作答情况、得分、教师评语/反馈等)          | 不在本PRD主要讨论范围，但作为关联方提及                               |

**注**：
- CRUD中，对于报告类功能，主要涉及"读取"(R)权限。
- "创建"(C)通常指布置作业，不在此作业报告PRD的核心范围，但与之关联。
- "更新"(U)可能涉及教师对学生的评价、标签、或发送提醒等操作。
- "删除"(D)在此PRD中较少涉及，除非指删除某些教师的批注或标记（需慎重）。

## 8. 非功能性需求

*IMPORTANT AI (Chief Product Manager) INSTRUCTION: 请系统架构师和运维团队关注并确保这些非功能性需求的实现。*

### 8.1 性能需求
1.  **页面加载速度**：
    -   作业报告列表页面（包含多张作业卡片）在常规网络条件下（如50Mbps带宽），首次加载时间应小于3秒，后续带缓存加载应小于1.5秒。
    -   作业详细报告页面（含学生列表、题目列表等）首次加载时间应小于4秒，Tab切换响应时间应小于1秒。
    -   学生个体报告页面加载时间应小于3秒。
2.  **数据查询与处理效率**：
    -   对于包含50名学生的班级、50道题的作业，学生报告Tab的数据统计（如班级平均正确率、学生列表生成）应在2秒内完成。
    -   答题结果Tab的题目列表（含正确率等统计）加载应在2秒内完成。单题详情查询应在1.5秒内完成。
    -   涉及大量数据计算的统计看板（如年级组长看板的多班级对比），应在5秒内返回结果，对于超大数据量可考虑异步加载或预计算机制。
3.  **并发用户数支持**：
    -   系统应能支持至少100名教师用户同时在线访问和使用作业报告功能，核心功能响应时间不应有明显下降。
    -   在高峰期（如月考结束后），系统应能承受至少300用户并发访问的压力，并保持服务稳定。

### 8.2 安全性需求
1.  **数据隔离与访问控制**：
    -   严格遵循第7节定义的权限管理规则，确保教师只能访问其授权范围内的数据（如自己班级、自己学科的学生数据和作业报告），防止数据越权访问。
    -   学生个人敏感信息（如具体成绩、评价）的传输和存储应进行加密处理，防止数据泄露。
2.  **防注入与攻击**：
    -   所有用户输入（如搜索框、参数）必须进行严格的合法性校验和过滤，防止SQL注入、XSS跨站脚本等常见Web攻击。
    -   对重要操作（如数据导出、参数配置）应有安全日志记录。
3.  **数据备份与恢复**：
    -   核心作业数据、学生作答数据、教师分析数据等应有定期备份机制（如每日全量备份，增量备份）。
    -   应具备数据恢复能力，在发生数据丢失或损坏时，能在规定时间内（如4小时内）将系统恢复到可用状态。

### 8.3 可用性需求
1.  **系统稳定性与可靠性**：
    -   核心作业报告功能应保证99.9%的可用性，即全年非计划停机时间不超过8.76小时。
    -   系统应具备容错能力，对于部分非核心组件的故障（如消息推送服务临时不可用），不应影响核心报告功能的查看和使用。
2.  **用户体验与易用性**：
    -   页面设计应简洁直观，符合教师用户的使用习惯，关键操作应有明确引导。
    -   系统应提供清晰的错误提示和用户反馈机制，帮助用户理解问题并进行下一步操作。
    -   兼容主流浏览器（如Chrome, Edge, Firefox, Safari的最新2个版本）和常见操作系统（Windows, macOS）。
3.  **帮助与支持**：
    -   （P1）提供在线用户手册或帮助文档，解释各项功能的使用方法和数据指标的含义。
    -   （P1）建立用户反馈渠道，方便教师用户报告问题或提出改进建议。

### 8.4 可扩展性与可维护性需求
1.  **系统架构**：
    -   系统应采用模块化、服务化的架构设计，便于未来新增功能模块（如更多类型的学情分析报告、AI智能辅导建议等）或对现有模块进行升级改造。
    -   数据模型设计应具有良好的前瞻性，能够适应未来可能增加的数据维度和分析需求。
2.  **代码质量与规范**：
    -   代码应遵循统一的编码规范，具备良好的可读性和可维护性，关键逻辑应有清晰的注释。
    -   核心算法和复杂业务逻辑应有单元测试覆盖，保证代码质量和重构的安全性。
3.  **部署与监控**：
    -   系统应支持自动化部署和持续集成（CI/CD），提高版本迭代效率。
    -   应建立完善的系统监控体系，对服务器资源使用情况、核心服务运行状态、关键接口性能等进行实时监控和告警，便于及时发现和处理问题。

## 9. 验收标准

*IMPORTANT AI (Chief Product Manager) INSTRUCTION: 请QA团队根据以下标准制定详细的测试用例，并在测试环境中严格验证。*

### 9.1 P0核心功能验收标准

#### 9.1.1 作业列表与核心信息展示 (P0)
1.  **标准1.1.1**：教师登录后，能查看到其权限范围内的作业列表（如"我布置的"或"我管理的"）。
2.  **标准1.1.2**：作业列表以卡片形式展示，每张卡片必须清晰显示作业名称、班级、发布时间、截止时间、完成率、正确率。
3.  **标准1.1.3**：点击作业卡片上的"查看报告"按钮，能正确跳转到对应作业的详细报告页面。
4.  **标准1.1.4**：支持按班级、学科（若适用）、作业状态（全部、未开始、进行中、已结束待批阅、已完成）筛选作业列表，筛选结果正确。
5.  **标准1.1.5**：支持按作业名称关键词搜索作业列表，搜索结果正确。

#### 9.1.2 学生报告Tab (P0)
1.  **标准1.2.1**：进入作业详细报告，默认显示"学生报告"Tab内容。
2.  **标准1.2.2**：页面顶部正确显示当前作业的班级平均进度和班级平均正确率，数值与"3. 计算规则与公式"中定义一致。
3.  **标准1.2.3**："建议鼓励"模块能根据"3.4 建议鼓励的推荐类型与规则"正确识别并展示学生列表（至少覆盖"连对王者"、"自我突破"规则的验证），显示学生姓名和鼓励原因。
4.  **标准1.2.4**："建议关注"模块能根据"3.5 建议关注的异常行为规则"正确识别并展示学生列表（至少覆盖"未开始学习"、"逾期未完成"规则的验证），显示学生姓名和关注原因。
5.  **标准1.2.5**：任务学生列表能正确展示班级所有学生，并列出学生信息、完成进度、正确率、错题数/答题数、用时。数据与学生实际作答情况一致。
6.  **标准1.2.6**：任务学生列表中的异常数据（如正确率远低于班级平均）能根据"3.3 数据异常与高亮规则"进行飘色展示。
7.  **标准1.2.7**：在任务学生列表中点击学生姓名或"查看详情"，能正确跳转到该学生的个体作业报告页面。
8.  **标准1.2.8**：支持按学生姓名搜索学生列表，搜索结果正确。

#### 9.1.3 答题结果Tab (P0)
1.  **标准1.3.1**：切换到"答题结果"Tab，页面顶部正确显示"共X道题，Y道共性错题"的统计，Y根据设定的共性错题阈值计算正确。
2.  **标准1.3.2**：题目列表正确展示作业中的所有题目，包含题干预览、正确率、错误人数、作答人数。数据与"3. 计算规则与公式"定义一致。
3.  **标准1.3.3**："只看共性错题"开关功能正常，打开后只显示错误率达到阈值的题目。
4.  **标准1.3.4**：支持按题型筛选题目列表，筛选结果正确。
5.  **标准1.3.5**：支持按题目关键词搜索题目列表，搜索结果正确。
6.  **标准1.3.6**：点击题目列表中的"查看"按钮，能正确跳转到该题的"答题结果详情"页面。
7.  **标准1.3.7 (单题详情页)**：页面正确展示题目的完整信息（题干、选项、答案、解析、题型、难度）。
8.  **标准1.3.8 (单题详情页)**：页面正确展示该题的整体作答统计（正答率、各选项选择人数分布，针对选择题），数据准确。
9.  **标准1.3.9 (单题详情页)**：页面能正确展示选择了各个选项（特别是错误选项）的学生名单。

#### 9.1.4 学生提问Tab (P0)
1.  **标准1.4.1**：切换到"学生提问"Tab，能按课程内容结构展示提问统计（各节点提问数）。
2.  **标准1.4.2**：点击有提问的内容节点，能正确展示该节点下的学生提问列表（提问学生、提问内容、提问时间）。

#### 9.1.5 学生个体报告页面 (P0)
1.  **标准1.5.1**：从班级报告跳转或直接访问，能正确打开特定学生的个体作业报告页面，显示学生和作业信息。
2.  **标准1.5.2**：页面正确展示该生的作答概览（完成情况、用时等）。
3.  **标准1.5.3 (答题结果Tab - 个体)**：正确显示该生的题目统计（共X题，错X题）。
4.  **标准1.5.4 (答题结果Tab - 个体)**：题目列表正确展示所有题目，并清晰标出该生每道题的作答结果（对/错/未作答）、学生答案、正确答案。
5.  **标准1.5.5 (学生提问Tab - 个体)**：若该生有提问，能正确展示其提出的问题列表。

### 9.2 P1核心功能验收标准

#### 9.2.1 学生报告Tab - 进阶功能 (P1)
1.  **标准2.1.1**："一键表扬"功能：点击后，能对"建议鼓励"列表中的所有学生发送预设的表扬消息（或记录表扬行为）。
2.  **标准2.1.2**："一键提醒"功能：点击后，能对"建议关注"列表中的所有学生发送预设的提醒消息（或记录提醒行为）。
3.  **标准2.1.3**：任务学生列表的分层标签（SABCD）能根据学生历史表现或配置规则正确显示。
4.  **标准2.1.4**：任务学生列表的答题难度（基于学生作答难度分层）能正确计算并显示。

#### 9.2.2 答题结果Tab - 进阶功能 (P1)
1.  **标准2.2.1**：单题详情页的"大屏讲解"按钮功能：点击后能将题目内容适配大屏展示的模式（具体UI和交互符合设计）。

#### 9.2.3 其他P1功能点 (若在"2.功能范围"中明确定义为P1)
*（根据2.2节和2.3节中标记为P1的功能点，补充具体的验收标准）*
1.  **标准2.3.1 (示例：数据导出)**：若学生报告Tab或答题结果Tab提供数据导出功能，导出的文件格式正确（如CSV/Excel），数据内容与页面显示一致且完整。
2.  **标准2.3.2 (示例：教师评价/批注)**：若支持教师在报告中添加评价或批注，评价内容能正确保存和显示。

## 10. 上线计划与迭代

*IMPORTANT AI (Chief Product Manager) INSTRUCTION: 请项目经理和各团队负责人协同确保上线计划的顺利执行。*

### 10.1 P0核心功能上线计划 (MVP)
-   **目标上线时间**：**6月份** (具体日期待定)
-   **核心目标**：确保产品在预定6月上线的版本中能够顺利跑通教师端作业报告的核心业务MVP（最小可行产品）流程。P0功能范围中定义的所有功能点必须完成开发、充分测试并通过验收。
-   **上线前置条件**：
    -   所有P0功能开发完毕，并完成单元测试、集成测试。
    -   QA团队完成P0功能的全面测试，所有严重及主要缺陷已修复。
    -   相关基础设施（服务器、数据库、网络等）准备就绪并经过测试。
    -   基础数据（如学生、班级、课程、题目）已按需准备或可同步。
    -   （若涉及）用户培训材料初稿完成。
-   **上线后关注**：
    -   密切监控系统运行状态，特别是性能和稳定性。
    -   收集首批用户（如种子教师）的使用反馈，快速响应和处理关键问题。

### 10.2 P1功能迭代计划
-   **目标上线窗口**：**9月份** (在P0版本稳定运行基础上，具体日期待定)
-   **核心目标**：在MVP版本基础上，扩展和增强作业报告的功能，提升教师用户的使用体验和数据分析的深度。
-   **P1迭代内容**：主要包括"2. 功能范围"中标记为P1的功能点，例如：
    -   学生报告Tab中的"一键表扬"、"一键提醒"等批量操作功能。
    -   学生列表中的"分层标签"、"答题难度"等精细化数据展示。
    -   答题结果Tab单题详情中的"大屏讲解"功能。
    -   （若适用）数据导出功能。
    -   （若适用）教师对学生的评价/批注功能。
    -   进一步优化"建议鼓励"和"建议关注"的规则和呈现方式。
-   **迭代方式**：采用敏捷迭代，成熟一个上线一个，或按主题分批上线。

### 10.3 后续迭代方向展望（P2及以后）

在P0和P1功能逐步完善和稳定后，后续可从以下方向考虑进一步迭代和优化：

1.  **智能化分析与建议增强**：
    -   **更智能的学情诊断**：引入更多AI算法，对学生的学习行为、知识点掌握情况进行更深层次的分析和画像，提供更精准的个性化学习建议给教师参考。
    -   **教学策略推荐**：基于班级整体学情和共性问题，向教师推荐针对性的教学调整策略或教学资源。
    -   **自动化报告解读**：尝试用自然语言生成作业报告的摘要和关键发现，帮助教师快速掌握核心信息。
2.  **多维度数据关联与下钻**：
    -   **跨作业学情追踪**：支持查看学生在多次作业中的表现变化趋势，形成更长期的学情发展轨迹。
    -   **知识图谱关联**：将题目与知识图谱深度结合，分析学生在知识点掌握上的关联性和薄弱路径。
    -   **与其他教学环节数据打通**：如与课堂表现数据、预习数据等多维度数据融合分析，形成更全面的学生评价。
3.  **互动与协作功能深化**：
    -   **教师间协作**：支持学科组长/备课组长基于作业报告数据，组织在线教研和集体备课。
    -   **家校互动**：在符合隐私政策的前提下，适度向家长开放学生学情报告的特定视图，促进家校共育。
    -   **生生互动**： （远期）基于错题和疑问，引导学生间的互助答疑和讨论（需良好设计和管理）。
4.  **用户体验持续优化**：
    -   **个性化配置**：允许教师自定义报告关注的指标、提醒的阈值等。
    -   **移动端适配**：优化作业报告在移动设备（手机、平板）上的查看体验。
    -   **性能与易用性**：根据用户反馈和技术发展，持续优化系统性能和操作便捷性。
5.  **更丰富的报告类型**：
    -   **测验/考试报告**：针对不同类型的评测任务，设计专门的报告模板和分析维度（如大屏模式）。
    -   **学习过程性评价报告**：综合多次作业、测验、课堂互动等数据，形成阶段性的学生综合评价报告。

## 11. FAQ (常见问题解答)

*IMPORTANT AI (Chief Product Manager) INSTRUCTION: 此FAQ列表旨在提前解答用户和内部团队的疑问，请持续更新。*

1.  **Q：作业报告中的"班级正确率"和任务卡片上的"正确率"是同一个东西吗？**
    A：是的，在"学生报告"Tab中展示的"班级正确率"其计算口径与作业列表任务卡片上展示的"正确率"指标保持一致，都是反映当前班级、当前素材范围内的整体平均正确率。详细计算规则请参考"3. 计算规则与公式"部分。

2.  **Q："共性错题"是如何定义的？这个阈值可以调整吗？**
    A：目前版本中，"共性错题"通常指错误率（即100% - 正确率）达到一定阈值（例如40%以上）的题目。P0版本的阈值可能由系统预设。未来P1或P2迭代中，可以考虑支持校级管理员或学科组长在一定范围内自定义此阈值，以适应不同学校或学科的教学管理需求。

3.  **Q：如果学生对同一道题目作答了多次（例如在不同任务中遇到，或作业允许重复修改提交），报告中会如何统计这道题的作答数据？**
    A：作业报告中的题目作答数据，特别是正确率、错误人数等，优先采用学生对该题目在当前任务下的"首次作答"数据进行统计。这是为了更准确地反映学生初次遇到问题时的真实掌握情况。详细原则请参考"3.1 题目答题数据统计规则"。

4.  **Q："建议鼓励"和"建议关注"的学生是如何筛选出来的？这些规则是固定的吗？**
    A：这些学生是系统根据一系列预设的规则和算法自动筛选的，具体规则在"3.4 建议鼓励的推荐类型与规则"和"3.5 建议关注的异常行为规则"中有详细定义。例如，"连对王者"是基于连续答对题数，"逾期未完成"是基于作业提交截止时间等。P0版本的规则是相对固定的，未来迭代（P1及以后）会考虑引入更多维度的规则，并可能支持一定程度的自定义或权重调整。

5.  **Q：教师能否导出作业报告中的数据，例如学生成绩列表或错题分析结果？**
    A：P0核心版本可能不包含或仅提供基础的数据导出。数据导出功能（如导出学生成绩为CSV/Excel，导出题目分析结果等）通常规划在P1或后续迭代中实现。具体请参考"2. 功能范围"和"10. 上线计划与迭代"中关于P1功能的描述。

6.  **Q：作业报告支持查看多久以前的作业数据？历史数据会一直保留吗？**
    A：系统会保留教师教学周期内的作业历史数据。具体的历史数据保留策略（如保留最近几个学期或几年）将根据服务器存储容量和系统性能考量来制定，并会提前通知用户。教师通常可以方便地查询和回顾近期及本学期的作业报告。

7.  **Q：如果我认为某个学生的"建议关注"标签不准确，或者某个"共性错题"的判断不符合我的教学经验，我该怎么办？**
    A：系统提供的"建议关注"和"共性错题"是基于数据分析的辅助判断，旨在帮助教师快速发现潜在问题。教师应结合自己的专业判断和对学生的实际了解来综合评估。我们欢迎教师用户通过反馈渠道（P1规划）提供宝贵意见，帮助我们持续优化算法和规则的准确性与实用性。

8.  **Q：开发团队在实现数据统计和图表展示时，如果遇到前端性能瓶颈或后端数据处理过慢，有什么建议？**
    A：
    -   **前端优化**：对于复杂图表和大量数据列表，考虑使用虚拟滚动、懒加载、分页加载等技术。图表库选择轻量且高性能的，避免不必要的DOM操作和重绘。
    -   **后端优化**：对于耗时的数据统计和查询，优先优化SQL语句和索引；考虑使用缓存机制（如Redis）缓存常用统计结果和热点数据；对于非常复杂或大数据量的计算，可以设计异步任务处理机制，或进行数据预计算和预聚合，存入专门的数据集市或OLAP分析库中，供前端快速查询。
    -   **API设计**：接口设计应遵循轻量化原则，避免一次性返回过多不必要的数据。按需加载，分页获取。
    -   具体性能要求请参考"8.1 性能需求"。

## 12. 附录与参考资料

*IMPORTANT AI (Chief Product Manager) INSTRUCTION: 请确保所有链接的有效性，并及时更新。*

1.  **原始需求输入文档**：
    -   `documents/raw-docs/教师端_1期_作业报告_analyzed.md` (本PRD最核心的原始输入，包含了详细的功能点描述、业务流程和页面截图)
2.  **PRD生成规范与指令文档**：
    -   `documents/prompt/s1-gen-ai-prd.md` (指导AI助手生成本PRD的结构、内容和格式要求的规范文档)
3.  **PRD模板文档**：
    -   `documents/templates/ai-prd-v2.2.md` (AI助手生成本PRD时所依据的基础模板)
4.  **相关数据口径说明 (原始PRD中提及的外部链接)**：
    -   [口径说明 (飞书文档)](https://wcng60ba718p.feishu.cn/wiki/DXKawJOESiypQ3kt3vbc64DgnTc?sheet=e3d974) (注意：此链接为原始PRD中提供，AI助手无法直接访问，具体数据规则以本PRD"3. 计算规则与公式"中已解析和提取的内容为准。建议产品经理和开发团队核对原始链接以获取最全面的信息。)

*(后续可根据项目进展，补充其他相关参考资料，如：详细的UI设计稿链接、技术架构设计文档链接、关键决策的会议纪要链接等。)*

---
*文档结束*

const { createServer } = require('http')
const { parse } = require('url')
const next = require('next')

const dev = process.env.NODE_ENV !== 'production'
const hostname = '0.0.0.0'
const port = parseInt(process.env.PORT || '80', 10)

console.log(`🚀 启动简化服务器...`)
console.log(`📍 主机: ${hostname}`)
console.log(`🔌 端口: ${port}`)
console.log(`🌍 环境: ${dev ? '开发' : '生产'}`)

// 创建 Next.js 应用实例
const app = next({ dev })
const handle = app.getRequestHandler()

app.prepare().then(() => {
  createServer((req, res) => {
    try {
      // 解析请求 URL
      const parsedUrl = parse(req.url, true)
      
      // 打印请求日志
      console.log(`📥 ${req.method} ${req.url}`)
      
      // 处理请求
      handle(req, res, parsedUrl)
    } catch (err) {
      console.error('❌ 处理请求时出错:', err)
      res.statusCode = 500
      res.end('internal server error')
    }
  }).listen(port, hostname, (err) => {
    if (err) throw err
    console.log(`✅ 服务器启动成功！`)
    console.log(`🌐 访问地址: http://${hostname}:${port}`)
    console.log(`🌐 外部访问: http://ai01.local.xiaoluxue.cn`)
  })
})
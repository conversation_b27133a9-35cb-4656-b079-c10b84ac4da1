# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** 巩固练习相关策略产品需求文档
- **PRD版本号：** V1.0
- **提取日期：** 2025-05-25

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 巩固练习记录 | 用户完成AI课程学习后进行的知识点巩固练习活动记录 | 2.1, 4.场景1 |
| 题目推荐记录 | 系统根据用户掌握度和题目难度系数推荐题目的记录 | 2.1, 4.场景2 |
| 专注度评分记录 | 通过检测用户答题行为判断是否认真答题的评分记录 | 2.2, 4.场景3 |
| 再练一次记录 | 针对掌握度不足的知识点提供再次练习机会的记录 | 2.1, 4.场景4 |
| 答题行为记录 | 用户在巩固练习中的具体答题行为和结果记录 | 4.场景1-4, 5.业务流程图 |
| 熔断记录 | 用户连续做错题目或专注度过低时触发练习熔断的记录 | 2.2, 4.场景3 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`巩固练习记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 练习记录ID | 巩固练习记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 4.场景1 |
| 用户ID | 进行巩固练习的学生用户标识 | 整数 | 非空, 外键关联用户中心 |  | 外键 | 4.场景1 |
| 知识点ID | 巩固练习对应的知识点标识 | 整数 | 非空, 外键关联内容平台 |  | 外键 | 4.场景1 |
| 当前掌握度 | 用户对该知识点的当前掌握程度 | 数值型(小数) | [0,1]区间 |  |  | 3.计算规则, 4.场景1 |
| 目标掌握度 | 该知识点对应的目标掌握度 | 数值型(小数) | [0,1]区间 |  |  | 3.计算规则 |
| 预估题目数量 | 根据公式计算的预估答题量 | 整数 | [8,14]范围，下限为题组数量*2，上限为15 |  |  | 3.计算规则 |
| 练习状态 | 巩固练习的当前状态 | 整数 | 0进行中，1正常完成，2熔断结束 | 0 |  | 4.场景1-3 |
| 开始时间 | 巩固练习开始时间 | 时间戳 | 非空 |  |  | 4.场景1 |
| 结束时间 | 巩固练习结束时间 | 时间戳 | 可空 |  |  | 4.场景1-3 |

**实体：`题目推荐记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 推荐记录ID | 题目推荐记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 4.场景2 |
| 练习记录ID | 关联的巩固练习记录 | 整数/字符串 | 非空, 外键 |  | 外键 | 4.场景2 |
| 题目ID | 推荐的题目标识 | 整数 | 非空, 外键关联内容平台 |  | 外键 | 4.场景2 |
| 题组ID | 题目所属的题组标识 | 整数 | 非空, 外键关联内容平台 |  | 外键 | 4.场景2 |
| 题目难度系数 | 题目的难度系数β | 数值型(小数) | [0,1]区间 |  |  | 3.计算规则 |
| 是否必做题 | 标识是否为老师推荐的必做题 | 布尔型 | true/false | false |  | 4.场景1, 5.题目推荐策略流程 |
| 预期掌握度增量 | 该题目对用户的预期掌握度增量 | 数值型(小数) | 可为负值 |  |  | 3.计算规则 |
| 推荐时间 | 题目推荐的时间 | 时间戳 | 非空 |  |  | 4.场景2 |
| 推荐原因 | 推荐该题目的原因 | 字符串 | 如"必做题优先"、"连续答对提升难度"等 |  |  | 4.场景2 |

**实体：`专注度评分记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 专注度记录ID | 专注度评分记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 4.场景3 |
| 练习记录ID | 关联的巩固练习记录 | 整数/字符串 | 非空, 外键 |  | 外键 | 4.场景3 |
| 当前专注度分值 | 用户当前的专注度分值 | 整数 | [0,10]区间 | 10 |  | 4.场景3 |
| 异常行为类型 | 导致专注度扣分的异常行为类型 | 字符串 | "快速作答且错误"、"连续做错3题"等 |  |  | 4.场景3 |
| 触发提醒级别 | 触发的提醒或熔断级别 | 整数 | 0无提醒，1第一次提醒，2第二次提醒，3熔断 | 0 |  | 4.场景3 |
| 记录时间 | 专注度变化的记录时间 | 时间戳 | 非空 |  |  | 4.场景3 |

**实体：`再练一次记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 再练记录ID | 再练一次记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 4.场景4 |
| 原练习记录ID | 关联的原始巩固练习记录 | 整数/字符串 | 非空, 外键 |  | 外键 | 4.场景4 |
| 用户ID | 进行再练一次的学生用户标识 | 整数 | 非空, 外键关联用户中心 |  | 外键 | 4.场景4 |
| 触发条件掌握度 | 触发再练一次时的用户掌握度 | 数值型(小数) | <目标掌握度的70% |  |  | 4.场景4 |
| 触发条件正确率 | 触发再练一次时的正确率 | 数值型(小数) | <70% |  |  | 4.场景4 |
| 可用题目数量 | 支持再练一次的可用题目数量 | 整数 | ≥3题 |  |  | 4.场景4 |
| 再练状态 | 再练一次的状态 | 整数 | 0进行中，1完成 | 0 |  | 4.场景4 |
| 开始时间 | 再练一次开始时间 | 时间戳 | 非空 |  |  | 4.场景4 |
| 结束时间 | 再练一次结束时间 | 时间戳 | 可空 |  |  | 4.场景4 |

**实体：`答题行为记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 答题记录ID | 答题行为记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 4.场景1-4 |
| 练习记录ID | 关联的练习记录(巩固练习或再练一次) | 整数/字符串 | 非空, 外键 |  | 外键 | 4.场景1-4 |
| 题目ID | 作答的题目标识 | 整数 | 非空, 外键关联内容平台 |  | 外键 | 4.场景1-4 |
| 用户答案 | 用户提交的答案 | 文本 | 非空 |  |  | 4.场景1-4 |
| 是否正确 | 答题是否正确 | 布尔型 | true/false |  |  | 4.场景1-4 |
| 作答时长 | 用户作答该题的时长(秒) | 整数 | ≥0 |  |  | 4.场景3 |
| 答题前掌握度 | 答题前用户对该知识点的掌握度 | 数值型(小数) | [0,1]区间 |  |  | 3.计算规则 |
| 答题后掌握度 | 答题后更新的用户掌握度 | 数值型(小数) | [0,1]区间 |  |  | 3.计算规则 |
| 掌握度增量 | 本次答题的掌握度变化量 | 数值型(小数) | 可为负值 |  |  | 3.计算规则 |
| 答题时间 | 答题提交的时间 | 时间戳 | 非空 |  |  | 4.场景1-4 |

**实体：`熔断记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 熔断记录ID | 熔断记录的唯一标识符 | 整数/字符串 | 唯一, 非空 |  | 主键 | 4.场景3 |
| 练习记录ID | 关联的巩固练习记录 | 整数/字符串 | 非空, 外键 |  | 外键 | 4.场景3 |
| 熔断原因 | 触发熔断的具体原因 | 字符串 | "专注度过低"、"连续做错5题且无更低难度题目"等 |  |  | 2.2, 4.场景3 |
| 熔断时专注度分值 | 熔断时的专注度分值 | 整数 | [0,10]区间 |  |  | 4.场景3 |
| 连续错题数量 | 熔断时的连续错题数量 | 整数 | ≥0 |  |  | 2.2 |
| 熔断时间 | 触发熔断的时间 | 时间戳 | 非空 |  |  | 4.场景3 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 (例如：用户 '拥有一个' 学校档案) | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 巩固练习记录包含多个题目推荐记录 | 巩固练习记录 | 题目推荐记录 | 1:N | 题目推荐记录.练习记录ID -> 巩固练习记录.练习记录ID | 4.场景1-2 |
| 巩固练习记录包含多个专注度评分记录 | 巩固练习记录 | 专注度评分记录 | 1:N | 专注度评分记录.练习记录ID -> 巩固练习记录.练习记录ID | 4.场景1,3 |
| 巩固练习记录可能触发再练一次记录 | 巩固练习记录 | 再练一次记录 | 1:1 | 再练一次记录.原练习记录ID -> 巩固练习记录.练习记录ID | 4.场景4 |
| 练习记录包含多个答题行为记录 | 巩固练习记录/再练一次记录 | 答题行为记录 | 1:N | 答题行为记录.练习记录ID -> 练习记录.记录ID | 4.场景1-4 |
| 巩固练习记录可能产生熔断记录 | 巩固练习记录 | 熔断记录 | 1:1 | 熔断记录.练习记录ID -> 巩固练习记录.练习记录ID | 4.场景3 |
| 用户参与多个巩固练习 | 用户(用户中心) | 巩固练习记录 | 1:N | 巩固练习记录.用户ID -> 用户.用户ID | 4.场景1 |
| 知识点对应多个巩固练习 | 知识点(内容平台) | 巩固练习记录 | 1:N | 巩固练习记录.知识点ID -> 知识点.知识点ID | 4.场景1 |
| 题目被多次推荐 | 题目(内容平台) | 题目推荐记录 | 1:N | 题目推荐记录.题目ID -> 题目.题目ID | 4.场景2 |
| 题组包含多个题目推荐 | 题组(内容平台) | 题目推荐记录 | 1:N | 题目推荐记录.题组ID -> 题组.题组ID | 4.场景2 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 预估题目数量计算 | 难度系数, 考频系数, 目标掌握度θ_tar, 当前掌握度θ_cur, 经验调节系数α(默认0.6), 题组数量 | 预估题目数量 | 3.计算规则与公式 |
| 预期掌握度增量计算 | 学生掌握度θ, 题目难度系数β | 预期掌握度增量 | 3.计算规则与公式 |
| 答对掌握度增量计算 | 题目难度系数β, 用户当前掌握度θ | 答对掌握度增量 | 3.计算规则与公式 |
| 答错掌握度增量计算 | 题目难度系数β, 用户当前掌握度θ | 答错掌握度增量 | 3.计算规则与公式 |
| 答对概率计算 | 学生掌握度θ, 题目难度系数β | 答对概率P(答对) | 3.计算规则与公式 |
| 动态调整系数计算 | 目标掌握度θ_tar, 当前掌握度θ_cur, 经验调节系数α | 动态调整系数A | 3.计算规则与公式 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 巩固练习记录.练习状态 | 用户开始巩固练习、正常完成练习、触发熔断 | 实时/每次状态变化 | 4.场景1-3 |
| 答题行为记录.答题后掌握度 | 用户每次完成答题之后 | 实时/每次答题 | 4.场景1-4 |
| 专注度评分记录.当前专注度分值 | 用户出现异常答题行为时(进入题目1s内作答且回答错误、连续做错3题) | 实时/每次异常行为 | 4.场景3 |
| 题目推荐记录 | 用户完成当前题目作答后，系统推荐下一题时 | 实时/每次推荐 | 4.场景2 |
| 熔断记录 | 专注度分值降至3分或连续做错5题且无更低难度题目时 | 实时/触发熔断时 | 4.场景3 |
| 再练一次记录 | 用户掌握度<目标掌握度的70%且正确率<70%，且有≥3题可用题目时 | 每次巩固练习完成后判断 | 4.场景4 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 用户ID | 用户中心服务提供，用户登录认证后获取 | 4.场景1 |
| 知识点ID | 内容平台服务提供，与AI课程关联 | 4.场景1 |
| 题目ID | 内容平台服务提供，包含题目信息、难度系数等 | 4.场景2 |
| 题组ID | 内容平台服务提供，题目按知识点分组 | 4.场景2 |
| 题目难度系数 | 内容平台服务提供，题目的预设难度系数β | 3.计算规则 |
| 考频系数 | 内容平台服务提供，知识点的考试频率系数 | 3.计算规则 |
| 是否必做题 | 教师服务提供，老师在选题时打上推荐标签 | 4.场景1, 11.1术语表 |
| 用户答案 | 用户在前端界面输入提交 | 4.场景1-4 |
| 作答时长 | 前端系统记录，从题目展示到提交答案的时间差 | 4.场景3 |

---

## 7. 当前版本明确排除的数据需求 (例如PRD中的V1.0版本)

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 (源自PRD"非本期功能"等章节) | PRD中说明的排除原因 (若有) | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 模型驱动的推荐算法数据：如IRT、知识追踪等高级算法推荐机制相关的数据结构 | V1.0不考虑，为后续算法推荐提供结构化数据积累 | 2.3 |
| 个性化学习路径数据：根据长期学习数据定制的完整学习路径规划相关数据 | V1.0不考虑 | 2.3 |
| 社交化学习功能数据：与同班或同水平学生的练习对比和互动相关数据 | V1.0不考虑 | 2.3 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别巩固练习、题目推荐、专注度评分、再练一次、答题行为、熔断等核心实体 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏（例如，本示例中的"题目信息"、"课程信息"）？                                                         | ✅ | 题目、题组、知识点等实体已在公共服务中定义，只保留关联关系 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已提取计算公式所需的所有输入属性和状态属性 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | 属性描述与PRD原文保持一致 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确各实体间的关联关系和基数 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 计算依赖关系已在第4部分详细说明 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 已列出预估题目数量、掌握度增量等所有计算公式的输入项 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 已记录实时更新的触发条件和数据来源 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 已排除IRT、知识追踪、个性化学习路径、社交化学习等V1.0不考虑的功能 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 与PRD术语表保持一致 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 所有提取项都标注了PRD来源章节 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 所有信息均来源于PRD原文 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 经验调节系数α默认值0.6，可能需要后续配置化管理 |

--- 
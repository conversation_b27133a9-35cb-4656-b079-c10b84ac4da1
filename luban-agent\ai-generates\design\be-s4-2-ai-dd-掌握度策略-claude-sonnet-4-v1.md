# 数据库设计文档 - 掌握度策略系统 - V1.0

**模板版本**: v1.3
**最后更新日期**: 2025-06-05
**设计负责人**: AI数据库设计工程师
**评审人**: 待填写
**状态**: 草稿

## 📋 输出格式总体要求
* **IMPORTANT AI:** 本模板定义了所有输出的标准化格式，必须严格遵循：
  - 所有表格都有固定的列结构，不得随意增减
  - ER图必须使用Mermaid语法，按照标准格式绘制
  - CRUD分析必须使用标准7列表格格式
  - 检查清单必须逐项填写，使用✅/❌/☐/N/A标记
  - 所有DDL代码必须包含完整中文注释

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-06-05 | AI数据库设计工程师 | 初始草稿     |

## 1. 项目概述与设计目标

### 1.1 项目背景与范围
掌握度策略系统是一个智能化学习评估平台，核心目标是通过精准的学科能力评估、实时的知识点掌握度计算和个性化的学习内容推荐，提升学生学习效率和个性化学习体验。本次数据库设计涵盖用户分层管理、知识点掌握度计算、外化掌握度展示、答题记录分析等核心功能的数据存储方案。

### 1.2 设计目标回顾
* 提供完整的数据库创建语句（如果适用）。
* 提供完整的、符合规范的表创建语句（DDL）。
* 清晰描述表间关系，并提供 ER 图。
* 详细说明核心用户场景/用户故事所涉及的表以及其 CRUD 操作方式。
* 阐述关键表的索引策略及其设计原因。
* 对核心表进行数据量预估和增长趋势分析。
* 明确数据生命周期管理/归档策略（如果适用）。
* 记录特殊设计考量与权衡。
* 总结设计如何体现对应数据库（PostgreSQL/ClickHouse）的关键规范。

## 2. 数据库环境与总体说明

### 2.1 目标数据库类型
* PostgreSQL 17（主存储）- 用于事务性数据和复杂关系查询
* ClickHouse 23.8.16.16（分析存储）- 用于大规模答题记录分析和统计

### 2.2 数据库创建语句 (如果适用)
* **PostgreSQL 示例**:
  ```sql
  -- CREATE DATABASE mastery_strategy_db
  -- WITH
  -- OWNER = mastery_user
  -- ENCODING = 'UTF8'
  -- LC_COLLATE = 'zh_CN.UTF-8'
  -- LC_CTYPE = 'zh_CN.UTF-8'
  -- TABLESPACE = pg_default
  -- CONNECTION LIMIT = -1
  -- TEMPLATE = template0;
  -- COMMENT ON DATABASE mastery_strategy_db IS '掌握度策略系统数据库';
  ```
* **ClickHouse 示例**:
  ```sql
  -- CREATE DATABASE IF NOT EXISTS mastery_analytics_db
  -- ENGINE = Atomic;
  -- COMMENT '掌握度策略系统分析数据库';
  ```

### 2.3 数据库选型决策摘要

**技术选型决策表**：
| 实体/表名 | 数据库类型 | 选型依据 | 数据特征 | 查询模式 | 预估数据量 |
|---------|-----------|---------|---------|---------|-----------|
| tbl_mastery_learning_task | PostgreSQL | 学习任务管理 | 事务性数据 | CRUD操作 | 中表(100万) |
| tbl_mastery_learning_task_question_rel | PostgreSQL | 关联关系管理 | 关系数据 | 查询关联 | 大表(千万级) |
| tbl_mastery_learning_goal | PostgreSQL | 目标设定管理 | 事务性数据 | CRUD操作 | 中表(100万) |
| tbl_mastery_knowledge_point_mastery | PostgreSQL | 实时更新需求 | 事务性数据 | 高频读写 | 大表(千万级) |
| tbl_mastery_externalized_mastery | PostgreSQL | 展示数据查询 | 事务性数据 | 读多写少 | 大表(千万级) |
| tbl_mastery_target_mastery | PostgreSQL | 目标管理需求 | 事务性数据 | CRUD操作 | 大表(千万级) |
| tbl_mastery_answer_record | PostgreSQL | 答题记录管理 | 事务性数据 | 高频写入 | 大表(千万级) |
| tbl_mastery_calculation_parameter | PostgreSQL | 配置管理需求 | 配置数据 | 读多写少 | 小表(1万) |
| tbl_mastery_weak_point | PostgreSQL | 复杂推荐算法 | 事务性数据 | 复杂查询+排序 | 大表(千万级) |
| tbl_mastery_answer_record_history | ClickHouse | 仅INSERT分析 | 时序数据 | 聚合分析 | 超大表(亿级) |
| tbl_mastery_knowledge_point_mastery_history | ClickHouse | 历史数据分析 | 时序数据 | 聚合分析 | 超大表(亿级) |
| tbl_mastery_externalized_mastery_history | ClickHouse | 历史数据分析 | 时序数据 | 聚合分析 | 超大表(亿级) |

## 3. 数据库表详细设计 - PostgreSQL

### 3.1 数据库表清单

#### PostgreSQL表清单
| 序号 | 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 |
|------|------|------------------|-------------|---------|
| 1 | `tbl_mastery_learning_task` | 存储学习任务(AI课、巩固练习等)信息 | 中表(100万) | 学习任务管理，需要事务保证 |
| 2 | `tbl_mastery_learning_task_question_rel` | 存储学习任务与题目的多对多关联关系 | 大表(千万级) | 关联关系管理，需要快速查询 |
| 3 | `tbl_mastery_learning_goal` | 存储学生设定的学习目标 | 中表(100万) | 目标设定管理，需要事务保证 |
| 4 | `tbl_mastery_knowledge_point_mastery` | 存储学生对知识点的掌握度数据 | 大表(千万级) | 高频实时更新，需要强一致性 |
| 5 | `tbl_mastery_externalized_mastery` | 存储学生对课程的外化掌握度数据 | 大表(千万级) | 读多写少，需要快速查询 |
| 6 | `tbl_mastery_target_mastery` | 存储学生对课程的目标掌握度数据 | 大表(千万级) | 事务性数据，V2.0版本使用 |
| 7 | `tbl_mastery_answer_record` | 存储学生的答题记录 | 大表(千万级) | 高频写入，需要事务保证 |
| 8 | `tbl_mastery_calculation_parameter` | 存储掌握度计算相关的参数配置 | 小表(1万) | 配置数据，需要事务保证 |
| 9 | `tbl_mastery_weak_point` | 存储学生的薄弱知识点信息 | 大表(千万级) | 复杂查询和推荐算法支持 |

#### ClickHouse表清单
| 序号 | 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 |
|------|------|------------------|-------------|---------|
| 10 | `tbl_mastery_answer_record_history` | 存储历史答题记录数据，用于分析 | 超大表(亿级) | 仅INSERT操作，大数据量聚合分析 |
| 11 | `tbl_mastery_knowledge_point_mastery_history` | 存储知识点掌握度历史变化数据，用于分析 | 超大表(亿级) | 历史数据分析，时序查询优化 |
| 12 | `tbl_mastery_externalized_mastery_history` | 存储外化掌握度历史变化数据，用于分析 | 超大表(亿级) | 历史数据分析，时序查询优化 |

---

### 3.2 表名: `tbl_mastery_learning_task` - 学习任务表

#### 3.2.1 表功能说明
tbl_mastery_learning_task 表用于存储学习任务的基本信息，包括AI课、巩固练习、作业、测验等不同类型的学习任务。学习任务是学生学习过程中的基本单元，与课程关联，包含多个题目。学习任务的完成会触发外化掌握度的更新。

#### 3.2.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_learning_task (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  task_name VARCHAR(255) NOT NULL,
  task_type SMALLINT NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  completion_threshold SMALLINT NOT NULL DEFAULT 5,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_learning_task IS '学习任务表，存储AI课、巩固练习、作业、测验等学习任务信息';
COMMENT ON COLUMN tbl_mastery_learning_task.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_learning_task.task_name IS '学习任务名称';
COMMENT ON COLUMN tbl_mastery_learning_task.task_type IS '学习任务类型 (10:AI课, 20:巩固练习, 30:作业, 40:测验)';
COMMENT ON COLUMN tbl_mastery_learning_task.course_id IS '关联的课程ID，引用内容平台的课程';
COMMENT ON COLUMN tbl_mastery_learning_task.completion_threshold IS '完成阈值，表示需要完成多少题目才算完成任务，默认为5题';
COMMENT ON COLUMN tbl_mastery_learning_task.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_learning_task.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_learning_task.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_learning_task.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_mastery_learning_task.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_mastery_learning_task.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_mastery_learning_task_course_id ON tbl_mastery_learning_task(course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_learning_task_task_type ON tbl_mastery_learning_task(task_type) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_learning_task_status ON tbl_mastery_learning_task(status) WHERE deleted_at IS NULL;
```

#### 3.2.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_user_tiering_user_subject` | user_id,subject_id | btree | 用户学科分层唯一性约束 | 按用户和学科查询分层信息 | 高效单行查询 |
| `idx_mastery_user_tiering_school` | school_id | btree | 学校维度查询优化 | 按学校统计分层分布 | 高效范围查询 |
| `idx_mastery_user_tiering_level` | tiering_level | btree | 分层等级查询优化 | 按分层等级统计用户数量 | 高效分组查询 |
| `idx_mastery_user_tiering_update_time` | update_time | btree | 时间范围查询优化 | 查询最近更新的分层数据 | 高效时序查询 |

---

### 3.3 表名: `tbl_mastery_knowledge_mastery` - 知识点掌握度表

#### 3.3.1 表功能说明
存储学生对各知识点的掌握度核心数据，支持实时更新和高频查询。是掌握度计算系统的核心数据表。

#### 3.3.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_knowledge_mastery (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    user_id BIGINT NOT NULL,
    knowledge_point_id BIGINT NOT NULL,
    current_mastery NUMERIC(5,4) NOT NULL DEFAULT 0.0000,
    subject_initial_value NUMERIC(3,2) NOT NULL DEFAULT 0.30,
    cumulative_answer_count INTEGER NOT NULL DEFAULT 0,
    -- 标准字段
    status SMALLINT NOT NULL DEFAULT 1,
    create_time BIGINT NOT NULL,
    update_time BIGINT NOT NULL,
    delete_time BIGINT DEFAULT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_knowledge_mastery IS '知识点掌握度表 - 存储学生对各知识点的掌握程度，支持实时更新';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.user_id IS '学生用户ID';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.knowledge_point_id IS '知识点ID，关联内容平台业务树';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.current_mastery IS '当前掌握度，取值范围[0,1]';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.subject_initial_value IS '学科初始值，统一设置为0.3';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.cumulative_answer_count IS '累计答题次数，用于判断是否更新外化掌握度';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.create_time IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.update_time IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.delete_time IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_knowledge_mastery_user_kp ON tbl_mastery_knowledge_mastery(user_id, knowledge_point_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_knowledge_mastery_user ON tbl_mastery_knowledge_mastery(user_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_knowledge_mastery_kp ON tbl_mastery_knowledge_mastery(knowledge_point_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_knowledge_mastery_mastery ON tbl_mastery_knowledge_mastery(current_mastery) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_knowledge_mastery_update_time ON tbl_mastery_knowledge_mastery(update_time) WHERE delete_time IS NULL;
```

#### 3.3.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_knowledge_mastery_user_kp` | user_id,knowledge_point_id | btree | 用户知识点掌握度唯一性约束 | 按用户和知识点查询掌握度 | 高效单行查询 |
| `idx_mastery_knowledge_mastery_user` | user_id | btree | 用户维度查询优化 | 查询用户所有知识点掌握度 | 高效范围查询 |
| `idx_mastery_knowledge_mastery_kp` | knowledge_point_id | btree | 知识点维度查询优化 | 统计知识点掌握度分布 | 高效聚合查询 |
| `idx_mastery_knowledge_mastery_mastery` | current_mastery | btree | 掌握度值查询优化 | 查询薄弱知识点 | 高效范围查询 |
| `idx_mastery_knowledge_mastery_update_time` | update_time | btree | 时间范围查询优化 | 查询最近更新的掌握度 | 高效时序查询 |

---

### 3.4 表名: `tbl_mastery_external_mastery` - 外化掌握度表

#### 3.4.1 表功能说明
存储向学生展示的外化掌握度数据，包括课程级别的掌握度百分比和薄弱知识点标识。支持只增不降的展示逻辑。

#### 3.4.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_external_mastery (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    external_mastery_value NUMERIC(5,4) NOT NULL DEFAULT 0.0000,
    mastery_upper_limit NUMERIC(3,2) NOT NULL DEFAULT 1.00,
    course_difficulty VARCHAR(10) NOT NULL,
    is_weak_knowledge_point BOOLEAN NOT NULL DEFAULT FALSE,
    -- 标准字段
    status SMALLINT NOT NULL DEFAULT 1,
    create_time BIGINT NOT NULL,
    update_time BIGINT NOT NULL,
    delete_time BIGINT DEFAULT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_external_mastery IS '外化掌握度表 - 存储向学生展示的掌握度百分比和薄弱知识点标识';
COMMENT ON COLUMN tbl_mastery_external_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_external_mastery.user_id IS '学生用户ID';
COMMENT ON COLUMN tbl_mastery_external_mastery.course_id IS '课程ID，关联内容平台';
COMMENT ON COLUMN tbl_mastery_external_mastery.external_mastery_value IS '外化掌握度值，向学生展示的掌握度百分比，取值范围[0,1]';
COMMENT ON COLUMN tbl_mastery_external_mastery.mastery_upper_limit IS '掌握度上限 (课程难度高:1, 中:0.9, 低:0.7)';
COMMENT ON COLUMN tbl_mastery_external_mastery.course_difficulty IS '课程难度等级 (高、中、低)';
COMMENT ON COLUMN tbl_mastery_external_mastery.is_weak_knowledge_point IS '是否为薄弱知识点标识';
COMMENT ON COLUMN tbl_mastery_external_mastery.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_external_mastery.create_time IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_external_mastery.update_time IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_external_mastery.delete_time IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_external_mastery_user_course ON tbl_mastery_external_mastery(user_id, course_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_external_mastery_user ON tbl_mastery_external_mastery(user_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_external_mastery_course ON tbl_mastery_external_mastery(course_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_external_mastery_weak ON tbl_mastery_external_mastery(is_weak_knowledge_point) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_external_mastery_difficulty ON tbl_mastery_external_mastery(course_difficulty) WHERE delete_time IS NULL;
```

#### 3.4.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_external_mastery_user_course` | user_id,course_id | btree | 用户课程外化掌握度唯一性约束 | 按用户和课程查询外化掌握度 | 高效单行查询 |
| `idx_mastery_external_mastery_user` | user_id | btree | 用户维度查询优化 | 查询用户所有课程掌握度 | 高效范围查询 |
| `idx_mastery_external_mastery_course` | course_id | btree | 课程维度查询优化 | 统计课程掌握度分布 | 高效聚合查询 |
| `idx_mastery_external_mastery_weak` | is_weak_knowledge_point | btree | 薄弱知识点查询优化 | 查询用户薄弱知识点 | 高效过滤查询 |
| `idx_mastery_external_mastery_difficulty` | course_difficulty | btree | 课程难度查询优化 | 按难度统计掌握度 | 高效分组查询 |

---

### 3.5 表名: `tbl_mastery_weak_knowledge` - 薄弱知识点表

#### 3.5.1 表功能说明
独立管理学生的薄弱知识点识别结果，包括薄弱程度评估、识别时间、推荐优先级等信息，为个性化学习推荐提供精准的数据支持。

#### 3.5.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_weak_knowledge (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    user_id BIGINT NOT NULL,
    knowledge_point_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    weakness_degree NUMERIC(5,4) NOT NULL DEFAULT 0.0000,
    identified_time BIGINT NOT NULL,
    recommendation_priority INTEGER NOT NULL DEFAULT 1,
    weakness_type VARCHAR(20) NOT NULL DEFAULT 'mastery_low',
    improvement_suggestion TEXT,
    last_practice_time BIGINT,
    practice_count INTEGER NOT NULL DEFAULT 0,
    -- 标准字段
    status SMALLINT NOT NULL DEFAULT 1,
    create_time BIGINT NOT NULL,
    update_time BIGINT NOT NULL,
    delete_time BIGINT DEFAULT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_weak_knowledge IS '薄弱知识点表 - 存储学生薄弱知识点识别结果和推荐信息';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.user_id IS '学生用户ID';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.knowledge_point_id IS '知识点ID';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.course_id IS '课程ID';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.weakness_degree IS '薄弱程度，取值范围[0,1]，值越高表示越薄弱';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.identified_time IS '识别时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.recommendation_priority IS '推荐优先级，数值越小优先级越高';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.weakness_type IS '薄弱类型 (mastery_low:掌握度低, error_frequent:错误频繁, time_long:用时过长)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.improvement_suggestion IS '改进建议文本';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.last_practice_time IS '最后练习时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.practice_count IS '针对性练习次数';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.status IS '状态 (0:已改善, 1:需要关注, 2:重点关注)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.create_time IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.update_time IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.delete_time IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_weak_knowledge_user_kp ON tbl_mastery_weak_knowledge(user_id, knowledge_point_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_weak_knowledge_user ON tbl_mastery_weak_knowledge(user_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_weak_knowledge_course ON tbl_mastery_weak_knowledge(course_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_weak_knowledge_priority ON tbl_mastery_weak_knowledge(recommendation_priority) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_weak_knowledge_type ON tbl_mastery_weak_knowledge(weakness_type) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_weak_knowledge_identified_time ON tbl_mastery_weak_knowledge(identified_time) WHERE delete_time IS NULL;
```

#### 3.5.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_weak_knowledge_user_kp` | user_id,knowledge_point_id | btree | 用户知识点薄弱记录唯一性约束 | 按用户和知识点查询薄弱信息 | 高效单行查询 |
| `idx_mastery_weak_knowledge_user` | user_id | btree | 用户维度查询优化 | 查询用户所有薄弱知识点 | 高效范围查询 |
| `idx_mastery_weak_knowledge_course` | course_id | btree | 课程维度查询优化 | 统计课程薄弱知识点分布 | 高效聚合查询 |
| `idx_mastery_weak_knowledge_priority` | recommendation_priority | btree | 推荐优先级查询优化 | 按优先级排序推荐 | 高效排序查询 |
| `idx_mastery_weak_knowledge_type` | weakness_type | btree | 薄弱类型查询优化 | 按薄弱类型分类统计 | 高效分组查询 |
| `idx_mastery_weak_knowledge_identified_time` | identified_time | btree | 时间范围查询优化 | 查询最近识别的薄弱点 | 高效时序查询 |

---

### 3.6 表名: `tbl_mastery_target_mastery` - 目标掌握度表

#### 3.6.1 表功能说明
存储学生的学习目标和期望达到的掌握度标准，支持个性化学习目标设定和进度跟踪。V1.0版本暂不实现，为V2.0版本预留设计。

#### 3.6.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_target_mastery (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    target_mastery_value NUMERIC(5,4) NOT NULL DEFAULT 0.8000,
    target_difficulty_coefficient NUMERIC(3,2) NOT NULL DEFAULT 1.00,
    single_course_difficulty_coefficient NUMERIC(3,2) NOT NULL DEFAULT 0.70,
    exam_frequency_coefficient NUMERIC(3,2) NOT NULL DEFAULT 1.00,
    target_school_type VARCHAR(20) NOT NULL DEFAULT 'undergraduate',
    target_completion_time BIGINT,
    current_progress NUMERIC(5,4) NOT NULL DEFAULT 0.0000,
    -- 标准字段
    status SMALLINT NOT NULL DEFAULT 1,
    create_time BIGINT NOT NULL,
    update_time BIGINT NOT NULL,
    delete_time BIGINT DEFAULT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_target_mastery IS '目标掌握度表 - 存储学生学习目标和期望掌握度标准 (V2.0版本使用)';
COMMENT ON COLUMN tbl_mastery_target_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_target_mastery.user_id IS '学生用户ID';
COMMENT ON COLUMN tbl_mastery_target_mastery.course_id IS '课程ID';
COMMENT ON COLUMN tbl_mastery_target_mastery.target_mastery_value IS '目标掌握度值，取值范围[0,1]，按0.05向上取整';
COMMENT ON COLUMN tbl_mastery_target_mastery.target_difficulty_coefficient IS '目标难度系数 (C9:1.3, 985/211:1.2, 一本:1.1, 二本:1.0, 二本以下:0.9)';
COMMENT ON COLUMN tbl_mastery_target_mastery.single_course_difficulty_coefficient IS '单课难度系数 (文科高:0.7/中:0.65/低:0.6, 理科高:0.8/中:0.7/低:0.6)';
COMMENT ON COLUMN tbl_mastery_target_mastery.exam_frequency_coefficient IS '考试频率系数 (高:1.0, 中:0.9, 低:0.8)';
COMMENT ON COLUMN tbl_mastery_target_mastery.target_school_type IS '目标院校类型 (C9, 985_211, undergraduate, second_tier, below_second_tier)';
COMMENT ON COLUMN tbl_mastery_target_mastery.target_completion_time IS '目标完成时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_target_mastery.current_progress IS '当前进度，相对于目标的完成比例';
COMMENT ON COLUMN tbl_mastery_target_mastery.status IS '状态 (0:已完成, 1:进行中, 2:已暂停)';
COMMENT ON COLUMN tbl_mastery_target_mastery.create_time IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_target_mastery.update_time IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_target_mastery.delete_time IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_target_mastery_user_course ON tbl_mastery_target_mastery(user_id, course_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_target_mastery_user ON tbl_mastery_target_mastery(user_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_target_mastery_course ON tbl_mastery_target_mastery(course_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_target_mastery_school_type ON tbl_mastery_target_mastery(target_school_type) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_target_mastery_completion_time ON tbl_mastery_target_mastery(target_completion_time) WHERE delete_time IS NULL;
```

#### 3.6.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_target_mastery_user_course` | user_id,course_id | btree | 用户课程目标掌握度唯一性约束 | 按用户和课程查询目标 | 高效单行查询 |
| `idx_mastery_target_mastery_user` | user_id | btree | 用户维度查询优化 | 查询用户所有学习目标 | 高效范围查询 |
| `idx_mastery_target_mastery_course` | course_id | btree | 课程维度查询优化 | 统计课程目标设定情况 | 高效聚合查询 |
| `idx_mastery_target_mastery_school_type` | target_school_type | btree | 目标院校类型查询优化 | 按院校类型统计目标分布 | 高效分组查询 |
| `idx_mastery_target_mastery_completion_time` | target_completion_time | btree | 完成时间查询优化 | 查询即将到期的目标 | 高效时序查询 |

---

### 3.7 表名: `tbl_mastery_calculation_config` - 掌握度计算配置表

#### 3.7.1 表功能说明
存储掌握度计算相关的各种参数配置和系数，支持算法参数的动态调整和版本管理，确保计算逻辑的灵活性和可维护性。

#### 3.7.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_calculation_config (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    config_key VARCHAR(100) NOT NULL,
    config_value VARCHAR(500) NOT NULL,
    config_type VARCHAR(50) NOT NULL,
    description TEXT,
    subject_id BIGINT,
    school_type VARCHAR(20),
    difficulty_level VARCHAR(10),
    version VARCHAR(20) NOT NULL DEFAULT 'v1.0',
    effective_time BIGINT NOT NULL,
    expire_time BIGINT,
    -- 标准字段
    status SMALLINT NOT NULL DEFAULT 1,
    create_time BIGINT NOT NULL,
    update_time BIGINT NOT NULL,
    delete_time BIGINT DEFAULT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_calculation_config IS '掌握度计算配置表 - 存储算法参数和系数配置';
COMMENT ON COLUMN tbl_mastery_calculation_config.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_calculation_config.config_key IS '配置键名，如 calibration_coefficient_a_range';
COMMENT ON COLUMN tbl_mastery_calculation_config.config_value IS '配置值，支持JSON格式存储复杂配置';
COMMENT ON COLUMN tbl_mastery_calculation_config.config_type IS '配置类型 (coefficient:系数, threshold:阈值, formula:公式, mapping:映射)';
COMMENT ON COLUMN tbl_mastery_calculation_config.description IS '配置说明和用途描述';
COMMENT ON COLUMN tbl_mastery_calculation_config.subject_id IS '适用学科ID，NULL表示通用配置';
COMMENT ON COLUMN tbl_mastery_calculation_config.school_type IS '适用学校类型，NULL表示通用配置';
COMMENT ON COLUMN tbl_mastery_calculation_config.difficulty_level IS '适用难度等级，NULL表示通用配置';
COMMENT ON COLUMN tbl_mastery_calculation_config.version IS '配置版本号';
COMMENT ON COLUMN tbl_mastery_calculation_config.effective_time IS '生效时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_calculation_config.expire_time IS '过期时间 (UTC秒数时间戳)，NULL表示永不过期';
COMMENT ON COLUMN tbl_mastery_calculation_config.status IS '状态 (0:禁用, 1:启用, 2:测试中)';
COMMENT ON COLUMN tbl_mastery_calculation_config.create_time IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_calculation_config.update_time IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_calculation_config.delete_time IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_calculation_config_key_version ON tbl_mastery_calculation_config(config_key, version) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_calculation_config_type ON tbl_mastery_calculation_config(config_type) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_calculation_config_subject ON tbl_mastery_calculation_config(subject_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_calculation_config_effective_time ON tbl_mastery_calculation_config(effective_time) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_calculation_config_version ON tbl_mastery_calculation_config(version) WHERE delete_time IS NULL;
```

#### 3.7.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_calculation_config_key_version` | config_key,version | btree | 配置键版本唯一性约束 | 按配置键和版本查询配置 | 高效单行查询 |
| `idx_mastery_calculation_config_type` | config_type | btree | 配置类型查询优化 | 按类型查询相关配置 | 高效分组查询 |
| `idx_mastery_calculation_config_subject` | subject_id | btree | 学科维度查询优化 | 查询特定学科的配置 | 高效范围查询 |
| `idx_mastery_calculation_config_effective_time` | effective_time | btree | 生效时间查询优化 | 查询当前有效的配置 | 高效时序查询 |
| `idx_mastery_calculation_config_version` | version | btree | 版本查询优化 | 按版本管理配置 | 高效分组查询 |

---

### 3.8 表名: `tbl_mastery_school_ranking_history` - 学校排名历史表

#### 3.8.1 表功能说明
存储学校历年的排名数据和统计信息，用于计算校准系数b。支持历史数据的积累和趋势分析，为用户分层提供准确的校准基础。

#### 3.8.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_school_ranking_history (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    school_id BIGINT NOT NULL,
    subject_id BIGINT NOT NULL,
    academic_year VARCHAR(20) NOT NULL,
    ranking_data JSONB NOT NULL,
    median_ranking_reciprocal NUMERIC(10,8) NOT NULL,
    total_students INTEGER NOT NULL DEFAULT 0,
    data_source VARCHAR(50) NOT NULL DEFAULT 'manual',
    data_quality_score NUMERIC(3,2) NOT NULL DEFAULT 1.00,
    -- 标准字段
    status SMALLINT NOT NULL DEFAULT 1,
    create_time BIGINT NOT NULL,
    update_time BIGINT NOT NULL,
    delete_time BIGINT DEFAULT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_school_ranking_history IS '学校排名历史表 - 存储学校历年排名数据，用于校准系数b计算';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.school_id IS '学校ID';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.subject_id IS '学科ID';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.academic_year IS '学年，格式如2023-2024';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.ranking_data IS '排名详细数据，JSON格式存储各分数段人数分布';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.median_ranking_reciprocal IS '排名中位数倒数，用于校准系数b计算';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.total_students IS '该学年该学科总学生数';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.data_source IS '数据来源 (manual:手动录入, import:批量导入, api:接口同步)';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.data_quality_score IS '数据质量评分，取值范围[0,1]';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.status IS '状态 (0:无效, 1:有效, 2:待审核)';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.create_time IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.update_time IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_school_ranking_history.delete_time IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_school_ranking_history_school_subject_year ON tbl_mastery_school_ranking_history(school_id, subject_id, academic_year) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_school_ranking_history_school ON tbl_mastery_school_ranking_history(school_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_school_ranking_history_subject ON tbl_mastery_school_ranking_history(subject_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_school_ranking_history_year ON tbl_mastery_school_ranking_history(academic_year) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_school_ranking_history_quality ON tbl_mastery_school_ranking_history(data_quality_score) WHERE delete_time IS NULL;
```

#### 3.8.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_school_ranking_history_school_subject_year` | school_id,subject_id,academic_year | btree | 学校学科学年唯一性约束 | 按学校学科学年查询历史数据 | 高效单行查询 |
| `idx_mastery_school_ranking_history_school` | school_id | btree | 学校维度查询优化 | 查询学校所有历史数据 | 高效范围查询 |
| `idx_mastery_school_ranking_history_subject` | subject_id | btree | 学科维度查询优化 | 按学科统计历史趋势 | 高效聚合查询 |
| `idx_mastery_school_ranking_history_year` | academic_year | btree | 学年查询优化 | 按学年查询所有学校数据 | 高效范围查询 |
| `idx_mastery_school_ranking_history_quality` | data_quality_score | btree | 数据质量查询优化 | 筛选高质量历史数据 | 高效过滤查询 |

---

## 4. 数据库表详细设计 - ClickHouse

### 4.1 表名: `tbl_mastery_answer_records` - 答题记录日志表

#### 4.1.1 表功能说明
存储学生的所有答题记录，用于掌握度计算、学习行为分析和教学效果评估。支持大规模数据的高效写入和聚合分析。

#### 4.1.2 表结构定义 (DDL)
```sql
CREATE TABLE default.tbl_mastery_answer_records
(
    -- 业务字段
    record_id UInt64 CODEC(T64, ZSTD(1)),
    user_id UInt64 CODEC(T64, ZSTD(1)),
    question_id UInt64 CODEC(T64, ZSTD(1)),
    knowledge_point_id UInt64 CODEC(T64, ZSTD(1)),
    course_id UInt64 CODEC(T64, ZSTD(1)),
    answer_result LowCardinality(String),
    question_difficulty_coefficient Decimal(3,2) CODEC(ZSTD(1)),
    question_type LowCardinality(String),
    answer_time_seconds UInt32 CODEC(T64, ZSTD(1)),
    is_repeat_answer UInt8 DEFAULT 0,
    previous_answer_result LowCardinality(Nullable(String)),
    mastery_increment Decimal(6,4) CODEC(ZSTD(1)),
    weight_coefficient Decimal(3,2) DEFAULT 1.00 CODEC(ZSTD(1)),
    wrong_answer_compensation_coefficient Decimal(3,2) CODEC(ZSTD(1)),
    answer_timestamp DateTime64(3) CODEC(Delta(4), ZSTD(1)),
    
    -- 辅助字段
    log_date Date MATERIALIZED toDate(answer_timestamp)
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date)
ORDER BY (log_date, user_id, answer_timestamp, knowledge_point_id)
TTL log_date + INTERVAL 2 YEAR DELETE
SETTINGS index_granularity = 8192, merge_with_ttl_timeout = 86400;

-- 添加表注释
COMMENT ON TABLE default.tbl_mastery_answer_records IS '答题记录日志表 - 存储学生答题历史，用于掌握度计算和学习分析';

-- 添加字段注释
COMMENT ON COLUMN default.tbl_mastery_answer_records.record_id IS '答题记录唯一标识符';
COMMENT ON COLUMN default.tbl_mastery_answer_records.user_id IS '学生用户ID';
COMMENT ON COLUMN default.tbl_mastery_answer_records.question_id IS '题目ID，关联内容平台';
COMMENT ON COLUMN default.tbl_mastery_answer_records.knowledge_point_id IS '知识点ID';
COMMENT ON COLUMN default.tbl_mastery_answer_records.course_id IS '课程ID';
COMMENT ON COLUMN default.tbl_mastery_answer_records.answer_result IS '答题结果 (正确、错误、部分正确)';
COMMENT ON COLUMN default.tbl_mastery_answer_records.question_difficulty_coefficient IS '题目难度系数 (L1:0.3, L2:0.5, L3:0.7, L4:0.9, L5:1.1)';
COMMENT ON COLUMN default.tbl_mastery_answer_records.question_type IS '题目类型 (客观题、主观题)';
COMMENT ON COLUMN default.tbl_mastery_answer_records.answer_time_seconds IS '作答时间（秒）';
COMMENT ON COLUMN default.tbl_mastery_answer_records.is_repeat_answer IS '是否重复答题 (0:否, 1:是)';
COMMENT ON COLUMN default.tbl_mastery_answer_records.previous_answer_result IS '重复答题时的之前结果';
COMMENT ON COLUMN default.tbl_mastery_answer_records.mastery_increment IS '本次答题产生的掌握度变化';
COMMENT ON COLUMN default.tbl_mastery_answer_records.weight_coefficient IS '权重系数 (主观题:0.5, 客观题:1.0)';
COMMENT ON COLUMN default.tbl_mastery_answer_records.wrong_answer_compensation_coefficient IS '答错补偿系数 (L1:1.8, L2:0.8, L3:0.5, L4:0.3, L5:0.2)';
COMMENT ON COLUMN default.tbl_mastery_answer_records.answer_timestamp IS '答题时间戳';
COMMENT ON COLUMN default.tbl_mastery_answer_records.log_date IS '答题日期 (从answer_timestamp派生，用于分区)';

-- 跳数索引
ALTER TABLE default.tbl_mastery_answer_records ADD INDEX idx_question_id question_id TYPE bloom_filter GRANULARITY 1;
ALTER TABLE default.tbl_mastery_answer_records ADD INDEX idx_course_id course_id TYPE set(0) GRANULARITY 1;
```

#### 4.1.3 ClickHouse 特有说明
* **排序键 (ORDER BY)**: `(log_date, user_id, answer_timestamp, knowledge_point_id)`
  * 设计原因: 按日期分区后，用户ID作为主要查询维度，时间戳保证时序性，知识点ID支持知识点维度分析
* **分区键 (PARTITION BY)**: `toYYYYMM(log_date)`
  * 设计原因: 按月分区便于数据管理和TTL策略，支持高效的时间范围查询和历史数据清理

#### 4.1.4 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `ORDER BY` | log_date,user_id,answer_timestamp,knowledge_point_id | - | 主排序键优化 | 按用户时序查询答题记录 | ClickHouse排序键 |
| `idx_question_id` | question_id | bloom_filter | 题目查询优化 | 按题目统计答题情况 | 高效过滤查询 |
| `idx_course_id` | course_id | set | 课程查询优化 | 按课程分析答题数据 | 高效集合查询 |

---

## 5. 表间关系与 ER 图

### 5.1 表间关系描述

**表间关系标准格式**：
| 主表           | 关系类型                       | 从表               | 外键字段 (从表)                           | 关联描述                                               | 一致性保障方式 |
| -------------- | ------------------------------ | ------------------ | ----------------------------------------- | ------------------------------------------------------ | ------------- |
| `用户中心.users`   | 一对多 (1:N)                   | `tbl_mastery_user_tiering`     | `user_id`                                 | 一个用户可以有多个学科的分层信息                                 | 应用层校验 |
| `用户中心.schools` | 一对多 (1:N)                   | `tbl_mastery_user_tiering`     | `school_id`                               | 一个学校可以有多个学生的分层信息                                 | 应用层校验 |
| `内容平台.subjects` | 一对多 (1:N)                   | `tbl_mastery_user_tiering`     | `subject_id`                              | 一个学科可以有多个用户的分层信息                                 | 应用层校验 |
| `用户中心.users`   | 一对多 (1:N)                   | `tbl_mastery_knowledge_mastery` | `user_id`                                | 一个用户可以有多个知识点的掌握度                                 | 应用层校验 |
| `内容平台.knowledge_points` | 一对多 (1:N)    | `tbl_mastery_knowledge_mastery` | `knowledge_point_id`                     | 一个知识点可以有多个用户的掌握度记录                             | 应用层校验 |
| `用户中心.users`   | 一对多 (1:N)                   | `tbl_mastery_external_mastery`  | `user_id`                                | 一个用户可以有多个课程的外化掌握度                               | 应用层校验 |
| `内容平台.courses` | 一对多 (1:N)                   | `tbl_mastery_external_mastery`  | `course_id`                              | 一个课程可以有多个用户的外化掌握度记录                           | 应用层校验 |
| `用户中心.users`   | 一对多 (1:N)                   | `tbl_mastery_answer_records`    | `user_id`                                | 一个用户可以有多条答题记录                                       | 应用层校验 |
| `内容平台.questions` | 一对多 (1:N)                 | `tbl_mastery_answer_records`    | `question_id`                            | 一个题目可以有多条答题记录                                       | 应用层校验 |
| `内容平台.knowledge_points` | 一对多 (1:N)    | `tbl_mastery_answer_records`    | `knowledge_point_id`                     | 一个知识点可以有多条答题记录                                     | 应用层校验 |
| `内容平台.courses` | 一对多 (1:N)                   | `tbl_mastery_answer_records`    | `course_id`                              | 一个课程可以有多条答题记录                                       | 应用层校验 |
| `用户中心.users`   | 一对多 (1:N)                   | `tbl_mastery_weak_knowledge`    | `user_id`                                | 一个用户可以有多个薄弱知识点记录                                 | 应用层校验 |
| `内容平台.knowledge_points` | 一对多 (1:N)    | `tbl_mastery_weak_knowledge`    | `knowledge_point_id`                     | 一个知识点可以被多个用户标识为薄弱                               | 应用层校验 |
| `内容平台.courses` | 一对多 (1:N)                   | `tbl_mastery_weak_knowledge`    | `course_id`                              | 一个课程可以包含多个薄弱知识点记录                               | 应用层校验 |
| `用户中心.users`   | 一对多 (1:N)                   | `tbl_mastery_target_mastery`    | `user_id`                                | 一个用户可以设置多个课程的目标掌握度                             | 应用层校验 |
| `内容平台.courses` | 一对多 (1:N)                   | `tbl_mastery_target_mastery`    | `course_id`                              | 一个课程可以有多个用户的目标掌握度设置                           | 应用层校验 |
| `内容平台.subjects` | 一对多 (1:N)                   | `tbl_mastery_calculation_config` | `subject_id`                            | 一个学科可以有多个计算配置参数                                   | 应用层校验 |

### 5.2 ER 图 (使用 Mermaid 语法)

```mermaid
erDiagram
    %% PostgreSQL 核心表
    TBL_MASTERY_USER_TIERING {
        BIGSERIAL id PK
        BIGINT user_id FK
        BIGINT subject_id FK
        BIGINT school_id FK
        NUMERIC user_subject_ranking_percentage
        NUMERIC calibration_coefficient_a
        NUMERIC calibration_coefficient_b
        NUMERIC subject_ability_ranking
        VARCHAR tiering_level
        NUMERIC tiering_mastery_coefficient
        SMALLINT status
        BIGINT create_time
        BIGINT update_time
        BIGINT delete_time
    }
    
    TBL_MASTERY_KNOWLEDGE_MASTERY {
        BIGSERIAL id PK
        BIGINT user_id FK
        BIGINT knowledge_point_id FK
        NUMERIC current_mastery
        NUMERIC subject_initial_value
        INTEGER cumulative_answer_count
        SMALLINT status
        BIGINT create_time
        BIGINT update_time
        BIGINT delete_time
    }
    
    TBL_MASTERY_EXTERNAL_MASTERY {
        BIGSERIAL id PK
        BIGINT user_id FK
        BIGINT course_id FK
        NUMERIC external_mastery_value
        NUMERIC mastery_upper_limit
        VARCHAR course_difficulty
        BOOLEAN is_weak_knowledge_point
        SMALLINT status
        BIGINT create_time
        BIGINT update_time
        BIGINT delete_time
    }
    
    TBL_MASTERY_WEAK_KNOWLEDGE {
        BIGSERIAL id PK
        BIGINT user_id FK
        BIGINT knowledge_point_id FK
        BIGINT course_id FK
        NUMERIC weakness_degree
        BIGINT identified_time
        INTEGER recommendation_priority
        VARCHAR weakness_type
        TEXT improvement_suggestion
        BIGINT last_practice_time
        INTEGER practice_count
        SMALLINT status
        BIGINT create_time
        BIGINT update_time
        BIGINT delete_time
    }
    
    TBL_MASTERY_TARGET_MASTERY {
        BIGSERIAL id PK
        BIGINT user_id FK
        BIGINT course_id FK
        NUMERIC target_mastery_value
        NUMERIC target_difficulty_coefficient
        NUMERIC single_course_difficulty_coefficient
        NUMERIC exam_frequency_coefficient
        VARCHAR target_school_type
        BIGINT target_completion_time
        NUMERIC current_progress
        SMALLINT status
        BIGINT create_time
        BIGINT update_time
        BIGINT delete_time
    }
    
    TBL_MASTERY_CALCULATION_CONFIG {
        BIGSERIAL id PK
        VARCHAR config_key
        VARCHAR config_value
        VARCHAR config_type
        TEXT description
        BIGINT subject_id FK
        VARCHAR school_type
        VARCHAR difficulty_level
        VARCHAR version
        BIGINT effective_time
        BIGINT expire_time
        SMALLINT status
        BIGINT create_time
        BIGINT update_time
        BIGINT delete_time
    }
    
    %% ClickHouse 分析表
    TBL_MASTERY_ANSWER_RECORDS {
        UInt64 record_id
        UInt64 user_id
        UInt64 question_id
        UInt64 knowledge_point_id
        UInt64 course_id
        LowCardinality_String answer_result
        Decimal question_difficulty_coefficient
        LowCardinality_String question_type
        UInt32 answer_time_seconds
        UInt8 is_repeat_answer
        LowCardinality_Nullable_String previous_answer_result
        Decimal mastery_increment
        Decimal weight_coefficient
        Decimal wrong_answer_compensation_coefficient
        DateTime64 answer_timestamp
        Date log_date
    }
    
    %% 外部服务表（逻辑关联）
    USERS {
        BIGINT id PK
        VARCHAR username
        VARCHAR email
        BIGINT school_id FK
    }
    
    SCHOOLS {
        BIGINT id PK
        VARCHAR name
        VARCHAR region
    }
    
    SUBJECTS {
        BIGINT id PK
        VARCHAR name
        VARCHAR code
    }
    
    KNOWLEDGE_POINTS {
        BIGINT id PK
        VARCHAR name
        BIGINT subject_id FK
        BIGINT course_id FK
    }
    
    COURSES {
        BIGINT id PK
        VARCHAR name
        BIGINT subject_id FK
        VARCHAR difficulty
    }
    
    QUESTIONS {
        BIGINT id PK
        VARCHAR title
        BIGINT knowledge_point_id FK
        VARCHAR difficulty_level
        VARCHAR question_type
    }

    %% 关系定义
    USERS ||--o{ TBL_MASTERY_USER_TIERING : "has tiering info"
    SCHOOLS ||--o{ TBL_MASTERY_USER_TIERING : "contains students"
    SUBJECTS ||--o{ TBL_MASTERY_USER_TIERING : "has tiering data"
    
    USERS ||--o{ TBL_MASTERY_KNOWLEDGE_MASTERY : "has mastery data"
    KNOWLEDGE_POINTS ||--o{ TBL_MASTERY_KNOWLEDGE_MASTERY : "tracked by"
    
    USERS ||--o{ TBL_MASTERY_EXTERNAL_MASTERY : "has external mastery"
    COURSES ||--o{ TBL_MASTERY_EXTERNAL_MASTERY : "has mastery display"
    
    USERS ||--o{ TBL_MASTERY_WEAK_KNOWLEDGE : "has weak points"
    KNOWLEDGE_POINTS ||--o{ TBL_MASTERY_WEAK_KNOWLEDGE : "identified as weak"
    COURSES ||--o{ TBL_MASTERY_WEAK_KNOWLEDGE : "contains weak points"
    
    USERS ||--o{ TBL_MASTERY_TARGET_MASTERY : "sets targets"
    COURSES ||--o{ TBL_MASTERY_TARGET_MASTERY : "has targets"
    
    SUBJECTS ||--o{ TBL_MASTERY_CALCULATION_CONFIG : "has config"
    
    USERS ||--o{ TBL_MASTERY_ANSWER_RECORDS : "generates records"
    QUESTIONS ||--o{ TBL_MASTERY_ANSWER_RECORDS : "answered in"
    KNOWLEDGE_POINTS ||--o{ TBL_MASTERY_ANSWER_RECORDS : "related to"
    COURSES ||--o{ TBL_MASTERY_ANSWER_RECORDS : "belongs to"
    
    SUBJECTS ||--o{ KNOWLEDGE_POINTS : "contains"
    SUBJECTS ||--o{ COURSES : "includes"
    COURSES ||--o{ KNOWLEDGE_POINTS : "covers"
    KNOWLEDGE_POINTS ||--o{ QUESTIONS : "tested by"
    SCHOOLS ||--o{ USERS : "enrolls"
```

## 6. 核心用户场景/用户故事与数据操作分析

### 6.1 场景/故事: 新用户初始化掌握度

#### 6.1.1 场景描述
当新学生首次进入系统时，需要根据其学校信息、成绩排名等数据计算用户分层，并初始化各知识点的掌握度。这是掌握度策略系统的起始场景。

#### 6.1.2 涉及的主要数据表
* `tbl_mastery_user_tiering`
* `tbl_mastery_knowledge_mastery`
* `tbl_mastery_external_mastery`

#### 6.1.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 获取学生基本信息 | Read | 外部用户中心 | user_id='xxx' | 外部服务索引 | 外部服务保障 | 低风险 |
| 获取学校质量信息 | Read | 外部用户中心 | school_id='xxx' | 外部服务索引 | 外部服务保障 | 低风险 |
| 创建用户分层记录 | Create | tbl_mastery_user_tiering | - | 主键自增 | 应用层校验 | 低风险 |
| 批量创建知识点掌握度 | Create | tbl_mastery_knowledge_mastery | - | 主键自增 | 应用层事务 | 中风险 |
| 批量创建外化掌握度 | Create | tbl_mastery_external_mastery | - | 主键自增 | 应用层事务 | 中风险 |

#### 6.1.4 性能考量与索引支持
用户初始化涉及大量数据插入操作，通过批量插入和事务控制保证性能和一致性。主键自增索引支持高效插入，唯一索引保证数据完整性。

---

### 6.2 场景/故事: 学生答题掌握度更新

#### 6.2.1 场景描述
学生完成答题后，系统需要实时计算掌握度增量，更新知识点掌握度，并判断是否需要更新外化掌握度。这是系统最高频的核心场景。

#### 6.2.2 涉及的主要数据表
* `tbl_mastery_answer_records`
* `tbl_mastery_knowledge_mastery`
* `tbl_mastery_external_mastery`

#### 6.2.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 获取题目信息 | Read | 外部内容平台 | question_id='xxx' | 外部服务索引 | 外部服务保障 | 低风险 |
| 查询当前掌握度 | Read | tbl_mastery_knowledge_mastery | user_id,knowledge_point_id | uk_mastery_knowledge_mastery_user_kp | 唯一约束 | 低风险 |
| 记录答题日志 | Create | tbl_mastery_answer_records | - | ClickHouse排序键 | 应用层校验 | 低风险 |
| 更新掌握度 | Update | tbl_mastery_knowledge_mastery | user_id,knowledge_point_id | uk_mastery_knowledge_mastery_user_kp | 应用层事务 | 中风险 |
| 更新外化掌握度 | Update | tbl_mastery_external_mastery | user_id,course_id | uk_mastery_external_mastery_user_course | 应用层事务 | 中风险 |

#### 6.2.4 性能考量与索引支持
高频更新场景通过唯一索引快速定位记录，ClickHouse的列式存储支持高效的日志写入，PostgreSQL的B-tree索引支持快速的掌握度查询和更新。

---

### 6.3 场景/故事: 教师查看班级掌握度统计

#### 6.3.1 场景描述
教师需要查看班级学生的掌握度分布情况，识别薄弱知识点，为教学决策提供数据支持。

#### 6.3.2 涉及的主要数据表
* `tbl_mastery_external_mastery`
* `tbl_mastery_answer_records`

#### 6.3.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 获取班级学生列表 | Read | 外部教师服务 | class_id='xxx' | 外部服务索引 | 外部服务保障 | 低风险 |
| 批量查询外化掌握度 | Read | tbl_mastery_external_mastery | user_id IN (...) | idx_mastery_external_mastery_user | 应用层校验 | 中风险 |
| 查询薄弱知识点 | Read | tbl_mastery_external_mastery | is_weak_knowledge_point=true | idx_mastery_external_mastery_weak | 应用层校验 | 低风险 |
| 统计答题数据 | Read | tbl_mastery_answer_records | user_id IN (...) | ClickHouse排序键 | 最终一致性 | 中风险 |

#### 6.3.4 性能考量与索引支持
批量查询通过IN条件和相关索引优化，ClickHouse的列式存储和聚合函数支持高效的统计分析，缓存策略可进一步提升查询性能。

---

### 6.4 场景/故事: 薄弱知识点识别和推荐

#### 6.4.1 场景描述
系统定期分析学生的掌握度数据，识别薄弱知识点，为个性化学习推荐提供数据基础。

#### 6.4.2 涉及的主要数据表
* `tbl_mastery_knowledge_mastery`
* `tbl_mastery_external_mastery`

#### 6.4.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 查询用户掌握度数据 | Read | tbl_mastery_knowledge_mastery | user_id='xxx' | idx_mastery_knowledge_mastery_user | 应用层校验 | 低风险 |
| 分析薄弱知识点 | Read | tbl_mastery_knowledge_mastery | current_mastery<=0.5 | idx_mastery_knowledge_mastery_mastery | 应用层校验 | 中风险 |
| 更新薄弱标识 | Update | tbl_mastery_external_mastery | user_id,course_id | uk_mastery_external_mastery_user_course | 应用层事务 | 低风险 |
| 获取推荐配置 | Read | 外部内容平台 | knowledge_point_id='xxx' | 外部服务索引 | 外部服务保障 | 低风险 |

#### 6.4.4 性能考量与索引支持
薄弱知识点识别通过掌握度值索引快速筛选，批量更新操作通过事务保证一致性，推荐算法可通过缓存优化性能。

---

### 6.5 场景/故事: 薄弱知识点管理和针对性练习

#### 6.5.1 场景描述
系统识别出学生的薄弱知识点后，需要记录薄弱程度、生成改进建议、设置推荐优先级，并跟踪学生的针对性练习进展，形成完整的薄弱知识点管理闭环。

#### 6.5.2 涉及的主要数据表
* `tbl_mastery_weak_knowledge`
* `tbl_mastery_knowledge_mastery`
* `tbl_mastery_answer_records`

#### 6.5.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 创建薄弱知识点记录 | Create | tbl_mastery_weak_knowledge | - | 主键自增 | 应用层校验 | 低风险 |
| 查询用户薄弱知识点 | Read | tbl_mastery_weak_knowledge | user_id='xxx' | idx_mastery_weak_knowledge_user | 应用层校验 | 低风险 |
| 按优先级排序推荐 | Read | tbl_mastery_weak_knowledge | user_id='xxx' ORDER BY recommendation_priority | idx_mastery_weak_knowledge_priority | 应用层校验 | 低风险 |
| 更新练习进度 | Update | tbl_mastery_weak_knowledge | user_id,knowledge_point_id | uk_mastery_weak_knowledge_user_kp | 应用层事务 | 低风险 |
| 统计练习效果 | Read | tbl_mastery_answer_records | user_id,knowledge_point_id | ClickHouse排序键 | 最终一致性 | 中风险 |
| 更新薄弱状态 | Update | tbl_mastery_weak_knowledge | weakness_degree<threshold | 应用层逻辑 | 应用层事务 | 低风险 |

#### 6.5.4 性能考量与索引支持
薄弱知识点管理通过专用的唯一索引和优先级索引支持高效查询，练习进度跟踪通过ClickHouse的时序数据分析提供统计支持，状态更新通过事务保证一致性。

---

## 7. 设计检查清单 (Design Checklist)

### 7.1 需求与架构对齐性检查

**标准化检查表格式**：每行必须对应一个具体的需求点或用户故事，确保可追溯性

| 需求点/用户故事 (来自PRD)        | 对应架构模块 (来自TD)           | 关联核心数据表 (本次设计) | ①需求覆盖完整性 | ②架构符合性 | ③一致性与无冲突 | ④业务场景满足度 | ⑤可追溯性与依赖清晰度 | 备注与待办事项                                                                                                                                  |
| -------------------------------- | ----------------------------- | --------------------- | --------------- | ----------- | --------------- | ------------- | ------------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| 学科能力评估与分层           | 用户分层服务+掌握度计算层 | `tbl_mastery_user_tiering` | ✅               | ✅           | ✅               | ✅             | ✅                   | 场景6.1已覆盖。支持校准系数计算、分层等级设定，设计符合架构中分层计算引擎的职责。                   |
| 知识点掌握度实时计算               | 掌握度计算引擎+掌握度数据服务       | `tbl_mastery_knowledge_mastery`, `tbl_mastery_answer_records` | ✅               | ✅           | ✅               | ✅             | ✅                   | 场景6.2已覆盖。PostgreSQL支持实时更新，ClickHouse支持高吞吐日志记录，满足架构要求。                                   |
| 外化掌握度展示      | 掌握度交互层+掌握度流程层               | `tbl_mastery_external_mastery`      | ✅               | ✅           | ✅               | ✅             | ✅                   | 支持只增不降逻辑、课程难度上限设置，符合架构中交互层的展示需求。                                                                                                                                               |
| 薄弱知识点识别      | 掌握度计算层+掌握度流程层               | `tbl_mastery_knowledge_mastery`, `tbl_mastery_external_mastery`      | ✅               | ✅           | ✅               | ✅             | ✅                   | 场景6.4已覆盖。通过掌握度阈值判断和标识字段支持薄弱知识点识别。                                                                                                                                               |
| 高性能并发处理      | 掌握度存储层+缓存策略               | 所有核心表+索引设计      | ✅               | ✅           | ✅               | ✅             | ✅                   | 通过PostgreSQL事务性保障+ClickHouse高吞吐+完善索引策略支持高并发需求。                                                                                                                                               |
| 教师班级统计查询      | 掌握度交互层+掌握度数据服务               | `tbl_mastery_external_mastery`, `tbl_mastery_answer_records`      | ✅               | ✅           | ✅               | ✅             | ✅                   | 场景6.3已覆盖。支持批量查询和聚合分析，满足教师端统计需求。                                                                                                                                               |
| 薄弱知识点管理      | 掌握度计算层+掌握度流程层               | `tbl_mastery_weak_knowledge`, `tbl_mastery_knowledge_mastery`      | ✅               | ✅           | ✅               | ✅             | ✅                   | 场景6.5已覆盖。独立的薄弱知识点表支持完整的识别、推荐、跟踪流程。                                                                                                                                               |
| 学习目标管理      | 掌握度流程层+掌握度数据服务               | `tbl_mastery_target_mastery`      | ✅               | ✅           | ✅               | ✅             | ✅                   | V2.0版本功能。支持个性化学习目标设定和进度跟踪。                                                                                                                                               |
| 算法参数配置      | 掌握度计算层+掌握度存储层               | `tbl_mastery_calculation_config`      | ✅               | ✅           | ✅               | ✅             | ✅                   | 支持掌握度计算算法的参数化配置和版本管理，提升系统灵活性。                                                                                                                                               |

---

### 7.2 数据库设计质量检查

**(针对本次设计的每个核心表或整体设计方案进行评估)**

| 检查维度                               | 评估结果 (✅/❌/N/A) | 具体说明与改进建议 (如果存在问题)                                                                                                |
| -------------------------------------- | ------------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| **⑥ 设计规范符合性** | ✅                   | 严格遵循PostgreSQL和ClickHouse设计规范，命名规范统一，数据类型选择合理，注释完整清晰                                                                                                |
|   - 命名规范                           | ✅                   | 所有表名使用tbl_前缀，字段名使用snake_case，索引名遵循标准格式                                                                                                                                |
|   - 标准字段                           | ✅                   | PostgreSQL表包含完整标准字段(id, status, create_time, update_time, delete_time)                                                                                                                                |
|   - 数据类型选择                       | ✅                   | 使用BIGINT存储UTC时间戳，NUMERIC保证精度，LowCardinality优化ClickHouse性能                                                                                                                                |
|   - 注释质量                           | ✅                   | 所有表和关键字段都有清晰的中文注释，包含业务含义和取值说明                                                                                                                                |
|   - 通用设计原则与反模式规避             | ✅                   | 遵循单一职责原则，避免巨石表，合理使用反规范化，禁用物理外键                                                                                                                                |
| **⑦ 性能与可伸缩性考量** | ✅                   | 索引设计合理，分区策略有效，表引擎选择最优，支持预估数据量和查询模式                                                                                                |
|   - 索引策略 (主键、排序键、跳数索引等)  | ✅                   | PostgreSQL使用B-tree索引优化查询，ClickHouse使用排序键和跳数索引优化分析                                                                                                                                |
|   - 分区/分片策略 (如果适用)             | ✅                   | ClickHouse按月分区，支持TTL策略和高效时间范围查询                                                                                                                                |
|   - 表引擎选择 (尤其 ClickHouse)        | ✅                   | 使用MergeTree引擎，支持排序键、分区和TTL，最适合分析场景                                                                                                                                |
|   - 数据压缩策略 (尤其 ClickHouse)      | ✅                   | 使用Delta+ZSTD压缩时间字段，T64+ZSTD压缩ID字段，LowCardinality优化枚举字段                                                                                                                                |
| **⑧ 可维护性与可扩展性** | ✅                   | 表结构清晰易懂，支持业务扩展，设计适度不过度复杂                                                                                                |
| **⑨ 数据完整性保障** | ✅                   | 通过NOT NULL约束、唯一索引、应用层校验保障数据完整性，软删除索引考虑完整                                                                                                |
| **⑩ (可选) 安全性考量** | ✅                   | 识别了学生学习数据的敏感性，建议在应用层实现数据脱敏和权限控制                                                                                                |
| **其他特定于数据库的检查点** (例如)      |                     |                                                                                                                                |
|   - PostgreSQL: `BIGINT` 时间戳使用        | ✅                   | 统一使用BIGINT存储UTC时间戳，符合规范要求                                                                                                                                |
|   - PostgreSQL: 软删除索引设计        | ✅                   | 所有索引都包含WHERE delete_time IS NULL条件                                                                                                                                |
|   - ClickHouse: `LowCardinality` 使用     | ✅                   | 合理使用LowCardinality优化枚举字段存储和查询性能                                                                                                                                |
|   - ClickHouse: 排序键设计合理性        | ✅                   | 排序键设计符合查询模式，支持高效的时序和用户维度查询                                                                                                                                |

## 8. 参考资料与附录
* 产品需求文档 (PRD): [PRD-掌握度策略-V1.0.md]
* 技术架构方案 (TD): [be-s2-1-ai-td-掌握度策略-claude-sonnet-4.md]
* 数据实体设计 (DE): [be-s3-1-ai-de-掌握度策略-claude-sonnet-4.md]
* PostgreSQL 设计规范与约束文件: [rules-postgresql-code-v2.md]
* ClickHouse 设计规范与约束文件: [rules-clickhouse-code-v2.md]
* 技术栈约束文件: [rules-tech-stack.md]

---

**数据库设计完成，已按模板要求完成质量检查，等待您的命令，指挥官**
| `ORDER BY` | log_date
### 3.3 表名: `tbl_mastery_learning_task_question_rel` - 学习任务与题目关联表

#### 3.3.1 表功能说明
tbl_mastery_learning_task_question_rel 表用于存储学习任务与题目的多对多关联关系。一个学习任务可以包含多个题目，一个题目也可以被多个学习任务使用。该表通过关联学习任务ID和题目ID，建立它们之间的映射关系，便于系统在学生完成学习任务时，能够准确地获取相关题目，并计算掌握度。

#### 3.3.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_learning_task_question_rel (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  task_id BIGINT NOT NULL,
  question_id VARCHAR(64) NOT NULL,
  question_order SMALLINT NOT NULL DEFAULT 0,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_learning_task_question_rel IS '学习任务与题目的关联表，建立多对多关系';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.task_id IS '学习任务ID，关联tbl_mastery_learning_task表';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.question_id IS '题目ID，引用内容平台的题目';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.question_order IS '题目在学习任务中的排序，从0开始';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_mastery_learning_task_question_rel_task_id ON tbl_mastery_learning_task_question_rel(task_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_learning_task_question_rel_question_id ON tbl_mastery_learning_task_question_rel(question_id) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX uk_mastery_learning_task_question_rel_task_question ON tbl_mastery_learning_task_question_rel(task_id, question_id) WHERE deleted_at IS NULL;
```

### 3.4 表名: `tbl_mastery_learning_goal` - 学习目标表

#### 3.4.1 表功能说明
tbl_mastery_learning_goal 表用于存储学生设定的学习目标信息。学习目标是学生在学习过程中设定的期望达到的成绩或水平，如高中阶段选择大学院校为目标，初中阶段选择年级排名为目标。系统会根据学生设定的目标，计算每节课应达到的目标掌握度，指导学生的学习方向。

#### 3.4.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_learning_goal (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  goal_type SMALLINT NOT NULL,
  goal_value VARCHAR(255) NOT NULL,
  goal_difficulty_factor NUMERIC(5, 2) NOT NULL,
  subject_ids VARCHAR(255) NOT NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_learning_goal IS '学习目标表，存储学生设定的学习目标';
COMMENT ON COLUMN tbl_mastery_learning_goal.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_learning_goal.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_learning_goal.goal_type IS '目标类型 (1:大学院校, 2:年级排名)';
COMMENT ON COLUMN tbl_mastery_learning_goal.goal_value IS '目标值，如大学院校名称或年级排名百分比';
COMMENT ON COLUMN tbl_mastery_learning_goal.goal_difficulty_factor IS '目标难度系数，根据目标值确定';
COMMENT ON COLUMN tbl_mastery_learning_goal.subject_ids IS '适用的学科ID列表，逗号分隔';
COMMENT ON COLUMN tbl_mastery_learning_goal.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_learning_goal.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_learning_goal.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_learning_goal.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE INDEX idx_mastery_learning_goal_user_id ON tbl_mastery_learning_goal(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_learning_goal_goal_type ON tbl_mastery_learning_goal(goal_type) WHERE deleted_at IS NULL;
```

### 3.5 表名: `tbl_mastery_knowledge_point_mastery` - 知识点掌握度表

#### 3.5.1 表功能说明
tbl_mastery_knowledge_point_mastery 表是掌握度策略系统的核心表之一，用于存储学生对知识点的掌握程度数据。掌握度值范围为[0,1]，表示学生对知识点的理解和掌握程度。系统会根据学生的答题情况实时更新掌握度值，并用于计算外化掌握度和判断薄弱知识点。

#### 3.5.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_knowledge_point_mastery (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  knowledge_point_id VARCHAR(64) NOT NULL,
  mastery_value NUMERIC(4, 3) NOT NULL DEFAULT 0.3,
  answer_count INTEGER NOT NULL DEFAULT 0,
  is_weak_point BOOLEAN NOT NULL DEFAULT FALSE,
  last_updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_reason SMALLINT NOT NULL DEFAULT 0,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_knowledge_point_mastery IS '知识点掌握度表，存储学生对知识点的掌握程度';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.knowledge_point_id IS '知识点ID，引用内容平台的知识点';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.mastery_value IS '掌握度值，范围[0,1]，表示学生对知识点的掌握程度';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.answer_count IS '累计答题量，记录学生在该知识点上的答题次数';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.is_weak_point IS '是否为薄弱知识点，TRUE表示是薄弱知识点';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.last_updated_at IS '掌握度最后更新时间';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.update_reason IS '更新原因 (0:初始化, 1:答题, 2:错题重练, 3:自评主观题)';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_knowledge_point_mastery_user_knowledge ON tbl_mastery_knowledge_point_mastery(user_id, knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_knowledge_point_mastery_user_id ON tbl_mastery_knowledge_point_mastery(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_knowledge_point_mastery_knowledge_point_id ON tbl_mastery_knowledge_point_mastery(knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_knowledge_point_mastery_is_weak_point ON tbl_mastery_knowledge_point_mastery(is_weak_point) WHERE deleted_at IS NULL AND is_weak_point = TRUE;
```

### 3.6 表名: `tbl_mastery_externalized_mastery` - 外化掌握度表

#### 3.6.1 表功能说明
tbl_mastery_externalized_mastery 表用于存储学生对课程的外化掌握度数据。外化掌握度是向学生展示的课程掌握程度，用百分比表示，是知识点掌握度的外部表现形式。外化掌握度遵循"只增不降"原则，即新计算的外化掌握度只有高于当前值时才会更新。学生完成学习单元（AI课、巩固练习、作业、测验）后，系统会更新外化掌握度。

#### 3.6.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_externalized_mastery (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  externalized_mastery_value NUMERIC(5, 2) NOT NULL DEFAULT 0,
  cumulative_answer_count INTEGER NOT NULL DEFAULT 0,
  last_updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_source SMALLINT NOT NULL DEFAULT 0,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_externalized_mastery IS '外化掌握度表，存储学生对课程的外化掌握度';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.course_id IS '课程ID，引用内容平台的课程';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.externalized_mastery_value IS '外化掌握度值，百分比表示，范围[0,100]';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.cumulative_answer_count IS '距离上次更新掌握度的累计答题量';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.last_updated_at IS '外化掌握度最后更新时间';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.update_source IS '更新来源 (10:AI课, 20:巩固练习, 30:作业, 40:测验)';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_externalized_mastery_user_course ON tbl_mastery_externalized_mastery(user_id, course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_externalized_mastery_user_id ON tbl_mastery_externalized_mastery(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_externalized_mastery_course_id ON tbl_mastery_externalized_mastery(course_id) WHERE deleted_at IS NULL;
```

### 3.7 表名: `tbl_mastery_target_mastery` - 目标掌握度表

#### 3.7.1 表功能说明
tbl_mastery_target_mastery 表用于存储学生对课程的目标掌握度数据。目标掌握度是基于学生学习目标，为每节课设定的期望掌握度，指导学生的学习方向。系统会根据学生设定的目标（如目标院校、年级排名），结合课程难度、考试频率等因素，计算每节课应达到的目标掌握度。

#### 3.7.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_target_mastery (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  target_mastery_value NUMERIC(5, 2) NOT NULL,
  learning_goal_id BIGINT NOT NULL,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_target_mastery IS '目标掌握度表，存储学生对课程的目标掌握度';
COMMENT ON COLUMN tbl_mastery_target_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_target_mastery.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_target_mastery.course_id IS '课程ID，引用内容平台的课程';
COMMENT ON COLUMN tbl_mastery_target_mastery.target_mastery_value IS '目标掌握度值，百分比表示，范围[0,100]';
COMMENT ON COLUMN tbl_mastery_target_mastery.learning_goal_id IS '学习目标ID，关联tbl_mastery_learning_goal表';
COMMENT ON COLUMN tbl_mastery_target_mastery.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_target_mastery.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_target_mastery.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_target_mastery_user_course ON tbl_mastery_target_mastery(user_id, course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_target_mastery_user_id ON tbl_mastery_target_mastery(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_target_mastery_course_id ON tbl_mastery_target_mastery(course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_target_mastery_learning_goal_id ON tbl_mastery_target_mastery(learning_goal_id) WHERE deleted_at IS NULL;
```

### 3.8 表名: `tbl_mastery_answer_record` - 答题记录表

#### 3.8.1 表功能说明
tbl_mastery_answer_record 表用于存储学生的答题记录数据。每条记录包含学生在特定学习任务中对特定题目的作答情况，包括答题结果、答题时间、作答时长、尝试次数等信息。这些数据是计算知识点掌握度的基础，也用于分析学生的学习行为和效果。

#### 3.8.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_answer_record (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  question_id VARCHAR(64) NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  task_id BIGINT NOT NULL,
  knowledge_point_ids VARCHAR(255) NOT NULL,
  answer_result SMALLINT NOT NULL,
  answer_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  answer_duration INTEGER NOT NULL DEFAULT 0,
  attempt_count INTEGER NOT NULL DEFAULT 1,
  mastery_increment NUMERIC(4, 3) NOT NULL,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_answer_record IS '答题记录表，存储学生的答题情况';
COMMENT ON COLUMN tbl_mastery_answer_record.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_answer_record.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_answer_record.question_id IS '题目ID，引用内容平台的题目';
COMMENT ON COLUMN tbl_mastery_answer_record.course_id IS '课程ID，引用内容平台的课程';
COMMENT ON COLUMN tbl_mastery_answer_record.task_id IS '学习任务ID，关联tbl_mastery_learning_task表';
COMMENT ON COLUMN tbl_mastery_answer_record.knowledge_point_ids IS '关联的知识点ID列表，逗号分隔';
COMMENT ON COLUMN tbl_mastery_answer_record.answer_result IS '答题结果 (1:正确, 2:部分正确, 3:错误)';
COMMENT ON COLUMN tbl_mastery_answer_record.answer_time IS '答题时间';
COMMENT ON COLUMN tbl_mastery_answer_record.answer_duration IS '作答时长，单位为秒';
COMMENT ON COLUMN tbl_mastery_answer_record.attempt_count IS '尝试次数，从1开始';
COMMENT ON COLUMN tbl_mastery_answer_record.mastery_increment IS '本次答题导致的知识点掌握度增量';
COMMENT ON COLUMN tbl_mastery_answer_record.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_answer_record.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_answer_record.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE INDEX idx_mastery_answer_record_user_id ON tbl_mastery_answer_record(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_answer_record_question_id ON tbl_mastery_answer_record(question_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_answer_record_course_id ON tbl_mastery_answer_record(course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_answer_record_task_id ON tbl_mastery_answer_record(task_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_answer_record_answer_time ON tbl_mastery_answer_record(answer_time) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_answer_record_user_question ON tbl_mastery_answer_record(user_id, question_id) WHERE deleted_at IS NULL;
```

### 3.9 表名: `tbl_mastery_calculation_parameter` - 掌握度计算参数表

#### 3.9.1 表功能说明
tbl_mastery_calculation_parameter 表用于存储掌握度计算相关的参数配置。这些参数包括掌握度初始值、掌握度增量计算系数、外化掌握度计算参数、目标掌握度计算参数等。通过集中管理这些参数，可以灵活调整掌握度计算策略，而无需修改代码。

#### 3.9.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_calculation_parameter (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  param_type VARCHAR(50) NOT NULL,
  param_key VARCHAR(100) NOT NULL,
  param_value VARCHAR(255) NOT NULL,
  param_desc TEXT NULL,
  applicable_scope VARCHAR(50) NOT NULL DEFAULT 'global',
  scope_id VARCHAR(64) NULL,
  effective_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  expire_time TIMESTAMPTZ NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_calculation_parameter IS '掌握度计算参数表，存储掌握度计算相关的参数配置';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.param_type IS '参数类型 (mastery_init:初始掌握度, mastery_increment:掌握度增量, externalized:外化掌握度, target:目标掌握度)';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.param_key IS '参数键名';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.param_value IS '参数值';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.param_desc IS '参数描述';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.applicable_scope IS '适用范围 (global:全局, subject:学科, grade:年级)';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.scope_id IS '范围ID，当applicable_scope不为global时使用';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.effective_time IS '参数生效时间';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.expire_time IS '参数失效时间，NULL表示永不失效';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_calculation_parameter_type_key_scope ON tbl_mastery_calculation_parameter(param_type, param_key, applicable_scope, COALESCE(scope_id, '')) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_calculation_parameter_param_type ON tbl_mastery_calculation_parameter(param_type) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_calculation_parameter_applicable_scope ON tbl_mastery_calculation_parameter(applicable_scope, scope_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_calculation_parameter_effective_time ON tbl_mastery_calculation_parameter(effective_time) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_calculation_parameter_expire_time ON tbl_mastery_calculation_parameter(expire_time) WHERE deleted_at IS NULL;
```

### 3.10 表名: `tbl_mastery_weak_point` - 薄弱知识点表

#### 3.10.1 表功能说明
tbl_mastery_weak_point 表用于存储学生的薄弱知识点信息。薄弱知识点是掌握度显著低于目标的知识点，需要额外关注和练习。系统根据知识点掌握度与目标掌握度的比较，判断薄弱知识点，并提供给推荐引擎进行内容推荐。判断标准为：知识点掌握度/目标掌握度≤0.7且知识点掌握度≤0.5。

#### 3.10.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_weak_point (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  knowledge_point_id VARCHAR(64) NOT NULL,
  current_mastery NUMERIC(4, 3) NOT NULL,
  target_mastery NUMERIC(5, 2) NOT NULL,
  gap_ratio NUMERIC(4, 3) NOT NULL,
  analysis_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  recommendation_priority SMALLINT NOT NULL DEFAULT 0,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_weak_point IS '薄弱知识点表，存储学生的薄弱知识点信息';
COMMENT ON COLUMN tbl_mastery_weak_point.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_weak_point.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_weak_point.knowledge_point_id IS '知识点ID，引用内容平台的知识点';
COMMENT ON COLUMN tbl_mastery_weak_point.current_mastery IS '当前掌握度，范围[0,1]';
COMMENT ON COLUMN tbl_mastery_weak_point.target_mastery IS '目标掌握度，百分比表示，范围[0,100]';
COMMENT ON COLUMN tbl_mastery_weak_point.gap_ratio IS '差距比例，计算公式：1 - (current_mastery / (target_mastery/100))';
COMMENT ON COLUMN tbl_mastery_weak_point.analysis_time IS '分析生成时间';
COMMENT ON COLUMN tbl_mastery_weak_point.recommendation_priority IS '推荐优先级 (0:普通, 1:低优先级, 2:中优先级, 3:高优先级)';
COMMENT ON COLUMN tbl_mastery_weak_point.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_weak_point.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_weak_point.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_weak_point_user_knowledge ON tbl_mastery_weak_point(user_id, knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_weak_point_user_id ON tbl_mastery_weak_point(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_weak_point_knowledge_point_id ON tbl_mastery_weak_point(knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_weak_point_recommendation_priority ON tbl_mastery_weak_point(recommendation_priority) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_weak_point_gap_ratio ON tbl_mastery_weak_point(gap_ratio DESC) WHERE deleted_at IS NULL;
```

## 4. 数据库表详细设计 - ClickHouse

### 4.1 表名: `tbl_mastery_answer_record_history` - 答题记录历史表

#### 4.1.1 表功能说明
tbl_mastery_answer_record_history 表用于存储历史答题记录数据，是 PostgreSQL 中 tbl_mastery_answer_record 表的历史数据归档表。由于答题记录会随着时间不断增长，为了保证查询性能和降低存储成本，将历史答题记录迁移到 ClickHouse 中进行存储和分析。该表主要用于数据分析和统计，如学生答题趋势分析、题目难度分析等。

#### 4.1.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_answer_record_history
(
    id UInt64,
    user_id String,
    question_id String,
    course_id String,
    task_id UInt64,
    knowledge_point_ids String,
    answer_result UInt8,
    answer_time DateTime,
    answer_duration UInt32,
    attempt_count UInt8,
    mastery_increment Float32,
    created_at DateTime,
    event_date Date DEFAULT toDate(answer_time)
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, question_id)
TTL event_date + INTERVAL 3 YEAR
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE tbl_mastery_answer_record_history IS '答题记录历史表，存储学生的历史答题数据';
```

### 4.2 表名: `tbl_mastery_knowledge_point_mastery_history` - 知识点掌握度历史表

#### 4.2.1 表功能说明
tbl_mastery_knowledge_point_mastery_history 表用于存储知识点掌握度的历史变化数据。该表记录了学生对知识点掌握度的变化过程，可用于分析学生的学习进度和效果，以及掌握度计算策略的有效性。通过这些历史数据，可以绘制掌握度变化曲线，评估学习效果。

#### 4.2.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_knowledge_point_mastery_history
(
    id UInt64,
    user_id String,
    knowledge_point_id String,
    mastery_value Float32,
    answer_count UInt32,
    is_weak_point UInt8,
    update_reason UInt8,
    update_time DateTime,
    event_date Date DEFAULT toDate(update_time)
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, knowledge_point_id, update_time)
TTL event_date + INTERVAL 3 YEAR
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE tbl_mastery_knowledge_point_mastery_history IS '知识点掌握度历史表，存储学生对知识点掌握度的历史变化数据';
```

### 4.3 表名: `tbl_mastery_externalized_mastery_history` - 外化掌握度历史表

#### 4.3.1 表功能说明
tbl_mastery_externalized_mastery_history 表用于存储外化掌握度的历史变化数据。该表记录了学生对课程外化掌握度的变化过程，可用于分析学生的学习进度和效果，以及外化掌握度计算策略的有效性。通过这些历史数据，可以绘制外化掌握度变化曲线，评估学习效果和学习路径的合理性。

#### 4.3.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_externalized_mastery_history
(
    id UInt64,
    user_id String,
    course_id String,
    externalized_mastery_value Float32,
    update_source UInt8,
    update_time DateTime,
    event_date Date DEFAULT toDate(update_time)
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, course_id, update_time)
TTL event_date + INTERVAL 3 YEAR
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE tbl_mastery_externalized_mastery_history IS '外化掌握度历史表，存储学生对课程外化掌握度的历史变化数据';
```

## 5. 表间关系与 ER 图

### 5.1 表间关系描述

掌握度策略系统的数据库表之间存在以下关联关系：

1. **用户与知识点掌握度关系**：
   - tbl_mastery_knowledge_point_mastery 表通过 user_id 字段关联用户中心的用户，通过 knowledge_point_id 字段关联内容平台的知识点。
   - 一个用户可以有多个知识点掌握度记录，一个知识点可以被多个用户掌握，形成多对多关系。

2. **用户与外化掌握度关系**：
   - tbl_mastery_externalized_mastery 表通过 user_id 字段关联用户中心的用户，通过 course_id 字段关联内容平台的课程。
   - 一个用户可以有多个课程的外化掌握度，一个课程可以被多个用户学习，形成多对多关系。

3. **用户与目标掌握度关系**：
   - tbl_mastery_target_mastery 表通过 user_id 字段关联用户中心的用户，通过 course_id 字段关联内容平台的课程。
   - 一个用户可以有多个课程的目标掌握度，一个课程可以被多个用户设置目标，形成多对多关系。
   - tbl_mastery_target_mastery 表通过 learning_goal_id 字段关联 tbl_mastery_learning_goal 表，表示目标掌握度是基于哪个学习目标计算的。

4. **用户与学习目标关系**：
   - tbl_mastery_learning_goal 表通过 user_id 字段关联用户中心的用户。
   - 一个用户可以设置多个学习目标，形成一对多关系。

5. **用户与答题记录关系**：
   - tbl_mastery_answer_record 表通过 user_id 字段关联用户中心的用户，通过 question_id 字段关联内容平台的题目，通过 course_id 字段关联内容平台的课程。
   - 一个用户可以有多个答题记录，形成一对多关系。
   - tbl_mastery_answer_record 表通过 task_id 字段关联 tbl_mastery_learning_task 表，表示答题是在哪个学习任务中完成的。

6. **学习任务与题目关系**：
   - tbl_mastery_learning_task_question_rel 表是学习任务和题目的关联表，通过 task_id 字段关联 tbl_mastery_learning_task 表，通过 question_id 字段关联内容平台的题目。
   - 一个学习任务可以包含多个题目，一个题目可以属于多个学习任务，形成多对多关系。

7. **用户与薄弱知识点关系**：
   - tbl_mastery_weak_point 表通过 user_id 字段关联用户中心的用户，通过 knowledge_point_id 字段关联内容平台的知识点。
   - 一个用户可以有多个薄弱知识点，形成一对多关系。

8. **历史数据关系**：
   - ClickHouse 中的历史表（tbl_mastery_answer_record_history、tbl_mastery_knowledge_point_mastery_history、tbl_mastery_externalized_mastery_history）分别对应 PostgreSQL 中的实时表，用于存储历史数据。

### 5.2 ER 图 (使用 Mermaid 语法)

```mermaid
erDiagram
    USER ||--o{ tbl_mastery_knowledge_point_mastery : "has mastery of"
    USER ||--o{ tbl_mastery_externalized_mastery : "has externalized mastery of"
    USER ||--o{ tbl_mastery_target_mastery : "has target mastery of"
    USER ||--o{ tbl_mastery_learning_goal : "sets"
    USER ||--o{ tbl_mastery_answer_record : "submits"
    USER ||--o{ tbl_mastery_weak_point : "has"
    
    KNOWLEDGE_POINT ||--o{ tbl_mastery_knowledge_point_mastery : "is mastered by"
    KNOWLEDGE_POINT ||--o{ tbl_mastery_weak_point : "can be weak point for"
    
    COURSE ||--o{ tbl_mastery_externalized_mastery : "has externalized mastery"
    COURSE ||--o{ tbl_mastery_target_mastery : "has target mastery"
    COURSE ||--o{ tbl_mastery_learning_task : "contains"
    
    QUESTION ||--o{ tbl_mastery_answer_record : "is answered in"
    QUESTION ||--o{ tbl_mastery_learning_task_question_rel : "belongs to"
    
    tbl_mastery_learning_task ||--o{ tbl_mastery_learning_task_question_rel : "contains"
    tbl_mastery_learning_task ||--o{ tbl_mastery_answer_record : "generates"
    
    tbl_mastery_learning_goal ||--o{ tbl_mastery_target_mastery : "determines"
    
    tbl_mastery_knowledge_point_mastery ||--o{ tbl_mastery_knowledge_point_mastery_history : "archives to"
    tbl_mastery_answer_record ||--o{ tbl_mastery_answer_record_history : "archives to"
    tbl_mastery_externalized_mastery ||--o{ tbl_mastery_externalized_mastery_history : "archives to"
    
    tbl_mastery_calculation_parameter ||..o{ tbl_mastery_knowledge_point_mastery : "configures calculation of"
    tbl_mastery_calculation_parameter ||..o{ tbl_mastery_externalized_mastery : "configures calculation of"
    tbl_mastery_calculation_parameter ||..o{ tbl_mastery_target_mastery : "configures calculation of"
    
    tbl_mastery_knowledge_point_mastery ||--o{ tbl_mastery_weak_point : "determines"
    tbl_mastery_target_mastery ||--o{ tbl_mastery_weak_point : "determines"
    
    tbl_mastery_learning_task {
        bigint id PK
        varchar task_name
        smallint task_type
        varchar course_id FK
        smallint completion_threshold
        smallint status
        timestamptz created_at
        timestamptz updated_at
        timestamptz deleted_at
        bigint created_by
        bigint updated_by
    }
    
    tbl_mastery_learning_task_question_rel {
        bigint id PK
        bigint task_id FK
        varchar question_id FK
        smallint question_order
        timestamptz created_at
        timestamptz updated_at
        timestamptz deleted_at
        bigint created_by
        bigint updated_by
    }
    
    tbl_mastery_learning_goal {
        bigint id PK
        varchar user_id FK
        smallint goal_type
        varchar goal_value
        numeric goal_difficulty_factor
        varchar subject_ids
        smallint status
        timestamptz created_at
        timestamptz updated_at
        timestamptz deleted_at
    }
    
    tbl_mastery_knowledge_point_mastery {
        bigint id PK
        varchar user_id FK
        varchar knowledge_point_id FK
        numeric mastery_value
        integer answer_count
        boolean is_weak_point
        timestamptz last_updated_at
        smallint update_reason
        timestamptz created_at
        timestamptz updated_at
        timestamptz deleted_at
    }
    
    tbl_mastery_externalized_mastery {
        bigint id PK
        varchar user_id FK
        varchar course_id FK
        numeric externalized_mastery_value
        integer cumulative_answer_count
        timestamptz last_updated_at
        smallint update_source
        timestamptz created_at
        timestamptz updated_at
        timestamptz deleted_at
    }
    
    tbl_mastery_target_mastery {
        bigint id PK
        varchar user_id FK
        varchar course_id FK
        numeric target_mastery_value
        bigint learning_goal_id FK
        timestamptz created_at
        timestamptz updated_at
        timestamptz deleted_at
    }
    
    tbl_mastery_answer_record {
        bigint id PK
        varchar user_id FK
        varchar question_id FK
        varchar course_id FK
        bigint task_id FK
        varchar knowledge_point_ids
        smallint answer_result
        timestamptz answer_time
        integer answer_duration
        integer attempt_count
        numeric mastery_increment
        timestamptz created_at
        timestamptz updated_at
        timestamptz deleted_at
    }
    
    tbl_mastery_calculation_parameter {
        bigint id PK
        varchar param_type
        varchar param_key
        varchar param_value
        text param_desc
        varchar applicable_scope
        varchar scope_id
        timestamptz effective_time
        timestamptz expire_time
        smallint status
        timestamptz created_at
        timestamptz updated_at
        timestamptz deleted_at
        bigint created_by
        bigint updated_by
    }
    
    tbl_mastery_weak_point {
        bigint id PK
        varchar user_id FK
        varchar knowledge_point_id FK
        numeric current_mastery
        numeric target_mastery
        numeric gap_ratio
        timestamptz analysis_time
        smallint recommendation_priority
        timestamptz created_at
        timestamptz updated_at
        timestamptz deleted_at
    }
```

## 6. 核心用户场景/用户故事与数据操作分析

### 6.1 场景/故事: 学生答题后实时更新知识点掌握度

#### 6.1.1 场景描述
学生通过客户端提交题目答案，系统需要实时更新其对相关知识点的掌握度。系统根据题目难度、答题结果（正确、部分正确、错误）、当前掌握度等因素，计算掌握度增量，并更新累计掌握度。

#### 6.1.2 涉及的主要数据表
- tbl_mastery_answer_record：记录学生的答题情况
- tbl_mastery_knowledge_point_mastery：存储和更新学生的知识点掌握度
- tbl_mastery_calculation_parameter：获取掌握度计算参数

#### 6.1.3 数据操作流程 (CRUD 分析)
1. **创建操作 (C)**: 创建答题记录，插入 tbl_mastery_answer_record 表
2. **读取操作 (R)**: 从 tbl_mastery_knowledge_point_mastery 表读取当前掌握度
3. **更新操作 (U)**: 更新 tbl_mastery_knowledge_point_mastery 表中的掌握度值
4. **异步操作**: 将答题记录归档到 tbl_mastery_answer_record_history 表

#### 6.1.4 性能考量与索引支持
- 使用Redis缓存热点掌握度数据
- 联合唯一索引(user_id, knowledge_point_id)支持快速定位和更新
- 使用乐观锁处理并发更新

### 6.2 场景/故事: 学生完成学习单元后更新外化掌握度

#### 6.2.1 场景描述
学生完成一个学习单元（如AI课、巩固练习、作业、测验）后，系统需要更新其对该课程的外化掌握度。外化掌握度遵循"只增不降"原则。

#### 6.2.2 涉及的主要数据表
- tbl_mastery_learning_task：记录学习任务的完成状态
- tbl_mastery_knowledge_point_mastery：获取知识点掌握度
- tbl_mastery_externalized_mastery：存储和更新外化掌握度

#### 6.2.3 数据操作流程 (CRUD 分析)
1. **更新操作 (U)**: 更新 tbl_mastery_learning_task 表中的完成状态
2. **读取操作 (R)**: 从 tbl_mastery_knowledge_point_mastery 表读取相关知识点掌握度
3. **更新操作 (U)**: 更新 tbl_mastery_externalized_mastery 表中的外化掌握度值
4. **异步操作**: 将外化掌握度变化归档到历史表

#### 6.2.4 性能考量与索引支持
- 联合唯一索引(user_id, course_id)支持快速定位和更新
- 缓存课程与知识点的关联关系
- 使用事务确保外化掌握度更新的原子性

### 6.3 场景/故事: 学生设定学习目标后计算目标掌握度

#### 6.3.1 场景描述
学生设定或修改学习目标（如目标院校、年级排名）后，系统需要计算其在每节课应达到的目标掌握度。

#### 6.3.2 涉及的主要数据表
- tbl_mastery_learning_goal：记录学生的学习目标
- tbl_mastery_target_mastery：存储每节课的目标掌握度
- tbl_mastery_calculation_parameter：获取目标难度系数等参数

#### 6.3.3 数据操作流程 (CRUD 分析)
1. **创建操作 (C)**: 创建学习目标记录，插入 tbl_mastery_learning_goal 表
2. **读取操作 (R)**: 从内容平台获取课程信息，从参数表读取计算系数
3. **创建/更新操作 (C/U)**: 对每个课程创建或更新 tbl_mastery_target_mastery 表记录
4. **事件发布**: 发布目标掌握度更新事件，触发薄弱知识点判断

#### 6.3.4 性能考量与索引支持
- 使用批量插入或更新操作
- 联合唯一索引(user_id, course_id)支持快速定位和更新
- 缓存计算参数和课程信息

### 6.4 场景/故事: 系统判断学生薄弱知识点

#### 6.4.1 场景描述
在学生的知识点掌握度或目标掌握度更新后，系统需要判断是否存在薄弱知识点。判断标准为：知识点掌握度/目标掌握度≤0.7且知识点掌握度≤0.5。

#### 6.4.2 涉及的主要数据表
- tbl_mastery_knowledge_point_mastery：获取学生的知识点掌握度
- tbl_mastery_target_mastery：获取学生的目标掌握度
- tbl_mastery_weak_point：存储学生的薄弱知识点信息

#### 6.4.3 数据操作流程 (CRUD 分析)
1. **读取操作 (R)**: 从掌握度表和目标掌握度表读取数据
2. **计算处理**: 计算掌握度与目标掌握度的比值，判断薄弱知识点条件
3. **创建/更新操作 (C/U)**: 对满足条件的知识点创建或更新薄弱知识点记录
4. **更新操作 (U)**: 更新知识点掌握度表中的 is_weak_point 字段
5. **外部系统交互**: 将薄弱知识点信息推送给推荐引擎

#### 6.4.4 性能考量与索引支持
- 联合唯一索引(user_id, knowledge_point_id)支持快速定位和更新
- 使用批量操作减少数据库交互
- 增量处理，只处理发生变化的知识点

## 7. 设计检查清单 (Design Checklist)

### 7.1 需求与架构对齐性检查

| 需求点 | 数据库设计支持情况 | 备注 |
| ------ | ------------------ | ---- |
| 学习任务管理 | ✅ 支持 | 通过 tbl_mastery_learning_task 和 tbl_mastery_learning_task_question_rel 表支持完整的学习任务管理 |
| 学习目标设定 | ✅ 支持 | 通过 tbl_mastery_learning_goal 表支持学习目标的设定和管理 |
| 知识点掌握度计算与管理 | ✅ 支持 | 通过 tbl_mastery_knowledge_point_mastery 表存储掌握度数据，支持各种场景下的掌握度计算 |
| 外化掌握度计算与更新 | ✅ 支持 | 通过 tbl_mastery_externalized_mastery 表存储外化掌握度，支持"只增不降"原则 |
| 目标掌握度计算 | ✅ 支持 | 通过 tbl_mastery_target_mastery 表支持目标掌握度计算和管理 |
| 薄弱知识点判断 | ✅ 支持 | 通过 tbl_mastery_weak_point 表存储薄弱知识点信息，支持推荐算法 |
| 答题记录管理 | ✅ 支持 | 通过 tbl_mastery_answer_record 表记录答题情况，支持掌握度计算 |
| 算法参数配置 | ✅ 支持 | 通过 tbl_mastery_calculation_parameter 表支持灵活的参数配置管理 |
| 历史数据分析 | ✅ 支持 | 通过 ClickHouse 中的历史表存储历史数据，支持数据分析和统计 |

### 7.2 数据库设计质量检查

| 检查维度 | 评估结果 | 说明 |
| -------- | -------- | ---- |
| **表结构设计** | ✅ 良好 | 表结构清晰，字段定义合理，符合业务需求 |
| **命名规范** | ✅ 符合 | 表名和字段名符合规范，使用下划线命名法，表名前缀统一 |
| **字段类型选择** | ✅ 合理 | 根据数据特性选择了合适的字段类型，如掌握度使用 NUMERIC(4, 3) |
| **索引设计** | ✅ 完善 | 为查询频繁的字段创建了索引，使用了联合索引和条件索引 |
| **约束设计** | ✅ 合理 | 使用了主键约束、非空约束，确保数据完整性 |
| **关系设计** | ✅ 合理 | 表间关系清晰，使用逻辑外键关联，避免了物理外键约束 |
| **数据库选型** | ✅ 合适 | PostgreSQL 存储核心业务数据，ClickHouse 存储历史数据 |
| **扩展性** | ✅ 良好 | 表设计考虑了未来扩展，参数配置表支持灵活配置 |
| **性能考量** | ✅ 充分 | 考虑了高并发场景，设计了合适的索引和缓存策略 |
| **安全性** | ✅ 考虑 | 使用软删除机制，保留数据变更历史 |
| **可维护性** | ✅ 良好 | 表结构清晰，字段有详细注释，便于理解和维护 |

## 8. 参考资料与附录

- 产品需求文档 (PRD): be-s3-1-ai-de-掌握度策略-claude-sonnet-4.md
- 技术架构方案 (TD): be-s2-1-ai-td-掌握度策略-claude-sonnet-4.md
- PostgreSQL 设计规范与约束文件: rules-postgresql-code-v2.md
- ClickHouse 设计规范与约束文件: rules-clickhouse-code-v2.md
- 数据库设计模板: be-s4-2-ai-dd-template-v1.3.md
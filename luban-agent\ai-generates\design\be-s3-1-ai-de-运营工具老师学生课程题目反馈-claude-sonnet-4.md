# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** 产品需求文档：运营工具_老师&学生课程题目反馈系统 v9.0.0
- **PRD版本号：** v9.0.0
- **提取日期：** 2025-05-30

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 反馈记录 | 用户提交的课程、题目、视频等问题反馈信息 | 2.1, 4.1, 4.2 |
| 用户 | 提交反馈的教师端和学生端用户（引用公共服务） | 1.1, 2.1 |
| 课程 | 用户反馈涉及的AI课程内容（引用公共服务） | 2.1, 4.1 |
| 题目 | 用户反馈涉及的题目内容（引用公共服务） | 2.1, 4.2 |
| 反馈处理记录 | 生产后台对反馈的处理状态和结果 | 2.1, 4.3 |
| 积分记录 | 教师用户因有效反馈获得的积分奖励 | 3, 4.4 |
| 消息通知 | 反馈处理结果的通知消息 | 2.1, 4.4 |
| Agent审核记录 | 智能Agent对反馈的自动审核结果 | 2.2, 5.2 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`反馈记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 反馈ID | 反馈记录的唯一标识符 | 整数/字符串 | 唯一, 非空 | 系统自动生成 | 主键 | 4.1, 4.2 |
| 用户ID | 提交反馈的用户标识（引用公共服务） | 整数 | 非空, 外键 |  | 外键 | 4.1, 4.2 |
| 用户类型 | 区分教师端或学生端用户 | 枚举型 | teacher/student | | | 2.1 |
| 反馈类型 | 反馈问题的分类 | 枚举型 | 课程反馈/题目反馈/视频MV反馈/其他建议反馈/其他bug反馈 | | | 2.1, 3 |
| 内容类型 | 区分文字类和非文字类内容 | 枚举型 | 文字类内容/非文字类内容 | | | 3 |
| 课程ID | 反馈涉及的课程标识（引用公共服务） | 整数 | 外键，可为空 | | 外键 | 4.1 |
| 题目ID | 反馈涉及的题目标识（引用公共服务） | 整数 | 外键，可为空 | | 外键 | 4.2 |
| 反馈内容 | 用户提交的文字描述 | 文本型 | 非空 | | | 4.1, 4.2 |
| 截图信息 | 用户上传或系统自动截取的图片 | 文本型(URL) | 可为空 | | | 4.1, 4.2 |
| 提交时间 | 反馈提交的时间戳 | 时间戳 | 非空 | 当前时间 | | 4.1, 4.2 |
| 状态 | 反馈的处理状态 | 枚举型 | 待处理/处理中/已采纳/未采纳 | 待处理 | | 4.3 |

**实体：`反馈处理记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 | 初始值/默认值 (源自PRD) | 是否为标识符? | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 处理ID | 处理记录的唯一标识符 | 整数 | 唯一, 非空 | 系统自动生成 | 主键 | 4.3 |
| 反馈ID | 关联的反馈记录 | 整数 | 非空, 外键 | | 外键 | 4.3 |
| 处理人ID | 处理反馈的生产人员ID | 整数 | 非空 | | | 4.3 |
| 处理结果 | 是否采纳反馈 | 布尔型 | true/false | | | 4.3 |
| 处理说明 | 采纳或未采纳的原因说明 | 文本型 | 可为空 | | | 4.4 |
| 处理时间 | 处理完成的时间戳 | 时间戳 | 非空 | 当前时间 | | 4.3 |
| 修改内容 | 对课程/题目的具体修改说明 | 文本型 | 可为空 | | | 4.3 |

**实体：`积分记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 | 初始值/默认值 (源自PRD) | 是否为标识符? | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 积分记录ID | 积分记录的唯一标识符 | 整数 | 唯一, 非空 | 系统自动生成 | 主键 | 3, 4.4 |
| 用户ID | 获得积分的教师用户ID | 整数 | 非空, 外键 | | 外键 | 3, 4.4 |
| 反馈ID | 关联的反馈记录 | 整数 | 非空, 外键 | | 外键 | 3, 4.4 |
| 积分数量 | 获得的积分数量 | 整数 | 大于0 | PRD未明确具体数值 | | 3 |
| 问题严重程度 | 影响积分数量的问题严重程度 | 枚举型 | 轻微/一般/严重 | | | 3 |
| 下发时间 | 积分下发的时间戳 | 时间戳 | 非空 | 当前时间 | | 4.4 |
| 下发人ID | 运营下发积分的人员ID | 整数 | 非空 | | | 1.1 |

**实体：`消息通知`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 | 初始值/默认值 (源自PRD) | 是否为标识符? | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 消息ID | 消息通知的唯一标识符 | 整数 | 唯一, 非空 | 系统自动生成 | 主键 | 2.1, 4.4 |
| 用户ID | 接收消息的用户ID | 整数 | 非空, 外键 | | 外键 | 4.4 |
| 反馈ID | 关联的反馈记录 | 整数 | 非空, 外键 | | 外键 | 4.4 |
| 消息类型 | 消息的类型分类 | 枚举型 | 反馈确认/处理结果通知 | | | 4.1, 4.4 |
| 消息标题 | 消息的标题 | 字符串 | 非空 | | | 4.4 |
| 消息内容 | 消息的详细内容 | 文本型 | 非空 | | | 4.4 |
| 发送时间 | 消息发送的时间戳 | 时间戳 | 非空 | 当前时间 | | 4.4 |
| 是否已读 | 用户是否已读消息 | 布尔型 | true/false | false | | 4.4 |

**实体：`Agent审核记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 | 初始值/默认值 (源自PRD) | 是否为标识符? | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 审核ID | Agent审核记录的唯一标识符 | 整数 | 唯一, 非空 | 系统自动生成 | 主键 | 2.2, 5.2 |
| 反馈ID | 关联的反馈记录 | 整数 | 非空, 外键 | | 外键 | 2.2, 5.2 |
| 工作流类型 | 使用的dify工作流类型 | 枚举型 | dify1工作流/dify2工作流/dify3工作流 | | | 5.2 |
| 审核结果 | Agent的审核结论 | 枚举型 | 有效反馈/无效反馈 | | | 2.2, 5.2 |
| 审核详情 | Agent的详细分析结果 | 文本型 | 可为空 | | | 5.2 |
| 审核时间 | Agent完成审核的时间戳 | 时间戳 | 非空 | 当前时间 | | 6.1 |
| 处理耗时 | Agent处理耗时（秒） | 数值型 | 应不超过5秒 | | | 6.1 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 用户提交多个反馈 | 用户 | 反馈记录 | 1:N | 反馈记录.用户ID -> 用户.ID | 4.1, 4.2 |
| 反馈关联课程内容 | 反馈记录 | 课程 | N:1 | 反馈记录.课程ID -> 课程.ID | 4.1 |
| 反馈关联题目内容 | 反馈记录 | 题目 | N:1 | 反馈记录.题目ID -> 题目.ID | 4.2 |
| 反馈有一个处理记录 | 反馈记录 | 反馈处理记录 | 1:1 | 反馈处理记录.反馈ID -> 反馈记录.ID | 4.3 |
| 反馈可产生积分记录 | 反馈记录 | 积分记录 | 1:1 | 积分记录.反馈ID -> 反馈记录.ID | 3, 4.4 |
| 用户获得多个积分记录 | 用户 | 积分记录 | 1:N | 积分记录.用户ID -> 用户.ID | 3, 4.4 |
| 反馈产生多个消息通知 | 反馈记录 | 消息通知 | 1:N | 消息通知.反馈ID -> 反馈记录.ID | 4.1, 4.4 |
| 用户接收多个消息通知 | 用户 | 消息通知 | 1:N | 消息通知.用户ID -> 用户.ID | 4.4 |
| 反馈有Agent审核记录 | 反馈记录 | Agent审核记录 | 1:1 | Agent审核记录.反馈ID -> 反馈记录.ID | 2.2, 5.2 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 反馈分类规则（文字类内容反馈） | 反馈内容, 课程/题目ID, 反馈类型 | Agent审核结果（有效/无效反馈） | 3 |
| 反馈分类规则（非文字类内容反馈） | 反馈类型, 截图信息, 课程/题目ID | 直接进入人工审核（无Agent输出） | 3 |
| 积分下发规则 | 反馈有效性, 问题严重程度, 问题类型 | 积分数量 | 3 |
| Agent审核流程 | 反馈信息, 原始内容（课程/题目） | 纠错分析结论 | 5.2 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 反馈记录.状态 | 用户提交反馈、生产人员处理反馈 | 实时更新 | 4.1, 4.2, 4.3 |
| Agent审核记录 | 文字类内容反馈提交后自动触发 | 实时，5秒内完成 | 2.2, 5.2, 6.1 |
| 反馈处理记录 | 生产人员完成反馈审核和处理 | 每次处理完成 | 4.3 |
| 积分记录 | 反馈被采纳且确认有效 | 每次有效反馈采纳 | 3, 4.4 |
| 消息通知 | 反馈提交确认、反馈处理完成 | 实时推送 | 4.1, 4.4 |
| 消息通知.是否已读 | 用户查看消息中心 | 用户操作时更新 | 4.4 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 用户基本信息 | 从公共服务-用户中心获取（用户ID、用户类型、学校信息等） | 1.1, 引用文件-公共服务 |
| 课程内容信息 | 从公共服务-内容平台获取（课程ID、课程名称等） | 4.1, 引用文件-公共服务 |
| 题目内容信息 | 从公共服务-内容平台获取（题目ID、题干、答案、解析等） | 4.2, 引用文件-公共服务 |
| 反馈内容 | 用户在教师端/学生端界面直接输入 | 4.1, 4.2 |
| 截图信息 | 系统自动截取当前页面或用户上传 | 4.1, 4.2 |
| 处理人员信息 | 生产后台管理人员身份信息 | 4.3 |

---

## 7. 当前版本明确排除的数据需求

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 | PRD中说明的排除原因 | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 积分历史&消费功能 | 建议后续迭代再考虑 | 2.3 |
| 奖品下发功能 | 建议后续迭代再考虑 | 2.3 |
| 积分兑换实物奖品的数据结构 | 当前版本不涉及积分消费 | 2.3 |
| 反馈质量评估机制的数据存储 | 下一个迭代重点 | 9.2 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别反馈记录、用户、课程、题目、处理记录、积分记录、消息通知、Agent审核记录 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏？                                                         | ✅ | 已通过业务流程分析补充了Agent审核记录实体 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已提取所有关键属性，包括状态流转、时间戳、关联关系等 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ⚠️ | 积分具体数量PRD未明确，标记为"PRD未明确具体数值" |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确所有实体间的关联关系和外键指向 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 已体现反馈->处理->积分->通知的完整依赖链 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 已列出反馈分类、积分下发、Agent审核的所有输入项 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 已记录所有数据更新触发条件和外部数据来源 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 已记录积分消费、奖品下发等排除功能 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 与PRD中的术语保持一致 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 所有提取项都标注了PRD章节号 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 所有信息均基于PRD原文，推断部分已标记 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 积分具体数量需与PM确认；Agent审核准确率阈值需进一步明确 |

---
# 数据库设计文档 - 掌握度策略 V1.0

技术架构提取摘要 (TD提取摘要)：be-s2-1-ai-td-掌握度策略-V1.0-claude-3.7.md
数据实体设计文档 (PRD核心摘要)：be-s3-2-ai-dd-掌握度策略-V1.0-claude-3.7.md
公共服务：common.md, ucenter.md, admin.md, question.md, teacher.md
设计模板：be-s3-4-ai-dd-template-v1.2.md
数据库设计规范：rules-postgresql-code-v2.md, rules-clickhouse-code-v2.md
模型：claude-3.7
日期：2025-05-22
输出文件：ai-generates/design/be-s3-4-ai-dd-掌握度策略-V1.0-claude-3.7.md

## 1. 数据库表清单

| 序号 | 表名 | 数据库类型 | 核心功能/用途简述 |
| --- | --- | --- | --- |
| 1 | `tbl_mastery_learning_task` | PostgreSQL | 存储学习任务(AI课、巩固练习等)信息 |
| 2 | `tbl_mastery_learning_task_question_rel` | PostgreSQL | 存储学习任务与题目的多对多关联关系 |
| 3 | `tbl_mastery_learning_goal` | PostgreSQL | 存储学生设定的学习目标 |
| 4 | `tbl_mastery_knowledge_point_mastery` | PostgreSQL | 存储学生对知识点的掌握度数据 |
| 5 | `tbl_mastery_externalized_mastery` | PostgreSQL | 存储学生对课程的外化掌握度数据 |
| 6 | `tbl_mastery_target_mastery` | PostgreSQL | 存储学生对课程的目标掌握度数据 |
| 7 | `tbl_mastery_answer_record` | PostgreSQL | 存储学生的答题记录 |
| 8 | `tbl_mastery_calculation_parameter` | PostgreSQL | 存储掌握度计算相关的参数配置 |
| 9 | `tbl_mastery_weak_point` | PostgreSQL | 存储学生的薄弱知识点信息 |
| 10 | `tbl_mastery_answer_record_history` | ClickHouse | 存储历史答题记录数据，用于分析 |
| 11 | `tbl_mastery_knowledge_point_mastery_history` | ClickHouse | 存储知识点掌握度历史变化数据，用于分析 |
| 12 | `tbl_mastery_externalized_mastery_history` | ClickHouse | 存储外化掌握度历史变化数据，用于分析 |

## 2. 表间关系描述

本系统的核心实体间关系如下：

1. 学习任务(tbl_mastery_learning_task)与课程是多对一关系，一个课程可以有多个学习任务（课程信息由内容平台提供）
2. 学习任务(tbl_mastery_learning_task)与题目是多对多关系，通过关联表(tbl_mastery_learning_task_question_rel)实现（题目信息由内容平台提供）
3. 学习目标(tbl_mastery_learning_goal)与用户是多对一关系，一个用户可以设置多个学习目标（用户信息由用户中心提供）
4. 知识点掌握度(tbl_mastery_knowledge_point_mastery)是用户与知识点的多对多关系，记录用户对知识点的掌握程度（用户信息由用户中心提供，知识点信息由内容平台提供）
5. 外化掌握度(tbl_mastery_externalized_mastery)是用户与课程的多对多关系，记录用户对课程的外化掌握程度（用户信息由用户中心提供，课程信息由内容平台提供）
6. 目标掌握度(tbl_mastery_target_mastery)是用户与课程的多对多关系，记录用户对课程的目标掌握程度（用户信息由用户中心提供，课程信息由内容平台提供）
7. 答题记录(tbl_mastery_answer_record)与用户、题目、课程、学习任务都有关联关系（用户信息由用户中心提供，题目和课程信息由内容平台提供）

## 3. 数据库表详细设计 - PostgreSQL

### 3.1 tbl_mastery_learning_task 表

#### 3.1.1 表功能说明

tbl_mastery_learning_task 表用于存储学习任务的基本信息，包括AI课、巩固练习、作业、测验等不同类型的学习任务。学习任务是学生学习过程中的基本单元，与课程关联，包含多个题目。学习任务的完成会触发外化掌握度的更新。

#### 3.1.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_learning_task (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  task_name VARCHAR(255) NOT NULL,
  task_type SMALLINT NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  completion_threshold SMALLINT NOT NULL DEFAULT 5,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_learning_task IS '学习任务表，存储AI课、巩固练习、作业、测验等学习任务信息';
COMMENT ON COLUMN tbl_mastery_learning_task.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_learning_task.task_name IS '学习任务名称';
COMMENT ON COLUMN tbl_mastery_learning_task.task_type IS '学习任务类型 (10:AI课, 20:巩固练习, 30:作业, 40:测验)';
COMMENT ON COLUMN tbl_mastery_learning_task.course_id IS '关联的课程ID，引用内容平台的课程';
COMMENT ON COLUMN tbl_mastery_learning_task.completion_threshold IS '完成阈值，表示需要完成多少题目才算完成任务，默认为5题';
COMMENT ON COLUMN tbl_mastery_learning_task.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_learning_task.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_learning_task.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_learning_task.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_mastery_learning_task.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_mastery_learning_task.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_mastery_learning_task_course_id ON tbl_mastery_learning_task(course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_learning_task_task_type ON tbl_mastery_learning_task(task_type) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_learning_task_status ON tbl_mastery_learning_task(status) WHERE deleted_at IS NULL;
```

### 3.2 tbl_mastery_learning_task_question_rel 表

#### 3.2.1 表功能说明

tbl_mastery_learning_task_question_rel 表用于存储学习任务与题目的多对多关联关系。一个学习任务可以包含多个题目，一个题目也可以被多个学习任务使用。该表通过关联学习任务ID和题目ID，建立它们之间的映射关系，便于系统在学生完成学习任务时，能够准确地获取相关题目，并计算掌握度。

#### 3.2.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_learning_task_question_rel (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  task_id BIGINT NOT NULL,
  question_id VARCHAR(64) NOT NULL,
  question_order SMALLINT NOT NULL DEFAULT 0,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_learning_task_question_rel IS '学习任务与题目的关联表，建立多对多关系';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.task_id IS '学习任务ID，关联tbl_mastery_learning_task表';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.question_id IS '题目ID，引用内容平台的题目';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.question_order IS '题目在学习任务中的排序，从0开始';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_mastery_learning_task_question_rel.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_mastery_learning_task_question_rel_task_id ON tbl_mastery_learning_task_question_rel(task_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_learning_task_question_rel_question_id ON tbl_mastery_learning_task_question_rel(question_id) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX uk_mastery_learning_task_question_rel_task_question ON tbl_mastery_learning_task_question_rel(task_id, question_id) WHERE deleted_at IS NULL;
```

### 3.3 tbl_mastery_learning_goal 表

#### 3.3.1 表功能说明

tbl_mastery_learning_goal 表用于存储学生设定的学习目标信息。学习目标是学生在学习过程中设定的期望达到的成绩或水平，如高中阶段选择大学院校为目标，初中阶段选择年级排名为目标。系统会根据学生设定的目标，计算每节课应达到的目标掌握度，指导学生的学习方向。

#### 3.3.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_learning_goal (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  goal_type SMALLINT NOT NULL,
  goal_value VARCHAR(255) NOT NULL,
  goal_difficulty_factor NUMERIC(5, 2) NOT NULL,
  subject_ids VARCHAR(255) NOT NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_learning_goal IS '学习目标表，存储学生设定的学习目标';
COMMENT ON COLUMN tbl_mastery_learning_goal.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_learning_goal.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_learning_goal.goal_type IS '目标类型 (1:大学院校, 2:年级排名)';
COMMENT ON COLUMN tbl_mastery_learning_goal.goal_value IS '目标值，如大学院校名称或年级排名百分比';
COMMENT ON COLUMN tbl_mastery_learning_goal.goal_difficulty_factor IS '目标难度系数，根据目标值确定';
COMMENT ON COLUMN tbl_mastery_learning_goal.subject_ids IS '适用的学科ID列表，逗号分隔';
COMMENT ON COLUMN tbl_mastery_learning_goal.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_learning_goal.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_learning_goal.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_learning_goal.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE INDEX idx_mastery_learning_goal_user_id ON tbl_mastery_learning_goal(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_learning_goal_goal_type ON tbl_mastery_learning_goal(goal_type) WHERE deleted_at IS NULL;
```

### 3.4 tbl_mastery_knowledge_point_mastery 表

#### 3.4.1 表功能说明

tbl_mastery_knowledge_point_mastery 表是掌握度策略系统的核心表之一，用于存储学生对知识点的掌握程度数据。掌握度值范围为[0,1]，表示学生对知识点的理解和掌握程度。系统会根据学生的答题情况实时更新掌握度值，并用于计算外化掌握度和判断薄弱知识点。

#### 3.4.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_knowledge_point_mastery (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  knowledge_point_id VARCHAR(64) NOT NULL,
  mastery_value NUMERIC(4, 3) NOT NULL DEFAULT 0.3,
  answer_count INTEGER NOT NULL DEFAULT 0,
  is_weak_point BOOLEAN NOT NULL DEFAULT FALSE,
  last_updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_reason SMALLINT NOT NULL DEFAULT 0,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_knowledge_point_mastery IS '知识点掌握度表，存储学生对知识点的掌握程度';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.knowledge_point_id IS '知识点ID，引用内容平台的知识点';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.mastery_value IS '掌握度值，范围[0,1]，表示学生对知识点的掌握程度';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.answer_count IS '累计答题量，记录学生在该知识点上的答题次数';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.is_weak_point IS '是否为薄弱知识点，TRUE表示是薄弱知识点';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.last_updated_at IS '掌握度最后更新时间';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.update_reason IS '更新原因 (0:初始化, 1:答题, 2:错题重练, 3:自评主观题)';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_knowledge_point_mastery.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_knowledge_point_mastery_user_knowledge ON tbl_mastery_knowledge_point_mastery(user_id, knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_knowledge_point_mastery_user_id ON tbl_mastery_knowledge_point_mastery(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_knowledge_point_mastery_knowledge_point_id ON tbl_mastery_knowledge_point_mastery(knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_knowledge_point_mastery_is_weak_point ON tbl_mastery_knowledge_point_mastery(is_weak_point) WHERE deleted_at IS NULL AND is_weak_point = TRUE;
```

### 3.5 tbl_mastery_externalized_mastery 表

#### 3.5.1 表功能说明

tbl_mastery_externalized_mastery 表用于存储学生对课程的外化掌握度数据。外化掌握度是向学生展示的课程掌握程度，用百分比表示，是知识点掌握度的外部表现形式。外化掌握度遵循"只增不降"原则，即新计算的外化掌握度只有高于当前值时才会更新。学生完成学习单元（AI课、巩固练习、作业、测验）后，系统会更新外化掌握度。

#### 3.5.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_externalized_mastery (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  externalized_mastery_value NUMERIC(5, 2) NOT NULL DEFAULT 0,
  cumulative_answer_count INTEGER NOT NULL DEFAULT 0,
  last_updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  update_source SMALLINT NOT NULL DEFAULT 0,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_externalized_mastery IS '外化掌握度表，存储学生对课程的外化掌握度';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.course_id IS '课程ID，引用内容平台的课程';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.externalized_mastery_value IS '外化掌握度值，百分比表示，范围[0,100]';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.cumulative_answer_count IS '距离上次更新掌握度的累计答题量';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.last_updated_at IS '外化掌握度最后更新时间';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.update_source IS '更新来源 (10:AI课, 20:巩固练习, 30:作业, 40:测验)';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_externalized_mastery.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_externalized_mastery_user_course ON tbl_mastery_externalized_mastery(user_id, course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_externalized_mastery_user_id ON tbl_mastery_externalized_mastery(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_externalized_mastery_course_id ON tbl_mastery_externalized_mastery(course_id) WHERE deleted_at IS NULL;
```

### 3.6 tbl_mastery_target_mastery 表

#### 3.6.1 表功能说明

tbl_mastery_target_mastery 表用于存储学生对课程的目标掌握度数据。目标掌握度是基于学生学习目标，为每节课设定的期望掌握度，指导学生的学习方向。系统会根据学生设定的目标（如目标院校、年级排名），结合课程难度、考试频率等因素，计算每节课应达到的目标掌握度。

#### 3.6.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_target_mastery (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  target_mastery_value NUMERIC(5, 2) NOT NULL,
  learning_goal_id BIGINT NOT NULL,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_target_mastery IS '目标掌握度表，存储学生对课程的目标掌握度';
COMMENT ON COLUMN tbl_mastery_target_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_target_mastery.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_target_mastery.course_id IS '课程ID，引用内容平台的课程';
COMMENT ON COLUMN tbl_mastery_target_mastery.target_mastery_value IS '目标掌握度值，百分比表示，范围[0,100]';
COMMENT ON COLUMN tbl_mastery_target_mastery.learning_goal_id IS '学习目标ID，关联tbl_mastery_learning_goal表';
COMMENT ON COLUMN tbl_mastery_target_mastery.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_target_mastery.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_target_mastery.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_target_mastery_user_course ON tbl_mastery_target_mastery(user_id, course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_target_mastery_user_id ON tbl_mastery_target_mastery(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_target_mastery_course_id ON tbl_mastery_target_mastery(course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_target_mastery_learning_goal_id ON tbl_mastery_target_mastery(learning_goal_id) WHERE deleted_at IS NULL;
```

### 3.7 tbl_mastery_answer_record 表

#### 3.7.1 表功能说明

tbl_mastery_answer_record 表用于存储学生的答题记录数据。每条记录包含学生在特定学习任务中对特定题目的作答情况，包括答题结果、答题时间、作答时长、尝试次数等信息。这些数据是计算知识点掌握度的基础，也用于分析学生的学习行为和效果。

#### 3.7.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_answer_record (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  question_id VARCHAR(64) NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  task_id BIGINT NOT NULL,
  knowledge_point_ids VARCHAR(255) NOT NULL,
  answer_result SMALLINT NOT NULL,
  answer_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  answer_duration INTEGER NOT NULL DEFAULT 0,
  attempt_count INTEGER NOT NULL DEFAULT 1,
  mastery_increment NUMERIC(4, 3) NOT NULL,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_answer_record IS '答题记录表，存储学生的答题情况';
COMMENT ON COLUMN tbl_mastery_answer_record.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_answer_record.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_answer_record.question_id IS '题目ID，引用内容平台的题目';
COMMENT ON COLUMN tbl_mastery_answer_record.course_id IS '课程ID，引用内容平台的课程';
COMMENT ON COLUMN tbl_mastery_answer_record.task_id IS '学习任务ID，关联tbl_mastery_learning_task表';
COMMENT ON COLUMN tbl_mastery_answer_record.knowledge_point_ids IS '关联的知识点ID列表，逗号分隔';
COMMENT ON COLUMN tbl_mastery_answer_record.answer_result IS '答题结果 (1:正确, 2:部分正确, 3:错误)';
COMMENT ON COLUMN tbl_mastery_answer_record.answer_time IS '答题时间';
COMMENT ON COLUMN tbl_mastery_answer_record.answer_duration IS '作答时长，单位为秒';
COMMENT ON COLUMN tbl_mastery_answer_record.attempt_count IS '尝试次数，从1开始';
COMMENT ON COLUMN tbl_mastery_answer_record.mastery_increment IS '本次答题导致的知识点掌握度增量';
COMMENT ON COLUMN tbl_mastery_answer_record.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_answer_record.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_answer_record.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE INDEX idx_mastery_answer_record_user_id ON tbl_mastery_answer_record(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_answer_record_question_id ON tbl_mastery_answer_record(question_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_answer_record_course_id ON tbl_mastery_answer_record(course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_answer_record_task_id ON tbl_mastery_answer_record(task_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_answer_record_answer_time ON tbl_mastery_answer_record(answer_time) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_answer_record_user_question ON tbl_mastery_answer_record(user_id, question_id) WHERE deleted_at IS NULL;
```

### 3.8 tbl_mastery_calculation_parameter 表

#### 3.8.1 表功能说明

tbl_mastery_calculation_parameter 表用于存储掌握度计算相关的参数配置。这些参数包括掌握度初始值、掌握度增量计算系数、外化掌握度计算参数、目标掌握度计算参数等。通过集中管理这些参数，可以灵活调整掌握度计算策略，而无需修改代码。

#### 3.8.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_calculation_parameter (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  param_type VARCHAR(50) NOT NULL,
  param_key VARCHAR(100) NOT NULL,
  param_value VARCHAR(255) NOT NULL,
  param_desc TEXT NULL,
  applicable_scope VARCHAR(50) NOT NULL DEFAULT 'global',
  scope_id VARCHAR(64) NULL,
  effective_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  expire_time TIMESTAMPTZ NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_calculation_parameter IS '掌握度计算参数表，存储掌握度计算相关的参数配置';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.param_type IS '参数类型 (mastery_init:初始掌握度, mastery_increment:掌握度增量, externalized:外化掌握度, target:目标掌握度)';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.param_key IS '参数键名';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.param_value IS '参数值';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.param_desc IS '参数描述';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.applicable_scope IS '适用范围 (global:全局, subject:学科, grade:年级)';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.scope_id IS '范围ID，当applicable_scope不为global时使用';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.effective_time IS '参数生效时间';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.expire_time IS '参数失效时间，NULL表示永不失效';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_mastery_calculation_parameter.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_calculation_parameter_type_key_scope ON tbl_mastery_calculation_parameter(param_type, param_key, applicable_scope, COALESCE(scope_id, '')) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_calculation_parameter_param_type ON tbl_mastery_calculation_parameter(param_type) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_calculation_parameter_applicable_scope ON tbl_mastery_calculation_parameter(applicable_scope, scope_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_calculation_parameter_effective_time ON tbl_mastery_calculation_parameter(effective_time) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_calculation_parameter_expire_time ON tbl_mastery_calculation_parameter(expire_time) WHERE deleted_at IS NULL;
```

### 3.9 tbl_mastery_weak_point 表

#### 3.9.1 表功能说明

tbl_mastery_weak_point 表用于存储学生的薄弱知识点信息。薄弱知识点是掌握度显著低于目标的知识点，需要额外关注和练习。系统根据知识点掌握度与目标掌握度的比较，判断薄弱知识点，并提供给推荐引擎进行内容推荐。判断标准为：知识点掌握度/目标掌握度≤0.7且知识点掌握度≤0.5。

#### 3.9.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_weak_point (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id VARCHAR(64) NOT NULL,
  knowledge_point_id VARCHAR(64) NOT NULL,
  current_mastery NUMERIC(4, 3) NOT NULL,
  target_mastery NUMERIC(5, 2) NOT NULL,
  gap_ratio NUMERIC(4, 3) NOT NULL,
  analysis_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  recommendation_priority SMALLINT NOT NULL DEFAULT 0,
  -- 标准字段
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL
);

-- 添加表和字段的中文注释
COMMENT ON TABLE tbl_mastery_weak_point IS '薄弱知识点表，存储学生的薄弱知识点信息';
COMMENT ON COLUMN tbl_mastery_weak_point.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_weak_point.user_id IS '用户ID，引用用户中心的用户';
COMMENT ON COLUMN tbl_mastery_weak_point.knowledge_point_id IS '知识点ID，引用内容平台的知识点';
COMMENT ON COLUMN tbl_mastery_weak_point.current_mastery IS '当前掌握度，范围[0,1]';
COMMENT ON COLUMN tbl_mastery_weak_point.target_mastery IS '目标掌握度，百分比表示，范围[0,100]';
COMMENT ON COLUMN tbl_mastery_weak_point.gap_ratio IS '差距比例，计算公式：1 - (current_mastery / (target_mastery/100))';
COMMENT ON COLUMN tbl_mastery_weak_point.analysis_time IS '分析生成时间';
COMMENT ON COLUMN tbl_mastery_weak_point.recommendation_priority IS '推荐优先级 (0:普通, 1:低优先级, 2:中优先级, 3:高优先级)';
COMMENT ON COLUMN tbl_mastery_weak_point.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_weak_point.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_weak_point.deleted_at IS '软删除标记时间 (NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_weak_point_user_knowledge ON tbl_mastery_weak_point(user_id, knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_weak_point_user_id ON tbl_mastery_weak_point(user_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_weak_point_knowledge_point_id ON tbl_mastery_weak_point(knowledge_point_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_weak_point_recommendation_priority ON tbl_mastery_weak_point(recommendation_priority) WHERE deleted_at IS NULL;
CREATE INDEX idx_mastery_weak_point_gap_ratio ON tbl_mastery_weak_point(gap_ratio DESC) WHERE deleted_at IS NULL;
```

## 4. 数据库表详细设计 - ClickHouse

### 4.1 tbl_mastery_answer_record_history 表

#### 4.1.1 表功能说明

tbl_mastery_answer_record_history 表用于存储历史答题记录数据，是 PostgreSQL 中 tbl_mastery_answer_record 表的历史数据归档表。由于答题记录会随着时间不断增长，为了保证查询性能和降低存储成本，将历史答题记录迁移到 ClickHouse 中进行存储和分析。该表主要用于数据分析和统计，如学生答题趋势分析、题目难度分析等。

#### 4.1.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_answer_record_history
(
    id UInt64,
    user_id String,
    question_id String,
    course_id String,
    task_id UInt64,
    knowledge_point_ids String,
    answer_result UInt8,
    answer_time DateTime,
    answer_duration UInt32,
    attempt_count UInt8,
    mastery_increment Float32,
    created_at DateTime,
    event_date Date DEFAULT toDate(answer_time)
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, question_id)
TTL event_date + INTERVAL 3 YEAR
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE tbl_mastery_answer_record_history IS '答题记录历史表，存储学生的历史答题数据';
```

### 4.2 tbl_mastery_knowledge_point_mastery_history 表

#### 4.2.1 表功能说明

tbl_mastery_knowledge_point_mastery_history 表用于存储知识点掌握度的历史变化数据。该表记录了学生对知识点掌握度的变化过程，可用于分析学生的学习进度和效果，以及掌握度计算策略的有效性。通过这些历史数据，可以绘制掌握度变化曲线，评估学习效果。

#### 4.2.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_knowledge_point_mastery_history
(
    id UInt64,
    user_id String,
    knowledge_point_id String,
    mastery_value Float32,
    answer_count UInt32,
    is_weak_point UInt8,
    update_reason UInt8,
    update_time DateTime,
    event_date Date DEFAULT toDate(update_time)
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, knowledge_point_id, update_time)
TTL event_date + INTERVAL 3 YEAR
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE tbl_mastery_knowledge_point_mastery_history IS '知识点掌握度历史表，存储学生对知识点掌握度的历史变化数据';
```

### 4.3 tbl_mastery_externalized_mastery_history 表

#### 4.3.1 表功能说明

tbl_mastery_externalized_mastery_history 表用于存储外化掌握度的历史变化数据。该表记录了学生对课程外化掌握度的变化过程，可用于分析学生的学习进度和效果，以及外化掌握度计算策略的有效性。通过这些历史数据，可以绘制外化掌握度变化曲线，评估学习效果和学习路径的合理性。

#### 4.3.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_mastery_externalized_mastery_history
(
    id UInt64,
    user_id String,
    course_id String,
    externalized_mastery_value Float32,
    update_source UInt8,
    update_time DateTime,
    event_date Date DEFAULT toDate(update_time)
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, course_id, update_time)
TTL event_date + INTERVAL 3 YEAR
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE tbl_mastery_externalized_mastery_history IS '外化掌握度历史表，存储学生对课程外化掌握度的历史变化数据';
```

## 5. 表间关系与 ER 图

### 5.1 表间关系描述

掌握度策略系统的数据库表之间存在以下关联关系：

1. **用户与知识点掌握度关系**：
   - tbl_mastery_knowledge_point_mastery 表通过 user_id 字段关联用户中心的用户，通过 knowledge_point_id 字段关联内容平台的知识点。
   - 一个用户可以有多个知识点掌握度记录，一个知识点可以被多个用户掌握，形成多对多关系。

2. **用户与外化掌握度关系**：
   - tbl_mastery_externalized_mastery 表通过 user_id 字段关联用户中心的用户，通过 course_id 字段关联内容平台的课程。
   - 一个用户可以有多个课程的外化掌握度，一个课程可以被多个用户学习，形成多对多关系。

3. **用户与目标掌握度关系**：
   - tbl_mastery_target_mastery 表通过 user_id 字段关联用户中心的用户，通过 course_id 字段关联内容平台的课程。
   - 一个用户可以有多个课程的目标掌握度，一个课程可以被多个用户设置目标，形成多对多关系。
   - tbl_mastery_target_mastery 表通过 learning_goal_id 字段关联 tbl_mastery_learning_goal 表，表示目标掌握度是基于哪个学习目标计算的。

4. **用户与学习目标关系**：
   - tbl_mastery_learning_goal 表通过 user_id 字段关联用户中心的用户。
   - 一个用户可以设置多个学习目标，形成一对多关系。

5. **用户与答题记录关系**：
   - tbl_mastery_answer_record 表通过 user_id 字段关联用户中心的用户，通过 question_id 字段关联内容平台的题目，通过 course_id 字段关联内容平台的课程。
   - 一个用户可以有多个答题记录，形成一对多关系。
   - tbl_mastery_answer_record 表通过 task_id 字段关联 tbl_mastery_learning_task 表，表示答题是在哪个学习任务中完成的。

6. **学习任务与题目关系**：
   - tbl_mastery_learning_task_question_rel 表是学习任务和题目的关联表，通过 task_id 字段关联 tbl_mastery_learning_task 表，通过 question_id 字段关联内容平台的题目。
   - 一个学习任务可以包含多个题目，一个题目可以属于多个学习任务，形成多对多关系。

7. **用户与薄弱知识点关系**：
   - tbl_mastery_weak_point 表通过 user_id 字段关联用户中心的用户，通过 knowledge_point_id 字段关联内容平台的知识点。
   - 一个用户可以有多个薄弱知识点，形成一对多关系。

8. **历史数据关系**：
   - ClickHouse 中的历史表（tbl_mastery_answer_record_history、tbl_mastery_knowledge_point_mastery_history、tbl_mastery_externalized_mastery_history）分别对应 PostgreSQL 中的实时表，用于存储历史数据。

### 5.2 ER 图 (使用 Mermaid 语法)

```mermaid
erDiagram
    USER ||--o{ tbl_mastery_knowledge_point_mastery : "has mastery of"
    USER ||--o{ tbl_mastery_externalized_mastery : "has externalized mastery of"
    USER ||--o{ tbl_mastery_target_mastery : "has target mastery of"
    USER ||--o{ tbl_mastery_learning_goal : "sets"
    USER ||--o{ tbl_mastery_answer_record : "submits"
    USER ||--o{ tbl_mastery_weak_point : "has"
    
    KNOWLEDGE_POINT ||--o{ tbl_mastery_knowledge_point_mastery : "is mastered by"
    KNOWLEDGE_POINT ||--o{ tbl_mastery_weak_point : "can be weak point for"
    
    COURSE ||--o{ tbl_mastery_externalized_mastery : "has externalized mastery"
    COURSE ||--o{ tbl_mastery_target_mastery : "has target mastery"
    
    QUESTION ||--o{ tbl_mastery_answer_record : "is answered in"
    QUESTION ||--o{ tbl_mastery_learning_task_question_rel : "belongs to"
    
    tbl_mastery_learning_task ||--o{ tbl_mastery_learning_task_question_rel : "contains"
    tbl_mastery_learning_task ||--o{ tbl_mastery_answer_record : "generates"
    
    tbl_mastery_learning_goal ||--o{ tbl_mastery_target_mastery : "determines"
    
    tbl_mastery_knowledge_point_mastery ||--o{ tbl_mastery_knowledge_point_mastery_history : "archives to"
    tbl_mastery_answer_record ||--o{ tbl_mastery_answer_record_history : "archives to"
    tbl_mastery_externalized_mastery ||--o{ tbl_mastery_externalized_mastery_history : "archives to"
    
    tbl_mastery_calculation_parameter ||..o{ tbl_mastery_knowledge_point_mastery : "configures calculation of"
    tbl_mastery_calculation_parameter ||..o{ tbl_mastery_externalized_mastery : "configures calculation of"
    tbl_mastery_calculation_parameter ||..o{ tbl_mastery_target_mastery : "configures calculation of"
    
    tbl_mastery_knowledge_point_mastery ||--o{ tbl_mastery_weak_point : "determines"
    tbl_mastery_target_mastery ||--o{ tbl_mastery_weak_point : "determines"
    
    tbl_mastery_learning_task {
        bigint id PK
        varchar task_name
        smallint task_type
        varchar course_id FK
        timestamptz completed_at
        smallint completion_status
    }
    
    tbl_mastery_learning_task_question_rel {
        bigint id PK
        bigint task_id FK
        varchar question_id FK
        int question_order
    }
    
    tbl_mastery_learning_goal {
        bigint id PK
        varchar user_id FK
        smallint goal_type
        varchar goal_value
        numeric goal_difficulty_factor
        timestamptz set_time
    }
    
    tbl_mastery_knowledge_point_mastery {
        bigint id PK
        varchar user_id FK
        varchar knowledge_point_id FK
        numeric mastery_value
        int answer_count
        boolean is_weak_point
        timestamptz last_updated_at
        smallint update_reason
    }
    
    tbl_mastery_externalized_mastery {
        bigint id PK
        varchar user_id FK
        varchar course_id FK
        numeric externalized_mastery_value
        int cumulative_answer_count
        timestamptz last_updated_at
        smallint update_source
    }
    
    tbl_mastery_target_mastery {
        bigint id PK
        varchar user_id FK
        varchar course_id FK
        numeric target_mastery_value
        bigint learning_goal_id FK
    }
    
    tbl_mastery_answer_record {
        bigint id PK
        varchar user_id FK
        varchar question_id FK
        varchar course_id FK
        bigint task_id FK
        varchar knowledge_point_ids
        smallint answer_result
        timestamptz answer_time
        int answer_duration
        int attempt_count
        numeric mastery_increment
    }
    
    tbl_mastery_calculation_parameter {
        bigint id PK
        varchar param_type
        varchar param_key
        varchar param_value
        text param_desc
        varchar applicable_scope
        varchar scope_id
        timestamptz effective_time
        timestamptz expire_time
        smallint status
    }
    
    tbl_mastery_weak_point {
        bigint id PK
        varchar user_id FK
        varchar knowledge_point_id FK
        numeric current_mastery
        numeric target_mastery
        numeric gap_ratio
        timestamptz analysis_time
        smallint recommendation_priority
    }
```

## 6. 核心用户场景/用户故事与数据操作分析

### 6.1 场景/故事: 系统初始化学生学科能力（用户分层）

#### 6.1.1 场景描述

学校提供新生数据后，系统需要为每位学生计算其在各个学科的初始能力分层。系统根据学校水平（校准系数a）、生源波动（校准系数b，仅高中）和学生在校排名百分比，计算学科能力排名，并确定学生的学科能力分层（S+, S, A, B, C）。对于录取率<40%的学校或较差中学，S+分层记为S。

#### 6.1.2 涉及的主要数据表

- tbl_mastery_calculation_parameter：存储校准系数、分层阈值等参数
- tbl_mastery_knowledge_point_mastery：初始化学生的知识点掌握度

#### 6.1.3 数据操作流程 (CRUD 分析)

1. **读取操作 (R)**:
   - 从外部系统（运营管理平台）获取学生数据（ID、学科排名等）和学校数据（类型、录取率等）
   - 从 tbl_mastery_calculation_parameter 表读取校准系数a、校准系数b、分层阈值等参数

2. **计算处理**:
   - 计算学科能力排名：排名% * 校准系数a * 校准系数b（高中）或 排名% * 校准系数a（初中）
   - 根据学科能力排名确定分层级别（S+, S, A, B, C）
   - 对于录取率<40%的学校或较差中学，将S+分层调整为S

3. **创建操作 (C)**:
   - 根据学科初始值和分层掌握系数，为每个学生在各知识点上创建初始掌握度记录，插入 tbl_mastery_knowledge_point_mastery 表

#### 6.1.4 性能考量与索引支持

- **批量插入优化**：使用批量插入而非逐条插入，减少数据库交互次数
- **索引利用**：tbl_mastery_knowledge_point_mastery 表的 user_id 和 knowledge_point_id 索引支持按用户或知识点快速查询
- **参数缓存**：将 tbl_mastery_calculation_parameter 表中的参数缓存在应用层，避免频繁查询
- **并行处理**：可以按学校或班级并行处理学生数据，提高初始化速度
- **事务管理**：使用事务确保一个学生的所有知识点掌握度要么全部初始化成功，要么全部失败，保证数据一致性

### 6.2 场景/故事: 学生答题后实时更新知识点掌握度

#### 6.2.1 场景描述

学生通过客户端提交题目答案，系统需要实时更新其对相关知识点的掌握度。系统根据题目难度、答题结果（正确、部分正确、错误）、当前掌握度等因素，计算掌握度增量，并更新累计掌握度。系统还需要处理特殊情况，如错题重练、自评主观题、同一题目多次作答等。

#### 6.2.2 涉及的主要数据表

- tbl_mastery_answer_record：记录学生的答题情况
- tbl_mastery_knowledge_point_mastery：存储和更新学生的知识点掌握度
- tbl_mastery_calculation_parameter：获取掌握度计算参数

#### 6.2.3 数据操作流程 (CRUD 分析)

1. **创建操作 (C)**:
   - 创建答题记录，插入 tbl_mastery_answer_record 表，包括用户ID、题目ID、课程ID、任务ID、答题结果、答题时间、作答时长、尝试次数等信息

2. **读取操作 (R)**:
   - 从外部系统（内容平台）获取题目信息（难度、关联知识点）
   - 从 tbl_mastery_knowledge_point_mastery 表读取学生当前的知识点掌握度
   - 从 tbl_mastery_calculation_parameter 表读取掌握度计算参数

3. **计算处理**:
   - 根据不同场景（首次答题/错题重练、自评主观题等）应用不同的计算规则
   - 计算掌握度增量并更新累计掌握度，确保结果在[0,1]区间内

4. **更新操作 (U)**:
   - 更新 tbl_mastery_knowledge_point_mastery 表中的掌握度值、累计答题量、最后更新时间等字段
   - 如果掌握度变化显著，可能需要更新 tbl_mastery_weak_point 表

5. **异步操作**:
   - 将答题记录异步归档到 tbl_mastery_answer_record_history 表（ClickHouse）
   - 将掌握度变化记录异步归档到 tbl_mastery_knowledge_point_mastery_history 表（ClickHouse）

#### 6.2.4 性能考量与索引支持

- **缓存策略**：使用Redis缓存热点掌握度数据，减轻数据库压力
- **索引利用**：
  - tbl_mastery_answer_record 表的 user_id、question_id、task_id 索引支持快速查询用户答题历史
  - tbl_mastery_knowledge_point_mastery 表的联合唯一索引(user_id, knowledge_point_id)支持快速定位和更新掌握度
- **并发控制**：使用乐观锁或版本号机制处理并发更新，避免更新冲突
- **批量更新**：当一道题关联多个知识点时，使用批量更新减少数据库交互
- **异步处理**：使用消息队列异步处理非关键路径操作，如历史数据归档

### 6.3 场景/故事: 学生完成学习单元后更新外化掌握度

#### 6.3.1 场景描述

学生完成一个学习单元（如AI课、巩固练习、作业、测验）后，系统需要更新其对该课程的外化掌握度。系统判断是否满足更新条件（如非中途退出），获取课程关联的知识点掌握度，计算外化掌握度。外化掌握度遵循"只增不降"原则，即新计算的外化掌握度只有高于当前值时才会更新。系统还需要处理特殊情况，如AI课题目不足或无题目的情况。

#### 6.3.2 涉及的主要数据表

- tbl_mastery_learning_task：记录学习任务的完成状态
- tbl_mastery_knowledge_point_mastery：获取知识点掌握度
- tbl_mastery_externalized_mastery：存储和更新外化掌握度

#### 6.3.3 数据操作流程 (CRUD 分析)

1. **更新操作 (U)**:
   - 更新 tbl_mastery_learning_task 表中的学习任务完成状态和完成时间

2. **读取操作 (R)**:
   - 从外部系统（内容平台）获取课程关联的知识点列表
   - 从 tbl_mastery_knowledge_point_mastery 表读取学生对相关知识点的掌握度
   - 从 tbl_mastery_externalized_mastery 表读取学生当前的外化掌握度

3. **计算处理**:
   - 判断是否满足更新条件（非中途退出且累计答题量足够）
   - 根据不同情况（AI课题目不足、无题目、正常情况）进行特殊处理或常规计算
   - 计算外化掌握度（知识点掌握度转百分比），遵循"只增不降"原则

4. **更新操作 (U)**:
   - 更新 tbl_mastery_externalized_mastery 表中的外化掌握度值、累计答题量、最后更新时间、更新来源等字段

5. **异步操作**:
   - 将外化掌握度变化记录异步归档到 tbl_mastery_externalized_mastery_history 表（ClickHouse）

#### 6.3.4 性能考量与索引支持

- **索引利用**：
  - tbl_mastery_externalized_mastery 表的联合唯一索引(user_id, course_id)支持快速定位和更新外化掌握度
  - tbl_mastery_knowledge_point_mastery 表的索引支持快速查询知识点掌握度
- **缓存策略**：缓存课程与知识点的关联关系，减少查询内容平台的次数
- **批量查询**：一次性查询所有相关知识点的掌握度，减少数据库交互
- **事务管理**：使用事务确保外化掌握度更新的原子性
- **并发控制**：使用乐观锁或版本号机制处理并发更新，避免更新冲突

### 6.4 场景/故事: 学生设定学习目标后计算目标掌握度

#### 6.4.1 场景描述

学生设定或修改学习目标（如目标院校、年级排名）后，系统需要计算其在每节课应达到的目标掌握度。系统根据学生设定的目标难度、课程难度、考试频率等因素，计算每节课的目标掌握度，并将结果换算成百分比，按0.05向上取整。这些目标掌握度将用于指导学生的学习方向和判断薄弱知识点。

#### 6.4.2 涉及的主要数据表

- tbl_mastery_learning_goal：记录学生的学习目标
- tbl_mastery_target_mastery：存储每节课的目标掌握度
- tbl_mastery_calculation_parameter：获取目标难度系数、考试频率系数等参数

#### 6.4.3 数据操作流程 (CRUD 分析)

1. **创建操作 (C)**:
   - 创建学习目标记录，插入 tbl_mastery_learning_goal 表，包括用户ID、目标类型、目标值、设置时间等信息

2. **读取操作 (R)**:
   - 从外部系统（内容平台）获取所有课程信息（课程ID、难度、文理科属性等）
   - 从 tbl_mastery_calculation_parameter 表读取目标难度系数、考试频率系数等参数

3. **计算处理**:
   - 对每个课程，计算目标掌握度 = 目标难度系数 * 单课难度系数 * 考试频率系数
   - 将计算结果换算成百分比，按0.05向上取整

4. **创建/更新操作 (C/U)**:
   - 对于每个课程，创建或更新 tbl_mastery_target_mastery 表中的记录，包括用户ID、课程ID、目标掌握度值、学习目标ID等信息

5. **事件发布**:
   - 发布目标掌握度更新事件，触发薄弱知识点判断流程

#### 6.4.4 性能考量与索引支持

- **批量操作**：使用批量插入或更新操作，减少数据库交互次数
- **索引利用**：
  - tbl_mastery_target_mastery 表的联合唯一索引(user_id, course_id)支持快速定位和更新目标掌握度
  - tbl_mastery_learning_goal 表的 user_id 索引支持快速查询用户的学习目标
- **参数缓存**：缓存计算参数和课程信息，减少数据库查询
- **异步计算**：对于大量课程的目标掌握度计算，可以考虑异步批处理，减少对用户交互的影响
- **事务管理**：使用事务确保所有课程的目标掌握度要么全部更新成功，要么全部失败

### 6.5 场景/故事: 系统判断学生薄弱知识点

#### 6.5.1 场景描述

在学生的知识点掌握度或目标掌握度更新后，系统需要判断是否存在薄弱知识点。系统获取学生的知识点掌握度和目标掌握度数据，计算知识点掌握度与目标掌握度的比值。如果同时满足"知识点掌握度/目标掌握度≤0.7"和"知识点掌握度≤0.5"两个条件，则将该知识点标记为薄弱知识点，并推送给推荐引擎，用于后续的学习内容推荐。

#### 6.5.2 涉及的主要数据表

- tbl_mastery_knowledge_point_mastery：获取学生的知识点掌握度
- tbl_mastery_target_mastery：获取学生的目标掌握度
- tbl_mastery_weak_point：存储学生的薄弱知识点信息

#### 6.5.3 数据操作流程 (CRUD 分析)

1. **读取操作 (R)**:
   - 从 tbl_mastery_knowledge_point_mastery 表读取学生的知识点掌握度
   - 从 tbl_mastery_target_mastery 表读取学生的目标掌握度
   - 从外部系统（内容平台）获取知识点与课程的关联关系

2. **计算处理**:
   - 对每个知识点，计算掌握度与目标掌握度的比值
   - 判断是否满足薄弱知识点的条件：掌握度/目标掌握度≤0.7且掌握度≤0.5
   - 计算差距比例：1 - (掌握度 / (目标掌握度/100))
   - 根据差距比例和其他因素，确定推荐优先级

3. **创建/更新操作 (C/U)**:
   - 对于满足条件的知识点，创建或更新 tbl_mastery_weak_point 表中的记录
   - 对于不再满足条件的知识点，从 tbl_mastery_weak_point 表中删除（软删除）

4. **更新操作 (U)**:
   - 更新 tbl_mastery_knowledge_point_mastery 表中对应知识点的 is_weak_point 字段

5. **外部系统交互**:
   - 将薄弱知识点信息推送给推荐引擎服务，用于内容推荐

#### 6.5.4 性能考量与索引支持

- **索引利用**：
  - tbl_mastery_weak_point 表的联合唯一索引(user_id, knowledge_point_id)支持快速定位和更新薄弱知识点
  - tbl_mastery_knowledge_point_mastery 表和 tbl_mastery_target_mastery 表的索引支持快速查询掌握度数据
- **批量处理**：对于大量知识点的薄弱点判断，使用批量操作减少数据库交互
- **增量处理**：只处理掌握度或目标掌握度发生变化的知识点，避免全量计算
- **并发控制**：使用乐观锁或版本号机制处理并发更新，避免更新冲突
- **异步处理**：薄弱知识点判断可以异步进行，不阻塞主流程
- **缓存策略**：缓存热点用户的掌握度数据，减少数据库查询

## 7. 设计检查清单 (Design Checklist)

### 7.1 需求与架构对齐性检查

| 需求点 | 数据库设计支持情况 | 备注 |
| ------ | ------------------ | ---- |
| 学科能力（用户分层）计算 | ✅ 支持 | 通过 tbl_mastery_calculation_parameter 表存储校准系数和分层阈值，支持不同学校类型的分层计算 |
| 知识点掌握度计算与管理 | ✅ 支持 | 通过 tbl_mastery_knowledge_point_mastery 表存储掌握度数据，tbl_mastery_answer_record 表记录答题情况，支持各种场景下的掌握度计算 |
| 外化掌握度计算与更新 | ✅ 支持 | 通过 tbl_mastery_externalized_mastery 表存储外化掌握度，支持"只增不降"原则和特殊情况处理 |
| 目标掌握度计算 | ✅ 支持 | 通过 tbl_mastery_learning_goal 和 tbl_mastery_target_mastery 表支持学习目标设置和目标掌握度计算 |
| 薄弱知识点判断 | ✅ 支持 | 通过 tbl_mastery_weak_point 表存储薄弱知识点信息，支持薄弱知识点的判断和推荐 |
| 知识点掌握度计算条件判断 | ✅ 支持 | 通过从内容平台获取知识点配置信息，判断是否计算掌握度 |
| 内容结构管理 | ✅ 支持 | 通过与内容平台的交互，获取课程与知识点的对应关系，题目与课程的关联关系 |
| 历史数据分析 | ✅ 支持 | 通过 ClickHouse 中的历史表存储历史数据，支持数据分析和统计 |

### 7.2 数据库设计质量检查

| 检查维度 | 评估结果 | 说明 |
| -------- | -------- | ---- |
| **表结构设计** | ✅ 良好 | 表结构清晰，字段定义合理，符合业务需求 |
| **命名规范** | ✅ 符合 | 表名和字段名符合规范，使用下划线命名法，表名前缀统一 |
| **字段类型选择** | ✅ 合理 | 根据数据特性选择了合适的字段类型，如掌握度使用 NUMERIC(4, 3)，外化掌握度使用 NUMERIC(5, 2) |
| **索引设计** | ✅ 完善 | 为查询频繁的字段创建了索引，使用了联合索引和条件索引，支持高效查询 |
| **约束设计** | ✅ 合理 | 使用了主键约束、非空约束，确保数据完整性 |
| **关系设计** | ✅ 合理 | 表间关系清晰，使用逻辑外键关联，避免了物理外键约束 |
| **数据库选型** | ✅ 合适 | PostgreSQL 存储核心业务数据，ClickHouse 存储历史数据，符合数据特性 |
| **扩展性** | ✅ 良好 | 表设计考虑了未来扩展，如参数配置表支持不同范围的参数配置 |
| **性能考量** | ✅ 充分 | 考虑了高并发场景，设计了合适的索引和缓存策略 |
| **安全性** | ✅ 考虑 | 使用软删除机制，保留数据变更历史 |
| **可维护性** | ✅ 良好 | 表结构清晰，字段有详细注释，便于理解和维护 |

## 8. 参考资料与附录

- 产品需求文档 (PRD): be-s3-2-ai-dd-掌握度策略-V1.0-claude-3.7.md
- 技术架构方案 (TD): be-s2-1-ai-td-掌握度策略-V1.0-claude-3.7.md
- PostgreSQL 设计规范与约束文件: rules-postgresql-code-v2.md
- ClickHouse 设计规范与约束文件: rules-clickhouse-code-v2.md
---
description: 
globs: 
alwaysApply: false
---
# Kafka 规范文档

## 1. 基本原则

- Kafka 作为系统的消息总线，用于实现服务间异步通信
- 严格遵循 CQRS 原则，将命令和查询分离
- 消息格式统一使用 JSON，字段名采用小写驼峰
- 时间戳统一使用 UTC 秒数（整数值）
- 每条消息必须包含唯一标识符和版本信息

## 2. 版本与配置

- Kafka 版本: 3.6.3
- 生产环境配置:
  - 副本因子: 3
  - min.insync.replicas: 2
  - 最小分区数: 根据吞吐量要求确定，默认不少于 3

## 3. 主题命名规范

### 3.1 格式规范

```
<环境>.<业务域>.<服务名>.<事件类型>
```

- 环境: dev, test, prod
- 业务域: 如 user, course, payment 等
- 服务名: 如 teacher, student, admin 等
- 事件类型: 如 created, updated, deleted 等

### 3.2 命名示例

```
prod.user.teacher.created
dev.course.lesson.updated
test.payment.order.completed
```

## 4. 消息格式规范

### 4.1 消息结构

```json
{
  "id": "消息唯一ID",
  "version": "消息版本号",
  "source": "消息来源服务",
  "type": "消息类型",
  "timestamp": 1618047315,
  "traceId": "分布式追踪ID",
  "payload": {
    // 业务数据
  }
}
```

### 4.2 版本控制

- 每个消息类型必须包含版本号
- 版本号采用语义化版本 (major.minor)
- 消费者必须向前兼容至少一个主要版本

## 5. 生产者规范

### 5.1 基本原则

- 使用事务保证消息发送的原子性
- 实现重试机制，但避免无限重试
- 消息发送失败应记录详细日志
- 关键业务消息需要实现本地消息表或事务出站表模式

### 5.2 代码示例

```go
import (
    "context"
    "time"
    
    "github.com/Shopify/sarama"
    "project/app/core/loger/logger_new"
)

type KafkaProducer struct {
    producer sarama.SyncProducer
    topic    string
}

func NewKafkaProducer(brokers []string, topic string) (*KafkaProducer, error) {
    config := sarama.NewConfig()
    config.Producer.RequiredAcks = sarama.WaitForAll
    config.Producer.Retry.Max = 5
    config.Producer.Return.Successes = true
    
    producer, err := sarama.NewSyncProducer(brokers, config)
    if err != nil {
        return nil, err
    }
    
    return &KafkaProducer{
        producer: producer,
        topic:    topic,
    }, nil
}

func (p *KafkaProducer) SendMessage(ctx context.Context, key string, data []byte) error {
    msg := &sarama.ProducerMessage{
        Topic: p.topic,
        Key:   sarama.StringEncoder(key),
        Value: sarama.ByteEncoder(data),
    }
    
    partition, offset, err := p.producer.SendMessage(msg)
    if err != nil {
        logger_new.Debug(ctx, "Failed to send message to Kafka", 
            "error", err, "topic", p.topic, "key", key)
        return err
    }
    
    logger_new.Debug(ctx, "Message sent successfully", 
        "topic", p.topic, "partition", partition, "offset", offset)
    return nil
}
```

## 6. 消费者规范

### 6.1 基本原则

- 消费者组 ID 格式: `<服务名>-<功能>-consumer`
- 使用手动提交偏移量，确保消息处理成功后再提交
- 实现幂等性处理，避免重复消费导致的问题
- 消费失败应记录详细错误日志并实现死信队列

### 6.2 代码示例

```go
import (
    "context"
    "time"
    "encoding/json"
    
    "github.com/Shopify/sarama"
    "project/app/core/loger/logger_new"
)

type KafkaConsumer struct {
    consumer sarama.ConsumerGroup
    topics   []string
    handler  MessageHandler
}

type MessageHandler interface {
    Process(ctx context.Context, message []byte) error
}

func NewKafkaConsumer(brokers []string, groupID string, topics []string, handler MessageHandler) (*KafkaConsumer, error) {
    config := sarama.NewConfig()
    config.Consumer.Group.Rebalance.Strategy = sarama.BalanceStrategyRoundRobin
    config.Consumer.Offsets.Initial = sarama.OffsetOldest
    
    consumer, err := sarama.NewConsumerGroup(brokers, groupID, config)
    if err != nil {
        return nil, err
    }
    
    return &KafkaConsumer{
        consumer: consumer,
        topics:   topics,
        handler:  handler,
    }, nil
}

func (c *KafkaConsumer) Start(ctx context.Context) error {
    // 实现消费者组处理逻辑
    handler := &consumerGroupHandler{
        handler: c.handler,
    }
    
    for {
        err := c.consumer.Consume(ctx, c.topics, handler)
        if err != nil {
            if err == context.Canceled {
                return nil
            }
            logger_new.Debug(ctx, "Error from consumer", "error", err)
            time.Sleep(time.Second)
        }
    }
}

type consumerGroupHandler struct {
    handler MessageHandler
}

func (h *consumerGroupHandler) Setup(_ sarama.ConsumerGroupSession) error   { return nil }
func (h *consumerGroupHandler) Cleanup(_ sarama.ConsumerGroupSession) error { return nil }
func (h *consumerGroupHandler) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
    for message := range claim.Messages() {
        ctx := context.Background()
        err := h.handler.Process(ctx, message.Value)
        if err != nil {
            logger_new.Debug(ctx, "Failed to process message", 
                "error", err, "topic", message.Topic, "partition", message.Partition, "offset", message.Offset)
            // 实现重试或发送到死信队列的逻辑
            continue
        }
        
        // 处理成功后手动提交偏移量
        session.MarkMessage(message, "")
    }
    return nil
}
```

## 7. 监控与可观察性

### 7.1 指标收集

- 生产者:
  - 消息发送成功/失败计数
  - 消息发送延迟
  - 每秒消息吞吐量

- 消费者:
  - 消息处理成功/失败计数
  - 消息处理延迟
  - 消费者组延迟（消费者偏移量与最新消息偏移量的差值）

### 7.2 日志记录

- 消息发送失败时记录详细信息
- 消息处理失败时记录详细信息
- 使用 traceId 关联消息链路

## 8. 错误处理策略

### 8.1 生产者错误处理

- 临时性错误: 实现指数退避重试
- 持久性错误: 记录日志并向监控系统发送告警
- 关键业务消息: 使用本地消息表实现最终一致性

### 8.2 消费者错误处理

- 临时性错误: 实现有限重试
- 持久性错误: 发送到死信队列并记录日志
- 格式或版本错误: 记录详细信息并跳过处理

## 9. 安全与认证

- 使用 SSL/TLS 加密传输
- 实现基于 SASL 的认证机制
- 为不同环境使用不同的认证凭证
- 定期轮换认证密钥

## 10. 性能优化

- 针对高吞吐量场景进行批量生产和消费
- 合理设置批处理大小和提交间隔
- 监控并优化消费者组延迟
- 合理设置分区数量，考虑负载均衡

## 11. 测试策略

- 单元测试: 使用模拟库测试生产者和消费者逻辑
- 集成测试: 使用嵌入式 Kafka 或测试容器
- 端到端测试: 验证消息流转的完整流程
- 混沌测试: 模拟 Kafka 节点故障和网络分区

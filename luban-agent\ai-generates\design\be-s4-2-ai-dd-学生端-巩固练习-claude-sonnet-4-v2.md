# 学生端巩固练习 - 数据库设计文档

## 文档信息
- **工作模式**: 现有库需求演进
- **设计师**: AI数据库设计工程师
- **模型**: claude-sonnet-4
- **日期**: 2025-05-30
- **版本**: V1.0

## 技术架构概述
基于现有AI课中和掌握度策略系统，新增学生端巩固练习功能。采用微服务架构+事件驱动模式，确保与现有系统的无缝集成。

### 技术栈
- **开发语言**: Go 1.24.3
- **微服务框架**: Kratos 2.8.4  
- **数据库**: PostgreSQL 17 (OLTP) + ClickHouse 23.8 (OLAP)
- **缓存**: Redis 6.0.3
- **消息队列**: Kafka 3.6
- **搜索引擎**: Elasticsearch 7.16.2

### 性能目标
- 支持1000+并发用户
- P95响应时间 < 2秒
- 掌握度计算延迟 < 500ms
- 推题响应时间 < 1秒

## 现有系统继承

### 继承的核心表结构
1. **AI课中系统**: `tbl_course`, `tbl_component`, `tbl_learning_session`, `tbl_learning_progress`, `tbl_mastery_record`
2. **掌握度策略系统**: `tbl_learning_task`, `tbl_knowledge_mastery`, `tbl_answer_record`, `tbl_weak_knowledge`

## 新增数据库设计

### PostgreSQL表设计（OLTP）

#### 1. 巩固练习会话表
```sql
CREATE TABLE tbl_consolidation_session (
    id BIGSERIAL PRIMARY KEY,
    session_code VARCHAR(64) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    course_id BIGINT NOT NULL,
    session_type VARCHAR(32) NOT NULL DEFAULT 'practice',
    difficulty_level INTEGER NOT NULL DEFAULT 1,
    target_mastery_rate DECIMAL(5,2) DEFAULT 0.80,
    current_mastery_rate DECIMAL(5,2) DEFAULT 0.00,
    total_questions INTEGER DEFAULT 0,
    answered_questions INTEGER DEFAULT 0,
    correct_answers INTEGER DEFAULT 0,
    session_status VARCHAR(32) NOT NULL DEFAULT 'active',
    start_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_by BIGINT DEFAULT 0,
    updated_by BIGINT DEFAULT 0,
    version INTEGER DEFAULT 1,
    remark TEXT
);

CREATE INDEX idx_consolidation_session_user_id ON tbl_consolidation_session(user_id);
CREATE INDEX idx_consolidation_session_course_id ON tbl_consolidation_session(course_id);
CREATE INDEX idx_consolidation_session_status ON tbl_consolidation_session(session_status);
```

#### 2. 巩固练习推题表
```sql
CREATE TABLE tbl_consolidation_question_recommendation (
    id BIGSERIAL PRIMARY KEY,
    session_id BIGINT NOT NULL,
    question_id BIGINT NOT NULL,
    knowledge_point_id BIGINT NOT NULL,
    recommendation_reason VARCHAR(255),
    difficulty_score DECIMAL(3,2) DEFAULT 0.50,
    priority_score DECIMAL(5,2) DEFAULT 0.00,
    question_order INTEGER NOT NULL,
    is_answered BOOLEAN DEFAULT FALSE,
    answer_result VARCHAR(32),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_by BIGINT DEFAULT 0,
    updated_by BIGINT DEFAULT 0,
    version INTEGER DEFAULT 1,
    remark TEXT,
    UNIQUE(session_id, question_id)
);

CREATE INDEX idx_consolidation_question_session_id ON tbl_consolidation_question_recommendation(session_id);
CREATE INDEX idx_consolidation_question_knowledge_point ON tbl_consolidation_question_recommendation(knowledge_point_id);
```

#### 3. 巩固练习答题记录表
```sql
CREATE TABLE tbl_consolidation_answer_record (
    id BIGSERIAL PRIMARY KEY,
    record_code VARCHAR(64) NOT NULL UNIQUE,
    session_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    question_id BIGINT NOT NULL,
    knowledge_point_id BIGINT NOT NULL,
    user_answer TEXT,
    correct_answer TEXT,
    is_correct BOOLEAN NOT NULL DEFAULT FALSE,
    confidence_level INTEGER DEFAULT 3,
    time_spent INTEGER NOT NULL DEFAULT 0,
    hint_used_count INTEGER DEFAULT 0,
    answer_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_by BIGINT DEFAULT 0,
    updated_by BIGINT DEFAULT 0,
    version INTEGER DEFAULT 1,
    remark TEXT
);

CREATE INDEX idx_consolidation_answer_session_id ON tbl_consolidation_answer_record(session_id);
CREATE INDEX idx_consolidation_answer_user_id ON tbl_consolidation_answer_record(user_id);
```

#### 4. 错题管理表
```sql
CREATE TABLE tbl_consolidation_wrong_question (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    question_id BIGINT NOT NULL,
    knowledge_point_id BIGINT NOT NULL,
    first_wrong_time TIMESTAMP WITH TIME ZONE,
    last_wrong_time TIMESTAMP WITH TIME ZONE,
    wrong_count INTEGER DEFAULT 1,
    is_mastered BOOLEAN DEFAULT FALSE,
    mastered_time TIMESTAMP WITH TIME ZONE,
    note TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_by BIGINT DEFAULT 0,
    updated_by BIGINT DEFAULT 0,
    version INTEGER DEFAULT 1,
    remark TEXT,
    UNIQUE(user_id, question_id)
);

CREATE INDEX idx_consolidation_wrong_user_id ON tbl_consolidation_wrong_question(user_id);
CREATE INDEX idx_consolidation_wrong_knowledge_point ON tbl_consolidation_wrong_question(knowledge_point_id);
CREATE INDEX idx_consolidation_wrong_mastered ON tbl_consolidation_wrong_question(user_id, is_mastered);
```

#### 5. 学习报告表
```sql
CREATE TABLE tbl_consolidation_learning_report (
    id BIGSERIAL PRIMARY KEY,
    report_code VARCHAR(64) NOT NULL UNIQUE,
    user_id BIGINT NOT NULL,
    session_id BIGINT,
    course_id BIGINT NOT NULL,
    report_type VARCHAR(32) NOT NULL DEFAULT 'session',
    report_period VARCHAR(32),
    start_time TIMESTAMP WITH TIME ZONE,
    end_time TIMESTAMP WITH TIME ZONE,
    total_sessions INTEGER DEFAULT 0,
    total_questions INTEGER DEFAULT 0,
    correct_rate DECIMAL(5,2) DEFAULT 0.00,
    average_time_per_question INTEGER DEFAULT 0,
    mastery_improvement DECIMAL(5,2) DEFAULT 0.00,
    weak_knowledge_points TEXT,
    strong_knowledge_points TEXT,
    recommendations TEXT,
    achievements TEXT,
    report_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    created_by BIGINT DEFAULT 0,
    updated_by BIGINT DEFAULT 0,
    version INTEGER DEFAULT 1,
    remark TEXT
);

CREATE INDEX idx_consolidation_report_user_id ON tbl_consolidation_learning_report(user_id);
CREATE INDEX idx_consolidation_report_session_id ON tbl_consolidation_learning_report(session_id);
CREATE INDEX idx_consolidation_report_course_id ON tbl_consolidation_learning_report(course_id);
CREATE INDEX idx_consolidation_report_type ON tbl_consolidation_learning_report(report_type);
```

### ClickHouse表设计（OLAP）

#### 1. 巩固练习行为分析表
```sql
CREATE TABLE consolidation_behavior_log (
    event_time DateTime64(3),
    user_id UInt64,
    session_id UInt64,
    event_type String,
    event_data String,
    page_url String,
    user_agent String,
    ip_address String,
    created_date Date DEFAULT toDate(event_time)
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(created_date)
ORDER BY (user_id, event_time);
```

#### 2. 掌握度变化追踪表
```sql
CREATE TABLE consolidation_mastery_tracking (
    event_time DateTime64(3),
    user_id UInt64,
    knowledge_point_id UInt64,
    old_mastery_level Float32,
    new_mastery_level Float32,
    change_reason String,
    session_id UInt64,
    question_id UInt64,
    created_date Date DEFAULT toDate(event_time)
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(created_date)
ORDER BY (user_id, knowledge_point_id, event_time);
```

#### 3. 学习效果分析表
```sql
CREATE TABLE consolidation_learning_analytics (
    analysis_date Date,
    user_id UInt64,
    course_id UInt64,
    session_count UInt32,
    total_questions UInt32,
    correct_answers UInt32,
    total_time_spent UInt32,
    average_confidence Float32,
    mastery_progress Float32,
    difficulty_preference Float32
) ENGINE = SummingMergeTree()
PARTITION BY toYYYYMM(analysis_date)
ORDER BY (user_id, course_id, analysis_date);
```

## 业务场景分析

### 核心用户场景

#### 场景1: 学生开始巩固练习
1. 学生选择课程章节，发起巩固练习
2. 系统分析学生历史掌握度和薄弱知识点
3. 智能推荐适合的练习题目
4. 创建练习会话，生成题目推荐记录

#### 场景2: 学生答题过程
1. 学生查看推荐题目，进行答题
2. 系统记录答题行为和结果
3. 实时更新掌握度计算
4. 根据答题表现调整后续推题策略

#### 场景3: 练习结束和报告生成
1. 学生完成练习或主动结束
2. 系统生成个性化学习报告
3. 更新长期掌握度记录
4. 识别新的薄弱知识点

## 数据模型ER关系图

### 核心实体关系
```
用户(User) 1:N 巩固练习会话(ConsolidationSession)
巩固练习会话 1:N 题目推荐(QuestionRecommendation)  
巩固练习会话 1:N 答题记录(AnswerRecord)
用户 1:N 错题管理(WrongQuestion)
用户 1:N 学习报告(LearningReport)
巩固练习会话 1:1 学习报告

题目推荐 N:1 题目(Question) [继承]
答题记录 N:1 知识点(KnowledgePoint) [继承]
巩固练习会话 N:1 课程(Course) [继承]
```

### 与现有系统的集成关系
```
现有掌握度记录表 ← 关联 → 新答题记录表
现有薄弱知识点表 ← 关联 → 新推题推荐表  
现有学习任务表 ← 关联 → 新练习会话表
现有课程表 ← 关联 → 新练习会话表
```

## 数据一致性设计

### 事务边界
1. **练习会话创建**: 原子性创建会话和初始推题记录
2. **答题记录**: 答题记录插入 + 掌握度更新 + 推题策略调整
3. **会话结束**: 会话状态更新 + 最终掌握度计算 + 报告生成

### 数据同步策略
1. **实时同步**: 答题记录 → 掌握度计算服务
2. **异步同步**: 行为日志 → ClickHouse分析表
3. **定时同步**: 长期掌握度趋势 → 数据仓库

## 性能优化策略

### 数据库优化
1. **分区策略**: 按时间分区存储历史数据
2. **索引优化**: 基于查询模式设计复合索引
3. **读写分离**: 查询操作使用只读副本

### 缓存策略
1. **用户掌握度**: Redis缓存最新掌握度状态
2. **推题结果**: 缓存推题算法计算结果
3. **题目内容**: 缓存题目详情和解析

## 部署架构

### 数据库集群
```
PostgreSQL主从集群:
- Master: 写操作 + 实时读操作  
- Slave1: 报告查询 + 数据分析
- Slave2: 备份 + 灾难恢复

ClickHouse集群:
- Shard1: 行为日志数据
- Shard2: 掌握度变化数据  
- Shard3: 学习分析数据
```

### 缓存架构
```
Redis集群:
- Cluster1: 用户会话数据
- Cluster2: 掌握度缓存
- Cluster3: 推题结果缓存
```

## 监控指标

### 业务指标
- 练习会话完成率
- 平均答题正确率
- 掌握度提升幅度
- 推题准确性

### 技术指标
- 数据库连接池使用率
- 查询响应时间分布
- 缓存命中率
- 消息队列积压情况

## 数据治理

### 数据生命周期
- **热数据**: 近30天，存储在主库
- **温数据**: 30天-1年，归档到历史库
- **冷数据**: 1年以上，压缩存储

### 数据安全
- 用户答题内容加密存储
- 敏感数据访问审计
- 定期数据备份和恢复演练

## 总结

本设计文档基于现有AI课中和掌握度策略系统，采用"现有库需求演进"模式，设计了学生端巩固练习功能的数据库架构。新设计完全兼容现有系统，通过事件驱动和微服务架构确保系统的可扩展性和可维护性。

### 核心特点
1. **无缝集成**: 与现有系统完全兼容
2. **高性能**: 支持1000+并发，亚秒级响应
3. **智能推题**: 基于掌握度的个性化推荐
4. **实时分析**: 学习行为实时跟踪和分析
5. **可扩展**: 支持未来功能扩展和性能扩容

### 技术创新
- 采用PostgreSQL+ClickHouse混合架构
- 事件驱动的数据同步机制
- 智能缓存策略优化性能
- 完善的监控和告警体系 
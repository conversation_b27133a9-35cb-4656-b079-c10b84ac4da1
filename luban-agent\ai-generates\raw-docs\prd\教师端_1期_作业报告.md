教师端_1期_作业报告

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0.0 | 2025-02-06 | 袁全 | 新建 | 新建文档 |
| V5.0.0 | 3-28 | 袁全 | 升稿 | ![in_table_image_E5LZbxs0LoiUcMx0mDmcMZ88nkZ](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523386140.png) |
|  | 4-2 | 袁全 | 补充说明口径 | 补充：口径说明 |
|  | 4-15 | 袁全 | 补充说明埋点 | 补充：作业模块埋点 |
|  | 4-26 | 袁全 | 补充：说明规则 | cc UI图待补充1.值得关注异常 数值显示2.最近一次推送于xx-xx-xx hh：mm “已推送2次” |

![analyze_in_table_image_E5LZbxs0LoiUcMx0mDmcMZ88nkZ](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523386140.png)

**关联需求**

[教师端_1期_All-in-one](https://wcng60ba718p.feishu.cn/wiki/A3F1wlepNiF5ibkI2NAcf4K6nQc)

**设计稿**

UI图 https://www.figma.com/design/d7RUo4uZ7uwRpb1Enf0Sng/%E9%93%B6%E6%B2%B3-%E6%95%99%E5%B8%88?node-id=11-4149&p=f&t=MkljbBY4IkwHZuo7-0



### 一、背景和目标

#### 1.1、需求背景

- 业务层面：
- 老师不再授课以后，最注重的就是把控学生的学习情况，即关注：
- 学生粒度（按班级/学生）：完成/进度、正确率/得分、错题/答题数、用时
- 答题粒度：共性错题、题目正确率、作答选项/得分分布、用时
- 提问粒度：共性问题知识点、提问内容
- 在学生的学习过程中，老师需要更多作为“鼓励者”，对学生的每一个进步进行表扬，对不当行为及时沟通
- 表扬：进步、连续答对xx等 由系统推荐
- 关注：未完成作业、完成情况明显下降等 由系统推荐
- 技术层面：
- 构建各类任务、素材和学生 之间的底层数据关系
|  | 作业类型 | 报告内tab |
| --- | --- | --- |
| P0核心功能 | 课程作业资源 | 学生报告答题结果学生提问 |
| 未来功能 | 测验 | 大屏模式 |

#### 1.2、项目收益

完成教师端P0核心功能，确保在6月上线的版本可以跑通整个业务MVP流程，9月可完成全部核心功能



#### 1.3、覆盖用户

目标用户：合作校的教师



#### 1.4、方案简述

1. 以“卡片”展示任务的最核心信息，展示在列表中
![image_XXEmb0Gf6o2gl8xiCogc1tewnsc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523387024.png)



1. 点击卡片后，区分为“学生报告”、“答题结果”、“学生提问”展示当次三个不同维度下的报告
![image_PswKbOca7ouLbDxJzFScXHognOc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523387682.png)

学生报告

![image_Kde9bV1SroJspDxtJQeccHOCneg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523388444.png)

答题结果

![image_L1owbZXY7oMVRcxYTTwcs1IfnOh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523389047.png)

学生提问

可进入下一级，查看详情

![image_I4hibE2j6o3Kr4xlH1DcE0A3nag](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523389646.png)

查看：学生详情



![image_CLqrb4W3XoPbH4x4xlUcGgfMnuf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523390368.png)

查看：题目详情

![image_Jjy8bZ98UoVtrxx90FIcfdvSnPS](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523391220.png)

查看：提问详情

1. 在学生报告页凸出建议干预的学生：鼓励列表、关注列表，并可点击后查看详情、干预学生
![image_ScatbZPE7oxcyXxerp1c7JAfnFf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523391829.png)

建议鼓励/关注 及 干预记录

![image_Z3zqbyGvDohwO0xfZg8cCNnlnCd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523392487.png)

鼓励学生

![image_WX2Kb1LPEo9ChSx1sKmcCDdMnoe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523393418.png)

关注学生

### 二、名词说明

- 
                                                                                     



### 三、业务流程

**作业报告-数据流**

![board_X6kawZ9omhPMqZbGbeJcm7obnob](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523384747.png)



**本期：P0核心功能 **

![board_OyjKwkBjIh5MwkbvEMScScwSnng](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523385473.png)



### 四、需求概览

| 序号 | 需求名称 | 内容概述 | 优先级 |
| --- | --- | --- | --- |
| 1 | 作业列表 | 展示当前老师：布置的、有权限看的全部作业 | P0 |
| 2 | 作业报告 | 按：学生报告、答题结果、学生提问tab展示作业报告 | p0 |
| 3 | xx学生的作业报告 | 按：答题结果、学生提问展示作业报告 | p0 |
| 4 | 策略说明 | 鼓励、关注、共性错题 | p0 |
| 5 | 提醒设置 | 支持用户自定义提醒策略 | p1 |

### 五、详细产品方案

[口径说明](https://wcng60ba718p.feishu.cn/wiki/DXKawJOESiypQ3kt3vbc64DgnTc?sheet=e3d974)

#### 作业列表

| 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| 作业 | 数据范围 | 由以下两个维度取并集，展示数据： | ![in_table_image_GSs0bDnxPomvPlxUP2ac1IKinJe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523394209.png) |  |
|  | 筛选 | 全部类型查看班级学科布置日期 | ![in_table_image_NCQkbWefmoYZ6JxwYtzcQGepnMh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523395627.png) |  |
|  | 搜索 | 在搜索框中输入关键词，当前页面即按照用户搜索的“关键词”模糊匹配全部任务名称：用户输入后可继续输入或回删关键字，则页面按用户最新输入进行搜索用户输入后可点击清空，则清空搜索栏，回到触发搜索之前的页面筛选和结果 | ![in_table_image_Sj3wb50N3otvfKx11KxcWJCynnW](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523396915.png) |  |
|  | 卡片指标 | 课程任务：作业任务/测验任务：资源任务 |  |  |
|  | 卡片操作 | 对于本人布置的任务，有...标记，点击后唤起菜单，展示：编辑任务、删除任务、复制任务 |  |  |

![analyze_in_table_image_GSs0bDnxPomvPlxUP2ac1IKinJe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523394209.png)

![analyze_in_table_image_ZqNFb5BlvoUGZZx5WOjcCRJmnZL](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523394758.png)

![analyze_in_table_image_NCQkbWefmoYZ6JxwYtzcQGepnMh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523395627.png)

![analyze_in_table_image_JxSrbz5Fko88AKxvtnfcJFs4nMx](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523396355.png)

![analyze_in_table_image_Sj3wb50N3otvfKx11KxcWJCynnW](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523396915.png)

#### 区分职务的数据

| 职务 | 模块 | 数据报告查看权限 | 功能概述 |
| --- | --- | --- | --- |
| 学科老师 | 作业 | 1.可以看：本班本学科 任务数据 | 2.修改、删除、复制权限：仅本人布置的任务 |
| 班主任 | 作业 | 1.可以看：本班全部学科 任务数据 | 2.修改、删除、复制权限：仅本人布置的任务 |
| 学科主任 | 作业 | 1.可以看：本年级本学科 任务数据 | 无布置权限 |
| 年级主任 | 作业 | 1.可以看：本年级全部班级全部学科 任务数据 | 无布置权限 |
| 校长 | 作业 | 1.可以看：本校全部班级全部学科 任务数据 | 无布置权限 |

#### 作业报告

从「布置」或「作业」点击任务卡片，即进入作业报告的详情页

从「上课」点击xx学生的学习卡片内容，也可进入该生对应的作业报告详情页

##### 学生报告Tab

| 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| 任务报告>学生报告tab | 页面名称 和 返回 | 页面居中，展示任务名称左上角，展示“返回”，点击后返回前一页 | ![in_table_image_Cd4kbLOXYo3T08xNlWccCmmenye](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523397485.png) |  |
| 顶部 | 任务详情 和 筛选 | 展示该任务发布时间展示该任务完成时间展示当前任务的班级：展示当前任务的素材范围 | ![in_table_image_XVHwbo9CZoKTRUxSFQicbvMXnmb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523397970.png) |  |
| tab筛选 | 学生报告、答题结果、学生提问 | 无特别说明，从外部进入学情页默认展示的都是「学生报告」tab | ![in_table_image_L2VrbecbxokMp0xHiVxcKVhJned](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523398588.png) |  |
| 中部 | 建议鼓励学生 | ![in_table_image_IH8ebP7lQoDQdlxZzN3cvmJonwh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523399991.png)

详见： | ![in_table_image_Y4CxbxKsroMQpexKRv9cgNpDnze](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523400661.png) |  |
|  | 建议关注学生 | ![in_table_image_MfM7bitX2ozpTCxMCtHcHPBonjE](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523402085.png)

详见： | ![in_table_image_GUMxbygcDoiiVexnWgFcLQTqnSg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523402795.png) |  |
|  | 班级进度 | 展示当前班级、当前素材范围内，班级平均进度（同任务卡片：完成率） |  |  |
|  | 班级正确率 | 展示当前班级、当前素材范围内，班级正确率（同任务卡片：正确率） |  |  |
| 底部 | 任务学生列表 | 排序：按学生uid顺序展示任务学生列表，其中：带有建议鼓励/关注标签的学生置顶操作：其他： | ![in_table_image_CFTebD2UVoAQkSx5g9bcc479n7e](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523403399.png) |  |
|  | 列表中异常数据的飘色 | 对于上述列表中和班级均值偏离较大的数据，进行飘色展示比如低于班级平均值的20%的数据、最后10%的数据 |  |  |

![analyze_in_table_image_Cd4kbLOXYo3T08xNlWccCmmenye](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523397485.png)

![analyze_in_table_image_XVHwbo9CZoKTRUxSFQicbvMXnmb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523397970.png)

![analyze_in_table_image_L2VrbecbxokMp0xHiVxcKVhJned](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523398588.png)

![analyze_in_table_image_AnvObATkyod5a5xurxecUjEQnKc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523399177.png)

![analyze_in_table_image_IH8ebP7lQoDQdlxZzN3cvmJonwh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523399991.png)

![analyze_in_table_image_Y4CxbxKsroMQpexKRv9cgNpDnze](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523400661.png)

![analyze_in_table_image_Pg2lbdqDboNMPlx1r4ccsxwmn1P](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523401224.png)

![analyze_in_table_image_MfM7bitX2ozpTCxMCtHcHPBonjE](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523402085.png)

![analyze_in_table_image_GUMxbygcDoiiVexnWgFcLQTqnSg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523402795.png)

![analyze_in_table_image_CFTebD2UVoAQkSx5g9bcc479n7e](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523403399.png)

##### 答题结果Tab

| 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| 任务报告>答题结果tab | 数据展示逻辑 | 资源任务没有答题环节，也没有这个tab课堂、作业、测验都有答题环节，这个tab放在第二位，根据上面选取的班级 & 素材范围，计算对应的题目列表课程任务（每个学生的答题内容、顺序、数量可能都不一样）：作业/测验任务（不分层情况：每个学生的答题内容一样）： | ![in_table_image_QbOCbI3ssoaDfKxamzIcZbB8nWd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523404333.png) |  |
|  | “共xx道题，xx道共性错题”共性错题解释 | 共xx道题：任务在当前班级&素材筛选条件下Xx道共性错题：任务在当前班级&素材筛选条件下 |  |  |
|  | 搜索题目关键词 | 在搜索框中输入关键词，当前页面即按照用户搜索的“关键词”模糊匹配全部题目的题干、选项和解析：用户输入后可继续输入或回删关键字，则页面按用户最新输入进行搜索用户输入后可点击清空，则清空搜索栏，回到触发搜索之前的页面筛选和结果 |  |  |
|  | 题目信息展示 | 正确率 ：=该题在任务下的首答正确率（对作答了的同学，答对小空数量/题目中的总小空之和 *100%）  xx答错：=该题在任务下的首答有错误（答错至少1个小空的学生数）  xx人作答：=该题有作答记录的学生数    原则：   1.若这道题学生在当前任务素材中被推送了多次，只看学生首次作答的数据   2.已经提交的：课堂、测试、作业任务下，学生看到了题、选“暂不未答”的记作已作答、答错；   3.未提交的作业可以先跳过作答别的题目，此时不算作答 | ![in_table_image_PsxWbEgV3opGFoxTzVqcplajnGg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523405750.png) |  |
| 侧边 | 题目面板 | 默认展开态，可收起展示当前任务下的全部题目对应的题号，并用颜色区分不同题目的答题正确率情况题号规则同之前 | ![in_table_image_DCBObWZ44oqd9wx3ifxcPpHRnzg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523406316.png) |  |

![analyze_in_table_image_QbOCbI3ssoaDfKxamzIcZbB8nWd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523404333.png)

1. 题号
课程任务：按当前页面排序的顺序号展示（答题人数/答错人数）

作业/测验任务：按布置顺序序号 或 页面排序的答错人数顺序号 展示

这个序号还显示在侧边栏的「题目面板」

1. 题干&选项
定高展示，在题目过长情况下默认收起，点击“展开查看更多”后查看完整题目；点击“收起”后可继续收起

题目中若有图片，展示默认像素，根据图片高度，做好图文混排

展示题目的题干后，另起一列展示选项：

在一行能放下所有选项的情况下，一行展示全部选项，各选项间距相等

在一行放不下所有选项的情况下，一行展示全部/2 个选项，各选项间距相等（比如4个选项，这时就展示2个）

在一行放不下所有选项/2的情况下，一行展示全部/4 个选项，各选项间距相等（比如4个选项，这时就展示1个）

1. 答案&解析
默认收起，点击后展开可查看完整解析；点击“收起”后可继续收起

1. 展示当前任务下，题目的答题数据：
1. 点击“查看”进入该题目详情页
![analyze_in_table_image_PybzbldlNoNYg0xqOFccXhHance](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523405090.png)

![analyze_in_table_image_PsxWbEgV3opGFoxTzVqcplajnGg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523405750.png)

![analyze_in_table_image_DCBObWZ44oqd9wx3ifxcPpHRnzg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523406316.png)

##### 学生提问Tab

| 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| 任务报告>学生提问tab | 展示任务下有评论的素材 | AI课中有学生提问模块，其他类型任务待确认（巩固练习？作业？资源任务？）展示当前所选班级 & 当前素材下的内容 | ![in_table_image_CMoSbVPqcoznYTxrEPec4qDQnce](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523406984.png) |  |
|  | 按课程幻灯片顺序展示 课程内容 &本班评论数统计 | 按播放顺序展示当前课程下的全部幻灯片操作 | ![in_table_image_An8wbFUSLo9yJjxSrPicif6ynbg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523408522.png) |  |

![analyze_in_table_image_CMoSbVPqcoznYTxrEPec4qDQnce](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523406984.png)

![analyze_in_table_image_QhIQbTY1ioenB0xA6YNcsFWsnrc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523407910.png)

![analyze_in_table_image_An8wbFUSLo9yJjxSrPicif6ynbg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523408522.png)

#### xx学生的作业报告



| 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| xx学生的作业报告 | 来源入口 | 从“任务报告>学生报告tab”在学生列表中点击“查看详情”从“上课>学生课堂详情弹窗”在学习任务卡片点击“查看详情” |  |  |
| 顶部 | 页面名称 | 页面居中，展示“学生名+任务名称”左上角，展示“返回”，点击后返回前一页 | ![in_table_image_FjLobW20loc0yVx64afcpujenMe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523409187.png) |  |
|  | 答题概览 | 展示该任务开始时间 &要求完成时间若进度100%：展示该任务本人完成时间若进度不是100%：展示本人进度展示当前任务的素材范围：默认展示当前所选素材，若包含多个素材，点击抽屉后可以在抽屉页查看更多 | ![in_table_image_OvxrbrH5zop670xgEdickNnBn6b](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523411050.png) |  |
| 作业报告>答题结果tab | “共xx道题，xx道错题”共性错题解释 | 共xx道题：任务在当前学生&素材筛选条件下Xx道错题：任务在当前学生&素材筛选条件下 | ![in_table_image_XT3tbg5KZoLgUixkle4csF4nn8u](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523411558.png) |  |
|  | 题目信息展示 | 其他不变，展示当前任务下，本人在该题目的答题数据：正确：展示✅，错误：展示❌，部分对：展示半对符号尚未作答：展示“！尚未作答，暂无结果” |  |  |
|  | 题目面板 | 同之前 |  |  |
| 作业报告>学生提问tab | 展示任务下有评论的素材 | AI课中有学生提问模块，其他类型任务待确认（巩固练习？作业？资源任务？）展示当前所选班级 & 当前素材下的内容 | ![in_table_image_Mn2obNTTAoNIktxmxcFcV5r7nvf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523412163.png) |  |
|  | 按课程幻灯片顺序展示 课程内容 &该生评论数统计 | 按播放顺序展示当前课程下的全部幻灯片操作 | ![in_table_image_SqYxbKRGRoaiIGxQEhlcAyK9nZd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523413655.png) |  |

![analyze_in_table_image_FjLobW20loc0yVx64afcpujenMe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523409187.png)

![analyze_in_table_image_HauRbUdhqogkfyxqoVLcIIR2nPd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523409970.png)

![analyze_in_table_image_GWDMb4q4go7XEzxxvmGcVSglnFb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523410484.png)

![analyze_in_table_image_OvxrbrH5zop670xgEdickNnBn6b](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523411050.png)

![analyze_in_table_image_XT3tbg5KZoLgUixkle4csF4nn8u](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523411558.png)

![analyze_in_table_image_Mn2obNTTAoNIktxmxcFcV5r7nvf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523412163.png)

![analyze_in_table_image_XywgbDWOioFCdExMZNxcSTK9nyf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523412844.png)

![analyze_in_table_image_SqYxbKRGRoaiIGxQEhlcAyK9nZd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523413655.png)

#### 题目详情页（ui修改中）

| 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| 作业报告>答题结果tab>xx题目详情 | 展示题目基本信息 | 1.题干及选项2.标签3.展示答案解析 | ![in_table_image_YOe7bcJuXofS8YxeykIcT3LEn0g](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523414293.png) |  |
|  | 展示作答统计 | 1.选择题/判断题单选题多选题2.非选择题无分数（本期）有分数（未来） |  |  |
| 作业报告>xx学生的答题结果tab>xx学生的题目详情 | 展示题目基本信息 | 和班级一致 |  |  |
|  | 展示作答统计 | 仅展示本人数据 |  |  |

![analyze_in_table_image_YOe7bcJuXofS8YxeykIcT3LEn0g](https://static.test.xiaoluxue.cn/demo41/prd_images/1746523414293.png)

#### 策略

##### 鼓励

| 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| 系统建议鼓励的学生 |  | 进度：学习分：（学生端策略可能变化）其他： |  | P0 |
| 自定义 | 提醒设置 | 您可以对每一条提醒规则设置：开启/关闭，或修改提醒阈值（输入整数）进度：学习分：（学生端策略可能变化）其他： |  | P1 |

1. 任务提前完成：任务布置前，已完成自学的学生；
1. 和之前自己上次完成的任务比，提升10%以上
1. 高于同层同学10%以上
1. 达到自己最多连对题数
1. 达到班级最多连对题数
1. 达到自己最多提问数
1. 开启了提问
1. 任务提前完成：任务布置前，已完成自学的学生；
1. 和之前自己上次完成的任务比，提升10%以上
1. 高于同层同学10%以上
1. 达到自己最多连对题数
1. 达到自己对多提问数
1. 开启提问
##### 关注

| 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| 系统建议关注的学生 |  | 1.进度：2.学习分：（学生端策略可能变化）三类偏离值得关注： |  | P0 |
| 自定义 |  | 您可以对每一条提醒规则设置：开启/关闭，或修改提醒阈值（输入整数）进度：学习分：（学生端策略可能变化） |  | P1 |

1. 任务进行中：班级平均进度达50%，仍未进入学习的学生；
1. 任务已过期：仍未完成的学生
1. 任务已过期：延期完成的学生
1. 和之前自己上次完成的任务比，偏离20%以上
1. 当前任务内各课程间偏离20%以上
1. 偏离同层同学20%以上
##### 共性错题

| 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- |
| 共性错题 | 任务卡片 待关注题目任务详情顶部 待关注题目答题结果tab 共性错题 | 根据“答题人数>10人，任务中正确率＜60%”，标记共性错题 |  | P0 |
|  |  | 根据用户自定义的阈值 设置 共性错题 |  | P1 |

##### 

### 六、数据需求

#### 6.1 效果追踪

描述计划监控数据效果的方式及所需数据字段

1. 进入作业页整体 & 各级页面的pv，时长
1. 进入页面后切换tab & 核心操作的点击统计
#### 6.2 数据埋点

[教师端数据埋点汇总](https://wcng60ba718p.feishu.cn/wiki/Pw1GwQgfEi7TsPk3dgrcysMDnR0?sheet=ceLy7M)

### 七、a/b实验需求

若有，则写清楚实验方案

**实验目的**

XXX

**实验对象**

限制条件：端/版本/用户

实验组策略：

对照组策略：

**实验及分流**

启动时间、启动时用户量、预计持续时间

**评价指标**

结果指标（标明预期提升值）、过程指标

**暂无**



### 附：评审记录

记录存档历次需求评审中的会议纪要，方便追踪

举例：

**20250204 - 初评纪要**

1. 会后尽快确定巩固类型本期题目类型范围@杨宇航
已确认，并补充进文档

1. 数据下发方式待服务端和客户端最终确认@服务端rd @客户端rd
确定服务端下发，客户端仅做兜底逻辑

1. ...


**20250321 - 二次评审纪要**

1. XX
1. 

# AI课中课程框架+文档组件产品需求文档 V0.9

**文档版本**: V1.0  
**创建日期**: 2025-05-23  
**创建人**: AI产品经理  
**模型**: claude-sonnet-4

## 1. 需求背景与目标

### 1.1 需求目标
- 通过优化AI课中学习体验，解决前期demo版本中发现的关键问题，提升课程基础体验质量
- 该需求上线后，课程满意度预计可以提升30%，学习完成率预计可以达到85%以上
- 为后续引入积分体系、小组战队、互动讲题等丰富玩法奠定基础

### 1.2 需求背景
在前期两个版本的demo进校调研过程中，收集了一系列待解决的关键问题：
- 板书内容过多，高亮时机和老师讲解对不上
- 不能双击播放暂停，缺乏快捷交互
- 自由/跟随模式的切换交互不好理解
- 缺乏同步播放勾画轨迹功能
- 倍速选择不够丰富，缺少快进快退功能

## 2. 功能范围

### 2.1 核心功能

- **课程框架优化**: 优化开场页与结算页设计，支持课程进度管理和退出/继续功能
  - 优先级：高
  - 依赖关系：无

- **文档组件内容播放增强**: 引入同步播放勾画轨迹，未播放区域呈现模糊/弱化效果，默认展示字幕
  - 优先级：高  
  - 依赖关系：无

- **文档组件交互操作优化**: 增加双击、长按等快捷交互，增加1.75和3倍速，增加前进/后退10s功能
  - 优先级：高
  - 依赖关系：依赖于内容播放增强

### 2.2 辅助功能

- **掌握度外化展示**: 在结算页展示掌握度信息，包括完成进度、变化情况和能量值增加
- **课程反馈机制**: 提供5级评价反馈系统，支持详细反馈选项和文字输入
- **答题详情查看**: 支持题目列表查看、错题筛选和题目详情解析

### 2.3 非本期功能

- **积分体系和小组战队**: 建议下一迭代考虑
- **互动讲题功能**: 建议后续版本实现
- **文档长按评论功能**: 建议后续版本实现
- **费曼组件、互动组件**: 建议后续版本实现

## 3. 计算规则与公式

### 3.1 掌握度计算规则
- **掌握度完成进度公式**: 用户当前掌握度 / 目标掌握度，保留小数点后两位，取值范围[0, 1]
  - 参数说明：
    - 用户当前掌握度：基于答题正确率和题目难度计算得出
    - 目标掌握度：用户设置的学习目标，默认为0.8
  - 规则：
    - 如果用户未设置目标，默认目标掌握度为0.8
    - 初始掌握度不用于进度计算

### 3.2 学习表现计算规则
- **正确率公式**: 答对的题目数量 / 答题数量 × 100%
  - 参数说明：
    - 答对题目数量：用户正确回答的题目总数
    - 答题数量：用户回答的题目总数
  - 规则：
    - 子母题按照小题数量计算正确率
    - 已学完课程重新学习时，数据不累计

### 3.3 动效展示策略
- **动效选择规则**: 
  - 掌握度提升 → 开心动效
  - 掌握度不变且正确率70%-100% → 开心动效  
  - 掌握度不变且其他情况 → 鼓励动效

## 4. 用户场景与故事

### 场景1：学生首次进入AI课程学习
作为一个学生，我希望能够顺畅地开始AI课程学习，这样我就可以获得良好的第一印象和学习体验。目前的痛点是开场页缺乏仪式感，如果能够提供有吸引力的开场页和自动播放功能，对我的学习积极性会有很大帮助。

**关键步骤：**
1. 进入课程后看到开场页，显示课程信息和IP动效
2. 开场页自动播放IP动效+音频
3. 播放完成后自动进入AI课第一小节

```mermaid
sequenceDiagram
    participant 学生
    participant 开场页服务
    participant 课程内容服务
    
    学生->>开场页服务: 进入课程
    开场页服务->>学生: 展示开场页（课程信息+IP动效）
    开场页服务->>学生: 自动播放IP动效+音频
    开场页服务->>课程内容服务: 播放完成，请求第一小节
    课程内容服务->>学生: 进入第一小节内容
```

- 优先级：高
- 依赖关系：无

### 场景2：学生在文档组件中进行自主学习
作为一个学生，我希望能够在学习过程中自由控制学习节奏，这样我就可以根据自己的理解程度调整学习速度。目前的痛点是缺乏快捷交互和灵活的播放控制，如果能够支持双击播放暂停、倍速调整等功能，对我的学习效率会有很大帮助。

**关键步骤：**
1. 在跟随模式下观看内容，看到勾画轨迹同步播放
2. 滑动内容进入自由模式，内容暂停播放
3. 双击屏幕或点击"跟随老师"返回跟随模式
4. 使用长按加速、倍速调整等快捷操作

```mermaid
stateDiagram-v2
    [*] --> 跟随模式
    跟随模式 --> 自由模式 : 滑动内容
    自由模式 --> 跟随模式 : 双击屏幕/点击跟随老师
    跟随模式 --> 操作面板 : 单击文档外区域
    自由模式 --> 操作面板 : 单击文档外区域
    操作面板 --> 跟随模式 : 选择操作后
    操作面板 --> 自由模式 : 选择操作后
```

- 优先级：高
- 依赖关系：依赖于场景1

### 场景3：学生完成课程后查看学习报告
作为一个学生，我希望能够了解自己的学习表现和掌握情况，这样我就可以明确自己的学习成果和需要改进的地方。目前的痛点是缺乏详细的学习反馈，如果能够提供掌握度、答题情况等详细信息，对我的持续学习会有很大帮助。

**关键步骤：**
1. 完成课程后进入结算页，看到庆祝动效和文案
2. 查看学习表现数据（用时、答题数、正确率）
3. 查看掌握度信息和提升情况
4. 查看答题详情，了解具体题目表现
5. 提供课程反馈评价

```mermaid
flowchart TD
    A[完成课程] --> B[进入结算页]
    B --> C[展示庆祝动效]
    C --> D[显示学习表现数据]
    D --> E[显示掌握度信息]
    E --> F[提供答题详情入口]
    F --> G[课程反馈评价]
    G --> H[选择后续操作]
    H --> I[回看课程]
    H --> J[完成退出]
```

- 优先级：中
- 依赖关系：依赖于场景1和场景2

## 5. 业务流程图

```mermaid
flowchart TD
    A[用户进入课程] --> B[开场页展示]
    B --> C[自动播放IP动效+音频]
    C --> D[进入第一个组件]
    D --> E{组件类型}
    E -->|文档组件| F[文档内容播放]
    E -->|练习组件| G[题目展示]
    F --> H{用户操作}
    H -->|滑动内容| I[进入自由模式]
    H -->|正常观看| J[跟随模式继续]
    I --> K[暂停播放，显示跟随老师按钮]
    K --> L{用户选择}
    L -->|双击/点击跟随老师| J
    L -->|单击文档外| M[唤起操作面板]
    M --> N[视频播控操作]
    J --> O[内容播放完成]
    G --> P[用户答题]
    P --> Q[显示答题结果]
    O --> R{是否还有组件}
    Q --> R
    R -->|是| D
    R -->|否| S[进入结算页]
    S --> T[展示学习表现和掌握度]
    T --> U[提供课程反馈]
    U --> V[用户选择后续操作]
    V -->|回看课程| D
    V -->|完成| W[退出课程]
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：文档组件内容加载响应时间不超过2秒
- 并发用户：系统需支持1000个并发用户同时学习，保证性能不衰减
- 数据量：系统需支持10万条学习记录的存储和查询，响应时间不超过1秒

### 6.2 安全需求
- 权限控制：课程内容访问需要有效的用户身份认证
- 数据加密：学习数据和答题记录需要使用AES-256进行加密存储和传输
- 操作审计：对学习进度变更、答题提交等关键操作需要记录操作日志，包括用户ID、时间戳、操作类型

## 7. 验收标准

### 7.1 功能验收

- **开场页功能**：
  - 使用场景：当用户首次进入课程时
  - 期望结果：系统应展示包含课程信息的开场页，自动播放IP动效+音频，播放完成后自动进入第一小节

- **文档组件模式切换**：
  - 使用场景：当用户在跟随模式下滑动内容时
  - 期望结果：系统应自动切换到自由模式，暂停所有播放内容，显示"跟随老师"按钮

- **快捷交互功能**：
  - 使用场景：当用户双击屏幕或长按文档外区域时
  - 期望结果：双击应切换播放/暂停状态，长按应启动3倍速播放

- **结算页展示**：
  - 使用场景：当用户完成全部课程内容时
  - 期望结果：系统应展示学习表现数据、掌握度信息和课程反馈入口

### 7.2 性能验收
- 响应时间：
  - 给定正常网络环境，当加载文档组件内容，响应时间不超过2秒
  - 给定正常网络环境，当查询学习记录，响应时间不超过1秒
- 并发用户：
  - 给定1000个并发用户同时访问，系统运行30分钟，无异常，平均响应时间不超过3秒

### 7.3 安全验收
- 权限控制：
  - 给定未登录用户，当访问课程内容，返回身份认证错误
  - 给定已登录用户，当访问课程内容，可以正常使用
- 数据加密：
  - 给定查询学习数据，当检查存储状态，数据是加密状态，且使用AES-256算法
- 操作审计：
  - 给定用户提交答题，当执行操作，操作日志正常记录，包含用户ID、时间戳、操作类型

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：文档组件的操作界面简洁友好，3步内可完成主要操作
- 容错处理：网络异常发生时，系统可以显示友好提示并支持重试，并给出明确的错误信息
- 兼容性：需支持Chrome/Safari/移动端浏览器等主流环境，保证界面和功能正常

### 8.2 维护性需求
- 配置管理：倍速选项、快进快退时长等参数可由系统管理员在后台界面进行配置和调整
- 监控告警：播放异常、答题提交失败等情况发生时，系统自动告警，通知相关技术人员
- 日志管理：系统自动记录用户学习行为日志，日志保存30天，可供系统管理员查询和分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 用户可以通过结算页反馈系统和客服渠道提交反馈
- 每周定期收集和分析用户反馈，识别改进点

### 9.2 迭代计划
- 迭代周期：每2周
- 每次迭代结束后，评估需求实现情况和用户反馈，调整下一个迭代的优先级和计划

## 10. 需求检查清单

使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
| ----------- | --------------- | ------ | ------ | ------ | -------- | -------- | ---- |
| 课程开场页优化，展示课程信息和IP动效 | 开场页功能设计 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 结算页优化，增加掌握度、答题情况展示 | 结算页功能设计 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 课程进度管理，支持组件间自由切换 | 课程进度功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 退出/继续课程功能 | 课程状态管理 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 文档组件增加勾画轨迹同步播放 | 内容播放增强 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 未播放区域呈现模糊/弱化效果 | 内容播放增强 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 默认展示字幕 | 内容播放增强 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 跟随模式和自由模式切换 | 模式切换功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 双击播放/暂停快捷交互 | 快捷交互功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 长按3倍速播放 | 快捷交互功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 增加1.75和3倍速选择 | 播放控制功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 增加前进/后退10s功能 | 播放控制功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 单击文档内容"从这里学"功能 | 快捷学习功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 单击文档外区域唤起操作面板 | 操作面板功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 掌握度外化展示和计算规则 | 掌握度功能 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 需要与掌握度策略文档对接 |
| 学习表现数据展示（用时、答题数、正确率） | 学习数据统计 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 答题详情查看和题目列表 | 答题详情功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 课程反馈评价系统 | 反馈功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 动效展示策略（开心/鼓励动效） | 动效展示规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 鼓励文案展示规则 | 文案展示规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 埋点需求和数据存储需求 | 数据需求 | ✅ | ✅ | ✅ | ✅ | ✅ | |

- 完整性：原始需求是否都有对应的优化后需求点，无遗漏
- 正确性：优化后需求描述是否准确表达了原始需求的意图
- 一致性：优化后需求之间是否有冲突或重复  
- 可验证性：优化后需求是否有明确的验收标准
- 可跟踪性：优化后需求是否有明确的优先级和依赖关系

✅表示通过；❌表示未通过；⚠️表示描述正确，但细节不完整（如计算规则、阈值等，需要和 PM 进一步沟通）；N/A表示不适用。

## 11. 附录

### 11.1 原型图
参考原始需求文档中的UI设计图和交互原型图

### 11.2 术语表
- **跟随模式**: 学生跟随老师讲解节奏进行学习的模式
- **自由模式**: 学生可以自主控制学习进度的模式  
- **勾画轨迹**: 与JS内容同步播放的板书绘制过程
- **掌握度**: 反映学生对知识点掌握程度的量化指标
- **IP动效**: 课程中虚拟角色的动画效果
- **从这里学**: 允许学生从指定内容位置开始学习的功能

### 11.3 参考资料
- PRD - AI课中 - 课程框架 + 文档组件 V0.9
- PRD - 掌握度策略 - V1.0
- AI课中需求盘点文档
- 相关设计稿和视觉规范

--- 等待您的命令，指挥官
# 课中练习推题策略服务架构设计

## 1. 引言

### 1.1 文档目的
为课中练习推题策略服务提供清晰的Golang服务端架构蓝图，指导核心模块/服务的设计与开发，确保系统满足关键的质量属性，并支持在Kubernetes环境中高效运行和扩展。

### 1.2 系统概述
课中练习推题策略服务是一个在AI课堂教学过程中，负责为学生提供合理练习题的策略服务。其核心目标是根据设定的推题策略，在课程中适时向学生推送练习题，以提升学生专注度、巩固知识点、增强课堂互动感。主要交互方包括学生客户端、教师端、内容平台和用户中心。

### 1.3 设计目标与核心原则
- **高可用性**：保证99.99%的服务可用性，确保课堂教学中推题服务的稳定性
- **低延迟**：题目推送响应时间P99<100ms，保证课堂教学流畅性
- **可扩展性**：支持未来2年用户量增长5倍，峰值QPS达5k
- **可维护性**：设计清晰的接口和模块划分，新成员1周内可理解并贡献代码
- **可测试性**：核心算法和策略模块的单元测试覆盖率>80%

### 1.4 范围
- **包含**：课中练习推题策略服务的核心模块、与其他服务的交互接口、数据流转
- **不包含**：UI/前端实现、详细数据库表结构、具体的第三方服务集成协议细节

### 1.5 术语与缩写
- **练习组件**：课程中包含的练习单元，每个练习组件可包含多个题目
- **相似题**：与当前题目知识点相同但表现形式不同的题目，用于当学生答错时进行补充练习
- **推题策略**：决定向学生推送哪些题目、何时推送以及推送顺序的一系列规则

## 2. 需求提炼与架构驱动因素

### 2.1 核心业务能力
根据需求文档分析，课中练习推题策略服务需要具备以下核心业务能力：

- **课中练习结构管理**：管理课程中的练习组件及其包含的题目，支持单题和相似题组两种题目类型
- **题目推送策略执行**：根据预设规则和学生答题结果，决定下一题的推送逻辑
- **答题结果处理**：接收和处理学生的答题结果，判断正确性并记录
- **相似题推送逻辑**：当学生答错题目时，检查并推送相似题
- **练习会话管理**：维护学生在某一练习组件中的答题状态和进度

### 2.2 关键质量属性

#### 2.2.1 性能
- **关键需求**：推题策略计算响应时间P99<100ms，以确保课堂教学流畅体验
- **并发能力**：支持单课堂最大200名学生同时在线练习
- **Golang考量**：
  - 使用goroutine池高效处理并发请求
  - 利用Redis缓存热点数据（如题目内容、相似题关系）
  - 优化数据库查询，使用索引和批量操作减少数据库负载

#### 2.2.2 可用性
- **关键需求**：99.99%服务可用性，确保课堂教学不中断
- **Golang考量**：
  - 设计无状态服务，便于在k8s环境中水平扩展和故障替换
  - 实现健康检查端点，配合k8s的liveness/readiness探针
  - 针对外部依赖服务的故障设计降级策略和容错机制

#### 2.2.3 可伸缩性
- **关键需求**：支持用户量和并发请求的增长，预期2年内用户增长5倍
- **Golang考量**：
  - 无状态服务设计，避免本地状态依赖
  - 使用连接池管理数据库和Redis连接
  - 设计可水平扩展的数据模型，避免单点瓶颈

#### 2.2.4 可维护性
- **关键需求**：清晰的代码结构和文档，新成员1周内可理解并参与开发
- **Golang考量**：
  - 遵循标准Go项目布局
  - 采用DDD分层架构，明确各层职责
  - 提供详细的API文档和领域模型说明

#### 2.2.5 可测试性
- **关键需求**：核心算法和策略的单元测试覆盖率>80%
- **Golang考量**：
  - 设计清晰的接口边界，便于单元测试和模拟
  - 使用依赖注入模式，便于替换真实依赖项
  - 利用Go标准库testing和testify框架构建测试套件

#### 2.2.6 安全性
- **关键需求**：确保学生无法获取题目答案或干扰推题逻辑
- **Golang考量**：
  - 实现JWT令牌验证，确保请求合法性
  - 使用参数校验中间件，防止注入和参数篡改
  - 实施API访问控制，确保学生只能查看自己的数据

### 2.3 主要约束与依赖

#### 2.3.1 技术栈约束
必须遵循团队统一的技术栈约束：
- Go 1.22版本
- Kratos微服务框架
- PostgreSQL数据库
- Redis缓存
- Kafka用于事件通知

#### 2.3.2 外部系统依赖
系统需要与以下外部服务进行交互：

1. **内容平台（题库服务）**
   - **需要的数据**：题目内容、答案、难度、知识点、相似题关系
   - **交互方式**：gRPC接口调用
   - **依赖程度**：高，核心依赖

2. **用户中心**
   - **需要的数据**：用户身份认证、基本信息
   - **交互方式**：gRPC接口调用，JWT验证
   - **依赖程度**：高，认证必要依赖

3. **教师服务**
   - **需要的数据**：课程信息、班级学生名单
   - **交互方式**：gRPC接口调用
   - **依赖程度**：中，配置数据依赖

## 3. 宏观架构设计

### 3.1 架构风格与模式

课中练习推题策略服务采用**分层架构**与**领域驱动设计(DDD)**相结合的架构风格，并融合**事件驱动**模式处理答题结果和状态变更。

**分层架构**选择理由：
- 清晰分离关注点，使核心业务逻辑与技术实现解耦
- 提高代码可维护性和测试性，便于团队协作开发
- 适合业务规则相对稳定且边界清晰的系统

**领域驱动设计**选择理由：
- 将复杂的推题策略和业务规则封装在领域层，使领域逻辑更清晰
- 便于应对未来可能的业务变化（如扩展推题策略、增加个性化推荐）
- 通过领域事件支持系统解耦和扩展

**事件驱动**选择理由：
- 答题结果可视为事件，触发后续的推题逻辑和状态更新
- 便于实现异步通知和状态变更，提高系统响应性
- 为未来扩展提供良好基础，如接入学习分析系统

### 3.2 系统分层视图

#### 3.2.1 推荐的逻辑分层模型

```mermaid
flowchart TD
    subgraph "推题策略服务 ExerciseStrategy Service"
        APILayer[API接口层]
        AppServiceLayer[应用服务层]
        DomainLayer[领域层]
        InfrastructureLayer[基础设施层]
        
        APILayer --> AppServiceLayer
        AppServiceLayer --> DomainLayer
        AppServiceLayer --> InfrastructureLayer
        DomainLayer --> InfrastructureLayer
    end
    
    subgraph "外部系统适配"
        InfrastructureLayer --> QuestionAdapter[题库服务适配器]
        InfrastructureLayer --> UserAdapter[用户中心适配器]
        InfrastructureLayer --> TeacherAdapter[教师服务适配器]
    end
    
    %% 外部系统
    QuestionAdapter -.-> QuestionSvc[题库服务]
    UserAdapter -.-> UserSvc[用户中心]
    TeacherAdapter -.-> TeacherSvc[教师服务]
    
    %% 数据存储
    InfrastructureLayer -.-> DB[(PostgreSQL)]
    InfrastructureLayer -.-> Cache[(Redis缓存)]
```

**各层职责说明**：

1. **API接口层**
   - **职责**：处理HTTP/gRPC请求，进行参数验证、请求转发和响应格式化
   - **封装变化**：接口协议变化、客户端需求变化、认证授权逻辑
   - **主要组件**：HTTP控制器、gRPC服务实现、请求/响应DTO、中间件（认证、日志）

2. **应用服务层**
   - **职责**：编排业务流程，协调多个领域对象协作，管理事务边界
   - **封装变化**：业务流程变化、跨领域对象的协作方式变化
   - **主要组件**：推题应用服务、答题记录应用服务、练习会话管理服务

3. **领域层**
   - **职责**：封装核心业务规则和推题策略，定义领域模型和领域服务
   - **封装变化**：业务规则变化、推题算法优化、领域模型调整
   - **主要组件**：推题策略领域服务、练习组件领域模型、题目领域模型、答题记录领域模型

4. **基础设施层**
   - **职责**：提供技术实现，包括数据持久化、缓存、外部系统集成
   - **封装变化**：持久化技术变化、外部系统API变化、缓存策略调整
   - **主要组件**：仓储实现、外部服务适配器、缓存管理器、事件发布器

**分层依赖规则**：
- 遵循**单向依赖**原则，上层依赖下层，下层不依赖上层
- 领域层是核心业务逻辑所在，不依赖于技术实现细节
- 通过**依赖倒置原则**，领域层定义接口，基础设施层提供实现

**分层模型选择理由**：
- 推题策略业务具有一定复杂度，需要清晰的分层和关注点分离
- 领域层专注于推题核心算法和业务规则，与技术实现解耦
- 应用服务层协调处理跨领域的业务流程，如答题-评分-推题的完整流程

### 3.3 模块/服务划分视图

#### 3.3.1 核心模块/服务识别与职责

基于业务边界和领域逻辑，将系统划分为以下核心模块：

1. **策略引擎模块 (StrategyEngine)**
   - **职责**：实现核心推题决策逻辑，根据答题情况和配置规则决定下一题
   - **边界**：推题算法、规则匹配与执行逻辑
   - **核心能力**：答错推相似题逻辑、题组随机选题、题目顺序维护

2. **练习会话模块 (ExerciseSession)**
   - **职责**：管理学生在练习组件中的答题会话状态
   - **边界**：会话生命周期（创建、进行中、结束）管理
   - **核心能力**：会话创建、状态查询、进度记录、会话恢复

3. **答题记录模块 (AnswerRecord)**
   - **职责**：记录和管理学生答题历史和结果
   - **边界**：答题提交、评分、结果存储
   - **核心能力**：答案验证、答题结果记录、答题历史查询

4. **练习组件模块 (ExerciseComponent)**
   - **职责**：管理课程中练习组件的配置和结构
   - **边界**：练习组件定义、题目关系配置
   - **核心能力**：组件信息查询、题目顺序配置读取、题组管理

5. **题目访问模块 (QuestionAccess)**
   - **职责**：访问和缓存题目信息，与题库服务交互
   - **边界**：题目数据获取、转换、缓存
   - **核心能力**：题目内容查询、相似题关系查询、题目元数据获取

#### 3.3.2 模块/服务交互模式与数据契约

```mermaid
graph TD
    Client[学生客户端]
    
    subgraph "推题策略服务"
        AuthModule[认证模块]
        SessionModule[练习会话模块]
        StrategyModule[策略引擎模块]
        AnswerModule[答题记录模块]
        ComponentModule[练习组件模块]
        QuestionModule[题目访问模块]
    end
    
    subgraph "外部服务"
        QuestionService[题库服务]
        UserService[用户中心]
        TeacherService[教师服务]
    end
    
    %% 客户端与服务交互
    Client -- 1.获取题目 --> AuthModule
    Client -- 2.提交答案 --> AuthModule
    
    %% 内部模块交互
    AuthModule -- 验证后转发 --> SessionModule
    SessionModule -- 获取下一题 --> StrategyModule
    StrategyModule -- 获取配置 --> ComponentModule
    StrategyModule -- 获取答题历史 --> AnswerModule
    StrategyModule -- 获取题目/相似题 --> QuestionModule
    AuthModule -- 验证后转发 --> AnswerModule
    AnswerModule -- 答题后触发推题 --> StrategyModule
    
    %% 与外部服务交互
    QuestionModule -- 查询题目信息 --> QuestionService
    AuthModule -- 验证用户身份 --> UserService
    ComponentModule -- 获取课程信息 --> TeacherService
```

**关键交互说明**：

1. **获取题目流程**
   - **交互方式**：同步HTTP/gRPC请求
   - **数据契约**：
     - 请求：会话ID（或首次请求的课程ID、组件ID）
     - 响应：题目信息（ID、内容、选项）、会话状态

2. **答题提交流程**
   - **交互方式**：同步HTTP/gRPC请求
   - **数据契约**：
     - 请求：会话ID、题目ID、答案
     - 响应：答题结果（正确/错误）、正确答案（错误时）

3. **推题决策流程**
   - **交互方式**：内部模块调用
   - **数据契约**：
     - 输入：学生ID、组件ID、当前题目ID、答题结果
     - 输出：下一题ID、是否为相似题、剩余题目数

4. **题目信息获取**
   - **交互方式**：gRPC调用，带缓存
   - **数据契约**：
     - 请求：题目ID列表
     - 响应：题目详情（内容、选项、答案、难度、知识点）

### 3.4 关键业务流程的高层视图

#### 3.4.1 学生顺序答题流程

```mermaid
sequenceDiagram
    actor Student as 学生
    participant Client as 客户端
    participant API as API层
    participant Session as 会话模块
    participant Strategy as 策略引擎
    participant Component as 练习组件模块
    participant Question as 题目访问模块
    
    Student->>Client: 进入练习组件
    Client->>API: 请求开始练习(课程ID,组件ID)
    API->>Session: 创建会话(学生ID,组件ID)
    Session->>Strategy: 获取第一题
    Strategy->>Component: 获取组件配置与题目顺序
    Component-->>Strategy: 返回配置信息
    
    alt 下一项是相似题组
        Strategy->>Strategy: 从题组中随机选择一题
    else 下一项是单题
        Strategy->>Strategy: 直接选择该题
    end
    
    Strategy->>Question: 获取题目详情
    Question-->>Strategy: 返回题目内容
    Strategy-->>Session: 返回所选题目
    Session-->>API: 返回会话与题目信息
    API-->>Client: 返回题目展示信息
    Client->>Student: 展示题目
    
    Student->>Client: 提交答案
    Client->>API: 提交答案(会话ID,题目ID,答案)
    API->>Session: 处理答题(会话ID,题目ID,答案)
    Session->>Strategy: 评估答案
    Strategy->>Strategy: 判断答案正确
    Strategy-->>Session: 答题正确，推送下一题
    Session-->>API: 返回答题结果与下一题
    API-->>Client: 返回结果与下一题信息
    Client->>Student: 显示结果并加载下一题
    
    Note over Student,Question: 继续答题直到练习组件中所有题目完成
```

**流程说明**：
1. 学生通过客户端进入练习组件，系统创建答题会话
2. 策略引擎根据配置获取第一题（若为相似题组则随机选择一题）
3. 学生作答后提交答案，系统判断正确与否
4. 答对情况下，系统继续推送下一题
5. 循环上述过程直到完成所有题目

#### 3.4.2 错题推送相似题流程

```mermaid
sequenceDiagram
    actor Student as 学生
    participant Client as 客户端
    participant API as API层
    participant Session as 会话模块
    participant Strategy as 策略引擎
    participant Answer as 答题记录模块
    participant Question as 题目访问模块
    
    Student->>Client: 提交答案
    Client->>API: 提交答案(会话ID,题目ID,答案)
    API->>Session: 处理答题(会话ID,题目ID,答案)
    Session->>Strategy: 评估答案
    
    Strategy->>Strategy: 判断答案错误
    Strategy->>Answer: 记录错题(学生ID,题目ID,答案)
    Answer-->>Strategy: 确认记录完成
    
    Strategy->>Question: 查询是否有相似题
    Question-->>Strategy: 返回相似题列表
    
    alt 有未推送过的相似题
        Strategy->>Strategy: 从相似题中随机选择一题
        Strategy-->>Session: 返回选中的相似题
        Session-->>API: 返回答题结果和相似题
        API-->>Client: 返回结果和相似题
        Client->>Student: 显示结果并加载相似题
        
        Student->>Client: 作答相似题
        Client->>API: 提交相似题答案
        API->>Session: 处理相似题答题
        Session->>Strategy: 评估相似题答案
        Strategy-->>Session: 无论对错，继续原题序列
    else 无相似题或已推送过
        Strategy-->>Session: 直接返回原序列下一题
        Session-->>API: 返回答题结果和下一题
        API-->>Client: 返回结果和下一题
        Client->>Student: 显示结果并加载下一题
    end
```

**流程说明**：
1. 学生提交答案，系统判断答案错误
2. 策略引擎查询当前题目是否有相似题且未被推送过
3. 若有符合条件的相似题，随机选择一题推送给学生
4. 学生作答相似题后，无论对错，继续推送原序列题目
5. 若无相似题或已推送过，直接推送原序列下一题

### 3.5 数据架构概述

#### 3.5.1 主要数据存储选型与理由

1. **PostgreSQL**：
   - **用途**：存储结构化业务数据，包括练习组件配置、答题记录等
   - **选型理由**：
     - 支持复杂数据类型和查询，满足复杂数据关系需求
     - 提供事务支持，确保数据一致性
     - 符合团队技术栈约束

2. **Redis**：
   - **用途**：缓存热点数据（题目信息、相似题关系）和会话状态
   - **选型理由**：
     - 高性能、低延迟，适合临时数据缓存和会话管理
     - 支持多种数据结构，适合存储复杂临时数据
     - 提供分布式锁机制，确保并发安全

3. **Kafka**（仅用于事件通知）：
   - **用途**：发布答题事件和状态变更事件，供分析系统消费
   - **选型理由**：
     - 高吞吐量，适合事件流处理
     - 支持消息持久化，确保事件不丢失

#### 3.5.2 核心领域对象/数据结构的概念定义

1. **练习组件 (ExerciseComponent)**
   - **核心属性**：组件ID、课程ID、组件名称、题目顺序列表
   - **业务含义**：课程中的练习单元，包含一组有序练习题
   - **关联关系**：与课程、题目（单题或题组）关联

2. **题目 (Question)**
   - **核心属性**：题目ID、题目内容、答案、难度等级、知识点标签
   - **业务含义**：学习内容的检验单元，测试学生对知识点掌握程度
   - **关联关系**：与知识点、相似题关联

3. **相似题组 (SimilarQuestionGroup)**
   - **核心属性**：题组ID、包含的题目ID列表、关联知识点
   - **业务含义**：围绕相同知识点的一组题目集合
   - **关联关系**：与题目、知识点关联

4. **答题记录 (AnswerRecord)**
   - **核心属性**：记录ID、学生ID、题目ID、答案、是否正确、作答时间
   - **业务含义**：记录学生对特定题目的作答情况
   - **关联关系**：与学生、题目、会话关联

5. **练习会话 (ExerciseSession)**
   - **核心属性**：会话ID、学生ID、组件ID、当前题目ID、已完成题目列表
   - **业务含义**：学生在特定练习组件中的作答过程状态跟踪
   - **关联关系**：与学生、练习组件、题目关联

#### 3.5.3 数据一致性与同步策略

1. **数据一致性策略**：
   - 使用数据库事务确保单次操作的原子性
   - 对于答题记录和会话状态更新，采用乐观锁避免并发冲突
   - 设计幂等API操作，避免重复提交造成的数据异常

2. **数据同步策略**：
   - 题目信息采用定时同步+缓存更新，定期从题库服务拉取最新数据
   - 通过Kafka发布领域事件（如答题完成、会话状态变更）
   - 缓存采用"先更新数据库，后删除缓存"策略，避免数据不一致

3. **关键场景举例**：
   - **学生答题结果一致性**：答题提交后，先持久化答题记录，再更新会话状态，最后调用推题逻辑，整个过程在一个事务中完成
   - **题目信息最新性**：题目更新后，通过版本号或定时任务触发缓存更新

## 4. Golang技术栈与关键库选型

### 4.1 Go 版本选择
- **Go 1.22**：
  - 使用团队技术栈约束中指定的版本
  - 利用Go 1.22中的泛型支持和性能改进
  - 确保与团队开发环境和CI/CD流程兼容

### 4.2 Web/RPC框架
- **Kratos 2.8.3**：
  - 选择原因：符合团队技术规范，提供完整的微服务框架能力
  - 核心功能：支持HTTP/gRPC协议，内置中间件，依赖注入，配置管理
  - 应用场景：构建API接口，服务注册发现，服务间通信

### 4.3 数据库驱动与ORM
- **GORM v1.25.0**：
  - 选择原因：与PostgreSQL兼容性好，API友好，功能丰富
  - 核心功能：ORM映射，事务支持，关联关系处理，迁移工具
  - 应用场景：所有数据库操作，包括练习会话状态管理和答题记录存储

- **sqlx**：
  - 选择原因：作为GORM的补充，用于复杂查询场景
  - 核心功能：轻量级SQL辅助工具，支持结构体映射
  - 应用场景：性能敏感的复杂查询，如统计分析

### 4.4 缓存客户端库
- **go-redis/redis v8.11.5**：
  - 选择原因：成熟稳定，API友好，功能完整
  - 核心功能：连接池管理，多种数据结构操作，分布式锁
  - 应用场景：题目信息缓存，会话状态管理，分布式锁

### 4.5 消息队列客户端库
- **segmentio/kafka-go v0.4.47**：
  - 选择原因：纯Go实现，性能良好，不依赖CGo
  - 核心功能：Kafka生产者/消费者API，异步消息处理
  - 应用场景：发布答题事件，学习进度变更通知

### 4.6 配置管理
- **Kratos配置模块**：
  - 选择原因：与Kratos框架无缝集成，支持多种配置源
  - 核心功能：配置加载，动态更新，环境变量支持
  - 应用场景：服务配置，数据库连接参数，缓存策略配置

### 4.7 日志库
- **Kratos日志模块**：
  - 选择原因：与Kratos框架集成，支持结构化日志
  - 核心功能：多级别日志，字段注入，输出格式定制
  - 应用场景：系统日志，业务日志，审计日志

### 4.8 监控/Metrics库
- **prometheus/client_golang**：
  - 选择原因：Prometheus官方Go客户端，与K8s监控生态集成
  - 核心功能：指标收集，指标暴露，自定义指标
  - 应用场景：服务健康监控，业务指标收集，性能分析

### 4.9 测试框架与工具
- **标准库testing + testify**：
  - 选择原因：Go标准库和社区流行测试辅助工具
  - 核心功能：单元测试，断言，Mock支持
  - 应用场景：业务逻辑测试，集成测试，API测试

- **golang/mock**：
  - 选择原因：官方支持的Mock生成工具
  - 核心功能：接口Mock，行为验证
  - 应用场景：隔离外部依赖的单元测试

### 4.10 其他关键Go库
- **google/wire**：
  - 选择原因：依赖注入工具，简化复杂依赖关系
  - 核心功能：代码生成，依赖图构建
  - 应用场景：服务启动组装，依赖注入

- **pkg/errors**：
  - 选择原因：增强错误处理能力，支持堆栈跟踪
  - 核心功能：错误包装，堆栈信息
  - 应用场景：错误处理，排查问题

- **golang-module/carbon**：
  - 选择原因：简化时间处理，功能丰富
  - 核心功能：时间计算，格式化，比较
  - 应用场景：过期时间计算，时间窗口处理

## 5. 跨功能设计考量

### 5.1 安全设计

#### 5.1.1 认证与授权机制
- 接入用户中心服务进行统一身份认证，采用JWT Bearer令牌方式
- 使用中间件实现API请求认证，验证令牌有效性和权限
- 按角色划分权限：学生只能访问自己的答题数据，教师可查看班级数据

#### 5.1.2 数据安全
- 敏感数据传输加密：采用TLS 1.3保障API通信安全
- 答案保护：学生在答题前无法获取题目答案
- 防止作弊：服务端校验答题顺序合法性，防止跳题

#### 5.1.3 防御恶意请求
- 实现限流机制：基于用户ID和IP的请求频率限制
- 输入验证：所有API请求参数严格校验，防止注入攻击
- 会话有效性校验：确保会话未过期且属于当前用户

### 5.2 错误处理与容错机制

#### 5.2.1 统一错误处理
- 定义领域错误类型，区分业务错误和系统错误
- 使用pkg/errors包装错误，保留错误上下文和堆栈信息
- API响应中返回结构化错误信息，包含错误码和友好提示

#### 5.2.2 外部依赖故障处理
- 内容平台服务故障：本地缓存最近使用的题目信息，可短暂离线工作
- 用户中心服务故障：令牌本地验证，降级为有限功能模式
- 数据库临时不可用：使用Redis作为临时存储，异步写入恢复后的数据库

#### 5.2.3 优雅降级策略
- 缓存未命中时直接请求原始数据源
- 非核心功能（如学习分析）可在高负载时暂时关闭
- 提供基本的推题模式作为复杂策略不可用时的备选方案

### 5.3 性能与并发策略

#### 5.3.1 缓存策略
- 多级缓存：本地内存缓存 + Redis分布式缓存
- 热点数据缓存：题目内容、答案、相似题关系等变化较少的数据
- 缓存淘汰策略：LRU策略，定期刷新，写时失效

#### 5.3.2 并发处理
- 使用goroutine池处理并发请求，控制资源使用
- 通过context传递超时控制，避免长时间阻塞
- 关键操作使用分布式锁保证数据一致性

#### 5.3.3 数据库优化
- 索引优化：为频繁查询字段创建索引
- 批量操作：聚合多次数据库操作，减少网络往返
- 读写分离：考虑将分析查询导向只读副本

### 5.4 可观测性设计

#### 5.4.1 结构化日志
- 采用JSON格式的结构化日志，包含固定字段：
  - 时间戳、日志级别、服务名称、TraceID、用户ID、请求路径、消息内容
- 关键业务操作记录详细日志，包括推题决策过程、答题结果
- 错误日志中包含详细上下文和堆栈信息

#### 5.4.2 关键指标监控
- 业务指标：
  - 推题请求量：QPS、按题目类型的分布
  - 答题统计：正确率、错误率、按知识点的分布
  - 会话指标：活跃会话数、平均答题时长、完成率
- 系统指标：
  - API延迟：各接口P95/P99响应时间
  - 资源使用：CPU、内存、网络IO、数据库连接
  - 错误率：API错误率、外部服务调用失败率

#### 5.4.3 分布式追踪
- 使用OpenTelemetry收集全链路追踪数据
- 关键业务流程埋点：推题决策、答题评估、相似题推送
- 外部服务调用追踪：向题库服务、用户中心的请求

### 5.5 配置管理

#### 5.5.1 配置分层
- 服务基础配置：监听地址、日志级别、最大并发数
- 业务配置：缓存过期时间、默认会话超时时间
- 推题策略参数：相似题推送阈值、题目难度递进系数

#### 5.5.2 配置来源
- 环境变量：部署环境特定配置
- 配置文件：默认配置和环境无关配置
- 配置中心：动态可调整的业务参数

## 6. 风险与挑战

### 6.1 技术风险

| 风险 | 可能性 | 影响 | 应对策略 |
|------|--------|------|----------|
| 题库服务响应时间不稳定，影响推题流畅度 | 中 | 高 | 1. 实现多层缓存策略减少外部依赖<br>2. 设置合理的超时和重试机制<br>3. 预加载可能需要的题目数据 |
| Redis缓存在高并发下性能瓶颈 | 低 | 中 | 1. 监控Redis性能指标<br>2. 优化缓存策略，避免大key<br>3. 考虑Redis集群部署 |
| 数据库连接池耗尽 | 低 | 高 | 1. 合理配置连接池大小<br>2. 实现连接池监控<br>3. 优化SQL减少长事务 |
| 并发处理导致的数据竞争问题 | 中 | 高 | 1. 仔细设计并发访问模式<br>2. 使用分布式锁保护共享资源<br>3. 充分测试并发场景 |

### 6.2 业务风险

| 风险 | 可能性 | 影响 | 应对策略 |
|------|--------|------|----------|
| 推题策略不符合教学预期 | 中 | 高 | 1. 提前与教研团队充分沟通<br>2. 策略可配置化，支持灵活调整<br>3. 提供策略评估和调优机制 |
| 学生答题数据丢失 | 低 | 高 | 1. 实现多重备份机制<br>2. 答题记录实时持久化<br>3. 设计会话恢复机制 |
| 题目内容或答案不准确 | 中 | 高 | 1. 与题库服务建立内容更新通知机制<br>2. 实现内容版本控制<br>3. 提供反馈渠道 |
| 用户体验不佳（如推题延迟） | 中 | 中 | 1. 建立端到端性能监控<br>2. 进行性能优化<br>3. 预加载和预测下一题 |

### 6.3 团队风险

| 风险 | 可能性 | 影响 | 应对策略 |
|------|--------|------|----------|
| 对领域驱动设计理解不一致 | 中 | 中 | 1. 提供清晰的设计文档和示例<br>2. 团队培训和知识分享<br>3. 代码审查确保一致性 |
| 技术栈经验不足 | 低 | 中 | 1. 提供学习资源和培训<br>2. 建立技术支持渠道<br>3. 安排有经验的成员指导 |
| 与外部团队协作不畅 | 中 | 中 | 1. 建立明确的接口契约<br>2. 定期沟通机制<br>3. 提前识别依赖项 |

## 7. 未来演进方向

### 7.1 核心扩展点

1. **推题策略接口**
   ```go
   // 通过接口抽象推题策略，支持未来添加新策略
   type RecommendationStrategy interface {
       NextQuestion(ctx context.Context, sessionID string, lastAnswer *AnswerResult) (*Question, error)
       // 其他策略方法
   }
   ```

2. **评分规则扩展点**
   ```go
   // 支持不同的答案评估方式
   type AnswerEvaluator interface {
       Evaluate(ctx context.Context, question *Question, userAnswer string) (*EvaluationResult, error)
       // 其他评估方法
   }
   ```

3. **事件处理扩展点**
   ```go
   // 基于事件的扩展机制
   type EventSubscriber interface {
       HandleEvent(ctx context.Context, event *DomainEvent) error
       InterestedEvents() []EventType
   }
   ```

### 7.2 中短期演进计划（1-2年）

1. **差异化错误处理策略**（3-6个月）
   - **需求**：根据错误严重程度推送不同数量或难度的相似题
   - **架构变化**：扩展答案评估模块，增加错误分类能力
   - **技术依赖**：需要题库服务提供更细粒度的错误类型支持

2. **动态题量调整**（6-9个月）
   - **需求**：根据学生实时表现动态调整推题数量和难度
   - **架构变化**：增强策略引擎，实现自适应推题算法
   - **技术依赖**：需要建立学生表现评估模型

3. **离线学习支持**（9-12个月）
   - **需求**：支持网络不稳定环境下的离线答题
   - **架构变化**：增加客户端数据同步机制，实现冲突解决策略
   - **技术依赖**：需要客户端开发配合

### 7.3 长期演进方向（2年以上）

1. **智能推题引擎**
   - 基于学习数据和知识图谱，构建个性化推题模型
   - 引入机器学习技术，根据学生学习风格优化推题路径
   - 与学习分析平台深度集成，实现数据驱动的推题决策

2. **跨课程知识点关联**
   - 识别跨课程关联的知识点，提供综合性练习
   - 建立知识图谱模型，挖掘知识点依赖关系
   - 支持多维度的学习路径规划

3. **教师干预机制**
   - 提供实时课堂干预接口，允许教师调整推题策略
   - 开发教师控制台，展示班级实时答题情况
   - 支持教师针对特定学生设置个性化推题参数

## 8. 架构文档自查清单

### 8.1 架构完备性自查清单

| 检查项 | 结果 | 备注 |
|-------|------|------|
| 是否明确定义了架构目标和设计原则？ | ✅ | 1.3节中定义了高可用性、低延迟等设计目标和核心原则 |
| 是否有清晰的系统边界定义？ | ✅ | 1.4节中明确了架构设计的覆盖范围，3.3节定义了各模块边界 |
| 是否完整覆盖了PRD中定义的所有核心功能需求？ | ✅ | 架构设计完整支持PRD中定义的课中练习推题策略需求 |
| 是否有明确的分层结构和各层职责？ | ✅ | 3.2节定义了四层架构及各层职责 |
| 是否明确了主要模块划分和职责？ | ✅ | 3.3节定义了5个核心模块及其职责 |
| 是否包含关键流程的描述？ | ✅ | 3.4节描述了顺序练习和错题推相似题两个核心流程 |
| 是否定义了数据模型和存储策略？ | ✅ | 3.5节定义了核心领域对象及存储策略 |
| 是否明确了技术栈选型？ | ✅ | 第4节详细说明了技术栈选型及理由 |
| 是否考虑了安全性设计？ | ✅ | 5.1节详细说明了安全设计考量 |
| 是否考虑了系统可观测性？ | ✅ | 5.4节详细说明了可观测性设计 |
| 是否考虑了系统的可扩展性和未来演进？ | ✅ | 第7节详细说明了扩展点和未来演进路线 |
| 是否有明确的外部依赖和集成点？ | ✅ | 2.3节明确了对题库服务、用户中心和教师服务的依赖 |
| 是否考虑了异常处理和故障恢复机制？ | ✅ | 5.2节详细说明了错误处理和容错机制 |

### 8.2 需求-架构完备性自查清单

| PRD对应章节/需求点 | 架构支持点 | 结果 | 备注 |
|-------------------|-----------|------|------|
| 二.1 练习内容结构（课程-组件-题目三级结构） | 3.3.1 练习组件模块、3.5.2 核心领域对象模型 | ✅ | 通过练习组件模块实现三级结构的管理 |
| 二.1 题目类型（单题和相似题组） | 3.5.2 题目和相似题组领域模型 | ✅ | 支持两种题目类型的管理和处理 |
| 三.1 按配置顺序推送题目 | 3.3.1 策略引擎模块、3.4.1 顺序答题流程 | ✅ | 策略引擎实现了按配置顺序推送题目的核心逻辑 |
| 三.1 相似题组随机选择一题 | 3.4.1 顺序答题流程中的条件判断 | ✅ | 实现了遇到相似题组时随机选择一题的逻辑 |
| 三.1 答对继续下一题 | 3.4.1 顺序答题流程 | ✅ | 实现了学生答对后继续推送下一题的逻辑 |
| 三.1 答错有相似题则推出相似题 | 3.3.1 策略引擎模块、3.4.2 错题推送相似题流程 | ✅ | 实现了答错时检查并推送相似题的完整逻辑 |
| 三.1 相似题只推送一次 | 3.4.2 错题推送相似题流程判断逻辑、7.1 扩展点设计 | ✅ | 确保每道错题的相似题只推送一次 |
| 推题响应时间要求（<100ms） | 5.3.1 缓存策略、4.4 高性能缓存库选择 | ✅ | 通过多级缓存和性能优化确保响应时间要求 |
| 防作弊（防止非法获取答案） | 5.1.2 数据安全、5.1.3 防御恶意请求 | ✅ | 实现完善的安全措施防止用户非法获取答案 |

### 8.3 风险评估完备性自查

| 检查项 | 结果 | 备注 |
|-------|------|------|
| 是否识别了关键技术风险？ | ✅ | 6.1节识别了4个主要技术风险 |
| 是否识别了关键业务风险？ | ✅ | 6.2节识别了4个主要业务风险 |
| 是否识别了关键团队风险？ | ✅ | 6.3节识别了3个主要团队风险 |
| 是否为每个风险提供了应对策略？ | ✅ | 每个风险都提供了3项具体应对策略 |
| 风险评估是否涵盖了各个架构维度？ | ✅ | 风险评估涵盖了技术、业务和团队三个维度 |

## 9. 附录

### 9.1 参考资料

1. [课中练习推题策略需求文档](ai-generates/raw-docs/prd/PRD - 课中练习推题策略 - V1.0_analyzed.md)
2. [题库服务文档](luban-agent/ai-generates/be-service/question.md)
3. [用户中心服务文档](luban-agent/ai-generates/be-service/ucenter.md)
4. [教师服务文档](luban-agent/ai-generates/be-service/teacher.md)
5. [公共服务文档](luban-agent/ai-generates/be-service/common.md)
6. [Go项目技术栈约束](ai-rules/backend/project-rules/golang/rules-tech-stack.md)
7. [Kratos框架使用规范](ai-rules/backend/project-rules/golang/rules-kratos-code.mdc)

### 9.2 术语表

| 术语 | 说明 |
|------|------|
| 练习组件 | 课程中的练习单元，包含一组练习题目 |
| 单题 | 独立的，不与其他题目关联的练习题目 |
| 相似题组 | 由多道围绕相同知识点的题目构成的题目集合 |
| 推题策略 | 决定题目推送顺序和逻辑的规则集合 |
| 练习会话 | 学生在特定练习组件中的答题过程状态记录 |
| DDD | Domain-Driven Design，领域驱动设计，一种软件设计方法 |
| JWT | JSON Web Token，用于身份验证的令牌格式 | 
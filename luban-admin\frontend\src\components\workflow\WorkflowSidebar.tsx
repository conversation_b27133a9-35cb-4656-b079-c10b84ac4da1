'use client';

import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { CheckIcon } from '@chakra-ui/icons';

interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  path: string;
  status: 'completed' | 'current' | 'pending';
}

interface WorkflowSidebarProps {
  className?: string;
}

export default function WorkflowSidebar({ className = '' }: WorkflowSidebarProps) {
  const pathname = usePathname();

  // 定义工作流步骤
  const getWorkflowSteps = (): WorkflowStep[] => {
    const steps = [
      { id: 'format-prd', name: 'P1-生成格式化PRD', description: '原始PRD进行格式化', path: '/workflow/format-prd' },
      { id: 'ai-prd', name: 'P1-生成AI-PRD', description: '生成AI版PRD文档', path: '/workflow/ai-prd' },
      { id: 'ai-td', name: 'P2-生成AI-TD', description: '生成技术设计文档', path: '/workflow/ai-td' },
      { id: 'ai-de', name: 'P3-生成AI-DE', description: '生成数据实体文档', path: '/workflow/ai-de' },
      { id: 'ai-dd', name: 'P3-生成AI-DD', description: '生成数据库设计文档', path: '/workflow/ai-dd' },
      { id: 'ai-ad', name: 'P4-生成AI-AD', description: '生成接口设计分析文档', path: '/workflow/ai-ad' },
      { id: 'ai-api', name: 'P4-生成AI-API', description: '生成API接口配置文件', path: '/workflow/ai-api' },
      { id: 'ai-done', name: '全部完成', description: '', path: '/workflow/ai-done' }
    ];

    // 根据当前路径确定状态
    const currentIndex = steps.findIndex(step => pathname === step.path);
    
    return steps.map((step, index) => {
      // 特殊处理ai-done页面：当在ai-done页面时，所有步骤都应该显示为已完成
      if (pathname === '/workflow/ai-done') {
        return {
          ...step,
          status: step.id === 'ai-done' ? 'completed' : 'completed'
        };
      }
      
      // 其他页面的正常逻辑
      return {
        ...step,
        status: index < currentIndex ? 'completed' :
                index === currentIndex ? 'current' : 'pending'
      };
    });
  };

  const workflowSteps = getWorkflowSteps();

  const getStepIcon = (step: WorkflowStep) => {
    switch (step.status) {
      case 'completed':
        return (
          <div className="w-4 h-4 bg-green-400 rounded-full flex items-center justify-center shadow-lg">
            <svg className="w-2 h-2 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
            </svg>
          </div>
        );
      case 'current':
        return (
          <div className="w-4 h-4 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center shadow-xl">
            <div className="w-1 h-1 bg-white rounded-full"></div>
          </div>
        );
      case 'pending':
        return (
          <div className="w-4 h-4 border-2 border-gray-400 rounded-full bg-white"></div>
        );
      default:
        return null;
    }
  };

  const getStatusLabel = (step: WorkflowStep) => {
    switch (step.status) {
      case 'completed':
        return (
          <span className="px-1.5 py-0.5 bg-green-100 border border-green-300 text-green-700 text-xs rounded-full">
            ✓ 已完成
          </span>
        );
      case 'current':
        return (
          <span className="px-1.5 py-0.5 bg-gradient-to-r from-green-500 to-emerald-500 text-white text-xs font-bold rounded-full shadow-lg">
            进行中
          </span>
        );
      case 'pending':
        return (
          <span className="px-1.5 py-0.5 bg-gray-100 border border-gray-300 text-gray-500 text-xs rounded-full">
            待处理
          </span>
        );
      default:
        return null;
    }
  };

  const getCardClasses = (step: WorkflowStep) => {
    const baseClasses = "relative p-2 rounded-lg transition-all duration-500 mb-2";
    
    switch (step.status) {
      case 'completed':
        return `${baseClasses} bg-green-50 hover:bg-green-100 cursor-pointer`;
      case 'current':
        return `${baseClasses} bg-gradient-to-r from-green-500/20 to-emerald-500/20 border-l-4 border-green-500`;
      case 'pending':
        return `${baseClasses}`;
      default:
        return baseClasses;
    }
  };

  const renderStep = (step: WorkflowStep, index: number) => {
    const content = (
      <div className={getCardClasses(step)}>
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1">
            {getStepIcon(step)}
            <div className="ml-2 flex-1">
              <h3 className={`font-bold text-sm ${
                step.status === 'current' ? 'text-green-800' :
                step.status === 'completed' ? 'text-green-800' : 'text-gray-500'
              }`}>
                {step.name}
              </h3>
              <p className={`text-xs mt-0.5 ${
                step.status === 'current' ? 'text-green-700' :
                step.status === 'completed' ? 'text-green-600' : 'text-gray-400'
              }`}>
                {step.description}
              </p>
            </div>
          </div>
          <div className="ml-1 flex-shrink-0">
            {getStatusLabel(step)}
          </div>
        </div>
      </div>
    );

    // 只有已完成的步骤可以点击跳转
    if (step.status === 'completed') {
      return (
        <Link key={step.id} href={step.path}>
          {content}
        </Link>
      );
    }

    return (
      <div key={step.id}>
        {content}
      </div>
    );
  };

  return (
    <div className={`bg-white h-full ${className}`}>
      <div className="p-4">
        <div className="mb-6">
          <h4 className="text-lg font-normal text-gray-800 mb-2">完成进度</h4>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-green-500 h-2 rounded-full transition-all duration-300"
              style={{
                width: `${pathname === '/workflow/ai-done' ? 100 : ((workflowSteps.filter(s => s.status === 'completed').length + workflowSteps.filter(s => s.status === 'current').length) / workflowSteps.length) * 100}%`
              }}
            ></div>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            {pathname === '/workflow/ai-done' ? workflowSteps.length : (workflowSteps.filter(s => s.status === 'completed').length + workflowSteps.filter(s => s.status === 'current').length)} / {workflowSteps.length} 已完成
          </p>
        </div>

        <div className="space-y-0">
          {workflowSteps.map((step, index) => renderStep(step, index))}
        </div>
      </div>
    </div>
  );
}
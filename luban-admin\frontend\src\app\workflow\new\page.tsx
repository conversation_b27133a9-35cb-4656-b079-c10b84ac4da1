// @ts-nocheck
// @ts-nocheck
'use client';

import { useState, FormEvent, useEffect, useRef, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import { documentApi } from '@/lib/api';
import MarkdownPreviewModal from '@/components/markdown/MarkdownPreviewModal';
import axios from 'axios';
import { Terminal } from 'lucide-react';
import {
  Button,
  Input,
  Progress,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Box,
  useToast
} from '@chakra-ui/react';
import { CopyIcon } from '@chakra-ui/icons';
import { API_BASE_URL } from '@/app/configs/api';

export default function NewWorkflowPage() {
  const [activeTab, setActiveTab] = useState('feishu');
  const [isLoading, setIsLoading] = useState(false);
  const [feishuUrl, setFeishuUrl] = useState('');
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState('');
  const [taskId, setTaskId] = useState('');
  const [logs, setLogs] = useState<string[]>([]);
  const [showLogs, setShowLogs] = useState(false);
  const [progress, setProgress] = useState(0);
  const [downloadUrl, setDownloadUrl] = useState('');
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const logEndRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const wsRef = useRef<WebSocket | null>(null);
  const toast = useToast();
  const [feishuUrlError, setFeishuUrlError] = useState('');
  
  // 自动滚动到最新日志
  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);
  
  // WebSocket连接处理
  useEffect(() => {
    // 清理WebSocket连接
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
    };
  }, []);
  
  // 监听taskId变化，创建WebSocket连接
  useEffect(() => {
    if (taskId) {
      // 关闭已有的连接
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
      
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      let wsHost;
      try {
        const apiUrl = new URL(API_BASE_URL);
        wsHost = apiUrl.host;
      } catch (e) {
        console.error("无效的API_BASE_URL: ", API_BASE_URL);
        wsHost = 'localhost:8000'; // 回退到默认值
      }
      const wsUrl = `${protocol}//${wsHost}/api/ws/${taskId}`;
      
      console.log('正在连接WebSocket...', wsUrl);
      
      const socket = new WebSocket(wsUrl);
      wsRef.current = socket;
      
      socket.onopen = (event) => {
        console.log('WebSocket连接已建立', event);
        addLog('WebSocket连接已建立，开始接收实时日志...');
        try {
          socket.send(JSON.stringify({
            type: 'hello',
            taskId: taskId,
            timestamp: Date.now()
          }));
        } catch (e) {
          console.error('发送测试消息失败', e);
        }
      };
      
      socket.onmessage = (event) => {
        console.log('收到WebSocket消息:', event.data);
        addLog(event.data);
        updateProgressFromLog(event.data);
      };
      
      socket.onclose = (event) => {
        console.log('WebSocket连接已关闭', event);
        addLog(`WebSocket连接已关闭. ${event.wasClean ? '正常关闭' : '异常关闭'} 代码: ${event.code}`);
      };
      
      socket.onerror = (error) => {
        console.error('WebSocket错误:', error);
        addLog(`WebSocket错误: ${error}`);
      };
      
      const pingInterval = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          try {
            socket.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
            console.log('发送心跳包');
          } catch (e) {
            console.error('发送心跳包失败', e);
          }
        }
      }, 30000);
      
      const checkStatusAndOutput = async () => {
        try {
          console.log('正在获取任务状态:', taskId);
          const taskStatus = await fetch(`${API_BASE_URL}/feishu/tasks/${taskId}`)
            .then(response => {
              if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
              }
              return response.json();
            });
          
          console.log('任务状态:', taskStatus);
          
          // 优先使用API下载URL (如果存在)
          if (taskStatus.api_download_url) {
            console.log('发现API下载链接:', taskStatus.api_download_url);
            const apiDownloadUrl = `${API_BASE_URL.replace(/\/api$/, '')}${taskStatus.api_download_url}`;
            setDownloadUrl(apiDownloadUrl);
            console.log('构建的API下载链接:', apiDownloadUrl);
          }
          // 否则使用常规下载URL
          else if (taskStatus.download_url) {
            console.log('发现下载链接:', taskStatus.download_url);
            const isDirectDownloadLink = taskStatus.download_url.startsWith('/download');
            
            // 为直接下载链接使用固定的后端地址
            const backendHost = API_BASE_URL.replace(/\/api$/, '');
            
            const fullDownloadUrl = taskStatus.download_url.startsWith('http') 
              ? taskStatus.download_url 
              : isDirectDownloadLink
                ? `${backendHost}${taskStatus.download_url}`
                : `${API_BASE_URL}${taskStatus.download_url.startsWith('/') ? '' : '/'}${taskStatus.download_url}`;
            
            setDownloadUrl(fullDownloadUrl);
            console.log('构建的完整下载链接:', fullDownloadUrl);
          }
          
          if (taskStatus.status === 'completed' || taskStatus.status === 'failed') {
            if (socket.readyState === WebSocket.OPEN) {
              socket.close();
            }
            if (taskStatus.status === 'completed') {
              addLog(`处理完成！下载链接: ${taskStatus.download_url || '无'}`);
              setIsLoading(false);
              // For new workflow page, we might want to redirect to the specific task page after completion.
              // setTimeout(() => {
              //   router.push(`/workflow/${taskId}`); 
              // }, 3000);
            } else {
              addLog(`处理失败：${taskStatus.message}`);
              setError(taskStatus.message || '处理失败');
              setIsLoading(false);
            }
            return false;
          }
          
          addLog(`当前状态: ${taskStatus.status} - ${taskStatus.message}`);
          return true;
        } catch (error) {
          console.error('获取任务状态失败:', error);
          addLog(`获取任务状态失败: ${error.message || '未知错误'}`);
          setError(`获取任务状态失败: ${error.message || '未知错误'}`);
          setIsLoading(false);
          return false;
        }
      };
      
      checkStatusAndOutput();
      const checkInterval = setInterval(async () => {
        const shouldContinue = await checkStatusAndOutput();
        if (!shouldContinue) {
          clearInterval(checkInterval);
          clearInterval(pingInterval);
        }
      }, 3000);
      
      return () => {
        clearInterval(checkInterval);
        clearInterval(pingInterval);
        if (socket.readyState === WebSocket.OPEN) {
          socket.close();
        }
        wsRef.current = null;
      };
    }
  }, [taskId, router]);
  
  const addLog = (message: string) => {
    setLogs((prevLogs) => [...prevLogs, message]);
  };
  
  const updateProgressFromLog = (logText: string) => {
    // 如果日志中包含"处理完成"，设置进度为100%
    if (logText.includes('处理完成') || logText.includes('生成文件')) {
      setProgress(100);
      return;
    }
    
    // 如果进度为0且正在处理中，设置为50%
    if (progress === 0 && isLoading && (logText.includes('开始处理') || logText.includes('正在处理'))) {
      setProgress(50);
      return;
    }
    
    // 尝试从日志文本中解析进度信息
    const progressMatch = /进度: \[([\d]+)\/([\d]+)\]/.exec(logText);
    if (progressMatch) {
      const current = parseInt(progressMatch[1]);
      const total = parseInt(progressMatch[2]);
      const progressPercent = Math.round((current / total) * 100);
      setProgress(progressPercent);
    }
  };
  
  // 验证飞书URL格式
  const validateFeishuUrl = (url: string): boolean => {
    if (!url) return false;
    
    try {
      // 检查是否包含.feishu.cn或larksuite.com域名
      const isFeishuDomain = url.includes('.feishu.cn') || url.includes('.larksuite.com');
      
      // 检查是否包含/wiki/路径（允许）
      const hasWikiPath = url.includes('/wiki/');
      
      // 检查是否包含/docx/路径（不允许）
      const hasDocxPath = url.includes('/docx/');
      
      if (!isFeishuDomain) {
        setFeishuUrlError('请输入有效的飞书文档链接，必须包含.feishu.cn或.larksuite.com域名');
        return false;
      }
      
      if (hasDocxPath) {
        setFeishuUrlError('不支持飞书文档（/docx/），请使用飞书知识库文档（/wiki/）');
        return false;
      }
      
      if (!hasWikiPath) {
        setFeishuUrlError('请使用飞书知识库文档链接，URL应包含/wiki/路径');
        return false;
      }
      
      setFeishuUrlError('');
      return true;
    } catch (error) {
      console.error('飞书URL验证错误:', error);
      setFeishuUrlError('URL格式无效，请检查');
      return false;
    }
  };
  
  const handleFormSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');
    setLogs([]);
    setShowLogs(false);
    setDownloadUrl('');
    setProgress(0);
    
    try {
      let result;
      if (activeTab === 'feishu') {
        if (!feishuUrl) {
          throw new Error('请输入飞书文档链接');
        }
        
        // 添加飞书URL验证
        if (!validateFeishuUrl(feishuUrl)) {
          setIsLoading(false);
          throw new Error(feishuUrlError || '飞书文档链接格式不正确');
        }
        
        result = await documentApi.processFeishuDocumentV2(feishuUrl, {
          replace_images: true
        });
        setTaskId(result.task_id);
        setShowLogs(true);
        addLog(`开始处理飞书文档: ${feishuUrl}`);
        addLog(`任务ID: ${result.task_id}`);
        // 设置初始进度为50%
        setProgress(50);
      } else if (activeTab === 'upload') {
        if (!selectedFile) {
          throw new Error('请选择要上传的文件');
        }
        const formData = new FormData();
        formData.append('file', selectedFile);
        const uploadResult = await axios.post(`${API_BASE_URL}/documents/upload`, formData, {
          headers: { 'Content-Type': 'multipart/form-data' }
        });
        const fileId = uploadResult.data.file_id;
        result = await axios.post(`${API_BASE_URL}/documents/process/pdf/${fileId}`, {
          replace_images: true,
          output_format: 'markdown',
          name: name || `${selectedFile.name}-处理`,
          description: description || '由PDF文档自动生成'
        }, { headers: { 'Content-Type': 'application/json' } });
        addLog(`文件上传和处理任务已提交，任务ID: ${result.data.task_id}`);
        setTaskId(result.data.task_id);
        setShowLogs(true);
        // 设置初始进度为50%
        setProgress(50);
      }
    } catch (error) {
      console.error('提交失败:', error);
      const errorMessage = error.response?.data?.detail || error.message || '提交失败，请重试';
      setError(errorMessage);
      setIsLoading(false);
      setShowLogs(false);
    }
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };
  
  const handleCopyLogs = () => {
    const logText = logs.join('\n');
    navigator.clipboard.writeText(logText)
      .then(() => {
        console.log('Logs copied to clipboard');
        toast({
          title: "日志已复制",
          description: "日志内容已成功复制到剪贴板。",
          status: "success",
          duration: 3000,
          isClosable: true,
        });
      })
      .catch(err => {
        console.error('Failed to copy logs: ', err);
        toast({
          title: "复制失败",
          description: "无法将日志复制到剪贴板。",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      });
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8 text-center">创建新的AI-PRD</h1>
        <div className="max-w-3xl mx-auto">
          <div className="flex border-b mb-6">
            <button
              type="button"
              className={`px-4 py-2 font-medium ${activeTab === 'feishu' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
              onClick={() => setActiveTab('feishu')}
            >
              飞书文档链接
            </button>
            <button
              type="button"
              className={`px-4 py-2 font-medium ${activeTab === 'upload' ? 'border-b-2 border-blue-500 text-blue-600' : 'text-gray-500 hover:text-blue-600'}`}
              onClick={() => setActiveTab('upload')}
            >
              上传PRD文档
            </button>
          </div>
          <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-1">新建AI-PRD文档处理</h2>
              <p className="text-gray-600 text-sm">通过PRD文档自动生成开发文档、API文档等</p>
            </div>
            {error && (
              <Alert status="error" mb={4} borderRadius="md" variant="subtle">
                <AlertIcon />
                <Box flex="1">
                  <AlertTitle fontWeight="semibold">发生错误:</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Box>
              </Alert>
            )}
            <form onSubmit={handleFormSubmit}>
              {activeTab === 'feishu' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="feishu-url">
                    *<span className="font-bold">飞书文档链接</span> <span className="text-red-400 font-normal">(必填)</span>
                  </label>
                  <Input
                    id="feishu-url"
                    name="url"
                    type="text"
                    value={feishuUrl}
                    onChange={(e) => {
                      setFeishuUrl(e.target.value);
                      if (feishuUrlError) validateFeishuUrl(e.target.value);
                    }}
                    placeholder="粘贴飞书文档分享链接"
                    required
                    isDisabled={isLoading}
                    width="100%"
                    px={3}
                    py={2}
                    borderColor={feishuUrlError ? "red.300" : "gray.300"}
                    borderRadius="md"
                    boxShadow="sm"
                    _focus={{ outline: "none", ring: "2px", ringColor: feishuUrlError ? "red.500" : "blue.500", borderColor: feishuUrlError ? "red.500" : "blue.500" }}
                  />
                  {feishuUrlError ? (
                    <p className="text-xs text-red-500 mt-1">{feishuUrlError}</p>
                  ) : (
                    <p className="text-xs text-gray-500 mt-1">
                      请确保文档已设置为可访问状态。仅支持飞书知识库文档(格式: https://组织或个人标识.feishu.cn/<span className="text-red-400 font-normal">wiki/</span>文档token)
                    </p>
                  )}
                </div>
              )}
              {activeTab === 'upload' && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="file">
                    上传PRD文档
                  </label>
                  <div className="border border-gray-300 rounded-md p-3">
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                      <div className="mt-4 flex text-sm justify-center leading-6 text-gray-600">
                        <label
                          htmlFor="file-upload"
                          className={`relative cursor-pointer rounded-md bg-white font-semibold text-blue-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2 hover:text-blue-500 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          <span>上传文件</span>
                          <input
                            id="file-upload"
                            name="file"
                            type="file"
                            accept=".pdf,.md,.docx"
                            className="sr-only"
                            onChange={handleFileChange}
                            required={activeTab === 'upload'}
                            disabled={isLoading}
                          />
                        </label>
                        <p className="pl-1">或拖拽到此处</p>
                      </div>
                      <p className="text-xs leading-5 text-gray-600 mt-2">支持 PDF, Markdown, DOCX (最大20MB)</p>
                      {selectedFile && (
                        <div className="mt-4 text-sm text-gray-700">
                          已选择: {selectedFile.name}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="name">
                  工作流名称 <span className="text-gray-400 font-normal">(选填)</span>
                </label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="例如：用户管理模块PRD分析"
                  isDisabled={isLoading}
                  width="100%"
                  px={3}
                  py={2}
                  borderColor="gray.300"
                  borderRadius="md"
                  boxShadow="sm"
                  _focus={{ outline: "none", ring: "2px", ringColor: "blue.500", borderColor: "blue.500" }}
                />
              </div>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="description">
                  描述 <span className="text-gray-400 font-normal">(选填)</span>
                </label>
                <Input
                  id="description"
                  name="description"
                  type="text"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="简要描述此PRD的内容和目标"
                  isDisabled={isLoading}
                  width="100%"
                  px={3}
                  py={2}
                  borderColor="gray.300"
                  borderRadius="md"
                  boxShadow="sm"
                  _focus={{ outline: "none", ring: "2px", ringColor: "blue.500", borderColor: "blue.500" }}
                />
              </div>
              {showLogs && (
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-lg font-semibold">处理日志 ({progress.toFixed(0)}%)</h3>
                    <Button 
                      leftIcon={<CopyIcon />}
                      variant="outline"
                      size="sm"
                      onClick={handleCopyLogs}
                      isDisabled={logs.length === 0}
                      borderColor="gray.300"
                      _hover={{ bg: "gray.100" }}
                    >
                      复制日志
                    </Button>
                  </div>
                  <Progress 
                    value={progress} 
                    width="100%" 
                    mb={2} 
                    size="sm"
                    colorScheme="blue"
                    isAnimated={progress < 100 && progress > 0}
                    hasStripe={progress < 100 && progress > 0}
                  />
                  <p className="text-xs text-gray-500 mb-2">如果文档图片较多，处理时间{'>'}10分钟，请耐心等待。</p>
                  <div className="h-60 bg-gray-100 border border-gray-300 rounded-md p-3 overflow-y-auto font-mono text-sm">
                    {logs.map((log, index) => (
                      <div key={index} className="py-1 whitespace-pre-wrap break-all">
                        {log}
                      </div>
                    ))}
                    <div ref={logEndRef} />
                  </div>
                  {downloadUrl && (
                    <div className="mt-2 flex space-x-4">
                      <a
                        href={downloadUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 hover:underline flex items-center"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                        </svg>
                        下载处理结果
                      </a>
                      <button
                        onClick={() => setIsPreviewOpen(true)}
                        className="text-green-600 hover:text-green-800 hover:underline flex items-center"
                      >
                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        预览文档
                      </button>
                    </div>
                  )}
                </div>
              )}
              <div className="flex justify-between items-center mt-8">
                <button
                    type="button"
                    onClick={() => {
                        setActiveTab('feishu');
                        setIsLoading(false);
                        setFeishuUrl('');
                        setName('');
                        setDescription('');
                        setSelectedFile(null);
                        setError('');
                        setTaskId('');
                        setLogs([]);
                        setShowLogs(false);
                        setProgress(0);
                        setDownloadUrl('');
                        if (wsRef.current) {
                            wsRef.current.close();
                            wsRef.current = null;
                        }
                    }}
                    className={`px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={isLoading}
                >
                  取消/重置
                </button>
                <button
                  type="submit"
                  disabled={isLoading || (activeTab === 'feishu' && !feishuUrl) || (activeTab === 'upload' && !selectedFile)}
                  className={`px-6 py-2 rounded-md text-white transition-colors duration-150 ${isLoading || (activeTab === 'feishu' && !feishuUrl) || (activeTab === 'upload' && !selectedFile) ? 'bg-blue-300 cursor-not-allowed' : 'bg-blue-600 hover:bg-blue-700 focus:ring-4 focus:ring-blue-300'}`}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      处理中...
                    </div>
                  ) : '开始处理'}
                </button>
              </div>
            </form>
          </div>
          
          {/* Markdown 预览模态框 */}
          <MarkdownPreviewModal
            isOpen={isPreviewOpen}
            onClose={() => setIsPreviewOpen(false)}
            downloadUrl={downloadUrl}
            title="Markdown 文档预览"
          />
        </div>
      </div>
    </MainLayout>
  );
} 
# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** be-s1-2-ai-prd-学生端-巩固练习-claude-sonnet-4-v1.md
- **PRD版本号：** V1.0
- **提取日期：** 2025-05-30

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 巩固练习会话 | 学生进行巩固练习的完整会话记录，包含练习状态、进度等信息 | 2.1, 4.1, 4.2 |
| 练习题目作答记录 | 学生在巩固练习中对每道题目的作答情况和结果 | 2.1, 3.1, 4.1 |
| 学习报告 | 总结学生每次完整练习的学习报告，展示掌握度变化和学习成果 | 2.1, 4.3 |
| 再练一次会话 | 根据学生掌握度情况推荐的再练一次环节记录 | 2.1, 3.2, 4.4 |
| 错题记录 | 自动添加错题和手动添加的错题本记录 | 2.2, 11.2 |
| 勾画记录 | 用户在作答环节时对题目进行勾画和标记的记录 | 2.2 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`巩固练习会话`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 会话ID | 巩固练习会话的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 4.1, 4.2 |
| 学生ID | 参与练习的学生标识符 | 整数 | 非空, 外键关联用户中心 |  | 外键 | 4.1 |
| 课程ID | 练习对应的课程标识符 | 整数 | 非空, 外键关联内容平台 |  | 外键 | 4.1 |
| 练习状态 | 练习当前状态：未开始、练习中、已完成 | 枚举/整数 | 取值范围：0未开始、1练习中、2已完成 | 0 |  | 2.1, 4.2 |
| 当前题目序号 | 学生当前作答到的题目序号 | 整数 | 大于等于0 | 0 |  | 4.2 |
| 开始时间 | 练习开始时间戳 | 时间戳 | 非空 |  |  | 4.1 |
| 结束时间 | 练习结束时间戳 | 时间戳 | 可为空，完成时填入 |  |  | 4.3 |
| 总题目数量 | 本次练习的预估题目数量 | 整数 | 大于0 |  |  | 2.1, 4.1 |

**实体：`练习题目作答记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 作答记录ID | 作答记录的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 4.1 |
| 会话ID | 所属练习会话标识符 | 字符串/整数 | 非空, 外键 |  | 外键 | 4.1 |
| 题目ID | 题目标识符，关联内容平台 | 整数 | 非空, 外键关联内容平台 |  | 外键 | 4.1 |
| 题目序号 | 题目在本次练习中的序号 | 整数 | 大于0 |  |  | 4.1 |
| 学生答案 | 学生提交的答案内容 | 文本 | 可为空（未作答） |  |  | 4.1 |
| 作答结果 | 作答正确性：正确、错误、部分正确 | 枚举/整数 | 取值范围：1正确、2错误、3部分正确 |  |  | 2.1, 4.1 |
| 作答时长 | 学生作答该题的时间（秒） | 整数 | 大于等于0 |  |  | 3.1 |
| 题目难度系数 | 题目的难度等级，用于掌握度计算 | 数值型 | 大于0 |  |  | 3.1 |
| 是否连续正确 | 标记是否为连续正确作答 | 布尔型 | true/false | false |  | 2.1, 4.1 |
| 作答时间戳 | 提交答案的时间戳 | 时间戳 | 非空 |  |  | 4.1 |

**实体：`学习报告`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 报告ID | 学习报告的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 4.3 |
| 会话ID | 关联的练习会话标识符 | 字符串/整数 | 非空, 外键 |  | 外键 | 4.3 |
| 学生ID | 学生标识符 | 整数 | 非空, 外键关联用户中心 |  | 外键 | 4.3 |
| 掌握度变化 | 本次练习前后的掌握度变化值 | 数值型 | 可为负数 |  |  | 3.1, 4.3 |
| 学习时长 | 本次练习的总学习时长（分钟） | 整数 | 大于0 |  |  | 4.3 |
| 答题数量 | 本次练习完成的题目数量 | 整数 | 大于0 |  |  | 4.3 |
| 正确率 | 本次练习的正确率百分比 | 数值型 | 0-100 |  |  | 3.1, 4.3 |
| IP角色反馈 | 基于正确率展示的角色和文案 | 文本 | 非空 |  |  | 4.3 |
| 是否推荐再练 | 是否推荐学生再练一次 | 布尔型 | true/false |  |  | 3.2, 4.3 |
| 生成时间 | 报告生成时间戳 | 时间戳 | 非空 |  |  | 4.3 |

**实体：`再练一次会话`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 再练会话ID | 再练一次会话的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 4.4 |
| 原会话ID | 关联的原始练习会话标识符 | 字符串/整数 | 非空, 外键 |  | 外键 | 4.4 |
| 学生ID | 学生标识符 | 整数 | 非空, 外键关联用户中心 |  | 外键 | 4.4 |
| 薄弱知识点数量 | 未达到掌握标准的知识点个数 | 整数 | 大于等于0 |  |  | 3.2 |
| 开始时间 | 再练开始时间戳 | 时间戳 | 非空 |  |  | 4.4 |
| 结束时间 | 再练结束时间戳 | 时间戳 | 可为空，完成时填入 |  |  | 4.4 |
| 练习状态 | 再练状态：进行中、已完成 | 枚举/整数 | 取值范围：1进行中、2已完成 | 1 |  | 4.4 |

**实体：`错题记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 错题记录ID | 错题记录的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 2.2 |
| 学生ID | 学生标识符 | 整数 | 非空, 外键关联用户中心 |  | 外键 | 2.2 |
| 题目ID | 题目标识符，关联内容平台 | 整数 | 非空, 外键关联内容平台 |  | 外键 | 2.2 |
| 作答记录ID | 关联的作答记录标识符 | 字符串/整数 | 可为空（手动添加时） |  | 外键 | 2.2 |
| 添加方式 | 错题添加方式：自动添加、手动添加 | 枚举/整数 | 取值范围：1自动添加、2手动添加 |  |  | 2.2 |
| 错因标签 | 错题的错因分类标签 | 文本 | 可为空 |  |  | 2.2 |
| 添加时间 | 错题添加时间戳 | 时间戳 | 非空 |  |  | 2.2 |

**实体：`勾画记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 勾画记录ID | 勾画记录的唯一标识符 | 字符串/整数 | 唯一, 非空 |  | 主键 | 2.2 |
| 作答记录ID | 关联的作答记录标识符 | 字符串/整数 | 非空, 外键 |  | 外键 | 2.2 |
| 勾画工具类型 | 使用的勾画工具类型 | 枚举/整数 | 多种勾画工具类型 |  |  | 2.2 |
| 勾画颜色 | 勾画使用的颜色 | 字符串 | 颜色代码格式 |  |  | 2.2 |
| 勾画坐标数据 | 勾画的具体坐标和路径数据 | JSON/文本 | 非空 |  |  | 2.2 |
| 创建时间 | 勾画创建时间戳 | 时间戳 | 非空 |  |  | 2.2 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 (例如：用户 '拥有一个' 学校档案) | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 学生进行多次巩固练习会话 | 学生（用户中心） | 巩固练习会话 | 1:N | 巩固练习会话.学生ID -> 学生.ID | 4.1, 4.2 |
| 巩固练习会话包含多个题目作答记录 | 巩固练习会话 | 练习题目作答记录 | 1:N | 练习题目作答记录.会话ID -> 巩固练习会话.会话ID | 4.1 |
| 巩固练习会话生成一个学习报告 | 巩固练习会话 | 学习报告 | 1:1 | 学习报告.会话ID -> 巩固练习会话.会话ID | 4.3 |
| 巩固练习会话可能触发再练一次会话 | 巩固练习会话 | 再练一次会话 | 1:1 | 再练一次会话.原会话ID -> 巩固练习会话.会话ID | 3.2, 4.4 |
| 题目作答记录关联题目信息 | 练习题目作答记录 | 题目（内容平台） | N:1 | 练习题目作答记录.题目ID -> 题目.ID | 4.1 |
| 错误作答记录自动生成错题记录 | 练习题目作答记录 | 错题记录 | 1:1 | 错题记录.作答记录ID -> 练习题目作答记录.作答记录ID | 2.2 |
| 作答记录可包含多个勾画记录 | 练习题目作答记录 | 勾画记录 | 1:N | 勾画记录.作答记录ID -> 练习题目作答记录.作答记录ID | 2.2 |
| 课程关联巩固练习会话 | 课程（内容平台） | 巩固练习会话 | 1:N | 巩固练习会话.课程ID -> 课程.ID | 4.1 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 掌握度变化计算 | 正确率（本次练习正确题目数/总题目数）、作答时长（每题作答时间）、难度系数（题目难度等级） | 掌握度变化值 | 3.1 |
| 再练一次触发判断 | 最终掌握度（练习完成后的掌握度水平）、正确率阈值、薄弱知识点数量（未达到掌握标准的知识点个数） | 是否推荐再练 | 3.2 |
| 正确率计算 | 本次练习正确题目数、总题目数 | 正确率百分比 | 3.1, 4.3 |
| 学习时长统计 | 练习开始时间、结束时间 | 学习时长（分钟） | 4.3 |
| 连续正确判断 | 连续作答记录的作答结果 | 是否连续正确标记 | 2.1, 4.1 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 巩固练习会话.练习状态 | 学生点击进入练习、中途退出、完成所有题目 | 实时更新 | 2.1, 4.1, 4.2 |
| 巩固练习会话.当前题目序号 | 学生完成当前题目进入下一题 | 每题作答后 | 4.2 |
| 练习题目作答记录 | 学生提交每道题目的答案 | 每题作答后 | 4.1 |
| 掌握度变化 | 学生每次完成答题之后 | 实时/每次答题 | 3.1 |
| 学习报告 | 学生完成所有题目后 | 练习完成时 | 4.3 |
| 错题记录 | 学生作答错误时自动添加，或学生手动添加 | 错误作答后/手动操作时 | 2.2 |
| 再练一次会话 | 学生掌握度未达标时推荐，学生选择再练一次 | 根据掌握度判断结果 | 3.2, 4.4 |
| 勾画记录 | 学生在作答环节进行勾画操作 | 实时保存 | 2.2 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 学生基本信息 | 用户中心提供，包括学生ID、姓名、班级等 | 4.1 |
| 题目信息 | 内容平台提供，包括题目内容、难度系数、答案、解析等 | 4.1, 2.2 |
| 课程信息 | 内容平台提供，包括课程名称、知识点等 | 4.1 |
| 学生答案 | 学生在练习过程中输入提交 | 4.1 |
| 作答时长 | 系统自动计算记录，基于题目展示和提交时间差 | 3.1 |
| 勾画数据 | 学生在作答界面进行勾画操作时实时采集 | 2.2 |
| IP角色反馈文案 | 系统配置或运营后台配置，基于正确率区间匹配 | 4.3 |

---

## 7. 当前版本明确排除的数据需求 (例如PRD中的V1.0版本)

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 (源自PRD"非本期功能"等章节) | PRD中说明的排除原因 (若有) | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 积分系统的详细规则和兑换机制 | 非本期功能 | 2.3 |
| 社交功能（如排行榜、好友对战等） | 非本期功能 | 2.3 |
| 高级数据分析和个性化学习路径推荐 | 非本期功能 | 2.3 |
| 熔断机制的具体规则参数 | 具体规则需策略文档补充 | 10 |
| 不认真作答检测的具体算法 | 检测规则需策略文档补充 | 10 |
| 掌握度计算的具体算法实现 | 具体算法需策略文档补充 | 10 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别巩固练习会话、作答记录、学习报告、再练一次会话、错题记录、勾画记录等核心实体 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏（例如，本示例中的"题目信息"、"课程信息"）？                                                         | ✅ | 题目信息、课程信息、学生信息已通过公共服务引用，不重复设计 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已提取练习状态、作答结果、掌握度变化、正确率等关键属性 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | 属性描述与PRD原文保持一致 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确会话与作答记录、学习报告、再练一次等关系 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 已体现掌握度计算依赖、错题记录依赖等关系 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 已列出掌握度计算、再练一次触发判断等计算的输入项 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 已记录练习状态变化、作答记录更新等触发条件 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 已列出积分系统、社交功能等排除项 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 使用PRD中的术语如"巩固练习"、"再练一次"等 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 每个提取项都标注了对应的PRD章节号 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 所有信息均基于PRD原文提取 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 熔断机制、不认真作答检测、掌握度计算的具体规则需策略文档补充 |

--- 
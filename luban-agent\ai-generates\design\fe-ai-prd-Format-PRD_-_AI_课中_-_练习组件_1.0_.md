# 前端产品需求文档 - AI 课中 - 练习组件 1.0
文档说明：
  - 标注⚠️的是 AI 基于用户角度补充的点，可能并未在原始 PRD 中体现

## 1. 需求背景与目标

### 1.1 需求目标
通过该需求的实现，可以优化 AI 课中整体学习体验，提升学生作答流畅度和情感反馈体验，从而提升课程满意度与学习效果。

### 1.2 用户价值
- 对于 **使用新 AI 课版本的全量学生用户**，解决了 **练习过程中转场生硬、缺乏即时反馈的问题**，带来了 **更流畅、更具激励性的作答体验**。
- 对于 **使用新 AI 课版本的全量学生用户**，满足了 **在练习中获得及时鼓励和引导的核心诉求**，实现了 **增强学习参与感和提升学习效果的用户收益**。

## 2. 功能范围

### 2.1 核心功能
#### **[核心功能点1]:** 练习组件转场交互优化
简洁描述该功能是什么：优化学生在进入、进行中和离开练习组件时的视觉和交互过渡效果，提供更流畅和友好的体验。

##### **[核心功能点1.1]:** 进入/离开练习的转场效果
###### 界面布局与核心UI元素
- **布局参考：**
    - [进入练习提示及IP动效](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XwKpbrJtaoPYEIxbkDbccUBKnYd.png) - 仅关注布局
    - [翻页动效](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_L4WcbI0zAoClQpx9hBmcJNjXnkd.png) - 仅关注布局
- **布局原则：**
    - **进入练习提示：** 根据图片解析 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:230`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:230) ，“即将进入练习”提示位于左侧内容展示区的底部，以按钮样式呈现。
    - **进入练习转场（IP动效+文案）：** 根据图片解析 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:301`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:301) ，IP形象和相关文案（如“开始练习”、“继续练习”）应居中或显著位置展示，作为转场的核心视觉元素。
    - **翻页动效：** 模拟物理翻页的视觉效果，应覆盖整个练习区域或屏幕，作为进入和离开练习时的主要过渡动画。
- **核心UI元素清单：**
    - “即将进入练习”提示条/按钮
    - IP形象动画区域
    - 转场文案区域（例如：“开始练习 + 课程名称”，“继续练习 + 课程名称”）
    - 翻页动态效果

###### 功能需求与交互原则
- **功能描述：**
    - **进入练习提示：**
        - 在练习组件最顶部，增加“即将进入练习” tip，预告即将进入练习环节。
        - **判断条件：** 当练习组件前有其他组件时，展示该提示。
    - **进入练习转场：**
        - **首次进入练习（所有题目未作答时）：**
            - **时机：** 用户完成课程（前序组件）后，进入练习组件时。
            - **转场动效：** 翻页。
            - **IP元素 + 文案：** IP动效 + 文案“开始练习 + [课程名称]”。
            - **展示时间：** 2秒。
        - **再次进入练习：**
            - **时机：** 用户退出课程后再次进入课程时，且进度在练习组件时。
            - **转场动效：** 无翻页，直接展示IP动效。
            - **IP元素 + 文案：** IP动效 + 文案“继续练习 + [课程名称]”。
            - **展示时间：** 2秒。
    - **离开练习转场：**
        - **时机：** 用户完成练习后（点击最后一题的“继续”按钮）。
        - **转场动效：** 翻页。
- **交互模式：**
    - “即将进入练习”提示为静态展示，告知用户即将发生的状态变化。
    - 进入练习的转场（首次/再次）为自动播放的动效和信息展示，用户被动接收。
    - 离开练习的转场由用户在最后一题点击“继续”按钮触发，然后自动播放翻页动效。
- **反馈原则：**
    - 转场动效本身即为一种视觉反馈，表明状态的切换。
    - IP形象和激励性文案提供情感化反馈。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点1.2]:** 题目间转场与即时反馈
###### 界面布局与核心UI元素
- **布局参考：**
    - [题目间转场反馈-答对](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GFWobx4zHo13YlxhwhlccN3GnRe.png) - 仅关注布局
    - [题目间转场反馈-答错](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BosmbvrKeo8580x9RqycMEZ7nYb.png) - 仅关注布局
- **布局原则：**
    - 根据图片解析 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:570`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:570) 和 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:693`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:693) ，题目间转场的反馈（如“太棒了！”、“连对5题！”、卡通形象）通常以模态弹窗或叠加层(Overlay)的形式出现在屏幕中央或主要视觉区域，背景可能是当前题目或下一题目的模糊预览。
- **核心UI元素清单：**
    - 即时反馈弹窗/叠加层
    - 反馈文案（例如：“太棒了！同学巩固得很牢固”，“再想想哦～”）
    - 卡通形象/激励动画
    - 连对数提示

###### 功能需求与交互原则
- **功能描述：**
    - 题目间转场的逻辑和展示同巩固练习，参考 [PRD - 巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-Sf2FdTzLqoUffPxG0hccJShLnKb)。
    - 答对题目后，展示积极的反馈（如“太棒了！”，卡通形象鼓励）。
    - 答错题目后，展示引导性或中性的反馈（如“再想想哦～”，卡通形象提示）。
    - 连对达到一定次数时，展示特殊的激励反馈（如“连对X题！实力派666”）。
- **交互模式：**
    - 用户提交答案后，系统自动判断对错，并展示相应的即时反馈动画和文案。
    - 反馈展示完毕后，自动或通过用户点击（如“继续”）切换到下一题。
- **反馈原则：**
    - **即时性：** 用户提交答案后立即给出反馈。
    - **情感化：** 使用卡通形象和激励/引导性文案，增强用户情感连接。
    - **差异化：** 根据答题的正确与否、连对情况等，提供不同类型的反馈。

**优先级：** P0
**依赖关系：** 核心功能点2 (练习环节)

##### 用户场景与故事 (针对此核心功能点1：练习组件转场交互优化)

###### [场景1标题：学生首次进入练习环节]
作为**首次进入练习的学生**，我需要**一个清晰且富有激励感的“开始练习”转场**以解决**从学习内容到练习的生硬过渡**，从而**让我能平滑地转换状态并对练习产生期待**。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 学生在练习组件前的最后一个“文档组件”学习完毕，系统自动触发或学生点击“下一步”等按钮。
    *   **界面变化:**
        *   如果练习组件前有其他组件，顶部出现“即将进入练习”的提示条。
        *   随后，当前“文档组件”内容消失，触发翻页转场动效。
        *   翻页动效过程中或结束后，屏幕中央出现IP动效和文案“开始练习 + [课程名称]”。
    *   **即时反馈:** 视觉上的翻页动效和IP欢迎动画。
2.  **[转场展示]:** IP动效和文案展示2秒。
    *   **界面变化:** 2秒后，IP动效和文案消失，平滑过渡到练习组件的第一道题目界面（核心功能点2.1）。
    *   **即时反馈:** 无缝衔接到题目。

**场景细节补充：**
*   **前置条件:** 学生已完成练习组件前的所有课程内容，且是第一次进入该练习组件。
*   **期望结果:** 学生感受到一个明确且愉悦的“练习开始”信号，顺利进入答题状态。
*   **异常与边界情况:**
    *   ⚠️如果课程名称过长，转场文案需要考虑折行或缩略显示。
    *   如果网络状况不佳导致IP动效资源加载缓慢，应有占位符或纯文案的优雅降级方案。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[学生完成前序组件学习] --> B{练习组件前是否有其他组件?};
    B -- 是 --> C["顶部展示'即将进入练习'提示"];
    B -- 否 --> D["触发进入练习转场"];
    C --> D;
    D --> E[翻页动效开始];
    E --> F["展示IP动效和文案('开始练习 + [课程名称]')"];
    F -- 持续2秒 --> G[IP动效和文案消失];
    G --> H[平滑过渡到第一道题目界面];
```

###### [场景2标题：学生再次进入未完成的练习环节]
作为**中途退出后再次进入练习的学生**，我需要**一个简洁的“继续练习”提示**以解决**忘记上次练习进度或直接进入题目感到突兀的问题**，从而**让我能快速回忆起状态并继续作答**。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 学生打开课程，系统定位到上次退出时的练习组件。
    *   **界面变化:** 直接展示IP动效和文案“继续练习 + [课程名称]”，无翻页动效。
    *   **即时反馈:** IP欢迎动画和提示性文案。
2.  **[转场展示]:** IP动效和文案展示2秒。
    *   **界面变化:** 2秒后，IP动效和文案消失，平滑过渡到上次作答的题目或练习的起始题目界面。
    *   **即时反馈:** 无缝衔接到题目。

**场景细节补充：**
*   **前置条件:** 学生此前进入过该练习组件，但未完成所有题目便退出，本次重新进入课程且进度直接定位到此练习组件。
*   **期望结果:** 学生被友好地提醒继续之前的练习，并顺利回到上次的答题点。
*   **异常与边界情况:**
    *   ⚠️如果上次退出时正在作答某题，应恢复到该题的作答界面，计时器状态也应恢复。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[学生再次进入课程] --> B{进度是否在练习组件？}
    B -- 是 --> C["展示IP动效和文案（继续练习 + [课程名称]）"]
    C -- 持续2秒 --> D[IP动效和文案消失]
    D --> E[平滑过渡到上次作答的题目界面]
    B -- 否 --> F[进入其他组件]
```

###### [场景3标题：学生完成练习环节并离开]
作为**完成所有练习题目的学生**，我需要**一个明确的“练习结束”转场**以解决**不知道练习是否已全部完成或直接跳转下一环节的茫然感**，从而**让我有成就感并清晰感知到学习阶段的转换**。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 学生在练习组件的最后一题完成作答，并点击“继续”按钮。
    *   **界面变化:** 当前题目界面消失，触发翻页转场动效。
    *   **即时反馈:** 视觉上的翻页动效。
2.  **[转场完成]:** 翻页动效结束后。
    *   **界面变化:** 平滑过渡到练习组件后的下一个课程组件（如“课程总结”或“学习报告”）。
    *   **即时反馈:** 进入新的学习内容或环节。

**场景细节补充：**
*   **前置条件:** 学生已完成练习组件内的所有题目，并在最后一题点击了“继续”按钮。
*   **期望结果:** 学生清晰地知道练习已结束，并顺利过渡到课程的下一个部分。
*   **异常与边界情况:**
    *   ⚠️如果练习组件是课程的最后一个组件，离开练习转场后应如何处理（例如，提示课程结束，或跳转到学习报告）。

**优先级：** 高
**依赖关系：** 核心功能点2 (练习环节)

```mermaid
flowchart TD
    A[学生完成最后一题并点击'继续'] --> B[触发翻页动效];
    B --> C[翻页动效结束];
    C --> D[平滑过渡到下一个课程组件];
```

###### [场景4标题：学生在题目间切换并获得即时反馈（答对）]
作为**正在答题的学生**，我需要**在答对题目后立即获得积极的反馈和流畅的题目切换**以解决**等待整体结果的焦虑和缺乏即时激励的问题**，从而**增强我的学习信心和持续作答的动力**。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 学生选择一个选项并提交答案。
    *   **界面变化:** 系统判断答案正确。
    *   **即时反馈:** 屏幕上出现积极的反馈动画（如卡通小鹿竖起大拇指）和激励性文案（如“太棒了！同学巩固得很牢固”）。如果触发连对，同时显示连对信息（如“连对5题！实力派666”）。
2.  **[反馈展示]:** 即时反馈动画和文案展示片刻（例如1-2秒）。
    *   **界面变化:** 反馈消失，界面平滑过渡到下一道题目。
    *   **即时反馈:** 新题目加载并呈现。

**场景细节补充：**
*   **前置条件:** 学生正在练习组件中答题。
*   **期望结果:** 学生因答对题目而感到愉悦和鼓舞，并顺畅地进入下一题的作答。
*   **异常与边界情况:**
    *   如果这是最后一题，则反馈后应触发“离开练习转场”（场景3）。

**优先级：** 高
**依赖关系：** 核心功能点2 (练习环节)

```mermaid
sequenceDiagram
    participant S as 学生
    participant UI as 前端界面
    participant System as 系统

    S->>UI: 选择答案并提交
    UI->>System: 提交答案数据
    System->>System: 判断答案是否正确
    alt 答案正确
        System->>UI: 返回正确状态及反馈内容（积极反馈，连对信息）
        UI->>UI: 展示积极反馈动画和文案
        Note over UI: 持续1-2秒
        UI->>UI: 过渡到下一题
    else 答案错误 (见场景5)
        %% ...
    end
```

###### [场景5标题：学生在题目间切换并获得即时反馈（答错）]
作为**正在答题的学生**，我需要**在答错题目后获得引导性的反馈并能快速进入下一题或查看解析**以解决**因错误而产生的挫败感和不知如何改进的问题**，从而**帮助我调整心态并从错误中学习**。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 学生选择一个选项并提交答案。
    *   **界面变化:** 系统判断答案错误。
    *   **即时反馈:** 屏幕上出现引导性或中性的反馈动画（如卡通形象做思考状）和文案（如“再想想哦～”）。
2.  **[反馈展示]:** 即时反馈动画和文案展示片刻（例如1-2秒）。
    *   **界面变化:** 反馈消失，界面平滑过渡到下一道题目，或者停留在当前题目并展示解析（根据具体策略）。
    *   **即时反馈:** 新题目加载或题目解析呈现。

**场景细节补充：**
*   **前置条件:** 学生正在练习组件中答题。
*   **期望结果:** 学生虽然答错，但能得到适当的引导，减少挫败感，并能顺利进行后续学习。
*   **异常与边界情况:**
    *   如果这是最后一题，则反馈后应触发“离开练习转场”（场景3）。
    *   ⚠️答错后是否直接看解析，还是先尝试下一题，需要明确策略。原始PRD中题目间转场逻辑同巩固练习，需参考其具体实现。

**优先级：** 高
**依赖关系：** 核心功能点2 (练习环节)

```mermaid
sequenceDiagram
    participant S as 学生
    participant UI as 前端界面
    participant System as 系统

    S->>UI: 选择答案并提交
    UI->>System: 提交答案数据
    System->>System: 判断答案是否正确
    alt 答案错误
        System->>UI: 返回错误状态及反馈内容（引导性反馈）
        UI->>UI: 展示引导性反馈动画和文案
        Note over UI: 持续1-2秒
        alt 显示解析 (策略A)
            UI->>UI: 展示当前题目解析
        else 过渡到下一题 (策略B)
            UI->>UI: 过渡到下一题
        end
    else 答案正确 (见场景4)
        %% ...
    end
```

---
#### **[核心功能点2]:** 练习环节核心交互
简洁描述该功能是什么：提供学生进行题目作答、查看题目信息、使用辅助工具（如勾画、错题本）以及与答疑组件交互的核心功能。

##### **[核心功能点2.1]:** 页面框架与导航
###### 界面布局与核心UI元素
- **布局参考：**
    - [练习页面框架-题目作答中](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_A0MHb8kW1o0HTPx3oDjcmw8Unfg.png) - 仅关注布局
    - [练习页面框架-题目解析中](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GhmPbn3b9ovENsxSfWZctrAEnGb.png) - 仅关注布局
- **布局原则：**
    - 根据图片解析 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:404`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:404) 和 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:479`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:479) ，页面顶部为导航栏，包含返回、计时、进度、问一问（解析状态）、课程进度等。中部为题目内容区，包括题型、来源、题干、选项。底部为操作按钮区，如勾画、不确定、提交、加入错题本、继续。
- **核心UI元素清单：**
    - **顶部导航栏:**
        - 退出按钮 (图标 "<")
        - 作答计时器 (格式 00:00)
        - 作答进度条/文本 (例如：分子/分母，或“连对X题”)
        - “问一问”按钮 (题目进入解析状态后显示)
        - “课程进度”按钮
    - **题型组件区域:** (详见核心功能点2.2)
    - **底部操作栏:**
        - “勾画”按钮
        - “不确定”按钮 (作答时)
        - “提交”按钮 (作答时)
        - “加入错题本”按钮 (解析时)
        - “继续”按钮 (解析时)

###### 功能需求与交互原则
- **功能描述：**
    - **顶部导航栏：**
        - **退出按钮：** 逻辑同[课程框架](https://wcng60ba718p.feishu.cn/wiki/XtfswiCCQiKHdgkev4Qc1Nqined#share-LFnSdUOGPoNj6JxMIkwcQ5vEnHh)。
        - **作答计时：** 复用题型组件。
            - 每题独立正计时（格式 00:00，上限 59:59）。
            - 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时（例：一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。
        - **作答进度：**
            - **进度展示：**
                - 分母：预估学生作答数量。
                - 分子：依据推题策略：
                    - 如果学生答对，进度涨。
                    - 如果学生答错，但没有相似题，进度涨。
                    - 如果学生答错，但有相似题需要再练一道，进度不变。
            - **连对触发效果：** 同巩固练习，参考[PRD - 巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-RaX7dZ4RTo1goOxLPVjc5k4vnsd)。
- **交互模式：**
    - 顶部导航栏元素常驻显示，提供全局操作和信息概览。
    - 退出按钮点击后应有确认提示，防止误操作。
    - 作答计时自动进行，并在特定条件下暂停和继续。
    - 作答进度根据学生答题情况动态更新。
- **反馈原则：**
    - 计时器实时更新时间。
    - 进度条/文本实时反映进度变化。
    - 连对达到阈值时，有明显的视觉效果反馈。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点2.2]:** 题型组件与题目推荐
###### 界面布局与核心UI元素
- **布局参考：**
    - [单选题作答界面](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_A0MHb8kW1o0HTPx3oDjcmw8Unfg.png) - 仅关注布局
    - [单选题解析界面](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GhmPbn3b9ovENsxSfWZctrAEnGb.png) - 仅关注布局
- **布局原则：**
    - 根据图片解析 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:410`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:410) 和 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:489`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:489) ，题目信息（题型、来源）位于题干上方。题干包含文字描述和辅助图形（若有）。选项列表垂直排列在题干下方。解析状态下，正确答案和解析内容会显示在选项下方或替换选项区域。
- **核心UI元素清单：**
    - 题目类型标识 (如“单选”)
    - 题目来源标识 (如“(2024春·浙江期中)”)
    - 题干内容区域 (文字、图片/图形)
    - 选项列表 (每个选项包含标识A/B/C/D、内容、选择状态、其他用户选择比例等)
    - 正确答案显示区域 (解析状态)
    - 题目解析内容区域 (解析状态)

###### 功能需求与交互原则
- **功能描述：**
    - **题型组件：** 各题型在练习中的展示和交互，见[PRD - 题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)。(一期仅支持单选)
    - **题目推荐：** 见课中题目推荐策略，参考[PRD - 课中练习推题策略 - V1.0](https://wcng60ba718p.feishu.cn/wiki/VMEawv89PiINjUkaLxncnSYWnqb?source_type=message&from=message&disposable_login_token=eyJ1c2VyX2lkIjoiNzQ1MzA0MDU3OTczMjQ1NTQyNiIsImRldmljZV9sb2dpbl9pZCI6Ijc0NjkzNjk2MDgxOTgzNTY5OTQiLCJ0aW1lc3RhbXAiOjE3NDU3MzMyNDUsInVuaXQiOiJldV9uYyIsInB3ZF9sZXNzX2xvZ2luX2F1dGgiOiIxIiwidmVyc2lvbiI6InYzIiwidGVuYW50X2JyYW5kIjoiZmVpc2h1IiwicGtnX2JyYW5kIjoi6aOe5LmmIn0=.84d78b821498de18a6f5eba14341bd8e413fef02d2cd2cf9739cfb49f4e1b36f)。
- **交互模式：**
    - 学生点击选项进行选择。
    - 提交答案后，界面会更新显示答题结果（对/错）、正确答案、题目解析以及其他用户的选项分布。
    - 题目内容（题干、选项）根据题目推荐策略动态加载。
- **反馈原则：**
    - 选中选项时有明确的视觉状态变化。
    - 提交答案后，正确/错误有清晰的视觉区分。
    - 解析内容清晰展示。

**优先级：** P0
**依赖关系：** 外部PRD (题型支持、推题策略)

##### **[核心功能点2.3]:** 勾画与错题本功能
###### 界面布局与核心UI元素
- **布局参考：**
    - [练习页面框架-题目作答中（含勾画按钮）](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_A0MHb8kW1o0HTPx3oDjcmw8Unfg.png) - 仅关注布局
    - [练习页面框架-题目解析中（含加入错题本按钮）](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GhmPbn3b9ovENsxSfWZctrAEnGb.png) - 仅关注布局
- **布局原则：**
    - 根据图片解析 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:417`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:417) ，“勾画”按钮位于底部操作栏。
    - 根据图片解析 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:517`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:517) ，“加入错题本”按钮位于底部操作栏，在题目解析状态下显示。
- **核心UI元素清单：**
    - “勾画”按钮
    - 勾画工具栏（点击“勾画”后出现，包含画笔、橡皮擦、清除、撤销、恢复等）
    - “加入错题本”按钮 (可切换为“已加入”或“移出错题本”状态)

###### 功能需求与交互原则
- **功能描述：**
    - **勾画组件：** 同巩固练习，参考[PRD - 巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-RaX7dZ4RTo1goOxLPVjc5k4vnsd)。允许学生在题目区域进行勾画、标记。
    - **错题本组件：** 同巩固练习，参考[PRD - 巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-WdgqdEfPKo1X4BxYtJ3c6FXgnhh)。允许学生将题目加入错题本或从错题本移除。
- **交互模式：**
    - 点击“勾画”按钮，展开勾画工具栏，用户可在题目上进行涂写。再次点击或选择其他工具可收起/切换。
    - 点击“加入错题本”按钮，题目被收藏，按钮状态可能变为“已加入”或类似提示。再次点击则取消收藏。
- **反馈原则：**
    - 勾画操作实时显示在屏幕上。
    - 加入/取消错题本后，按钮状态应有明确变化，并可有轻提示（Toast）确认操作成功。

**优先级：** P0
**依赖关系：** 外部PRD (巩固练习)

##### **[核心功能点2.4]:** 答疑组件交互
###### 界面布局与核心UI元素
- **布局参考：**
    - [练习页面框架-题目解析中（含问一问按钮）](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GhmPbn3b9ovENsxSfWZctrAEnGb.png) - 仅关注布局
- **布局原则：**
    - 根据图片解析 [`ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:485`](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md:485) ，“问一问”按钮位于顶部导航栏右侧区域，在题目进入解析状态后常驻展示。
- **核心UI元素清单：**
    - “问一问”按钮
    - 答疑组件界面（点击“问一问”后跳转或弹出）

###### 功能需求与交互原则
- **功能描述：**
    - **入口：**
        - 右上角常驻展示「问一问」(在题目进入解析状态后)。
        - 长按题目内容，唤起问一问 (**本期不做**)。
    - **可触发时机：**
        - 题目进入解析状态前屏蔽问一问入口。
        - 题目进入解析状态才能选中的唤起问一问。
    - **交互：**
        - 点击问一问，进入答疑组件。
- **交互模式：**
    - “问一问”按钮在特定条件下才可点击。
    - 点击后，通常会跳转到独立的答疑界面或以弹窗/侧边栏形式展示答疑组件。
- **反馈原则：**
    - 按钮在不可用时应有置灰等视觉提示。
    - 点击后，界面平滑过渡到答疑组件。

**优先级：** P0
**依赖关系：** 答疑组件本身的功能

##### 用户场景与故事 (针对此核心功能点2：练习环节核心交互)

###### [场景1标题：学生查看题目并进行作答]
作为**正在参与练习的学生**，我需要**清晰地看到题目内容、选项，并能方便地选择答案和提交**以解决**信息混乱或操作不便导致的影响答题效率的问题**，从而**让我能专注于题目本身，顺利完成作答**。

**前端界面与交互关键步骤：**
1.  **[界面加载]:** 系统展示一道新的练习题。
    *   **界面变化:** 顶部导航栏显示当前用时、进度。中部区域显示题型、来源、题干文字和图形（如有），下方列出选项A、B、C、D。底部操作栏显示“勾画”、“不确定”、“提交”按钮。
    *   **即时反馈:** 题目内容清晰可见。
2.  **[用户操作1]:** 学生阅读题目，思考后点击某个选项（例如：选项B）。
    *   **界面变化:** 被选中的选项B高亮或出现选中标记。
    *   **即时反馈:** 视觉上明确哪个选项被选中。
3.  **[用户操作2]:** 学生点击“提交”按钮。
    *   **界面变化:** “提交”按钮可能变为不可用或显示加载状态，随后界面根据答案对错进行更新（触发核心功能点1.2的题目间转场与反馈）。
    *   **即时反馈:** 提交操作被响应，等待系统反馈。

**场景细节补充：**
*   **前置条件:** 学生已进入练习环节。
*   **期望结果:** 学生能够无障碍地理解题目、选择答案并成功提交。
*   **异常与边界情况:**
    *   题目内容过长时，应支持滚动查看。
    *   选项内容过长时，应能完整显示或支持折行。
    *   网络请求提交答案失败时，应有友好提示并允许用户重试。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[新题目加载完成] --> B[学生阅读题目和选项];
    B --> C{学生选择一个选项};
    C -- 点击选项 --> D[选中选项高亮];
    D --> E{学生点击'提交'按钮};
    E -- 点击提交 --> F[系统处理答案];
    F --> G[触发题目间转场与反馈 （核心功能点1.2）];
```

###### [场景2标题：学生在答题过程中使用勾画功能]
作为**需要辅助思考的学生**，我需要**在答题时使用勾画工具在题目上进行标记或演算**以解决**仅靠大脑记忆或想象难以理清思路的问题**，从而**帮助我更好地分析题目，提高解题准确率**。

**前端界面与交互关键步骤：**
1.  **[用户操作1]:** 学生在答题界面点击“勾画”按钮。
    *   **界面变化:** “勾画”按钮下方或旁边展开勾画工具栏，包含画笔、橡皮擦、颜色选择、清空等工具。题目区域变为可勾画状态。
    *   **即时反馈:** 工具栏出现，表明勾画功能已激活。
2.  **[用户操作2]:** 学生选择画笔工具，并在题目区域进行勾画。
    *   **界面变化:** 学生的勾画痕迹实时显示在题目上。
    *   **即时反馈:** 所画即所得。
3.  **[用户操作3]:** 学生完成勾画，再次点击“勾画”按钮或选择其他操作（如选择选项）。
    *   **界面变化:** 勾画工具栏收起，题目区域的勾画痕迹保留。
    *   **即时反馈:** 勾画模式退出，恢复正常答题交互。

**场景细节补充：**
*   **前置条件:** 学生正在答题界面。
*   **期望结果:** 学生能够自由地在题目上进行勾画，辅助思考，且勾画内容能正确保存和显示。
*   **异常与边界情况:**
    *   勾画内容不应遮挡题目关键信息。
    *   切换题目时，上一题的勾画内容是否保留或清空需要明确。 (原始PRD未明确，通常应清空或不带到下一题)

**优先级：** 高
**依赖关系：** 勾画组件 (复用巩固练习)

```mermaid
sequenceDiagram
    participant S as 学生
    participant UI as 前端界面

    S->>UI: 点击'勾画'按钮
    UI->>UI: 展开勾画工具栏
    UI->>UI: 题目区域变为可勾画
    S->>UI: 选择画笔并在题目上勾画
    UI->>UI: 实时显示勾画痕迹
    S->>UI: 再次点击'勾画'按钮 (或进行其他操作)
    UI->>UI: 收起勾画工具栏
    Note over UI: 勾画痕迹保留
```

###### [场景3标题：学生在题目解析状态下将题目加入错题本]
作为**希望复习错题或重点题目的学生**，我需要**在查看题目解析时方便地将该题加入错题本**以解决**后续难以找到这些题目进行针对性复习的问题**，从而**提高我的学习效率和效果**。

**前端界面与交互关键步骤：**
1.  **[界面状态]:** 学生已提交某题答案，界面处于该题的解析状态。
    *   **界面变化:** 底部操作栏显示“加入错题本”和“继续”按钮。
    *   **即时反馈:** 无。
2.  **[用户操作1]:** 学生点击“加入错题本”按钮。
    *   **界面变化:** “加入错题本”按钮的文本变为“已加入”（或类似提示），或按钮图标发生变化。可能会有短暂的toast提示“已加入错题本”。
    *   **即时反馈:** 明确告知用户收藏成功。
3.  **[用户操作2]:** (可选) 学生再次点击已变为“已加入”的按钮。
    *   **界面变化:** 按钮文本恢复为“加入错题本”，或按钮图标恢复。可能会有短暂的toast提示“已移出错题本”。
    *   **即时反馈:** 明确告知用户取消收藏成功。

**场景细节补充：**
*   **前置条件:** 学生正在查看某一道题的解析。
*   **期望结果:** 学生能够轻松地将有价值的题目收藏到错题本，并在需要时取消收藏。
*   **异常与边界情况:**
    *   如果加入错题本操作失败（如网络问题），应有错误提示。

**优先级：** 高
**依赖关系：** 错题本组件 (复用巩固练习)

```mermaid
sequenceDiagram
    participant S as 学生
    participant UI as 前端界面
    participant System as 系统

    Note over UI: 当前为题目解析状态
    S->>UI: 点击'加入错题本'按钮
    UI->>System: 发送加入错题本请求 (题目ID)
    System->>UI: 返回成功状态
    UI->>UI: 更新按钮为'已加入'状态
    UI->>UI: (可选)显示Toast提示'已加入错题本'

    %% 可选：取消收藏
    S->>UI: 再次点击'已加入'按钮
    UI->>System: 发送移出错题本请求 (题目ID)
    System->>UI: 返回成功状态
    UI->>UI: 更新按钮为'加入错题本'状态
    UI->>UI: (可选)显示Toast提示'已移出错题本'
```

###### [场景4标题：学生在题目解析状态下使用“问一问”功能]
作为**对题目解析仍有疑问的学生**，我需要**在查看解析时能方便地使用“问一问”功能进行提问**以解决**解析看不懂或有延伸问题无法得到解答的困境**，从而**彻底搞懂该题目涉及的知识点**。

**前端界面与交互关键步骤：**
1.  **[界面状态]:** 学生已提交某题答案，界面处于该题的解析状态。
    *   **界面变化:** 顶部导航栏右侧显示“问一问”按钮。
    *   **即时反馈:** 无。
2.  **[用户操作1]:** 学生点击“问一问”按钮。
    *   **界面变化:** 当前练习界面被替换或叠加显示答疑组件界面。
    *   **即时反馈:** 进入答疑交互流程。

**场景细节补充：**
*   **前置条件:** 学生正在查看某一道题的解析，且“问一问”功能已对该题目开放。
*   **期望结果:** 学生能够顺利进入答疑组件，并针对当前题目进行提问。
*   **异常与边界情况:**
    *   如果答疑组件加载失败，应有提示。
    *   从答疑组件返回时，应能回到之前的练习解析界面。

**优先级：** 高
**依赖关系：** 答疑组件

```mermaid
flowchart TD
    A[学生查看题目解析] --> B{是否显示'问一问'按钮?};
    B -- 是 --> C[学生点击'问一问'按钮];
    C --> D[进入答疑组件界面];
    D --> E[学生与AI进行问答交互];
    E --> F{学生退出答疑};
    F --> A;
    B -- 否 (例如解析前) --> G[按钮不显示或置灰];
```

**Mermaid图示 (核心功能点2主要流程或复杂场景图):**
```mermaid
  flowchart TD
    subgraph "练习组件核心交互流程"
        direction LR
        A[加载题目] --> B{选择答案};
        B -- 作答 --> C[提交答案];
        C --> D{答案判断};
        D -- 正确 --> E[积极反馈 & 连对处理];
        D -- 错误 --> F[引导反馈];
        E --> G[显示解析/下一题];
        F --> G;
        G --> H{查看解析?};
        H -- 是 --> I[展示题目解析];
        H -- 否 --> J[直接进入下一题];
        I --> K{使用辅助功能?};
        K -- 点击'加入错题本' --> L[处理错题本逻辑];
        K -- 点击'问一问' --> M[进入答疑组件];
        L --> J;
        M -- 退出答疑 --> I;
        J --> A; 
        %% 循环到下一题
        B -- 使用'勾画' --> N[勾画操作];
        N --> B;
    end
```

---
### 2.2 辅助功能
- **[辅助功能点1]:** 作答计时器暂停与恢复
    - **功能详述与前端呈现:**
        - **功能描述:** 当学生未作答（即未提交答案）而退出练习组件（例如切换到其他应用或关闭课程），该题的作答计时器应暂停。当学生再次回到该题目时，计时器应从上次暂停的时间点继续计时。
        - **前端呈现:** 计时器数字停止跳动表示暂停，恢复时从暂停的数字继续增加。
    - **Mermaid图示 (可选，若交互复杂则建议提供):**
      ```mermaid
      stateDiagram-v2
          [*] --> NotStarted: 题目加载
          NotStarted --> InProgress: 开始作答 (计时开始)
          InProgress --> Paused: 学生退出练习 (计时暂停)
          Paused --> InProgress: 学生返回练习 (计时恢复)
          InProgress --> Submitted: 提交答案 (计时停止)
          Submitted --> [*]
      ```
- **[辅助功能点2]:** 连对效果展示
    - **功能详述与前端呈现:**
        - **功能描述:** 根据[PRD - 巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f#share-RaX7dZ4RTo1goOxLPVjc5k4vnsd)的规则，当学生连续答对题目达到特定数量时，在题目间转场或即时反馈中展示特殊的视觉和文案激励。
        - **前端呈现:** 可能是更华丽的动画、特殊的音效（若有）、以及如“连对X题！”、“太强了！”等更高级别的激励文案和视觉元素（如奖杯、星星）。
    - **Mermaid图示 (可选，若交互复杂则建议提供):**
      ```mermaid
      flowchart TD
          A[学生答对一题] --> B{是否达到连对阈值?};
          B -- 是 --> C[展示特殊连对激励效果];
          B -- 否 --> D[展示普通答对反馈];
          C --> E[进入下一题];
          D --> E;
      ```

### 2.3 非本期功能
- **[功能点1]:** 长按题目内容唤起问一问
    - **简要描述:** 学生通过长按题目中的特定文本或区域来快速触发“问一问”答疑功能。
    - **原因:** 本期优先实现点击按钮触发“问一问”，长按交互后续迭代考虑。
    - **建议:** 后续迭代再考虑。
- **[功能点2]:** 互动讲题模块
    - **简要描述:** 基于题目分布讲解和互动，进一步提升课中练习体验与教学效果。
    - **原因:** 原始PRD中提及为“未来会做什么”，不属于本次1.0版本范围。
    - **建议:** 未来版本规划。


## 3. 业务流程图 (可选，用于整体业务流)

```mermaid
graph TD
    A[用户进入AI课中] --> B{课程组件};
    B -- 文档组件 --> C[学习文档内容];
    C --> B; 
    %% 可能有多个文档组件或返回选择其他组件
    B -- 练习组件 --> D[进入练习转场];
    D --> E[学生进行题目作答];
    E -- 答题循环/题目间转场 --> E;
    E -- 完成所有题目 --> F[离开练习转场];
    F --> B; 
    %% 进入下一个课程组件
    C -- 可选:答疑 --> G[答疑组件交互];
    E -- 可选:答疑(解析状态) --> G;
    G --> C;
    G --> E;
    B -- 答疑组件(挂载) --> G;
    F --> H[学习报告/课程结束];

    %% 简化表示，实际组件顺序和类型可配置
    subgraph "单节课程示例流程"
        S_Intro[课程引入（文档）] --> S_K1[知识点1（文档）]
        S_K1 --> S_Ex1[练习1（练习）]
        S_Ex1 --> S_K2[知识点2（文档）]
        S_K2 --> S_Sum[课程总结（文档）]
        S_Sum --> S_Report[学习报告（功能页）]
    end
    A --> S_Intro

    %% 答疑组件的挂载关系
    S_Intro -.-> S_QA1[答疑组件]
    S_K1 -.-> S_QA2[答疑组件]
    S_Ex1 -.-> S_QA3[答疑组件]
    S_K2 -.-> S_QA4[答疑组件]
    S_Sum -.-> S_QA5[答疑组件]

```
*原始PRD业务流程图图片解析已包含Mermaid图，此处根据其解析并结合练习组件的上下文进行调整和补充。*
*原始图片解析中的Mermaid图：*
```mermaid
flowchart TD
    subgraph 单节课程内容
        A_img[课程开场页] --> B_img[课程引入]
        B_img --> C_img[知识点1]
        C_img --> D_img[知识点2]
        D_img --> E_img[练习1]
        E_img --> F_img[知识点3]
        F_img --> G_img[知识点4]
        G_img --> H_img[练习2]
        H_img --> I_img[课程总结]
        I_img --> J_img[学习报告]
    end

    A1_img[功能页面]
    B1_img[文档组件]
    C1_img[文档组件]
    D1_img[文档组件]
    E1_img[练习组件]
    F1_img[文档组件]
    G1_img[文档组件]
    H1_img[练习组件]
    I1_img[文档组件]
    J1_img[功能页面]

    B2_img[答疑组件]
    C2_img[答疑组件]
    D2_img[答疑组件]
    E2_img[答疑组件]
    F2_img[答疑组件]
    G2_img[答疑组件]
    H2_img[答疑组件]
    I2_img[答疑组件]

    A_img -.-> A1_img
    B_img -.-> B1_img
    C_img -.-> C1_img
    D_img -.-> D1_img
    E_img -.-> E1_img
    F_img -.-> F1_img
    G_img -.-> G1_img
    H_img -.-> H1_img
    I_img -.-> I1_img
    J_img -.-> J1_img

    B1_img -.-> B2_img
    C1_img -.-> C2_img
    D1_img -.-> D2_img
    E1_img -.-> E2_img
    F1_img -.-> F2_img
    G1_img -.-> G2_img
    H1_img -.-> H2_img
    I1_img -.-> I2_img
```

## 4. 性能与安全需求

### 4.1 性能需求
-   **响应时间：**
    -   进入/离开练习的转场动画（翻页、IP动效）应在 **0.5秒** 内开始播放，动画过程流畅不卡顿。
    -   题目间转场的即时反馈（答对/答错动画和文案）应在用户提交答案后的 **0.3秒** 内开始展示。
    -   题目内容（特别是包含图片的题目）加载时间应在 **1秒** 内完成 (95th percentile)。
    -   勾画操作的响应（落笔、擦除）应接近实时，延迟感小于 **100毫秒**。
-   **并发用户：** (主要由后端承载，前端关注单用户体验流畅性)
-   **数据量：** (练习组件通常是单题展示，不涉及大量数据列表)
-   **前端性能感知：**
    -   **转场动画与IP动效**: 动画资源（如序列帧、Lottie文件）应提前预加载或优化体积，确保首次播放流畅。若资源较大，可使用骨架屏或占位符过渡。
    -   **题目内容加载**: 若题目包含大图片或复杂公式渲染，应有加载状态提示（如loading动画），避免长时间白屏。
    -   **勾画操作**: 避免因勾画操作导致CPU占用过高，影响页面其他交互的响应。

### 4.2 安全需求
-   **权限控制：** (练习组件本身不直接涉及强权限控制，依赖课程整体的访问权限)
-   **数据加密：** 学生作答数据（选项、用时）在提交给后端时，应通过 HTTPS 传输。
-   **操作审计：** (主要由后端记录学生答题行为)
-   **输入校验:** (练习组件主要是选择题，输入场景较少，若有填空题等，需进行基础校验)

## 5. 验收标准

### 5.1 功能验收
-   **[核心功能点1.1：进入/离开练习的转场效果]：**
    -   **用户场景：**
        -   当学生首次进入练习组件时。
        -   当学生再次进入未完成的练习组件时。
        -   当学生完成练习组件所有题目并离开时。
    -   **界面与交互：**
        -   首次进入时，顶部正确显示“即将进入练习”提示（若适用），随后播放翻页动效、IP动效及“开始练习+[课程名称]”文案，持续2秒后进入第一题。
        -   再次进入时，直接播放IP动效及“继续练习+[课程名称]”文案，持续2秒后进入上次作答题目。
        -   完成练习离开时，点击最后一题“继续”后，播放翻页动效，然后进入下一组件。
    -   **期望结果：** 转场动画流畅，文案正确，计时准确，符合PRD描述的各种条件和时机。
-   **[核心功能点1.2：题目间转场与即时反馈]：**
    -   **用户场景：** 学生在练习中提交答案后。
    -   **界面与交互：**
        -   答对题目后，展示积极的反馈动画和文案（如“太棒了！”）。
        -   答错题目后，展示引导性的反馈动画和文案（如“再想想哦～”）。
        -   触发连对时，展示特殊的连对激励效果。
        -   反馈展示后，平滑过渡到下一题或题目解析。
    -   **期望结果：** 反馈类型准确，动画流畅，文案符合预期，符合巩固练习的逻辑。
-   **[核心功能点2.1：页面框架与导航]：**
    -   **用户场景：** 学生在练习组件中进行操作时。
    -   **界面与交互：**
        -   顶部导航栏正确显示退出按钮、作答计时器（格式00:00，上限59:59，支持暂停和恢复）、作答进度（分子/分母，根据策略更新）。
        -   连对效果按巩固练习规则触发并展示。
    -   **期望结果：** 导航栏各元素功能正常，计时准确，进度更新符合逻辑。
-   **[核心功能点2.2：题型组件与题目推荐]：**
    -   **用户场景：** 学生作答不同题目时。
    -   **界面与交互：**
        -   题目能正确展示（一期仅单选），包含题型、来源、题干、选项。
        -   题目推荐逻辑符合[PRD - 课中练习推题策略 - V1.0]。
        -   提交答案后，能正确显示解析（若有）。
    -   **期望结果：** 题目展示和交互符合[PRD - 题型支持 - 一期]，推题逻辑正确。
-   **[核心功能点2.3：勾画与错题本功能]：**
    -   **用户场景：** 学生使用勾画或错题本功能时。
    -   **界面与交互：**
        -   勾画功能（画笔、橡皮擦等）操作正常，痕迹能正确显示和清除。
        -   “加入错题本”功能可正常将题目加入或移出错题本，按钮状态随之更新。
    -   **期望结果：** 勾画和错题本功能符合巩固练习的逻辑和体验。
-   **[核心功能点2.4：答疑组件交互]：**
    -   **用户场景：** 学生在题目解析状态下点击“问一问”。
    -   **界面与交互：**
        -   “问一问”按钮在题目进入解析状态前屏蔽，解析状态下可点击。
        -   点击“问一问”后能成功进入答疑组件。
    -   **期望结果：** 答疑组件入口控制正确，能顺利跳转。

### 5.2 性能验收
-   **响应时间：**
    -   **测试用例1:** 首次进入练习，转场动画（翻页+IP动效）在用户触发后0.5秒内开始，动画播放流畅。
    -   **测试用例2:** 提交答案后，即时反馈动画在0.3秒内开始展示。
    -   **测试用例3:** 加载包含标准大小图片的题目，内容在1秒内完全展示。
-   **前端性能感知验收：**
    -   **测试用例1 (转场动画):** 多次触发进入/离开练习转场，动画无明显卡顿或掉帧。
    -   **测试用例2 (勾画):** 连续进行勾画操作，界面无卡顿，CPU占用在合理范围。

### 5.3 安全验收
-   **数据加密：**
    -   **测试用例1:** 通过浏览器开发者工具查看学生提交答案的网络请求，确认传输使用HTTPS。

## 6. 其他需求

### 6.1 可用性与体验需求
-   **界面友好与直观性：**
    -   转场动画和即时反馈应自然、不突兀，增强用户沉浸感。
    -   所有可交互元素（按钮、选项）点击区域适中，易于操作。
    -   图标和文案清晰易懂，符合学生用户认知。
-   **容错处理：**
    -   当题目资源加载失败（如图片无法显示），应有占位符或错误提示，不影响整体作答流程。
    -   当提交答案或加入错题本等操作因网络问题失败时，应有明确提示并允许用户重试。
-   **兼容性：**
    -   **浏览器：** 需支持最新版本的 Chrome, Firefox, Safari, Edge 浏览器，保证核心功能和界面显示正常。
    -   **响应式布局：** ⚠️原始PRD未明确是否需要响应式，若需要，应考虑在不同屏幕尺寸（桌面、平板、移动端）下的布局适配，特别是练习区域的元素排布和可操作性。
-   **可访问性 (A11y)：**
    -   ⚠️核心交互元素（如选项、提交按钮、继续按钮）应支持键盘操作。
    -   图片（如IP形象、题目配图）应提供有意义的 `alt` 文本。
    -   颜色对比度应考虑可读性。
-   **交互一致性：**
    -   练习组件内的按钮样式、交互反馈模式应与课程内其他组件（如文档组件）及巩固练习保持一致性。

### 6.2 维护性需求
-   **配置管理：**
    -   转场动画时长、IP形象资源、反馈文案等，如有可能，建议支持后台配置或通过前端配置文件管理，方便调整和运营。
-   **监控告警：**
    -   监控题目加载失败率、答案提交成功率。
    -   监控转场动画播放异常（如卡顿、无法加载）。
-   **日志管理：**
    -   前端记录关键用户行为：进入/离开练习、每题提交（答案、用时、对错）、使用勾画、加入错题本、点击问一问。
    -   记录前端JS错误、API请求异常。

## 7. 用户反馈和迭代计划

### 7.1 用户反馈机制
-   用户可以通过AI课中已有的反馈渠道（如客服、App内反馈入口）提交关于练习组件体验的问题和建议。
-   重点关注用户对转场效果、即时反馈的接受度和喜爱程度。

### 7.2 迭代计划
-   **迭代周期：** 跟随AI课中整体迭代计划。
-   **优先级调整：** 本次需求为P0，完成后根据用户反馈和数据表现，评估优化点，如新增转场类型、丰富反馈动画等，纳入后续迭代。

## 8. 需求检查清单
使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| -------------------------------- | ----------------------------------- | ------ | ------ | ------ | ------------------------- | --------------------------- | ----------- | ---- |
| 进入练习提示 | 2.1 核心功能点1.1 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] |  |
| 首次进入练习转场 | 2.1 核心功能点1.1 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] |  |
| 再次进入练习转场 | 2.1 核心功能点1.1 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] |  |
| 离开练习转场 | 2.1 核心功能点1.1 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] |  |
| 顶部导航栏：退出按钮 | 2.1 核心功能点2.1 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] |  |
| 顶部导航栏：作答计时 | 2.1 核心功能点2.1 / 2.2 辅助功能点1 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] |  |
| 顶部导航栏：作答进度 | 2.1 核心功能点2.1 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] |  |
| 顶部导航栏：连对触发效果 | 2.1 核心功能点2.1 / 2.2 辅助功能点2 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] |  |
| 题型组件 | 2.1 核心功能点2.2 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] | 依赖外部PRD |
| 题目推荐 | 2.1 核心功能点2.2 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] | 依赖外部PRD |
| 勾画组件 | 2.1 核心功能点2.3 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] | 依赖外部PRD |
| 错题本组件 | 2.1 核心功能点2.3 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] | 依赖外部PRD |
| 答疑组件入口与交互 | 2.1 核心功能点2.4 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] |  |
| 题目间转场 | 2.1 核心功能点1.2 | [✅] | [✅] | [✅] | [✅] (5.1) | [✅] (P0) | [✅] | 依赖外部PRD |
| 长按题目内容唤起问一问 | 2.3 非本期功能 | [✅] | [✅] | [✅] | [N/A]                     | [N/A]                       | [✅]         |  |

-   **完整性：** 原始需求是否都有对应的优化后需求点，无遗漏。
-   **正确性：** 优化后需求描述是否准确表达了原始需求的意图。
-   **一致性：** 优化后需求之间是否有冲突或重复。
-   **可验证性：** 优化后需求是否有明确的验收标准（对应5. 验收标准）。
-   **可跟踪性：** 优化后需求是否有明确的优先级和依赖关系。
-   **UI/UX明确性：** 优化后需求是否清晰描述了前端界面、交互和用户体验细节。

✅表示通过；❌表示未通过；⚠️表示描述正确，但UI/UX细节、验收标准或图示等仍需与PM进一步沟通完善；N/A表示不适用。每个需求点都需要填写。

## 9. 附录

### 9.1 原型图/设计稿链接
-   视觉稿：待补充 (来自原始PRD)
-   [进入练习提示及IP动效参考图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XwKpbrJtaoPYEIxbkDbccUBKnYd.png)
-   [翻页动效参考图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_L4WcbI0zAoClQpx9hBmcJNjXnkd.png)
-   [练习页面框架-题目作答中参考图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_A0MHb8kW1o0HTPx3oDjcmw8Unfg.png)
-   [练习页面框架-题目解析中参考图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GhmPbn3b9ovENsxSfWZctrAEnGb.png)
-   [题目间转场反馈-答对参考图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GFWobx4zHo13YlxhwhlccN3GnRe.png)
-   [题目间转场反馈-答错参考图](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BosmbvrKeo8580x9RqycMEZ7nYb.png)

### 9.2 术语表
-   **IP (Intellectual Property):** 在此指课程或品牌相关的卡通形象、角色等。
-   **Tip:** 小提示，通常以简短文字或图标形式出现。
-   **LCP (Largest Contentful Paint):** 最大内容绘制，衡量页面加载速度的指标。
-   **FID (First Input Delay):** 首次输入延迟，衡量页面交互响应性的指标。
-   **INP (Interaction to Next Paint):** 交互至下次绘制，衡量页面整体响应性的指标。
-   **A11y (Accessibility):** 可访问性，指产品设计应确保残障人士也能无障碍使用。

### 9.3 参考资料
-   [原始需求文档：Format-PRD_-_AI_课中_-_练习组件_1.0_.md](ai-generates/raw-docs/prd/Format-PRD_-_AI_课中_-_练习组件_1.0_.md)
-   [PRD - AI课中 - V1.0](https://wcng60ba718p.feishu.cn/wiki/XtfswiCCQiKHdgkev4Qc1Nqined) (关联需求)
-   [PRD - 题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd) (关联需求)
-   [PRD - 巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f) (多处功能复用此文档逻辑)
-   [PRD - 课中练习推题策略 - V1.0](https://wcng60ba718p.feishu.cn/wiki/VMEawv89PiINjUkaLxncnSYWnqb?source_type=message&from=message&disposable_login_token=eyJ1c2VyX2lkIjoiNzQ1MzA0MDU3OTczMjQ1NTQyNiIsImRldmljZV9sb2dpbl9pZCI6Ijc0NjkzNjk2MDgxOTgzNTY5OTQiLCJ0aW1lc3RhbXAiOjE3NDU3MzMyNDUsInVuaXQiOiJldV9uYyIsInB3ZF9sZXNzX2xvZ2luX2F1dGgiOiIxIiwidmVyc2lvbiI6InYzIiwidGVuYW50X2JyYW5kIjoiZmVpc2h1IiwicGtnX2JyYW5kIjoi6aOe5LmmIn0=.84d78b821498de18a6f5eba14341bd8e413fef02d2cd2cf9739cfb49f4e1b36f) (关联需求)
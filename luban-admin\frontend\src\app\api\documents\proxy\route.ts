import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // 从查询参数获取目标URL
    const url = request.nextUrl.searchParams.get('url');
    
    if (!url) {
      return NextResponse.json(
        { error: '缺少URL参数' },
        { status: 400 }
      );
    }

    console.log('[Proxy] 代理请求URL:', url);

    // 发起请求获取内容
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'text/markdown, text/plain, */*',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      },
    });

    if (!response.ok) {
      console.error('[Proxy] 请求失败:', response.status, response.statusText);
      return NextResponse.json(
        { error: `获取内容失败: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    // 获取内容
    const content = await response.text();
    console.log('[Proxy] 成功获取内容，长度:', content.length);

    // 返回内容，设置适当的headers
    return new NextResponse(content, {
      status: 200,
      headers: {
        'Content-Type': 'text/markdown; charset=utf-8',
        'Cache-Control': 'no-cache',
        'Access-Control-Allow-Origin': '*',
      },
    });
  } catch (error) {
    console.error('[Proxy] 代理请求错误:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : '代理请求失败' },
      { status: 500 }
    );
  }
}

// 处理OPTIONS请求（CORS预检）
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
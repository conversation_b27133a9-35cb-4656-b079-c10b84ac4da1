# 前端产品需求文档模板 v1.1 (参照 AI PM 工作指南修订)

## 1. 需求背景与目标

### 1.1 需求目标

- 通过该需求的实现，可以 **优化AI课中练习环节的用户体验**，重点提升 **作答流程的流畅度** 和 **即时反馈的有效性与情感化**。
- 该需求上线后，预计核心指标 **练习题完成率** 预计可以提升 **8%**，**用户对练习环节的满意度评分** 预计可以达到 **4.2分（5分制）**。

### 1.2 用户价值

- 对于 **使用新AI课的学生**，解决了 **练习切换生硬、缺乏及时反馈和激励** 的痛点，带来了 **更流畅、更有趣、更受鼓励的作答体验**。
- 对于 **使用新AI课的学生**，满足了 **在练习中获得即时指导和成就感** 的核心诉求，实现了 **增强学习参与感和自信心，提升知识巩固效果**。

## 2. 功能范围

### 2.1 核心功能

### **[核心功能点1]: 练习组件交互与反馈优化**

**用户价值与场景:** 优化学生从课程内容进入练习、以及在练习题目之间切换时的体验，提供更自然的过渡和即时的作答反馈，增强学习流畅性和用户的情感连接。

**功能详述与前端呈现:**

- **主要交互流程:**
    1. **进入练习:**
        - 从上一组件（如文档）进入练习组件前，顶部出现提示条“下拉进入练习”。
        - **首次进入:** 触发转场动效（如翻页），展示 IP 动效及文案“开始练习 + 课程名称”，持续2秒后进入第一题。
        - **再次进入 (续学):** 触发转场动效，展示 IP 动效及文案“继续练习 + 课程名称”，持续2秒后进入上次未完成的题目。
    2. **题目作答:**
        - 展示题目界面，包含顶部导航（退出、计时、进度）、题干区域（题型、题干、选项）、底部操作区（不确定、提交、勾画、问一问入口）。
        - 用户选择选项，点击“提交”。
    3. **题目间转场与反馈:**
        - 提交答案后，根据作答结果（正确/错误/连对等）触发不同的即时反馈动画和音效（如正确的小星星动画、错误的提示音效、连对的激励动画）。
        - 反馈动画播放完毕后，自动过渡到下一题或练习总结（如果全部完成）。
        - （可选）根据作答情况（如连续错误）可能触发难度调整提示。
        - （可选）根据作答行为（如过快）可能触发不认真作答提示。
- **界面布局与元素:**
    - **进入练习提示条:** 顶部固定提示，包含文字和下拉指示图标。
    - **进入练习转场:** 全屏动画效果，包含IP形象动画和文字展示区域。
    - **答题界面:**
        - **顶部导航:** 左侧退出按钮，中间计时器（MM:SS），右侧进度指示（X/Y）和问一问入口。
        - **题干区:** 题型标签（如“单选”）、题干文本、选项列表（A/B/C/D...，包含选项内容）。
        - **底部操作区:** 左侧“不确定”按钮，中间“提交”按钮（核心操作），右侧“勾画”按钮。
        - **问一问入口:** 右上角常驻（题目进入解析状态前屏蔽）。
        - **勾画/错题本功能:** 复用现有组件实现，入口位于底部操作区或通过特定交互触发。
    - **题目间反馈:** 通常为叠加在当前题目上或全屏的短暂动画效果，包含图形元素（星星、叉号、烟花等）和可能的反馈文案（如“答对了！”、“再想想哦”、“太棒了！连对X题！”）。
    - **UI元素状态:**
        - 选项：默认、选中。
        - 提交按钮：默认（可用）、点击、禁用（如未选择答案）。
        - 不确定按钮：默认、选中（标记状态）。
        - 勾画按钮：默认、激活（进入勾画模式）。
        - 问一问入口：可用、禁用（解析状态）。
        - 反馈动画：播放中、播放完毕。
- **用户操作与反馈:**
    - **下拉进入练习:** (假设需要手势触发) 触发转场提示条。
    - **进入练习:** 自动播放转场动画和IP欢迎语。
    - **选择选项:** 选项状态变为选中。
    - **点击提交:** 提交按钮可能显示加载状态，随后触发即时反馈动画和音效。答案正确/错误有明显区分。连对达到阈值有特殊激励反馈。
    - **点击不确定:** 按钮状态变化，标记题目为不确定。
    - **点击勾画:** 进入勾画模式，可在题目区域进行涂鸦标记。
    - **长按题目内容/点击问一问:** 触发问答流程（复用答疑组件）。
    - **点击退出:** 弹出确认框（或直接退出），保存当前进度。
- **数据展示与更新:**
    - 计时器实时更新。
    - 进度指示根据推题策略和作答结果动态更新分子（已完成有效题目数）。
    - 连对次数达到阈值时，触发连对反馈。

**优先级：** 高
**依赖关系：** 依赖课程配置（题目数据）、用户进度服务、答疑组件、勾画组件、错题本组件、题目推荐策略服务。

### 用户场景与故事 (针对此核心功能点)

### [场景1: 学生首次从文档组件进入练习环节]

作为一个 **正在学习AI课程的学生**，我希望 **从讲解部分过渡到练习部分时，有一个清晰且有趣的提示和转场**，这样我就可以 **明确知道要开始做题了，并能以积极的心态进入练习**。目前的痛点是 **直接切换到题目可能感觉比较突然，缺乏过渡**，如果能够 **有一个带有角色动画和提示语的转场效果**，对我的学习流畅性和情绪会有很好的提升。

**前端界面与交互关键步骤：**

1. **[用户操作1]:** 学生在文档组件学习，内容即将结束，顶部出现提示条：“下拉进入练习”。（或自动触发）
    - **界面变化:** 顶部导航栏下方出现一个提示条。
    - **即时反馈:** 用户知道下一步是练习。
2. **[用户操作2]:** 学生完成文档学习（或执行下拉手势）。
    - **界面变化:** 触发翻页或其他转场动效。屏幕中央出现IP形象动画，并显示文字“开始练习：《[课程名称]》”。
    - **即时反馈:** 视觉上看到生动的转场，明确告知练习开始。
3. **[用户操作3]:** 等待2秒转场动画结束。
    - **界面变化:** 转场动画消失，呈现练习组件的第一道题目界面。包含顶部导航（退出、计时00:00、进度0/Y）、题干区和底部操作区。
    - **即时反馈:** 用户进入答题状态。

**场景细节补充：**

- **前置条件:** 用户正在学习一个包含练习组件的AI课程，且即将从前一个组件进入练习组件。这是用户本次会话首次进入该练习组件。
- **期望结果:** 用户平滑、愉快地从学习讲解过渡到练习环节。
- **异常与边界情况:**
    - 转场动画加载失败：应快速跳过，直接展示题目，避免卡顿。
    - 课程名称获取失败：转场文案中不显示课程名或使用通用文案。

**优先级：** 高
**依赖关系：** 依赖课程配置获取课程名称，依赖转场动画资源。

```mermaid
%% 首次进入练习转场流程
sequenceDiagram
    participant User as 用户
    participant Frontend as 前端应用
    participant AnimationEngine as 动画引擎

    User->>Frontend: 完成前一组件 / 执行进入练习手势
    Frontend->>Frontend: 判断为首次进入该练习
    Frontend->>AnimationEngine: 请求播放“首次进入”转场 (翻页效果, IP动画, 文案: 开始练习+课程名)
    AnimationEngine-->>Frontend: 开始播放转场动画
    Frontend->>User: 展示转场动画 (持续2秒)

    %% 2秒后
    AnimationEngine-->>Frontend: 动画播放结束
    Frontend->>Frontend: 加载第一道练习题数据
    Frontend->>User: 显示第一题界面 (含计时器, 进度, 题干, 选项, 操作按钮)

```

### [场景2: 学生提交答案后获得即时反馈]

作为一个 **在AI课中认真做题的学生**，我希望 **每次提交答案后能立刻知道对错，并且能得到一些有趣的反馈**，这样我就可以 **及时了解自己的掌握情况，并保持做题的动力**。目前的痛点是 **做完所有题才知道结果，或者反馈很单调**，如果能够 **每做完一题就有对错的动画/音效提示，答对多了还有特殊鼓励**，我的练习体验会好很多。

**前端界面与交互关键步骤：**

1. **[用户操作1]:** 学生在答题界面选择一个选项（如B）。
    - **界面变化:** 选项B呈现选中状态。
    - **即时反馈:** 用户确认自己的选择。
2. **[用户操作2]:** 学生点击“提交”按钮。
    - **界面变化:** “提交”按钮可能短暂显示“提交中...”或禁用状态。随后，根据后端返回的判断结果：
        - **答对:** 屏幕上可能出现短暂的“正确”图标（如√）、星星动画，并伴有清脆的提示音效。连对次数增加。如果达到连对阈值（如连对5题），可能触发更华丽的庆祝动画（如烟花）和成就提示（如“实力派666！连对5题！”）。
        - **答错:** 屏幕上可能出现短暂的“错误”图标（如×）、悲伤表情动画，并伴有提示音效。连对次数中断。
    - **即时反馈:** 用户通过视觉和听觉即时获知答案对错，并获得相应的情感反馈。
3. **[用户操作3]:** 反馈动画播放完毕。
    - **界面变化:** 自动加载并显示下一道题目。计时器重置（如果是每题独立计时），进度条可能更新。
    - **即时反馈:** 练习流程自动向前推进。

**场景细节补充：**

- **前置条件:** 学生正在练习组件的答题界面，已选择答案。
- **期望结果:** 学生在提交答案后能获得即时、清晰、富有情感的反馈，并能无缝进入下一题。
- **异常与边界情况:**
    - 网络延迟：提交后按钮应有loading状态，避免重复提交。若长时间无响应，应有提示。
    - 反馈动画资源加载失败：应跳过动画，直接显示对错结果或进入下一题。
    - 这是最后一题：反馈动画后应进入练习总结或结算页。

**优先级：** 高
**依赖关系：** 依赖后端快速判断答案对错，依赖反馈动画和音效资源。

```mermaid
stateDiagram-v2
    [*] --> Answering: 进入答题界面

    Answering --> Submitting: 用户点击"提交"
    Submitting --> Processing: 发送答案至后端

    Processing --> CorrectFeedback: 答案正确
    Processing --> IncorrectFeedback: 答案错误
    Processing --> ErrorState: 提交错误/超时

    CorrectFeedback --> CheckStreak: 显示正确反馈
    CheckStreak --> HighStreakFeedback: 连对次数达标
    CheckStreak --> NormalCorrectFeedback: 连对次数未达标

    HighStreakFeedback --> LoadNext: 显示连对成就
    NormalCorrectFeedback --> LoadNext: 反馈结束

    IncorrectFeedback --> LoadNext: 显示错误反馈

    LoadNext --> [*]: 加载下一题或进入总结

    ErrorState --> Answering: 显示错误提示，重试

    state Answering {
        [*] --> Viewing: 查看题目
        Viewing --> Answered: 用户选择答案
        Answered --> ReadyToSubmit: 答案已选择
    }

    state Submitting {
        [*] --> Sending: 数据传输中
        Sending --> Sent: 数据已发送
    }

    state Processing {
        [*] --> Waiting: 等待响应
        Waiting --> ResultReceived: 结果已接收
    }


```

### [场景3: 学生在练习中遇到难题需要提问]

作为一个 **在练习中遇到困难的学生**，我希望 **能够方便地针对当前题目进行提问**，这样我就可以 **及时解决疑问，更好地理解题目和知识点**。目前的痛点是 **遇到难题只能跳过或者去其他地方寻求帮助，打断了练习流程**，如果能够 **在题目界面直接长按文字或者点击提问按钮就能发起提问**，对我的学习连续性和问题解决效率会有很大帮助。

**前端界面与交互关键步骤：**

1. **[用户操作1]:** 学生在答题界面，长按题干中的某段文字。
    - **界面变化:** 被长按的文字段落高亮显示，并在旁边出现一个“问一问”的小图标或按钮。
    - **即时反馈:** 用户知道已成功选中文字，并看到了提问入口。
2. **[用户操作2]:** 学生点击出现的“问一问”图标/按钮。
    - **界面变化:** 底部或侧边弹出答疑组件界面，并将选中的文字自动填充到提问输入框中。
    - **即时反馈:** 用户进入提问流程，且提问内容已自动关联到题目原文。
3. **[用户操作 alternate]:** 或者，学生点击右上角常驻的“问一问”按钮（假设题目非解析状态）。
    - **界面变化:** 底部或侧边弹出答疑组件界面，输入框为空，等待用户输入问题。界面可能同时展示当前题目的上下文。
    - **即时反馈:** 用户进入提问流程。

**场景细节补充：**

- **前置条件:** 学生正在练习组件的答题界面。题目处于可提问状态（非解析后）。答疑组件功能可用。
- **期望结果:** 学生能够方便地通过长按选中文字或点击按钮，在练习过程中针对当前题目发起提问。
- **异常与边界情况:**
    - 答疑组件加载失败：应有提示。
    - 题目处于解析状态：右上角“问一问”按钮应置灰或隐藏。
    - 长按非文本区域：无反应或按标准长按事件处理。

**优先级：** 中（依赖答疑组件）
**依赖关系：** 依赖答疑组件及其交互逻辑。

```mermaid
%% 练习中提问流程
sequenceDiagram
    participant User as 用户
    participant ExerciseUI as 练习界面
    participant QA_Component as 答疑组件

    alt 长按提问
        User->>ExerciseUI: 长按题干文字
        ExerciseUI->>ExerciseUI: 高亮选中文字
        ExerciseUI->>User: 显示'问一问'按钮/图标
        User->>ExerciseUI: 点击'问一问'
        ExerciseUI->>QA_Component: 请求唤起答疑组件 (传入选中文字)
        QA_Component->>User: 显示答疑界面 (输入框已填充文字)
    else 点击按钮提问
        User->>ExerciseUI: 点击右上角'问一问'按钮
        ExerciseUI->>QA_Component: 请求唤起答疑组件 (无预设文字)
        QA_Component->>User: 显示答疑界面 (输入框为空)
    end

    User->>QA_Component: 输入问题 / 修改问题
    User->>QA_Component: 点击发送
    QA_Component->>Backend_QA: 发送问题 (关联题目ID)
    Backend_QA-->>QA_Component: 返回回答
    QA_Component->>User: 显示回答

    User->>QA_Component: 关闭答疑组件
    QA_Component->>ExerciseUI: 隐藏答疑界面
    ExerciseUI->>User: 返回练习界面

```

### 2.2 辅助功能

- **[辅助功能点1]:** 勾画功能，为 [核心功能点1 - 练习组件] 提供支持
    - **功能详述与前端呈现:** 复用现有“巩固练习”中的勾画组件。在练习答题界面的底部操作区提供“勾画”按钮入口。点击后激活勾画模式，用户可在题目区域（题干、选项）进行自由勾画、标记。再次点击按钮或特定操作可退出勾画模式。勾画内容应能随题目保存（如果需要）或在切换题目时清除。
    - **Mermaid图示 (可选):** (由于是复用，且交互相对独立，暂省略，可在勾画组件PRD中查找)
- **[辅助功能点2]:** 错题本功能，为 [核心功能点1 - 练习组件] 提供支持
    - **功能详述与前端呈现:** 复用现有“巩固练习”中的错题本组件。在题目作答反馈界面（如显示答案和解析时）提供“加入错题本”按钮。点击后将当前题目加入用户错题本。按钮状态应能反映当前题目是否已在错题本中（如“加入错题本” vs “已加入”）。
    - **Mermaid图示 (可选):** (由于是复用，且交互相对独立，暂省略，可在错题本组件PRD中查找)
- **[辅助功能点3]:** 答疑组件，为 [核心功能点1 - 练习组件] 提供支持
    - **功能详述与前端呈现:** 复用答疑组件。在练习答题界面右上角提供常驻入口（解析状态前可用），并通过长按题目内容唤起。负责处理用户的提问和展示回答。
    - **Mermaid图示 (可选):** (参见核心功能点1-场景3的图示)

### 2.3 非本期功能

- **[功能点1]:** 引入互动讲题模块，[原因]：功能复杂度高，涉及AI讲解逻辑和内容制作，V1.0聚焦基础交互优化，[建议]：未来版本规划。

## 3. 业务流程图 (可选，用于整体业务流)

```mermaid
graph TD
    A[进入AI课程] --> B(学习文档组件);
    B --> C{判断是否进入练习};
    C -- 是 --> D[播放进入练习转场];
    C -- 否 --> B; 
    %% 继续学习或进入其他组件

    D --> E[展示第一道练习题];
    subgraph 练习答题循环
        E --> F{用户作答};
        F -- 选择答案 --> G[点击提交];
        G --> H{后端判断对错};
        H --> I[播放即时反馈动画/音效];
        I --> J{是否还有下一题?};
        J -- 是 --> K[加载下一题];
        K --> E;
        J -- 否 --> L[练习结束/进入总结];
    end

    subgraph 答题中可操作
        E -- 点击勾画 --> M[进入/退出勾画模式];
        E -- 点击不确定 --> N[标记/取消标记不确定];
        E -- 长按内容/点击问一问 --> O[唤起/关闭答疑组件];
        E -- 点击退出 --> P[保存进度并退出];
    end

    L --> Q(返回课程主流程/结算页); 
    %% 练习结束后的去向

    %% 提示：此图简化了首次进入和再次进入的不同转场，以及各种反馈类型的分支。

```

## 4. 性能与安全需求

### 4.1 性能需求

- **响应时间：**
    - 练习题目加载时间（从请求到题目完全渲染可交互）不超过 **1.5** 秒 (95th percentile)。
    - 用户提交答案后，即时反馈动画的启动延迟应小于 **300** 毫秒。
    - 题目间切换（反馈结束到下一题出现）的耗时不应超过 **1** 秒。
    - 页面主要内容加载时间（LCP，指题目主体出现）应在 **2** 秒内。
    - 首次交互延迟（FID）或交互至下次绘制时间（INP）应小于 **100** 毫秒。
- **并发用户：** （同课程框架）系统需支持 **1000** 个并发用户同时在线练习，核心功能性能不显著衰减。
- **数据量：** 即使练习包含较多题目（如30+），题目间的切换和加载也应保持流畅。
- **前端性能感知：**
    - **[题目加载]**: 应使用骨架屏或轻量loading动画，避免白屏。
    - **[反馈动画]**: 即时反馈动画应流畅播放，不掉帧，动画资源应预加载或优化大小。
    - **[勾画功能]**: 勾画操作应实时响应，无明显延迟，不影响答题界面的性能。

### 4.2 安全需求

- **权限控制：** 用户只能访问其课程包含的练习题目。答题记录和错题本内容仅对用户本人可见。
- **数据加密：** 用户答案、作答时长、错题本数据等在前后端传输过程中必须使用 HTTPS。前端避免在本地存储明文答案。
- **操作审计：** 对用户提交答案、加入错题本等关键操作，后端需记录日志。前端需确保相关信息传递。
- **输入校验:** （如果练习题包含输入类型）前端需对用户输入进行基础校验（如格式、长度），防止注入，后端必须二次校验。答疑组件的输入校验遵循答疑组件规范。

## 5. 验收标准

### 5.1 功能验收

- **[核心功能点1 - 练习组件交互与反馈优化]：**
    - **用户场景：** 用户首次从文档组件进入练习。
    - **界面与交互：**
        - 触发正确的“首次进入”转场动效和文案。
        - 转场结束后正确显示第一题。
    - **期望结果：** 符合场景1描述，过渡流畅自然。
    - **用户场景：** 用户提交答案。
    - **界面与交互：**
        - 根据答案对错，触发对应的即时反馈动画和音效（正确/错误区分明显）。
        - 达到连对阈值时，触发特殊的激励反馈。
        - 反馈结束后自动加载下一题（若有）。
    - **期望结果：** 符合场景2描述，反馈及时、准确、有区分度。
    - **用户场景：** 用户在练习中需要提问。
    - **界面与交互:**
        - 可通过长按题干文字或点击右上角“问一问”按钮成功唤起答疑组件。
        - 长按提问时，选中文字能自动填充到输入框。
    - **期望结果：** 符合场景3描述，提问入口便捷有效。
    - **用户场景:** 用户再次进入未完成的练习。
    - **界面与交互:**
        - 触发正确的“再次进入”转场动效和文案。
        - 转场结束后正确恢复到上次中断的题目和计时。
    - **期望结果:** 续学流程正确。
    - **用户场景:** 用户使用勾画、不确定、错题本功能。
    - **界面与交互:**
        - 勾画功能可正常激活、使用和退出。
        - 不确定按钮可正常标记和取消标记。
        - 加入错题本按钮功能正常，状态显示正确。
    - **期望结果:** 辅助功能集成正常。

### 5.2 性能验收

- **响应时间：**
    - **测试用例1:** 多次加载不同类型的练习题，95%的情况下，题目加载时间 < 1.5秒。
    - **测试用例2:** 多次提交答案，95%的情况下，从点击提交到反馈动画开始播放的延迟 < 300ms。
    - **测试用例3:** 连续完成多道题目，题目间切换（上一题反馈结束到下一题可交互）的平均耗时 < 1秒。
- **前端性能感知验收：**
    - **测试用例1 (题目加载):** 题目加载过程中，骨架屏或loading动画展示清晰。
    - **测试用例2 (反馈动画):** 正确、错误、连对等反馈动画播放流畅，无卡顿或掉帧。
    - **测试用例3 (勾画):** 在题目上进行连续勾画操作，笔迹跟随无明显延迟。

### 5.3 安全验收

- **权限控制：**
    - **测试用例1:** 尝试访问非当前用户课程包含的练习题ID，期望结果：后端应拒绝或返回无权限。
    - **测试用例2:** 尝试获取其他用户的答题记录或错题本数据，期望结果：后端应拒绝请求。
- **数据加密：**
    - **测试用例1:** 抓包检查提交答案的网络请求，确认答案内容和用户信息在传输中已加密（HTTPS）。
    - **测试用例2:** 检查本地存储，确认无明文存储答案。

## 6. 其他需求

### 6.1 可用性与体验需求

- **界面友好与直观性：**
    - 题目展示清晰，选项易于点选。
    - 计时器、进度条、操作按钮布局合理，易于理解。
    - 反馈动画应简洁明了，不过度干扰流程。
    - 转场效果不应过于冗长或花哨，以流畅过渡为首要目标。
- **容错处理：**
    - 当 [题目加载失败] 时，应提供明确提示（如“题目加载失败，请重试”）和重试机制。
    - 当 [答案提交失败] 时，应提示用户，并允许用户重新提交，避免数据丢失。
    - 当 [用户快速重复点击提交] 时，应做防抖处理，只响应一次。
- **兼容性：** (同课程框架)
    - **浏览器：** 支持最新版本的 Chrome, Safari, Edge 及主流移动端 WebView。
    - **响应式布局：** 确保在不同尺寸移动设备上布局合理，操作便捷。
- **可访问性 (A11y)：**
    - 题目文本、选项、按钮等元素应能被屏幕阅读器正确读取。
    - 交互元素支持键盘操作（若适用）。
    - 颜色对比度符合标准。
    - 即时反馈（对错）应有非视觉提示（如声音）。
- **交互一致性：**
    - 练习组件内的按钮样式、弹窗交互应与课程框架及其他组件保持一致。
    - 勾画、答疑、错题本等复用组件的交互体验应与原组件保持一致。

### 6.2 维护性需求

- **配置管理：** [即时反馈动画类型、连对阈值、难度调整策略（若前端涉及）、提示文案] 可由 [运营/产品] 在后台配置调整。
- **监控告警：** 前端监控需覆盖 [题目加载失败率、答案提交成功率、反馈动画播放异常] 等关键指标，异常时告警。
- **日志管理：** 前端需记录 [进入/退出练习、加载题目、提交答案（含对错）、触发反馈类型、勾画/错题本/答疑操作] 等关键行为日志。

## 7. 用户反馈和迭代计划

### 7.1 用户反馈机制

- 用户可通过 [结算页反馈模块] 或 [通用反馈渠道] 提交关于练习体验的问题和建议。
- 可考虑在练习组件内部增加特定的反馈入口（如针对某题反馈）。
- 定期分析反馈数据，关注关于流畅度、反馈效果、题目难度等方面的意见。

### 7.2 迭代计划

- **迭代周期：** (同课程框架) 初定每 **2** 周为一个迭代周期。
- **迭代流程：** 标准迭代流程。
- **优先级调整：** V1.0上线后，根据数据表现（如各反馈动画的 تاثیر、用户跳出率）和用户反馈，评估后续优化点，如增加更多反馈类型、优化推题策略、引入互动讲题（见2.3）。

## 8. 需求检查清单

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 需求背景：优化作答流畅度、情感反馈 | 1. 需求背景与目标 | ✅ | ✅ | ✅ | ✅ | N/A | N/A |  |
| 方案简述：进入转场优化、题目间即时反馈、复用勾画/错题本 | 2.1 核心功能点1 / 2.2 辅助功能点1&2 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 需求概览：增加进入转场和题目间转场和反馈 | 2.1 核心功能点1 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：进入练习提示条 | 2.1 核心功能点1 (交互流程) | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：首次进入练习转场 (翻页、IP动效、文案、时长) | 2.1 核心功能点1 (交互流程, 界面元素) / 场景1 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：再次进入练习转场 (IP动效、文案、时长) | 2.1 核心功能点1 (交互流程, 界面元素) | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：练习页面框架（顶部导航、计时、进度、答题组件、勾画、错题本、答疑入口） | 2.1 核心功能点1 (界面元素) / 2.2 辅助功能 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：题目间转场/反馈（基于作答结果触发动画/音效） | 2.1 核心功能点1 (交互流程, 用户反馈) / 场景2 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 详细方案：作答进度计算逻辑 | 2.1 核心功能点1 (数据展示) | ✅ | ✅ | ✅ | ⚠️(需后端策略明确) | ✅ | N/A | 逻辑描述，前端主要展示 |
| 详细方案：连对触发效果（复用巩固练习） | 2.1 核心功能点1 (用户反馈) | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | 提及复用 |
| 详细方案：勾画组件（复用巩固练习） | 2.2 辅助功能点1 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | 提及复用 |
| 详细方案：错题本组件（复用巩固练习） | 2.2 辅助功能点2 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | 提及复用 |
| 详细方案：答疑组件入口与交互 | 2.2 辅助功能点3 / 场景3 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | 提及复用 |
| 数据需求：埋点需求 | 6.2 维护性需求 (日志) / (文档中明确列出) | ✅ | ✅ | ✅ | N/A | N/A | N/A | 已转为日志要求 |
| 数据需求：存储需求 | (后端需求) | N/A | N/A | N/A | N/A | N/A | N/A |  |
- **完整性：** 原始PRD中关于练习组件的核心需求点已覆盖。✅
- **正确性：** 优化后需求准确反映了原始需求的意图。✅
- **一致性：** 需求描述内部及与课程框架PRD的关联保持一致。✅
- **可验证性：** 主要功能点均有对应的验收标准。✅
- **可跟踪性：** 核心功能已标注优先级和依赖。✅
- **UI/UX明确性：** 通过详述、场景和图示分析，对交互和反馈进行了明确描述。✅

## 9. 附录

### 9.1 原型图/设计稿链接

- 视觉稿：待补充 (来自原始PRD)
- (原型图链接可在此处补充)

### 9.2 术语表

- **练习组件:** AI课中用于承载互动练习题目的功能模块。
- **转场动效:** 在不同界面或状态切换时播放的过渡动画效果。
- **即时反馈:** 用户完成操作（如提交答案）后系统立刻给予的响应（如动画、音效）。
- **连对:** 用户连续答对题目的次数。
- **IP:** 指课程中使用的虚拟形象或角色。
- **勾画组件:** 允许用户在界面上进行手写标记、涂鸦的功能模块。
- **错题本组件:** 收集用户做错或标记的题目，供后续复习的功能模块。
- **答疑组件:** 提供用户提问并获得解答（可能来自AI或人工）的功能模块。

### 9.3 参考资料

- [原始需求文档：PRD - AI 课中 - 练习组件 1.0] (ai-generates/raw-docs/prd/PRD - AI 课中 - 练习组件 1.0_analyzed.md)
- [关联需求文档：AI 课中 - V1.0] (链接待补充)
- [关联需求文档：题型支持 PRD] (链接待补充)
- [关联需求文档：AI课中 - 课程框架文档组件 V1.0 PRD] (ai-generates/ai-docs/ai-prd-AI课中-课程框架文档组件-V1.0.md) - (假设存在，用于参考复用组件)
import { api } from './client';
import { DocumentFile, DocumentTask, FeishuDocumentRequest, PDFDocumentRequest, DocumentProcessResponse } from '@/types/document';
import { API_BASE_URL } from '@/app/configs/api';

// 上传文档
export const uploadDocument = async (file: File): Promise<DocumentFile> => {
  const formData = new FormData();
  formData.append('file', file);
  return api.post<DocumentFile>('/api/documents/upload', formData, true);
};

// 处理PDF文件
export const processPdf = async (
  fileId: string, 
  options: { replace_images?: boolean; output_format?: string }
): Promise<DocumentProcessResponse> => {
  const formData = new URLSearchParams();
  if (options.replace_images !== undefined) {
    formData.append('replace_images', options.replace_images.toString());
  }
  if (options.output_format) {
    formData.append('output_format', options.output_format);
  }
  
  // 使用application/x-www-form-urlencoded格式提交
  return api.post<DocumentProcessResponse>(
    `/api/documents/process/pdf/${fileId}`,
    formData.toString(),
    false
  );
};

/**
 * 从飞书URL中提取文档token
 * 示例: https://wcng60ba718p.feishu.cn/wiki/XbLewDVC1iit0tk1z05c7QeBnSg
 * 返回: XbLewDVC1iit0tk1z05c7QeBnSg
 */
export const extractFeishuToken = (url: string): string => {
  try {
    // 检查是否已经是token (不包含https://)
    if (!url.startsWith('http')) {
      return url;
    }
    
    // 使用正则表达式提取最后一段路径
    const match = url.match(/([A-Za-z0-9_-]+)(?:\?.*)?$/);
    if (match && match[1]) {
      return match[1];
    }
    
    // 如果上面的方法无法提取，尝试分割URL
    const segments = url.split('/');
    return segments[segments.length - 1].split('?')[0];
  } catch (error) {
    console.error('提取飞书文档token失败:', error);
    return url; // 如果提取失败，返回原始URL
  }
};

// 处理飞书文档
export const processFeishuDocument = async (
  feishuUrl: string,
  options: { replace_images?: boolean; output_format?: string }
): Promise<DocumentProcessResponse> => {
  // 提取飞书文档token
  const token = extractFeishuToken(feishuUrl);
  
  const request: FeishuDocumentRequest = {
    url: token,
    replace_images: options.replace_images || false,
    output_format: options.output_format || 'markdown'
  };
  
  return api.post<DocumentProcessResponse>('/api/documents/process/feishu', request);
};

// 新增：处理飞书文档（使用新API，支持WebSocket实时输出）
export const processFeishuDocumentV2 = async (
  feishuUrl: string,
  options: {
    replace_images?: boolean;
    app_id?: string;
    app_secret?: string;
    flush?: boolean;
    workflow_params?: any;
    form_data?: any;
  }
): Promise<{ task_id: string; status: string; message: string }> => {
  // 构建请求数据，不需要提取token，后端会处理
  const request = {
    url: feishuUrl,
    replace_images: options.replace_images || false,
    app_id: options.app_id,
    app_secret: options.app_secret,
    flush: options.flush || false, // 添加flush参数，默认为false
    workflow_params: options.workflow_params,
    form_data: options.form_data
  };
  
  return api.post<{ task_id: string; status: string; message: string }>('/api/feishu/format-prd', request);
};

// 新增：获取飞书文档处理任务状态
export const getFeishuTaskStatus = async (taskId: string): Promise<{
  task_id: string;
  status: string;
  message: string;
  download_url?: string;
  output_path?: string;
}> => {
  return api.get<{
    task_id: string;
    status: string;
    message: string;
    download_url?: string;
    output_path?: string;
  }>(`/api/feishu/tasks/${taskId}`);
};

// 新增：创建WebSocket连接
export const createWebSocketConnection = (taskId: string): WebSocket => {
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = window.location.host;
  const wsUrl = `${protocol}//${host}/api/ws/${taskId}`;
  
  return new WebSocket(wsUrl);
};

// 获取任务状态
export const getTaskStatus = async (taskId: string): Promise<DocumentTask> => {
  console.log('获取任务状态:', taskId);
  return api.get<DocumentTask>(`/api/documents/tasks/${taskId}`);
};

// 智能获取任务状态 - 自动尝试不同的API端点
export const getTaskStatusSmart = async (taskId: string): Promise<DocumentTask> => {
  console.log('智能获取任务状态:', taskId);
  
  // 定义要尝试的API端点列表
  const endpoints = [
    `/api/documents/tasks/${taskId}`,  // 标准文档API
    `/api/feishu/tasks/${taskId}`,     // 飞书API
  ];
  
  let lastError;
  
  for (const endpoint of endpoints) {
    try {
      console.log(`尝试API端点: ${endpoint}`);
      const result = await api.get<DocumentTask>(endpoint);
      console.log(`成功获取任务状态 (${endpoint}):`, result);
      return result;
    } catch (error) {
      console.log(`API端点失败 (${endpoint}):`, error);
      lastError = error;
      // 继续尝试下一个端点
    }
  }
  
  // 所有端点都失败，抛出最后一个错误
  console.error('所有API端点都失败，任务状态获取失败:', lastError);
  throw lastError;
};

// 获取所有任务
export const getAllTasks = async (): Promise<DocumentTask[]> => {
  return api.get<DocumentTask[]>('/api/documents/tasks');
};

// 下载处理后的文件
export const downloadProcessedFile = (fileId: string): string => {
  // 返回下载链接
  // 使用相对路径，确保通过Next.js的API代理
  return `/api/documents/download/${fileId}`;
};

// 处理AI-PRD
export const processAiPrd = async (
  request: {
    url: string;
    replace_images?: boolean;
    output_format?: string;
    template_id?: string;
    type?: string; // 添加 type 参数，用于指定前端(fe)或后端(be)
    workflow_params?: any; // 工作流参数
    form_data?: any; // 表单数据
  }
): Promise<{ task_id: string; status: string; message: string }> => {
  console.log('处理AI-PRD请求参数:', request);
  
  // 使用 FormData 对象，确保参数正确编码
  const formData = new FormData();
  
  // 直接使用原始URL，不进行额外编码
  formData.append('url', request.url);
  formData.append('type', request.type || 'fe'); // 默认为前端
  
  if (request.replace_images !== undefined) {
    formData.append('replace_images', request.replace_images.toString());
  }
  
  if (request.output_format) {
    formData.append('output_format', request.output_format);
  }
  
  if (request.template_id) {
    formData.append('template_id', request.template_id);
  }
  
  // 添加工作流参数
  if (request.workflow_params) {
    formData.append('workflow_params', JSON.stringify(request.workflow_params));
  }
  
  // 添加表单数据
  if (request.form_data) {
    formData.append('form_data', JSON.stringify(request.form_data));
  }
  
  try {
    // 使用 POST 请求，将参数放在表单中，避免URL编码问题
    // 使用以/api开头的endpoint，确保通过Next.js的API代理发送
    const result = await api.post<{ task_id: string; status: string; message: string }>(
      '/api/documents/process/ai-prd',
      formData,
      true // 指定为表单数据
    );
    
    console.log('处理AI-PRD响应结果:', result);
    return result;
  } catch (error) {
    console.error('处理AI-PRD请求失败:', error);
    throw error;
  }
};

// 上传并处理AI-PRD
export const uploadAndProcessAiPrd = async (formData: FormData): Promise<{ task_id: string; status: string; message: string }> => {
  return api.post<{ task_id: string; status: string; message: string }>('/api/documents/process/ai-prd', formData, true);
};

// 处理AI-TD
export const processAiTd = async (
  request: {
    url: string;
    replace_images?: boolean;
    output_format?: string;
    template_id?: string;
    type?: string; // 添加 type 参数，用于指定前端(fe)或后端(be)
    td_type?: string; // 兼容旧版本的 td_type 参数
    workflow_params?: any; // 工作流参数
    form_data?: any; // 表单数据
  }
): Promise<{ task_id: string; status: string; message: string }> => {
  console.log('处理AI-TD请求参数:', request);
  
  // 使用 FormData 对象，确保参数正确编码
  const formData = new FormData();
  
  // 直接使用原始URL，不进行额外编码
  formData.append('url', request.url);
  
  // 优先使用 type 参数，如果没有则尝试使用 td_type 参数，默认为前端
  formData.append('type', request.type || request.td_type || 'fe');
  
  if (request.replace_images !== undefined) {
    formData.append('replace_images', request.replace_images.toString());
  }
  
  if (request.output_format) {
    formData.append('output_format', request.output_format);
  }
  
  if (request.template_id) {
    formData.append('template_id', request.template_id);
  }
  
  // 添加工作流参数和表单数据
  if (request.workflow_params) {
    formData.append('workflow_params', JSON.stringify(request.workflow_params));
  }
  if (request.form_data) {
    formData.append('form_data', JSON.stringify(request.form_data));
  }
  
  try {
    // 使用 POST 请求，将参数放在表单中，避免URL编码问题
    // 使用以/api开头的endpoint，确保通过Next.js的API代理发送
    const result = await api.post<{ task_id: string; status: string; message: string }>(
      '/api/documents/process/ai-td',
      formData,
      true // 指定为表单数据
    );
    
    console.log('处理AI-TD响应结果:', result);
    return result;
  } catch (error) {
    console.error('处理AI-TD请求失败:', error);
    throw error;
  }
};

// 上传并处理AI-TD
export const uploadAndProcessAiTd = async (formData: FormData): Promise<{ task_id: string; status: string; message: string }> => {
  return api.post<{ task_id: string; status: string; message: string }>('/api/documents/process/ai-td', formData, true);
};

// 处理AI-DD
export const processAiDd = async (
  request: {
    url: string;
    replace_images?: boolean;
    output_format?: string;
    template_id?: string;
    type?: string; // 添加 type 参数，用于指定前端(fe)或后端(be)
    workflow_params?: any; // 工作流参数
    form_data?: any; // 表单数据
  }
): Promise<{ task_id: string; status: string; message: string }> => {
  console.log('处理AI-DD请求参数:', request);
  
  // 使用 FormData 对象，确保参数正确编码
  const formData = new FormData();
  
  // 直接使用原始URL，不进行额外编码
  formData.append('url', request.url);
  formData.append('type', request.type || 'be'); // 默认为后端
  
  if (request.replace_images !== undefined) {
    formData.append('replace_images', request.replace_images.toString());
  }
  
  if (request.output_format) {
    formData.append('output_format', request.output_format);
  }
  
  if (request.template_id) {
    formData.append('template_id', request.template_id);
  }
  
  // 添加工作流参数和表单数据
  if (request.workflow_params) {
    formData.append('workflow_params', JSON.stringify(request.workflow_params));
  }
  if (request.form_data) {
    formData.append('form_data', JSON.stringify(request.form_data));
  }
  
  try {
    // 使用 POST 请求，将参数放在表单中，避免URL编码问题
    // 使用以/api开头的endpoint，确保通过Next.js的API代理发送
    const result = await api.post<{ task_id: string; status: string; message: string }>(
      '/api/documents/process/ai-dd',
      formData,
      true // 指定为表单数据
    );
    
    console.log('处理AI-DD响应结果:', result);
    return result;
  } catch (error) {
    console.error('处理AI-DD请求失败:', error);
    throw error;
  }
};

// 上传并处理AI-DD
export const uploadAndProcessAiDd = async (formData: FormData): Promise<{ task_id: string; status: string; message: string }> => {
  return api.post<{ task_id: string; status: string; message: string }>('/api/documents/process/ai-dd', formData, true);
};

// 处理AI-AD
export const processAiAd = async (
  request: {
    url: string;
    replace_images?: boolean;
    output_format?: string;
    template_id?: string;
    type?: string; // 添加 type 参数，用于指定前端(fe)或后端(be)
    workflow_params?: any; // 工作流参数
    form_data?: any; // 表单数据
  }
): Promise<{ task_id: string; status: string; message: string }> => {
  console.log('处理AI-AD请求参数:', request);
  
  // 使用 FormData 对象，确保参数正确编码
  const formData = new FormData();
  
  // 直接使用原始URL，不进行额外编码
  formData.append('url', request.url);
  formData.append('type', request.type || 'be'); // 默认为后端
  
  if (request.replace_images !== undefined) {
    formData.append('replace_images', request.replace_images.toString());
  }
  
  if (request.output_format) {
    formData.append('output_format', request.output_format);
  }
  
  if (request.template_id) {
    formData.append('template_id', request.template_id);
  }
  
  // 添加工作流参数和表单数据
  if (request.workflow_params) {
    formData.append('workflow_params', JSON.stringify(request.workflow_params));
  }
  if (request.form_data) {
    formData.append('form_data', JSON.stringify(request.form_data));
  }
  
  try {
    // 使用 POST 请求，将参数放在表单中，避免URL编码问题
    // 使用以/api开头的endpoint，确保通过Next.js的API代理发送
    const result = await api.post<{ task_id: string; status: string; message: string }>(
      '/api/documents/process/ai-ad',
      formData,
      true // 指定为表单数据
    );
    
    console.log('处理AI-AD响应结果:', result);
    return result;
  } catch (error) {
    console.error('处理AI-AD请求失败:', error);
    throw error;
  }
};

// 上传并处理AI-AD
export const uploadAndProcessAiAd = async (formData: FormData): Promise<{ task_id: string; status: string; message: string }> => {
  return api.post<{ task_id: string; status: string; message: string }>('/api/documents/process/ai-ad', formData, true);
};

// 处理AI-DE
export const processAiDe = async (
  request: {
    url: string;
    replace_images?: boolean;
    output_format?: string;
    template_id?: string;
    type?: string; // 添加 type 参数，用于指定前端(fe)或后端(be)
    workflow_params?: any; // 工作流参数
    form_data?: any; // 表单数据
  }
): Promise<{ task_id: string; status: string; message: string }> => {
  console.log('处理AI-DE请求参数:', request);
  
  // 使用 FormData 对象，确保参数正确编码
  const formData = new FormData();
  
  // 直接使用原始URL，不进行额外编码
  formData.append('url', request.url);
  formData.append('type', request.type || 'be'); // 默认为后端
  
  if (request.replace_images !== undefined) {
    formData.append('replace_images', request.replace_images.toString());
  }
  
  if (request.output_format) {
    formData.append('output_format', request.output_format);
  }
  
  if (request.template_id) {
    formData.append('template_id', request.template_id);
  }
  
  // 添加工作流参数和表单数据
  if (request.workflow_params) {
    formData.append('workflow_params', JSON.stringify(request.workflow_params));
  }
  if (request.form_data) {
    formData.append('form_data', JSON.stringify(request.form_data));
  }
  
  try {
    // 使用 POST 请求，将参数放在表单中，避免URL编码问题
    // 使用以/api开头的endpoint，确保通过Next.js的API代理发送
    const result = await api.post<{ task_id: string; status: string; message: string }>(
      '/api/documents/process/ai-de',
      formData,
      true // 指定为表单数据
    );
    
    console.log('处理AI-DE响应结果:', result);
    return result;
  } catch (error) {
    console.error('处理AI-DE请求失败:', error);
    throw error;
  }
};

// 上传并处理AI-DE
export const uploadAndProcessAiDe = async (formData: FormData): Promise<{ task_id: string; status: string; message: string }> => {
  return api.post<{ task_id: string; status: string; message: string }>('/api/documents/process/ai-de', formData, true);
};

// 处理AI-API
export const processAiApi = async (
  request: {
    url: string;
    replace_images?: boolean;
    output_format?: string;
    template_id?: string;
    type?: string; // 添加 type 参数，用于指定前端(fe)或后端(be)
    workflow_params?: any; // 工作流参数
    form_data?: any; // 表单数据
  }
): Promise<{ task_id: string; status: string; message: string }> => {
  console.log('处理AI-API请求参数:', request);
  
  // 使用 FormData 对象，确保参数正确编码
  const formData = new FormData();
  
  // 直接使用原始URL，不进行额外编码
  formData.append('url', request.url);
  formData.append('type', request.type || 'be'); // 默认为后端
  
  if (request.replace_images !== undefined) {
    formData.append('replace_images', request.replace_images.toString());
  }
  
  if (request.output_format) {
    formData.append('output_format', request.output_format);
  }
  
  if (request.template_id) {
    formData.append('template_id', request.template_id);
  }
  
  // 添加工作流参数和表单数据
  if (request.workflow_params) {
    formData.append('workflow_params', JSON.stringify(request.workflow_params));
  }
  if (request.form_data) {
    formData.append('form_data', JSON.stringify(request.form_data));
  }
  
  try {
    // 使用 POST 请求，将参数放在表单中，避免URL编码问题
    // 使用以/api开头的endpoint，确保通过Next.js的API代理发送
    const result = await api.post<{ task_id: string; status: string; message: string }>(
      '/api/documents/process/ai-api',
      formData,
      true // 指定为表单数据
    );
    
    console.log('处理AI-API响应结果:', result);
    return result;
  } catch (error) {
    console.error('处理AI-API请求失败:', error);
    throw error;
  }
};

// 上传并处理AI-API
export const uploadAndProcessAiApi = async (formData: FormData): Promise<{ task_id: string; status: string; message: string }> => {
  return api.post<{ task_id: string; status: string; message: string }>('/api/documents/process/ai-api', formData, true);
};

// 获取AI-Done文档列表
export const getAiDoneDocuments = async (
  request: {
    url?: string;
    feishu_url?: string;
    feishu_token?: string;
    type: string; // 必填参数
  }
): Promise<{
  status: string;
  message: string;
  feishu_token: string;
  dev_type: string;
  target_url: string;
  summary: {
    total_documents: number;
    existing_documents: number;
    missing_documents: number;
    completion_rate: string;
  };
  documents: Array<{
    doc_type: string;
    filename: string;
    exists: boolean;
    download_url: string | null;
    file_size: number;
    full_url: string | null;
  }>;
}> => {
  console.log('获取AI-Done文档列表请求参数:', request);
  
  try {
    // 构建查询参数
    const params = new URLSearchParams();
    if (request.url) {
      params.append('url', request.url);
    }
    if (request.feishu_url) {
      params.append('feishu_url', request.feishu_url);
    }
    if (request.feishu_token) {
      params.append('feishu_token', request.feishu_token);
    }
    // type参数是必填的
    params.append('type', request.type);
    
    const result = await api.get<{
      status: string;
      message: string;
      feishu_token: string;
      dev_type: string;
      target_url: string;
      summary: {
        total_documents: number;
        existing_documents: number;
        missing_documents: number;
        completion_rate: string;
      };
      documents: Array<{
        doc_type: string;
        filename: string;
        exists: boolean;
        download_url: string | null;
        file_size: number;
        full_url: string | null;
      }>;
    }>(`/api/documents/process/ai-done?${params.toString()}`);
    
    console.log('获取AI-Done文档列表响应结果:', result);
    return result;
  } catch (error) {
    console.error('获取AI-Done文档列表请求失败:', error);
    throw error;
  }
};

// 批量下载AI-Done文档
export const batchDownloadAiDoneDocuments = async (
  request: {
    url?: string;
    feishu_url?: string;
    feishu_token?: string;
    type: string; // 必填参数
  }
): Promise<Blob> => {
  console.log('批量下载AI-Done文档请求参数:', request);
  
  try {
    // 使用 FormData 对象，确保参数正确编码
    const formData = new FormData();
    
    if (request.url) {
      formData.append('url', request.url);
    }
    if (request.feishu_url) {
      formData.append('feishu_url', request.feishu_url);
    }
    if (request.feishu_token) {
      formData.append('feishu_token', request.feishu_token);
    }
    // type参数是必填的
    formData.append('type', request.type);
    
    // 发送POST请求获取ZIP文件
    const response = await fetch('/api/documents/process/ai-done/batch-download', {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`批量下载失败: ${response.status} ${errorText}`);
    }
    
    // 返回Blob对象
    const blob = await response.blob();
    console.log('批量下载AI-Done文档成功，文件大小:', blob.size);
    return blob;
  } catch (error) {
    console.error('批量下载AI-Done文档请求失败:', error);
    throw error;
  }
};

export const documentApi = {
  uploadDocument,
  processPdf,
  processFeishuDocument,
  processFeishuDocumentV2,
  getFeishuTaskStatus,
  createWebSocketConnection,
  extractFeishuToken,
  getTaskStatus,
  getTaskStatusSmart,
  getAllTasks,
  downloadProcessedFile,
  processAiPrd,
  uploadAndProcessAiPrd,
  processAiTd,
  uploadAndProcessAiTd,
  processAiDe,
  uploadAndProcessAiDe,
  processAiDd,
  uploadAndProcessAiDd,
  processAiAd,
  uploadAndProcessAiAd,
  processAiApi,
  uploadAndProcessAiApi,
  getAiDoneDocuments,
  batchDownloadAiDoneDocuments,
};
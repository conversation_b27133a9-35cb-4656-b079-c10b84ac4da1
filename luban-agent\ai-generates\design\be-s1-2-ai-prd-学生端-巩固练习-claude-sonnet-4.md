# 产品需求文档 - 学生端巩固练习 v1.0

## 1. 需求背景与目标

### 1.1 需求目标
- 通过巩固练习功能的实现，可以提升学生在AI课后的知识掌握程度和学习效果
- 该需求上线后，学生练习完成率预计可以提升30%，知识点掌握度预计可以达到85%以上
- 通过再练一次机制，确保每个学生都能达到掌握知识的目标，提升整体学习效果

### 1.2 需求背景
巩固练习是产品的核心学习场景之一，是在AI学习后进行的巩固作用的练习，和AI课知识点关联度非常高。调研发现原练习存在缺乏有效的情感反馈、激励机制不足等问题，需要整体优化学习体验，提升学习效果。

### 1.3 覆盖用户
初中、高中学生

## 2. 功能范围

### 2.1 核心功能
- **巩固练习入口管理**: 提供清晰的练习状态展示和智能入口变化，让学生能够快速识别练习状态并进入相应的练习环节。支持未完成状态下的"进入练习"/"继续练习"和已完成状态下的"看报告"/"再练一次"操作，解决学生不知道从哪里开始练习的痛点。
  - 优先级：高
  - 依赖关系：无

- **练习环节执行**: 提供完整的答题体验，包括题目展示、多题型作答支持、实时进度管理和精确计时功能。支持题组转场、勾画标记、错题收藏等辅助功能，确保学生能够专注于学习内容本身，解决练习流程不流畅和缺乏辅助工具的痛点。
  - 优先级：高
  - 依赖关系：依赖于巩固练习入口管理

- **题目间转场反馈**: 提供丰富的情感化反馈机制，包括正确/错误作答反馈、连胜激励、难度调整提示和不认真作答提醒。通过IP角色动效和个性化文案，及时给予学生正向激励或温和引导，解决缺乏激励机制和情感反馈的痛点。
  - 优先级：高
  - 依赖关系：依赖于练习环节执行

- **学习报告生成**: 生成详细的学习成果报告，展示掌握度变化、答题统计、学习时长等关键数据，并提供题目详情查看功能。让学生清晰了解自己的学习效果和进步情况，为后续学习提供明确方向，解决学习成果不可见的痛点。
  - 优先级：高
  - 依赖关系：依赖于练习环节执行

### 2.2 辅助功能
- **勾画组件**: 提供多色彩、多粗细的勾画工具，支持橡皮擦、撤回恢复、一键清空等功能，为学生在答题过程中进行思路标记和重点标注提供支持
- **错题本组件**: 支持答错题目自动添加和正确题目手动添加到错题本，提供错因标签分类功能，为学生的错题管理和后续复习提供支持
- **再练一次环节**: 基于学生掌握度和正确率的智能判断，推荐针对性的重复练习，确保学生达到知识掌握目标，为学习效果保障提供支持

### 2.3 非本期功能
- **完整积分系统**: 当前仅展示积分获得数量，完整的积分兑换、使用和奖励机制建议迭代2再考虑
- **社交功能扩展**: 小组排名的详细展示、好友互动、学习动态分享等功能，建议迭代3再考虑

## 3. 计算规则与公式

### 3.1 掌握度计算规则
- **掌握度提升公式**: 掌握度变化 = f（答题正确率, 题目难度, 完成时间）
  - 参数说明：
    - 答题正确率: 本次练习中答对题目数/总题目数
    - 题目难度: 题目的难度系数（1-5级）
    - 完成时间: 相对于标准时间的比值
  - 规则：
    - 取值范围：掌握度为0-100%
    - 正确率越高、难度越大、用时合理，掌握度提升越明显

### 3.2 进度计算规则
- **进度计算公式**: 进度 = 已完成有效题目数 / 预估总题目数
  - 参数说明：
    - 已完成有效题目数: 根据推题策略确定的有效完成数
    - 预估总题目数: 基于推题策略的预估值
  - 规则：
    - 学生答对题目时，进度增加
    - 学生答错但没有相似题时，进度增加
    - 学生答错且有相似题需要重练时，进度不变

### 3.3 积分计算规则
- **积分获得公式**: 积分 = 基础分 × 难度系数 × 连胜加成
  - 参数说明：
    - 基础分: 每题的基础积分值
    - 难度系数: 题目难度对应的系数（1.0-2.0）
    - 连胜加成: 连续答对的加成倍数（1.0-1.5）
  - 规则：
    - 取值范围：单题积分1-50分
    - 连胜2题以上开始有加成效果

### 3.4 再练一次触发规则
- **触发条件**: 掌握度 < 80% 或 正确率 < 70%
  - 参数说明：
    - 掌握度阈值: 80%（可配置）
    - 正确率阈值: 70%（可配置）
  - 规则：
    - 满足任一条件即触发再练一次推荐
    - 再练一次内容基于薄弱知识点生成

## 4. 用户场景与故事

### 场景1: 学生首次进入巩固练习
作为一个初中学生，我希望能够在完成AI课程后立即进行巩固练习，这样我就可以及时检验和巩固刚学到的知识点。目前的痛点是不知道从哪里开始练习，如果能够有清晰的练习入口和引导，对我的学习会有很大帮助。

**关键步骤：**
1. 在学科页面的学习路径中找到巩固练习卡片
2. 查看练习状态和预估题目数量（约8题）
3. 点击"进入练习"按钮展开练习卡片
4. 观看"开始练习：《课程名称》"转场动画（2秒）
5. 如有题组则观看题组转场，了解当前题组信息
6. 开始第一道题目的作答，查看题目内容和选项
7. 使用勾画工具进行标记（可选）
8. 提交答案并查看作答反馈
9. 根据反馈继续下一题或查看解析

- 优先级：高
- 依赖关系：无

```mermaid
sequenceDiagram
    participant S as 学生
    participant UI as 学习界面
    participant PS as 练习服务
    participant CS as 课程服务
    
    S->>UI: 进入学科页面
    UI->>CS: 获取课程学习状态
    CS-->>UI: 返回课程完成状态
    UI->>PS: 获取巩固练习状态
    PS-->>UI: 返回练习状态（未开始）
    UI-->>S: 展示巩固练习卡片（约8题）
    
    S->>UI: 点击"进入练习"
    UI->>PS: 开始练习请求
    PS-->>UI: 返回练习配置
    UI-->>S: 展示转场动画（开始练习）
    
    alt 存在题组
        UI-->>S: 展示题组转场
    end
    
    UI->>PS: 获取第一题
    PS-->>UI: 返回题目内容
    UI-->>S: 展示题目和作答界面
    
    S->>UI: 提交答案
    UI->>PS: 提交作答结果
    PS-->>UI: 返回批改结果和反馈
    UI-->>S: 展示作答反馈动画
```

### 场景2: 学生中途退出后继续练习
作为一个高中学生，我希望能够在中途退出练习后还能继续之前的进度，这样我就可以灵活安排学习时间而不用担心进度丢失。目前的痛点是担心退出后要重新开始，如果能够保存进度并支持继续练习，对我的学习安排会有很大帮助。

**关键步骤：**
1. 在练习过程中点击顶部导航栏的退出按钮
2. 系统弹出确认弹窗："确定要退出练习吗？进度已保存"
3. 选择"确认退出"，系统自动保存当前进度和最后作答题目ID
4. 返回到课程详情页，练习状态更新为"练习中"
5. 稍后重新进入学科页面，看到巩固练习卡片显示"继续练习"
6. 点击"继续练习"按钮
7. 观看"继续练习：《课程名称》"转场动画（2秒）
8. 直接进入上次退出的题目，继续作答
9. 计时器从上次暂停的时间继续累计

- 优先级：中
- 依赖关系：依赖于场景1

```mermaid
flowchart TD
    A[学生正在练习] --> B[点击退出按钮]
    B --> C[弹出确认弹窗]
    C --> D{选择操作}
    D -->|确认退出| E[保存进度和题目ID]
    D -->|继续练习| F[关闭弹窗继续]
    E --> G[更新练习状态为'练习中']
    E --> H[返回课程详情页]
    
    I[学生重新进入] --> J[显示'继续练习'状态]
    J --> K[点击继续练习]
    K --> L[播放继续练习转场]
    L --> M[恢复到上次题目]
    M --> N[继续计时和作答]
    
    F --> N
```

### 场景3: 学生查看学习报告和再练一次
作为一个学生，我希望能够在完成练习后看到详细的学习报告，这样我就可以了解自己的学习效果和需要改进的地方。目前的痛点是不知道自己哪些知识点掌握得不好，如果能够有清晰的报告和针对性的再练建议，对我的学习提升会有很大帮助。

**关键步骤：**
1. 完成所有练习题目，提交最后一题答案
2. 系统自动跳转到学习报告页面
3. 查看IP角色反馈（根据正确率显示不同角色和文案）
4. 查看本次掌握度变化的动效展示
5. 了解学习时长、答题数量、正确率等统计数据
6. 查看本次获得的积分数量
7. 点击题目列表查看每题的作答结果
8. 点击"查看详情"了解具体题目、答案和解析
9. 根据系统推荐，点击"再练一次"按钮（如果掌握度不足）
10. 或点击"完成"按钮结束本次练习

- 优先级：中
- 依赖关系：依赖于场景1

```mermaid
stateDiagram-v2
    [*] --> 练习完成
    练习完成 --> 生成报告: 提交最后一题
    生成报告 --> 展示报告: 计算统计数据
    
    state 展示报告 {
        [*] --> IP反馈
        IP反馈 --> 掌握度动效
        掌握度动效 --> 统计数据
        统计数据 --> 题目详情
    }
    
    展示报告 --> 判断推荐: 查看完整报告
    
    state 判断推荐 {
        [*] --> 检查掌握度
        检查掌握度 --> 推荐再练: 掌握度<80%
        检查掌握度 --> 完成练习: 掌握度>=80%
    }
    
    推荐再练 --> 再练一次: 点击再练一次
    完成练习 --> [*]: 点击完成
    再练一次 --> 练习完成: 开始新的练习轮次
```

## 5. 业务流程图

```mermaid
flowchart TD
    A[开始练习] --> B{练习状态检查}
    B -->|首次进入| C[进入练习转场]
    B -->|继续练习| D[继续练习转场]
    C --> E{是否有题组}
    D --> E
    E -->|有| F[题组转场]
    E -->|无| G[当前题目]
    F --> G
    
    G --> H[用户作答]
    H --> I[提交答案]
    I --> J{作答结果判断}
    
    J -->|正确| K[正确反馈]
    J -->|错误| L[错误反馈]
    J -->|部分正确| M[部分正确反馈]
    
    K --> N{连胜检查}
    N -->|是连胜| O[连胜反馈]
    N -->|非连胜| P[难度调整检查]
    O --> P
    L --> P
    M --> P
    
    P --> Q{难度是否调整}
    Q -->|上升| R[难度上升反馈]
    Q -->|下降| S[难度下降反馈]
    Q -->|不变| T[不认真作答检查]
    R --> T
    S --> T
    
    T --> U{是否最后一题}
    U -->|否| G
    U -->|是| V[生成学习报告]
    
    V --> W[展示报告页面]
    W --> X{用户选择}
    X -->|再练一次| Y[开始再练环节]
    X -->|完成| Z[结束练习]
    Y --> C
    
    %% 中途退出流程
    G --> AA[中途退出]
    H --> AA
    AA --> BB[保存进度]
    BB --> CC[返回课程页]
    CC --> DD[继续练习入口]
    DD --> D
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：题目加载、作答提交、转场动画的P95响应时间应不超过2秒
- 并发用户数：系统需能支持至少1000名学生同时在线进行巩固练习，且性能无明显下降
- 数据处理能力：系统需能处理每秒500次作答提交请求
- 数据量：系统需支持千万级学生练习数据的存储和查询，关键查询场景（如学习报告生成）响应时间应在3秒内

### 6.2 安全需求
- 用户认证与授权：所有练习相关接口必须经过学生身份认证和权限校验，确保学生只能访问自己的练习数据
- 数据保护：学生的练习记录、答题数据、学习报告等敏感信息在存储和传输过程中必须加密
- 操作审计：对学生的关键学习操作（如练习开始、完成、退出）必须记录详细的操作日志，包含学生ID、操作时间、练习内容等关键信息
- 防护要求：系统应具备对恶意刷题、异常提交等行为的检测和防护能力

## 7. 验收标准

### 7.1 功能验收
- **巩固练习入口管理**：
  - 使用场景：当学生完成AI课程后在学科页面查看巩固练习时
  - 期望结果：系统应正确展示练习状态（未完成/已完成），显示预估题目数量，提供正确的操作按钮（进入练习/继续练习/看报告/再练一次）

- **练习环节执行**：
  - 使用场景：当学生在练习过程中进行答题操作时
  - 期望结果：系统应正确展示题目内容，支持各种题型的作答，准确记录答题时间，实时更新进度条

- **题目间转场反馈**：
  - 使用场景：当学生完成一道题目后
  - 期望结果：系统应根据作答结果展示相应的反馈动画和文案，在连胜或难度变化时给出特殊提示

- **学习报告生成**：
  - 使用场景：当学生完成所有练习题目后
  - 期望结果：系统应生成包含掌握度变化、答题统计、错题分析的完整报告，并提供查看详情功能

### 7.2 性能验收
- 响应时间：
  - 测试场景：模拟100个学生同时进行练习，包括题目加载、作答提交、报告生成等操作
  - 验收标准：P95响应时间不超过2秒

- 并发用户数：
  - 测试场景：逐步增加并发学生数至1000人，持续进行练习操作30分钟
  - 验收标准：系统保持稳定运行，无错误率飙升，核心操作平均响应时间不超过3秒

### 7.3 安全验收
- 用户认证与授权：
  - 测试场景：未登录学生尝试访问练习功能
  - 验收标准：系统应拒绝访问，并跳转到登录页面
  - 测试场景：已登录学生访问自己的练习数据
  - 验收标准：学生可以正常访问和操作自己的练习内容

- 数据保护：
  - 验证点：通过数据库检查和网络抓包验证敏感数据加密情况
  - 验收标准：学生练习数据在存储和传输层面符合加密标准

- 操作审计：
  - 测试场景：学生进行练习开始、答题、完成等操作
  - 验收标准：所有关键操作被准确记录，包含学生ID、时间戳、操作内容等必要字段

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：练习功能的操作界面应简洁直观，学生在无额外培训的情况下，3步内可完成主要练习操作
- 容错处理：当发生网络中断或误操作时，系统应能给出清晰的错误提示和引导，自动保存学生进度，避免数据丢失
- 兼容性：练习功能需在主流移动设备和浏览器得到良好支持，包括iOS最新版、Android主流版本，保证界面和功能正常

### 8.2 维护性需求
- 配置管理需求：关键练习参数（如题目数量阈值、掌握度计算权重、积分奖励规则）应支持运营人员通过管理后台进行调整，调整后可实时生效
- 监控与告警需求：练习核心流程（如练习完成率异常下降、系统响应时间超标、题目加载失败率突增）发生异常时，系统应触发告警通知技术和产品团队
- 日志需求：学生关键学习操作（如练习开始、题目作答、练习完成、错题收藏）必须被记录，日志应包含学生ID、操作时间、练习内容、设备信息等上下文，支持学习行为分析和问题排查

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 产品应提供至少2种用户反馈渠道：应用内反馈入口、客服反馈系统
- 应有机制每周收集、整理和分析来自各渠道的学生反馈，识别练习功能的问题和改进机会

### 9.2 迭代计划（高阶产品方向）
- 产品计划以每双周为周期进行迭代，持续优化练习体验和学习效果
- 下一个迭代重点可能包括：积分系统完善、社交功能增强、个性化练习推荐算法优化

## 10. 需求检查清单

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| 巩固练习入口状态展示和交互 | 2.1核心功能-巩固练习入口管理 | ✅ | ✅ | ✅ | ✅ | ✅ | 包含未完成/已完成状态 |
| 进入练习转场设计 | 4.用户场景-场景1关键步骤 | ✅ | ✅ | ✅ | ✅ | ✅ | 首次进入和继续练习转场 |
| 练习环节页面框架和作答功能 | 2.1核心功能-练习环节执行 | ✅ | ✅ | ✅ | ✅ | ✅ | 包含题型组件、计时、进度 |
| 题目间转场反馈机制 | 2.1核心功能-题目间转场反馈 | ✅ | ✅ | ✅ | ✅ | ✅ | 正确/错误/连胜/难度反馈 |
| 学习报告展示和交互 | 2.1核心功能-学习报告生成 | ✅ | ✅ | ✅ | ✅ | ✅ | 掌握度变化、答题详情 |
| 再练一次环节 | 2.2辅助功能-再练一次环节 | ✅ | ✅ | ✅ | ✅ | ✅ | 基于掌握度判断触发 |
| 勾画组件功能 | 2.2辅助功能-勾画组件 | ✅ | ✅ | ✅ | ✅ | ✅ | 颜色、粗细、橡皮擦等 |
| 错题本组件功能 | 2.2辅助功能-错题本组件 | ✅ | ✅ | ✅ | ✅ | ✅ | 自动添加和手动添加 |
| 中途退出和继续练习 | 4.用户场景-场景2 | ✅ | ✅ | ✅ | ✅ | ✅ | 进度保存和恢复机制 |
| 题组转场机制 | 5.业务流程图-题组转场节点 | ✅ | ✅ | ✅ | ✅ | ✅ | 多题组时的转场展示 |
| 作答计时功能 | 2.1核心功能-练习环节执行 | ✅ | ✅ | ✅ | ✅ | ✅ | 每题独立计时 |
| 进度展示逻辑 | 3.2进度计算规则 | ✅ | ✅ | ✅ | ✅ | ✅ | 基于推题策略的动态计算 |
| 不认真作答反馈 | 2.1核心功能-题目间转场反馈 | ✅ | ✅ | ✅ | ✅ | ✅ | 轻提示不遮挡题目 |
| 难度变化反馈 | 2.1核心功能-题目间转场反馈 | ✅ | ✅ | ✅ | ✅ | ✅ | 全屏展示优先级高 |
| 连胜反馈机制 | 2.1核心功能-题目间转场反馈 | ✅ | ✅ | ✅ | ✅ | ✅ | 连续答对的激励反馈 |
| 小组排名反馈 | 2.3非本期功能-社交功能扩展 | ✅ | ✅ | ✅ | N/A | ✅ | 建议后续迭代实现 |
| 熔断机制 | 6.2安全需求-防护要求 | ✅ | ✅ | ✅ | ✅ | ✅ | 防止异常行为的保护机制 |
| 积分获得和展示 | 3.3积分计算规则 | ✅ | ✅ | ✅ | ✅ | ✅ | 基础分×难度×连胜加成 |
| 掌握度计算和展示 | 3.1掌握度计算规则 | ✅ | ✅ | ✅ | ✅ | ✅ | 基于正确率、难度、时间综合计算 |
| 推荐学习模块 | 4.用户场景-场景3关键步骤 | ✅ | ✅ | ✅ | ✅ | ✅ | 基于掌握度的智能推荐 |

## 11. 附录

### 11.1 原型图
- 练习入口原型图：展示未完成/已完成状态的巩固练习卡片设计
- 练习环节原型图：包含题目展示、作答界面、勾画工具、进度条等完整答题界面
- 转场反馈原型图：各种反馈动画的设计稿，包括IP角色动效和文案展示
- 学习报告原型图：报告页面的数据展示和交互设计

### 11.2 术语表
- **巩固练习**: 在AI课程学习后进行的知识巩固练习，与课程知识点高度关联，帮助学生从"知道"转变为"会用"
- **掌握度**: 学生对特定知识点的掌握程度，通过答题正确率、题目难度、完成时间等因素综合计算得出
- **再练一次**: 基于学生掌握度和正确率判断，推荐进行的额外练习环节，确保学生达到知识掌握目标
- **题组**: 按知识点或难度分组的题目集合，支持分组转场和独立进度管理
- **熔断机制**: 防止异常情况下系统过载或学生恶意操作的保护机制
- **IP角色**: 产品中的卡通形象（如Dolli、Dron），用于提供情感化反馈和学习激励
- **连胜**: 学生连续答对题目的状态，触发特殊的激励反馈和积分加成
- **推题策略**: 根据学生学习状态和知识掌握情况，智能推荐合适题目的算法策略

### 11.3 参考资料
- 巩固练习相关策略文档：详细的推题算法和熔断规则
- 题型支持一期PRD文档：各种题型的统一展示和交互标准
- 内容管理平台选题支持文档：题组选题和管理的技术支持
- 教师端作业布置相关文档：与教师端功能的协同设计
- 竞品调研报告：多邻国、Kahoot等产品的成功要素分析
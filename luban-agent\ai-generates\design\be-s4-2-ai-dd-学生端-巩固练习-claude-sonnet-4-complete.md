# 数据库设计文档 - 学生端巩固练习系统 - V1.0

**模板版本**: v1.3
**最后更新日期**: 2025-05-30
**设计负责人**: claude-sonnet-4
**评审人**: 待填写
**状态**: 草稿

## 📋 输出格式总体要求
* **IMPORTANT AI:** 本模板定义了所有输出的标准化格式，必须严格遵循：
  - 所有表格都有固定的列结构，不得随意增减
  - ER图必须使用Mermaid语法，按照标准格式绘制
  - CRUD分析必须使用标准7列表格格式
  - 检查清单必须逐项填写，使用✅/❌/☐/N/A标记
  - 所有DDL代码必须包含完整中文注释

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-05-30 | claude-sonnet-4 | 初始草稿，基于现有掌握度策略和课程框架设计演进 |

## 1. 项目概述与设计目标

### 1.1 项目背景与范围
本项目是学生端巩固练习系统的数据库设计，旨在为学生提供智能化的练习体验，通过巩固练习功能提升学生学习效果，将知识从"知道"转变为"会用"。系统提供智能推题、实时掌握度计算、情感化反馈、学习报告生成等核心功能。

本次数据库设计基于现有的掌握度策略系统和课程框架系统进行演进设计，新增巩固练习相关的核心表结构，确保与现有系统的兼容性和数据一致性。

### 1.2 设计目标回顾
* 在现有数据库设计基础上演进，新增巩固练习相关表结构
* 提供完整的、符合规范的表创建语句（DDL）
* 清晰描述表间关系，并提供 ER 图
* 详细说明核心用户场景/用户故事所涉及的表以及其 CRUD 操作方式
* 阐述关键表的索引策略及其设计原因
* 对核心表进行数据量预估和增长趋势分析
* 明确数据生命周期管理/归档策略（如果适用）
* 记录特殊设计考量与权衡
* 总结设计如何体现对应数据库（PostgreSQL/ClickHouse）的关键规范

## 2. 数据库环境与总体说明

### 2.1 目标数据库类型
* PostgreSQL 17（用于核心业务数据）
* ClickHouse 23.8.16.16（用于分析数据和历史记录）

### 2.2 数据库创建语句 (如果适用)
使用现有数据库实例，目标数据库名称：
* PostgreSQL: `ai_course_db`（与现有掌握度策略和课程框架共用）
* ClickHouse: `ai_analytics_db`（用于学习行为分析）

### 2.3 数据库选型决策摘要

**技术选型决策表**：
| 实体/表名 | 数据库类型 | 选型依据 | 数据特征 | 查询模式 | 预估数据量 |
|---------|-----------|---------|---------|---------|-----------|
| tbl_consolidation_exercise_session | PostgreSQL | 频繁CRUD操作，需要事务保证 | 事务性数据 | 复杂查询+状态更新 | 中表(100万) |
| tbl_consolidation_answer_record | PostgreSQL | 频繁写入，需要实时查询 | 事务性数据 | 插入+条件查询 | 大表(1000万) |
| tbl_consolidation_learning_report | PostgreSQL | 定期生成，频繁查询 | 事务性数据 | 查询+统计 | 中表(100万) |
| tbl_consolidation_retry_session | PostgreSQL | 低频操作，需要关联查询 | 事务性数据 | 关联查询 | 小表(10万) |
| tbl_consolidation_mistake_record | PostgreSQL | 错题管理，需要去重 | 事务性数据 | 查询+更新 | 中表(500万) |
| tbl_consolidation_drawing_record | PostgreSQL | 勾画数据，JSON存储 | 半结构化数据 | 插入+查询 | 大表(2000万) |
| tbl_consolidation_answer_log | ClickHouse | 仅INSERT分析 | 时序数据 | 聚合分析 | 大表(亿级) |
| tbl_consolidation_session_log | ClickHouse | 仅INSERT分析 | 时序数据 | 聚合分析 | 大表(千万级) | 

## 3. 数据库表详细设计 - PostgreSQL

### 3.1 数据库表清单

#### PostgreSQL表清单
| 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 | 预定数据库类型 |
|------|------------------|-------------|---------|--------------|
| `tbl_consolidation_exercise_session` | 巩固练习会话管理，记录练习状态和进度 | 中表(100万) | 需要事务保证和状态管理 | PostgreSQL |
| `tbl_consolidation_answer_record` | 学生作答记录，存储每次作答的详细信息 | 大表(1000万) | 频繁写入和实时查询需求 | PostgreSQL |
| `tbl_consolidation_learning_report` | 学习报告，汇总练习成果和掌握度变化 | 中表(100万) | 复杂统计查询和报告生成 | PostgreSQL |
| `tbl_consolidation_retry_session` | 再练一次会话，管理再练环节 | 小表(10万) | 低频操作，需要关联查询 | PostgreSQL |
| `tbl_consolidation_mistake_record` | 错题记录，管理学生错题本 | 中表(500万) | 需要去重和分类管理 | PostgreSQL |
| `tbl_consolidation_drawing_record` | 勾画记录，存储学生勾画数据 | 大表(2000万) | JSON数据存储和查询 | PostgreSQL |

#### ClickHouse表清单  
| 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 | 预定数据库类型 |
|------|------------------|-------------|---------|--------------|
| `tbl_consolidation_answer_log` | 作答行为日志，用于学习行为分析 | 大表(亿级) | 仅INSERT分析查询 | ClickHouse |
| `tbl_consolidation_session_log` | 练习会话日志，用于学习路径分析 | 大表(千万级) | 时序数据分析 | ClickHouse |

---

### 3.2 表名: `tbl_consolidation_exercise_session` (巩固练习会话表)

#### 3.2.1 表功能说明
存储学生巩固练习会话的完整生命周期信息，包括练习状态、进度、开始结束时间等。支持中途退出和继续练习功能，是巩固练习系统的核心数据表。

#### 3.2.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_consolidation_exercise_session (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    student_id BIGINT NOT NULL,                                    -- 学生ID，关联用户中心
    course_id BIGINT NOT NULL,                                     -- 课程ID，关联内容平台
    session_status SMALLINT NOT NULL DEFAULT 1,                   -- 会话状态：1进行中，2已完成，3已暂停
    current_question_index SMALLINT NOT NULL DEFAULT 0,           -- 当前题目序号
    total_questions INTEGER NOT NULL DEFAULT 0,                   -- 总题目数量
    start_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,    -- 开始时间
    end_time TIMESTAMPTZ NULL,                                     -- 结束时间
    pause_time TIMESTAMPTZ NULL,                                   -- 暂停时间
    resume_time TIMESTAMPTZ NULL,                                  -- 恢复时间
    session_config JSONB NULL,                                     -- 会话配置信息
    -- 标准字段
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
    created_by BIGINT NOT NULL DEFAULT 0,
    updated_by BIGINT NOT NULL DEFAULT 0
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_consolidation_exercise_session IS '巩固练习会话表 - 记录学生练习会话的完整生命周期';
COMMENT ON COLUMN tbl_consolidation_exercise_session.id IS '主键ID';
COMMENT ON COLUMN tbl_consolidation_exercise_session.student_id IS '学生ID，关联用户中心服务';
COMMENT ON COLUMN tbl_consolidation_exercise_session.course_id IS '课程ID，关联内容平台服务';
COMMENT ON COLUMN tbl_consolidation_exercise_session.session_status IS '会话状态 (1:进行中, 2:已完成, 3:已暂停)';
COMMENT ON COLUMN tbl_consolidation_exercise_session.current_question_index IS '当前题目序号，从0开始';
COMMENT ON COLUMN tbl_consolidation_exercise_session.total_questions IS '本次练习的总题目数量';
COMMENT ON COLUMN tbl_consolidation_exercise_session.start_time IS '练习开始时间';
COMMENT ON COLUMN tbl_consolidation_exercise_session.end_time IS '练习结束时间';
COMMENT ON COLUMN tbl_consolidation_exercise_session.pause_time IS '练习暂停时间';
COMMENT ON COLUMN tbl_consolidation_exercise_session.resume_time IS '练习恢复时间';
COMMENT ON COLUMN tbl_consolidation_exercise_session.session_config IS '会话配置信息，JSON格式存储';
COMMENT ON COLUMN tbl_consolidation_exercise_session.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_consolidation_exercise_session.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_consolidation_exercise_session.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_consolidation_exercise_session.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_consolidation_exercise_session.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_consolidation_exercise_session_student_id ON tbl_consolidation_exercise_session(student_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_exercise_session_course_id ON tbl_consolidation_exercise_session(course_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_exercise_session_status ON tbl_consolidation_exercise_session(session_status) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_exercise_session_start_time ON tbl_consolidation_exercise_session(start_time) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_exercise_session_student_course ON tbl_consolidation_exercise_session(student_id, course_id, session_status) WHERE deleted_at IS NULL;
```

#### 3.2.3 索引策略说明
* `idx_consolidation_exercise_session_student_id`: 支持按学生查询练习记录
* `idx_consolidation_exercise_session_course_id`: 支持按课程查询练习统计
* `idx_consolidation_exercise_session_status`: 支持按状态筛选会话
* `idx_consolidation_exercise_session_start_time`: 支持按时间范围查询
* `idx_consolidation_exercise_session_student_course`: 复合索引，支持学生课程维度的高效查询

---

### 3.3 表名: `tbl_consolidation_answer_record` (作答记录表)

#### 3.3.1 表功能说明
存储学生在巩固练习中每道题目的作答详细信息，包括答案内容、正确性、作答时长等。是掌握度计算和学习分析的重要数据源。

#### 3.3.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_consolidation_answer_record (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    session_id BIGINT NOT NULL,                                   -- 练习会话ID
    question_id BIGINT NOT NULL,                                  -- 题目ID，关联内容平台
    question_index SMALLINT NOT NULL,                             -- 题目在练习中的序号
    student_answer TEXT NULL,                                     -- 学生答案内容
    correct_answer TEXT NOT NULL,                                 -- 正确答案
    is_correct BOOLEAN NOT NULL DEFAULT FALSE,                   -- 是否正确
    answer_result SMALLINT NOT NULL DEFAULT 0,                   -- 作答结果：0未作答，1正确，2错误，3部分正确
    answer_duration INTEGER NOT NULL DEFAULT 0,                  -- 作答时长（秒）
    question_difficulty NUMERIC(3,2) NOT NULL DEFAULT 1.0,       -- 题目难度系数
    knowledge_points JSONB NULL,                                 -- 关联的知识点信息
    is_continuous_correct BOOLEAN NOT NULL DEFAULT FALSE,        -- 是否连续正确
    submit_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,  -- 提交时间
    -- 标准字段
    created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
    created_by BIGINT NOT NULL DEFAULT 0,
    updated_by BIGINT NOT NULL DEFAULT 0
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_consolidation_answer_record IS '巩固练习作答记录表 - 记录学生每道题目的作答详情';
COMMENT ON COLUMN tbl_consolidation_answer_record.id IS '主键ID';
COMMENT ON COLUMN tbl_consolidation_answer_record.session_id IS '练习会话ID，关联tbl_consolidation_exercise_session';
COMMENT ON COLUMN tbl_consolidation_answer_record.question_id IS '题目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_consolidation_answer_record.question_index IS '题目在本次练习中的序号';
COMMENT ON COLUMN tbl_consolidation_answer_record.student_answer IS '学生提交的答案内容';
COMMENT ON COLUMN tbl_consolidation_answer_record.correct_answer IS '题目的正确答案';
COMMENT ON COLUMN tbl_consolidation_answer_record.is_correct IS '答案是否正确';
COMMENT ON COLUMN tbl_consolidation_answer_record.answer_result IS '作答结果 (0:未作答, 1:正确, 2:错误, 3:部分正确)';
COMMENT ON COLUMN tbl_consolidation_answer_record.answer_duration IS '作答时长，单位秒';
COMMENT ON COLUMN tbl_consolidation_answer_record.question_difficulty IS '题目难度系数，用于掌握度计算';
COMMENT ON COLUMN tbl_consolidation_answer_record.knowledge_points IS '关联的知识点信息，JSON格式';
COMMENT ON COLUMN tbl_consolidation_answer_record.is_continuous_correct IS '是否为连续正确作答';
COMMENT ON COLUMN tbl_consolidation_answer_record.submit_time IS '答案提交时间';
COMMENT ON COLUMN tbl_consolidation_answer_record.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_consolidation_answer_record.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_consolidation_answer_record.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_consolidation_answer_record.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_consolidation_answer_record.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_consolidation_answer_record_session_id ON tbl_consolidation_answer_record(session_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_answer_record_question_id ON tbl_consolidation_answer_record(question_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_answer_record_submit_time ON tbl_consolidation_answer_record(submit_time) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_answer_record_result ON tbl_consolidation_answer_record(answer_result) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_answer_record_session_index ON tbl_consolidation_answer_record(session_id, question_index) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_answer_record_knowledge_points ON tbl_consolidation_answer_record USING gin (knowledge_points) WHERE deleted_at IS NULL;

-- 添加表和字段注释
COMMENT ON TABLE tbl_consolidation_mistake_record IS '巩固练习错题记录表 - 管理学生的错题本';
COMMENT ON COLUMN tbl_consolidation_mistake_record.id IS '主键ID';
COMMENT ON COLUMN tbl_consolidation_mistake_record.student_id IS '学生ID，关联用户中心服务';
COMMENT ON COLUMN tbl_consolidation_mistake_record.question_id IS '题目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_consolidation_mistake_record.answer_record_id IS '关联的作答记录ID，自动添加时有值';
COMMENT ON COLUMN tbl_consolidation_mistake_record.add_type IS '添加方式 (1:自动添加, 2:手动添加)';
COMMENT ON COLUMN tbl_consolidation_mistake_record.mistake_reason IS '错误原因分析';
COMMENT ON COLUMN tbl_consolidation_mistake_record.mistake_tags IS '错误标签分类，JSON格式';
COMMENT ON COLUMN tbl_consolidation_mistake_record.review_status IS '复习状态 (0:未复习, 1:已复习, 2:已掌握)';
COMMENT ON COLUMN tbl_consolidation_mistake_record.review_count IS '复习次数统计';
COMMENT ON COLUMN tbl_consolidation_mistake_record.last_review_time IS '最后一次复习时间';
COMMENT ON COLUMN tbl_consolidation_mistake_record.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_consolidation_mistake_record.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_consolidation_mistake_record.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_consolidation_mistake_record.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_consolidation_mistake_record.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE UNIQUE INDEX uk_consolidation_mistake_record_student_question ON tbl_consolidation_mistake_record(student_id, question_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_mistake_record_student_id ON tbl_consolidation_mistake_record(student_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_mistake_record_question_id ON tbl_consolidation_mistake_record(question_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_mistake_record_add_type ON tbl_consolidation_mistake_record(add_type) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_mistake_record_review_status ON tbl_consolidation_mistake_record(review_status) WHERE deleted_at IS NULL;
CREATE INDEX idx_consolidation_mistake_record_answer_record_id ON tbl_consolidation_mistake_record(answer_record_id) WHERE deleted_at IS NULL;
    
    tbl_consolidation_drawing_record {
        bigint id PK
        bigint answer_record_id FK
        smallint drawing_tool_type
        varchar drawing_color
        jsonb drawing_data
        integer drawing_order
        timestamptz created_at
        timestamptz updated_at
        timestamptz deleted_at
    }
    
    tbl_consolidation_answer_log {
        string log_id PK
        uint64 student_id
        uint64 session_id
        uint64 question_id
        uint64 course_id
        uint8 answer_result
        uint32 answer_duration
        decimal question_difficulty
        string knowledge_points
        decimal mastery_before
        decimal mastery_after
        datetime64 submit_time
        date date_partition
        uint8 hour_partition
        datetime64 created_at
    }
    
    tbl_consolidation_session_log {
        string log_id PK
        uint64 session_id
        uint64 student_id
        uint64 course_id
        uint8 session_status
        string event_type
        uint16 total_questions
        uint16 completed_questions
        uint16 correct_count
        uint32 session_duration
        datetime64 event_time
        date date_partition
        uint8 hour_partition
        datetime64 created_at
    }
* **劣势**：查询性能相对较低，数据类型检查较弱
* **选择原因**：配置信息和分析数据具有动态性，结构化存储成本过高

---

## 4. 数据库表详细设计 - ClickHouse

### 4.1 ClickHouse表清单

| 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 | 分区策略 |
|------|------------------|-------------|---------|---------|
| `tbl_consolidation_answer_log` | 作答行为日志，用于学习行为分析 | 大表(亿级) | 仅INSERT分析查询，时序数据 | 按日期分区 |
| `tbl_consolidation_session_log` | 练习会话日志，用于学习路径分析 | 大表(千万级) | 仅INSERT分析查询，时序数据 | 按日期分区 |

---

### 4.2 表名: `tbl_consolidation_answer_log` (作答行为日志表)

#### 4.2.1 表功能说明
存储学生作答行为的详细日志数据，用于学习行为分析、掌握度变化追踪、个性化推荐等分析场景。数据来源于PostgreSQL的作答记录表。

#### 4.2.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_consolidation_answer_log (
    log_id String,                                    -- 日志唯一标识
    student_id UInt64,                               -- 学生ID
    session_id UInt64,                               -- 练习会话ID
    question_id UInt64,                              -- 题目ID
    course_id UInt64,                                -- 课程ID
    answer_result UInt8,                             -- 作答结果：0未作答，1正确，2错误，3部分正确
    answer_duration UInt32,                          -- 作答时长（秒）
    question_difficulty Decimal32(2),                -- 题目难度系数
    knowledge_points String,                         -- 关联的知识点信息（JSON字符串）
    mastery_before Decimal32(4),                     -- 作答前掌握度
    mastery_after Decimal32(4),                      -- 作答后掌握度
    is_continuous_correct UInt8,                     -- 是否连续正确（0/1）
    submit_time DateTime64(3),                       -- 提交时间
    date_partition Date DEFAULT toDate(submit_time), -- 日期分区字段
    hour_partition UInt8 DEFAULT toHour(submit_time), -- 小时分区字段
    created_at DateTime64(3) DEFAULT now64()         -- 记录创建时间
) ENGINE = MergeTree()
PARTITION BY date_partition
ORDER BY (student_id, course_id, submit_time)
SETTINGS index_granularity = 8192
TTL date_partition + INTERVAL 2 YEAR DELETE;

-- 添加表注释
ALTER TABLE tbl_consolidation_answer_log COMMENT '巩固练习作答行为日志表 - 用于学习行为分析和掌握度追踪';
```

#### 4.2.3 表设计说明
* **引擎选择**：使用MergeTree引擎，支持高效的插入和查询
* **分区策略**：按日期分区，便于数据管理和查询优化
* **排序键**：按(student_id, course_id, submit_time)排序，支持学生维度的时序分析
* **TTL设置**：数据保留2年后自动删除
* **索引颗粒度**：8192行，平衡查询性能和存储效率

---

### 4.3 表名: `tbl_consolidation_session_log` (练习会话日志表)

#### 4.3.1 表功能说明
存储学生练习会话的生命周期事件日志，包括会话开始、暂停、恢复、完成等关键事件，用于学习路径分析和用户行为研究。

#### 4.3.2 表结构定义 (DDL)

```sql
CREATE TABLE tbl_consolidation_session_log (
    log_id String,                                    -- 日志唯一标识
    session_id UInt64,                               -- 练习会话ID
    student_id UInt64,                               -- 学生ID
    course_id UInt64,                                -- 课程ID
    session_status UInt8,                            -- 会话状态：1进行中，2已完成，3已暂停
    event_type String,                               -- 事件类型：START、PAUSE、RESUME、COMPLETE、UPDATE
    total_questions UInt16,                          -- 总题目数量
    completed_questions UInt16,                      -- 已完成题目数量
    correct_count UInt16,                            -- 正确答题数量
    session_duration UInt32,                         -- 会话持续时长（秒）
    device_info String,                              -- 设备信息（JSON字符串）
    ip_address String,                               -- IP地址
    user_agent String,                               -- 用户代理
    event_time DateTime64(3),                        -- 事件发生时间
    date_partition Date DEFAULT toDate(event_time),  -- 日期分区字段
    hour_partition UInt8 DEFAULT toHour(event_time), -- 小时分区字段
    created_at DateTime64(3) DEFAULT now64()         -- 记录创建时间
) ENGINE = MergeTree()
PARTITION BY date_partition
ORDER BY (student_id, course_id, event_time)
SETTINGS index_granularity = 8192
TTL date_partition + INTERVAL 2 YEAR DELETE;

-- 添加表注释
ALTER TABLE tbl_consolidation_session_log COMMENT '巩固练习会话日志表 - 用于学习路径分析和用户行为研究';
```

#### 4.3.3 表设计说明
* **引擎选择**：使用MergeTree引擎，支持高效的时序数据处理
* **分区策略**：按日期分区，支持高效的时间范围查询
* **排序键**：按(student_id, course_id, event_time)排序，支持用户行为时序分析
* **TTL设置**：数据保留2年后自动删除
* **扩展字段**：包含设备信息、IP地址等用于用户行为分析

### 4.4 ClickHouse表索引策略

#### 4.4.1 查询优化考虑
* **时间范围查询**：分区剪枝优化，快速定位时间范围
* **用户维度分析**：排序键支持单用户时序分析
* **课程维度统计**：支持课程维度的聚合查询
* **实时仪表板**：支持近期数据的实时查询

#### 4.4.2 数据写入优化
* **批量写入**：使用批量INSERT减少写入开销
* **异步写入**：不影响主业务流程的性能
* **数据去重**：通过log_id保证数据幂等性

---

## 5. 数据库表关系设计 (ER图)

## 10. 设计规范体现总结

### 10.1 PostgreSQL规范体现

#### 10.1.1 命名规范体现
* ✅ **表名规范**：所有表名使用`tbl_`前缀，采用下划线分隔
* ✅ **字段命名**：使用小写字母和下划线，语义明确
* ✅ **索引命名**：使用`idx_`前缀，唯一索引使用`uk_`前缀
* ✅ **约束命名**：遵循`约束类型_表名_字段名`格式

#### 10.1.2 数据类型规范体现
* ✅ **时间类型**：统一使用`TIMESTAMPTZ`存储时间
* ✅ **JSON类型**：使用`JSONB`而非`JSON`，提升查询性能
* ✅ **数值类型**：根据数据范围选择合适的数值类型
* ✅ **文本类型**：使用`TEXT`而非`VARCHAR`，避免长度限制

#### 10.1.3 标准字段规范体现
* ✅ **审计字段**：所有表包含created_at、updated_at、deleted_at
* ✅ **操作人字段**：包含created_by、updated_by记录操作人
* ✅ **软删除**：使用deleted_at实现软删除机制
* ✅ **主键设计**：使用BIGSERIAL作为主键类型

#### 10.1.4 索引设计规范体现
* ✅ **软删除适配**：所有索引包含`WHERE deleted_at IS NULL`条件
* ✅ **复合索引**：根据查询模式设计复合索引
* ✅ **唯一约束**：使用唯一索引保证数据唯一性
* ✅ **性能优化**：避免在大表上创建过多索引

### 10.2 ClickHouse规范体现

#### 10.2.1 表引擎选择
* ✅ **MergeTree引擎**：选择MergeTree引擎支持高效查询和数据管理
* ✅ **分区策略**：按日期分区，便于数据管理和查询优化
* ✅ **排序键设计**：根据查询模式设计合理的排序键

#### 10.2.2 数据类型选择
* ✅ **数值类型**：使用UInt64、UInt32等无符号整型节省空间
* ✅ **时间类型**：使用DateTime64支持毫秒精度
* ✅ **字符串类型**：使用String类型存储变长字符串
* ✅ **小数类型**：使用Decimal类型保证精度

#### 10.2.3 性能优化规范
* ✅ **颗粒度设置**：使用8192行颗粒度平衡性能和存储
* ✅ **压缩算法**：使用默认LZ4压缩算法
* ✅ **TTL设置**：设置数据生命周期管理

---

## 11. 设计检查清单

### 11.1 功能完整性检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 巩固练习会话管理 | ✅ | tbl_consolidation_exercise_session表完整支持 |
| 学生作答记录存储 | ✅ | tbl_consolidation_answer_record表完整支持 |
| 学习报告生成 | ✅ | tbl_consolidation_learning_report表完整支持 |
| 再练一次功能 | ✅ | tbl_consolidation_retry_session表完整支持 |
| 错题本管理 | ✅ | tbl_consolidation_mistake_record表完整支持 |
| 勾画记录存储 | ✅ | tbl_consolidation_drawing_record表完整支持 |
| 学习行为分析 | ✅ | ClickHouse日志表完整支持 |
| 数据软删除 | ✅ | 所有PostgreSQL表支持软删除 |

### 11.2 性能设计检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 高频查询索引覆盖 | ✅ | 为高频查询场景设计了专门索引 |
| 复合索引设计 | ✅ | 根据查询模式设计了复合索引 |
| 软删除索引适配 | ✅ | 所有索引包含deleted_at过滤条件 |
| 大表分区策略 | ✅ | ClickHouse表采用日期分区 |
| JSON字段索引 | ✅ | 使用GIN索引支持JSON字段查询 |
| 查询性能预估 | ✅ | 核心查询预估在200ms以内 |

### 11.3 数据安全检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 数据备份策略 | ✅ | 制定了完整的备份策略 |
| 数据恢复机制 | ✅ | 软删除支持数据恢复 |
| 敏感数据保护 | N/A | 当前设计无敏感数据 |
| 数据访问控制 | ✅ | 应用层实现权限控制 |
| 数据审计日志 | ✅ | 标准字段支持操作审计 |

### 11.4 扩展性检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| 水平扩展支持 | ✅ | 设计支持按学生ID分片 |
| 垂直扩展支持 | ✅ | 表结构支持字段扩展 |
| 跨库查询支持 | ✅ | 通过应用层实现跨库关联 |
| 历史数据归档 | ✅ | 制定了完整的归档策略 |
| 新功能扩展 | ✅ | JSONB字段支持灵活扩展 |

### 11.5 规范遵循检查

| 检查项 | 状态 | 说明 |
|--------|------|------|
| PostgreSQL命名规范 | ✅ | 严格遵循命名规范 |
| PostgreSQL数据类型规范 | ✅ | 使用推荐的数据类型 |
| PostgreSQL标准字段规范 | ✅ | 所有表包含标准字段 |
| ClickHouse表引擎规范 | ✅ | 使用MergeTree引擎 |
| ClickHouse数据类型规范 | ✅ | 使用合适的数据类型 |
| 中文注释完整性 | ✅ | 所有表和字段都有中文注释 |

---

## 12. 参考资料

### 12.1 设计输入文档
* `be-s2-1-ai-td-学生端-巩固练习-claude-sonnet-4.md` - 技术架构设计
* `be-s3-1-ai-de-学生端-巩固练习-claude-sonnet-4.md` - 数据实体设计
* `be-s3-4-ai-dd-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md` - 现有数据库设计
* `be-s3-4-ai-dd-掌握度策略-V1.0-claude-3.7.md` - 现有数据库设计

### 12.2 设计规范文档
* `rules-postgresql-code-v2.md` - PostgreSQL设计规范
* `rules-clickhouse-code-v2.md` - ClickHouse设计规范
* `be-s4-2-ai-dd-template-v1.3.md` - 数据库设计模板

### 12.3 相关技术文档
* PostgreSQL 17 官方文档
* ClickHouse 23.8.16.16 官方文档
* JSONB性能优化最佳实践
* 数据库分区策略指南

---

**文档结束**

*本文档基于claude-sonnet-4模型生成，遵循数据库设计模板v1.3规范*

---

--- 数据库设计完成，已按模板要求完成质量检查，等待您的命令，指挥官 
PRD - 巩固练习相关策略 - V1.0

# 一、背景和目标

##  需求背景

在 AI 课程的学习流程中，“巩固练习”是承接课堂知识理解与能力内化的关键环节。学生是否能够在练习阶段接触到**适合其能力水平**的题目，直接决定了知识点掌握的效率和质量。

当前阶段，我们正处于系统冷启动初期，题目缺乏足够的答题数据，难以直接使用模型驱动的推荐算法，因此需要通过一定的“人工可控的策略”，尽早建立“能力匹配、难度适配”的练习体验，并为后续数据积累和算法推荐打下基础。

## 项目收益

1. 为不同能力水平的学生提供匹配的练习题
避免“题太难打击信心”或“题太简单浪费时间”，提升学习效率和满意度。
1. 让学生更有效地掌握课程重点知识点
通过对典型题的优先覆盖与逐步难度推进，提升知识掌握度和迁移能力。
1. 为后续算法推荐提供结构化数据积累
每一次基于策略推送的练习和作答，都是训练模型参数的有效样本，便于未来迭代上线 IRT、知识追踪等自动推荐机制。
1. 提升整体产品智能感与完成率
通过系统“主动推题、智能响应”，增强学生对产品智能化的感知。
## 覆盖用户

使用新AI课学习的全部用户



# 二、内容数据结构

## 内容结构

![board_RCLUwZrT1h4bSjbssEocOVGnn3d](https://static.test.xiaoluxue.cn/demo41/prd_images/1747280690537.png)

- 每节课的巩固练习由 1-5 个题组构成，题组对应为和这节课知识点相关的典型题型。
- 每个题组初期配置10道题左右
- 题组中的典型必做题，老师在选题时会打上推荐标签。每个题组中可以有“0-多”个必做题
- 每个题目都有知识点内的难度等级L1 - L5，一个题组内的难度分布会尽量符合正态分布
## 题目难度系数

根据题目的难度等级，每道题目有对应的难度系数。

冷启动阶段的难度系数为人工设定，当有用户作答数据后，会重新离线计算更准确的难度系数。

| 难度等级 | L1 | L2 | L3 | L4 | L5 |
| --- | --- | --- | --- | --- | --- |
| 题目难度系数 | 0.2 | 0.4 | 0.6 | 0.8 | 1.0 |



# 三、策略方案

## 题目推荐策略

### 前置计算

#### 预估题目数量

- 整体题量
1. 根据这个知识点的难度和考试频率，计算基础答题量
基础题量和知识点难度、考试频率相关。考频高的难点应该多练习，而相对简单且低频的知识点，练习量可以减少。

基础答题量=10*难度系数*考频系数   四舍五入取整   [8 , 14]

1. 根据用户当前掌握度，计算动态调整系数 A。默认值为1。


- θ_tar为该知识点对应的目标掌握度
- θ_cur为学生对该知识点的当前掌握度
- α 是“经验调节系数”，用来控制差距对题量的影响强度，默认取0.6
1. 预估题目数量 = 基础答题量 * 动态调整系数 A
1. 全局上下限
- 下限：题组数量 * 2（保证每个题组至少有2道题）
如：预估整体题量为8，共5个题组。则最终的预估整体题量为10

- 上限：15 （避免单次练习量过多）


示例：当前知识点难度系数 0.9，考频系数 1.2 ，学生当前掌握度 θ_cur=0.3，目标掌握度θ_tar=0.8。

1. 计算基础答题量  0.9 * 1.2 * 10 = 10.8 ≈ 11
1. 计算动态调整系数 A = 1 + 0.6 * [(0.8−0.3)/0.8] ≈ 1.375
1. 计算预估题目数量 11 * 1.375 ≈ 15


- 每个题组的题量
巩固练习的定位是需要让学生把典型题都练到，因此题组在设置的时候也应该是典型的，每个题组都同等重要。

把预估的整体题量，平均到每个题组，如果不能整除，则有部分题组会多1题。

如：共10题，3个题组，则每个题组的预估题量为 3,3,4



- 计算时机
1. 业务树完成难度系数和考频系数配置后，离线计算基础答题量
1. 离线更新用户初始掌握度&目标掌握度后，离线计算动态调整系数
1. 用户完成AI课后，更新动态调整系数。


#### 预期掌握度增量

预估每道题对当前用户带来的掌握度变化量作为推荐依据，掌握度增量计算方式详见：[PRD - 掌握度 - V1.0](https://wcng60ba718p.feishu.cn/wiki/J3d0wrEaDiWRy6kj2uVcQKLfnmh)

1. 
θ为学生的掌握度，β为题目难度系数

Sigmoid 函数的数学表达式为

1. 
- 示例
用户当前掌握度 0.3，题目难度系数0.6（L3）

- P(答对）≈ sigmoid(4 × (0.3 − 0.6)) ≈ 0.2315
- P(答错）≈  1 - 0.2315 = 0.7685
- 答对掌握度增量 = 0.2 * （0.6-0.3）= 0.06
- 答错掌握度增量 = -0.1 * （0.6-0.3）* 0.5 = -0.015
- 预期掌握度增量 = 0.2315 * 0.06 + 0.7685 * -0.015 ≈ 0.002
- 计算时机
用户每次答题后计算



### 巩固练习题目推荐

#### 整体流程

![board_ANrJwuzNVhn2PnbsQv3cASfrn9e](https://static.test.xiaoluxue.cn/demo41/prd_images/1747280692576.png)

#### 题目推荐策略

1. 巩固练习中的多个题组，需要依次作答。
1. 每次推题默认在当前题组中选择「预期掌握度增量」最大的题目
1. 如果有必做题，优先推荐必做题。
1. 如果有多道题的「预期掌握度增量」相等，随机选择一道。
1. 最大答题数量（不包括错题）不超过预估答题数，最小答题数量为题组数量*2
**限制条件**：

1. 连续两题之间难度最多提升一个等级（防止难度剧烈跳变）
1. 上题答错，不可提升难度等级
1. 上题答对，不可降低难度等级
**特殊情况：**当前题组作答未满2题已没有用户可推荐的题目，则直接进入下一题组。

注：当推荐的题目难度上升/下降时，C端展示对应动效



#### 熔断策略

熔断策略优先级高于题目推荐优先级，当触发熔断时，直接结束巩固练习。会触发熔断的场景如下：

1. 用户连续做错5题 且 题池中没有难度更低的题目：结束巩固练习
1. 用户专注度分值等于3：在用户提交当前题目后，结束巩固练习。




## 再练一次

### 报告页引导

用户完成巩固练习后，系统根据其对当前知识点的掌握情况和本次练习表现，判断是否需要引导用户再练一次

**判断条件：**

1. 用户当前掌握度低于目标掌握度的70% 
1. 巩固练习正确率低于 70%
当同时满足上述2个条件时，报告页面需要引导用户再练一次。



### 判断是否满足再练一次

用户完成巩固练习后，需要根据题库中的剩余可练习题目数量来判断，是否满足再练一次。如果满足则展示入口，如果不满足则不展示入口（详见C端需求）

**判断步骤：**

1. 确定可练习的题组：只有在“巩固练习”环节中出现过错题的题组，才会进入再练一次环节。
1. 计算单个题组的可用题目：找出该题组内学生在“巩固练习”中未作答过，并且其“预期掌握度增量”不小于0.01的题目。将这些筛选出来的题目数量记为“可用题目数”。
1. 判断是否满足再练一次：所有可练习题组中的可用题目数量≥3题时，满足再练一次条件


### 计算预估题目数量

1. 单个题组的预估题量 = Min(“可用题目数”, 该题组在巩固练习中的“预估答题数”)
1. 所有可练习的题组计算出的“预估题量”进行累加，得到整体的“预估题目数量”


### 再练一次推题策略

和正常的巩固练习推题策略相比

1. 再练一次增加了错题重练：每个题组的首题为巩固练习中「预期掌握度增量最大」的错题原题
1. 没有每个题组最少作答2题的限制
![board_COrbwAFQKhxQCjbyoeZcKn4xnmu](https://static.test.xiaoluxue.cn/demo41/prd_images/1747280694562.png)



## 不认真答题判断

用户答题专注度与作答时间、用户行为等多种特征相关。V1.0阶段可构建极简版专注度评分系统，仅考虑作答时长及正确率。

**判断方式：**

| 类型 | 事件 |
| --- | --- |
| 作答时长 | 进入题目1s内作答，且回答错误 |
|  | 作答用时小于平均时长1/3，且回答错误（一期不支持） |
| 正确率 | 连续做错3题 |
|  | 做错难度系数低于当前掌握度的题目 |

引入专注度计分，每个巩固练习的初始分值为10分

每次答题后，如命中下述事件，则专注度分值 -1。同时命中多个事件不累计扣分。

**使用场景：**

- 分数等于7时，触发第一次提醒
- 分数等于5时，触发第二次提醒
- 分数等于3时，触发熔断




# 历史存档（不用看）

![board_OVtFw41M0h0jGKbqJ7ucFUChnEb](https://static.test.xiaoluxue.cn/demo41/prd_images/1747280696609.png)
































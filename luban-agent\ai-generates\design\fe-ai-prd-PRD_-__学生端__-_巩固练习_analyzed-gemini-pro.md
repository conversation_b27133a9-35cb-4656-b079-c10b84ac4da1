# 前端产品需求文档 - PRD - 学生端 - 巩固练习

文档说明：
  - 标注⚠️的是 AI 基于用户角度补充的点，可能并未在原始 PRD 中体现

## 1. 需求背景与目标

### 1.1 需求目标
通过本次巩固练习的方案升级，旨在打造一个流畅、有效、且反馈友好的练习体验。具体目标包括：
-   **提升学习效果：** 通过与AI课知识点高度关联的题目，并根据学生实时作答表现自动调整题目难度，帮助学生检验学习效果并增强对知识点的掌握程度。
-   **确保知识掌握：** 通过“再练一次”逻辑，确保每个学生在完成巩固练习后都能达到掌握知识的目标。
-   **优化学习体验：** 整体优化练习流程，增加更丰富的情感反馈和激励机制，如对答题正确的学生及时给予激励，对答题错误的学生采用有效方式缓解其心理压力。
-   **提升用户接受度：** 通过上述改进，提升学生对巩固练习的接受度，进而促进学生成绩的提升。

### 1.2 用户价值
-   对于 **初中、高中学生**，解决了 **原有练习缺乏有效情感反馈、激励机制不足的问题**，带来了 **更具吸引力、更能及时反馈学习表现、更能帮助巩固所学知识点的练习体验，最终有助于提升学习效果和学习信心。**
-   对于 **追求高效学习和知识点掌握的学生**，满足了 **在AI课后进行有效巩固、检验学习成果的核心诉求**，实现了 **通过针对性练习和“再练一次”机制达到知识点掌握的目标。**

## 2. 功能范围

### 2.1 核心功能
#### **[核心功能点1]:** 巩固练习入口管理
负责在不同阶段（练习前、练习中、练习后）展示巩固练习的入口及其对应状态，并处理用户的进入操作。

##### **[核心功能点1.1]:** 练习入口状态展示与交互
###### 界面布局与核心UI元素
-   **布局参考：**
    *   [学习Tab-学科页-巩固练习入口](https://static.test.xiaoluxue.cn/demo41/prd_images/image_FDkbbgFaPowFYmxY9UacwsFKnsc.png) - 仅关注布局
    *   [学科页-知识点学习路径中的巩固练习入口](https://static.test.xiaoluxue.cn/demo41/prd_images/image_UCaRbRVzFoEaP7xXhmAcEKDynwc.png) - 仅关注布局
    *   [任务列表中的巩固练习入口](https://static.test.xiaoluxue.cn/demo41/prd_images/image_HNgEbyNCroqG8oxPLZEcacB0n7b.png) - 仅关注布局
    *   [练习完成后带有看报告和再练一次的入口](https://static.test.xiaoluxue.cn/demo41/prd_images/image_N577bLqY5oTFMUxEqcpccsmGnHd.png) - 仅关注布局

-   **布局原则：**
    *   **学习Tab-学科页-巩固练习入口 (image_FDkbbgFaPowFYmxY9UacwsFKnsc.png):** 界面主要由顶部信息区、左侧边栏（日程）、中心内容区（学科学习模块）、右侧边栏（个性化推荐）和底部导航栏构成。巩固练习入口通常位于中心内容区的学科卡片内，或作为学科下的一个学习模块。
    *   **学科页-知识点学习路径中的巩固练习入口 (image_UCaRbRVzFoEaP7xXhmAcEKDynwc.png):** 界面包含顶部状态栏、导航与概要信息栏、主内容区（学习路径图）和底部浮动信息卡片。巩固练习入口表现为学习路径上的一个核心学习活动模块，通常与其他学习活动（如课程学习、拓展练习）并列。
    *   **任务列表中的巩固练习入口 (image_HNgEbyNCroqG8oxPLZEcacB0n7b.png):** 界面展示学习路径的同时，通过浮层或侧边栏显示任务列表。巩固练习作为一种任务类型，会在任务列表中列出，包含任务名称和截止时间。
    *   **练习完成后带有看报告和再练一次的入口 (image_N577bLqY5oTFMUxEqcpccsmGnHd.png):** 界面同样为学习路径图，当巩固练习完成后，在对应的学习节点（如图中“巩固练习”图标）附近会弹出交互弹窗，展示练习成果（如“已获20星”）并提供“看报告”和“再练一次”的操作按钮。

-   **核心UI元素清单：**
    *   巩固练习模块/卡片
    *   模块名称文本（“巩固练习”）
    *   预估题目数量文本（例如“约 8 题”）
    *   状态图标（例如“已完成”icon）
    *   已获得星星数量及icon（练习完成后）
    *   任务到期时间文本（如适用）
    *   【进入练习】/【继续练习】按钮
    *   【看报告】按钮（练习完成后）
    *   【再练一次】按钮（练习完成后）

###### 功能需求与交互原则
-   **功能描述：**
    *   **入口路径：**
        *   【学习】Tab → 学科 → 学习地图 → 某个知识点 → 巩固练习模块。
        *   【学习】Tab → 学科 → 老师布置的任务 → 巩固练习。
    *   **入口展示：**
        *   名称固定为“巩固练习”。
        *   预估题目数量：根据推题策略自动计算并展示，格式为“约 N 题”。
    *   **入口状态管理：**
        *   **练习未完成状态：**
            *   判定条件：存在未作答题目（包括初始进入或中途退出）。
            *   入口显示：巩固练习。
            *   任务到期时间：如果是老师布置的任务，需展示对应的到期时间。
            *   交互：点击后展开巩固练习卡片，显示【进入练习】或【继续练习】按钮，点击按钮跳转至作答页面。
        *   **练习已完成状态：**
            *   判定条件：首次巩固练习全部题目完成作答。
            *   入口显示：巩固练习 + “已完成”icon。
            *   星星数量：显示“已获+数量” + 星星icon。
            *   交互：点击后展开巩固练习卡片，提供：
                *   【看报告】按钮：点击跳转至报告页面。
                *   【再练一次】/【继续练习】按钮：点击跳转至作答页面进行再练。
-   **交互模式：**
    *   用户通过点击巩固练习模块/卡片来展开详情或直接进入。
    *   按钮（【进入练习】、【继续练习】、【看报告】、【再练一次】）为主要交互触发点。
-   **反馈原则：**
    *   点击入口模块后，应有明确的视觉变化，如卡片展开、高亮等。
    *   点击操作按钮后，应立即响应页面跳转或状态更新。

**优先级：** P0
**依赖关系：** 无

##### 用户场景与故事 (针对此核心功能点)

###### [场景1：首次进入未完成的巩固练习]
作为一名**学生**，我需要**在知识点学习后找到并开始巩固练习**以解决**对知识点掌握不牢固的问题**，从而**加深理解和记忆**。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 在学科学习地图中，点击某个知识点下的“巩固练习”模块。
    *   **界面变化:** 巩固练习模块展开（如果之前是折叠状态），显示“约 N 题”和【进入练习】按钮。
    *   **即时反馈:** 无特殊即时反馈，界面元素按规则展示。
2.  **用户操作2:** 点击【进入练习】按钮。
    *   **界面变化:** 页面跳转至“进入练习转场”动画（详见[核心功能点2]）。
    *   **即时反馈:** 按钮点击态，页面开始加载转场。

**场景细节补充：**
*   **前置条件:** 用户已登录，并进入到学科学习地图界面，存在未开始的巩固练习。
*   **期望结果:** 用户成功进入巩固练习的作答流程。
*   **异常与边界情况:**
    *   ⚠️ 网络请求失败导致无法获取预估题目数量：显示默认文案或loading状态，允许用户尝试点击。
    *   ⚠️ 练习数据加载失败：跳转后提示错误信息，引导用户重试或返回。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户在学科学习地图] --> B{点击巩固练习模块};
    B -- 显示详情 --> C[展示预估题数和'进入练习'按钮];
    C --> D{点击'进入练习'按钮};
    D --> E[跳转至进入练习转场界面];
```

###### [场景2：继续未完成的巩固练习]
作为一名**学生**，我需要**快速找到上次未完成的巩固练习并继续作答**以解决**练习中断后重新开始的麻烦**，从而**高效利用碎片时间完成练习**。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 在学科学习地图中，点击之前中途退出的“巩固练习”模块。
    *   **界面变化:** 巩固练习模块展开，显示“约 N 题”和【继续练习】按钮。
    *   **即时反馈:** 无特殊即时反馈。
2.  **用户操作2:** 点击【继续练习】按钮。
    *   **界面变化:** 页面跳转至“继续练习转场”动画（详见[核心功能点2]）。
    *   **即时反馈:** 按钮点击态，页面开始加载转场。

**场景细节补充：**
*   **前置条件:** 用户已登录，并有中途退出的巩固练习记录。
*   **期望结果:** 用户成功从上次中断的题目继续巩固练习。
*   **异常与边界情况:**
    *   ⚠️ 练习进度数据加载失败：提示错误，引导用户重试或作为新练习开始。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户在学科学习地图] --> B{点击曾中途退出的巩固练习模块};
    B -- 显示详情 --> C[展示预估题数和'继续练习'按钮];
    C --> D{点击'继续练习'按钮};
    D --> E[跳转至继续练习转场界面];
```

###### [场景3：查看已完成练习的报告]
作为一名**学生**，我需要**在完成巩固练习后查看练习报告**以解决**不清楚自己练习表现和薄弱点的问题**，从而**了解学习成果并为后续学习提供方向**。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 在学科学习地图中，点击已完成的“巩固练习”模块（该模块会显示“已完成”icon和获得的星星）。
    *   **界面变化:** 巩固练习模块展开，显示【看报告】和【再练一次】按钮。
    *   **即时反馈:** 无特殊即时反馈。
2.  **用户操作2:** 点击【看报告】按钮。
    *   **界面变化:** 页面跳转至“学习报告”页面（详见[核心功能点5]）。
    *   **即时反馈:** 按钮点击态，页面开始加载报告。

**场景细节补充：**
*   **前置条件:** 用户已登录，并有已完成的巩固练习记录。
*   **期望结果:** 用户成功查看到该次巩固练习的学习报告。
*   **异常与边界情况:**
    *   ⚠️ 报告数据加载失败：提示错误，引导用户重试或返回。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户在学科学习地图] --> B{点击已完成的巩固练习模块};
    B -- 显示详情 --> C[展示'看报告'和'再练一次'按钮];
    C --> D{点击'看报告'按钮};
    D --> E[跳转至学习报告页面];
```

###### [场景4：已完成练习后选择再练一次]
作为一名**学生**，我可能需要**对已完成的巩固练习进行再次练习**以解决**希望进一步巩固或挑战更高正确率的问题**，从而**深化知识掌握**。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 在学科学习地图中，点击已完成的“巩固练习”模块。
    *   **界面变化:** 巩固练习模块展开，显示【看报告】和【再练一次】按钮。
    *   **即时反馈:** 无特殊即时反馈。
2.  **用户操作2:** 点击【再练一次】按钮。
    *   **界面变化:** 页面跳转至“进入练习转场”动画，开始一次新的练习流程（详见[核心功能点2]和[核心功能点6]）。
    *   **即时反馈:** 按钮点击态，页面开始加载转场。

**场景细节补充：**
*   **前置条件:** 用户已登录，并有已完成的巩固练习记录。
*   **期望结果:** 用户成功开始一次新的巩固练习（再练一次）。
*   **异常与边界情况:**
    *   ⚠️ "再练一次"的题目获取失败：提示错误，引导用户重试或返回。

**优先级：** 高
**依赖关系：** 无

```mermaid
flowchart TD
    A[用户在学科学习地图] --> B{点击已完成的巩固练习模块};
    B -- 显示详情 --> C[展示'看报告'和'再练一次'按钮];
    C --> D{点击'再练一次'按钮};
    D --> E[跳转至进入练习转场界面（开始新的练习）];
```
---
#### **[核心功能点2]:** 进入练习转场
负责在用户首次进入练习或继续练习时，展示过渡动画和信息，提升体验。

##### **[核心功能点2.1]:** 首次进入与继续练习的转场效果
###### 界面布局与核心UI元素
-   **布局参考：**
    *   [首次进入练习转场](https://static.test.xiaoluxue.cn/demo41/prd_images/image_Co9ybh7b3o8bfHxkn6Wc5qOanrg.png) - 仅关注布局
    *   [继续练习转场](https://static.test.xiaoluxue.cn/demo41/prd_images/image_ZPWxbu2yxo7DIoxk3tucfxO0nRg.png) - 仅关注布局 (注意此图OCR解析为答题界面，但PRD文字描述的是继续练习转场，应以文字为准调整理解或假设其为转场后的界面示意，此处主要关注转场文案和IP形象)

-   **布局原则：**
    *   **首次进入练习转场 (image_Co9ybh7b3o8bfHxkn6Wc5qOanrg.png):** 界面以一个圆角矩形卡片为内容承载层，上方中部为引导性文本（如“开始练习”），下方为主题文本（如“《课程名称》”）。卡片右上角可能有装饰性图标，右下角为IP卡通形象。背景为浅色，带有与学习相关的浅色图标。
    *   **继续练习转场 (image_ZPWxbu2yxo7DIoxk3tucfxO0nRg.png - 依据文字描述调整):** 转场界面应与首次进入类似，主要差异在于引导性文本（如“继续练习”）和可能出现的IP卡通形象的动效。
    *   整体上，转场界面应简洁、聚焦，通过IP形象和动效营造轻松、积极的氛围。

-   **核心UI元素清单：**
    *   IP角色动效
    *   引导文案（“开始练习：《课程名称》” 或 “继续练习：《课程名称》”）
    *   课程名称文本

###### 功能需求与交互原则
-   **功能描述：**
    *   **首次进入练习转场：**
        *   触发条件：用户在所有题目未作答时（例如从“进入练习”按钮）进入。
        *   IP角色动效出现。
        *   文案显示：“开始练习：《课程名称》”。
        *   课程名称过长时的显示规则需UI确定。
        *   展示时长：2秒。
        *   转场结束后，若有题组转场则进入题组转场，否则直接进入题目页。
    *   **继续练习转场：**
        *   触发条件：用户在练习状态为“练习中”时（例如从“继续练习”按钮）进入。
        *   IP角色动效出现。
        *   文案显示：“继续练习：《课程名称》”。
        *   课程名称过长时的显示规则需UI确定。
        *   展示时长：2秒。
        *   转场结束后，若有题组转场则进入题组转场，否则直接进入题目页。
    *   **加载失败兜底：** 若转场加载超过2秒未成功，应直接进入题目页。
-   **交互模式：**
    *   转场为自动播放的过渡效果，用户无需操作。
-   **反馈原则：**
    *   转场期间应有视觉上的动态效果（IP动效、文案加载等）。
    *   转场结束后平滑过渡到下一环节。

**优先级：** P0
**依赖关系：** [核心功能点1]

##### 用户场景与故事 (针对此核心功能点)

###### [场景1：用户首次开始某个巩固练习]
作为一名**学生**，我希望**在点击“开始练习”后有一个友好的过渡**以解决**直接进入题目可能带来的突兀感**，从而**让我感觉更轻松地开始学习**。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 用户在巩固练习入口点击【进入练习】。
    *   **界面变化:** 界面开始加载并显示“首次进入练习”的转场动画。IP角色出现并伴有动效，屏幕上显示文案“开始练习：《课程名称》”。
    *   **即时反馈:** 用户看到平滑的动画过渡。
2.  **系统行为:** 转场动画持续2秒。
    *   **界面变化:** 2秒后，转场动画结束，界面跳转至题组转场（如有）或直接跳转至第一道题目。
    *   **即时反馈:** 平滑过渡到下一学习环节。

**场景细节补充：**
*   **前置条件:** 用户选择了一个全新的巩固练习。
*   **期望结果:** 用户体验到流畅且友好的练习开始过渡。
*   **异常与边界情况:**
    *   转场资源加载缓慢，超过2秒：系统应自动跳过转场，直接进入题组转场或题目页，避免用户长时间等待。
    *   课程名称非常长：UI应有优雅截断或换行处理机制。

**优先级：** 高
**依赖关系：** [核心功能点1.1]

```mermaid
flowchart TD
    A[用户点击'进入练习'] --> B[加载并显示'首次进入练习'转场];
    B --> C{IP动效和文案展示};
    C -- 持续2秒 --> D{转场结束};
    D --> E{判断是否有题组};
    E -- 是 --> F[进入题组转场];
    E -- 否 --> G[进入题目页];
    B -- 加载超过2秒 --> E; 
    %% 加载失败兜底
```

###### [场景2：用户继续上次未完成的巩固练习]
作为一名**学生**，我希望**在点击“继续练习”后能快速回到上次的进度，并有一个简短的过渡提示**以解决**不确定是否回到正确位置的疑虑**，从而**无缝衔接上次的学习**。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 用户在巩固练习入口点击【继续练习】。
    *   **界面变化:** 界面开始加载并显示“继续练习”的转场动画。IP角色出现并伴有动效，屏幕上显示文案“继续练习：《课程名称》”。
    *   **即时反馈:** 用户看到平滑的动画过渡。
2.  **系统行为:** 转场动画持续2秒。
    *   **界面变化:** 2秒后，转场动画结束，界面跳转至题组转场（如有，且上次在该题组中断）或直接跳转至上次中断的题目。
    *   **即时反馈:** 平滑过渡到上次学习的环节。

**场景细节补充：**
*   **前置条件:** 用户选择了一个上次中途退出的巩固练习。
*   **期望结果:** 用户体验到流畅的练习恢复过渡，并准确回到上次中断的位置。
*   **异常与边界情况:**
    *   转场资源加载缓慢，超过2秒：系统应自动跳过转场，直接进入题组转场或题目页。
    *   课程名称非常长：UI应有优雅截断或换行处理机制。

**优先级：** 高
**依赖关系：** [核心功能点1.1]

```mermaid
flowchart TD
    A[用户点击'继续练习'] --> B[加载并显示'继续练习'转场];
    B --> C{IP动效和文案展示};
    C -- 持续2秒 --> D{转场结束};
    D --> E{判断是否有题组且上次在此中断};
    E -- 是 --> F[进入题组转场];
    E -- 否 --> G[进入上次中断的题目页];
    B -- 加载超过2秒 --> E; 
    %% 加载失败兜底
```

---
#### **[核心功能点3]:** 练习环节核心功能
负责练习过程中的页面框架设计、题目展示、作答交互、进度管理、辅助工具（勾画、错题本）以及熔断机制。

##### **[核心功能点3.1]:** 练习页面框架与导航
###### 界面布局与核心UI元素
-   **布局参考：**
    *   [练习中页面框架-题目展示](https://static.test.xiaoluxue.cn/demo41/prd_images/image_IQG9beGLHoBodyxmxPrcDF7Dn3b.png) - 仅关注布局
    *   [练习中页面框架-题目解析/反馈后](https://static.test.xiaoluxue.cn/demo41/prd_images/image_FsN1b2MTvoUUQwx6uwDc6eiMnFK.png) - 仅关注布局
    *   [退出确认弹窗](https://static.test.xiaoluxue.cn/demo41/prd_images/image_RIPGb6P1boVigPxDJq2c9LuUnYK.png) - 仅关注布局

-   **布局原则：**
    *   **练习中页面框架 (image_IQG9beGLHoBodyxmxPrcDF7Dn3b.png & image_FsN1b2MTvoUUQwx6uwDc6eiMnFK.png):** 整体答题页面包含顶部导航栏（返回按钮、计时器、答题进度条）、题目内容区域（题型及来源、题干文本、题干图片/图表）、选项区域（各选项按钮）、底部操作栏（如不确定、提交按钮）以及可能的浮动辅助工具（如勾画按钮）。题目和选项是核心展示区域。
    *   **退出确认弹窗 (image_RIPGb6P1boVigPxDJq2c9LuUnYK.png):** 弹窗应居中显示，叠加在当前练习页面之上。包含提示信息区域（主提示文本、辅助提示文本）和操作按钮区域（确认退出按钮、继续练习按钮）。

-   **核心UI元素清单：**
    *   顶部导航栏：
        *   退出按钮 (通常为 "X" 或 "<" 图标)
        *   作答计时器 (格式 00:00)
        *   作答进度条 (分子/分母形式)
    *   题型组件区域 (承载各类题型)
    *   退出确认弹窗：
        *   提示文案 ("确定要退出练习吗？进度已保存")
        *   【确认退出】按钮
        *   【继续练习】按钮

###### 功能需求与交互原则
-   **功能描述：**
    *   **顶部导航栏：**
        *   **退出按钮：**
            *   点击【退出按钮】弹出二次确认弹窗。
            *   弹窗文案：“确定要退出练习吗？进度已保存”。
            *   点击弹窗【确认退出】：退出至课程详情页。同时，自动保存当前进度，记录最后作答题目ID，更新练习状态为“练习中”，课程详情页计算并展示掌握度变化。
            *   点击弹窗【继续练习】：关闭弹窗，留在当前页面。
        *   **作答计时：**
            *   复用题型组件的计时功能。
            *   每题独立正计时，格式 00:00，上限 59:59。
            *   学生未作答退出时，暂停计时；回归后继续累计计时（例如，一道题看了5分钟未作答，退出后再进来，从5分钟开始计时）。
        *   **作答进度：**
            *   进度展示格式：X/Y （当前题数/总题数）。
            *   分母（Y）：预估学生作答数量（由推题策略提供）。
            *   分子（X）：依据推题策略动态更新：
                *   如果学生答对，进度（X）涨。
                *   如果学生答错，但没有相似题，进度（X）涨。
                *   如果学生答错，但有相似题需要再练一道，进度（X）不变。
    *   **题型组件：**
        *   各题型按统一标准展示和交互，细节参考《PRD - 题型支持 - 一期》。
    *   **题目推荐：**
        *   练习中的题目和顺序，遵循《PRD - 巩固练习相关策略 - V1.0》。
-   **交互模式：**
    *   用户通过点击退出按钮触发退出流程。
    *   计时器和进度条实时自动更新。
-   **反馈原则：**
    *   退出确认弹窗提供清晰的操作后果提示。
    *   计时和进度提供实时的状态反馈。

**优先级：** P0
**依赖关系：** [核心功能点2], 题型组件, 推题策略

##### **[核心功能点3.2]:** 题组转场
###### 界面布局与核心UI元素
-   **布局参考：** [题组转场示意图](https://static.test.xiaoluxue.cn/demo41/prd_images/image_GEmEbgKgwoVf2lxw3V3clW90ndd.png) - 仅关注布局

-   **布局原则：**
    *   **题组转场 (image_GEmEbgKgwoVf2lxw3V3clW90ndd.png):** 界面中央或显著位置展示当前题组的名称和预估题目数量。下方或一侧可能会有指示当前是第几个题组的圆点指示器。整体风格应与进入练习转场保持一致，简洁明了。

-   **核心UI元素清单：**
    *   题组名称文本
    *   题组预估题目数量文本（“约 N 题”）
    *   题组切换指示器（例如圆点，表示总题组数和当前题组位置）

###### 功能需求与交互原则
-   **功能描述：**
    *   **展示判断：** 当课程中有两个及以上的题组时，展示题组转场。如果仅有一个题组，则不展示。
    *   **展示时机：** 在“进入/继续练习转场”后，下一题组开始前，出现当前题组的转场。
    *   **内容显示：**
        *   题组名称：取选题时对应题组的名称。
        *   题组预估题目：格式为“约 N 题”，N由推题策略提供。
        *   题组数量指示器（转场上圆点数量）：
            *   数量取自本次巩固练习包含的题组总数。
            *   一屏最多展示3个圆点（若超过3个，需UI确认交互方式，如滚动或省略）。
    *   **数据来源：**
        *   题组名称和数量：取自本次巩固练习包含的题组信息。
        *   题组内题目：取自本次巩固练习对应题组内推荐的题目，题目的全集来自于内容平台选题时题组内的题目。
-   **交互模式：**
    *   题组转场通常为自动播放的过渡效果，告知用户即将进入新的题组。
    *   可能伴有短暂的停留或动画效果后自动进入该题组的第一题。
-   **反馈原则：**
    *   清晰展示题组名称和题目数量，帮助用户建立预期。
    *   平滑过渡到题组的第一题。

**优先级：** P0
**依赖关系：** 推题策略, 内容平台题组数据

##### **[核心功能点3.3]:** 勾画组件
###### 界面布局与核心UI元素
-   **布局参考：** [勾画组件面板](https://static.test.xiaoluxue.cn/demo41/prd_images/image_H0V7bUXxko2eY3xdYA4cRjKXnec.png) - 仅关注布局

-   **布局原则：**
    *   **勾画组件面板 (image_H0V7bUXxko2eY3xdYA4cRjKXnec.png):** 勾画组件通常以悬浮面板或底部工具栏的形式出现。面板内包含颜色选择器（多种颜色、不同粗细的线条示例）、绘图/书写工具集（画笔、橡皮擦、删除、撤销、重做图标）和完成按钮。

-   **核心UI元素清单：**
    *   【勾画】悬浮按钮（常驻入口）
    *   勾画工具面板：
        *   颜色调整选项（如：蓝、红、黑）
        *   笔迹粗细调整选项（如：细、中、粗）
        *   橡皮擦工具按钮
        *   橡皮擦粗细调整选项（如：细、中、粗）
        *   撤回按钮
        *   恢复按钮
        *   一键清空按钮
        *   【完成】按钮（收起勾画组件）

###### 功能需求与交互原则
-   **功能描述：**
    *   **入口：** 作答界面常驻显示【勾画】悬浮按钮。
    *   **唤起与收起：**
        *   点击【勾画】按钮，唤起勾画组件面板。
        *   点击勾画面板内的【完成】按钮，收起勾画组件，同时自动保存勾画内容并将其展示在题目区域。
    *   **勾画工具：**
        *   **笔迹调整：**
            *   颜色调整：支持蓝色、红色、黑色。
            *   粗细调整：支持细、中等、粗三种级别。
        *   **橡皮擦：**
            *   粗细调整：支持细、中等、粗三种级别。
        *   **操作：**
            *   支持撤回上一步操作。
            *   支持恢复上一步撤回的操作。
            *   支持一键清空所有勾画内容。
    *   **内容保存与展示：**
        *   用户在勾画过程中的内容应实时显示。
        *   收起勾画组件时，当前勾画内容自动保存，并叠加显示在题目区域。
        *   ⚠️ 再次进入已勾画题目时，应能加载并显示之前的勾画内容。
-   **交互模式：**
    *   用户通过点击选择不同的工具、颜色、粗细。
    *   在题目区域进行手指或触控笔的拖动进行勾画或擦除。
-   **反馈原则：**
    *   所选工具、颜色、粗细应有明确的视觉选中态。
    *   勾画、擦除、撤销、恢复、清空操作应实时在界面上反馈结果。

**优先级：** P0
**依赖关系：** 无

##### **[核心功能点3.4]:** 错题本组件
###### 界面布局与核心UI元素
-   **布局参考：** (原始PRD未直接提供错题本组件面板的UI图，但描述了按钮和反馈)
    *   按钮位置：每道题底部。
    *   反馈提示：toast形式，如“已收藏进错题本”。
    *   错因标签弹窗：(推测)标准弹窗样式，包含标题、副标题、标签列表。

-   **布局原则：**
    *   【收藏进错题本】/【已加错题本】按钮应放置在每道题目的固定、易于操作的位置（如题目作答区域下方）。
    *   添加/移除成功的toast提示应在屏幕中上部或底部显示，不遮挡核心内容，并在2秒后自动消失。
    *   “修改错因标签”弹窗应为模态弹窗，居中显示，包含清晰的标题、副标题和可供选择的系统标签列表。

-   **核心UI元素清单：**
    *   【收藏进错题本】按钮
    *   【已加错题本】按钮 (收藏后的状态)
    *   Toast提示文案（例如“已收藏进错题本”，“已移除错题”）
    *   错因标签修改弹窗：
        *   标题：“修改错因标签”
        *   副标题：“标签的选择会影响智能推荐”
        *   系统标签列表（如：重点复习、没有思路等）
        *   确认/保存按钮
        *   取消/关闭按钮

###### 功能需求与交互原则
-   **功能描述：**
    *   **自动添加规则：**
        *   答错的题目自动添加到错题本。
        *   自动添加后，系统提示“已收藏进错题本”。
        *   对应题目的收藏按钮名称变为【已加错题本】。
        *   系统记录错误次数和最近错误时间。
    *   **手动添加：**
        *   对于正确作答的题目，用户可选择手动添加到错题本。
        *   界面位置：每道题底部设置【收藏进错题本】按钮。
        *   添加成功反馈：点击【收藏进错题本】后，按钮变为【已加错题本】，同时toast提示“已加错题本”，展示2秒。
    *   **取消收藏：**
        *   用户点击【已加错题本】按钮。
        *   按钮状态变更为【收藏进错题本】。
        *   移除成功反馈：toast提示“已移除错题”，展示2秒。
    *   **添加错因标签：**
        *   ⚠️ 触发时机：题目加入错题本后，（推测）可引导用户添加错因标签，或用户在错题本模块进行编辑。此处PRD描述不明确，暂定为点击【已加错题本】按钮后或在题目解析页提供入口。
        *   弹窗标题：“修改错因标签”。
        *   弹窗副标题：“标签的选择会影响智能推荐”。
        *   系统标签列表：重点复习、没有思路、概念理解不清、思路不清晰、没有分类讨论、方法运用错误、思维定式、忽略隐含条件。
        *   用户选择标签后保存。
-   **交互模式：**
    *   通过点击按钮进行收藏/取消收藏。
    *   通过弹窗选择错因标签。
-   **反馈原则：**
    *   操作成功后有明确的toast提示和按钮状态变化。
    *   错因标签选择后，应有保存成功的反馈。

**优先级：** P0
**依赖关系：** 用户作答结果

##### **[核心功能点3.5]:** 练习熔断机制
###### 界面布局与核心UI元素
-   (无特定UI图，通常为逻辑判断后的流程中止或特定提示)
-   **核心UI元素清单：**
    *   （可能）熔断触发时的提示信息（如弹窗或toast）
    *   （可能）引导用户结束练习或查看报告的按钮

###### 功能需求与交互原则
-   **功能描述：**
    *   熔断规则遵循《PRD - 巩固练习相关策略 - V1.0》文档中的具体定义（链接：`https://wcng60ba718p.feishu.cn/docx/JYjedBulhoHiBDx1wGScCacHnrd#share-N4BHdyohpomiq1x0cHTcB8YTnYf`）。
    *   当前端判断满足熔断条件时，应中止当前练习流程。
    *   ⚠️ 熔断触发后的具体前端表现（例如：是直接跳转到报告页，还是弹出提示框告知用户练习已结束，并提供“查看报告”按钮）需根据策略文档细化。
-   **交互模式：**
    *   熔断是系统根据练习情况自动触发的机制。
-   **反馈原则：**
    *   若有用户感知的熔断提示，应清晰告知用户练习已结束的原因（或直接结束）。
    *   引导用户进行下一步操作（如查看报告）。

**优先级：** P0
**依赖关系：** 推题策略, 用户作答数据

##### 用户场景与故事 (针对练习环节核心功能)

###### [场景1：学生在练习中途需要退出]
作为一名**学生**，我可能因为临时有事需要**中途退出练习**以解决**无法一次性完成所有题目的问题**，同时希望**练习进度能够保存，方便下次继续**。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 在练习界面，点击顶部的【退出】按钮。
    *   **界面变化:** 弹出二次确认弹窗，文案为“确定要退出练习吗？进度已保存”。弹窗包含【确认退出】和【继续练习】按钮。
    *   **即时反馈:** 弹窗出现，背景练习界面变暗或模糊。
2.  **用户操作2 (选择确认退出):** 点击弹窗中的【确认退出】按钮。
    *   **界面变化:** 弹窗关闭，页面跳转回课程详情页。当前练习进度被保存，练习状态更新为“练习中”。
    *   **即时反馈:** 页面平滑跳转。课程详情页的相关巩固练习入口状态更新（如有掌握度变化会显示）。
3.  **用户操作2 (选择继续练习):** 点击弹窗中的【继续练习】按钮。
    *   **界面变化:** 弹窗关闭，返回到当前练习题目界面。
    *   **即时反馈:** 用户可以继续作答。

**场景细节补充：**
*   **前置条件:** 用户正在进行巩固练习。
*   **期望结果:** 用户可以顺利退出练习并保存进度，或取消退出操作。
*   **异常与边界情况:**
    *   ⚠️ 保存进度失败：应有友好提示，并询问用户是否仍要退出（可能丢失少量进度）。

**优先级：** 高
**依赖关系：** 核心功能点3.1

```mermaid
flowchart TD
    A[用户在练习中点击'退出'按钮] --> B[弹出'确定要退出练习吗？进度已保存'确认框];
    B -- 点击'确认退出' --> C{保存进度};
    C -- 成功 --> D[跳转至课程详情页];
    C -- 失败 --> E[提示保存失败，询问是否仍退出];
    B -- 点击'继续练习' --> F[关闭弹窗，返回当前题目];
```

###### [场景2：学生使用勾画工具辅助解题]
作为一名**学生**，我需要在**解题时进行草稿勾画**以解决**复杂题目仅靠心算难以理清思路的问题**，从而**提高解题的准确性和效率**。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 在作答界面，点击悬浮的【勾画】按钮。
    *   **界面变化:** 勾画工具面板从底部或侧边滑出/显示。
    *   **即时反馈:** 工具面板出现。
2.  **用户操作2:** 在工具面板选择颜色（如红色）、笔迹粗细（如中等）。
    *   **界面变化:** 所选颜色和粗细在工具面板上高亮显示。
    *   **即时反馈:** 选择状态更新。
3.  **用户操作3:** 在题目区域进行勾画。
    *   **界面变化:** 题目区域根据用户的触摸轨迹实时显示红色中等粗细的笔迹。
    *   **即时反馈:** 实时绘制。
4.  **用户操作4:** 点击工具面板中的【橡皮擦】工具，选择粗细，擦除部分勾画。
    *   **界面变化:** 笔迹被擦除。
    *   **即时反馈:** 实时擦除。
5.  **用户操作5:** 点击工具面板中的【完成】按钮。
    *   **界面变化:** 勾画工具面板收起，当前勾画内容保留并显示在题目区域。
    *   **即时反馈:** 面板消失，勾画内容固化。

**场景细节补充：**
*   **前置条件:** 用户正在作答某道题目。
*   **期望结果:** 用户能够顺畅使用勾画工具辅助思考和作答。
*   **异常与边界情况:**
    *   ⚠️ 勾画内容过多导致性能下降：前端应有性能优化考虑。

**优先级：** 高
**依赖关系：** 核心功能点3.3

```mermaid
sequenceDiagram
    participant U as 用户
    participant P as 练习界面
    participant TP as 工具面板

    U->>P: 点击【勾画】按钮
    P->>TP: 显示勾画工具面板
    U->>TP: 选择颜色、粗细
    TP-->>P: 更新画笔设置
    U->>P: 在题目区勾画
    P-->>P: 实时显示笔迹
    U->>TP: 点击【完成】
    TP-->>P: 收起工具面板
    P-->>P: 保存并显示勾画内容
```

###### [场景3：学生将答错的题目自动或手动加入错题本]
作为一名**学生**，我希望**答错的题目能自动加入错题本，对正确的题目也能手动收藏**以解决**后续复习薄弱点不方便的问题**，从而**有针对性地进行复习，提高学习效率**。

**前端界面与交互关键步骤：**
1.  **系统行为 (自动添加):** 学生提交某题答案后，系统判定为错误。
    *   **界面变化:** 题目下方【收藏进错题本】按钮自动变为【已加错题本】状态。屏幕弹出toast提示“已收藏进错题本”。
    *   **即时反馈:** 按钮状态更新，toast提示出现2秒后消失。
2.  **用户操作1 (手动添加):** 学生提交某题答案后，系统判定为正确。学生点击题目下方的【收藏进错题本】按钮。
    *   **界面变化:** 按钮文字变为【已加错题本】。屏幕弹出toast提示“已加错题本”。
    *   **即时反馈:** 按钮状态更新，toast提示出现2秒后消失。
3.  **用户操作2 (取消收藏):** 学生点击已是【已加错题本】状态的按钮。
    *   **界面变化:** 按钮文字变回【收藏进错题本】。屏幕弹出toast提示“已移除错题”。
    *   **即时反馈:** 按钮状态更新，toast提示出现2秒后消失。
4.  **用户操作3 (添加错因标签 - 假设入口在点击“已加错题本”后或题目解析页):** 用户触发错因标签编辑。
    *   **界面变化:** 弹出“修改错因标签”弹窗，显示系统预设标签列表。
    *   **即时反馈:** 弹窗出现。
5.  **用户操作4:** 在弹窗中选择一个或多个错因标签，点击保存。
    *   **界面变化:** 弹窗关闭。
    *   **即时反馈:** ⚠️（应有保存成功提示或状态反馈，原始PRD未明确）。

**场景细节补充：**
*   **前置条件:** 用户正在作答或查看题目。
*   **期望结果:** 用户可以方便地管理错题，为后续复习做准备。
*   **异常与边界情况:**
    *   ⚠️ 添加/移除错题本操作时网络异常：应有失败提示，按钮状态回滚。

**优先级：** 高
**依赖关系：** 核心功能点3.4

```mermaid
flowchart TD
    subgraph "自动添加错题"
        A[用户答错题目] --> B{系统自动添加至错题本};
        B --> C[按钮变为'已加错题本'];
        B --> D[Toast提示 '已收藏进错题本'];
    end
    subgraph "手动操作错题本"
        E[用户查看题目] --> F{点击'收藏进错题本'};
        F --> G[按钮变为'已加错题本'];
        F --> H[Toast提示 '已加错题本'];
        G --> I{点击'已加错题本'};
        I --> J[按钮变回'收藏进错题本'];
        I --> K[Toast提示 '已移除错题'];
    end
    subgraph "添加错因"
        L[触发添加错因] --> M[弹出'修改错因标签'弹窗];
        M --> N{用户选择标签并保存};
        N --> O[关闭弹窗，保存错因];
    end
```

---
#### **[核心功能点4]:** 题目间转场与反馈
负责在用户作答题目后，根据作答结果（正确、错误、连续正确、部分正确）和系统判断（不认真作答、难度调整）给出即时反馈和转场效果。

##### **[核心功能点4.1]:** 作答结果反馈
###### 界面布局与核心UI元素
-   **布局参考：**
    *   [作答正确反馈](https://static.test.xiaoluxue.cn/demo41/prd_images/image_PgGRbjgxsogDxgxmJApcopqonKc.png) - 仅关注反馈弹窗/效果
    *   [作答错误反馈](https://static.test.xiaoluxue.cn/demo41/prd_images/image_OmLGbk0H6ojvoQxgdt7cHNb0nGb.png) - 仅关注反馈弹窗/效果
    *   [连续正确反馈](https://static.test.xiaoluxue.cn/demo41/prd_images/image_I2Dwb6LSwoNP4LxRXNzcleQHn6e.png) - 仅关注反馈弹窗/效果
    *   (部分正确反馈无单独UI图，推测与正确/错误反馈类似，IP形象和文案不同)

-   **布局原则：**
    *   **各类作答反馈 (image_PgGRbjgxsogDxgxmJApcopqonKc.png, image_OmLGbk0H6ojvoQxgdt7cHNb0nGb.png, image_I2Dwb6LSwoNP4LxRXNzcleQHn6e.png):** 反馈通常以非模态弹窗、全屏动画或Toast形式出现，在用户提交答案后、进入下一题前展示。包含IP角色、音效（未在UI体现但PRD提及）和文案。反馈应在视觉上醒目，情感上积极或安慰。连续正确反馈可能更强调激励和成就感。

-   **核心UI元素清单：**
    *   IP角色动画/静态图
    *   反馈文案
    *   （隐性）音效
    *   积分获得数量文本（如适用）
    *   连胜次数文本（如适用）

###### 功能需求与交互原则
-   **功能描述：**
    *   **转场顺序逻辑图参考：** [转场顺序逻辑图](https://static.test.xiaoluxue.cn/demo41/prd_images/board_LTzNwdc9ehjdhwbltjEcan8rnDg.png)
    *   **弹出时机：** 每次作答自动批改/自评后触发，以下四种（正确、错误、连续正确、部分正确）仅弹一种。
    *   **展示时长：** 2秒。
    *   **反馈类型与内容：**
        *   **正确反馈 (P1)：**
            *   IP角色 + 音效 + 随机文案之一：
                *   “完美无缺！ + 积分获得数量”
                *   “进步神速！ + 积分获得数量”
                *   “你真的太棒了！ + 积分获得数量”
                *   “nice！正确！ + 积分获得数量”
                *   “优秀优秀！ + 积分获得数量”
        *   **错误反馈 (P1)：**
            *   IP角色（如Dron安慰动作） + 音效 + 随机文案之一：
                *   “别灰心，继续！”
                *   “别着急，慢慢来～”
                *   “小错误，大进步！”
                *   “这道题像老朋友，再认认？” (针对学生做过的错题再练)
                *   “偶尔失误很正常！” (一个题错误时、连对中断时)
        *   **连续正确反馈 (P0，优先于普通正确反馈)：**
            *   IP角色（Dolli热血动作） + 不同连胜音效（2次以上连胜对应不同音效） + 随机文案之一：
                *   “[N 连胜] + 断连？不存在的～ + 积分获得数量”
                *   “[N 连胜] + 连胜守护成功！ + 积分获得数量”
                *   “[N 连胜] + 连战连胜！ + 积分获得数量”
                *   “[N 连胜] + 越战越勇！ + 积分获得数量”
                *   “[N 连胜] + 稳定输出，超棒！ + 积分获得数量”
        *   **部分正确反馈 (P1)：**
            *   IP角色（如Dron鼓励动作） + 音效 + 随机文案之一：
                *   “部分正确，再补一步！”
                *   “接近完美，只差一点！”
-   **交互模式：**
    *   反馈自动弹出并展示2秒后自动消失，然后进入下一题或下一反馈环节。
-   **反馈原则：**
    *   反馈内容积极、鼓励，符合对应场景。
    *   视觉和听觉（音效）结合，增强反馈效果。

**优先级：** P0
**依赖关系：** 用户作答结果, 积分系统 (如有)

##### **[核心功能点4.2]:** 特殊反馈（不认真作答、难度变化）
###### 界面布局与核心UI元素
-   **布局参考：**
    *   [不认真作答反馈](无特定UI图，描述为轻提示，不遮挡题目)
    *   [难度上升反馈](https://static.test.xiaoluxue.cn/demo41/prd_images/image_JbUVbLSsdojkJzxKGGbcmN0Gnka.png) - 仅关注反馈样式
    *   [难度下降反馈](https://static.test.xiaoluxue.cn/demo41/prd_images/image_Lw0XbrQEEo4xbWx099Ucbz5Vn2b.png) - 仅关注反馈样式 (注意此图OCR为排名上升，但PRD描述为难度下降反馈，以文字为准)

-   **布局原则：**
    *   **不认真作答反馈:** 应为轻量级提示，如页面顶部/底部的toast或小banner，不遮挡题目内容，在用户切换至下一题时、在作答前出现。
    *   **难度变化反馈 (image_JbUVbLSsdojkJzxKGGbcmN0Gnka.png & image_Lw0XbrQEEo4xbWx099Ucbz5Vn2b.png):** 全屏展示，遮挡题目，包含IP角色、音效和文案。风格应与难度变化方向（上升/下降）匹配，上升更具挑战感，下降更显轻松。

-   **核心UI元素清单：**
    *   **不认真作答反馈：**
        *   提示框/Toast
        *   提示文案
    *   **难度变化反馈：**
        *   全屏背景/动画
        *   IP角色动画/静态图
        *   反馈文案
        *   （隐性）音效

###### 功能需求与交互原则
-   **功能描述：**
    *   **不认真作答反馈：**
        *   判定规则：遵循《PRD - 巩固练习相关策略 - V1.0》。
        *   触发时机 (P1)：命中规则时，在用户切换至下一题时、在作答前出现。若无下一题（最后一题），则不触发。
        *   展示时长：2秒。
        *   提示样式：轻题型，弹出提示框，不遮挡题目。
        *   随机文案之一：
            *   “认真作答有助于掌握度提升”
            *   “分心会迷路哦～”
    *   **难度变化反馈：**
        *   触发时机 (P0，优先于不认真作答反馈)：当依据推荐策略，题目难度调整时，在下一题出现前展示。
        *   展示时长：2秒。
        *   提示样式：全屏展示，遮挡题目。
        *   **难度上升反馈：**
            *   IP角色 + 音效 (UI确定) + 随机文案之一：
                *   “挑战升级！”
                *   “难度 + 1，继续冲！”
                *   “小试牛刀？现在才是正餐！”
                *   “精英题库已解锁！”
                *   “难度持续上升，勇攀更高峰！” (当难度上升三次及以上时展示)
        *   **难度下降反馈：**
            *   IP角色 + 音效 (UI确定) + 随机文案之一：
                *   “阶梯下降，轻松跨越！”
                *   “坡度放缓，稳步积累！”
                *   “难度下降，再试试吧～”
                *   “难度持续下降，相信这次你一定可以！” (当难度下降三次及以上时展示，⚠️此处原文为“当难度上升三次及以上”，疑为笔误，应指持续下降)
-   **交互模式：**
    *   反馈自动弹出并展示2秒后自动消失，然后进入下一题。
-   **反馈原则：**
    *   反馈与对应场景（不认真、难度升/降）强相关，引导用户行为或调整用户预期。
    *   视觉和听觉（音效）结合，增强反馈效果。

**优先级：** P0/P1 (按类型)
**依赖关系：** 推题策略, 用户作答行为分析

##### 用户场景与故事 (针对题目间转场与反馈)

###### [场景1：学生连续答对题目获得连胜反馈]
作为一名**学生**，我希望**在连续答对题目时能获得更强的激励反馈**以解决**普通正确反馈激励效果有限的问题**，从而**增强我的成就感和学习动力**。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 学生连续提交了N题（N≥2）的正确答案。在提交第N题正确答案后：
    *   **界面变化:** 系统优先展示“连续正确反馈”。IP角色（Dolli热血动作）出现，配合特定连胜音效，屏幕显示如“[N 连胜] + 连战连胜！ + 积分获得数量”的文案。
    *   **即时反馈:** 强烈的视觉和听觉激励。
2.  **系统行为:** 连续正确反馈展示2秒。
    *   **界面变化:** 反馈消失，判断是否有难度变化或不认真作答反馈，若无则直接加载下一题。
    *   **即时反馈:** 平滑过渡。

**场景细节补充：**
*   **前置条件:** 用户正在练习，并且连续答对了至少2道题。
*   **期望结果:** 用户感受到强烈的正向激励，更有动力继续。
*   **异常与边界情况:**
    *   ⚠️ 连胜计数在何时重置（如答错一题后，或退出练习后）需明确。

**优先级：** 高 (P0)
**依赖关系：** 核心功能点4.1

```mermaid
flowchart TD
    A[用户提交第N题答案] --> B{系统判断是否正确};
    B -- 正确 --> C{是否构成连胜（N>=2）?};
    C -- 是 --> D[展示'连续正确反馈'（IP、音效、文案）];
    D -- 持续2秒 --> E[反馈消失，进入下一环节/题目];
    C -- 否 --> F[展示普通'正确反馈'];
    F -- 持续2秒 --> E;
    B -- 错误 --> G[展示'错误反馈'];
    G -- 持续2秒 --> E;
```

###### [场景2：系统检测到学生可能不认真作答，给出提示]
作为一名**学生**（从系统角度看是需要引导的学生），系统需要**在我可能出现不认真作答时给出提醒**以解决**因不认真导致学习效果不佳的问题**，从而**引导我更专注地练习**。

**前端界面与交互关键步骤：**
1.  **系统行为:** 学生完成一道题后，系统根据策略判断其可能存在不认真作答行为。在加载下一题之前：
    *   **界面变化:** 若无更高优先级的难度变化反馈，则在题目区域上方或下方弹出轻提示框，显示如“认真作答有助于掌握度提升”的文案。提示框不遮挡题目核心内容。
    *   **即时反馈:** 轻量提示出现。
2.  **系统行为:** 不认真作答反馈展示2秒。
    *   **界面变化:** 反馈消失，然后加载并显示下一道题目。
    *   **即时反馈:** 平滑过渡到下一题。

**场景细节补充：**
*   **前置条件:** 系统不认真作答策略被触发。
*   **期望结果:** 学生接收到提醒，可能会调整后续的作答态度。
*   **异常与边界情况:**
    *   如果当前是最后一题，则不触发此反馈。

**优先级：** 中 (P1)
**依赖关系：** 核心功能点4.2, 不认真作答策略

```mermaid
flowchart TD
    A[用户完成上一题] --> B{系统判断是否触发'不认真作答'策略};
    B -- 是 --> C{是否有更高优先级的'难度反馈'?};
    C -- 否 --> D[展示'不认真作答'轻提示];
    D -- 持续2秒 --> E[反馈消失，加载下一题];
    C -- 是 --> F[优先展示'难度反馈', 然后再判断是否展示'不认真作答']; 
%% 参照转场顺序图
    F --> E;
    B -- 否 --> E;
```

###### [场景3：题目难度发生变化，系统给出全屏提示]
作为一名**学生**，我希望**在练习题目难度发生显著变化时得到明确告知**以解决**突然遇到过难或过易题目可能产生的困惑或挫败感**，从而**更好地调整心态和预期，适应练习节奏**。

**前端界面与交互关键步骤：**
1.  **系统行为:** 学生完成一道题后，系统根据推题策略决定下一题的难度将发生变化（上升或下降）。在加载下一题之前：
    *   **界面变化:** 屏幕全屏展示“难度变化反馈”。例如难度上升时，IP角色骑火箭，文案“挑战升级！”；难度下降时，IP角色轻松状，文案“阶梯下降，轻松跨越！”。
    *   **即时反馈:** 强烈的视觉和听觉提示。
2.  **系统行为:** 难度变化反馈展示2秒。
    *   **界面变化:** 反馈消失，判断是否有不认真作答反馈（根据转场顺序图，难度变化优先），若无则直接加载调整了难度后的下一道题目。
    *   **即时反馈:** 平滑过渡到下一题。

**场景细节补充：**
*   **前置条件:** 系统推题策略决定调整下一题难度。
*   **期望结果:** 学生了解难度的变化，有心理准备。
*   **异常与边界情况:**
    *   如果和不认真作答反馈同时触发，难度变化优先展示。

**优先级：** 高 (P0)
**依赖关系：** 核心功能点4.2, 推题策略

```mermaid
flowchart TD
    A[用户完成上一题] --> B{系统判断下一题难度是否变化};
    B -- 是 --> C[全屏展示相应'难度变化反馈'（上升/下降）];
    C -- 持续2秒 --> D{反馈消失，判断是否触发'不认真作答'};
    D -- 否 --> E[加载调整难度后的下一题];
    D -- 是 --> F[展示'不认真作答'反馈后进入下一题];
    F --> E;
    B -- 否 --> E;
```
---
#### **[核心功能点5]:** 学习报告
负责在用户完成一次完整的练习（或再练一次）后，展示本次练习的数据总结、掌握度变化、知识点分析等，并提供后续操作入口。

##### **[核心功能点5.1]:** 练习结算与报告展示
###### 界面布局与核心UI元素
-   **布局参考：**
    *   [学习报告页/练习结算页](https://static.test.xiaoluxue.cn/demo41/prd_images/image_WC4gb8MsSoqY17xOJ5ycXN3Tn2g.png) - 仅关注布局
    *   [报告页题目详情列表项](https://static.test.xiaoluxue.cn/demo41/prd_images/image_BRCsbahKeoSKN3xBspNcFlGHnRI.png) - 仅关注布局

-   **布局原则：**
    *   **学习报告页 (image_WC4gb8MsSoqY17xOJ5ycXN3Tn2g.png):** 界面分为左右两块或上下结构。一块为激励与装饰区（IP形象、奖励元素、赞赏性文本）。另一块为学习数据与操作区，包含核心数据统计（学习用时、答题数、正确率）、掌握度模块（提升描述、进度条、答题详情链接）、成长值模块（获得星数）、用户反馈模块（本节课感受）以及操作按钮（再练一次、完成）。
    *   **报告页题目详情列表项 (image_BRCsbahKeoSKN3xBspNcFlGHnRI.png):** 在报告页的“答题详情”部分，题目列表以卡片形式展示，每个题目项包含题号、题型标签、题干摘要、作答状态（正确/错误图标和文字），并提供查看解析的入口。

-   **核心UI元素清单：**
    *   IP形象 + 激励文案（根据正确率变化）
    *   本次掌握度变化动效/文本
    *   本次学习时长文本
    *   本次答题数量文本
    *   本次正确率文本
    *   本次获得积分文本
    *   题目列表（展示题号、作答结果，支持点击查看详情）
        *   题目详情区域（显示题目、答案、解析）
    *   推荐学习模块（根据策略提供）
    *   【完成】按钮
    *   【再练一次】按钮

###### 功能需求与交互原则
-   **功能描述：**
    *   **练习完成转场：** (PRD中此部分被划掉，但符合逻辑，暂保留)
        *   ~~触发时机：学生完成最后一道题目并点击【继续】按钮后。~~ (应为自动进入结算页)
        *   ~~转场内容：显示报告生成加载进度，文案提示：“练习完成，报告生成中...”~~
    *   **练习结算页入口：**
        *   首次进入：练习完成后自动跳转至练习结算页。
        *   非首次进入：当练习已完成时，从巩固练习入口点击【看报告】进入。
    *   **内容展示：**
        *   **IP + 文案：**
            *   正确率 >= 90%：IP（dolli）+ 文案（完美通关!）
            *   正确率 >= 60% 且 < 90%：IP（dolli）+ 文案（干的不错!）
            *   正确率 < 60%：IP（dron）+ 文案（再接再厉!）
        *   **本次掌握度变化：** 动效展示上涨或下降幅度。
        *   **本次学习时长：** 本次学习的总时长（单位：分）。
        *   **本次答题数量：** 本次完成的题目总数（单位：题）。
        *   **本次正确率：** 本次正确题目数 / 总题目数（单位：%）。
        *   **本次获得积分：** 本次练习获得的积分数量。
        *   **题目作答结果列表：**
            *   展示练习中所有题目的题号和对应的作答结果（对/错图标）。
            *   支持点击查看详情：展开/跳转后显示该题的完整题目、用户答案、正确答案、详细解析。
            *   题干在列表中可简略显示（如10字，超出部分用...）。
            *   作答、答案和解析在列表中默认隐藏，点击题目项后展开或跳转显示。
        *   **推荐学习：** 具体规则由策略《巩固练习相关策略》提供。
    *   **交互：**
        *   点击【完成】按钮：关闭结算页，回到对应的课程详情页。
        *   点击【再练一次】按钮：进入“再练一次”的练习环节（详见[核心功能点6]）。
-   **交互模式：**
    *   用户通过浏览查看报告内容。
    *   通过点击按钮进行后续操作。
    *   点击题目列表项可查看题目详情。
-   **反馈原则：**
    *   数据展示清晰、准确。
    *   IP和文案根据表现提供恰当的情感反馈。
    *   操作按钮点击后有及时响应。

**优先级：** P0
**依赖关系：** 用户完成练习, 推题策略 (用于推荐学习)

##### 用户场景与故事 (针对此核心功能点)

###### [场景1：学生完成一次巩固练习后查看学习报告]
作为一名**学生**，我希望**在完成练习后能立即看到自己的表现总结**以解决**对学习成果不明确的问题**，从而**了解自己的强项和弱项，并获得成就感或改进方向**。

**前端界面与交互关键步骤：**
1.  **系统行为:** 学生完成练习的最后一道题并提交后。
    *   **界面变化:** （若有加载转场，则先显示“报告生成中...”）界面自动跳转至“练习结算页”。
    *   **即时反馈:** 页面平滑过渡。
2.  **界面展示 (练习结算页):**
    *   根据本次练习的正确率，显示对应的IP形象和激励文案（如“完美通关！”）。
    *   展示本次掌握度变化、学习时长、答题数量、正确率、获得积分等数据。
    *   下方列出本次练习的所有题目及其作答结果（对/错）。
    *   显示【完成】和【再练一次】按钮。
    *   （可能）显示“推荐学习”模块。
3.  **用户操作1:** 点击某一道题目的条目。
    *   **界面变化:** 该题目条目展开，显示完整的题干、用户答案、正确答案和解析。或者跳转到新的题目详情视图。
    *   **即时反馈:** 题目详情出现。
4.  **用户操作2:** 点击【完成】按钮。
    *   **界面变化:** 关闭学习报告，返回到课程详情页。
    *   **即时反馈:** 页面跳转。

**场景细节补充：**
*   **前置条件:** 用户刚刚完成了一次巩固练习的所有题目。
*   **期望结果:** 用户清晰了解本次练习的各项数据，并能方便地查看错题详情。
*   **异常与边界情况:**
    *   报告数据计算错误或加载失败：应有友好提示，并允许用户尝试刷新或稍后查看。

**优先级：** 高
**依赖关系：** 无 (指从用户完成练习到查看报告的流程)

```mermaid
flowchart TD
    A[用户完成最后一道题并提交] --> B[系统自动批改并计算结果];
    B --> C[（可选）显示'报告生成中...'转场];
    C --> D[跳转并展示'练习结算页'];
    D --> E{IP形象、激励文案、各项数据展示};
    D --> F[题目列表及作答结果展示];
    D --> G[显示'完成'和'再练一次'按钮];
    F --> H{点击某题目项};
    H --> I[展开/跳转显示该题详情（题目、答案、解析）];
    G --> J{点击'完成'按钮};
    J --> K[返回课程详情页];
    G --> L{点击'再练一次'按钮};
    L --> M[进入'再练一次'流程];
```

---
#### **[核心功能点6]:** 再练一次环节
在用户完成首次巩固练习后，根据其掌握情况，推荐进行“再练一次”，题目来源会针对薄弱知识点进行调整。

##### **[核心功能点6.1]:** “再练一次”的触发与执行
###### 界面布局与核心UI元素
-   **布局参考：**
    *   [练习结算页的“再练一次”入口强化](https://static.test.xiaoluxue.cn/demo41/prd_images/image_WC4gb8MsSoqY17xOJ5ycXN3Tn2g.png) - 关注【再练一次】按钮
    *   [课程详情页的“再练一次”入口](https://static.test.xiaoluxue.cn/demo41/prd_images/image_Q22sbx71moTZLtxxT56cTC0CnjK.png) - 关注巩固练习模块在满足再练条件时的入口变化，如按钮变为“再练一次”或增加提示。

-   **布局原则：**
    *   **练习结算页 (image_WC4gb8MsSoqY17xOJ5ycXN3Tn2g.png):** 若策略判断需要再练一次，【再练一次】按钮应被强化显示（如颜色、大小、动效）以吸引用户点击。
    *   **课程详情页 (image_Q22sbx71moTZLtxxT56cTC0CnjK.png):** 巩固练习模块的入口状态会更新。原先的【看报告】旁可能会并列或替换为【再练一次】按钮，或者在模块卡片上有明确的“推荐再练”之类的提示。

-   **核心UI元素清单：**
    *   【再练一次】按钮（在结算页、课程详情页巩固练习模块）
    *   （可能）“推荐再练”提示文本或角标
    *   进入“再练一次”的转场动画（同[核心功能点2]）
    *   “再练一次”中的练习界面（同[核心功能点3]）
    *   “再练一次”完成后的学习报告（同[核心功能点5]，数据会累加或特殊标记）

###### 功能需求与交互原则
-   **功能描述：**
    *   **触发规则：**
        *   触发条件：基于学生在巩固练习中的掌握度情况判断，具体规则见《PRD - 巩固练习相关策略 - V1.0》。
        *   入口强化：当策略判断需要“再练一次”时，练习报告页的【再练一次】按钮会得到强化推荐点击。
    *   **入口：**
        *   练习结算页 - 【再练一次】按钮（可能作为推荐学习的一部分）。
        *   课程详情页 - 学习路径中的巩固练习模块（状态更新后点击可进入“再练一次”）。
    *   **进入再练一次转场：**
        *   同[核心功能点2]的进入练习转场逻辑。
        *   首次进入“再练一次”：文案“开始练习：《课程名称》”，展示2秒。
        *   继续“再练一次”（若支持中途退出）：文案“继续练习：《课程名称》”，展示2秒。
    *   **内容和交互（练习过程）：**
        *   **题目来源：** 策略根据掌握薄弱的知识点，从该节课程对应的题库中捞取题目。
        *   **作答体验：** 与普通巩固练习的答题交互规范完全一致，包括：
            *   题组分组机制（如果“再练一次”的题目也分题组）
            *   作答反馈
            *   难度动态调整（如果“再练一次”也支持）
            *   勾画工具
            *   错题本功能
        *   **熔断机制：** “再练一次”环节同样有熔断机制，规则见《PRD - 巩固练习相关策略 - V1.0》。
    *   **学习报告页：**
        *   “再练一次”完成后，学习报告的展示方式与普通巩固练习一致，但数据可能是基于“再练一次”的表现，或者与首次练习的数据进行对比/累加（需策略明确）。
        *   推荐学习内容由策略提供。
-   **交互模式：**
    *   用户通过点击入口按钮开始“再练一次”。
    *   练习过程中的交互与普通巩固练习相同。
-   **反馈原则：**
    *   入口强化应明确引导用户。
    *   练习体验与反馈与普通巩固练习保持一致，确保用户体验的统一性。

**优先级：** P0
**依赖关系：** [核心功能点1], [核心功能点2], [核心功能点3], [核心功能点5], 推题策略

##### 用户场景与故事 (针对此核心功能点)

###### [场景1：学生在练习报告页被推荐并开始“再练一次”]
作为一名**学生**，我希望**在练习表现不佳时，系统能推荐我进行针对性的“再练一次”**以解决**知识点未完全掌握的问题**，从而**通过再次练习来巩固薄弱环节**。

**前端界面与交互关键步骤：**
1.  **系统行为:** 学生完成首次巩固练习，进入练习报告页。系统根据策略判断该学生需要进行“再练一次”。
    *   **界面变化:** 练习报告页上的【再练一次】按钮被突出显示（例如：颜色更鲜艳、增加动效或提示角标）。
    *   **即时反馈:** 视觉上引导用户点击。
2.  **用户操作1:** 点击被强化推荐的【再练一次】按钮。
    *   **界面变化:** 界面跳转至“开始练习”转场动画，文案为“开始练习：《课程名称》”。
    *   **即时反馈:** 页面开始加载新的练习。
3.  **后续流程:** 进入“再练一次”的练习环节，题目内容针对薄弱点调整。练习过程、反馈、熔断、最终报告与普通练习类似。

**场景细节补充：**
*   **前置条件:** 学生完成首次巩固练习，且策略判断需要“再练一次”。
*   **期望结果:** 用户被有效引导并顺利开始针对性的“再练一次”练习。
*   **异常与边界情况:**
    *   “再练一次”的题目获取失败：应提示用户，并允许返回报告页或课程详情。

**优先级：** 高
**依赖关系：** 核心功能点5.1

```mermaid
flowchart TD
    A[用户查看练习报告页] --> B{系统策略判断是否推荐'再练一次'};
    B -- 是 --> C[强化显示'再练一次'按钮];
    C --> D{用户点击'再练一次'按钮};
    D --> E[进入'开始练习'转场（针对再练一次）];
    E --> F[开始'再练一次'答题流程];
    F --> G[完成后展示'再练一次'的学习报告];
    B -- 否 --> H[正常显示'再练一次'按钮（无强化）];
```

###### [场景2：学生从课程详情页入口进入“再练一次”]
作为一名**学生**，我希望**在课程学习路径中，对于已完成但推荐“再练一次”的巩固练习，能有明确的入口**以解决**不知道如何再次进行针对性练习的问题**，从而**方便地进行复习和提升**。

**前端界面与交互关键步骤：**
1.  **系统行为:** 学生进入课程详情页（学习地图）。之前完成的某个巩固练习被策略标记为需要“再练一次”。
    *   **界面变化:** 该巩固练习模块的入口状态更新，例如按钮变为【再练一次】，或旁边有“推荐再练”的提示。
    *   **即时反馈:** 用户可以看到该练习有进一步操作的引导。
2.  **用户操作1:** 点击该巩固练习模块更新后的【再练一次】入口。
    *   **界面变化:** 界面跳转至“开始练习”转场动画，文案为“开始练习：《课程名称》”。
    *   **即时反馈:** 页面开始加载新的练习。
3.  **后续流程:** 同上一个场景的后续流程。

**场景细节补充：**
*   **前置条件:** 学生已完成某巩固练习，且策略推荐“再练一次”。
*   **期望结果:** 用户能从课程主路径方便地找到并开始“再练一次”。
*   **异常与边界情况:**
    *   同上。

**优先级：** 高
**依赖关系：** 核心功能点1.1

```mermaid
flowchart TD
    A[用户进入课程详情页/学习地图] --> B{定位到已完成且推荐再练的巩固练习模块};
    B -- 模块入口状态已更新（如显示'再练一次'） --> C{用户点击该入口};
    C --> D[进入'开始练习'转场（针对再练一次）];
    D --> E[开始'再练一次'答题流程];
    E --> F[完成后展示'再练一次'的学习报告];
```

---
### 2.2 辅助功能
-   **[辅助功能点1]:** 积分展示与累计 (本期PRD中提及积分获得，但未详述积分系统)
    -   **功能详述与前端呈现:**
        *   在作答反馈（正确、连续正确）中，应预留显示“+ 积分获得数量”的位置。
        *   在学习报告页，应展示“本次获得积分”的总数。
        *   ⚠️ 积分的具体数值来源、计算规则、以及用户总积分的展示和使用场景，超出了本PRD范围，但前端需预留接口和显示位置。
    -   **Mermaid图示 (可选，若交互复杂则建议提供):**
      ```mermaid
      %% 暂无复杂交互，主要是数据展示
      ```
-   **[辅助功能点2]:** 课程名称显示与处理
    -   **功能详述与前端呈现:**
        *   在“进入练习转场”和“再练一次转场”时，需要显示《课程名称》。
        *   PRD中提及“课程名称过长时展示，待 UI 确定”。前端需与UI/UE确认长名称的处理方式（如截断、跑马灯、换行等）。
    -   **Mermaid图示 (可选，若交互复杂则建议提供):**
      ```mermaid
      %% 纯展示逻辑，暂无复杂交互
      ```

### 2.3 非本期功能
-   **[功能点1]:** 详细的积分系统与商城兑换：本期仅涉及积分的简单展示，完整的积分规则、排行榜、兑换商城等非本期范围。
-   **[功能点2]:** 小组排名功能的详细实现：PRD中提及“小组排名反馈”，但未提供具体的小组定义、排名规则和详细界面，此功能若需实现，要补充详细需求。

## 3. 业务流程图 (整体业务流)

```mermaid
graph TD
    A[用户选择/进入巩固练习] --> B{练习状态判断};
    B -- 新练习/再练一次 --> C[进入练习转场];
    B -- 继续未完成练习 --> D[继续练习转场];
    C --> E{题组判断};
    D --> E;
    E -- 有题组 --> F[题组转场];
    E -- 无题组/题组结束 --> G[题目展示与作答];
    F --> G;
    G --> H{提交答案};
    H -- 作答反馈 --> I[作答结果反馈（对/错/连对）];
    I --> J{特殊反馈判断（不认真/难度）};
    J -- 有 --> K[特殊反馈展示];
    J -- 无 --> L{是否最后一道题?};
    K --> L;
    L -- 否 --> E; 
%% 返回判断下一题组或题目
    L -- 是 --> M[练习完成，进入学习报告页];
    G -- 中途退出 --> N[保存进度，返回课程详情];
    M -- 点击完成 --> N;
    M -- 点击再练一次 --> A;
```

## 4. 性能与安全需求

### 4.1 性能需求
-   **响应时间：**
    *   题目加载与切换响应时间不超过 1 秒 (95th percentile)。
    *   勾画工具的笔迹实时跟随，延迟感官上不明显。
    *   学习报告生成与展示时间（从完成练习到报告可见）不超过 3 秒。
-   **并发用户：** (由后端评估，前端需保证在正常并发下交互流畅)
-   **数据量：** 练习报告中的题目列表支持流畅展示至少50题的记录，滚动和查看详情操作响应快。
-   **前端性能感知：**
    *   **转场动画**: 应流畅不卡顿，资源预加载。
    *   **IP形象和动效**: 优化资源大小，避免影响加载速度。
    *   **题目资源（图片、音视频等）**: 若题目中包含较多或较大的媒体资源，应考虑懒加载或分步加载。

### 4.2 安全需求
-   **权限控制：** 巩固练习内容应与用户的课程购买/授权状态匹配，防止越权访问未授权课程的练习。
-   **数据加密：** 用户答案、练习进度等敏感数据在前后端传输过程中必须使用 HTTPS。
-   **操作审计：** (主要为后端责任) 记录用户开始练习、完成练习、提交答案等关键行为。
-   **输入校验:** (本需求中用户输入较少，主要为选择题) 若有开放性题目或反馈输入，需进行基础校验。

## 5. 验收标准

### 5.1 功能验收
-   **[核心功能点1：巩固练习入口]：**
    *   **用户场景：** 用户在学习地图找到巩固练习模块。
    *   **界面与交互：** “未完成”状态下显示“进入练习”或“继续练习”按钮；“已完成”状态下显示“已完成”icon、星星数、并提供“看报告”和“再练一次”按钮。预估题数正确显示。
    *   **期望结果：** 用户能根据练习状态正确进入对应流程。
-   **[核心功能点2：进入练习转场]：**
    *   **用户场景：** 用户点击“进入练习”或“继续练习”。
    *   **界面与交互：** 显示对应文案（“开始练习”/“继续练习”）和IP动效，2秒后自动跳转。加载超2秒能跳过。
    *   **期望结果：** 转场平滑，信息传达清晰。
-   **[核心功能点3：练习环节核心功能]：**
    *   **用户场景：** 用户在练习中进行退出、查看进度、使用勾画、收藏错题等操作。
    *   **界面与交互：** 退出有确认弹窗且进度保存；计时和进度准确；勾画工具功能符合描述；错题本自动/手动添加及反馈正确。熔断机制按策略触发。
    *   **期望结果：** 练习过程顺畅，各辅助功能可用且符合预期。
-   **[核心功能点4：题目间转场与反馈]：**
    *   **用户场景：** 用户提交答案后，或系统检测到特殊情况（不认真、难度变化）。
    *   **界面与交互：** 根据作答结果弹出对应情感反馈（IP、文案、音效）2秒；根据策略触发不认真/难度变化反馈2秒。反馈优先级符合描述。
    *   **期望结果：** 用户获得及时、恰当的反馈，引导学习行为。
-   **[核心功能点5：学习报告]：**
    *   **用户场景：** 用户完成练习后查看报告。
    *   **界面与交互：** IP及文案根据正确率显示；各项数据（时长、题数、正确率、积分、掌握度变化）准确展示；题目列表可查看详情；推荐学习按策略展示；“完成”和“再练一次”按钮功能正确。
    *   **期望结果：** 报告内容全面、准确，用户能清晰了解学习成果。
-   **[核心功能点6：“再练一次”环节]：**
    *   **用户场景：** 用户从报告页或课程详情页进入“再练一次”。
    *   **界面与交互：** 入口强化符合描述；转场同首次练习；练习内容、体验、熔断、报告均与普通练习一致，但题目源针对薄弱点。
    *   **期望结果：** 用户能顺利进行针对性的“再练一次”并获得相应报告。

### 5.2 性能验收
-   **响应时间：**
    *   **测试用例1:** 模拟普通WiFi网络，用户点击下一题，题目内容（含图片等资源）在1秒内加载完成。连续测试20次，95%的响应时间不超过1秒。
    *   **测试用例2:** 在练习界面使用勾画工具，笔迹跟随无肉眼可感知的延迟。
-   **前端性能感知验收：**
    *   **测试用例1 (转场动画):** 所有转场动画（进入练习、题组、反馈）在主流测试机型上播放流畅，无掉帧卡顿。

### 5.3 安全验收
-   **权限控制：**
    *   **测试用例1:** 尝试通过直接访问URL等方式进入未授权课程的巩固练习，期望结果：系统应拒绝访问或引导至正确页面。
-   **数据加密：**
    *   **测试用例1:** 通过浏览器开发者工具查看网络请求，确认用户答案、练习进度等数据在传输过程中通过HTTPS加密。

## 6. 其他需求

### 6.1 可用性与体验需求
-   **界面友好与直观性：**
    *   所有交互按钮的含义清晰，用户无需过多学习即可理解并操作。
    *   IP形象和情感化反馈文案设计贴合学生心理，营造积极的学习氛围。
-   **容错处理：**
    *   当网络请求失败（如获取题目、提交答案、加载报告失败）时，应有清晰、友好的错误提示（如toast提示“网络不给力，请稍后重试”），并提供重试机制（如点击重试按钮）。
    *   避免因单次操作失败导致整个练习流程中断或数据丢失。
-   **兼容性：**
    *   **浏览器：** 需支持最新版本的 Chrome, Firefox, Safari, Edge 浏览器（桌面端），以及主流移动端浏览器内嵌WebView（iOS Safari WebView, Android Chrome WebView）。
    *   **响应式布局：** 重点考虑移动端屏幕适配，确保在不同尺寸手机屏幕上均有良好视觉和操作体验。
-   **可访问性 (A11y)：**
    *   核心操作按钮（如提交、下一题、选项）应支持键盘操作。
    *   IP形象图片等非文本内容需提供有意义的 `alt` 文本。
    *   颜色对比度应考虑可读性。
-   **交互一致性：**
    *   各类反馈弹窗（作答反馈、难度反馈等）的出现方式、持续时间、消失方式应保持一致。
    *   按钮样式和交互行为在整个巩固练习模块中应统一。

### 6.2 维护性需求
-   **配置管理：**
    *   反馈文案（如正确、错误、连胜等文案库）建议可由运营人员在后台配置和调整，方便A/B测试和内容更新。
    *   推题策略、熔断阈值等核心逻辑参数应与后端服务解耦，方便策略调整。
-   **监控告警：** 前端关键异常（JS错误、API请求失败率高）应能通过监控系统捕获并告警。
-   **日志管理：** 前端应记录关键用户行为（如进入练习、完成练习、触发重要反馈）和异常信息，辅助问题排查。

## 7. 用户反馈和迭代计划

### 7.1 用户反馈机制
-   在学习报告页提供用户对本次练习体验的反馈入口（如“本节课感受如何？”的星级或标签评价）。
-   通过App内统一的反馈渠道收集用户对巩固练习功能的意见和建议。

### 7.2 迭代计划
-   **迭代周期：** 初定每2周为一个迭代周期。
-   **迭代流程：** 需求评审 -> UI/UX设计 -> 开发与联调 -> 测试 -> 发布上线 -> 数据跟踪与用户反馈收集。
-   **优先级调整：** 根据上线后的数据表现（参与率、完成率、正确率变化等）和用户反馈，持续优化题目推荐策略、反馈机制和整体体验。

## 8. 需求检查清单
使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| -------------------------------- | ----------------------------------- | ------ | ------ | ------ | ------------------------- | --------------------------- | ----------- | ---- |
| 巩固练习入口（路径、展示、状态、交互） | 2.1-[核心功能点1], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 进入练习转场（首次、继续、失败兜底）| 2.1-[核心功能点2], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 练习环节-页面框架（退出、计时、进度）| 2.1-[核心功能点3.1], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 练习环节-题组转场 | 2.1-[核心功能点3.2], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 练习环节-勾画组件 | 2.1-[核心功能点3.3], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 练习环节-错题本组件 | 2.1-[核心功能点3.4], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 练习环节-熔断机制 | 2.1-[核心功能点3.5], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [⚠️]         | 具体前端表现需细化 |
| 题目间转场-作答反馈（正确/错误/连对/部分） | 2.1-[核心功能点4.1], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 题目间转场-特殊反馈（不认真/难度） | 2.1-[核心功能点4.2], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 学习报告（转场、入口、内容、交互） | 2.1-[核心功能点5], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         | 练习完成转场原文已划掉 |
| 再练一次（触发、入口、内容、报告） | 2.1-[核心功能点6], 5.1 | [✅]    | [✅]    | [✅]    | [✅]                       | [✅]                         | [✅]         |      |
| 数据需求-效果追踪与埋点 | (未在本PRD中体现，属数据分析范畴) | N/A    | N/A    | N/A    | N/A                       | N/A                         | N/A         | 原始PRD第六节内容 |

-   **完整性：** 原始需求是否都有对应的优化后需求点，无遗漏。
-   **正确性：** 优化后需求描述是否准确表达了原始需求的意图。
-   **一致性：** 优化后需求之间是否有冲突或重复。
-   **可验证性：** 优化后需求是否有明确的验收标准（对应5. 验收标准）。
-   **可跟踪性：** 优化后需求是否有明确的优先级和依赖关系。
-   **UI/UX明确性：** 优化后需求是否清晰描述了前端界面、交互和用户体验细节。

✅表示通过；❌表示未通过；⚠️表示描述正确，但UI/UX细节、验收标准或图示等仍需与PM进一步沟通完善；N/A表示不适用。每个需求点都需要填写。

## 9. 附录

### 9.1 原型图/设计稿链接
-   视觉稿：[Figma](https://www.figma.com/design/COdZQZZOFHZHsdI1zWX8Tm/%E5%AD%A6%E7%94%9F%E7%AB%AF-1.0?node-id=1805-6218&p=f&m=dev)
-   (交互稿、动效链接原始PRD未提供)

### 9.2 术语表
-   **巩固练习：** 在学生掌握新知识或技能后，借助有针对性的练习加深理解、强化记忆，将知识从 “知道” 转变为 “会用”，满足的场景是 AI 课后的巩固。
-   **题组：** 将一组关联性较强或针对特定小知识模块的题目集合在一起的单位。
-   **再练一次：** 在首次巩固练习完成后，根据学生掌握情况推荐的再次练习环节，题目可能针对薄弱点调整。
-   **熔断机制：** 根据学生在练习中的表现（如连续错误、正确率过低等）提前中止练习的机制。

### 9.3 参考资料
-   原始需求文档：PRD - 学生端 - 巩固练习_analyzed.md
-   关联需求：
    -   教师端 - 作业布置: [教师端_1期_备课 & 作业布置](https://wcng60ba718p.feishu.cn/wiki/G6OTwydxOi8gdmkYTKUcmQmHngd)
    -   巩固练习推题策略: [巩固练习相关策略](https://wcng60ba718p.feishu.cn/docx/JYjedBulhoHiBDx1wGScCacHnrd)
    -   内容平台选题支持分题组选题: [PRD - 内容管理平台2.0 - 选题支持题组](https://wcng60ba718p.feishu.cn/wiki/C91Gwf402i2iU5kMs1vcPnICnDc)
    -   题型支持: [PRD - 题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)
```
--- 等待您的命令，指挥官

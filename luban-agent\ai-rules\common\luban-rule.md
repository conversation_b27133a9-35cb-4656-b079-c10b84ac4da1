# 鲁班工具规则定义

本文档描述了鲁班工具的规则定义和生效条件。

## 指令规则定义

1. 指令中使用的标签`<luban></luban>`是鲁班工具的规则定义，鲁班工具会根据定义执行相应的操作。
2. 规则定义中，`<luban>`标签中可以包含多个属性，如`<luban type="file"></luban>`、`<luban type="folder"></luban>`、`<luban type="ignore"></luban>`等。

## 生效条件

1. 非鲁班运行环境忽略`<luban>`标签，但内容对指令仍然有效，如：`文件：<luban type="file">ai-templates/templates/dd/be-s4-2-ai-dd-template-v1.2.md</luban>`，在非鲁班环境，如`cursor`中，内容依旧有效：`文件：ai-templates/templates/dd/be-s4-2-ai-dd-template-v1.2.md`。

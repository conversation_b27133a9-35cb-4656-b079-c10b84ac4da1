# Phase 4: Service层完成汇总

**项目名称**: {project_name}
**业务模块**: {biz_module}
**完成时间**: {completion_time}
**执行人**: {developer_name}

## 1. 已生成文件清单

### 1.1 Service接口文件
- `app/service/{biz_module}/{entity1}_service.go` - {entity1}服务接口
- `app/service/{biz_module}/{entity2}_service.go` - {entity2}服务接口
- `app/service/{biz_module}/{entity3}_service.go` - {entity3}服务接口

### 1.2 Service实现文件
- `internal/service/{biz_module}/{entity1}_service_impl.go` - {entity1}服务实现
- `internal/service/{biz_module}/{entity2}_service_impl.go` - {entity2}服务实现
- `internal/service/{biz_module}/{entity3}_service_impl.go` - {entity3}服务实现

### 1.3 业务逻辑服务文件
- `internal/service/{biz_module}/{business_service1}_service_impl.go` - {business1}业务服务
- `internal/service/{biz_module}/{business_service2}_service_impl.go` - {business2}业务服务

### 1.4 依赖注入文件
- `internal/service/{biz_module}/wire.go` - Wire依赖注入配置
- `internal/service/{biz_module}/provider.go` - 服务提供者配置

### 1.5 单元测试文件
- `internal/service/{biz_module}/{entity1}_service_impl_test.go` - {entity1}服务测试
- `internal/service/{biz_module}/{entity2}_service_impl_test.go` - {entity2}服务测试
- `internal/service/{biz_module}/{business_service1}_service_impl_test.go` - 业务服务测试

### 1.6 Mock文件
- `internal/service/{biz_module}/mock/{entity1}_service_mock.go` - {entity1}服务Mock
- `internal/service/{biz_module}/mock/{entity2}_service_mock.go` - {entity2}服务Mock

## 2. Service接口定义汇总

### 2.1 {Entity1}Service接口
- **文件位置**: `app/service/{biz_module}/{entity1}_service.go`
- **接口定义**:
```go
type {Entity1}Service interface {
    // 基础CRUD操作
    Create(ctx context.Context, req *dto.{Entity1}CreateReq) (*dto.{Entity1}Resp, error)
    GetByID(ctx context.Context, id int64) (*dto.{Entity1}Resp, error)
    Update(ctx context.Context, req *dto.{Entity1}UpdateReq) (*dto.{Entity1}Resp, error)
    Delete(ctx context.Context, id int64) error
    List(ctx context.Context, req *dto.{Entity1}ListReq) (*dto.{Entity1}ListResp, error)
    
    // 业务方法
    {BusinessMethod1}(ctx context.Context, req *dto.{BusinessReq1}) (*dto.{BusinessResp1}, error)
    {BusinessMethod2}(ctx context.Context, req *dto.{BusinessReq2}) error
    {BusinessMethod3}(ctx context.Context, id int64, params *dto.{BusinessParams}) (*dto.{BusinessResp3}, error)
    
    // 状态操作
    {ChangeStatus}(ctx context.Context, id int64, status int32) error
    {BatchOperation}(ctx context.Context, ids []int64, operation string) error
}
```

### 2.2 {Entity2}Service接口
- **文件位置**: `app/service/{biz_module}/{entity2}_service.go`
- **接口定义**: [类似上述结构]

### 2.3 {Entity3}Service接口
- **文件位置**: `app/service/{biz_module}/{entity3}_service.go`
- **接口定义**: [类似上述结构]

## 3. Service实现汇总

### 3.1 {Entity1}ServiceImpl实现
- **文件位置**: `internal/service/{biz_module}/{entity1}_service_impl.go`
- **依赖注入**:
```go
type {Entity1}ServiceImpl struct {
    {entity1}Repo      repository.{Entity1}Repository
    {entity1}Converter *converter.{Entity1}Converter
    logger             *logger.Logger
    cache              cache.Cache
    eventBus           event.Bus
}
```

#### 3.1.1 基础CRUD实现
```go
// Create 创建{entity1}
func (s *{Entity1}ServiceImpl) Create(ctx context.Context, req *dto.{Entity1}CreateReq) (*dto.{Entity1}Resp, error) {
    logger.Debug(ctx, "开始创建{entity1}", "req", req)
    
    // 1. 参数验证
    if err := s.validateCreateReq(req); err != nil {
        logger.Debug(ctx, "创建{entity1}参数验证失败", "error", err)
        return nil, err
    }
    
    // 2. 业务规则验证
    if err := s.validateBusinessRules(ctx, req); err != nil {
        logger.Debug(ctx, "创建{entity1}业务规则验证失败", "error", err)
        return nil, err
    }
    
    // 3. DTO转Entity
    entity := s.{entity1}Converter.CreateReqToEntity(req)
    
    // 4. 调用Repository创建
    if err := s.{entity1}Repo.Create(ctx, entity); err != nil {
        logger.Debug(ctx, "创建{entity1}失败", "error", err)
        return nil, err
    }
    
    // 5. 发布事件
    s.publishEvent(ctx, "{entity1}.created", entity)
    
    // 6. Entity转DTO返回
    resp := s.{entity1}Converter.EntityToResp(entity)
    logger.Debug(ctx, "创建{entity1}成功", "id", entity.ID)
    
    return resp, nil
}

// GetByID 根据ID获取{entity1}
func (s *{Entity1}ServiceImpl) GetByID(ctx context.Context, id int64) (*dto.{Entity1}Resp, error) {
    logger.Debug(ctx, "开始获取{entity1}", "id", id)
    
    // 1. 参数验证
    if id <= 0 {
        return nil, errors.New("ID必须大于0")
    }
    
    // 2. 先查缓存
    if cached := s.getFromCache(ctx, id); cached != nil {
        logger.Debug(ctx, "从缓存获取{entity1}成功", "id", id)
        return cached, nil
    }
    
    // 3. 查询数据库
    entity, err := s.{entity1}Repo.GetByID(ctx, id)
    if err != nil {
        logger.Debug(ctx, "获取{entity1}失败", "id", id, "error", err)
        return nil, err
    }
    
    // 4. Entity转DTO
    resp := s.{entity1}Converter.EntityToResp(entity)
    
    // 5. 写入缓存
    s.setToCache(ctx, id, resp)
    
    logger.Debug(ctx, "获取{entity1}成功", "id", id)
    return resp, nil
}

// Update 更新{entity1}
func (s *{Entity1}ServiceImpl) Update(ctx context.Context, req *dto.{Entity1}UpdateReq) (*dto.{Entity1}Resp, error) {
    logger.Debug(ctx, "开始更新{entity1}", "req", req)
    
    // 1. 参数验证
    if err := s.validateUpdateReq(req); err != nil {
        return nil, err
    }
    
    // 2. 获取现有实体
    existing, err := s.{entity1}Repo.GetByID(ctx, req.ID)
    if err != nil {
        return nil, err
    }
    
    // 3. 业务规则验证
    if err := s.validateUpdateBusinessRules(ctx, req, existing); err != nil {
        return nil, err
    }
    
    // 4. DTO转Entity
    updated := s.{entity1}Converter.UpdateReqToEntity(req, existing)
    
    // 5. 更新数据库
    if err := s.{entity1}Repo.Update(ctx, updated); err != nil {
        return nil, err
    }
    
    // 6. 清除缓存
    s.clearCache(ctx, req.ID)
    
    // 7. 发布事件
    s.publishEvent(ctx, "{entity1}.updated", updated)
    
    // 8. 返回结果
    resp := s.{entity1}Converter.EntityToResp(updated)
    logger.Debug(ctx, "更新{entity1}成功", "id", req.ID)
    
    return resp, nil
}

// Delete 删除{entity1}
func (s *{Entity1}ServiceImpl) Delete(ctx context.Context, id int64) error {
    logger.Debug(ctx, "开始删除{entity1}", "id", id)
    
    // 1. 参数验证
    if id <= 0 {
        return errors.New("ID必须大于0")
    }
    
    // 2. 检查是否存在
    existing, err := s.{entity1}Repo.GetByID(ctx, id)
    if err != nil {
        return err
    }
    
    // 3. 业务规则验证
    if err := s.validateDeleteBusinessRules(ctx, existing); err != nil {
        return err
    }
    
    // 4. 执行删除
    if err := s.{entity1}Repo.Delete(ctx, id); err != nil {
        return err
    }
    
    // 5. 清除缓存
    s.clearCache(ctx, id)
    
    // 6. 发布事件
    s.publishEvent(ctx, "{entity1}.deleted", existing)
    
    logger.Debug(ctx, "删除{entity1}成功", "id", id)
    return nil
}

// List 获取{entity1}列表
func (s *{Entity1}ServiceImpl) List(ctx context.Context, req *dto.{Entity1}ListReq) (*dto.{Entity1}ListResp, error) {
    logger.Debug(ctx, "开始获取{entity1}列表", "req", req)
    
    // 1. 参数验证
    if err := s.validateListReq(req); err != nil {
        return nil, err
    }
    
    // 2. 构建查询参数
    params := s.buildListParams(req)
    
    // 3. 查询数据
    entities, total, err := s.{entity1}Repo.List(ctx, params)
    if err != nil {
        logger.Debug(ctx, "获取{entity1}列表失败", "error", err)
        return nil, err
    }
    
    // 4. 转换为DTO
    resp := s.{entity1}Converter.EntitiesToListResp(entities, total, &req.PageReq)
    
    logger.Debug(ctx, "获取{entity1}列表成功", "total", total)
    return resp, nil
}
```

#### 3.1.2 业务方法实现
```go
// {BusinessMethod1} {business_method1_description}
func (s *{Entity1}ServiceImpl) {BusinessMethod1}(ctx context.Context, req *dto.{BusinessReq1}) (*dto.{BusinessResp1}, error) {
    logger.Debug(ctx, "开始执行{business_method1}", "req", req)
    
    // 1. 参数验证
    if err := s.validate{BusinessMethod1}Req(req); err != nil {
        return nil, err
    }
    
    // 2. 业务逻辑处理
    // ... 具体业务逻辑实现
    
    // 3. 数据库操作
    // ... Repository调用
    
    // 4. 事件发布
    // ... 事件处理
    
    // 5. 返回结果
    // ... 结果转换
    
    logger.Debug(ctx, "{business_method1}执行成功")
    return resp, nil
}

// {BusinessMethod2} {business_method2_description}
func (s *{Entity1}ServiceImpl) {BusinessMethod2}(ctx context.Context, req *dto.{BusinessReq2}) error {
    // 类似实现结构
    return nil
}
```

#### 3.1.3 辅助方法实现
```go
// validateCreateReq 验证创建请求
func (s *{Entity1}ServiceImpl) validateCreateReq(req *dto.{Entity1}CreateReq) error {
    if req == nil {
        return errors.New("请求参数不能为空")
    }
    
    // 业务规则验证
    if len(req.{Field1}) == 0 {
        return errors.New("{field1}不能为空")
    }
    
    return nil
}

// validateBusinessRules 验证业务规则
func (s *{Entity1}ServiceImpl) validateBusinessRules(ctx context.Context, req *dto.{Entity1}CreateReq) error {
    // 检查唯一性约束
    if exists, err := s.{entity1}Repo.ExistsByField(ctx, req.{Field1}); err != nil {
        return err
    } else if exists {
        return errors.New("{field1}已存在")
    }
    
    return nil
}

// getFromCache 从缓存获取
func (s *{Entity1}ServiceImpl) getFromCache(ctx context.Context, id int64) *dto.{Entity1}Resp {
    key := fmt.Sprintf("{entity1}:%d", id)
    if data, err := s.cache.Get(ctx, key); err == nil {
        var resp dto.{Entity1}Resp
        if err := json.Unmarshal(data, &resp); err == nil {
            return &resp
        }
    }
    return nil
}

// setToCache 写入缓存
func (s *{Entity1}ServiceImpl) setToCache(ctx context.Context, id int64, resp *dto.{Entity1}Resp) {
    key := fmt.Sprintf("{entity1}:%d", id)
    if data, err := json.Marshal(resp); err == nil {
        s.cache.Set(ctx, key, data, time.Hour)
    }
}

// publishEvent 发布事件
func (s *{Entity1}ServiceImpl) publishEvent(ctx context.Context, eventType string, data interface{}) {
    event := &event.Event{
        Type:      eventType,
        Data:      data,
        Timestamp: time.Now(),
    }
    s.eventBus.Publish(ctx, event)
}
```

### 3.2 {Entity2}ServiceImpl实现
- **文件位置**: `internal/service/{biz_module}/{entity2}_service_impl.go`
- **实现结构**: [类似上述结构]

### 3.3 {Entity3}ServiceImpl实现
- **文件位置**: `internal/service/{biz_module}/{entity3}_service_impl.go`
- **实现结构**: [类似上述结构]

## 4. 业务逻辑服务汇总

### 4.1 {BusinessService1}ServiceImpl
- **文件位置**: `internal/service/{biz_module}/{business_service1}_service_impl.go`
- **职责**: {business_service1_responsibility}
- **核心方法**:
```go
// {ComplexBusinessMethod} 复杂业务逻辑处理
func (s *{BusinessService1}ServiceImpl) {ComplexBusinessMethod}(ctx context.Context, req *dto.{ComplexReq}) (*dto.{ComplexResp}, error) {
    logger.Debug(ctx, "开始执行复杂业务逻辑", "req", req)
    
    // 1. 开启事务
    tx, err := s.db.BeginTx(ctx, nil)
    if err != nil {
        return nil, err
    }
    defer tx.Rollback()
    
    // 2. 多表操作
    // ... 涉及多个实体的业务逻辑
    
    // 3. 业务规则验证
    // ... 复杂业务规则检查
    
    // 4. 数据一致性保证
    // ... 事务内多步操作
    
    // 5. 提交事务
    if err := tx.Commit(); err != nil {
        return nil, err
    }
    
    // 6. 异步处理
    // ... 发送消息队列等
    
    logger.Debug(ctx, "复杂业务逻辑执行成功")
    return resp, nil
}
```

### 4.2 {BusinessService2}ServiceImpl
- **文件位置**: `internal/service/{biz_module}/{business_service2}_service_impl.go`
- **职责**: {business_service2_responsibility}
- **核心方法**: [类似上述结构]

## 5. 依赖注入配置汇总

### 5.1 Wire配置
- **文件位置**: `internal/service/{biz_module}/wire.go`
```go
//go:build wireinject
// +build wireinject

package {biz_module}

import (
    "github.com/google/wire"
    "your-project/app/service/{biz_module}"
    "your-project/internal/service/{biz_module}"
)

// ProviderSet 服务提供者集合
var ProviderSet = wire.NewSet(
    // Service实现
    New{Entity1}ServiceImpl,
    wire.Bind(new(service.{Entity1}Service), new(*{Entity1}ServiceImpl)),
    
    New{Entity2}ServiceImpl,
    wire.Bind(new(service.{Entity2}Service), new(*{Entity2}ServiceImpl)),
    
    New{Entity3}ServiceImpl,
    wire.Bind(new(service.{Entity3}Service), new(*{Entity3}ServiceImpl)),
    
    // 业务服务
    New{BusinessService1}ServiceImpl,
    New{BusinessService2}ServiceImpl,
)

// InitServices 初始化所有服务
func InitServices(
    {entity1}Repo repository.{Entity1}Repository,
    {entity2}Repo repository.{Entity2}Repository,
    {entity3}Repo repository.{Entity3}Repository,
    logger *logger.Logger,
    cache cache.Cache,
    eventBus event.Bus,
) (
    service.{Entity1}Service,
    service.{Entity2}Service,
    service.{Entity3}Service,
    *{BusinessService1}ServiceImpl,
    *{BusinessService2}ServiceImpl,
    error,
) {
    wire.Build(ProviderSet)
    return nil, nil, nil, nil, nil, nil
}
```

### 5.2 Provider配置
- **文件位置**: `internal/service/{biz_module}/provider.go`
```go
// New{Entity1}ServiceImpl 创建{Entity1}服务实现
func New{Entity1}ServiceImpl(
    {entity1}Repo repository.{Entity1}Repository,
    {entity1}Converter *converter.{Entity1}Converter,
    logger *logger.Logger,
    cache cache.Cache,
    eventBus event.Bus,
) *{Entity1}ServiceImpl {
    return &{Entity1}ServiceImpl{
        {entity1}Repo:      {entity1}Repo,
        {entity1}Converter: {entity1}Converter,
        logger:             logger,
        cache:              cache,
        eventBus:           eventBus,
    }
}
```

## 6. 事务处理汇总

### 6.1 事务管理策略
- **单表操作**: 使用Repository自动事务
- **多表操作**: 使用显式事务管理
- **分布式事务**: 使用Saga模式或TCC模式
- **读写分离**: 读操作使用从库，写操作使用主库

### 6.2 事务实现示例
```go
// 复杂业务事务处理
func (s *ServiceImpl) ComplexBusinessOperation(ctx context.Context, req *dto.ComplexReq) error {
    // 开启事务
    return s.db.Transaction(func(tx *gorm.DB) error {
        // 使用事务Repository
        entity1Repo := s.entity1Repo.WithTx(tx)
        entity2Repo := s.entity2Repo.WithTx(tx)
        
        // 业务操作1
        if err := entity1Repo.Create(ctx, entity1); err != nil {
            return err
        }
        
        // 业务操作2
        if err := entity2Repo.Update(ctx, entity2); err != nil {
            return err
        }
        
        // 业务规则验证
        if err := s.validateComplexRules(ctx, entity1, entity2); err != nil {
            return err
        }
        
        return nil
    })
}
```

## 7. 缓存策略汇总

### 7.1 缓存层级
- **L1缓存**: 本地内存缓存 (5分钟)
- **L2缓存**: Redis缓存 (1小时)
- **L3缓存**: 数据库查询缓存

### 7.2 缓存模式
- **Cache-Aside**: 手动管理缓存
- **Write-Through**: 写入时同步更新缓存
- **Write-Behind**: 异步写入缓存
- **Refresh-Ahead**: 预刷新缓存

### 7.3 缓存键设计
```go
// 缓存键命名规范
const (
    {Entity1}CacheKeyPrefix = "{biz_module}:{entity1}:"
    {Entity1}ListCacheKey   = "{biz_module}:{entity1}:list:"
    {Entity1}CountCacheKey  = "{biz_module}:{entity1}:count"
)

// 生成缓存键
func (s *ServiceImpl) buildCacheKey(prefix string, params ...interface{}) string {
    key := prefix
    for _, param := range params {
        key += fmt.Sprintf("%v:", param)
    }
    return strings.TrimSuffix(key, ":")
}
```

## 8. 事件处理汇总

### 8.1 事件类型定义
```go
// 事件类型常量
const (
    Event{Entity1}Created = "{biz_module}.{entity1}.created"
    Event{Entity1}Updated = "{biz_module}.{entity1}.updated"
    Event{Entity1}Deleted = "{biz_module}.{entity1}.deleted"
    Event{Entity1}StatusChanged = "{biz_module}.{entity1}.status_changed"
)
```

### 8.2 事件发布
```go
// 发布领域事件
func (s *ServiceImpl) publishDomainEvent(ctx context.Context, eventType string, data interface{}) {
    event := &event.DomainEvent{
        ID:        uuid.New().String(),
        Type:      eventType,
        Data:      data,
        Timestamp: time.Now(),
        Source:    "{biz_module}_service",
    }
    
    if err := s.eventBus.Publish(ctx, event); err != nil {
        logger.Debug(ctx, "发布事件失败", "event", event, "error", err)
    }
}
```

### 8.3 事件处理
```go
// 事件处理器
func (s *ServiceImpl) HandleEntityCreated(ctx context.Context, event *event.DomainEvent) error {
    logger.Debug(ctx, "处理实体创建事件", "event", event)
    
    // 异步处理逻辑
    // 1. 发送通知
    // 2. 更新统计
    // 3. 同步到其他系统
    
    return nil
}
```

## 9. 错误处理汇总

### 9.1 错误分类
- **参数错误**: 400 Bad Request
- **业务错误**: 422 Unprocessable Entity  
- **权限错误**: 403 Forbidden
- **资源不存在**: 404 Not Found
- **系统错误**: 500 Internal Server Error

### 9.2 错误处理策略
```go
// 统一错误处理
func (s *ServiceImpl) handleError(ctx context.Context, err error, operation string) error {
    logger.Debug(ctx, fmt.Sprintf("%s失败", operation), "error", err)
    
    // 错误分类处理
    switch {
    case errors.Is(err, gorm.ErrRecordNotFound):
        return errors.NewNotFoundError("资源不存在")
    case errors.Is(err, gorm.ErrDuplicatedKey):
        return errors.NewConflictError("资源已存在")
    default:
        return errors.NewInternalError("系统内部错误")
    }
}
```

## 10. 性能优化汇总

### 10.1 查询优化
- ✅ 使用索引优化查询
- ✅ 分页查询避免深度分页
- ✅ 批量操作减少数据库交互
- ✅ 预加载关联数据避免N+1问题

### 10.2 缓存优化
- ✅ 热点数据缓存
- ✅ 查询结果缓存
- ✅ 缓存预热策略
- ✅ 缓存穿透保护

### 10.3 并发优化
- ✅ 使用连接池管理数据库连接
- ✅ 异步处理非关键业务
- ✅ 限流保护系统稳定性
- ✅ 熔断机制防止雪崩

## 11. 测试覆盖情况

### 11.1 单元测试统计
- **{Entity1}Service测试覆盖率**: {service1_coverage}%
- **{Entity2}Service测试覆盖率**: {service2_coverage}%
- **{Entity3}Service测试覆盖率**: {service3_coverage}%
- **业务服务测试覆盖率**: {business_service_coverage}%

### 11.2 测试场景覆盖
- ✅ 正常业务流程测试
- ✅ 异常情况处理测试
- ✅ 边界条件测试
- ✅ 并发安全测试
- ✅ 性能基准测试
- ✅ 集成测试

### 11.3 Mock测试
- ✅ Repository层Mock
- ✅ 外部服务Mock
- ✅ 缓存层Mock
- ✅ 事件总线Mock

## 12. 下一Phase输入资源

### 12.1 可用Service接口
- `{Entity1}Service` - 完整CRUD和业务方法
- `{Entity2}Service` - 完整CRUD和业务方法
- `{Entity3}Service` - 完整CRUD和业务方法

### 12.2 业务能力
- 所有基础CRUD操作已实现
- 复杂业务逻辑已封装
- 事务处理机制完善
- 缓存策略已配置
- 事件机制已集成

### 12.3 技术特性
- 依赖注入配置完成
- 错误处理统一
- 日志记录完整
- 性能优化到位
- 测试覆盖充分

## 13. Controller层开发指导

### 13.1 Service使用建议
- Controller只调用Service接口方法
- 不在Controller中实现业务逻辑
- 统一使用DTO进行数据传输
- 合理处理Service返回的错误

### 13.2 依赖注入建议
- 通过构造函数注入Service依赖
- 使用Wire自动生成依赖注入代码
- 保持Controller的轻量级
- 避免在Controller中直接操作Repository

### 13.3 错误处理建议
- 将Service错误转换为HTTP状态码
- 提供统一的错误响应格式
- 记录必要的错误日志
- 不向客户端暴露内部错误详情

## 14. 注意事项

### 14.1 业务逻辑封装
- 核心业务逻辑在Service层实现
- 避免在Controller层处理业务逻辑
- 保持Service方法的单一职责
- 合理拆分复杂业务方法

### 14.2 数据一致性
- 使用事务保证数据一致性
- 合理设计补偿机制
- 避免长事务影响性能
- 考虑分布式事务场景

### 14.3 性能考虑
- 避免在循环中调用数据库
- 使用批量操作提高效率
- 合理使用缓存减少数据库压力
- 异步处理非关键业务

## 15. 验收确认

- ✅ 所有Service接口定义完成
- ✅ 所有Service实现完成
- ✅ 业务逻辑正确实现
- ✅ 事务处理机制完善
- ✅ 缓存策略配置完成
- ✅ 事件机制集成完成
- ✅ 错误处理统一完成
- ✅ 单元测试全部通过
- ✅ 集成测试通过
- ✅ 性能测试通过
- ✅ 代码评审通过
- ✅ 文档更新完成
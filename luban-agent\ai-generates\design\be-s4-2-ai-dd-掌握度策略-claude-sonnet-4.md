# 数据库设计文档 - 掌握度策略系统 - V1.0

**模板版本**: v1.3
**最后更新日期**: 2025-06-05
**设计负责人**: AI数据库设计工程师
**评审人**: 待填写
**状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-06-05 | AI数据库设计工程师 | 初始草稿     |

## 1. 项目概述与设计目标

### 1.1 项目背景与范围
掌握度策略系统是一个智能化学习评估平台，核心目标是通过精准的掌握度计算和分析，为学生提供个性化的学习路径规划和学习效果反馈。本次数据库设计覆盖用户分层、掌握度计算、薄弱知识点识别、学习分析等核心功能的数据存储需求。

### 1.2 设计目标回顾
* 提供完整的数据库创建语句（如果适用）。
* 提供完整的、符合规范的表创建语句（DDL）。
* 清晰描述表间关系，并提供 ER 图。
* 详细说明核心用户场景/用户故事所涉及的表以及其 CRUD 操作方式。
* 阐述关键表的索引策略及其设计原因。
* 对核心表进行数据量预估和增长趋势分析。
* 明确数据生命周期管理/归档策略（如果适用）。
* 记录特殊设计考量与权衡。
* 总结设计如何体现对应数据库（PostgreSQL/ClickHouse）的关键规范。

## 2. 数据库环境与总体说明

### 2.1 目标数据库类型
* PostgreSQL 17.x (事务性数据存储)
* ClickHouse 23.8.16.16 (分析型数据存储)

### 2.2 数据库创建语句 (如果适用)
* **PostgreSQL 示例**:
  ```sql
  -- CREATE DATABASE mastery_strategy_db
  -- WITH
  -- OWNER = mastery_user
  -- ENCODING = 'UTF8'
  -- LC_COLLATE = 'zh_CN.UTF-8'
  -- LC_CTYPE = 'zh_CN.UTF-8'
  -- TABLESPACE = pg_default
  -- CONNECTION LIMIT = -1
  -- TEMPLATE = template0;
  -- COMMENT ON DATABASE mastery_strategy_db IS '掌握度策略系统数据库';
  ```
* **ClickHouse 示例**:
  ```sql
  -- CREATE DATABASE IF NOT EXISTS db_mastery_analytics
  -- ENGINE = Atomic;
  -- COMMENT '掌握度策略系统分析数据库';
  ```

### 2.3 数据库选型决策摘要

**技术选型决策表**：
| 实体/表名 | 数据库类型 | 选型依据 | 数据特征 | 查询模式 | 预估数据量 |
|---------|-----------|---------|---------|---------|-----------|
| tbl_mastery_user_tiering | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 中表(100万) |
| tbl_mastery_knowledge_mastery | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 实时查询+更新 | 大表(千万级) |
| tbl_mastery_external_mastery | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 实时查询+更新 | 大表(千万级) |
| tbl_mastery_weak_knowledge | PostgreSQL | 频繁CRUD操作 | 事务性数据 | 复杂查询+关联 | 中表(500万) |
| tbl_mastery_course_config | PostgreSQL | 频繁CRUD操作 | 配置数据 | 查询为主 | 小表(10万) |
| tbl_mastery_answer_log | ClickHouse | 仅INSERT分析 | 时序数据 | 聚合分析 | 大表(亿级) |
| tbl_mastery_statistics | ClickHouse | 仅INSERT分析 | 聚合数据 | 分析查询 | 大表(千万级) |

## 3. 数据库表详细设计 - PostgreSQL

### 3.1 数据库表清单

#### PostgreSQL表清单
| 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 | 预定数据库类型 |
|------|------------------|-------------|---------|--------------|
| `tbl_mastery_user_tiering` | 用户分层数据表 - 存储学生能力分层信息 | 中表(100万) | 频繁CRUD操作 | PostgreSQL |
| `tbl_mastery_knowledge_mastery` | 知识点掌握度表 - 存储用户对知识点的掌握程度 | 大表(千万级) | 频繁CRUD操作 | PostgreSQL |
| `tbl_mastery_external_mastery` | 外化掌握度表 - 存储向学生展示的掌握度信息 | 大表(千万级) | 频繁CRUD操作 | PostgreSQL |
| `tbl_mastery_weak_knowledge` | 薄弱知识点表 - 存储识别的薄弱知识点信息 | 中表(500万) | 频繁CRUD操作 | PostgreSQL |
| `tbl_mastery_course_config` | 课程配置表 - 存储课程难度和练习配置信息 | 小表(10万) | 频繁CRUD操作 | PostgreSQL |

#### ClickHouse表清单  
| 表名 | 核心功能/用途简述 | 预估数据量级 | 选型理由 | 预定数据库类型 |
|------|------------------|-------------|---------|--------------|
| `tbl_mastery_answer_log` | 答题记录日志表 - 存储学生答题行为数据 | 大表(亿级) | 仅INSERT分析查询 | ClickHouse |
| `tbl_mastery_statistics` | 掌握度统计表 - 存储班级和个人掌握度统计数据 | 大表(千万级) | 仅INSERT分析查询 | ClickHouse |

---

### 3.2 表名: `tbl_mastery_user_tiering` - 用户分层数据表

#### 3.2.1 表功能说明
存储学生的能力分层信息，包括基于考试成绩和学校信息计算的分层等级、校准系数等。用于个性化掌握度计算的基础数据。

#### 3.2.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_user_tiering (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    user_id BIGINT NOT NULL,                                    -- 用户ID，关联用户中心服务
    subject_id BIGINT NOT NULL,                                 -- 学科ID，关联内容平台服务
    user_ranking_percentage NUMERIC(5,2) NOT NULL,              -- 用户单科排名百分比 (0-100)
    calibration_coefficient_a NUMERIC(5,2) NOT NULL DEFAULT 1.0, -- 校准系数a (1-3)
    calibration_coefficient_b NUMERIC(5,2) NOT NULL DEFAULT 1.0, -- 校准系数b (>0)
    subject_ability_ranking NUMERIC(5,4) NOT NULL,              -- 学科能力排名 (0-1)
    tiering_level VARCHAR(10) NOT NULL,                         -- 分层等级 (S+, S, A, B, C)
    tiering_mastery_coefficient NUMERIC(3,2) NOT NULL,          -- 分层掌握系数
    -- 标准字段
    status SMALLINT NOT NULL DEFAULT 1,                         -- 状态 (0:禁用, 1:启用)
    create_time BIGINT NOT NULL,                                -- 创建时间 (UTC秒数)
    update_time BIGINT NOT NULL,                                -- 更新时间 (UTC秒数)
    delete_time BIGINT DEFAULT NULL                             -- 删除时间 (UTC秒数)
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_user_tiering IS '用户分层数据表 - 存储学生能力分层信息，用于个性化掌握度计算';
COMMENT ON COLUMN tbl_mastery_user_tiering.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_user_tiering.user_id IS '用户ID，关联用户中心服务';
COMMENT ON COLUMN tbl_mastery_user_tiering.subject_id IS '学科ID，关联内容平台服务';
COMMENT ON COLUMN tbl_mastery_user_tiering.user_ranking_percentage IS '用户单科排名百分比 (0-100)';
COMMENT ON COLUMN tbl_mastery_user_tiering.calibration_coefficient_a IS '校准系数a，基于学校本科录取率 (1-3)';
COMMENT ON COLUMN tbl_mastery_user_tiering.calibration_coefficient_b IS '校准系数b，基于生源波动情况 (>0)';
COMMENT ON COLUMN tbl_mastery_user_tiering.subject_ability_ranking IS '学科能力排名百分比 (0-1)';
COMMENT ON COLUMN tbl_mastery_user_tiering.tiering_level IS '分层等级 (S+, S, A, B, C)';
COMMENT ON COLUMN tbl_mastery_user_tiering.tiering_mastery_coefficient IS '分层掌握系数 (S+:1.2, S:1.0, A:0.8, B:0.5, C:0.4)';
COMMENT ON COLUMN tbl_mastery_user_tiering.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_user_tiering.create_time IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_user_tiering.update_time IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_user_tiering.delete_time IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_user_tiering_user_subject ON tbl_mastery_user_tiering(user_id, subject_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_user_tiering_user ON tbl_mastery_user_tiering(user_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_user_tiering_subject ON tbl_mastery_user_tiering(subject_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_user_tiering_level ON tbl_mastery_user_tiering(tiering_level) WHERE delete_time IS NULL;
```

#### 3.2.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_user_tiering_user_subject` | user_id,subject_id | btree | 用户学科分层唯一性 | 按用户和学科查询分层信息 | 高效单行查询 |
| `idx_mastery_user_tiering_user` | user_id | btree | 用户分层查询 | 查询用户所有学科分层 | 高效范围查询 |
| `idx_mastery_user_tiering_subject` | subject_id | btree | 学科分层统计 | 按学科统计分层分布 | 高效聚合查询 |
| `idx_mastery_user_tiering_level` | tiering_level | btree | 分层等级查询 | 按分层等级查询用户 | 高效过滤查询 |

---

### 3.3 表名: `tbl_mastery_knowledge_mastery` - 知识点掌握度表

#### 3.3.1 表功能说明
存储用户对特定知识点的掌握程度数据，包括当前掌握度、初始掌握度、累计答题次数等。是掌握度计算的核心数据表。

#### 3.3.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_knowledge_mastery (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    user_id BIGINT NOT NULL,                                    -- 用户ID，关联用户中心服务
    knowledge_point_id BIGINT NOT NULL,                         -- 知识点ID，关联内容平台服务
    current_mastery NUMERIC(5,4) NOT NULL DEFAULT 0.0000,       -- 当前掌握度 (0-1)
    initial_mastery NUMERIC(5,4) NOT NULL DEFAULT 0.0000,       -- 初始掌握度 (0-1)
    total_answer_count INTEGER NOT NULL DEFAULT 0,              -- 累计答题次数
    last_answer_time BIGINT DEFAULT NULL,                       -- 最后答题时间 (UTC秒数)
    -- 标准字段
    status SMALLINT NOT NULL DEFAULT 1,                         -- 状态 (0:禁用, 1:启用)
    create_time BIGINT NOT NULL,                                -- 创建时间 (UTC秒数)
    update_time BIGINT NOT NULL,                                -- 更新时间 (UTC秒数)
    delete_time BIGINT DEFAULT NULL                             -- 删除时间 (UTC秒数)
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_knowledge_mastery IS '知识点掌握度表 - 存储用户对知识点的掌握程度数据';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.user_id IS '用户ID，关联用户中心服务';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.knowledge_point_id IS '知识点ID，关联内容平台服务';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.current_mastery IS '当前掌握度 (0-1)';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.initial_mastery IS '初始掌握度 (0-1)';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.total_answer_count IS '累计答题次数';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.last_answer_time IS '最后答题时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.create_time IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.update_time IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_knowledge_mastery.delete_time IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_knowledge_mastery_user_kp ON tbl_mastery_knowledge_mastery(user_id, knowledge_point_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_knowledge_mastery_user ON tbl_mastery_knowledge_mastery(user_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_knowledge_mastery_kp ON tbl_mastery_knowledge_mastery(knowledge_point_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_knowledge_mastery_mastery ON tbl_mastery_knowledge_mastery(current_mastery) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_knowledge_mastery_update_time ON tbl_mastery_knowledge_mastery(update_time) WHERE delete_time IS NULL;
```

#### 3.3.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_knowledge_mastery_user_kp` | user_id,knowledge_point_id | btree | 用户知识点掌握度唯一性 | 按用户和知识点查询掌握度 | 高效单行查询 |
| `idx_mastery_knowledge_mastery_user` | user_id | btree | 用户掌握度查询 | 查询用户所有知识点掌握度 | 高效范围查询 |
| `idx_mastery_knowledge_mastery_kp` | knowledge_point_id | btree | 知识点掌握度统计 | 按知识点统计掌握度分布 | 高效聚合查询 |
| `idx_mastery_knowledge_mastery_mastery` | current_mastery | btree | 掌握度范围查询 | 查询特定掌握度范围的记录 | 高效范围查询 |
| `idx_mastery_knowledge_mastery_update_time` | update_time | btree | 时间范围查询 | 查询最近更新的掌握度记录 | 高效时序查询 |

---

### 3.4 表名: `tbl_mastery_external_mastery` - 外化掌握度表

#### 3.4.1 表功能说明
存储向学生展示的掌握度信息，包括外化掌握度值、掌握度上限、累计答题数等。用于学生端的掌握度展示。

#### 3.4.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_external_mastery (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    user_id BIGINT NOT NULL,                                    -- 用户ID，关联用户中心服务
    knowledge_point_id BIGINT NOT NULL,                         -- 知识点ID，关联内容平台服务
    external_mastery_value NUMERIC(5,4) NOT NULL DEFAULT 0.0000, -- 外化掌握度值 (0-1)
    mastery_upper_limit NUMERIC(3,2) NOT NULL DEFAULT 1.00,     -- 掌握度上限 (高:1.0, 中:0.9, 低:0.7)
    total_answer_count INTEGER NOT NULL DEFAULT 0,              -- 累计答题数量
    is_update_condition_met BOOLEAN NOT NULL DEFAULT FALSE,     -- 是否满足更新条件
    last_update_time BIGINT DEFAULT NULL,                       -- 最后更新时间 (UTC秒数)
    -- 标准字段
    status SMALLINT NOT NULL DEFAULT 1,                         -- 状态 (0:禁用, 1:启用)
    create_time BIGINT NOT NULL,                                -- 创建时间 (UTC秒数)
    update_time BIGINT NOT NULL,                                -- 更新时间 (UTC秒数)
    delete_time BIGINT DEFAULT NULL                             -- 删除时间 (UTC秒数)
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_external_mastery IS '外化掌握度表 - 存储向学生展示的掌握度信息';
COMMENT ON COLUMN tbl_mastery_external_mastery.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_external_mastery.user_id IS '用户ID，关联用户中心服务';
COMMENT ON COLUMN tbl_mastery_external_mastery.knowledge_point_id IS '知识点ID，关联内容平台服务';
COMMENT ON COLUMN tbl_mastery_external_mastery.external_mastery_value IS '外化掌握度值 (0-1)，向学生展示的掌握度百分比';
COMMENT ON COLUMN tbl_mastery_external_mastery.mastery_upper_limit IS '掌握度上限，基于课程难度 (高:1.0, 中:0.9, 低:0.7)';
COMMENT ON COLUMN tbl_mastery_external_mastery.total_answer_count IS '累计答题数量';
COMMENT ON COLUMN tbl_mastery_external_mastery.is_update_condition_met IS '是否满足更新条件 (累计答题≥5题 或 课程完成)';
COMMENT ON COLUMN tbl_mastery_external_mastery.last_update_time IS '最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_external_mastery.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_external_mastery.create_time IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_external_mastery.update_time IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_external_mastery.delete_time IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_external_mastery_user_kp ON tbl_mastery_external_mastery(user_id, knowledge_point_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_external_mastery_user ON tbl_mastery_external_mastery(user_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_external_mastery_kp ON tbl_mastery_external_mastery(knowledge_point_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_external_mastery_value ON tbl_mastery_external_mastery(external_mastery_value) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_external_mastery_update_condition ON tbl_mastery_external_mastery(is_update_condition_met) WHERE delete_time IS NULL;
```

#### 3.4.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_external_mastery_user_kp` | user_id,knowledge_point_id | btree | 用户知识点外化掌握度唯一性 | 按用户和知识点查询外化掌握度 | 高效单行查询 |
| `idx_mastery_external_mastery_user` | user_id | btree | 用户外化掌握度查询 | 查询用户所有知识点外化掌握度 | 高效范围查询 |
| `idx_mastery_external_mastery_kp` | knowledge_point_id | btree | 知识点外化掌握度统计 | 按知识点统计外化掌握度分布 | 高效聚合查询 |
| `idx_mastery_external_mastery_value` | external_mastery_value | btree | 外化掌握度范围查询 | 查询特定外化掌握度范围的记录 | 高效范围查询 |
| `idx_mastery_external_mastery_update_condition` | is_update_condition_met | btree | 更新条件查询 | 查询满足更新条件的记录 | 高效过滤查询 |

---

### 3.5 表名: `tbl_mastery_weak_knowledge` - 薄弱知识点表

#### 3.5.1 表功能说明
存储识别的薄弱知识点信息，包括薄弱标识、识别时间、推荐优先级等。用于薄弱知识点的智能识别和推荐。

#### 3.5.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_weak_knowledge (
    id BIGSERIAL PRIMARY KEY,
    -- 业务字段
    user_id BIGINT NOT NULL,                                    -- 用户ID，关联用户中心服务
    knowledge_point_id BIGINT NOT NULL,                         -- 知识点ID，关联内容平台服务
    is_weak BOOLEAN NOT NULL DEFAULT FALSE,                     -- 是否为薄弱知识点
    identified_time BIGINT NOT NULL,                            -- 识别时间 (UTC秒数)
    recommendation_priority INTEGER NOT NULL DEFAULT 5,         -- 推荐优先级 (1-10)
    current_mastery NUMERIC(5,4) NOT NULL DEFAULT 0.0000,       -- 当前掌握度快照
    target_mastery NUMERIC(5,4) NOT NULL DEFAULT 0.7000,        -- 目标掌握度
    -- 标准字段
    status SMALLINT NOT NULL DEFAULT 1,                         -- 状态 (0:禁用, 1:启用)
    create_time BIGINT NOT NULL,                                -- 创建时间 (UTC秒数)
    update_time BIGINT NOT NULL,                                -- 更新时间 (UTC秒数)
    delete_time BIGINT DEFAULT NULL                             -- 删除时间 (UTC秒数)
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_weak_knowledge IS '薄弱知识点表 - 存储识别的薄弱知识点信息';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.id IS '主键ID';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.user_id IS '用户ID，关联用户中心服务';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.knowledge_point_id IS '知识点ID，关联内容平台服务';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.is_weak IS '是否为薄弱知识点';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.identified_time IS '识别为薄弱知识点的时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.recommendation_priority IS '推荐优先级 (1-10，数值越小优先级越高)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.current_mastery IS '识别时的当前掌握度快照';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.target_mastery IS '目标掌握度';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.status IS '状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.create_time IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.update_time IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN tbl_mastery_weak_knowledge.delete_time IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';

-- 索引定义
CREATE UNIQUE INDEX uk_mastery_weak_knowledge_user_kp ON tbl_mastery_weak_knowledge(user_id, knowledge_point_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_weak_knowledge_user ON tbl_mastery_weak_knowledge(user_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_weak_knowledge_kp ON tbl_mastery_weak_knowledge(knowledge_point_id) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_weak_knowledge_is_weak ON tbl_mastery_weak_knowledge(is_weak) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_weak_knowledge_priority ON tbl_mastery_weak_knowledge(recommendation_priority) WHERE delete_time IS NULL;
CREATE INDEX idx_mastery_weak_knowledge_identified_time ON tbl_mastery_weak_knowledge(identified_time) WHERE delete_time IS NULL;
```

#### 3.5.3 索引设计策略

**索引设计标准表格**：
| 索引名称 | 索引字段 | 索引类型 | 用途说明 | 预期查询场景 | 性能评估 |
|---------|---------|---------|---------|-------------|---------|
| `uk_mastery_weak_knowledge_user_kp` | user_id,knowledge_point_id | btree | 用户知识点薄弱标识唯一性 | 按
_answer_log | - | 排序键索引 | 应用层保障 | 低风险 |
| 查询当前掌握度 | Read | tbl_mastery_knowledge_mastery | user_id,knowledge_point_id | uk_mastery_knowledge_mastery_user_kp | 应用层校验 | 低风险 |
| 更新掌握度 | Update | tbl_mastery_knowledge_mastery | user_id,knowledge_point_id | uk_mastery_knowledge_mastery_user_kp | 应用层事务 | 中风险 |
| 更新外化掌握度 | Update | tbl_mastery_external_mastery | user_id,knowledge_point_id | uk_mastery_external_mastery_user_kp | 应用层事务 | 中风险 |
| 更新薄弱知识点 | Update | tbl_mastery_weak_knowledge | user_id,knowledge_point_id | uk_mastery_weak_knowledge_user_kp | 应用层事务 | 中风险 |

#### 6.2.4 性能考量与索引支持
答题更新场景是高频操作，当前索引设计能够有效支持：
- 通过复合唯一索引快速定位用户知识点记录
- 更新时间索引支持最近更新记录的查询
- ClickHouse答题日志表的排序键设计支持高效的时序分析

---

### 6.3 场景/故事: 教师查看班级掌握度报告 (管理场景)

#### 6.3.1 场景描述
教师需要查看班级学生的掌握度分布情况，包括平均掌握度、薄弱知识点分析等，用于指导教学决策。

#### 6.3.2 涉及的主要数据表
* `tbl_mastery_statistics` (掌握度统计表)
* `tbl_mastery_knowledge_mastery` (知识点掌握度表)
* `tbl_mastery_weak_knowledge` (薄弱知识点表)

#### 6.3.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 查询班级统计数据 | Read | tbl_mastery_statistics | class_id,stat_type='class' | 排序键索引 | 最终一致性 | 低风险 |
| 查询薄弱知识点分布 | Read | tbl_mastery_weak_knowledge | is_weak=true | idx_mastery_weak_knowledge_is_weak | 应用层校验 | 中风险 |
| 生成报告数据 | Read | tbl_mastery_knowledge_mastery | user_id IN (...) | idx_mastery_knowledge_mastery_user | 应用层校验 | 中风险 |

#### 6.3.4 性能考量与索引支持
教师查看报告场景主要涉及聚合查询，ClickHouse统计表的设计能够有效支持：
- 按班级和统计类型的排序键设计支持快速过滤
- 预聚合的统计数据减少实时计算压力
- 分区设计支持按时间范围的高效查询

---

### 6.4 场景/故事: 学生查看个人学习报告 (反思场景)

#### 6.4.1 场景描述
学生查看自己的掌握度情况和学习建议，包括当前掌握度、薄弱知识点、学习趋势等。

#### 6.4.2 涉及的主要数据表
* `tbl_mastery_external_mastery` (外化掌握度表)
* `tbl_mastery_weak_knowledge` (薄弱知识点表)
* `tbl_mastery_answer_log` (答题记录日志表)

#### 6.4.3 数据操作流程 (CRUD 分析)

**标准CRUD分析表格式**：
| 操作步骤 | 操作类型 | 涉及表名 | 关键查询条件 | 预期使用索引 | 数据一致性保障 | 性能风险评估 |
|---------|---------|---------|-------------|-------------|---------------|-------------|
| 查询外化掌握度 | Read | tbl_mastery_external_mastery | user_id | idx_mastery_external_mastery_user | 应用层校验 | 低风险 |
| 查询薄弱知识点 | Read | tbl_mastery_weak_knowledge | user_id,is_weak=true | idx_mastery_weak_knowledge_user | 应用层校验 | 低风险 |
| 查询学习趋势 | Read | tbl_mastery_answer_log | user_id,时间范围 | 排序键索引 | 最终一致性 | 中风险 |

#### 6.4.4 性能考量与索引支持
个人报告查询场景涉及用户维度的数据聚合：
- 用户维度的索引设计支持快速过滤个人数据
- ClickHouse答题日志的用户ID排序键支持高效的个人数据查询
- 缓存策略可以进一步提升查询性能

---

## 7. 设计检查清单 (Design Checklist)

### 7.1 需求与架构对齐性检查

**标准化检查表格式**：每行必须对应一个具体的需求点或用户故事，确保可追溯性

| 需求点/用户故事 (来自PRD)        | 对应架构模块 (来自TD)           | 关联核心数据表 (本次设计) | ①需求覆盖完整性 | ②架构符合性 | ③一致性与无冲突 | ④业务场景满足度 | ⑤可追溯性与依赖清晰度 | 备注与待办事项                                                                                                                                  |
| -------------------------------- | ----------------------------- | --------------------- | --------------- | ----------- | --------------- | ------------- | ------------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| 新学生系统初始化（冷启动场景） | 掌握度策略服务+用户分层计算 | `tbl_mastery_user_tiering`, `tbl_mastery_knowledge_mastery`, `tbl_mastery_external_mastery` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景6.1已覆盖。通过用户分层计算和初始掌握度设置支持冷启动，设计符合架构中的分层计算模块。 |
| 学生日常答题学习（核心使用场景） | 掌握度策略服务+实时计算引擎 | `tbl_mastery_knowledge_mastery`, `tbl_mastery_answer_log`, `tbl_mastery_external_mastery` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景6.2已覆盖。通过实时掌握度更新和答题日志记录支持核心学习场景，ClickHouse选型满足高吞吐写入需求。 |
| 教师数据驱动教学（管理场景） | 掌握度分析服务+统计报告模块 | `tbl_mastery_statistics`, `tbl_mastery_weak_knowledge` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景6.3已覆盖。通过预聚合统计数据和薄弱知识点分析支持教师教学决策，设计符合架构中的分析服务模块。 |
| 学生自主学习规划（反思场景） | 掌握度策略服务+个人分析模块 | `tbl_mastery_external_mastery`, `tbl_mastery_weak_knowledge`, `tbl_mastery_answer_log` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景6.4已覆盖。通过外化掌握度展示和个人学习趋势分析支持自主学习规划。 |

---

### 7.2 数据库设计质量检查

**(针对本次设计的每个核心表或整体设计方案进行评估)**

| 检查维度                               | 评估结果 (✅/❌/N/A) | 具体说明与改进建议 (如果存在问题)                                                                                                |
| -------------------------------------- | ------------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| **⑥ 设计规范符合性** | ✅ | 严格遵循PostgreSQL和ClickHouse设计规范，命名规范统一，数据类型选择恰当，注释完整清晰 |
|   - 命名规范                           | ✅ | 所有表名使用`tbl_`前缀，字段名使用snake_case，索引命名遵循规范 |
|   - 标准字段                           | ✅ | PostgreSQL表包含完整的标准字段（id, create_time, update_time, delete_time, status） |
|   - 数据类型选择                       | ✅ | 使用BIGINT存储UTC时间戳，NUMERIC存储精确小数，LowCardinality优化ClickHouse字符串字段 |
|   - 注释质量                           | ✅ | 所有表和关键字段都有清晰的中文注释，包含字段用途和取值说明 |
|   - 通用设计原则与反模式规避             | ✅ | 避免了巨石表、过度规范化等反模式，遵循单一职责原则 |
| **⑦ 性能与可伸缩性考量** | ✅ | 索引设计合理，分区策略恰当，表引擎选择最优，支持预估数据量和查询模式 |
|   - 索引策略 (主键、排序键、跳数索引等)  | ✅ | PostgreSQL使用B-tree索引优化查询，ClickHouse排序键设计支持主要查询模式 |
|   - 分区/分片策略 (如果适用)             | ✅ | ClickHouse按月分区，支持TTL策略和查询优化 |
|   - 表引擎选择 (尤其 ClickHouse)        | ✅ | 使用MergeTree引擎，支持高性能写入和查询 |
|   - 数据压缩策略 (尤其 ClickHouse)      | ✅ | 使用Delta+ZSTD压缩优化存储，T64优化整数字段 |
| **⑧ 可维护性与可扩展性** | ✅ | 表结构清晰易懂，支持业务需求演进，设计具备良好的可扩展性 |
| **⑨ 数据完整性保障** | ✅ | 通过唯一约束、NOT NULL约束保障数据有效性，应用层保障逻辑外键一致性 |
| **⑩ (可选) 安全性考量** | ✅ | 识别了用户ID等敏感数据，建议在应用层进行权限验证和数据脱敏 |
| **其他特定于数据库的检查点** (例如)      |                     |                                                                                                                                |
|   - PostgreSQL: `TIMESTAMPTZ` 使用        | N/A | 使用BIGINT存储UTC时间戳，符合项目规范要求 |
|   - PostgreSQL: `JSONB` 使用合理性        | N/A | 当前设计未使用JSONB字段，如需扩展可考虑在配置表中使用 |
|   - ClickHouse: `LowCardinality` 使用     | ✅ | 合理使用LowCardinality优化枚举类型字段的存储和查询性能 |
|   - ClickHouse: 物化视图设计合理性        | N/A | 当前设计未包含物化视图，后续可根据查询需求添加 |

## 8. 特殊设计考量与权衡

### 8.1 数据库选型权衡
**PostgreSQL vs ClickHouse选型决策**：
- **PostgreSQL**：选择用于存储需要频繁CRUD操作的事务性数据，如掌握度记录、用户分层等
- **ClickHouse**：选择用于存储仅追加的分析型数据，如答题日志、统计数据等
- **权衡考虑**：牺牲了单一数据库的简洁性，换取了更好的性能和可扩展性

### 8.2 索引设计权衡
**复合索引 vs 单字段索引**：
- 优先设计复合索引支持主要查询模式
- 单字段索引作为补充，支持灵活查询
- 权衡了索引维护成本与查询性能

### 8.3 数据一致性权衡
**物理外键 vs 逻辑外键**：
- 严格禁止物理外键约束，采用应用层保障一致性
- 提升了性能和可扩展性，但增加了应用层复杂度
- 通过事务和校验机制保障数据一致性

### 8.4 数据生命周期管理
**TTL策略设计**：
- ClickHouse表设置合理的TTL策略（答题日志2年，统计数据3年）
- PostgreSQL表通过软删除机制管理数据生命周期
- 平衡了存储成本与数据可用性

## 9. 数据量预估与增长趋势分析

### 9.1 核心表数据量预估

| 表名 | 初期数据量 | 1年后预估 | 3年后预估 | 增长模式 | 关键影响因素 |
|------|-----------|----------|----------|---------|-------------|
| `tbl_mastery_user_tiering` | 10万 | 100万 | 300万 | 线性增长 | 用户数量增长 |
| `tbl_mastery_knowledge_mastery` | 100万 | 1000万 | 3000万 | 线性增长 | 用户数×知识点数 |
| `tbl_mastery_external_mastery` | 100万 | 1000万 | 3000万 | 线性增长 | 与掌握度表同步 |
| `tbl_mastery_weak_knowledge` | 50万 | 500万 | 1500万 | 线性增长 | 薄弱知识点识别频率 |
| `tbl_mastery_course_config` | 1万 | 10万 | 30万 | 缓慢增长 | 课程内容扩展 |
| `tbl_mastery_answer_log` | 1000万 | 1亿 | 10亿 | 指数增长 | 用户活跃度×答题频率 |
| `tbl_mastery_statistics` | 100万 | 1000万 | 5000万 | 线性增长 | 统计频率×统计维度 |

### 9.2 存储容量规划

**PostgreSQL存储预估**：
- 1年后：约500GB
- 3年后：约1.5TB
- 建议配置：主从复制+读写分离

**ClickHouse存储预估**：
- 1年后：约2TB（压缩后）
- 3年后：约20TB（压缩后）
- 建议配置：分布式集群+多副本

## 10. 参考资料与附录

* 产品需求文档 (PRD): [PRD - 掌握度策略 - V1.0]
* 技术架构方案 (TD): [be-s2-1-ai-td-掌握度策略-claude-sonnet-4.md]
* 数据实体设计 (DE): [be-s3-1-ai-de-掌握度策略-claude-sonnet-4.md]
* PostgreSQL 设计规范与约束文件: [rules-postgresql-code-v2.md]
* ClickHouse 设计规范与约束文件: [rules-clickhouse-code-v2.md]
* 技术栈约束文件: [rules-tech-stack.md]

---

## 设计总结

本数据库设计严格遵循了PostgreSQL和ClickHouse的设计规范，通过合理的数据库选型、索引策略和表结构设计，有效支持了掌握度策略系统的核心业务需求。设计充分考虑了性能、可扩展性、可维护性等质量属性，为系统的长期发展奠定了坚实的数据基础。

关键设计亮点：
1. **双数据库架构**：PostgreSQL处理事务性数据，ClickHouse处理分析型数据
2. **逻辑外键设计**：避免物理外键约束，提升性能和可扩展性
3. **完善的索引策略**：支持主要查询模式，平衡查询性能与维护成本
4. **合理的分区和TTL策略**：优化存储成本和查询性能
5. **标准化的字段设计**：统一的命名规范和数据类型选择

--- 数据库设计完成，已按模板要求完成质量检查，等待您的命令，指挥官
---
description: 一套结构化的工作流程，用于指导从Figma设计到组件实现的AI辅助前端开发。
globs:
alwaysApply: false
---
# AI 辅助前端开发工作流 (v1.0)

## 当前开发进度
详细进度请参见：[`luban-agent/ai-workflows/progress/{app_name}-{page_name}-progress.md`](luban-agent/ai-workflows/progress/{app_name}-{page_name}-progress.md)
当前主要阶段：[未开始]


## 1. 引言

### 1.1. 目的
本文档旨在定义一套结构化的工作流程，指导开发者如何有效利用 AI 助手，结合项目已定义的规范，高效、高质量地完成从 Figma 设计稿到前端组件的开发任务。

### 1.2. 核心思想
本流程强调 **人机协作 (Human-AI Collaboration)**。AI 作为强大的辅助工具，负责执行明确指令下的图像处理、布局分析、文档生成和初始代码生成等任务；开发者则扮演**需求阐述者、评审者、测试执行者和最终决策者**的角色。流程成功的关键在于在正确的时间为 AI 提供清晰、上下文相关的输入，并对 AI 生成的产出进行迭代优化。

### 1.3. 参考规范
本流程的执行依赖于各类输入及项目中隐含的规范。开发者和 AI 在各阶段需参考：
    * **设计解读与任务分解规范:**
        * `luban-agent/ai-rules/frontend/public-rules/layout-analysis.mdc`:指导如何从Figma进行布局分析，是`Phase 1`的核心依据。
        * `luban-agent/ai-rules/frontend/public-rules/component-breakdown.mdc`:定义组件拆分的原则（粒度、职责），是`Phase 2.1`的核心依据。
        * `luban-agent/ai-rules/frontend/public-rules/design-token-extraction.mdc`:规范如何从Figma中精确提取Design Tokens，是`Phase 2.3`的核心依据。
        * `luban-agent/ai-rules/frontend/public-rules/asset-extraction.mdc`:定义何时以及如何导出SVG/PNG等静态资源，是`Phase 2.3`的核心依据。
    * **代码生成与实现规范:**
        * `luban-agent/ai-rules/frontend/public-rules/gen-view-guide.mdc`:视图代码生成指南与规范。
    * **质量保证与验证规范:**
        * `luban-agent/ai-rules/frontend/public-rules/visual-qa.mdc`:指导如何进行像素级视觉对比，是`Phase 4`的核心依据。
    * **小工作流:**
        * `luban-agent/ai-workflows/dev-fe/fe-design-to-code-workflow.md` (隐含，用于`Phase 2`和`Phase 3`的自动化流程)


### 1.4. 进度追踪与结果记录
为确保对开发进度的清晰掌握和各任务结果的可追溯性，本工作流采用分层级的进度追踪机制：

**A. 宏观工作流进度 (`luban-agent/ai-workflows/progress/{app_name}-{page_name}-progress.md`)**
* **用途：** 此文件用于跟踪整个 `fe-ai-dev-new-project-workflow.md` 工作流中各个 `Phase` (阶段) 的完成状态。
* **结构：** 建议此文件包含 `fe-ai-dev-new-project-workflow.md` 的主要阶段列表，并标记每个阶段的状态 (例如：[未开始], [进行中], [已完成], [遇到问题], [待评审]) 及完成时间。
* **更新时机：** 在本工作流的**每个 `Phase` 主要步骤完成后**，AI 需负责更新此 `...-progress.md` 文件中对应 `Phase` 的状态和结果摘要。
* **更新内容示例 (在 `...-progress.md` 文件中):**
    * `## Phase 0: 资产准备 - [已完成] (YYYY-MM-DD HH:MM)`
        * `结果：PNG 图像已生成并保存至 ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}.png。`
    * `## Phase 1: 布局分析文档生成 - [进行中]`
* **责任方：** AI 在其能力范围内，并在明确指令下，会尝试更新此文件。最终的准确性和完整性由开发者负责审核和保障。

**B. 微观任务项进度 (`luban-agent/ai-generates/fe/tasks/[应用名称]/[页面简称]/[页面简称]_tasktree.md`)**
* **用途：** 此文件用于跟踪 `Phase 2` 中拆解出的具体前端组件或任务项的开发状态。
* **更新时机：** 当 `Phase 3` 中的某个具体组件任务（或其他细分任务）完成后，AI 需负责更新此 `_tasktree.md` 文件中对应的任务项。
* **更新内容：** 更新应至少包括：
    * 对应任务的状态标记（例如：[未开始], [进行中], [已完成], [遇到问题], [待评审]）。
    * 任务完成的结果摘要或指向结果产物（例如代码路径、文档路径、测试报告简述）的链接。
    * （可选）完成时间戳。
* **责任方：** 与宏观进度文件类似，AI 可辅助更新，开发者最终负责。


---
## 2. 开发工作流阶段

本工作流将开发过程划分为多个阶段，旨在通过人机协作，系统性地将 Figma 设计转化为可用的前端组件。

### Phase 0: 资产准备（通过MCP服务）

* **阶段核心:** 开发者提供初始信息，AI 通过调用外部 "mcp服务" 获取图像。
* **AI 角色:** 脚本调用者 / 信息传递者。
* **目标:** 利用开发者提供的 Figma 链接，通过 AI 调用 "mcp服务" 下载目标页面的一个或多个状态的 PNG 图像，并按照 {app_name}-{page_name}.png 的命名规则保存。
* **输入:**
    * Figma 链接 (字符串)
        * 多状态情况：提供多个figma链接，每个链接都是同一个页面的不同状态
        * 单状态情况：提供一个figma链接，该链接就是该页面的设计稿
    * 应用名称 (`{app_name}`) (字符串)
    * 页面名称 (`{page_name}`) (字符串)
* **输出:**
    * 单状态情况：生成单个图像文件
        * `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}.png` (图像文件)
    * 多状态情况：生成带有状态后缀的多个图像文件
        * `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}-{state_name}.png` (图像文件)
        * `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}-{state_name}.png` (图像文件)
        * `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}-{state_name}.png` (图像文件)
        * `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}-{state_name}.png` (图像文件)
* **步骤:**
    1.  **开发者向AI提供信息:**
        * 开发者将 页面Figma 链接、应用名称 (`{app_name}`) 和页面名称 (`{page_name}`) 作为输入提供给 AI。
    2.  **AI分析输入:**
        * AI识别输入是单状态还是多状态。
    3.  **AI循环调用“mcp”服务:**
        * 对于多状态，AI遍历每个状态的figma链接，为每个状态调用服务。
        * 对于单状态，AI只调用一次。
    4.  **图像文件命名与保存:**
        * 单状态：AI 根据开发者提供的应用名称 (`{app_name}`) 和页面名称 (`{page_name}`)，将下载的 PNG 图像命名为`{app_name}-{page_name}.png`。
        * 多状态：AI 根据开发者提供的应用名称 (`{app_name}`) 和页面名称 (`{page_name}`)，将下载的 PNG 图像命名为`{app_name}-{page_name}-{state_name}.png`。
        * AI 将此命名的图像文件保存到预定路径：`luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/`。
* **完成标志:**
    * `{app_name}-{page_name}.png` 成功下载并可用。
    * AI 确认脚本执行完成 (无严重错误)。
    * 宏观进度文件 `luban-agent/ai-workflows/progress/{app_name}-{page_name}-progress.md` 中 Phase 0 的状态和结果已更新,并注明本次处理的状态列表。

### 下一步操作
Phase 0 完成后，下一步将进入 **Phase 1: 布局分析文档生成**。

---
### Phase 1: 布局分析文档生成

* **阶段核心:** 基于开发者输入，AI 驱动内容生成。同时要基于初始状态的布局进行分析，并参考其他状态识别动态区域。
* **AI 角色:** 布局分析器 / 文档生成器。
* **目标:** 基于 Figma 设计，从前端开发角度生成描述页面布局的 `components-location.md` 文件，同时清晰描述页面级骨架结构。
* **输入:**
    * Figma 链接 (主要使用初始状态的链接)
    * 页面名称 (`{page_name}`) (字符串)
    * 应用名称 (`{app_name}`) (字符串)
    * figma设计稿的图片：
        * 单状态：phase 0下载的单个设计图
            * `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}.png`
        * 多状态：phase 0下载的多个设计图
            * `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}-{state_name}.png` 
            * `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}-{state_name}.png` 
            * `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}-{state_name}.png` 
    * 布局分析规范：`luban-agent/ai-rules/frontend/public-rules/layout-analysis.mdc`
* **输出:**
    * `luban-agent/ai-generates/fe/tasks/[应用简称]/[页面简称]/components-location.md` (Markdown 文档)
* **步骤:**
    1.  **开发者提供输入:**
        * 开发者提供 Figma 链接、页面名称和应用名称。
    2.  **AI 执行 Prompt:**
        * **Prompt:**
            ```
            @figma链接 @{app_name}-{page_name}.png @luban-agent/ai-rules/frontend/public-rules/layout-analysis.mdc
            请启动mcp服务获取对应figma链接的信息
            你将收到一个或多个页面状态的PNG图像。请以...--default.png作为基础，进行主要的布局分析。 然后，请对比其他状态的图像（如...--loading.png, ...--error.png），在分析文档的末尾简要说明哪些区域是动态变化的。例如：‘在loading状态下，主内容区域被一个加载动画替代’或‘在error状态下，页面顶部会额外出现一个错误提示条’。这个分析将为后续的状态管理提供关键信息。作为一名经验丰富的前端架构师，请你对页面名称为“{page_name}”（所属应用“{app_name}”）的设计稿链接和PNG图像，进行一次从前端开发和DOM组织角度出发的、**极其详尽和结构化的布局分析**。输出的 `components-location.md` 文档必须为后续的组件拆解和实现提供坚实、无歧义的布局依据。请严格遵循以下要求：
            从前端开发组织 DOM 元素布局的角度，对提供的页面图像链接进行详细的布局分析。请务必遵循以下要求，确保描述的准确性和全面性：
            1. 描述顺序与层级： 采用从上到下、从左到右的顺序描述页面中的所有模块和子模块，确保布局的层级和顺序清晰。
            2. 自然语言描述： 只使用自然语言描述布局方式，不提供任何代码或具体的单位（如像素px）。
            3. 避免具体数值： 描述中禁止使用任何具体的像素值或尺寸数值，只提供相对位置、相对大小和布局关系。
            4. 聚焦相对位置与布局方式： 重点描述每个模块和子模块的相对位置、布局方式、以及模块之间的关系。
                * 相对位置： 明确描述模块在整体页面中的位置关系（例如：“页面顶部的返回按钮位于页面的左上角”，“页面标题位于返回按钮的右侧，两者紧邻并有固定间隙”）。
                * 布局方式： 描述采用的布局方式（例如 flexbox、grid、垂直/水平排列），以及元素如何排列对齐（例如：居中对齐、左对齐、右对齐、等间距分布）。
                * 模块间关系： 描述模块之间的距离、比例或对齐方式（例如：“左侧区域的宽度占整个页面的 1/4，右侧区域占 3/4”，“返回按钮与标题之间有固定的小间隙”）。
            5. 详细描绘小模块关系： 详细描述所有小模块（如按钮、文本、图标等）之间的相对位置关系、间距和排列顺序，确保不遗漏任何细节。
                * 明确“左侧”、“中间”、“右侧”区域内的相对位置。* 强调元素之间的对齐方式（例如：左对齐、右对齐、垂直居中）。
            6. 全面性和准确性： 确保对所有可见的模块和子模块的相对层级、位置关系、对齐、间距和排列方式都进行了清晰、准确的描述。
            完成后的形成的文档放置在apps/[应用简称]/app/task/[页面简称]/components-location.md中，请开始分析。
            现在，请开始对设计稿进行上述详细的布局分析。
            ```
        * AI 根据 Prompt 处理 Figma 链接，并在指定路径生成 `components-location.md` 文件。
* **完成标志:**
    * `components-location.md` 已生成并保存到正确位置。
    * `components-location.md` 的内容根据 Prompt 要求准确反映了页面布局，并包含了对页面级骨架结构的描述。

* **开发者评审:**
    * 开发者检查`components-location.md`的描述是否准确、详尽，是否符合`layout-analysis.mdc`的要求。确认无误后，授权进入下一阶段，

### 下一步操作
Phase 1 完成后，下一步将进入 **Phase 2: 任务拆解与文档生成**。

---
### Phase 2: 任务拆解与文档生成

* **阶段核心:** AI 驱动的任务分解和全面的文档创建，重点优化图形资源的识别与提取，强调不可替代性和实现复杂度，以及对于设计细节的精确提取。
* **AI 角色:** 任务分解器 / 文档套件生成器/ 图形资源分析与决策辅助。
* **目标:** 利用 `components-location.md` 和 Figma 链接，自动化生成一套开发任务文档，并智能决策哪些图形元素因其独特性、复杂性而需要导出为 SVG 或 PNG，哪些应通过代码实现。确保 `design-tokens.json `和各任务项文档中的CSS样式信息直接且精确地来源于Figma设计稿。为每个待开发组件确定规范且唯一的建议名称。

* **输入:**
    * `luban-agent/ai-generates/fe/tasks/[应用简称]/[页面简称]/components-location.md` (来自 Phase 1)
    * Figma 链接 (字符串)
    * 页面名称 (`{page_name}`) (字符串)
    * 应用名称 (`{app_name}`) (字符串)
    * 所有在phase 0下载的状态PNG图像及其状态名
    * `luban-agent/ai-workflows/dev-fe/fe-design-to-code-workflow.md` (自动化逻辑参考)
    * `luban-agent/ai-rules/frontend/public-rules/component-breakdown.mdc` 组件拆分规则
    * `luban-agent/ai-rules/frontend/public-rules/design-token-extraction.mdc` design token提取规范
    * `luban-agent/ai-rules/frontend/public-rules/asset-extraction.mdc` 静态资源提取规范
* **输出:** 所有文件放置在 `luban-agent/ai-generates/fe/tasks/[应用名称]/[页面简称]/` 目录下：
    * `[页面简称]_tasktree.md` (任务树文档)
    * `[序号]-[组件类型].md` (多个任务项文档，如果是多个设计稿，增加“状态描述”部分，详细说明该组件在不同状态下的具体样式和内容)
    * `[页面简称]-state-matrix.yaml` (交互状态矩阵：基于一个/多个设计稿生成，内容更精确，明确描述每个组件在不同设计状态下的视觉表现)
    * `[页面简称]-animation-hints.yaml` (动画提示文档)
    * `design-tokens.json` (统一设计规范)
    * `ai-confidence-summary.yaml` (AI 置信度汇总)
    * `svg/` (子文件夹，包含必要且适合导出的 SVG 文件。例如，复杂的矢量图标、品牌 IP 的矢量版本、代码难以精确绘制的矢量形状等。命名：`[任务ID或清晰描述性名称].svg`)
    * `png/` (子文件夹，包含必要且适合导出的 PNG 文件。例如，品牌 IP 的位图版本（当矢量不适用或效果不佳时）、照片类元素、代码难以实现的复杂背景图、无法用 SVG 良好表达且体积可控的纹理等。命名：`[任务ID或清晰描述性名称].png`)
* **步骤:**
    1.  **开发者提供输入:**
        * 开发者提供 `components-location.md` 的路径、Figma 链接、页面名称和应用名称。
    2.  **AI确认接受到的输入材料:**
        * AI对已经接受到的材料进行汇总，并向开发者进行确认，开发者**确认后**才能继续下一个步骤。
    3.  **AI 执行 Prompt:**
        * **Prompt:**
            ```

            启动mcp服务获取figma链接的信息。
            我希望你根据design-to-code-workflow工作流和输入材料，完整执行一次“设计到前端任务文档”的自动化拆分流程，输出对应的所有文档与资源。请严格按照文档结构、命名规则、格式规范进行输出。该页面的名称为：{page_name}，所属的应用为{app_name}。
            📥 输入：
            1. PRD 文件内容：无
            2. Figma 设计图链接：{figma链接}
            📤 输出要求：
            请按如下结构和命名规范输出以下所有内容，并组织在目录 `apps/[应用名称]/task/[页面简称]/` 下：
            1. `[页面简称]_tasktree.md`（任务树文档）
             包含任务概述（任务对应的figma链接）、任务列表（状态、优先级）、依赖关系图、开发顺序建议、注意事项、各组件在页面的位置。
            2. `[序号]-[组件类型].md`（任务项文档）
             每个组件为一个任务项，包含：简要描述、PRD 条目映射、Figma 链接、SVG 图标、Figma设计稿中对应节点的CSS样式、验收标准、依赖、状态、时间戳、ai_confidence 与 requires_review 字段。
            3. `[页面简称]-state-matrix.yaml`（交互状态矩阵）
             记录每个组件的状态、状态迁移逻辑与触发方式。
            4. `[页面简称]-animation-hints.yaml`（动画提示文档）
             给出状态切换时推荐的动效类名或 CSS 属性。
            5. `design-tokens.json`（统一样式 Token）
             提取颜色、字体、边距、圆角等 design token。精确提取Figma设计稿中定义的所有Design Tokens，特别是颜色 (必须为Figma中定义的HEX/RGBA值)、字体 (family, size, weight, line-height)、主要间距单位、圆角尺寸等。确保这些值是设计规范的直接反映，而非从PNG图片中估算。
            6. `ai-confidence-summary.yaml`（AI 置信度汇总）
             列出所有任务的 AI 可实现可信度及是否推荐人工 review。
            7. svg/: 存放 **判断为最适合且必要（基于实现复杂度和视觉保真度）导出为 SVG** 的矢量图形资源。避免导出可以用CSS轻松实现的简单几何形状。包括但不限于：复杂图标、品牌IP的矢量版本、代码难以精确绘制的独特矢量形状。
            8.  png/: 存放 **判断为最适合且必要（基于实现复杂度和视觉效果）导出为 PNG** 的图形资源。包括但不限于：品牌IP的位图版本（若矢量不适用）、照片类元素、代码难以实现的复杂或独特的背景图、无法用SVG良好表达且体积可控的纹理。
            📌 附加要求：
            - 拆分粒度为“模块级”，即每个任务项应代表页面中的一个功能完整、结构独立的视图区域或逻辑单元。避免拆分到按钮、图标或字段级别。
            - 每个任务项应能在 1 天内完成开发，具备输入输出、状态逻辑、样式定义。
            - 所有子任务项应聚焦于页面模块（如卡片组件、列表、侧边栏、表单区块、内容区域等）。
            - 状态矩阵和动画提示需覆盖所有存在状态变化的组件。
            - 每份文档内容应完整，语义清晰，可供前端开发直接使用。
            - 所有文档应严格遵守命名规则与格式标准。
            - 若部分信息在 PRD 或 Figma 中缺失，可尝试推理或在文档中标注“待补充。
            - **图形资源提取智能化决策：**
                - **优先代码实现：** 对于简单的几何形状、纯色块、标准阴影、圆角、边框以及可以通过CSS和Design Tokens高效实现的视觉效果，**应在任务文档中明确建议使用代码实现，不导出为图像资源。**
                - **SVG 适用场景：** 针对**代码实现成本过高或无法精确复现**的复杂矢量路径、不规则形状、精细的矢量图标、品牌IP图案（矢量版）等，考虑导出为 SVG。确保导出的 SVG 是优化过的。
                - **PNG 适用场景：** 针对**代码无法实现或矢量格式无法良好表现**的视觉元素，如：
                    * **品牌IP图案**（当其包含复杂光影或纹理，位图效果更佳时，或作为备选方案）。
                    * **照片类图像**（例如用户头像、产品图片等）。
                    * **独特的、程序化生成困难的背景图或纹理**。
                    * 无法用SVG良好表达且体积可控的**复杂纹理小图标**。
                    **AI需评估导出PNG的必要性，避免不必要地将大型UI模块或背景导出为PNG，除非该背景本身就是一张不可替代的图像。**
                - **图标库协同：** 若项目中存在图标库，优先推荐使用。若Figma中的图标适合加入库，建议导出为SVG并按规范添加，而非单独引用。
                - **稳定性与开发者评审：** AI应尝试判断SVG/PNG导出的质量和必要性。如果某个元素的导出结果可能不佳、文件过大或其必要性存疑，应在 `ai-confidence-summary.yaml` 或任务项文档中标记 `requires_review`，并建议开发者手动检查、优化或寻找替代实现方案（如与设计师沟通确认是否可简化）。
            ```
        * AI 根据 Prompt 和 `design-to-code-workflow` 处理输入，生成所有指定的文档和 SVG 资源到正确的目录结构中。
* **完成标志:**
    * 所有列出的输出文档和 `svg/` 文件夹和`png/`文件夹已在指定路径生成。
    * 文档遵循 Prompt 中概述的结构和内容要求。
    * SVG 资源已正确提取并命名。
    * PNG 资源已正确提取并命名。
    * `design-tokens.json`和各任务项文档中的css样式信息已确保精确来源于figma设计数据。

### 下一步操作
Phase 2 完成后，下一步将进入 **Phase 3: 组件实现**。

---

### Phase 3: 组件实现

* **阶段核心:** AI 辅助的单个组件代码生成，以及后续的页面级组件组装，开发者监督。
* **AI 角色:** 代码生成器 / 组件实现器/ 页面组装器。
* **目标:** 
    1. 针对 Phase 2 中拆解出的、并由开发者**当前选定**的单个任务项，使用指定的技术栈 (TypeScript, React, Tailwind CSS) 生成对应的前端组件代码，确保响应式设计并更新相关文档。
    2. 在页面所有核心组件完成后，根据 `components-location.md `和` _tasktree.md` 将这些组件组装成完整的页面视图。
    3. 所有组件都完成之后，需要在`apps/{app_name}/app/components/README.md`更新新组件的相关数据。
* **输入:**
    * **当前选定的任务项标识 (由开发者在启动此阶段时提供):**
        * 例如：任务序号 "01" 或 完整的任务项文件名 "01-Header-Navigation.md"。AI 将使用此标识结合 `{app_name}` 和 `{page_name}` 来定位下方的具体任务项文档。
    * **具体目标任务项文档 (AI根据“当前选定的任务项标识”定位):** `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{当前选定的任务项标识对应的文件名}.md`
    * **通用上下文文件:**
        * 任务树文档: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}_tasktree.md`
        * 动画提示: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}-animation-hints.yaml`
        * 状态矩阵: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}-state-matrix.yaml`
        * 布局信息: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/components-location.md`
        * 设计规范: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/design-tokens.json`
        * (可选，若任务项文档中已包含足够信息) phase 0下载的PNG: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}.png`
        * (可选，若任务项文档中已包含足够信息) Figma 链接
    * **规范与流程参考:**
        * UI 编码规范: `luban-agent/ai-rules/frontend/public-rules/gen-view-guide.mdc` 
        * 整体工作流: `luban-agent/ai-workflows/dev-fe/fe-design-to-code-workflow.md` 
    * **项目与技术栈信息:**
        * 应用名称 (`{app_name}`)
        * 页面名称 (`{page_name}`)
        * 技术栈: TypeScript, React, Tailwind CSS

* **输出:**
    * 已实现的组件代码，位于 `apps/{app_name}/app/components/{组件名}/` (建议为每个组件创建一个子目录，例如 `apps/{app_name}/app/components/HeaderNavigation/HeaderNavigation.tsx`)。
    * 已实现的所有组件的更新版 README 文档，位于 `apps/{app_name}/app/components/README.md`。
    * 已实现的组件应可在 `https://localhost:3013/{page_name}` 或特定组件预览页面查看 (开发者需确保环境配置)。
    * 微观任务项进度文件 `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}_tasktree.md` 中对应任务的状态更新。

* **步骤:**
    1.  **开发者选择任务并提供核心输入:**
        * 开发者查阅 `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}_tasktree.md`，选择一个**具体**的任务项进行实现 (例如任务 "01-Header-Navigation" 或其对应的文件名 "01-Header-Navigation.md")。
        * 开发者向 AI 提供：
            * **明确的“当前选定的任务项标识”** (例如 "01-Header-Navigation.md" 或 "任务01")。
            * 应用名称 (`{app_name}`) 和页面名称 (`{page_name}`)。
            * (如果需要，AI可提示开发者确认其他通用上下文文件的路径是否正确，或自动根据`{app_name}`和`{page_name}`推断)。
    2.  **AI 上下文加载与验证:**
        * **2.1. 识别文件清单:** AI根据Prompt指令，识别出完成本次组件实现所需的所有上下文件列表。
        * **2.2. 逐一加载与摘要:** AI按清单顺序，逐一读取每一个文件，每读取一个，就生成一句简短的内部摘要（例如：`design-tokens.json` - 已读取，包含颜色、字体等规范）
        * **2.3. 生成加载报告:** 在全部文件加载完毕后，AI**必须**向用户输出一份明确的“加载报告”，格式如下：
        ```
        报告：任务“{AI动态填写的任务项名称}”所需的所有上下文文件已加载完毕。清单如下：
        - [√] 核心任务详情: {具体任务项文档名}.md
        - [√] 整体任务结构: {..._tasktree.md}
        - [√] 动画效果: {...-animation-hints.yaml}
        - [√] 交互状态: {...-state-matrix.yaml}
        - [√] 页面布局: {components-location.md}
        - [√] 设计规范: {design-tokens.json}
        - [√] UI编码规范: {gen-view-guide.mdc}
        - [√] 整体开发流程参考: {fe-design-to-code-workflow.md}
        - [√] (可选) 页面PNG截图: {... .png}
        - [√] Figma设计稿链接

        所有上下文已准备就绪。现等待指令，开始生成组件代码。
        ```
    3.  **开发者确认，AI 执行 Prompt 生成组件:**
        * 开发者收到加载报告确认后，**回复“请开始生成组件代码”或类似指令**。
        * **AI收到指令后，才开始执行核心的代码生成任务**
        * **Prompt :**
            ```
            # Phase 3: 组件实现 - 任务指令

            ## 目标任务
            我需要你为应用 `{app_name}` 的页面 `{page_name}` 实现一个前端组件。

            **当前聚焦的任务项是：** (AI 在此动态填写从开发者处获取并定位到的具体任务项文档的名称或核心内容摘要，例如："01-Header-Navigation.md")

            ## **第一步：加载并确认所有输入 (MANDATORY FIRST STEP)**
            在你开始任何分析或编码之前，你的首要且必须完成的任务是：**加载并验证**以下“输入文件”部分列出的所有文档和信息。
            加载完成后，你**必须**按照“步骤2.3”中定义的格式，向我输出一份**加载报告**。
            **只有在我确认你的加载报告后，你才能继续执行第二步。**

            ### 输入文件 (上下文信息)
            你将使用以下已加载的文件来完成此任务：
            1.  **核心任务详情**: `@具体目标任务项文档` (这是你本次实现的主要依据，包含了该组件的Figma节点CSS、验收标准等)
            2.  **整体任务结构**: `@任务树文档` (路径: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}_tasktree.md` - 用于理解此组件在页面中的位置和依赖)
            3.  **动画效果**: `@动画提示` (路径: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}-animation-hints.yaml`)
            4.  **交互状态**: `@状态矩阵` (路径: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}-state-matrix.yaml`)
            5.  **页面布局**: `@布局信息` (路径: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/components-location.md`)
            6.  **设计规范**: `@设计规范` (路径: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/design-tokens.json`)
            7.  **UI编码规范**: `@UI编码规范` (路径: `luban-agent/ai-rules/frontend/project-rules/ui.mdc`)
            8.  **整体开发流程参考**: `@design-to-code-workflow.md` (路径: `luban-agent/ai-workflows/dev-fe/fe-design-to-code-workflow.md`)
            9.  (可选，若任务文档中信息不足) **页面PNG截图**: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}.png`
            10. **Figma设计稿链接**: (开发者提供或从任务树/任务项文档中获取)

            ### **第二步：生成组件代码 (待指令执行)**
            在收到我的明确指令后，请根据你已加载的全部上下文信息，执行以下操作：

            ### 技术栈与要求
            * **技术栈**: TypeScript, React, Tailwind CSS。
            * **代码复用**: 在开始编码前，请先检查目标项目路径 `apps/{app_name}/app/components/`下是否有已存在的、可直接复用的组件。如果找到合适的，优先复用；如果没有，则创建新组件。不要修改已经存在的组件！！
            * **响应式设计**: 我正在开发的是移动端前端，组件需要能动态适应不同的屏幕尺寸。在实现过程中，请**避免使用固定的 `px` 单位**，优先使用相对单位、百分比、flex/grid布局以及Tailwind CSS的响应式修饰符（如 `sm:`, `md:`）来实现流式布局和弹性调整。
            * **代码产出位置**:
                * 组件代码应放置在 `apps/{app_name}/app/components/{组件名}/` 目录下。请为组件创建一个合适的名称 (例如，如果任务是 "01-Header-Navigation.md"，组件名可以是 `HeaderNavigation`)，并在该目录下创建 `组件名.tsx` 文件。
                * 同时，在该组件目录下创建或更新 `README.md` 文件，简要描述组件功能、props（如果有）以及如何使用。
            * **可预览性**: 理论上，完成的组件应能通过某种方式在 `https://localhost:3013/{page_name}` 或特定的组件预览环境中进行查看（这部分由开发者负责集成和配置）。你的任务是确保组件本身是可独立渲染和功能完整的。
            * **遵循规范**: 严格遵守 `@UI编码规范` 和 `@设计规范` 中的指导。

            ### 执行步骤
            1.  启动mcp服务获取对应的figma链接信息来辅助完成开发
            2.  仔细阅读并理解 `@具体目标任务项文档` 中的所有要求。
            3.  结合所有提供的上下文文件，规划组件的结构、状态和逻辑。
            4.  检查现有组件库以进行复用。
            5.  使用 TypeScript, React, 和 Tailwind CSS 编写组件代码。
            6.  确保代码的响应式特性和对移动端的良好支持。
            7.  将代码和 README 文件保存到指定的输出路径。
            8.  (内部步骤) 准备更新 `_tasktree.md` 中对应任务项的状态。

            请开始为任务“**{AI动态填写的任务项名称}**”生成组件代码。
            ```
        * AI 根据上述信息和 Prompt，执行代码生成。
    4.  **AI 更新任务状态:**
        * 组件代码生成后，AI 应更新 `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}_tasktree.md` 文件中对应已完成任务项的状态 (例如，从未开始/进行中 -> 待评审/已完成)。
        * 组件代码生成后，AI应更新 `apps/{app_name}/app/components/README.md` 文件中对应已完成任务项的简单描述，即该组件的简单描述。
    5.  **开发者验证:**
        * 开发者检查生成的代码，在本地环境中集成和测试组件，确保其在指定的本地 URL (或组件预览环境) 可见且功能符合预期。

* **完成标志:**
    * 针对**选定任务项**的组件代码已生成并保存到 `apps/{app_name}/app/components/{组件名}/`。
    * 组件的 README.md 已创建或更新。
    * 代码遵循指定的技术栈和响应式设计原则。
    * `{page_name}_tasktree.md` 中对应任务的状态已更新。
    * (理想情况下) 组件在本地开发环境中功能上可见并通过初步测试。

### 下一步操作
Phase 3 完成后，下一步将进入 **Phase 4: 视觉 QA 与迭代**。

---

### Phase 4: 全自动化组件与页面视觉 QA 及迭代

* **阶段核心:** AI 驱动的、自动化的、分层级的代码视觉 QA。首先，AI 自动遍历页面中的每个组件，进行独立的像素级视觉对齐；然后，对整个页面的组件布局进行最终的视觉对齐。
* **AI 角色:** 自动化视觉 QA 流程执行器 / 注重细节的视觉 QA 工程师 / 精准代码优化器 / 图像获取助手。
* **目标:**
    1.  **组件层面 (自动化迭代):** AI 自动识别并逐个处理页面中的所有组件，确保每个已实现组件的视觉外观（布局、尺寸、间距、颜色、字体、效果等）与其在 Figma 设计稿中对应的独立渲染图精确匹配。
    2.  **页面层面 (最终检查):** 在所有组件通过组件级 QA 后，AI 确保所有组件在页面上的整体布局、排列和相互间距与主页面 Figma 设计 PNG 精确匹配。
* **输入 (初始化阶段时提供):**
    * **主页面设计 PNG:** `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}.png` (来自 Phase 0).
    * **任务树文档:** `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}_tasktree.md` (来自 Phase 2, AI 将从此文档中获取所有组件列表及其对应的任务项文档名称/路径).
    * **组件代码根目录:** `apps/{app_name}/app/components/` (Phase 3 生成的组件存放位置).
    * **任务项文档根目录:** `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/` (Phase 2 生成的任务项文档存放位置).
    * **相关的设计规范:** `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/design-tokens.json` (来自 Phase 2).
    * **视觉QA规范:** `luban-agent/ai-rules/frontend/public-rules/visual-qa.mdc` 
    * 应用名称 (`{app_name}`), 页面名称 (`{page_name}`).
    * (可选) 开发者预先识别的、希望 AI 在自动化流程中特别关注的通用问题或特定组件的问题列表。
* **输出:**
    * 所有组件代码均经过视觉对齐优化。
    * (若有调整) 潜在的页面级布局调整代码。
    * AI 生成的每个组件及页面布局的差异点列表及对应的代码修改说明文档。
    * 更新后的 `_tasktree.md` 中各组件的 QA 状态。
* **步骤:**

    1.  **开发者启动自动化 Phase 4 并提供全局输入:**
        * 开发者指示 AI 开始对 `{app_name}` 应用的 `{page_name}` 页面执行自动化的 Phase 4 视觉 QA。
        * 开发者确认上述所有全局输入（主页面PNG、任务树文档路径、组件代码根目录、任务项文档根目录、design-tokens路径等）均准确无误。
        * (可选) 开发者提供希望 AI 特别留意的通用问题或特定组件的已知问题。

    2.  **AI 全局上下文加载与验证:**
        * AI**必须**首先加载并确认执行此阶段所需的**所有全局文件**。
        * AI**必须**向用户输出一份加载报告：
            ```
            报告：Phase 4 全局上下文已加载完毕。清单如下：
            - [√] 主页面设计 PNG: {.../{app_name}-{page_name}.png}
            - [√] 任务树文档: {..._tasktree.md}
            - [√] 组件代码根目录: apps/{app_name}/app/components/ (路径已知)
            - [√] 设计规范: {.../design-tokens.json}

            全局上下文已准备就绪。即将开始解析任务树并对各组件进行循环QA
            ```
    3.  **AI 解析任务树并准备组件QA队列:**
        * AI 读取并解析 `{page_name}_tasktree.md` 文件。
        * AI 从任务树中提取出该页面包含的所有组件列表（通常是任务项列表，每个任务项对应一个组件）及其对应的任务项文档文件名（例如 `[序号]-[组件类型]-[功能描述].md`）。
        * AI 创建一个组件 QA 处理队列。

    4.  **AI 自动化迭代执行组件级视觉 QA (对队列中的每个组件):**
        * **For each component in the QA queue:**
            * **A. 确定当前组件信息:**
                * 当前组件的任务项文档名 (e.g., `01-Header-Navigation.md`).
                * 当前组件的代码路径 (e.g., `apps/{app_name}/app/components/HeaderNavigation/HeaderNavigation.tsx` - AI 可能需要根据任务项文档名或约定规则推断组件名和代码路径).
            * **B. AI 获取当前组件的特定设计PNG:**
                * AI 读取当前组件的任务项文档 (路径为: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{当前任务项文档名}`).
                * AI 从文档中提取 Figma 设计稿链接 (指向该特定组件或其 Frame)。
                * AI 调用 "mcp服务" (或类似服务) 使用此 Figma 链接下载当前组件的精确PNG图像。
                * 保存到 `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/component-qa-previews/` 目录下，命名为 `{当前任务项文档名}-design.png` (例如 `01-Header-Navigation.md-design.png`). AI 确认图像成功下载。
            * **C. 组件级上下文加载与验证:**
                * 在执行视觉对比之前，AI**必须**加载并确认当前组件循环所需的所有文件，
                * AI在内部日志或可选的详细模式下记录：
                    ```
                    正在处理组件: {组件名}
                    - [√] 组件特定设计PNG: 已下载至...-design.png
                    - [√] 当前组件代码: 已读取 {当前组件的代码路径}
                    - [√] 任务项文档: 已读取 {当前任务项文档名}
                    组件QA上下文加载完毕，准备执行Prompt Part A。
                    ```
            * **D. AI 执行组件级视觉 QA 与优化 (使用 Prompt Part A):**
                * AI 构造 Prompt Part A 的输入，动态填充当前组件的特定信息（组件特定PNG路径、组件代码内容/路径、其任务项文档路径等）。
                * **Prompt Part A (组件级视觉QA):**
                    ```text
                    # Phase 4: 视觉QA与迭代 - Part A: 组件级完美还原

                    ## 你的角色
                    你是一位极度注重细节的前端视觉QA工程师。当前任务是确保所提供组件的代码渲染效果与其特定的设计截图达到像素级的完美匹配。请遵循 @visual-qa.mdc 中定义的检查清单和比对方法，系统性地扫描差异。

                    ## 确认此组件的输入材料 (请先确认以下文件已全部加载)
                    1.  **组件特定设计PNG (视觉基准):** `.../{当前任务项文档名}-design.png`
                    2.  **当前组件代码:** (位于: {当前组件的代码路径})
                    3.  **Design Tokens (设计规范):** `@design-tokens.json`
                    4.  **任务项文档 (上下文参考):** `@{当前任务项文档路径}`

                    ## 此组件的输入材料
                    1.  **组件特定设计PNG (主要参考图):** `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/component-qa-previews/{当前任务项文档名}-design.png` (这是我刚为组件 {当前任务项文档名} 下载的PNG图。请以此作为此组件的视觉基准。)
                    2.  **当前组件代码:** (组件 {推断的组件名} 的代码, 位于: {当前组件的代码路径})
                    3.  **Design Tokens (设计规范):** `@design-tokens.json` (路径: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/design-tokens.json`)
                    4.  **任务项文档 (上下文参考):** `@{当前任务项文档路径}` (路径: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{当前任务项文档名}`)
                    5.  **(可选) 开发者指出的此组件或通用问题:**
                        ```
                        {开发者指出的该组件或通用问题列表}
                        ```

                    ## 任务：组件级视觉完美还原
                    你的目标是修改**当前组件代码**，使其视觉输出与**组件特定设计PNG**完全一致。

                    ### 步骤 1: 优先处理开发者指出的问题 (若有)
                    如果开发者提供了适用于此组件的问题列表或通用问题，请首先在当前组件的上下文中解决这些问题。解释你的修复方案。

                    ### 步骤 2: 全面视觉差异扫描与修正 (组件代码 vs 组件PNG)
                    系统地比较组件代码的渲染效果 (你对其的理解) 与其**组件特定设计PNG**。仔细检查每一个细节：
                    * **布局与定位 (组件内部):** 尺寸、内部的 `padding/margin`、子元素的对齐方式。
                    * **排版:** 字体族、字号、字重、行高、颜色、字符间距、文本对齐 (参考Design Tokens)。
                    * **颜色与背景:** 背景色、渐变、背景图片 (参考Design Tokens)。
                    * **边框与效果:** 边框属性 (宽度、样式、颜色、圆角半径)、盒阴影、透明度 (参考Design Tokens)。
                    * **组件内的图像与图标:** 正确的资源、尺寸、宽高比、渲染质量。

                    ### 步骤 3: 代码调整与解释
                    对于发现的每一个差异点：
                    1.  **描述差异：** 哪个元素，哪个属性，它与组件特定设计PNG有何不同？
                    2.  **期望的Figma值：** PNG图中该属性的目标值/外观是什么？
                    3.  **代码更改：** 你将如何修改HTML结构、Tailwind CSS类，或者 (仅在绝对必要时作为最后手段) 自定义样式？优先使用Tailwind和Design Tokens。
                    4.  **Token校验：** 对照 `@design-tokens.json` 进行校验。如适用，优先使用Tokens。

                    提供针对当前组件的**完整的、修订后的组件代码**，使其视觉效果与其组件特定设计PNG达到100%匹配。如果你认为在当前技术栈下某个细节无法100%完美匹配，请明确指出并解释原因。
                    ```
                * AI 执行 Prompt Part A，获取修改后的组件代码。
                * AI 将修改后的代码写回原文件 (`{当前组件的代码路径}`).
            * **E. (可选配置) 开发者中间确认点 / AI 自我评估:**
                * **选项1 (人工介入):** 对于每个组件的 QA 结果，AI 暂停并请求开发者进行快速目视检查和确认。
                * **选项2 (自动化标记):** AI 对其修正结果进行自我评估（例如，基于修改的复杂性、是否完全依赖已知模式等）。如果置信度低，则将该组件标记为“组件QA待人工复核”，否则标记为“组件QA通过（AI）”。
            * **F. 更新任务树中的组件QA状态:** AI 在 `{page_name}_tasktree.md` 中标记当前组件的组件级QA状态（例如：[组件QA通过(AI)], [组件QA待人工复核]）。

    5.  **AI 执行页面级布局 QA 与优化 (在队列中所有组件完成组件级QA后):**
        * AI 确认所有组件均已完成组件级 QA (或达到可接受状态，例如所有组件均为“组件QA通过(AI)”或经人工确认为“组件QA通过”)。
        * AI 构造 Prompt Part B 的输入，其中“Current Page/Parent Component Code”应指向承载这些组件的页面级文件或最顶层的布局组件。
        * **Prompt Part B (页面级布局QA):**
            ```text
            # Phase 4: 视觉QA与迭代 - Part B: 页面级布局完整性

            ## 你的角色
            你是一位一丝不苟的前端布局QA工程师。在已确保各个独立组件视觉上完美（或已标记为此状态）之后，你的新任务是验证并修正整体页面布局，确保所有组件都按照主页面设计稿的要求，在其应在的位置正确布局、间距正确。

            ## 页面布局的输入材料
            1.  **主页面设计PNG (主要参考图):** `@{app_name}-{page_name}.png` (路径: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}.png`。这是整体页面布局的视觉基准。)
            2.  **当前页面/父组件代码:** (主页面文件或顶层布局组件的路径或内容，例如 `apps/{app_name}/app/pages/{page_name}.tsx` 或 `apps/{app_name}/app/layouts/MainLayout.tsx`)
            3.  **Design Tokens (用于间距参考):** `@design-tokens.json` (路径: `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/design-tokens.json`)
            4.  **(可选) 开发者指出的页面布局或通用问题:**
                ```
                {开发者指出的页面布局或通用问题列表}
                ```

            ## 任务：页面级布局完美还原
            你的目标是确保页面上所有组件的排列、间距和对齐方式与**主页面设计PNG**完全一致。你应该假设子组件本身的内部样式在Part A阶段已经正确。现在的重点是它们之间的相互关系以及在页面上的位置。

            ### 步骤 1: 优先处理开发者指出的布局问题 (若有)
            首先处理开发者指定的任何页面布局问题。

            ### 步骤 2: 全面页面布局差异扫描与修正
            将当前页面的整体布局（组件的排列方式）与**主页面设计PNG**进行比较。重点关注：
            * **组件定位：** 每个组件（例如页头、侧边栏、产品卡片1、产品卡片2、页脚，根据任务树或页面结构识别）是否位于页面上的正确X/Y位置？
            * **组件间距：** 相邻组件之间的 `margin/gap`（例如，页头与主内容区之间的间距，产品卡片1和产品卡片2之间的间距）是否准确？
            * **页面网格与对齐：** 整体排列是否遵循任何隐含或明确的页面网格？组件是否与页面边界或主要布局线正确对齐？
            * **响应式考量 (若PNG能体现)：** 布局是否看起来符合PNG所代表视口下的整体响应式设计意图？

            ### 步骤 3: 代码调整与解释
            对于每一个布局差异：
            1.  **描述差异：** 哪些组件位置不当或间距不正确？它与主页面设计PNG有何不同？
            2.  **期望的Figma布局：** 描述目标排列/间距。
            3.  **代码更改：** 你将如何修改页面结构、父组件样式或工具类（例如Tailwind的grid、flex容器类，包裹元素上的 `margin/padding` 工具类）来修正布局？**避免修改独立子组件的内部样式，因为它们在Part A中已被假定为正确。** 重点关注用于排列这些子组件的“胶水”代码或容器样式。
            4.  **Token校验：** 适用时，使用 `@design-tokens.json` 中的间距Tokens。

            提供对页面结构或父组件进行必要的**代码修改**，以实现与主页面设计PNG的100%页面布局准确性。
            ```
        * AI 执行 Prompt Part B，获取可能的页面级布局修改代码。
        * AI 将修改后的代码写回相应的页面/布局文件。

    6.  **开发者最终评审与确认:**
        * AI 报告整个 Phase 4 自动化流程已完成，并提供所有组件的 QA 状态、页面布局 QA 结果的摘要，特别指出任何被标记为“待人工复核”的组件。
        * 开发者全面评审所有组件的视觉效果和整个页面的布局，在浏览器中与 Figma 设计稿进行最终的人工比对。
        * 如果仍有差异，开发者可以：
            * 手动修改。
            * 或针对特定组件/页面布局问题，再次手动触发AI进行小范围的QA迭代。

* **完成标志:**
    * `_tasktree.md` 中所有组件的组件级QA状态已更新。
    * 所有组件代码及其在页面上的布局均已根据视觉QA结果进行了修订。
    * 经过开发者最终确认，整个页面在主流浏览器中的视觉渲染效果与Figma设计图达到可接受的最高精确匹配度。
    * AI已提供各阶段的差异点和修改总结。


### 下一步操作
Phase 4 完成后，下一步将进入 **Phase 5: 代码规范审查**。

---
### Phase 5: 代码规范审查

* **阶段核心:** 开发者主导，AI 可辅助的代码标准验证。
* **AI 角色:** 代码审计员 / 标准遵循检查器 (如果被提示)。
* **目标:** 验证所有生成和修改的代码是否符合项目定义的编码标准、linting 规则和最佳实践。
* **输入:**
    * 所有先前阶段生成的代码 (组件、文档等)。
    * 项目编码标准文档 (例如 `gen-view-guide.mdc`)。
    * Linting 配置。
* **输出:**
    * 通过所有规范检查的代码库。
    * (如果发现问题) 需要解决的不符合项列表。
* **步骤:**
    1.  **开发者发起审查:**
        * 开发者准备代码库以进行规范审查。
    2.  **代码分析:**
        * 开发者 (可选地在 AI 辅助下或使用自动化 linting/工具) 检查代码。
        * 对照检查:
            * `gen-view-guide.mdc`。
            * 通用的 TypeScript, React, 和 Tailwind CSS 最佳实践。
            * 项目特定的 linting 规则。
            * 命名约定、注释标准、文件结构。
    3.  **解决不符合项:**
        * 任何识别出的与标准不符之处，由开发者修正，或通过向 AI 提供具体的修正指令来修正。
* **完成标志:**
    * 所有项目代码成功通过自动化 linting 检查。
    * 人工审查确认代码遵循更广泛的编码标准和最佳实践。
    * 没有遗留的规范问题。

### 下一步操作
此阶段结束主要的开发和优化循环。后续步骤可能包括最终的集成测试、部署，或开始新的功能/页面开发周期。

---
## 3. 关键成功因素

* **高保真度 Figma 解读:** AI 准确理解 Figma 中的布局、样式和组件的能力至关重要。
* **`components-location.md` 的清晰度:** 在 Phase 1 中生成良好的布局文档能显著提高 Phase 2 中任务分解的质量。
* **模块化和可复用的组件:** 应引导 AI (并设计流程) 生成可复用的模块化组件，遵循 DRY 原则。
* **有效的人机视觉反馈循环:** Phase 4 中的迭代视觉 QA 过程对于实现设计准确性至关重要。开发者提供清晰反馈的能力是关键。
* **遵循响应式设计:** 始终应用响应式设计原则并避免使用固定单位 (如 'px') (按指示) 以确保跨设备适应性。
* **定义良好的 UI 编码规则 (`gen-view-guide.mdc`) 和设计规范 (Design Tokens):** 清晰的代码和设计一致性标准简化了 AI 生成和开发者审查。
* **准确的 Prompt 工程:** AI 输出的质量与开发者在每个阶段提供的 Prompt 的清晰度、特异性和完整性直接相关。

---
## 4. AI 行为约束 (通用指南)

* **遵循工作流结构:** AI 应在定义的阶段内操作，并利用每个阶段指定的输入/输出。
* **遵守技术栈:** 严格按照规定使用 TypeScript, React, 和 Tailwind CSS 进行组件实现。
* **优先考虑复用性:** 当被指示时 (如 Phase 3 中)，AI 必须在生成新组件前检查并使用现有组件。
* **响应式代码生成:** 实现组件时要考虑响应式设计，优先使用 Tailwind 的功能类和响应式前缀或相对单位，而不是固定像素值，以实现移动优先的动态适应。
* **准确应用设计规范:** 正确解读并应用来自 `design-tokens.json` 文件的样式。
* **文件和命名约定:** 严格遵循 Prompt 和输出中指定的文件结构和命名约定。
* **Prompt 依从性:** 尽可能准确地解读和执行每个 Prompt 中的指令，包括所有约束和输出格式要求。
* **迭代优化:** 预期并支持迭代优化，尤其是在视觉 QA 期间。
* **上下文感知:** 利用 Prompt 中提供的所有文档 (例如任务树、状态矩阵) 来指导代码生成。


---

## 5. AI 交互与确认机制

为确保开发流程的顺利进行以及开发者对进度的掌控，AI 与用户（开发者）的交互将遵循以下确认机制：

* **阶段完成报告与下一步建议：** 当 AI 完成某个阶段或该阶段内定义的关键步骤，并且所有相关的“完成标志”均已达成时，AI 将主动向用户报告已完成的工作内容。同时，AI 将参照文档中当前阶段定义的“下一步操作”或整个工作流的下一阶段，向用户提示建议执行的后续阶段或具体步骤。

* **用户确认授权：** AI 在进入下一个建议的阶段或执行新的关键步骤之前，必须等待用户的明确指令。用户需要通过清晰的回复来授权 AI 继续操作，例如：
    * “请继续 Phase X”（例如：“请继续 Phase 2: 任务拆解与文档生成”）
    * “好的，开始执行[具体步骤名称]”
    * 或针对 AI 提问的具体肯定性答复。

* **指令的明确性：** 用户在授权时，应尽量确保指令的明确性，以便 AI 能够准确理解并执行。如果 AI 对用户的指令有疑问，会请求用户进一步澄清。

* **开发者主导权：** 此机制旨在强化开发者在人机协作流程中的主导地位。开发者可以根据实际情况调整任务优先级，或在 AI 提示进行下一步之前，要求 AI 对当前阶段的产出进行修改或补充。

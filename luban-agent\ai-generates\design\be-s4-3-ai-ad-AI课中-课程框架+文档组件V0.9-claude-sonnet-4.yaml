openapi: 3.0.3
info:
  title: AI课中学习服务 API
  description: |
    提供AI课中学习系统的核心功能，包括课程内容管理、学习进度跟踪、文档组件交互播放、掌握度计算等功能。
    主要解决学生在AI课程学习过程中的内容获取、进度管理、交互控制和学习效果评估问题。
    本文档遵循 OpenAPI 3.0.3 规范。
  version: v1.0.0
  contact:
    name: AI课中开发团队
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api/v1
    description: 本地开发服务器
  - url: https://api-study.example.com/api/v1
    description: 生产环境服务器

tags:
  - name: CourseContent
    description: 课程内容相关接口
  - name: StudySession
    description: 学习会话管理相关接口
  - name: StudyInteraction
    description: 学习交互操作相关接口
  - name: StudyReport
    description: 学习报告和反馈相关接口

components:
  schemas:
    # 通用响应结构
    GenericError:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码，0表示成功，非0表示错误
          example: 4000001
        message:
          type: string
          description: 用户可读的错误信息
          example: '无效的参数'
        detail:
          type: string
          description: 可选的详细错误信息或调试信息
          nullable: true
          example: '字段 [courseId] 不能为空'
        data:
          type: object
          description: 可选的附加数据
          nullable: true
        pageInfo:
          type: object
          description: 可选的分页信息
          nullable: true

    GenericSuccess:
      type: object
      properties:
        code:
          type: integer
          description: 业务状态码，0表示成功
          example: 0
        message:
          type: string
          description: 成功信息
          example: 'Success'
        data:
          type: object
          description: 返回的业务数据
        pageInfo:
          type: object
          description: 可选的分页信息
          nullable: true

    # 课程相关实体
    OpeningPageConfig:
      type: object
      description: 课程开场页配置
      properties:
        courseId:
          type: integer
          format: int64
          description: 课程ID
          example: 1001
        courseName:
          type: string
          description: 课程名称
          example: 'AI基础课程'
        courseInfo:
          type: string
          description: 课程信息
          example: '本课程介绍人工智能的基础概念'
        ipAnimationConfig:
          type: object
          description: IP动效配置
          properties:
            animationUrl:
              type: string
              description: 动效资源URL
              example: 'https://cdn.example.com/animations/course-intro.mp4'
            duration:
              type: integer
              description: 动效时长（秒）
              example: 30
        audioConfig:
          type: object
          description: 音频配置
          properties:
            audioUrl:
              type: string
              description: 音频资源URL
              example: 'https://cdn.example.com/audio/course-intro.mp3'
            duration:
              type: integer
              description: 音频时长（秒）
              example: 30
        autoPlay:
          type: boolean
          description: 是否自动播放
          example: true
      required:
        - courseId
        - courseName

    CourseComponent:
      type: object
      description: 课程组件详细内容
      properties:
        componentId:
          type: integer
          format: int64
          description: 组件唯一ID
          example: 2001
        courseId:
          type: integer
          format: int64
          description: 所属课程ID
          example: 1001
        componentType:
          type: string
          description: 组件类型
          enum: [document, exercise]
          example: 'document'
        componentName:
          type: string
          description: 组件名称
          example: '第一章：人工智能概述'
        componentOrder:
          type: integer
          description: 组件在课程中的顺序
          example: 1
        documentContent:
          type: object
          description: 文档具体内容
          properties:
            title:
              type: string
              example: '人工智能概述'
            content:
              type: string
              example: '人工智能是计算机科学的一个分支...'
            pages:
              type: array
              items:
                type: object
        drawingTrajectoryData:
          type: object
          description: 与音频同步的勾画轨迹数据
          properties:
            trajectories:
              type: array
              items:
                type: object
                properties:
                  timestamp:
                    type: integer
                    description: 时间戳（毫秒）
                  coordinates:
                    type: array
                    items:
                      type: object
                      properties:
                        x:
                          type: number
                        y:
                          type: number
        subtitleData:
          type: object
          description: 字幕数据
          nullable: true
          properties:
            subtitles:
              type: array
              items:
                type: object
                properties:
                  startTime:
                    type: integer
                  endTime:
                    type: integer
                  text:
                    type: string
        totalDuration:
          type: integer
          description: 组件总播放时长（秒）
          example: 600
        playbackConfig:
          type: object
          description: 播放配置信息
          nullable: true
      required:
        - componentId
        - courseId
        - componentType
        - componentName
        - documentContent
        - drawingTrajectoryData
        - totalDuration

    # 学习会话相关实体
    StudySession:
      type: object
      description: 学习会话信息
      properties:
        sessionId:
          type: integer
          format: int64
          description: 会话唯一ID
          readOnly: true
          example: 3001
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1001
        courseId:
          type: integer
          format: int64
          description: 课程ID
          example: 1001
        sessionStatus:
          type: string
          description: 会话状态
          enum: [in_progress, completed, exited]
          example: 'in_progress'
        currentComponentPosition:
          type: integer
          description: 当前学习到的组件位置
          example: 1
        startTime:
          type: integer
          format: int64
          description: 学习开始时间 (Unix时间戳，秒)
          example: 1716789600
        endTime:
          type: integer
          format: int64
          description: 学习结束时间 (Unix时间戳，秒)
          nullable: true
          example: 1716793200
        totalDuration:
          type: integer
          description: 学习总用时（秒）
          example: 3600
        learningMode:
          type: string
          description: 学习模式
          enum: [follow, free]
          example: 'follow'
      required:
        - sessionId
        - userId
        - courseId
        - sessionStatus
        - startTime

    StartSessionRequest:
      type: object
      description: 开始学习会话请求
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1001
        courseId:
          type: integer
          format: int64
          description: 课程ID
          example: 1001
        learningMode:
          type: string
          description: 初始学习模式
          enum: [follow, free]
          default: 'follow'
          example: 'follow'
      required:
        - userId
        - courseId

    EndSessionRequest:
      type: object
      description: 结束学习会话请求
      properties:
        sessionId:
          type: integer
          format: int64
          description: 会话ID
          example: 3001
      required:
        - sessionId

    # 学习交互相关实体
    InteractionRequest:
      type: object
      description: 学习交互操作请求
      properties:
        sessionId:
          type: integer
          format: int64
          description: 会话ID
          example: 3001
        componentId:
          type: integer
          format: int64
          description: 组件ID
          example: 2001
        operation:
          type: string
          description: 操作类型
          enum: [mode_switch, play_pause, speed_change, position_update, long_press_start, long_press_end]
          example: 'mode_switch'
        playbackMode:
          type: string
          description: 播放模式
          enum: [follow, free]
          nullable: true
          example: 'free'
        playbackSpeed:
          type: number
          description: 播放倍速
          nullable: true
          enum: [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 3.0]
          example: 1.5
        currentPosition:
          type: integer
          description: 当前播放位置（秒）
          nullable: true
          example: 120
        interactionData:
          type: object
          description: 交互详细数据
          nullable: true
          properties:
            clickType:
              type: string
              enum: [single, double, long_press]
            targetArea:
              type: string
              enum: [document, control_bar, screen]
            coordinates:
              type: object
              properties:
                x:
                  type: number
                y:
                  type: number
      required:
        - sessionId
        - componentId
        - operation

    InteractionResult:
      type: object
      description: 学习交互操作结果
      properties:
        success:
          type: boolean
          description: 操作是否成功
          example: true
        currentState:
          type: object
          description: 当前播放状态
          properties:
            playbackMode:
              type: string
              enum: [follow, free]
              example: 'free'
            playbackSpeed:
              type: number
              example: 1.5
            currentPosition:
              type: integer
              example: 120
            isPlaying:
              type: boolean
              example: true
        message:
          type: string
          description: 操作结果信息
          example: '模式切换成功'
      required:
        - success
        - currentState

    # 学习报告相关实体
    StudyReport:
      type: object
      description: 完整学习报告
      properties:
        sessionId:
          type: integer
          format: int64
          description: 会话ID
          example: 3001
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1001
        courseId:
          type: integer
          format: int64
          description: 课程ID
          example: 1001
        studyPerformance:
          type: object
          description: 学习表现数据
          properties:
            totalQuestions:
              type: integer
              description: 总答题数量
              example: 20
            correctAnswers:
              type: integer
              description: 正确答题数量
              example: 16
            accuracyRate:
              type: number
              description: 正确率（百分比）
              example: 80.0
            studyDuration:
              type: integer
              description: 学习用时（秒）
              example: 3600
            completionRate:
              type: number
              description: 完成率（百分比）
              example: 100.0
        masteryRecords:
          type: array
          description: 掌握度记录
          items:
            type: object
            properties:
              knowledgePointId:
                type: integer
                format: int64
                description: 知识点ID
                example: 5001
              knowledgePointName:
                type: string
                description: 知识点名称
                example: '人工智能基础概念'
              currentMastery:
                type: number
                description: 当前掌握度值 (0.00-1.00)
                example: 0.85
              targetMastery:
                type: number
                description: 目标掌握度值 (0.00-1.00)
                example: 0.80
              masteryProgress:
                type: number
                description: 掌握度完成进度 (0.00-1.00)
                example: 1.0
              energyGained:
                type: integer
                description: 获得的能量值
                example: 50
        celebrationConfig:
          type: object
          description: 庆祝动效配置
          properties:
            animationType:
              type: string
              description: 动效类型
              enum: [happy, encourage]
              example: 'happy'
            animationUrl:
              type: string
              description: 动效资源URL
              example: 'https://cdn.example.com/animations/celebration.mp4'
            encouragementText:
              type: string
              description: 鼓励文案
              example: '恭喜你完成了本次学习！'
      required:
        - sessionId
        - userId
        - courseId
        - studyPerformance
        - masteryRecords

    # 反馈相关实体
    FeedbackRequest:
      type: object
      description: 课程反馈提交请求
      properties:
        sessionId:
          type: integer
          format: int64
          description: 会话ID
          example: 3001
        ratingLevel:
          type: integer
          description: 评价等级 (1-5级评价)
          minimum: 1
          maximum: 5
          example: 4
        feedbackOptions:
          type: array
          description: 详细反馈选项
          nullable: true
          items:
            type: string
            example: '内容丰富'
        textFeedback:
          type: string
          description: 用户输入的文字反馈
          nullable: true
          maxLength: 500
          example: '课程内容很好，希望能增加更多实践案例'
      required:
        - sessionId
        - ratingLevel

    FeedbackResult:
      type: object
      description: 反馈提交结果
      properties:
        submitted:
          type: boolean
          description: 是否提交成功
          example: true
        feedbackId:
          type: integer
          format: int64
          description: 反馈记录ID
          example: 7001
        message:
          type: string
          description: 提交结果信息
          example: '反馈提交成功，感谢您的宝贵意见'
      required:
        - submitted
        - feedbackId

  parameters:
    # 课程相关参数
    courseIdQueryParameter:
      name: courseId
      in: query
      required: true
      description: 课程ID
      schema:
        type: integer
        format: int64
        example: 1001

    componentIdQueryParameter:
      name: componentId
      in: query
      required: true
      description: 组件ID
      schema:
        type: integer
        format: int64
        example: 2001

    # 会话相关参数
    sessionIdQueryParameter:
      name: sessionId
      in: query
      required: true
      description: 会话ID
      schema:
        type: integer
        format: int64
        example: 3001

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "请输入JWT Token, 格式为: Bearer {token}"

  responses:
    UnauthorizedError:
      description: 未认证或认证令牌无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4010001
                message: "需要认证或认证失败"
                detail: "请提供有效的认证令牌"

    ForbiddenError:
      description: 无权限访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4030001
                message: "无权限执行此操作"
                detail: "用户权限不足"

    NotFoundError:
      description: 请求的资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4040001
                message: "请求的资源不存在"
                detail: "指定的资源ID无效或已被删除"

    BadRequestError:
      description: 请求参数无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4000001
                message: "请求参数无效"
                detail: "参数格式错误或缺少必需参数"

    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 5000001
                message: "服务器内部错误"
                detail: "请稍后重试或联系技术支持"

paths:
  # 课程内容相关接口
  /course/opening:
    get:
      tags:
        - CourseContent
      summary: 获取课程开场页配置
      description: 获取指定课程的开场页配置信息，包括IP动效、音频配置等，用于课程学习开始前的展示。
      operationId: getCourseOpening
      parameters:
        - $ref: '#/components/parameters/courseIdQueryParameter'
      responses:
        '200':
          description: 成功获取课程开场页配置
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericSuccess'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/OpeningPageConfig'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  /course/component:
    get:
      tags:
        - CourseContent
      summary: 获取组件详细内容
      description: 获取指定文档组件的详细内容，包括文档内容、勾画轨迹数据、字幕数据等，用于文档组件的播放展示。
      operationId: getCourseComponent
      parameters:
        - $ref: '#/components/parameters/componentIdQueryParameter'
      responses:
        '200':
          description: 成功获取组件详细内容
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericSuccess'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/CourseComponent'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  # 学习会话管理相关接口
  /study/session/start:
    post:
      tags:
        - StudySession
      summary: 开始学习会话
      description: 创建新的学习会话记录，初始化学习状态，为用户开始课程学习做准备。
      operationId: startStudySession
      requestBody:
        description: 开始学习会话的请求信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StartSessionRequest'
      responses:
        '200':
          description: 学习会话创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericSuccess'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/StudySession'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  /study/session/end:
    post:
      tags:
        - StudySession
      summary: 结束学习会话
      description: 结束指定的学习会话，触发掌握度计算，更新学习状态为已完成。
      operationId: endStudySession
      requestBody:
        description: 结束学习会话的请求信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EndSessionRequest'
      responses:
        '200':
          description: 学习会话结束成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericSuccess'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          sessionId:
                            type: integer
                            format: int64
                            example: 3001
                          completed:
                            type: boolean
                            example: true
                          endTime:
                            type: integer
                            format: int64
                            example: 1716793200
                          masteryCalculated:
                            type: boolean
                            description: 掌握度是否已计算完成
                            example: true
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  # 学习交互操作相关接口
  /study/interaction:
    post:
      tags:
        - StudyInteraction
      summary: 学习交互操作
      description: 处理学习过程中的各种交互操作，包括模式切换、播放控制、进度更新、倍速调整等。
      operationId: handleStudyInteraction
      requestBody:
        description: 学习交互操作的请求信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InteractionRequest'
      responses:
        '200':
          description: 交互操作处理成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericSuccess'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/InteractionResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  # 学习报告和反馈相关接口
  /study/report:
    get:
      tags:
        - StudyReport
      summary: 获取学习报告
      description: 获取指定学习会话的完整学习报告，包括学习表现、掌握度记录、答题详情等信息。
      operationId: getStudyReport
      parameters:
        - $ref: '#/components/parameters/sessionIdQueryParameter'
      responses:
        '200':
          description: 成功获取学习报告
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericSuccess'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/StudyReport'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  /study/feedback/submit:
    post:
      tags:
        - StudyReport
      summary: 提交课程反馈
      description: 提交用户对课程的评价和反馈信息，用于课程质量改进和用户体验优化。
      operationId: submitCourseFeedback
      requestBody:
        description: 课程反馈提交的请求信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FeedbackRequest'
      responses:
        '200':
          description: 反馈提交成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericSuccess'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/FeedbackResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []
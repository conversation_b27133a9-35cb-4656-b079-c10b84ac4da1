---
description: 
globs: backend/tests/**/test_*.py
alwaysApply: false
---
# 单元测试通用规则

**核心原则：简洁优先**

## 组织与命名
- 类 `Test{Feature}`；方法 `test_{功能}_{场景}`  
- 辅助函数动词开头 `assert_{条件}`
- 确保符合AAA模式 `Arrange-Act-Assert`

## 约定
- 为测试类和方法添加 Docstring 描述功能场景
- 使用 `pytest`；断言直接 `assert`，并提供清晰失败信息
- 复杂初始化使用 `@pytest.fixture` 封装
- 使用异常测试 `with pytest.raises(ExpectedError)`  捕获预期错误
- 使用参数化测试 `@pytest.mark.parametrize` ，使用 `ids` 参数增强可读性
- Mock 仅用于外部依赖，避免模拟业务逻辑
- 当方法超过30行或断言>5条时应拆分或提取辅助方法

## 覆盖率目标
- 核心业务行覆盖率 ≥ 90 %  
- 工具/辅助模块 ≥ 70 %

## 示例代码
```python
"""
{Feature}单元测试

测试{Feature}的行为和方法，确保符合业务需求。
"""

class TestUser:
    """User实体测试类"""
    
    @pytest.fixture
    def username(self):
        """创建用户名值对象"""
        return Username("testuser")
    
    @pytest.fixture
    def password(self):
        """创建密码值对象"""
        return Password(value="Password123")
    
    @pytest.fixture
    def test_user(self, username, password):
        """提供基本测试用户"""
        return User(
            user_id=1,
            # 其它属性初始化赋值...
        )
    
    @pytest.fixture
    def custom_times(self):
        """提供自定义时间"""
        return {
            "created_at": timezone.now() - timedelta(days=5),
            "last_login_at": timezone.now() - timedelta(days=2)
        }
    
    @pytest.fixture
    def custom_user(self, custom_times):
        """提供带有自定义属性的用户"""
        return User(
            user_id=100,
             # 其它属性初始化赋值...
            created_at=custom_times["created_at"]
        )
    
    # 用户创建测试
    def test_user_creation_with_password_object(self, username, password):
        """测试使用密码对象创建用户"""
        # 动作
        user = User(
            user_id=1,
            username=username,
            password=password
        )
        
        # 断言
        assert user.user_id == 1, "用户ID应正确设置"
        assert user.username == username, "用户名应正确设置"
        # 其它更多实体属性的断言 ...
    
    # 密码验证测试
    @pytest.mark.parametrize("input_password, expected_result", [
        ("Password123", True),  # 正确密码
        ("WrongPassword", False),  # 错误密码
        ("", False),  # 空密码
    ], ids=["correct_password", "wrong_password", "empty_password"])
    def test_verify_password(self, test_user, input_password, expected_result):
        """测试密码验证功能"""
        # 动作
        result = test_user.verify_password(input_password)
        
        # 断言
        assert result == expected_result, f"密码验证应返回{expected_result}"
    

    # 状态管理测试
    def test_change_status(self, test_user):
        """测试修改账号状态"""
        # 前置断言
        assert test_user.status == UserStatus.ACTIVE, "初始状态应为活跃"
        
        # 动作
        test_user.change_status(UserStatus.LOCKED)
        
        # 断言
        assert test_user.status == UserStatus.LOCKED, "状态应变更为锁定"

    
    # 最后登录时间测试
    def test_update_last_login(self, test_user, monkeypatch):
        """测试更新最后登录时间"""
        # 安排 - 模拟当前时间
        mock_now = datetime(2023, 1, 1, 12, 0, 0)
        monkeypatch.setattr(timezone, "now", lambda: mock_now)
        
        # 前置断言
        assert test_user.last_login_at is None, "初始最后登录时间应为None"
        
        # 动作
        test_user.update_last_login()
        
        # 断言
        assert test_user.last_login_at == mock_now, "最后登录时间应更新为当前时间"
```
# 技术架构设计文档：教师端作业报告 V1.0

**版本历史**

| 版本号 | 日期       | 作者     | 变更内容         |
| ------ | ---------- | -------- | ---------------- |
| 1.0    | {当前日期} | Gemini AI | 初稿             |

## 1. 引言

### 1.1. 项目背景

本技术架构设计文档旨在为"教师端作业报告 V1.0"项目提供清晰、健壮且可扩展的后端服务架构方案。需求源于教师在教学过程中，需要高效、全面地了解学生作业完成情况、答题表现、学习进度等，以便及时进行教学调整和学生辅导。本期核心目标是实现教师端作业报告的核心功能MVP，打通业务流程，提升教师对学生学习数据的掌控能力，并辅助教师识别需关注和鼓励的学生。

### 1.2. 设计目标

本架构设计致力于达成以下目标：

*   **快速迭代与MVP实现**：优先支持P0核心功能的快速开发与上线，验证业务闭环。
*   **可维护性与可扩展性**：采用清晰的分层和模块化设计，方便后续功能迭代和系统扩展。
*   **数据准确性与一致性**：确保报告数据的计算和展示准确无误，与原始数据源保持一致。
*   **性能与响应**：保证教师在查询和浏览报告时，系统能提供流畅的用户体验。
*   **技术栈统一**：遵循团队统一的技术栈规范，降低开发和维护成本。
*   **简洁高效**：秉承简洁即高效的原则，避免不必要的复杂性和过度设计。

### 1.3. 范围

本文档定义的架构范围主要包括"教师端作业报告"后端服务的逻辑架构、关键业务流程、数据模型、接口设计原则、技术选型以及非功能性需求等方面。

**包含:**

*   作业列表展示服务的架构设计。
*   作业报告详情（含学生报告、答题结果、学生提问Tab）服务的架构设计。
*   单学生作业报告服务的架构设计。
*   相关计算规则（如正确率、异常数据识别、建议学生等）的后端处理逻辑。

**不包含:**

*   前端UI/UX的具体实现细节。
*   底层基础设施（如数据库、消息队列、缓存服务）的部署和运维细节。
*   非本期功能（P1）如自定义提醒设置的具体实现。
*   具体的算法细节（如"鼓励"、"关注"学生的精确匹配算法，将依赖于外部配置或策略服务）。

### 1.4. 读者对象

*   项目开发工程师
*   测试工程师
*   产品经理
*   项目经理
*   运维工程师
*   其他相关技术人员

### 1.5. 关键术语与缩略语

| 术语/缩略语 | 完整名称/解释                                  |
| ----------- | ---------------------------------------------- |
| PRD         | Product Requirement Document (产品需求文档)    |
| TD          | Technical Design Document (技术设计文档)       |
| MVP         | Minimum Viable Product (最小可行性产品)        |
| API         | Application Programming Interface (应用程序编程接口) |
| P0          | 优先级最高的任务                               |
| P1          | 优先级次高的任务                               |
| UID         | User Identifier (用户唯一标识)                 |
| 小空        | 题目中的填空、选择等计分点                     |
| 首答        | 学生在任务中多次遇到同题，仅计算首次作答数据     |

### 1.6. 参考资料

*   `documents/ai-docs/ai-prd-教师端_1期_作业报告_analyzed-Gemini-2.5-Pro.md`
*   `documents/prompt/s2-gen-ai-td.md`
*   `documents/templates/ai-td-v2.4.md`
*   `documents/service/question.md` (内容平台服务描述)
*   `documents/service/admin.md` (运营管理平台服务描述)
*   `documents/service/ucenter.md` (用户中心服务描述)
*   `backend/global_rules/rules-tech-stack.mdc` (技术栈约束)

## 2. 需求概述

### 2.1. 功能性需求回顾

根据PRD (`ai-prd-教师端_1期_作业报告_analyzed-Gemini-2.5-Pro.md`)，核心功能需求包括：

1.  **作业列表展示**：
    *   展示教师负责或有权限的作业任务列表。
    *   核心信息：类型、名称、班级、学科、日期、完成率、正确率等。
    *   支持筛选：类型、班级、学科、日期。
    *   支持搜索：关键词模糊匹配任务名称。
    *   操作：编辑、删除、复制（本人布置的任务）。
2.  **作业报告详情**：
    *   点击作业任务卡片进入。
    *   包含三个Tab：学生报告、答题结果、学生提问。
    *   顶部展示任务详情（发布/完成时间、班级、素材范围）和筛选功能。
3.  **学生报告 Tab**：
    *   整体班级统计：发布/完成时间、班级、素材范围、平均进度、平均正确率。
    *   建议"鼓励"和"关注"的学生列表（高亮）。
    *   完整学生列表（UID排序），高亮建议学生，展示个体进度、正确率，异常数据视觉提示。
    *   点击学生进入单学生作业报告。
4.  **答题结果 Tab**：
    *   适用于有答题环节的资源任务。
    *   题目总数和共性错题数。
    *   题目关键词搜索（题干、选项、解析）。
    *   题目列表：题干、选项（展开/收起）、答案解析（展开/收起）、正确率、答错人数、作答人数。
    *   侧边题目面板：快速导航，颜色区分题目正确率。
    *   点击题目查看题目详情（题目详情可能由内容平台提供）。
5.  **学生提问 Tab**：
    *   汇总展示学生针对该次作业提出的问题列表。
    *   支持查看提问详情。
6.  **单学生作业报告**：
    *   展示特定学生在该次作业中的详细答题结果和提问情况。
7.  **策略说明应用**：
    *   后端应用"鼓励"、"关注"学生规则和"共性错题"识别规则。

### 2.2. 非功能性需求

| 类别     | 需求描述                                                                 | 优先级 | 备注                                                                         |
| -------- | ------------------------------------------------------------------------ | ------ | ---------------------------------------------------------------------------- |
| **性能** | 作业列表加载时间应在2秒内（50个任务）。                                    | 高     | 需考虑数据量和查询优化。                                                     |
|          | 作业报告详情页（各Tab）数据加载时间应在3秒内（标准班级50学生，20道题目）。 | 高     | 复杂计算可能需要异步处理或缓存。                                             |
| **可扩展性** | 架构应支持未来P1功能的平滑引入，如自定义提醒。                           | 中     | 模块化设计，接口预留。                                                       |
|          | 支持未来可能增加新的报告维度或分析指标。                                   | 中     |                                                                              |
| **可维护性** | 代码结构清晰，易于理解和修改。遵循统一编码规范。                           | 高     |                                                                              |
| **安全性** | 教师只能访问其权限范围内的作业和学生数据。                                 | 高     | 依赖用户中心进行认证，并进行严格的权限校验。                                 |
|          | 防止未授权的数据访问和操作。                                               | 高     |                                                                              |
| **可靠性** | 服务应具备高可用性，核心功能故障率低。                                     | 高     |                                                                              |
| **数据准确性**| 所有统计数据（如正确率、完成率）必须与原始数据一致，计算逻辑清晰可验证。 | 高     |                                                                              |

## 3. 系统架构设计

### 3.1. 总体架构视图 (High-Level Architecture)

本期"教师端作业报告"功能将作为一个独立的服务（或一组紧密协作的模块）存在，它将依赖于外部的公共服务（如用户中心、内容平台、运营管理平台）以及可能的内部基础服务（如作业管理、学生作答数据服务等——这些目前假设为已存在或由其他团队提供）。

```mermaid
graph TD
    subgraph '外部依赖服务'
        UC['用户中心 （UCenter Service）
提供用户认证、用户信息']
        ADMIN['运营管理平台 （Admin Service）
提供班级、学生、教师信息']
        QUES['内容平台 （Question Service）
提供题目、试卷、知识点信息']
        HW_MGT['作业管理服务 （Homework Management Service）
（假设存在）
提供作业任务基础信息、布置信息']
        ANSWER_DATA['学生作答数据服务 （Answer Data Service）
（假设存在）
提供学生原始答题记录']
    end

    subgraph '教师端作业报告服务 AssignmentReport Service'
        direction LR
        API_GW['API网关 （API Gateway）']
        REPORT_SVC['报告核心服务 （Report Core Service）']
        CALC_ENGINE['计算引擎 （Calculation Engine）']
        DATA_ACCESS['数据访问层 （Data Access Layer）']
    end

    USER_FE['教师端前端 （Teacher Frontend）'] -- HTTP/gRPC --> API_GW
    API_GW -- 路由 --> REPORT_SVC
    REPORT_SVC -- 调用 --> CALC_ENGINE
    REPORT_SVC -- 数据请求 --> DATA_ACCESS
    CALC_ENGINE -- 数据请求 --> DATA_ACCESS
    DATA_ACCESS -- DB/RPC --> HW_MGT
    DATA_ACCESS -- DB/RPC --> ANSWER_DATA
    DATA_ACCESS -- RPC --> QUES
    DATA_ACCESS -- RPC --> ADMIN
    DATA_ACCESS -- RPC --> UC

    %% 核心交互流程说明
    %% 1. 教师通过前端发起报告查看请求。
    %% 2. API网关接收请求，进行初步校验和路由。
    %% 3. 报告核心服务处理业务逻辑，编排数据获取和计算。
    %% 4. 计算引擎负责复杂的统计和策略应用（如鼓励/关注学生）。
    %% 5. 数据访问层统一管理对内外部数据源的访问。
```

**核心思路:**

*   **独立服务/模块**: 将作业报告功能封装成一个高内聚的服务，便于独立开发、测试、部署和扩展。
*   **依赖清晰**: 明确服务对外部系统的依赖关系，通过接口进行交互。
*   **分层设计**: 服务内部采用逻辑分层，实现关注点分离。

### 3.2. 逻辑分层架构

#### 3.2.1. 推荐的逻辑分层模型

为了满足当前教师端作业报告的需求，并兼顾未来的可扩展性和可维护性，我们设计如下逻辑分层模型。此模型旨在清晰地分离不同层面的职责，确保每一层都专注于其核心任务，并与相邻层通过明确定义的接口进行交互。

```mermaid
graph TD
    subgraph '教师端作业报告服务内部逻辑分层'
        A[接口呈现层 （Interface/Presentation Layer）]
        B[应用服务层 （Application Service Layer）]
        C[领域逻辑层 （Domain Logic Layer）]
        D[基础设施层 （Infrastructure Layer）]
    end

    A --> B
    B --> C
    B --> D
    %% 应用服务层可能直接调用基础设施层进行数据获取或与外部服务交互
    C --> D
    %% 领域逻辑层依赖基础设施层进行数据持久化和外部数据获取

    %% 层级职责说明
    %% A (接口呈现层): 负责处理外部请求（如HTTP/gRPC），进行协议转换、参数校验、认证鉴权（部分可委托给API网关或通用库），并将应用层的响应格式化后返回给调用方。
    %% B (应用服务层): 负责编排和协调领域对象和服务来完成特定的业务用例（用户场景）。它不包含核心业务规则，而是委托给领域层。处理事务边界、日志记录、事件发布等。
    %% C (领域逻辑层): 包含核心业务逻辑、业务规则、计算逻辑（如正确率计算、共性错题识别、鼓励/关注学生策略）。封装核心的业务实体和值对象。这是系统的核心，应尽可能独立于外部技术。
    %% D (基础设施层): 提供通用的技术能力，如数据库访问、消息队列交互、缓存操作、第三方服务客户端（如调用内容平台、用户中心等）。为其他层提供支持，隔离业务逻辑与具体的技术实现。
```

**各层职责详述:**

1.  **接口呈现层 (Interface/Presentation Layer)**:
    *   **职责**:
        *   接收来自客户端（教师端前端）的请求（例如 HTTP RESTful API 或 gRPC 调用）。
        *   对请求进行初步的格式校验和安全校验（如输入参数验证）。
        *   调用应用服务层处理业务请求。
        *   将应用服务层的处理结果（数据或错误信息）格式化并返回给客户端。
        *   处理用户认证和授权（可与用户中心服务集成，或通过网关统一处理）。
    *   **封装的变化**: 对外接口协议的变化、请求/响应格式的变化。
    *   **交互边界**: 与客户端直接交互，向下调用应用服务层。

2.  **应用服务层 (Application Service Layer)**:
    *   **职责**:
        *   编排一个或多个领域服务/对象来完成一个完整的用户场景/业务用例（例如，"获取作业列表"、"生成学生报告"）。
        *   管理事务边界。
        *   处理应用级别的日志、监控。
        *   作为领域逻辑层与接口呈现层、基础设施层之间的协调者。
        *   不包含具体的业务规则实现，这些应委托给领域逻辑层。
    *   **封装的变化**: 业务流程的编排逻辑变化、跨多个领域对象的协调逻辑变化。
    *   **交互边界**: 向上为接口呈现层提供服务，向下调用领域逻辑层的方法，并可能直接使用基础设施层（如数据访问、外部服务调用）。

3.  **领域逻辑层 (Domain Logic Layer)**:
    *   **职责**:
        *   实现核心的业务规则、业务逻辑和计算逻辑。这是系统的"心脏"。
        *   定义和管理核心的业务实体（如 `作业报告HomeworkReport`, `学生个体报告StudentReport`, `题目统计ItemStat`）和值对象。
        *   实现具体的计算公式，如：题目正确率、班级平均正确率、异常数据飘色规则、共性错题识别规则、建议鼓励/关注学生规则。
        *   确保业务数据的一致性和有效性。
    *   **封装的变化**: 核心业务规则的变化、计算逻辑的变化、业务实体状态和行为的变化。
    *   **交互边界**: 向上为应用服务层提供业务能力，向下通过基础设施层定义的接口进行数据持久化或获取外部数据。这一层应尽可能独立于具体的技术实现。

4.  **基础设施层 (Infrastructure Layer)**:
    *   **职责**:
        *   提供通用的技术能力支撑上层业务。
        *   实现数据持久化逻辑（数据库访问，如CRUD操作）。
        *   封装对外部服务（如用户中心、内容平台、运营管理平台、作业管理服务、学生作答数据服务）的客户端调用。
        *   提供消息队列、缓存、文件存储等基础设施的访问接口和实现。
        *   配置管理、日志组件等。
    *   **封装的变化**: 具体数据存储技术的变化（如从MySQL切换到PostgreSQL）、外部服务接口的变化、消息队列或缓存中间件的变化。
    *   **交互边界**: 为应用服务层和领域逻辑层提供数据访问和外部服务调用能力。

**设计合理性论证:**

该分层模型选择了接口呈现层、应用服务层、领域逻辑层和基础设施层这四层结构。

*   **关注点分离 (SoC) 和单一职责原则 (SRP)**:
    *   接口层专注于API交互和协议转换。
    *   应用层专注于业务流程编排和事务管理。
    *   领域层专注于核心业务规则和计算逻辑的实现。
    *   基础设施层专注于数据持久化和外部服务集成。
    这样的划分使得每一层的职责清晰，降低了系统的复杂度和耦合度。

*   **高内聚低耦合**:
    *   各层内部的组件功能紧密相关（高内聚）。
    *   层与层之间通过明确定义的接口进行交互，依赖关系清晰（通常是单向依赖，上层依赖下层），减少了层间耦合。这使得修改一层时对其他层的影响最小化。

*   **可测试性**:
    *   领域逻辑层因其独立性，可以方便地进行单元测试，无需依赖外部API或数据库。
    *   应用服务层也可以通过模拟（Mock）领域服务和基础设施接口来进行测试。

*   **可维护性与可扩展性**:
    *   当业务规则变化时，主要修改领域逻辑层。
    *   当接口协议变化时，主要修改接口呈现层。
    *   当底层技术或外部服务变化时，主要修改基础设施层。
    *   这种分离使得系统更容易维护，并且在需要引入新功能或技术时，可以更有针对性地进行扩展，而不会对整个系统造成大的冲击。例如，未来引入P1的"提醒设置"功能，可以在应用层增加新的服务，在领域层实现提醒规则逻辑，并通过基础设施层与通知服务交互。

*   **KISS (Keep It Simple, Stupid) 和 YAGNI (You Ain't Gonna Need It)**:
    *   对于当前MVP阶段的需求，四层结构提供了足够的灵活性和分离度，而没有引入不必要的复杂层级。例如，没有单独划分出一个复杂的"共享内核层"或过细的"数据聚合层"，因为当前需求尚未体现出这种复杂性。
    *   如果未来业务变得极其复杂，可以考虑在领域层内部进一步划分限界上下文（Bounded Contexts）或引入更细致的领域驱动设计模式，但目前阶段此四层结构是合适的。

与模板中可能存在的通用示例相比，本设计强调了领域逻辑层的核心地位，因为作业报告涉及较多的数据计算和业务规则（如正确率、关注/鼓励学生等）。应用服务层则扮演协调者的角色。这种划分更贴合当前项目的实际需求。

### 3.3. 数据模型设计 (Conceptual Data Model)

此为概念模型，非物理数据库设计。主要展示核心领域对象及其关系。

```mermaid
erDiagram
    Teacher {
    }

    Class {
    }

    Student {
    }

    HomeworkTask {
    }

    HomeworkReport {
    }

    StudentHomeworkReport {
    }

    QuestionStat {
    }

    StudentQuestion {
    }

    Teacher ||--o{ HomeworkTask : "负责/有权限查看"
    HomeworkTask ||--o{ HomeworkReport : "生成"
    Class ||--o{ HomeworkReport : "属于"
    HomeworkReport ||--o{ StudentHomeworkReport : "包含多个"
    HomeworkReport ||--o{ QuestionStat : "包含多个"
    Student ||--o{ StudentHomeworkReport : "属于"
    Student ||--o{ StudentQuestion : "提出"
    HomeworkTask ||--o{ StudentQuestion : "针对"

    %% 说明：以下是对核心概念实体的职责描述，而非详细字段。
    %% erDiagram仅展示实体及其关系。
```

**核心实体说明:**

*   **`Teacher` (教师)**: 代表系统中的教师用户。需关联到其教学职责范围，如所教班级、学科，并承载教师的身份认证与权限信息（通常由用户中心和运营管理平台提供）。
*   **`Class` (班级)**: 代表教学活动中的一个班级单元。需包含班级的唯一标识及其基本描述信息（如班级名称，关联的教师和学生，通常由运营管理平台提供）。
*   **`Student` (学生)**: 代表参与学习和作业的学生用户。需包含学生的唯一标识及其在班级中的身份信息（通常由运营管理平台提供）。
*   **`HomeworkTask` (作业任务)**: 封装了一次具体的作业活动。它需要承载作业的唯一标识、名称、类型（如课后作业、测验）、所属学科、布置时间等元数据，并能关联到具体的教学内容（如题目、素材范围，通常由作业管理服务和内容平台提供）。
*   **`HomeworkReport` (作业报告)**: 封装了一次特定作业在某个班级下的整体分析结果。它需要承载作业和班级的标识，反映班级层面的关键统计指标（如整体完成度、平均答题表现），并能够关联到系统建议的需特别关注或表扬的学生群体。
*   **`StudentHomeworkReport` (学生个体作业报告)**: 封装了一个学生在特定作业中的个体学习表现。需关联学生和作业报告，承载该生在此次作业中的完成进度、答题准确度、具体的作答详情（概念上指学生对每道题的作答情况），以及该生提出的相关问题记录。
*   **`QuestionStat` (题目统计)**: 封装了特定作业中每一道题目的答题统计信息。需关联到作业报告和内容平台中的具体题目，反映该题目在本次作业中的整体作答情况，如被作答的次数、答对的人数、答错的人数、以及是否构成共性错误等。
*   **`StudentQuestion` (学生提问)**: 封装了学生在作业过程中针对教学内容或题目提出的具体疑问。需关联学生和作业任务，并记录提问的内容和时间。

**数据流转与来源:**

*   **作业基础信息**: 来自"作业管理服务"（假设）。
*   **学生原始答题数据**: 来自"学生作答数据服务"（假设）。
*   **题目内容与解析**: 来自"内容平台"。
*   **班级、学生、教师信息**: 来自"运营管理平台"和"用户中心"。
*   **计算结果 (正确率、建议列表等)**: 由本服务的"计算引擎"或"领域逻辑层"根据原始数据和预设规则计算得出。

### 3.4. 关键业务流程的高层视图

#### 3.4.1. 流程1: 查看作业列表

```mermaid
sequenceDiagram
    participant TeacherFE as 教师前端
    participant APIGW as API网关
    participant ReportSvc as 作业报告服务
    participant HWMgtSvc as 作业管理服务
    participant UCSvc as 用户中心
    participant AdminSvc as 运营管理平台

    TeacherFE->>APIGW: 请求作业列表 （携带筛选/搜索条件）
    APIGW->>ReportSvc: 转发请求 （含用户身份信息）
    ReportSvc->>UCSvc: 验证用户身份, 获取权限信息
    UCSvc-->>ReportSvc: 返回用户权限
    ReportSvc->>HWMgtSvc: 根据权限和条件查询作业任务ID列表
    HWMgtSvc-->>ReportSvc: 返回作业任务ID列表
    ReportSvc->>ReportSvc: （循环/批量）对每个任务ID, 获取简要统计（完成率/正确率）
    %% 简要统计可能需要从本服务缓存或预计算结果获取，或实时轻量计算
    ReportSvc->>AdminSvc: （可选）获取班级/学科名称等辅助信息
    AdminSvc-->>ReportSvc: 返回辅助信息
    ReportSvc-->>APIGW: 返回作业列表数据
    APIGW-->>TeacherFE: 响应作业列表
```

#### 3.4.2. 流程2: 查看作业报告详情 (以学生报告Tab为例)

```mermaid
sequenceDiagram
    participant TeacherFE as 教师前端
    participant APIGW as API网关
    participant ReportSvc_App as 作业报告服务（应用层）
    participant ReportSvc_Domain as 作业报告服务（领域层/计算引擎）
    participant ReportSvc_Infra as 作业报告服务（基础设施层）
    participant AnswerDataSvc as 学生作答数据服务
    participant QUESvc as 内容平台
    participant AdminSvc as 运营管理平台

    TeacherFE->>APIGW: 请求作业报告详情 （作业ID, 班级ID, Tab='学生报告'）
    APIGW->>ReportSvc_App: 转发请求
    ReportSvc_App->>ReportSvc_Infra: 查询作业基本信息 （来自作业管理服务缓存或DB）
    ReportSvc_Infra-->>ReportSvc_App: 返回作业信息
    ReportSvc_App->>ReportSvc_Infra: 查询班级学生列表 （来自AdminSvc）
    ReportSvc_Infra-->>ReportSvc_App: 返回学生列表
    ReportSvc_App->>ReportSvc_Domain: 请求计算学生报告数据 （传递学生列表, 作业ID）
    ReportSvc_Domain->>ReportSvc_Infra: （批量）查询学生作答数据 （来自AnswerDataSvc）
    ReportSvc_Infra-->>ReportSvc_Domain: 返回学生作答原始数据
    ReportSvc_Domain->>ReportSvc_Infra: （可选, 如需题目详情） 查询题目信息 （来自QUESvc）
    ReportSvc_Infra-->>ReportSvc_Domain: 返回题目信息
    ReportSvc_Domain->>ReportSvc_Domain: 计算平均进度、平均正确率
    ReportSvc_Domain->>ReportSvc_Domain: 应用策略识别"鼓励"和"关注"学生
    ReportSvc_Domain->>ReportSvc_Domain: 计算个体学生进度、正确率，标记异常数据
    ReportSvc_Domain-->>ReportSvc_App: 返回计算后的学生报告数据
    ReportSvc_App->>ReportSvc_Infra: （可选） 获取学生姓名等补充信息 （来自AdminSvc）
    ReportSvc_Infra-->>ReportSvc_App: 返回补充信息
    ReportSvc_App-->>APIGW: 返回学生报告Tab数据
    APIGW-->>TeacherFE: 响应学生报告Tab
```

#### 3.4.3. 流程3: 查看答题结果Tab

```mermaid
sequenceDiagram
    participant TeacherFE as 教师前端
    participant APIGW as API网关
    participant ReportSvc_App as 作业报告服务（应用层）
    participant ReportSvc_Domain as 作业报告服务（领域层/计算引擎）
    participant ReportSvc_Infra as 作业报告服务（基础设施层）
    participant AnswerDataSvc as 学生作答数据服务
    participant QUESvc as 内容平台

    TeacherFE->>APIGW: 请求作业报告详情 （作业ID, 班级ID, Tab='答题结果'）
    APIGW->>ReportSvc_App: 转发请求
    ReportSvc_App->>ReportSvc_Infra: 查询作业中的题目列表 （来自作业管理服务或内容平台）
    ReportSvc_Infra-->>ReportSvc_App: 返回题目ID列表
    ReportSvc_App->>ReportSvc_Domain: 请求计算题目统计数据 （传递题目ID列表, 作业ID, 班级ID）
    ReportSvc_Domain->>ReportSvc_Infra: （批量）查询学生针对这些题目的作答数据 （来自AnswerDataSvc）
    ReportSvc_Infra-->>ReportSvc_Domain: 返回学生作答原始数据
    ReportSvc_Domain->>ReportSvc_Infra: （批量）查询题目详情（题干、选项、答案） （来自QUESvc）
    ReportSvc_Infra-->>ReportSvc_Domain: 返回题目详情
    ReportSvc_Domain->>ReportSvc_Domain: 计算每题正确率、答错人数、作答人数
    ReportSvc_Domain->>ReportSvc_Domain: 识别共性错题
    ReportSvc_Domain-->>ReportSvc_App: 返回计算后的题目统计数据
    ReportSvc_App-->>APIGW: 返回答题结果Tab数据
    APIGW-->>TeacherFE: 响应答题结果Tab
```

#### 3.4.4. 流程4: 系统识别并展示需关注/鼓励学生 (细化流程2中的一部分)

```mermaid
sequenceDiagram
    participant ReportSvc_App as 应用服务层
    participant ReportSvc_Domain as 领域逻辑层
    participant ReportSvc_Infra as 基础设施层
    participant AnswerDataSvc as 学生作答数据服务
    participant AdminSvc as 运营管理平台

    ReportSvc_App->>ReportSvc_Infra: 获取班级内所有学生ID列表 （AdminSvc）
    ReportSvc_Infra-->>ReportSvc_App: 返回学生ID列表
    ReportSvc_App->>ReportSvc_Domain: 传入（作业ID, 学生ID列表） 请求识别
    loop 针对每个学生
        ReportSvc_Domain->>ReportSvc_Infra: 获取该学生作业完成情况、答题数据 （AnswerDataSvc）
        ReportSvc_Infra-->>ReportSvc_Domain: 返回学生数据
        ReportSvc_Domain->>ReportSvc_Domain: 应用"鼓励"策略规则 （如进步、连续答对）
        ReportSvc_Domain->>ReportSvc_Domain: 应用"关注"策略规则 （如未完成、表现下降）
    end
    ReportSvc_Domain->>ReportSvc_Domain: 汇总建议列表
    ReportSvc_Domain-->>ReportSvc_App: 返回"鼓励"和"关注"学生ID列表及原因（可选）
```

### 3.5. 服务交互与API设计原则

*   **交互方式**:
    *   前端与API网关/作业报告服务之间：优先考虑使用 HTTP/RESTful API，辅以JSON数据格式。对于需要双向通信或低延迟的场景，未来可评估gRPC。
    *   作业报告服务与内部依赖服务（作业管理、学生作答数据）之间：如果在一个单体应用内部署为模块，则为内部方法调用；如果为独立微服务，则推荐使用gRPC进行内部通信，以获得更好的性能和类型安全。
    *   作业报告服务与外部公共服务（用户中心、内容平台、运营管理平台）：遵循这些公共服务提供的接口规范，通常为HTTP/RESTful或gRPC。
*   **API设计原则**:
    *   **资源导向**: 基于资源进行URL设计 (e.g., `/reports/{reportId}/students`, `/reports/{reportId}/questions/{questionId}/stats`)。
    *   **明确的HTTP动词**: 使用 `GET` (查询), `POST` (创建), `PUT` (更新), `DELETE` (删除)。
    *   **版本控制**: 在API URL中或HTTP Header中包含版本信息 (e.g., `/api/v1/...`)。
    *   **统一的响应格式**:
        ```json
        {
          "code": 0, // 0表示成功，其他表示错误码
          "message": "Success", // 成功或错误信息
          "data": { /* 业务数据 */ } // 或者 data: []
        }
        ```
    *   **错误处理**: 定义清晰的错误码和错误信息，方便客户端处理。
    *   **分页与排序**: 对于列表数据，支持分页参数（如 `page`, `pageSize` 或 `offset`, `limit`）和排序参数（如 `sortBy`, `order`）。
    *   **幂等性**: 对于 `PUT` 和 `DELETE` 操作，确保幂等性。`POST` 操作根据业务场景判断是否需要保证幂等性。
    *   **安全性**: 所有API均需进行认证和授权校验。敏感数据在传输和存储时考虑加密。

## 4. 技术选型

严格遵循 `backend/global_rules/rules-tech-stack.mdc` 中定义的技术栈。

### 4.1. 服务端语言与框架

*   **语言**: Go
*   **Web/API框架**: Gin (因其轻量级、高性能和广泛的社区支持，符合技术栈约束)
*   **RPC框架**: gRPC (如果进行微服务拆分，或与内部gRPC服务交互，符合技术栈约束)

### 4.2. 数据存储

*   **关系型数据库**: MySQL (符合技术栈约束)
    *   用途: 存储作业报告核心数据、学生个体报告数据、题目统计数据、学生提问等结构化数据。需要事务支持的场景。
*   **NoSQL数据库 (文档型)**: MongoDB (符合技术栈约束)
    *   用途: 可选，用于存储一些半结构化或需要灵活模式的数据，例如学生答题的详细过程快照，或者复杂的嵌套报告结构。PRD中 `answersDetails` 这种JSON结构可以考虑。
*   **缓存服务**: Redis (符合技术栈约束)
    *   用途: 缓存热点数据，如已生成的报告片段、常用统计结果、外部服务（如内容平台、用户信息）的查询结果，以提高响应速度和降低后端负载。

### 4.3. 消息队列

*   **消息队列**: Kafka (符合技术栈约束)
    *   用途: (初期MVP可能不直接引入，但架构上预留)
        *   **异步计算**: 对于复杂的报告生成（如涉及大量学生或题目的大型作业），可以将计算任务投递到消息队列，由后台工作进程异步处理，避免长时间阻塞API请求。
        *   **事件驱动**: 发布领域事件（如"作业报告已生成"），供其他下游服务（如未来的提醒服务）消费。
        *   **数据同步/ETL**: 如果原始答题数据或作业数据源变更频繁，可以通过MQ进行数据同步。

### 4.4. 日志与监控

*   **日志库**: Zap (Go生态中高性能的结构化日志库，符合技术栈约束)
*   **监控与告警**: Prometheus + Grafana (符合技术栈约束)
    *   `Prometheus` 用于指标收集。
    *   `Grafana` 用于指标可视化和仪表盘展示。
    *   配置关键业务指标（API响应时间、错误率、队列长度等）和系统资源指标的监控和告警。

### 4.5. 配置管理

*   **配置中心**: Viper (Go库，支持多种配置源) 或 结合 Apollo/Nacos (如果已有统一配置中心，符合技术栈约束的集成方式)
    *   管理数据库连接、外部服务地址、业务规则阈值（如共性错题识别阈值、鼓励/关注学生策略参数）等。

### 4.6. 部署与容器化

*   **容器化**: Docker
*   **容器编排**: Kubernetes (K8s) (如果公司有统一K8s平台)

### 4.7. API网关

*   APISIX 或 Kong (如果已有统一API网关，符合技术栈约束的集成方式)
    *   处理路由、认证、限流、日志等。

## 5. 非功能性设计考量

### 5.1. 性能与可伸缩性

*   **读写分离**: 对于报告这种读多写少的场景，如果数据量大，未来可考虑数据库读写分离。
*   **缓存策略**: 积极使用缓存（Redis）存储计算结果、热点数据。定义合理的缓存雪崩、穿透、击穿防护策略。
    *   例如，作业报告一旦生成，在作业未发生显著变化（如学生提交新答案）前可以缓存。
    *   班级学生列表、题目信息等来自外部服务的数据也可以设置适当的缓存时间。
*   **异步处理**: 对于耗时的计算（如首次生成大型作业报告），采用消息队列（Kafka）进行异步处理，接口快速响应任务已接收。
*   **数据库优化**:
    *   合理设计索引，特别是针对筛选、排序、关联查询的字段。
    *   避免大事务，优化慢查询。
*   **水平扩展**: 服务设计为无状态或易于水平扩展，方便通过增加实例数来提升处理能力。API网关配合负载均衡器分发请求。
*   **数据预计算/聚合**: 对于常用的统计指标（如班级整体完成率、正确率），可以考虑定时任务进行预计算或在数据写入时进行部分聚合，存储到分析型数据存储或缓存中，避免查询时实时计算大量数据。

### 5.2. 可用性与容错性

*   **服务冗余**: 通过Kubernetes等编排工具部署多个服务实例，确保单点故障不影响整体可用性。
*   **依赖降级与熔断**:
    *   当依赖的外部服务（如内容平台、运营管理平台）不可用或响应超时时，应有降级策略（例如，返回缓存数据，或提示部分功能暂不可用），避免自身服务雪崩。
    *   使用如 `gobreaker` 或 `Hystrix-go` (如果适用)等库实现熔断和舱壁隔离模式。
*   **超时控制**: 对所有外部调用和内部耗时操作设置合理的超时时间。
*   **重试机制**: 对临时的网络抖动或外部服务瞬时错误，引入适当的重试机制（如指数退避）。
*   **健康检查**: 实现健康检查接口，供K8s等进行存活探针和就绪探针检查。
*   **数据备份与恢复**: 依赖数据库自身的备份恢复机制。

### 5.3. 安全性

*   **认证与授权**:
    *   所有API接口必须经过认证，确认用户身份。依赖用户中心（UCenter）进行Token验证或类似的认证机制。
    *   严格的授权检查，确保教师只能访问其有权限查看的班级作业报告和学生数据。权限数据可能来自运营管理平台或作业管理服务。
*   **输入校验**: 对所有外部输入（API参数、查询条件）进行严格校验，防止SQL注入、XSS等常见攻击。
*   **数据传输安全**: 对外API接口使用HTTPS。内部服务间通信（如gRPC）也应考虑启用TLS。
*   **敏感数据处理**:
    *   学生UID等敏感信息在日志中输出时考虑脱敏。
    *   根据合规要求，评估是否需要对存储的敏感数据进行加密。
*   **依赖库安全**: 定期扫描和更新依赖的第三方库，避免已知的安全漏洞。
*   **防爬与限流**: API网关层面或服务自身实现必要的防爬虫策略和接口限流，防止恶意请求消耗资源。

### 5.4. 可维护性与可测试性

*   **遵循编码规范**: 团队统一的Go编码规范。
*   **代码注释**: 关键逻辑、复杂算法、对外接口必须有清晰的注释。
*   **模块化设计**: 严格遵循3.2节的逻辑分层，各模块职责单一。
*   **单元测试**: 核心业务逻辑（领域层）、工具类、算法等必须有单元测试覆盖，推荐使用Go内置的testing包。
*   **集成测试**: 针对API接口和关键业务流程编写集成测试。
*   **日志规范**: 结构化日志（如Zap），包含TraceID，方便问题排查和链路追踪。
*   **配置分离**: 业务逻辑与配置分离，配置通过配置中心管理。
*   **文档化**: 重要的设计决策、接口定义、复杂逻辑应有文档记录。

## 6. 数据迁移与上线策略 (MVP阶段)

### 6.1. 数据初始化/迁移

*   **PRD中计算规则涉及的原始数据**:
    *   **学生作答数据**: 假设已存在于"学生作答数据服务"中。报告服务将通过接口查询，无需迁移。
    *   **作业、班级、学生、题目基础信息**: 假设已存在于各自的源服务（作业管理、运营管理、内容平台）。报告服务通过接口查询。
*   **本服务自身数据**:
    *   `HomeworkReport`, `StudentHomeworkReport`, `QuestionStat`, `StudentQuestion` 等表结构将在首次部署时通过数据库脚本创建。
    *   历史作业报告的生成：
        *   **策略1 (按需生成)**: 上线初期，当教师首次访问某个历史作业的报告时，系统实时计算并存储该报告。优点是简单快速上线，缺点是首次访问可能较慢。
        *   **策略2 (批量预生成)**: 上线前或低峰期，通过脚本批量跑批处理最近一段时间内或重要的历史作业，预先生成报告数据并存入数据库。优点是用户访问时体验好，缺点是需要额外的跑批开发和时间。
        *   **MVP建议**: 初期可采用策略1，快速上线。同时监控首次访问性能，如果不可接受，再考虑实现策略2或优化实时计算。

### 6.2. 上线部署策略

*   **环境准备**: 准备好测试环境、预发布环境、生产环境的服务器、数据库、缓存、消息队列等资源。
*   **灰度发布 (可选)**: 如果条件允许，可以先对部分教师用户或部分班级开放功能，收集反馈，验证稳定性，再逐步全量。
*   **监控先行**: 在正式上线前，确保相关的监控告警已配置并生效。
*   **回滚预案**: 准备好快速回滚方案，一旦出现严重问题，能迅速恢复到上一个稳定版本。
*   **上线步骤**:
    1.  数据库结构变更部署。
    2.  服务程序包部署到服务器。
    3.  配置更新。
    4.  启动服务实例。
    5.  进行基本功能验证（冒烟测试）。
    6.  逐步开放流量（如果采用灰度发布）。
    7.  密切监控系统各项指标。

## 7. 未来演进与扩展性思考

*   **P1功能 - 提醒设置**:
    *   当前架构可以通过在应用服务层增加新的服务来处理提醒规则的配置和触发逻辑。
    *   领域层可以扩展以包含提醒策略的评估。
    *   基础设施层需要集成通知服务（邮件、短信、App推送等）。
    *   可以利用Kafka等消息队列，当特定事件（如报告生成、识别到需重点关注学生）发生时，发布事件，提醒服务消费这些事件并根据用户配置触发通知。
*   **更复杂的分析维度**:
    *   如果未来需要引入更复杂的跨作业分析、学生成长轨迹分析等，当前的数据模型和计算引擎可能需要扩展。
    *   可能需要引入专门的数据分析引擎或OLAP数据库。
    *   数据预聚合和ETL过程会更加重要。
*   **服务拆分 (微服务化)**:
    *   当前MVP阶段，作业报告功能可以作为一个单体服务或一个大模块存在。
    *   如果未来业务复杂度极高，或者团队规模扩大，可以考虑将"计算引擎"、"报告数据存储与查询"、"学生提问管理"等进一步拆分为更细粒度的微服务。
    *   拆分时需注意服务边界的划分，确保高内聚、低耦合。gRPC是推荐的服务间通信方式。
*   **个性化报告与推荐**:
    *   基于更丰富的学生行为数据和画像，未来可以提供更个性化的学习报告和资源推荐。这可能需要引入机器学习能力。
*   **数据导出**:
    *   PRD中提及"数据导出(可能)"。可以在应用服务层增加导出接口，支持将报告数据导出为CSV或Excel格式。对于大数据量的导出，应采用异步方式，生成文件后提供下载链接。

## 8. 架构文档自查清单

### 8.1. 架构完备性自查清单

| 序号 | 检查领域         | 检查项                                                                 | 自查结果 | 备注 (如否或需注意，说明原因或改进计划)                                                              |
|------|------------------|------------------------------------------------------------------------|----------|--------------------------------------------------------------------------------------------------|
| 1    | 文档结构与清晰度 | 项目背景、目标、范围是否明确？                                         | ✅        | 章节 1.1, 1.2, 1.3 已清晰描述。                                                                    |
| 2    | 文档结构与清晰度 | 文档结构是否清晰，易于阅读和理解？                                       | ✅        | 遵循模板结构，内容组织有序，力求表达清晰。                                                             |
| 3    | 架构视图         | 是否给出了清晰的总体架构视图？                                             | ✅        | 章节 3.1 提供了高层架构图和核心思路。                                                                 |
| 4    | 架构视图         | 逻辑分层是否清晰，并解释了各层职责及设计合理性？                             | ✅        | 章节 3.2.1 详细描述了四层逻辑分层模型的职责、封装变化、交互边界及设计合理性。                                |
| 5    | 设计原创性       | 逻辑分层是否基于实际需求定制，而非简单复制模板？                             | ✅        | 章节 3.2.1 的设计合理性论证部分已明确说明是针对本项目特点独立设计。                                      |
| 6    | 数据模型         | 是否描述了核心数据模型及其关系（概念层面），避免了数据库设计细节？             | ✅        | 章节 3.3 提供了概念性的 `erDiagram` 和实体职责描述，聚焦业务概念。                                  |
| 7    | API设计          | 服务交互方式和API设计原则是否明确？                                        | ✅        | 章节 3.5 阐述了交互方式选型原则及API设计关键原则。                                                 |
| 8    | 技术选型         | 技术选型是否遵循了统一的技术栈约束？                                       | ✅        | 章节 4 严格按照 `backend/global_rules/rules-tech-stack.mdc` 进行选型，并说明了理由。                 |
| 9    | 非功能性考量     | 是否充分考虑了性能、可伸缩性、可用性、安全性等非功能性需求？                   | ✅        | 章节 5 详细讨论了针对这些关键质量属性的设计策略和技术手段。                                            |
| 10   | 未来规划         | 是否对未来的演进和扩展性进行了思考？                                       | ✅        | 章节 7 讨论了P1功能实现路径及更长远的扩展方向。                                                   |
| 11   | 引用与规范       | 是否所有引用的外部文档和服务都已在参考资料中列出？                           | ✅        | 章节 1.6 列出了所有已知的项目相关文档和依赖服务描述文件。                                               |
| 12   | 图表规范         | Mermaid图表是否符合规范（符号、注释、子图名等），确保可正确渲染？              | ✅        | 已根据Mermaid规范对文档中所有图表进行检查和修正（如全角括号、独立注释行、合规子图名）。                            |
| 13   | 设计焦点         | 是否避免了过度关注实现细节（如具体字段定义、接口参数），聚焦架构层面？           | ✅        | 设计侧重于服务、模块、层次、核心概念及其交互关系，符合架构师职责。                                        |
| 14   | 模板遵循         | 对于模板中要求独立思考和原创设计的部分（如逻辑分层、流程图）是否做到了？         | ✅        | 逻辑分层模型（章节 3.2.1）和关键业务流程视图（章节 3.4）均根据项目需求独立设计并作了阐述。                      |
| 15   | 设计原则         | 架构设计是否遵循了SOLID, KISS, YAGNI, DRY等核心设计原则？                  | ✅        | 在逻辑分层论证、技术选型、非功能性设计等环节均体现了这些原则的考量。                                      |
| 16   | 文件规范         | 输出文件名是否符合规定格式和路径？                                           | ✅        | 文件名：`ai-td-教师端_1期_作业报告_analyzed-Gemini-2.5-Pro.md`。路径：`documents/ai-docs/`。符合生成规则。  |

### 8.2. 需求-架构完备性自查清单

| 序号 | PRD对应章节/需求点                                  | 架构支持检查项                                                                                                | 自查结果 | 备注 (说明架构如何支持或未覆盖原因)                                                                                                                                                              |
|------|---------------------------------------------------|-------------------------------------------------------------------------------------------------------------|----------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| 1    | PRD Sec 2.1: 作业列表展示                           | 架构是否支持展示教师负责或有权限的作业任务列表，包含任务核心信息（类型、名称、班级、学科、日期、完成率、正确率等）？        | ✅        | 章节 3.1、3.4.1、3.5：`ReportSvc` 负责处理，通过 `DATA_ACCESS` 从 `HW_MGT` 获取任务，计算/获取统计数据。API设计支持列表展示。                                                                           |
| 2    | PRD Sec 2.1: 作业列表展示 - 筛选与搜索                | 架构是否支持作业列表按类型、班级、学科、日期进行筛选和关键词搜索（任务名称）？                                        | ✅        | 章节 3.4.1、3.5：API设计中已考虑筛选和搜索参数，`ReportSvc` 将这些条件传递给 `DATA_ACCESS` 和底层服务（`HW_MGT`）。                                                                                 |
| 3    | PRD Sec 2.1: 作业列表展示 - 操作                    | 架构是否为本人布置的任务支持编辑、删除、复制操作预留了能力？（注：本期主要为报告展示，操作接口可能由作业管理服务直接提供） | ✅        | 章节 3.1、3.5：虽然核心是报告，但架构上 `ReportSvc` 可作为BFF与 `HW_MGT` 交互，或前端直接调用 `HW_MGT`。API设计原则支持这些操作。                                                                        |
| 4    | PRD Sec 2.1: 作业报告详情 (整体)                    | 架构是否支持点击作业列表进入详细报告页，包含顶部任务详情与筛选，并承载学生报告、答题结果、学生提问三个Tab页的切换？         | ✅        | 章节 3.1、3.4.2、3.4.3、3.5：`ReportSvc` 作为核心，处理报告详情的整体逻辑和Tab内容的分发。API设计支持按作业ID获取报告详情。                                                                                 |
| 5    | PRD Sec 2.1: 学生报告 Tab - 班级整体统计              | 架构是否支持展示任务的整体班级统计数据（发布/完成时间、班级、素材范围、平均进度、平均正确率）？                           | ✅        | 章节 3.2.1 (领域逻辑层/计算引擎)、3.4.2：`CALC_ENGINE` 负责计算平均值，`ReportSvc` 整合展示。数据源自 `HW_MGT`、`ANSWER_DATA`。                                                                              |
| 6    | PRD Sec 2.1: 学生报告 Tab - 建议鼓励/关注学生         | 架构是否支持系统根据策略建议"鼓励"和"关注"的学生列表，并在列表中高亮显示？                                          | ✅        | 章节 3.2.1 (领域逻辑层/计算引擎)、3.4.4：`CALC_ENGINE` 应用预设策略（PRD Sec 3 提及规则待细化）进行识别。`ReportSvc` 在数据准备好后供前端高亮。                                                                  |
| 7    | PRD Sec 2.1: 学生报告 Tab - 完整学生列表与个体数据      | 架构是否支持展示完整的按UID排序的学生列表，包含个体进度、正确率，并对异常数据进行视觉提示的后端数据支持？                     | ✅        | 章节 3.4.2、3.4.4：`ReportSvc` 从 `AdminSvc` 获取学生列表，`CALC_ENGINE` 计算个体数据及识别异常（基于PRD Sec 3飘色规则）。                                                                                  |
| 8    | PRD Sec 2.1: 答题结果 Tab - 整体统计与搜索            | 架构是否支持展示题目总数、共性错题数，并支持题目关键词搜索（题干、选项、解析）？                                        | ✅        | 章节 3.4.3：`ReportSvc` 从 `QUESvc` 获取题目信息，`CALC_ENGINE` 计算共性错题（基于PRD Sec 3规则）。API和数据访问层支持关键词搜索。                                                                               |
| 9    | PRD Sec 2.1: 答题结果 Tab - 题目列表与统计            | 架构是否支持列表展示每道题的题干、选项、答案解析（可展开/收起），以及该题的正确率、答错人数、作答人数？                       | ✅        | 章节 3.4.3：`ReportSvc` 从 `QUESvc` 获取题目详情，`CALC_ENGINE` 基于 `AnswerDataSvc` 的数据计算各项统计指标。                                                                                              |
| 10   | PRD Sec 2.1: 答题结果 Tab - 侧边题目面板              | 架构是否为侧边题目面板提供数据支持（快速导航并用颜色区分题目正确率）？                                                  | ✅        | 章节 3.4.3：`ReportSvc` 在准备答题结果数据时，会包含每题的ID和正确率，供前端构建侧边面板。                                                                                                               |
| 11   | PRD Sec 2.1: 学生提问 Tab                           | 架构是否支持汇总展示学生针对该次作业提出的问题列表，并支持查看提问详情？                                                | ✅        | 章节 3.1、3.3、3.5：数据模型中包含 `StudentQuestion` 实体。`ReportSvc` 负责从数据存储中提取并展示学生提问列表及详情。                                                                                        |
| 12   | PRD Sec 2.1: 单学生作业报告                         | 架构是否支持从学生报告Tab点击学生，或从其他入口进入，展示特定学生在该次作业中的详细报告（答题结果和提问情况）？                 | ✅        | 章节 3.1、3.5：API设计支持按学生ID和作业ID查询其个体报告。`ReportSvc` 整合该学生的答题统计和提问数据。                                                                                                         |
| 13   | PRD Sec 2.1 & Sec 3: 策略说明与计算规则应用         | 架构（特别是领域逻辑层/计算引擎）是否能够支撑PRD中定义的各项计算规则与策略（如题目正确率、异常数据、共性错题、鼓励/关注等）的实现？ | ✅        | 章节 3.2.1、4：`CALC_ENGINE` (领域逻辑层的一部分) 是这些规则和策略的核心实现场所。技术选型 (Go) 支持复杂计算。PRD中部分规则细节待产品明确。                                                                     |
| 14   | PRD Sec 2.2: 非功能性需求 - 性能                    | 架构设计是否考虑了作业列表和报告详情页的加载性能要求？                                                                | ✅        | 章节 5.1：通过缓存（Redis）、异步处理（Kafka预留）、数据库优化、数据预计算等手段保障性能。                                                                                                             |
| 15   | PRD Sec 2.2: 非功能性需求 - 安全性                  | 架构设计是否考虑了教师权限控制和数据访问安全？                                                                      | ✅        | 章节 5.3：依赖用户中心认证，进行严格授权检查。API设计包含安全原则。                                                                                                                              |

--- 等待您的命令，指挥官 
'use client';

import Link from 'next/link';
import MainLayout from '@/components/layout/MainLayout';

export default function WorkflowError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  console.error('工作流页面错误:', error);

  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-lg mx-auto bg-white shadow-md rounded-lg overflow-hidden">
          <div className="p-6">
            <div className="flex flex-col items-center">
              <svg className="w-16 h-16 text-red-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h2 className="text-2xl font-bold text-gray-800 mb-2">加载工作流失败</h2>
              <p className="text-gray-600 text-center mb-6">
                {error.message || "无法加载工作流详情，请检查您的网络连接或稍后再试。"}
              </p>
              <div className="flex space-x-4">
                <button
                  onClick={() => reset()}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  重试
                </button>
                <Link
                  href="/workflow"
                  className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors"
                >
                  返回工作流列表
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
} 
# AI文档工作流前端

基于Next.js、React、Tailwind CSS和Chakra UI的AI文档工作流平台前端，提供完整的文档处理和AI增强功能。

## 功能特点

### 核心功能
- **文档处理**：解析飞书文档和PDF，转换为Markdown格式
- **工作流管理**：完整的5步工作流（文档处理→PRD生成→技术设计→开发文档→API文档）
- **AI增强文档生成**：支持前端/后端分离的PRD和技术设计文档生成
- **实时任务监控**：WebSocket实时状态更新和进度跟踪
- **Prompt模板管理**：创建、编辑和测试AI生成模型的Prompt模板

### 用户体验
- **响应式设计**：完美适配桌面和移动设备
- **实时反馈**：任务进度实时更新，状态变化即时响应
- **错误处理**：友好的错误提示和自动重试机制
- **数据持久化**：防止页面刷新导致的数据丢失

## 技术栈

### 核心框架
- **React 18** - 用户界面库，支持并发特性
- **Next.js 14** - 全栈React框架，使用App Router
- **TypeScript** - 类型安全的JavaScript超集

### UI与样式
- **Tailwind CSS** - 原子化CSS框架
- **Chakra UI** - 现代化React组件库，提供丰富的UI组件
- **Radix UI** - 无样式的可访问性组件库（部分组件）
- **Framer Motion** - 流畅的动画库
- **Lucide React** - 现代化图标库
- **Emotion** - CSS-in-JS样式库（Chakra UI依赖）

### 状态管理与数据
- **Zustand** - 轻量级状态管理，支持持久化
- **React Query (@tanstack/react-query)** - 数据获取、缓存和同步
- **React Hook Form** - 高性能表单处理
- **Zod** - TypeScript优先的数据验证

### 文档处理
- **React Markdown** - Markdown内容渲染
- **React Syntax Highlighter** - 代码语法高亮
- **Markdown-it** - Markdown解析和处理

## 项目架构

### 目录结构
```
frontend/
├── public/                 # 静态资源
│   └── images/             # 项目图片资源
├── src/                    # 源代码
│   ├── app/                # Next.js App Router
│   │   ├── api/            # API路由处理
│   │   ├── workflow/       # 工作流相关页面
│   │   │   ├── ai-prd/     # AI PRD生成页面
│   │   │   ├── ai-td/      # AI技术设计页面
│   │   │   ├── format-prd/ # PRD格式化页面
│   │   │   └── [id]/       # 动态工作流详情页
│   │   ├── configs/        # 配置管理
│   │   ├── layout.tsx      # 全局布局
│   │   └── page.tsx        # 首页
│   ├── components/         # UI组件
│   │   ├── layout/         # 布局组件
│   │   ├── home/           # 首页组件
│   │   ├── workflow/       # 工作流组件
│   │   ├── markdown/       # Markdown处理组件
│   │   ├── template/       # 模板组件
│   │   └── ui/             # 基础UI组件
│   ├── lib/                # 工具库
│   │   ├── api/            # API客户端
│   │   └── store/          # 状态管理
│   ├── types/              # TypeScript类型定义
│   │   ├── workflow.ts     # 工作流类型
│   │   ├── document.ts     # 文档类型
│   │   └── user.ts         # 用户类型
│   └── styles/             # 样式文件
├── env.ts                  # 环境配置
├── next.config.js          # Next.js配置
├── tailwind.config.js      # Tailwind配置
└── package.json            # 项目依赖
```

### 环境配置系统
项目支持多环境配置，自动根据运行环境选择合适的API地址：

- **本地开发环境**：`http://localhost:8000/api`
- **远程开发环境**：`http://*************:8000/api`
- **生产环境**：通过环境变量配置

配置文件：
- `env.ts` - 环境配置逻辑
- `next.config.js` - Next.js和API代理配置
- `.env.development` / `.env.production` - 环境变量

## 开发

安装依赖：

```bash
npm install
```

启动开发服务器：

```bash
npm run dev
```

构建生产版本：

```bash
npm run build
```

## 核心功能模块

### 工作流管理系统
完整的5步工作流处理流程：

1. **文档处理** - 上传PDF或输入飞书文档URL，解析转换为Markdown
2. **PRD生成** - 基于文档内容生成产品需求文档（支持前端/后端分离）
3. **技术设计** - 生成技术架构和设计方案（支持前端/后端分离）
4. **开发文档** - 生成详细的开发指南和实现文档
5. **API文档** - 生成OpenAPI格式的接口文档

### 状态管理架构
使用Zustand实现的模块化状态管理：

- **AuthStore** - 用户认证状态和操作
- **WorkflowStore** - 工作流状态管理
- **DocumentStore** - 文档处理状态
- **ConfigStore** - 系统配置管理

### API通信层
基于Fetch API的统一请求客户端：

- **自动重试机制** - 网络异常时的智能重试
- **认证管理** - JWT token自动管理和刷新
- **错误处理** - 统一的错误处理和用户提示
- **请求拦截** - 自动添加认证头和处理响应

### 主要API接口

#### 文档处理API
- `POST /api/documents/upload` - 上传PDF文档
- `POST /api/documents/process/pdf/{file_id}` - 处理PDF文件
- `POST /api/documents/process/feishu` - 处理飞书文档
- `POST /api/documents/process/ai-prd` - AI生成PRD文档
- `POST /api/documents/process/ai-td` - AI生成技术设计文档

#### 任务管理API
- `GET /api/documents/tasks/{task_id}` - 获取任务状态
- `GET /api/documents/tasks` - 获取所有任务列表

#### 认证API
- `POST /api/auth/token` - 用户登录获取token
- `GET /api/auth/me` - 获取当前用户信息

#### Prompt管理API
- `GET /api/prompts/categories` - 获取Prompt分类
- `GET /api/prompts/{category}/{name}` - 获取Prompt详情
- `POST /api/prompts/{category}/{name}` - 创建新Prompt
- `PUT /api/prompts/{category}/{name}` - 更新Prompt

## API调用最佳实践

前端调用API时，请遵循以下最佳实践：

1. **使用API客户端**：所有API调用应通过`src/lib/api`中的客户端函数进行，避免直接使用fetch
   ```typescript
   // 推荐
   import { documentApi } from '@/lib/api/document';
   const result = await documentApi.getTaskStatus(taskId);
   
   // 不推荐
   const result = await fetch(`/api/documents/tasks/${taskId}`);
   ```

2. **错误处理**：使用try-catch块处理API错误，并提供用户友好的错误信息
   ```typescript
   try {
     const result = await documentApi.getTaskStatus(taskId);
   } catch (error) {
     console.error('获取任务状态失败:', error);
     // 显示用户友好的错误信息
   }
   ```

3. **状态管理**：使用React Query或Zustand管理API状态和缓存

4. **重试机制**：API客户端已内置重试机制，无需在组件中实现额外的重试逻辑

## 路由系统

### App Router架构
项目使用Next.js 14的App Router，提供现代化的路由体验：

#### 主要路由
- `/` - 首页，产品介绍和快速入口
- `/workflow` - 工作流列表页面
- `/workflow/format-prd` - PRD格式化处理页面
- `/workflow/ai-prd` - AI PRD生成页面
- `/workflow/ai-td` - AI技术设计生成页面
- `/workflow/[id]` - 动态工作流详情页面

#### 路由特性
- **嵌套布局** - 支持多层级布局组件
- **加载状态** - 自动处理页面加载状态
- **错误边界** - 页面级错误处理
- **动态路由** - 支持参数化路由

### API代理配置
通过Next.js的rewrites功能实现API代理：

```javascript
// next.config.js
async rewrites() {
  return [
    {
      source: '/api/:path*',
      destination: `${API_HOST}/api/:path*`,
    }
  ];
}
```

- 自动代理所有`/api/*`请求到后端服务
- 支持CORS跨域请求
- 开发/生产环境自动切换API地址

## 组件系统

### 布局组件
- **MainLayout** - 主布局组件，包含Header和Footer
- **Header** - 顶部导航栏，支持响应式设计
- **Footer** - 底部信息栏

### 业务组件
- **WorkflowModal** - 工作流创建和编辑弹窗
- **MarkdownPreview** - Markdown内容预览组件
- **TemplateModal** - Prompt模板管理弹窗

### UI组件
基于Chakra UI构建的可复用组件：
- **Card** - 卡片容器组件
- **Button** - 按钮组件，支持多种样式和尺寸
- **Input** - 输入框组件，支持各种输入类型
- **Select** - 下拉选择组件
- **Modal** - 弹窗组件
- **Toast** - 消息提示组件
- **Progress** - 进度条组件
- **Spinner** - 加载动画组件

### Radix UI组件
项目中使用的Radix UI无头组件：
- **Dialog** - 对话框组件
- **Dropdown Menu** - 下拉菜单
- **Popover** - 弹出层组件
- **Tabs** - 标签页组件
- **Tooltip** - 工具提示组件

## 性能优化

### 代码分割
- **路由级分割** - 基于页面的自动代码分割
- **组件懒加载** - 使用React.lazy()动态导入大型组件
- **第三方库分割** - 将大型依赖库单独打包

### 数据缓存
- **React Query缓存** - API响应数据智能缓存
- **状态持久化** - 关键状态数据本地存储
- **图片优化** - Next.js Image组件自动优化

### 用户体验优化
- **骨架屏** - 数据加载时的占位界面
- **进度指示** - 长时间任务的进度显示
- **错误重试** - 失败操作的自动重试机制

## 开发最佳实践

### TypeScript使用
- **严格类型检查** - 启用strict模式
- **接口定义** - 完整的API和组件类型定义
- **类型推导** - 充分利用TypeScript的类型推导

### 组件开发
- **单一职责** - 每个组件只负责一个功能
- **Props接口** - 明确定义组件的输入输出
- **错误边界** - 组件级错误处理

### 状态管理
- **模块化Store** - 按功能模块划分状态
- **不可变更新** - 使用不可变的状态更新方式
- **副作用处理** - 合理处理异步操作和副作用
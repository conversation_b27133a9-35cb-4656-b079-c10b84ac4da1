# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** be-s2-1-ai-td-学生端-巩固练习-claude-sonnet-4.md
- **PRD版本号：** V1.0
- **提取日期：** 2025-05-29

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 练习会话 | 表示一次完整的练习过程，管理练习的生命周期状态 | 3.5.1 |
| 作答记录 | 记录学生的每次作答行为，用于掌握度计算和学习分析 | 3.5.1 |
| 掌握度记录 | 量化学生对特定知识点的掌握程度，支持个性化推题 | 3.5.1 |
| 推题策略 | 定义智能推题的算法规则和参数配置 | 3.5.1 |
| 学习报告 | 汇总学习成果，提供学习效果的可视化展示 | 3.5.1 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`练习会话`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 会话ID | 练习会话的唯一标识符 | 字符串 | 唯一, 非空 |  | 主键 | 3.5.1 |
| 学生ID | 参与练习的学生标识符 | 字符串 | 非空 |  | 外键 | 3.5.1 |
| 课程ID | 练习所属的课程标识符 | 字符串 | 非空 |  | 外键 | 3.5.1 |
| 练习状态 | 练习的当前状态 | 枚举类型 | 取值：进行中、已暂停、已完成、已退出 | 进行中 |  | 3.5.1 |
| 开始时间 | 练习开始的时间戳 | 时间戳 | 非空 |  |  | 3.5.1 |
| 结束时间 | 练习结束的时间戳 | 时间戳 | 可空 |  |  | 3.5.1 |
| 当前题目索引 | 当前正在作答的题目序号 | 整数 | 大于等于0 | 0 |  | 3.5.1 |
| 总题目数量 | 本次练习的题目总数 | 整数 | 大于0 |  |  | 3.5.1 |

**实体：`作答记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 记录ID | 作答记录的唯一标识符 | 字符串 | 唯一, 非空 |  | 主键 | 3.5.1 |
| 会话ID | 所属练习会话的标识符 | 字符串 | 非空 |  | 外键 | 3.5.1 |
| 题目ID | 作答题目的标识符 | 字符串 | 非空 |  | 外键 | 3.5.1 |
| 学生答案 | 学生提交的答案内容 | 文本 | 非空 |  |  | 3.5.1 |
| 正确答案 | 题目的标准答案 | 文本 | 非空 |  |  | 3.5.1 |
| 是否正确 | 作答结果的正确性判断 | 布尔值 | 非空 |  |  | 3.5.1 |
| 作答时长 | 学生作答所用时间（秒） | 整数 | 大于等于0 |  |  | 3.5.1 |
| 提交时间 | 作答提交的时间戳 | 时间戳 | 非空 |  |  | 3.5.1 |

**实体：`掌握度记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 学生ID | 学生的唯一标识符 | 字符串 | 非空 |  | 主键组成部分 | 3.5.1 |
| 知识点ID | 知识点的唯一标识符 | 字符串 | 非空 |  | 主键组成部分 | 3.5.1 |
| 掌握度值 | 学生对知识点的掌握程度量化值 | 数值型 (小数) | 0-100范围 |  |  | 3.5.1 |
| 更新时间 | 掌握度最后更新的时间戳 | 时间戳 | 非空 |  |  | 3.5.1 |
| 变化趋势 | 掌握度的变化方向 | 枚举类型 | 取值：上升、下降、稳定 |  |  | 3.5.1 |
| 计算版本 | 掌握度计算算法的版本号 | 字符串 | 非空 |  |  | 3.5.1 |

**实体：`推题策略`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 策略ID | 推题策略的唯一标识符 | 字符串 | 唯一, 非空 |  | 主键 | 3.5.1 |
| 策略名称 | 推题策略的名称 | 字符串 | 非空 |  |  | 3.5.1 |
| 适用条件 | 策略适用的条件描述 | 文本 | 非空 |  |  | 3.5.1 |
| 推题规则 | 具体的推题算法规则 | JSON文本 | 非空 |  |  | 3.5.1 |
| 难度调整参数 | 题目难度调整的参数配置 | JSON文本 | 非空 |  |  | 3.5.1 |

**实体：`学习报告`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 报告ID | 学习报告的唯一标识符 | 字符串 | 唯一, 非空 |  | 主键 | 3.5.1 |
| 学生ID | 报告所属学生的标识符 | 字符串 | 非空 |  | 外键 | 3.5.1 |
| 练习会话ID | 关联的练习会话标识符 | 字符串 | 非空 |  | 外键 | 3.5.1 |
| 掌握度变化 | 练习前后掌握度的变化情况 | JSON文本 | 非空 |  |  | 3.5.1 |
| 学习时长 | 总学习时间（分钟） | 整数 | 大于等于0 |  |  | 3.5.1 |
| 正确率 | 作答正确率（百分比） | 数值型 (小数) | 0-100范围 |  |  | 3.5.1 |
| 完成时间 | 报告生成的时间戳 | 时间戳 | 非空 |  |  | 3.5.1 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 (例如：用户 '拥有一个' 学校档案) | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 练习会话包含多个作答记录 | 练习会话 | 作答记录 | 1:N | 作答记录.会话ID -> 练习会话.会话ID | 3.5.1 |
| 学生拥有多个掌握度记录 | 学生 | 掌握度记录 | 1:N | 掌握度记录.学生ID -> 学生.ID | 3.5.1 |
| 知识点对应多个掌握度记录 | 知识点 | 掌握度记录 | 1:N | 掌握度记录.知识点ID -> 知识点.ID | 3.5.1 |
| 练习会话生成学习报告 | 练习会话 | 学习报告 | 1:1 | 学习报告.练习会话ID -> 练习会话.会话ID | 3.5.1 |
| 学生拥有多个学习报告 | 学生 | 学习报告 | 1:N | 学习报告.学生ID -> 学生.ID | 3.5.1 |
| 作答记录关联题目 | 作答记录 | 题目 | N:1 | 作答记录.题目ID -> 题目.ID | 3.5.1 |
| 练习会话属于课程 | 练习会话 | 课程 | N:1 | 练习会话.课程ID -> 课程.ID | 3.5.1 |
| 练习会话属于学生 | 练习会话 | 学生 | N:1 | 练习会话.学生ID -> 学生.ID | 3.5.1 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 实时掌握度计算 | 学生当前掌握度值, 作答结果(是否正确), 题目难度系数, 作答时长 | 更新后的掌握度值 | 2.1, 3.5.1 |
| 智能推题策略 | 学生掌握度值, 知识点难度, 学习表现历史, 推题策略参数 | 推荐题目列表 | 2.1, 3.3.1 |
| 再练一次判断 | 当前掌握度值, 掌握度阈值, 练习完成情况 | 是否需要再练一次 | 2.1, 3.3.1 |
| 学习报告生成 | 练习记录, 掌握度变化, 作答统计, 学习时长 | 学习报告内容 | 2.1, 3.4.4 |
| 练习完成度计算 | 已完成题目数, 总题目数, 练习状态 | 练习完成百分比 | 3.4.1, 3.4.2 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 练习会话.练习状态 | 学生开始练习、暂停练习、继续练习、完成练习、退出练习 | 实时/每次状态变化 | 3.4.1, 3.4.2, 3.4.3 |
| 作答记录 | 学生每次提交作答 | 实时/每次作答 | 3.4.2 |
| 掌握度记录.掌握度值 | 学生完成作答后，根据作答结果实时计算更新 | 实时/每次作答后 | 3.4.2, 3.5.2 |
| 练习会话.当前题目索引 | 学生完成当前题目，推送下一题时 | 实时/每题完成后 | 3.4.2 |
| 学习报告 | 练习会话完成后，通过异步事件触发生成 | 每次练习完成后 | 3.4.4 |
| 练习会话.结束时间 | 练习完成或退出时 | 练习结束时 | 3.4.2, 3.4.3 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 学生基本信息 | 用户中心服务提供，通过gRPC同步调用获取 | 2.3, 3.3.2 |
| 题目内容和元数据 | 内容平台服务提供，通过gRPC同步调用获取 | 2.3, 3.3.2 |
| 课程配置信息 | 教师端服务提供，通过gRPC同步调用获取 | 2.3, 3.3.2 |
| 学生作答输入 | 学生客户端提交，通过HTTP REST API接收 | 3.4.2 |
| 推题策略参数 | 配置中心(Nacos)提供，支持动态配置更新 | 4.5 |
| 掌握度计算参数 | 配置中心(Nacos)提供，支持运营调整 | 4.5 |

---

## 7. 当前版本明确排除的数据需求 (例如PRD中的V1.0版本)

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 (源自PRD"非本期功能"等章节) | PRD中说明的排除原因 (若有) | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 详细的数据库表结构和字段设计 | 仅涉及概念数据模型，具体实现由DBA负责 | 1.5 |
| 具体第三方服务集成协议细节 | 超出架构设计范围 | 1.5 |
| 数据库性能调优和备份恢复策略 | DBA专项工作 | 1.5 |
| 具体的安全攻防策略和审计机制实现 | 超出当前架构设计范围 | 1.5 |
| DevOps的CI/CD流程和基础设施配置 | 运维专项工作 | 1.5 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别练习会话、作答记录、掌握度记录、推题策略、学习报告等核心实体 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏（例如，本示例中的"题目信息"、"课程信息"）？                                                         | ⚠️ | 学生、题目、课程、知识点等实体在公共服务中已定义，仅保留关联关系 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已提取所有核心属性，包括状态、时间戳、计算输入等 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | 属性定义与PRD中的领域对象描述保持一致 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确定义实体间的关联关系和基数 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 掌握度计算、推题策略等依赖关系已体现 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 已列出掌握度计算、推题策略、报告生成等核心计算的输入项 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 已记录实时更新、异步更新等不同更新机制 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 已列出架构设计范围外的数据相关工作 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 使用PRD中定义的领域术语 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 所有提取项都标注了对应的PRD章节号 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 所有信息均来源于PRD中的架构设计描述 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ✅ | 已在备注中说明公共服务实体的处理方式 |

--- 
---
description: 
globs: 
alwaysApply: false
---
# DDD 架构与 AI 约束规则

## AI行为约束规则

### 基本原则
1. **最小生成原则**
   - 仅生成接口定义要求的代码
   - 禁止生成额外的工具函数或辅助代码
   - 禁止修改已定义的数据结构和接口

2. **规范遵循原则**
   - 严格遵循项目定义的各层规范
   - 禁止引入未在项目中定义的依赖
   - 禁止更改项目的配置和结构

3. **安全性原则**
   - 禁止生成可能导致安全问题的代码
   - 禁止在日志中包含敏感信息
   - 禁止使用不安全的数据库操作

### 分层约束规则

#### DAO层约束
- 仅实现Repository接口定义的方法
- 禁止跨库操作和直接SQL拼接
- 禁止自定义缓存策略
- 错误必须转换为预定义类型

#### Service层约束
- 仅实现用例接口定义的方法
- 禁止直接访问数据库
- 禁止实现非业务逻辑的功能
- 事务必须在Service层控制

#### Controller层约束
- 仅实现API接口定义的方法
- 禁止包含业务逻辑
- 入参校验必须使用统一验证器
- 响应格式必须统一

### 代码生成限制

#### 禁止生成的代码类型
1. 工具类或辅助函数
2. 中间件代码
3. 配置管理代码
4. 自定义验证器
5. 数据库迁移代码
6. 缓存管理器
7. 连接池管理
8. 性能监控代码

#### 依赖注入限制
- 只允许注入配置文件中明确定义的依赖
- 禁止创建全局变量和单例
- 禁止直接初始化外部服务
- 所有依赖必须通过构造函数注入

#### 数据库操作限制
- 禁止直接拼接SQL语句
- 禁止使用原生SQL查询（除非有特殊性能要求）
- 禁止修改数据库连接配置
- 必须使用预编译语句

#### 缓存操作限制
- 只允许缓存配置文件中定义的数据
- 禁止自行决定缓存策略
- 禁止实现分布式锁
- 缓存key必须遵循统一前缀规范

### 测试代码约束
- 只允许使用指定的测试框架
- 禁止在测试中连接真实数据库
- 必须使用mock数据
- 禁止更改测试配置

### 错误处理约束
- 只允许使用预定义的错误类型
- 禁止自定义新的错误码
- 必须在对应层处理相应的错误
- 禁止在错误信息中包含敏感数据

### 日志记录约束
- 只允许使用项目定义的日志接口
- 禁止直接打印到标准输出
- 禁止记录敏感信息
- 日志级别必须符合规范要求

### 性能相关约束
- 禁止自行添加索引
- 禁止修改数据库配置参数
- 批量操作必须使用预定义的批次大小
- 禁止更改连接池设置

### 代码模板规范
每个层的具体代码模板和实现示例请参考对应的规范文档：
- DAO层：`project-rules/backend-golang/ddd/dao.mdc`
- Service层：`project-rules/backend-golang/ddd/service.mdc`
- Controller层：`project-rules/backend-golang/ddd/controller.mdc`

## DDD分层架构编码规则

### DAO层规范
- **职责**：实现数据访问接口 (Repository)
- **位置**：`app/{module}/dao/impl/`
- **文件组织**：
  - `{entity}_impl.go`: DAO接口实现
  - `{entity}_impl_test.go`: DAO单元测试
- **规范要求**：
  1. 实现Domain层定义的Repository接口
  2. 负责领域模型与数据模型 (DO/PO) 的转换
  3. 封装数据库操作细节 (GORM等)
  4. 使用预定义的数据库错误类型
  5. 包含基础的CRUD和特定查询方法
  6. 必须有完整的单元测试 (Mock DB)
  7. 禁止包含业务逻辑
  8. 具体规范参考 `project-rules/backend-golang/ddd/dao.mdc`

### Domain层规范
- **职责**：定义核心业务模型和接口
- **位置**：`app/{module}/domain/`
- **文件组织**：
  - `struct.go`: 领域实体定义
  - `interface.go`: Repository接口定义
  - `errors.go`: 领域错误定义
  - `const.go`: 领域常量定义
- **规范要求**：
  1. 实体必须是充血模型，包含业务行为
  2. 所有字段必须有明确的业务含义
  3. 不允许引入基础设施依赖
  4. 错误必须定义为领域错误
  5. Repository接口设计必须遵循CQRS原则
  6. 注释必须包含业务规则说明
  7. 具体规范参考 `project-rules/backend-golang/ddd/domain.mdc`

### Service层规范
- **职责**：实现业务用例和编排
- **位置**：`app/{module}/service/`
- **文件组织**：
  - `{usecase}_service.go`: 具体用例实现
  - `{usecase}_service_test.go`: 用例测试
- **规范要求**：
  1. 仅依赖Domain层接口和DAO层接口 (通过注入)
  2. 必须包含完整的业务逻辑
  3. 事务控制必须在Service层
  4. 必须处理所有异常场景 (捕获DomainError, DAOError)
  5. 必须包含详细的日志记录
  6. 必须有完整的单元测试 (Mock Repository)
  7. 禁止直接操作数据库或进行数据模型转换
  8. 具体规范参考 `project-rules/backend-golang/ddd/service.mdc`

### DTO层规范
- **职责**：定义数据传输对象
- **位置**：`app/{module}/dto/`
- **文件组织**：
  - `request.go`: 请求DTO定义
  - `response.go`: 响应DTO定义
  - `converter.go`: 转换器定义 (Domain <-> DTO)
- **规范要求**：
  1. 字段名必须使用驼峰命名
  2. 必须包含字段验证标签 (如 `binding`)
  3. JSON标签必须是小写驼峰
  4. 时间字段必须是UTC秒数 (int64)
  5. 必须提供与Domain实体的转换方法 (通常在Service层调用)
  6. 注释必须包含字段说明

### Controller层规范
- **职责**：处理HTTP请求和响应
- **位置**：`app/{module}/controller/`
- **文件组织**：
  - `{resource}_controller.go`: 控制器实现
  - `{resource}_controller_test.go`: 控制器测试
- **规范要求**：
  1. 只负责请求绑定、参数验证、调用Service、响应封装
  2. 必须进行参数验证 (使用DTO的验证标签或方法)
  3. 错误必须转换为HTTP错误 (基于Service返回的错误)
  4. 必须遵循RESTful规范
  5. 必须有Swagger文档注解
  6. 必须处理请求上下文 (TraceID, UserID等)
  7. 禁止包含业务逻辑或直接调用DAO
  8. 具体规范参考 `project-rules/backend-golang/ddd/controller.mdc`

### Router层规范
- **职责**：定义路由规则和中间件
- **位置**：`app/{module}/router/`
- **文件组织**：
  - `router.go`: 路由注册
  - `middleware.go`: 中间件定义
- **规范要求**：
  1. 路由必须版本化 (如 `/v1`)
  2. 必须包含认证、授权中间件
  3. 必须包含请求日志、TraceID中间件
  4. 必须处理CORS
  5. 可选包含限流措施
  6. 必须处理panic恢复

### 跨层规范
1. **依赖规则**：
   - Router -> Controller -> Service -> Domain/DAO
   - 外层可以依赖内层
   - 内层不能依赖外层
   - 依赖必须通过接口注入 (Wire)

2. **错误处理**：
   - DAO层：定义/转换数据库错误 -> DAO错误
   - Domain层：定义业务错误
   - Service层：处理DAO错误和Domain错误 -> Service错误
   - Controller层：处理Service错误 -> HTTP错误

3. **日志记录**：
   - DAO层：数据库操作日志 (DEBUG/WARN)
   - Domain层：仅核心业务变化日志 (INFO)
   - Service层：完整业务流程日志 (INFO/ERROR)
   - Controller层：请求入口/出口日志 (INFO)

4. **数据转换**：
   - DO/PO <-> Domain：在DAO层转换
   - Domain <-> DTO：在Service层调用DTO转换器进行转换

5. **验证规则**：
   - DTO层/Controller层：请求参数格式验证
   - Service层：业务逻辑验证
   - Domain层：业务规则验证 (实体方法内)

6. **测试要求**：
   - DAO层：单元测试 (Mock DB)
   - Domain层：单元测试
   - Service层：单元测试 (Mock Repository) / 集成测试
   - Controller层：单元测试 (Mock Service) / API测试

### 性能优化规范
1. **DAO层性能优化**：
   - 合理使用索引
   - 批量操作优化
   - 分页查询优化
   - 连接池管理
   - 缓存策略实现

2. **Service层性能优化**：
   - 并发处理
   - 异步任务处理
   - 批量处理优化
   - 缓存预热策略
   - 限流熔断机制

3. **Controller层性能优化**：
   - 请求参数校验优化
   - 响应数据压缩
   - 静态资源缓存
   - 连接复用
   - 超时控制

### 监控规范
1. **基础监控指标**：
   - CPU使用率
   - 内存使用率
   - 磁盘IO
   - 网络IO
   - goroutine数量

2. **业务监控指标**：
   - QPS统计
   - 响应时间
   - 错误率
   - 业务成功率
   - 并发用户数

3. **日志监控**：
   - 错误日志监控
   - 慢查询日志
   - 业务异常日志
   - 安全审计日志
   - 操作日志

### 安全规范
1. **数据安全**：
   - 敏感数据加密
   - 数据脱敏处理
   - 访问权限控制
   - 数据备份策略
   - 数据销毁机制

2. **接口安全**：
   - 接口鉴权
   - 参数校验
   - SQL注入防护
   - XSS防护
   - CSRF防护

3. **传输安全**：
   - HTTPS加密
   - 数据签名
   - 时间戳校验
   - 防重放攻击
   - 加密算法选择

### 代码质量规范
1. **命名规范**：
   - 包名：全小写，简短有意义
   - 接口名：动词+名词，如`UserService`
   - 结构体名：名词，如`User`
   - 方法名：驼峰，动词开头，如`CreateUser`
   - 变量名：驼峰，名词，如`userID`

2. **注释规范**：
   - 包注释：包的用途、功能说明
   - 接口注释：接口的职责、用例
   - 方法注释：功能、参数、返回值
   - 变量注释：用途、取值范围
   - 实现注释：复杂逻辑说明

3. **代码组织**：
   - 文件大小控制：建议不超过500行
   - 函数大小：建议不超过50行
   - 参数数量：建议不超过5个
   - 代码嵌套：建议不超过4层
   - 包的大小：建议不超过20个文件

### 错误处理规范
1. **错误定义**：
   ```go
   // 错误码定义
   const (
       ErrCodeSuccess        = 0
       ErrCodeParamInvalid   = 400001
       ErrCodeUnauthorized   = 401001
       ErrCodeForbidden      = 403001
       ErrCodeNotFound       = 404001
       ErrCodeInternal       = 500001
   )

   // 错误信息定义
   var (
       ErrParamInvalid   = errors.New("参数无效")
       ErrUnauthorized   = errors.New("未授权")
       ErrForbidden      = errors.New("禁止访问")
       ErrNotFound       = errors.New("资源不存在")
       ErrInternal       = errors.New("内部错误")
   )
   ```

2. **错误处理流程**：
   ```go
   // Service层错误处理示例
   func (s *userService) CreateUser(ctx context.Context, req *dto.CreateUserReq) (*dto.CreateUserResp, error) {
       // 参数校验
       if err := req.Validate(); err != nil {
           return nil, errors.Wrap(ErrParamInvalid, err.Error())
       }

       // 业务处理
       user, err := s.userRepo.Create(ctx, req.ToEntity())
       if err != nil {
           // 特定错误处理
           if errors.Is(err, ErrDuplicateKey) {
               return nil, errors.Wrap(ErrParamInvalid, "用户已存在")
           }
           // 未知错误
           return nil, errors.Wrap(ErrInternal, "创建用户失败")
       }

       return dto.NewCreateUserResp(user), nil
   }
   ```

3. **错误日志规范**：
   ```go
   // 错误日志记录示例
   func logError(ctx context.Context, err error, msg string) {
       logger.WithContext(ctx).WithError(err).
           WithField("trace_id", trace.GetTraceID(ctx)).
           WithField("user_id", auth.GetUserID(ctx)).
           Error(msg)
   }
   ```

### 测试规范
1. **单元测试命名**：
   ```go
   // 正确的测试方法命名
   func TestUserService_CreateUser_Success(t *testing.T)
   func TestUserService_CreateUser_ParamInvalid(t *testing.T)
   func TestUserService_CreateUser_UserExists(t *testing.T)
   ```

2. **测试用例组织**：
   ```go
   func TestUserService_CreateUser(t *testing.T) {
       tests := []struct {
           name    string
           req     *dto.CreateUserReq
           mock    func(repo *mockRepo)
           wantErr error
       }{
           {
               name: "成功创建用户",
               req:  &dto.CreateUserReq{Username: "test"},
               mock: func(repo *mockRepo) {
                   repo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
               },
               wantErr: nil,
           },
           // ... 其他测试用例
       }

       for _, tt := range tests {
           t.Run(tt.name, func(t *testing.T) {
               // ... 测试逻辑
           })
       }
   }
   ```

3. **基准测试规范**：
   ```go
   func BenchmarkUserService_CreateUser(b *testing.B) {
       svc := setupTestService(b)
       req := &dto.CreateUserReq{Username: "test"}

       b.ResetTimer()
       for i := 0; i < b.N; i++ {
           _, _ = svc.CreateUser(context.Background(), req)
       }
   }
   ```

### DDD分层架构实现细节

#### DAO层实现规范

关于 DAO 层的具体实现职责、文件组织和规范要求，请参考上文 [DAO层规范](mdc:#dao层规范)。

**有关使用 GORM 实现 DAO 层的具体 Go 代码示例（包括数据模型定义、DAO 实现结构、Domain/DO转换、错误处理等），请参见 `project_rules/backend_golang/ddd/dao.mdc` 文件中的 `实现示例 (GORM)` 部分。**

#### Domain层实现规范
- **职责**：定义核心业务模型和接口
- **位置**：`app/{module}/domain/`
- **文件组织**：
  - `struct.go`: 领域实体定义
  - `interface.go`: Repository接口定义
  - `errors.go`: 领域错误定义
  - `const.go`: 领域常量定义
- **规范要求**：
  1. 实体必须是充血模型，包含业务行为
  2. 所有字段必须有明确的业务含义
  3. 不允许引入基础设施依赖
  4. 错误必须定义为领域错误
  5. Repository接口设计必须遵循CQRS原则
  6. 注释必须包含业务规则说明
  7. 具体规范参考 `project-rules/backend-golang/ddd/domain.mdc`

#### Service层实现规范
- **职责**：实现业务用例和编排
- **位置**：`app/{module}/service/`
- **文件组织**：
  - `{usecase}_service.go`: 具体用例实现
  - `{usecase}_service_test.go`: 用例测试
- **规范要求**：
  1. 仅依赖Domain层接口和DAO层接口 (通过注入)
  2. 必须包含完整的业务逻辑
  3. 事务控制必须在Service层
  4. 必须处理所有异常场景 (捕获DomainError, DAOError)
  5. 必须包含详细的日志记录
  6. 必须有完整的单元测试 (Mock Repository)
  7. 禁止直接操作数据库或进行数据模型转换
  8. 具体规范参考 `project-rules/backend-golang/ddd/service.mdc`

#### DTO层实现规范
- **职责**：定义数据传输对象
- **位置**：`app/{module}/dto/`
- **文件组织**：
  - `request.go`: 请求DTO定义
  - `response.go`: 响应DTO定义
  - `converter.go`: 转换器定义 (Domain <-> DTO)
- **规范要求**：
  1. 字段名必须使用驼峰命名
  2. 必须包含字段验证标签 (如 `binding`)
  3. JSON标签必须是小写驼峰
  4. 时间字段必须是UTC秒数 (int64)
  5. 必须提供与Domain实体的转换方法 (通常在Service层调用)
  6. 注释必须包含字段说明

#### Controller层实现规范
- **职责**：处理HTTP请求和响应
- **位置**：`app/{module}/controller/`
- **文件组织**：
  - `{resource}_controller.go`: 控制器实现
  - `{resource}_controller_test.go`: 控制器测试
- **规范要求**：
  1. 只负责请求绑定、参数验证、调用Service、响应封装
  2. 必须进行参数验证 (使用DTO的验证标签或方法)
  3. 错误必须转换为HTTP错误 (基于Service返回的错误)
  4. 必须遵循RESTful规范
  5. 必须有Swagger文档注解
  6. 必须处理请求上下文 (TraceID, UserID等)
  7. 禁止包含业务逻辑或直接调用DAO
  8. 具体规范参考 `project-rules/backend-golang/ddd/controller.mdc`

#### Router层实现规范
- **职责**：定义路由规则和中间件
- **位置**：`app/{module}/router/`
- **文件组织**：
  - `router.go`: 路由注册
  - `middleware.go`: 中间件定义
- **规范要求**：
  1. 路由必须版本化 (如 `/v1`)
  2. 必须包含认证、授权中间件
  3. 必须包含请求日志、TraceID中间件
  4. 必须处理CORS
  5. 可选包含限流措施
  6. 必须处理panic恢复

#### 跨层规范
1. **依赖规则**：
   - Router -> Controller -> Service -> Domain/DAO
   - 外层可以依赖内层
   - 内层不能依赖外层
   - 依赖必须通过接口注入 (Wire)

2. **错误处理**：
   - DAO层：定义/转换数据库错误 -> DAO错误
   - Domain层：定义业务错误
   - Service层：处理DAO错误和Domain错误 -> Service错误
   - Controller层：处理Service错误 -> HTTP错误

3. **日志记录**：
   - DAO层：数据库操作日志 (DEBUG/WARN)
   - Domain层：仅核心业务变化日志 (INFO)
   - Service层：完整业务流程日志 (INFO/ERROR)
   - Controller层：请求入口/出口日志 (INFO)

4. **数据转换**：
   - DO/PO <-> Domain：在DAO层转换
   - Domain <-> DTO：在Service层调用DTO转换器进行转换

5. **验证规则**：
   - DTO层/Controller层：请求参数格式验证
   - Service层：业务逻辑验证
   - Domain层：业务规则验证 (实体方法内)

6. **测试要求**：
   - DAO层：单元测试 (Mock DB)
   - Domain层：单元测试
   - Service层：单元测试 (Mock Repository) / 集成测试
   - Controller层：单元测试 (Mock Service) / API测试

#### 性能优化规范
1. **DAO层性能优化**：
   - 合理使用索引
   - 批量操作优化
   - 分页查询优化
   - 连接池管理
   - 缓存策略实现

2. **Service层性能优化**：
   - 并发处理
   - 异步任务处理
   - 批量处理优化
   - 缓存预热策略
   - 限流熔断机制

3. **Controller层性能优化**：
   - 请求参数校验优化
   - 响应数据压缩
   - 静态资源缓存
   - 连接复用
   - 超时控制

#### 监控规范
1. **基础监控指标**：
   - CPU使用率
   - 内存使用率
   - 磁盘IO
   - 网络IO
   - goroutine数量

2. **业务监控指标**：
   - QPS统计
   - 响应时间
   - 错误率
   - 业务成功率
   - 并发用户数

3. **日志监控**：
   - 错误日志监控
   - 慢查询日志
   - 业务异常日志
   - 安全审计日志
   - 操作日志

#### 安全规范
1. **数据安全**：
   - 敏感数据加密
   - 数据脱敏处理
   - 访问权限控制
   - 数据备份策略
   - 数据销毁机制

2. **接口安全**：
   - 接口鉴权
   - 参数校验
   - SQL注入防护
   - XSS防护
   - CSRF防护

3. **传输安全**：
   - HTTPS加密
   - 数据签名
   - 时间戳校验
   - 防重放攻击
   - 加密算法选择

#### 代码质量规范
1. **命名规范**：
   - 包名：全小写，简短有意义
   - 接口名：动词+名词，如`UserService`
   - 结构体名：名词，如`User`
   - 方法名：驼峰，动词开头，如`CreateUser`
   - 变量名：驼峰，名词，如`userID`

2. **注释规范**：
   - 包注释：包的用途、功能说明
   - 接口注释：接口的职责、用例
   - 方法注释：功能、参数、返回值
   - 变量注释：用途、取值范围
   - 实现注释：复杂逻辑说明

3. **代码组织**：
   - 文件大小控制：建议不超过500行
   - 函数大小：建议不超过50行
   - 参数数量：建议不超过5个
   - 代码嵌套：建议不超过4层
   - 包的大小：建议不超过20个文件

#### 错误处理规范
1. **错误定义**：
   ```go
   // 错误码定义
   const (
       ErrCodeSuccess        = 0
       ErrCodeParamInvalid   = 400001
       ErrCodeUnauthorized   = 401001
       ErrCodeForbidden      = 403001
       ErrCodeNotFound       = 404001
       ErrCodeInternal       = 500001
   )

   // 错误信息定义
   var (
       ErrParamInvalid   = errors.New("参数无效")
       ErrUnauthorized   = errors.New("未授权")
       ErrForbidden      = errors.New("禁止访问")
       ErrNotFound       = errors.New("资源不存在")
       ErrInternal       = errors.New("内部错误")
   )
   ```

2. **错误处理流程**：
   ```go
   // Service层错误处理示例
   func (s *userService) CreateUser(ctx context.Context, req *dto.CreateUserReq) (*dto.CreateUserResp, error) {
       // 参数校验
       if err := req.Validate(); err != nil {
           return nil, errors.Wrap(ErrParamInvalid, err.Error())
       }

       // 业务处理
       user, err := s.userRepo.Create(ctx, req.ToEntity())
       if err != nil {
           // 特定错误处理
           if errors.Is(err, ErrDuplicateKey) {
               return nil, errors.Wrap(ErrParamInvalid, "用户已存在")
           }
           // 未知错误
           return nil, errors.Wrap(ErrInternal, "创建用户失败")
       }

       return dto.NewCreateUserResp(user), nil
   }
   ```

3. **错误日志规范**：
   ```go
   // 错误日志记录示例
   func logError(ctx context.Context, err error, msg string) {
       logger.WithContext(ctx).WithError(err).
           WithField("trace_id", trace.GetTraceID(ctx)).
           WithField("user_id", auth.GetUserID(ctx)).
           Error(msg)
   }
   ```

#### 测试规范
1. **单元测试命名**：
   ```go
   // 正确的测试方法命名
   func TestUserService_CreateUser_Success(t *testing.T)
   func TestUserService_CreateUser_ParamInvalid(t *testing.T)
   func TestUserService_CreateUser_UserExists(t *testing.T)
   ```

2. **测试用例组织**：
   ```go
   func TestUserService_CreateUser(t *testing.T) {
       tests := []struct {
           name    string
           req     *dto.CreateUserReq
           mock    func(repo *mockRepo)
           wantErr error
       }{
           {
               name: "成功创建用户",
               req:  &dto.CreateUserReq{Username: "test"},
               mock: func(repo *mockRepo) {
                   repo.EXPECT().Create(gomock.Any(), gomock.Any()).Return(nil)
               },
               wantErr: nil,
           },
           // ... 其他测试用例
       }

       for _, tt := range tests {
           t.Run(tt.name, func(t *testing.T) {
               // ... 测试逻辑
           })
       }
   }
   ```

3. **基准测试规范**：
   ```go
   func BenchmarkUserService_CreateUser(b *testing.B) {
       svc := setupTestService(b)
       req := &dto.CreateUserReq{Username: "test"}

       b.ResetTimer()
       for i := 0; i < b.N; i++ {
           _, _ = svc.CreateUser(context.Background(), req)
       }
   }
   ```

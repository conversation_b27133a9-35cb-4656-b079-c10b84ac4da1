@echo off
echo 启动测试环境...
echo ==========================================

REM 设置测试环境变量
set NODE_ENV=test
set NEXT_PUBLIC_API_HOST=http://test-api.xiaoluxue.com
set NEXT_PUBLIC_API_PATH=/api
set NEXT_PUBLIC_USE_REMOTE_API=true
set PORT=3001

echo 测试环境配置：
echo - NODE_ENV: test
echo - API_HOST: http://test-api.xiaoluxue.com
echo - PORT: 3001
echo ==========================================

REM 清理缓存
if exist ".next" (
    echo 清理 .next 目录...
    rmdir /s /q ".next" 2>nul
)

echo 启动测试环境服务器...
npm run dev
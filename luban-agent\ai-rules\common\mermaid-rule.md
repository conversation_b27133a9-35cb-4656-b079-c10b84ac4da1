# 本文档为 mermaid 图常见错误处理及规范说明

1. mermaid 语法不支持英文括号"()"、双引号和 html 标签，代替规则：
    1. 英文括号"()"请用全角括号"（）"代替
    2. 双引号(")请用单引号(')代替
    3. html 标签只保留`<br>`和`<br/>`，其他 html 标签不得保留，直接去除。
2. mermaid 不能有多个空行，否则会识别错误，每生成完成一个mermaid图时，需要检查是否包含多个空行。
3. 注释规范: 统一使用 %%（严禁使用 "\#"，""，"//" 等无法识别的符号），所有非显而易见的节点和复杂逻辑都需要添加，用于解释这么做的原因，避免在简单连接的行尾添加不必要的注释。
4. 示例：
    1. stateDiagram-v2 节点定义推荐: `state "用户输入的目标" as UserInput` (而非 UserInput{描述})
    2. stateDiagram-v2 转换标签推荐: `CS_Course --> foreach_course_fork : 转换描述`
    3. 错误使用英文括号
        1. 错误：`subgraph '教师端作业报告服务 (AssignmentReport Service)'`
        2. 正确：`subgraph '教师端作业报告服务（AssignmentReport Service）'`
    4. 所有注释（%%）必须独立成行，严禁行尾注释
        1. 错误：`ReportSvc -- RPC/HTTP & Kafka Consumer --> AssignmentSvc; %% 假设混合模式`
        2. 正确：
        ```mermaid
        ReportSvc -- RPC/HTTP & Kafka Consumer --> AssignmentSvc;
        %% 假设混合模式
        ```
    5. 双引号(")错误使用，导致不可渲染
        1. 错误：`Client["教师客户端 (Frontend)"] -- HTTP/gRPC --> ReportSvc["ReportService (作业报告服务)"];`
        2. 正确：`Client['教师客户端 (Frontend)'] -- HTTP/gRPC --> ReportSvc['ReportService (作业报告服务)'];`
    6. 单引号多余
        1. 错误：
        ```mermaid
        flowchart TD A['Client: 教师已进入指定作业报告详情页（学生报告Tab）']
        ```
        2. 正确：
        ```mermaid
        flowchart TD A[Client: 教师已进入指定作业报告详情页（学生报告Tab）]
        ```
    7. 子图(subgraph)名称不支持中英文标点符号，如括号、逗号、句号等，但支持空格、单连接符（双连接符不支持）
        1. 错误：`subgraph '教师端作业报告服务（AssignmentReport Service）'`
        2. 正确：`subgraph '教师端作业报告服务 AssignmentReport Service'`
        3. 错误：`subgraph '教师端作业报告服务--AssignmentReport Service'`
        4. 正确：`subgraph '教师端作业报告服务-AssignmentReport Service'`
        5. 错误：`subgraph '教师端作业报告服务(AssignmentReport Service)'`
import MainLayout from '@/components/layout/MainLayout';

export default function Loading() {
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* 标题骨架屏 */}
          <div className="mb-6">
            <div className="h-8 bg-gray-200 rounded-md w-3/4 animate-pulse"></div>
            <div className="flex items-center mt-2">
              <div className="h-4 bg-gray-200 rounded-md w-1/4 animate-pulse mr-4"></div>
              <div className="h-4 bg-gray-200 rounded-md w-1/3 animate-pulse"></div>
            </div>
          </div>
          
          {/* 处理进度骨架屏 */}
          <div className="bg-white shadow rounded-lg p-6 mb-8">
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <div className="h-6 bg-gray-200 rounded-md w-1/4 animate-pulse"></div>
                <div className="h-5 bg-gray-200 rounded-md w-16 animate-pulse"></div>
              </div>
              <div className="h-2 bg-gray-200 rounded-full w-full animate-pulse"></div>
            </div>
            
            {/* 处理阶段骨架屏 */}
            <div className="space-y-4">
              {[...Array(4)].map((_, index) => (
                <div key={index} className="flex items-start p-3 rounded-md bg-gray-50">
                  <div className="flex-shrink-0 w-6 h-6 rounded-full bg-gray-300 mr-3"></div>
                  <div className="w-full">
                    <div className="h-5 bg-gray-200 rounded-md w-1/4 animate-pulse mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded-md w-3/4 animate-pulse"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* 加载动画 */}
          <div className="flex justify-center items-center py-8">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
            <p className="ml-2 text-gray-500">加载中...</p>
          </div>
        </div>
      </div>
    </MainLayout>
  );
} 
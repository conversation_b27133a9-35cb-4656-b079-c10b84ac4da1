好的，架构师。我将严格按照您的指示，为“教师端_1期_作业报告”这个项目梳理出详细的前端业务架构设计和逻辑描述。我们的目标是让实习生能够专注于纯函数和纯UI组件的实现，避免任何复杂的业务逻辑组织。

以下是针对产品说明文档中各个模块的详细设计：

## 一、整体架构思路

我们将采用分层架构：Model层负责数据获取与处理，Presenter层负责业务逻辑与状态管理，View层负责UI展示与用户交互。重点利用 `@preact-signals/safe-react`进行状态管理，SWR进行数据获取，并通过Context API处理跨层级或复杂组件间的状态共享和逻辑分发，确保View层组件的纯粹性。

**全局状态与Context设计：**

考虑到不同报告模块间可能存在共享的上下文信息（如当前选中的班级、作业ID等），以及为了更清晰地管理权限和用户角色相关的数据，我们会设计一个顶层的 `AppShellContext`。

```typescript
// src/contexts/AppShellContext.ts
import { signal, Signal } from "@preact/signals-react";
import { createContext, useContext } from "react";

// 假设的用户角色和权限定义
export type UserRole = "学科老师" | "班主任" | "学科主任" | "年级主任" | "校长";
export interface UserPermissions {
  canViewAllSubjects: boolean;
  canViewAllGrades: boolean;
  canEditTasks: (taskCreatorId: string) => boolean;
  canDeleteTasks: (taskCreatorId: string) => boolean;
  canCopyTasks: (taskCreatorId: string) => boolean;
  // ... 其他权限
}

export interface AppShellContextState {
  currentUser: Signal<{ id: string; name: string; role: UserRole } | null>;
  permissions: Signal<UserPermissions | null>;
  // 全局筛选条件或上下文，例如当前学校ID等
  // currentSchoolId: Signal<string | null>;
}

export interface AppShellContextActions {
  // 初始化或更新用户及权限的方法
  // loadUserAndPermissions: () => Promise<void>;
}

export type AppShellContextType = AppShellContextState & AppShellContextActions;

// 默认值可以根据实际情况调整
const defaultState: AppShellContextState = {
  currentUser: signal(null),
  permissions: signal(null),
};

const defaultActions: AppShellContextActions = {
  // loadUserAndPermissions: async () => {},
};

export const AppShellContext = createContext<AppShellContextType>({ ...defaultState, ...defaultActions });

export const useAppShell = () => {
  const context = useContext(AppShellContext);
  if (!context) {
    throw new Error("useAppShell must be used within an AppShellProvider");
  }
  return context;
};

// AppShellProvider 组件将在应用根部或布局组件中使用
// 实习生不需要实现 Provider，会由架构师或资深同事提供
// Provider 内部会包含 currentUser 和 permissions 的信号初始化和更新逻辑
// 例如:
// const AppShellProvider = ({ children }) => {
//   const currentUser = signal<{ id: string; name: string; role: UserRole } | null>(null);
//   const permissions = signal<UserPermissions | null>(null);
//
//   const loadUserAndPermissions = async () => {
//     // 伪代码: 调用API获取用户信息和权限
//     // const userData = await api.fetchCurrentUser();
//     // const userPermissions = await api.fetchPermissions(userData.role);
//     // currentUser.value = userData;
//     // permissions.value = userPermissions;
//   };
//
//   useEffect(() => {
//     loadUserAndPermissions();
//   }, []);
//
//   return (
//     <AppShellContext.Provider value={{ currentUser, permissions, loadUserAndPermissions }}>
//       {children}
//     </AppShellContext.Provider>
//   );
// };
```

## 二、功能模块详细设计

### 模块1：作业列表页

#### 1. 功能范围

* **核心功能**:
    * **作业列表展示**: 以卡片形式展示当前教师权限范围内的所有作业（包括课程任务、作业任务/测验任务、资源任务）。卡片上需展示作业核心信息和关键指标。
    * **数据范围确定**: 根据教师职务（学科老师、班主任、学科主任、年级主任、校长）动态确定可查看的作业数据范围。
    * **筛选功能**: 提供按“全部类型”（内容类型）、“查看班级”、“学科”、“布置日期”等维度进行筛选。
    * **搜索功能**: 支持按作业名称关键词进行模糊搜索。
* **辅助功能**:
    * **卡片操作**: 对于本人布置的任务，提供“编辑任务”、“删除任务”、“复制任务”的操作入口（通常通过更多操作菜单）。
    * **状态标识**: 明确标识作业的“到期”状态等。
    * **指标展示**: 不同类型的任务卡片展示不同的核心指标（例如课程任务：平均进度、课时数；作业/测验任务：完成率、正确率、待关注数；资源任务：相关指标）。
* **交互方式**:
    * 用户通过顶部的Tabs切换内容类型（课程、作业、资源、测验）。
    * 用户通过下拉菜单或日期选择器选择筛选条件。
    * 用户在搜索框输入关键词进行搜索。
    * 用户点击作业卡片进入作业报告详情页。
    * 用户点击卡片上的“更多”操作按钮，显示操作菜单。

#### 2. 用户场景

* **场景1: 快速概览 (学科老师)**
    * 作为一名数学学科老师，李老师登录系统后进入“作业”列表页。
    * 他希望默认看到他所教的高一(3)班的数学作业。
    * 他能快速浏览每个作业的完成率、正确率和待关注题目数。
    * 他看到一个作业“3.2.2函数的最值”已经“到期”，点击该卡片，跳转到该作业的报告详情页。
* **场景2: 按条件筛选 (班主任)**
    * 作为高一(3)班的班主任王老师，她进入“作业”列表。
    * 她想查看本班“最近一周”布置的所有学科的作业。
    * 她首先选择“高一(3)班”，然后通过“布置日期”筛选器选择“最近一周”。
    * 列表动态更新，展示了符合条件的语文、数学、英语等学科的作业卡片。
* **场景3: 搜索特定作业 (学科老师)**
    * 李老师记得自己布置过一个关于“函数零点”的作业，但具体日期忘了。
    * 他在搜索框中输入“函数零点”。
    * 列表实时过滤，显示出标题包含“函数零点”的作业卡片。
* **场景4: 管理已布置作业 (学科老师)**
    * 李老师发现之前布置的一个作业“集合的基本运算”有些描述不准确。
    * 他在作业卡片上找到“...”操作按钮，点击后选择“编辑任务”，进入编辑页面。
    * 对于另一个他布置的已结束的测验，他想复制一份作为下个班级的模板，他点击“复制任务”。

#### 3. 分层设计

##### a. Model 层

* **数据定义 (`src/features/homework/list/types.ts`)**:
    ```typescript
    export type HomeworkType = "course" | "homework" | "quiz" | "resource";
    export type TaskStatus = "active" | "expired" | "pending"; // 根据实际状态定义

    export interface BaseHomeworkCardData {
      id: string;
      title: string;
      subject: string; // 如 "数学"
      grade: string; // 如 "高一"
      className: string; // 如 "高一3班"
      publishTime: string; // ISO Date String
      deadlineTime: string; // ISO Date String
      status: TaskStatus;
      isMyTask: boolean; // 是否为当前用户布置
      creatorId: string; // 布置者ID
    }

    export interface CourseTaskCardData extends BaseHomeworkCardData {
      type: "course";
      avgProgress: number; // 平均进度 0-100
      lessonCount: number; // 课时数
    }

    export interface HomeworkOrQuizTaskCardData extends BaseHomeworkCardData {
      type: "homework" | "quiz";
      completionRate: number; // 完成率 0-100
      accuracyRate: number; // 正确率 0-100
      attentionCount: number; // 待关注数 (如题目数)
      toBeMarkedCount?: number; // 待批阅数 (可选)
    }

    export interface ResourceTaskCardData extends BaseHomeworkCardData {
      type: "resource";
      // 资源任务相关指标，例如查看人数、平均学习时长等
      viewCount?: number;
      avgStudyTime?: number; // in minutes
    }

    export type HomeworkCardData = CourseTaskCardData | HomeworkOrQuizTaskCardData | ResourceTaskCardData;

    export interface HomeworkListFilters {
      contentType: HomeworkType | "all";
      classId?: string;
      subject?: string;
      dateRange?: { start: string; end: string }; // ISO Date Strings
      searchTerm?: string;
      page?: number;
      pageSize?: number;
    }

    export interface HomeworkListResponse {
      tasks: HomeworkCardData[];
      totalCount: number;
      currentPage: number;
    }

    // 筛选器选项数据结构
    export interface FilterOption {
      value: string;
      label: string;
    }
    export interface ClassFilterOption extends FilterOption {
      grade: string;
    }
    ```

* **接口数据获取 (`src/features/homework/list/services.ts`)**:
    * `WorkspaceHomeworkList(filters: Signal<HomeworkListFilters>): Promise<HomeworkListResponse>`: 使用SWR。实习生无需关心SWR具体实现，只需知道有此hook可用。
        ```typescript
        import useSWR from 'swr';
        import { Signal } from "@preact/signals-react";
        import { HomeworkListFilters, HomeworkListResponse, FilterOption, ClassFilterOption } from './types';

        // 假设的API端点
        const API_HOMEWORK_LIST = "/api/teacher/homeworks";
        const API_FILTER_OPTIONS_CLASSES = "/api/teacher/filter-options/classes";
        const API_FILTER_OPTIONS_SUBJECTS = "/api/teacher/filter-options/subjects";

        // 封装的 fetcher 函数
        const genericFetcher = async <T>(url: string): Promise<T> => {
          const res = await axios.get(url);
          return res.data;
        };

        // 获取作业列表的SWR Hook
        // 实习生仅需了解此函数签名和用途，Presenter层会调用它
        export const useHomeworkList = (filtersSignal: Signal<HomeworkListFilters>) => {
          // SWR key 会依赖 filtersSignal.value 的变化而变化，从而自动重新请求
          const getKey = () => {
            const filters = filtersSignal.value;
            const params = new URLSearchParams();
            if (filters.contentType && filters.contentType !== "all") params.append("contentType", filters.contentType);
            if (filters.classId) params.append("classId", filters.classId);
            if (filters.subject) params.append("subject", filters.subject);
            if (filters.dateRange?.start) params.append("dateRangeStart", filters.dateRange.start);
            if (filters.dateRange?.end) params.append("dateRangeEnd", filters.dateRange.end);
            if (filters.searchTerm) params.append("searchTerm", filters.searchTerm);
            if (filters.page) params.append("page", filters.page.toString());
            if (filters.pageSize) params.append("pageSize", filters.pageSize.toString());
            return `${API_HOMEWORK_LIST}?${params.toString()}`;
          };

          const { data, error, isLoading, mutate } = useSWR<HomeworkListResponse>(getKey, genericFetcher);
          return {
            data, // HomeworkListResponse | undefined
            error,
            isLoading,
            mutateHomeworkList: mutate, // 用于手动触发刷新或更新本地数据
          };
        };

        // 获取班级筛选选项
        export const useClassFilterOptions = () => {
          const { data, error, isLoading } = useSWR<ClassFilterOption[]>(API_FILTER_OPTIONS_CLASSES, genericFetcher);
          return { classOptions: data, classOptionsError: error, classOptionsLoading: isLoading };
        };

        // 获取学科筛选选项
        export const useSubjectFilterOptions = () => {
          const { data, error, isLoading } = useSWR<FilterOption[]>(API_FILTER_OPTIONS_SUBJECTS, genericFetcher);
          return { subjectOptions: data, subjectOptionsError: error, subjectOptionsLoading: isLoading };
        };

        // 删除作业的API调用 (示例)
        export const deleteTaskApi = async (taskId: string): Promise<void> => {
          await axios.delete(`/api/teacher/homeworks/${taskId}`);
        };
        // 编辑、复制作业的API调用类似
        ```

* **接口数据转换 (`src/features/homework/list/transformers.ts`)**:
    * `formatDateForDisplay(isoDate: string): string`: (纯函数) 将ISO日期字符串格式化为"YYYY-MM-DD HH:mm"。
    * `getTaskStatusText(status: TaskStatus): string`: (纯函数) 将状态枚举转换为显示文本。
    * 实习生需要实现这些纯函数。

##### b. Presenter 层 (`src/features/homework/list/HomeworkListPresenter.ts` 或作为自定义Hook `useHomeworkListPresenter`)

* **核心职责**:
    * 管理筛选条件 (`filtersSignal: Signal<HomeworkListFilters>`)。
    * 管理当前页码、分页大小。
    * 调用 `useHomeworkList` 获取作业数据。
    * 处理用户交互：更新筛选条件、执行搜索、处理分页、处理卡片操作（编辑、删除、复制）的逻辑分发。
    * 根据 `AppShellContext` 中的用户角色和权限，控制卡片操作的可见性/可用性。
* **状态变量 (Signals)**:
    ```typescript
    // import { signal, computed } from "@preact/signals-react";
    // import { HomeworkListFilters, HomeworkCardData, HomeworkType, FilterOption, ClassFilterOption } from './types';
    // import { useAppShell } from '@/contexts/AppShellContext';
    // import { useHomeworkList, useClassFilterOptions, useSubjectFilterOptions, deleteTaskApi } from './services';
    // import { showToast } from '@repo/ui/sonner'; // 假设的Toast组件

    // 内部状态由 Presenter 管理
    const filters = signal<HomeworkListFilters>({
      contentType: "all",
      page: 1,
      pageSize: 10, // 默认每页10条
    });
    const selectedContentType = signal<HomeworkType | "all">("all");
    const selectedClassId = signal<string | undefined>(undefined);
    const selectedSubject = signal<string | undefined>(undefined);
    const selectedDateRange = signal<{ start: string; end: string } | undefined>(undefined);
    const searchTerm = signal<string>("");

    // 从 SWR hooks 获取数据
    // const { data: homeworkListData, error: homeworkListError, isLoading: homeworkListLoading, mutateHomeworkList } = useHomeworkList(filters);
    // const { classOptions, classOptionsLoading } = useClassFilterOptions();
    // const { subjectOptions, subjectOptionsLoading } = useSubjectFilterOptions();
    // const { currentUser, permissions } = useAppShell(); // 获取全局用户和权限

    // 计算属性 (Computed Signals)
    const displayedTasks = computed(() => {
      return homeworkListData.value?.tasks.map(task => ({
        ...task,
        publishTime: formatDateForDisplay(task.publishTime), // 调用纯函数转换
        deadlineTime: formatDateForDisplay(task.deadlineTime),
        statusText: getTaskStatusText(task.status),
      })) || [];
    });
    const totalPages = computed(() => {
      if (!homeworkListData.value || !homeworkListData.value.totalCount) return 1;
      return Math.ceil(homeworkListData.value.totalCount / filters.value.pageSize!);
    });
    ```
* **核心函数 (由Presenter暴露给View层或内部调用)**:
    ```typescript
    // 更新筛选条件的纯函数逻辑（可以放在Presenter内部或utils）
    const updateFilters = (newFilterPart: Partial<HomeworkListFilters>) => {
        filters.value = { ...filters.value, ...newFilterPart, page: 1 }; // 筛选变化时重置到第一页
    };

    // Presenter 暴露的函数
    const handleContentTypeChange = (type: HomeworkType | "all") => {
        selectedContentType.value = type;
        updateFilters({ contentType: type });
    };
    const handleClassChange = (classId?: string) => {
        selectedClassId.value = classId;
        updateFilters({ classId });
    };
    const handleSubjectChange = (subject?: string) => {
        selectedSubject.value = subject;
        updateFilters({ subject });
    };
    const handleDateRangeChange = (range?: { start: string; end: string }) => {
        selectedDateRange.value = range;
        updateFilters({ dateRange: range });
    };
    const handleSearchTermChange = (term: string) => { // 通常由输入框 onChange 触发
        searchTerm.value = term;
    };
    const handleSearchSubmit = () => { // 通常由搜索按钮点击或回车触发
        updateFilters({ searchTerm: searchTerm.value });
    };
    const handleClearSearch = () => {
        searchTerm.value = "";
        updateFilters({ searchTerm: "" });
    };
    const handlePageChange = (newPage: number) => {
        filters.value = { ...filters.value, page: newPage };
    };

    // 卡片操作相关
    const canEditTask = (taskCreatorId: string): boolean => {
      return permissions.value?.canEditTasks(taskCreatorId) ?? false;
    };
    const canDeleteTask = (taskCreatorId: string): boolean => {
      return permissions.value?.canDeleteTasks(taskCreatorId) ?? false;
    };
    const canCopyTask = (taskCreatorId: string): boolean => {
      return permissions.value?.canCopyTasks(taskCreatorId) ?? false;
    };

    const handleEditTask = (taskId: string) => {
      // router.push(`/homework/edit/${taskId}`); // 使用Next.js router
      console.log(`Navigating to edit task ${taskId}`);
    };
    const handleDeleteTask = async (taskId: string) => {
      try {
        await deleteTaskApi(taskId); // 调用 Model 层服务
        // showToast.success("作业删除成功");
        mutateHomeworkList(); // 刷新列表
      } catch (e) {
        // showToast.error("作业删除失败");
        console.error("Failed to delete task", e);
      }
    };
    const handleCopyTask = (taskId: string) => {
      console.log(`Copying task ${taskId}`);
      // 实现复制逻辑，可能跳转到新建页面并预填数据
    };
    const handleViewTaskReport = (taskId: string) => {
        // router.push(`/homework/report/${taskId}`);
        console.log(`Navigating to report for task ${taskId}`);
    };
    ```
    **Presenter构造函数/自定义Hook返回值**:
    实习生将从Presenter获取以下内容注入到View层组件：
    `{ displayedTasks, totalPages, currentPage: filters.peek().page, homeworkListLoading, homeworkListError, classOptions, classOptionsLoading, subjectOptions, subjectOptionsLoading, selectedContentType, selectedClassId, selectedSubject, selectedDateRange, searchTerm, handleContentTypeChange, handleClassChange, handleSubjectChange, handleDateRangeChange, handleSearchTermChange, handleSearchSubmit, handleClearSearch, handlePageChange, canEditTask, canDeleteTask, canCopyTask, handleEditTask, handleDeleteTask, handleCopyTask, handleViewTaskReport }`

##### c. View 层

* **组件树结构 (`graph TD`):**
    ```mermaid
    graph TD
        A[HomeworkListPage] --> B(PageLayout);
        B --> C[HomeworkListHeader];
        C --> C1[ContentTypeTabs_UI];
        C --> C2[SearchBar_UI];
        C --> C3[FilterDropdown_Class_UI];
        C --> C4[FilterDropdown_Subject_UI];
        C --> C5[DateRangePicker_UI];
        B --> D[HomeworkCardList_UI];
        D --> E[HomeworkCard_UI];
        E --> F[CardMenu_UI]; %% "更多"操作菜单
        B --> G[Pagination_UI];
        B --> H[LoadingSpinner_UI];
        B --> I[ErrorDisplay_UI];
    ```
    (_UI后缀表示这些是纯UI组件，由实习生实现_)

* **组件职责**:
    * `HomeworkListPage`: 页面容器，初始化 `HomeworkListPresenter`，并将Presenter返回的数据和方法传递给子组件。
    * `PageLayout`: 通用页面布局组件。
    * `HomeworkListHeader`: 包含所有筛选和搜索控件的头部区域。
        * `ContentTypeTabs_UI`: (纯UI) 接收 `selectedContentType`, `contentTypes (固定或动态)`, `onContentTypeChange`。
        * `SearchBar_UI`: (纯UI) 接收 `searchTerm`, `onSearchTermChange`, `onSearchSubmit`, `onClearSearch`。
        * `FilterDropdown_Class_UI`: (纯UI) 接收 `classOptions`, `selectedClassId`, `onClassChange`, `isLoading`。
        * `FilterDropdown_Subject_UI`: (纯UI) 接收 `subjectOptions`, `selectedSubject`, `onSubjectChange`, `isLoading`。
        * `DateRangePicker_UI`: (纯UI) 接收 `selectedDateRange`, `onDateRangeChange`。
    * `HomeworkCardList_UI`: (纯UI) 接收 `tasks: HomeworkCardData[]` (由Presenter处理后的 `displayedTasks`), `onViewReport: (taskId) => void`, `onEdit: (taskId) => void`, `onDelete: (taskId) => void`, `onCopy: (taskId) => void`, `canEdit: (creatorId) => boolean`, `canDelete: (creatorId) => boolean`, `canCopy: (creatorId) => boolean`。负责遍历渲染 `HomeworkCard_UI`。
        * `HomeworkCard_UI`: (纯UI) 接收单个 `task: HomeworkCardData` 和上述操作函数及权限判断函数，展示卡片信息和操作菜单。
            * `CardMenu_UI`: (纯UI) 接收操作函数和权限判断结果，控制菜单项的显示和行为。
    * `Pagination_UI`: (纯UI) 接收 `currentPage`, `totalPages`, `onPageChange`。
    * `LoadingSpinner_UI`: (纯UI) 接收 `isLoading`。
    * `ErrorDisplay_UI`: (纯UI) 接收 `error`。

##### d. 状态管理与跨组件通信

* **全局状态**: 通过 `AppShellContext` 获取用户角色和权限，用于控制卡片操作的可用性。
* **局部状态**: 作业列表页的大部分状态（筛选条件、列表数据、加载状态）由 `HomeworkListPresenter` 内部的signals管理。
* **数据流**:
    1.  View (筛选组件) -> Presenter (调用 `handleFilterChange` 等方法更新 `filters` signal)。
    2.  Presenter (`filters` signal 变化) -> Model (`useHomeworkList` SWR hook自动重新获取数据)。
    3.  Model (数据返回) -> Presenter (SWR更新 `homeworkListData`, `displayedTasks` computed signal 自动更新)。
    4.  Presenter (`displayedTasks` 更新) -> View (重新渲染 `HomeworkCardList_UI`)。
* **实习生工作**:
    * 实现上述所有带 `_UI` 后缀的纯UI组件，严格按照Presenter提供的props进行数据展示和事件回调。
    * 实现Model层中的纯数据转换函数 (`transformers.ts`)。

---

### 模块2：作业报告总览页 (点击作业卡片进入的页面，包含多个Tab)

此页面作为多种报告内容的容器，其自身需要管理当前选中的Tab、作业基本信息、班级和素材范围筛选等。

#### 1. 功能范围

* **核心功能**:
    * **作业信息展示**: 显示当前报告所属作业的名称、发布时间、截止时间、班级、素材（课程）范围。
    * **Tab切换**: 提供“学生报告”、“答题结果”、“学生提问”三个Tab，允许用户切换查看不同维度的报告。
    * **预警设置入口**: （可选，根据UI图）提供“预警设置”入口。
    * **关键指标概览**: 在页面顶部或特定区域展示该作业的整体关键数据，如班级整体进度、待关注题目数、需关注学生数等（这些数据可能由当前激活的Tab或一个通用模块提供）。
* **辅助功能**:
    * **返回功能**: 返回到作业列表页。
    * **筛选功能**: （部分Tab内）如“学生报告Tab”内可能有班级平均数据、学生列表筛选；“答题结果Tab”内可能有题目关键词搜索、共性错题筛选。这些筛选是Tab内部的，但其作用域可能影响整个报告页面的数据上下文（如全局筛选的班级）。
* **交互方式**:
    * 用户点击Tab切换报告视图。
    * 用户点击返回按钮返回上一页。
    * 用户在特定Tab内进行搜索、筛选等操作。

#### 2. 用户场景

* **场景1: 查看作业整体情况 (学科老师)**
    * 李老师从作业列表点击“3.2.2函数的最值”作业卡片，进入该作业的报告总览页。
    * 页面顶部显示作业名称、时间等信息。
    * 默认显示“学生报告”Tab，他能看到班级整体进度、建议鼓励/关注的学生列表。
* **场景2: 分析题目作答 (学科老师)**
    * 李老师想看学生们具体的答题情况，他点击切换到“答题结果”Tab。
    * 该Tab下展示了题目列表、每道题的正确率、错误人数等。
    * 他可以通过“只看共性错题”快速定位教学难点。
* **场景3: 查看学生疑问 (学科老师)**
    * 李老师想了解学生在学习过程中提出了哪些问题，他点击“学生提问”Tab。
    * 该Tab下展示了与此作业/课程相关的学生提问列表。

#### 3. 分层设计

##### HomeworkReportContext 设计

由于作业报告总览页及其下的各个Tab页（学生报告、答题结果、学生提问）高度共享作业ID、班级ID、筛选的素材范围等上下文信息，并且这些信息可能会被不同层级的组件使用，因此设计一个 `HomeworkReportContext` 是非常必要的。

```typescript
// src/features/homework/report/contexts/HomeworkReportContext.ts
import { signal, Signal, ReadonlySignal } from "@preact/signals-react";
import { createContext, useContext, ReactNode } from "react";

// 假设的作业基本信息类型
export interface HomeworkBasicInfo {
  id: string;
  title: string;
  publishTime: string; // ISO Date String
  deadlineTime: string; // ISO Date String
  // ... 其他作业基本信息
}

// 假设的素材范围类型 (例如课程中的课节)
export interface MaterialScope {
  id: string;
  name: string;
}

export type ReportTabType = "studentReport" | "answerResult" | "studentQuestions";

export interface HomeworkReportContextState {
  // 只读信号，从外部传入或在Provider初始化时获取
  homeworkId: ReadonlySignal<string>;
  currentHomeworkInfo: Signal<HomeworkBasicInfo | null>; // 作业基本信息
  availableClasses: Signal<{ id: string; name: string }[]>; // 可选班级列表 (如果支持切换班级)
  selectedClassId: Signal<string | null>; // 当前查看的班级ID
  availableMaterialScopes: Signal<MaterialScope[]>; // 可选的素材范围
  selectedMaterialScopeIds: Signal<string[]>; // 当前选中的素材范围ID列表
  activeTab: Signal<ReportTabType>;
  isLoadingInfo: Signal<boolean>; // 加载作业、班级、素材等基础信息的状态
}

export interface HomeworkReportContextActions {
  setActiveTab: (tab: ReportTabType) => void;
  setSelectedClassId: (classId: string) => void;
  setSelectedMaterialScopeIds: (scopeIds: string[]) => void;
  // refreshHomeworkInfo: () => Promise<void>; // 重新加载作业基本信息
}

export type HomeworkReportContextType = HomeworkReportContextState & HomeworkReportContextActions;

// 创建Context，提供默认值
const defaultHomeworkInfo = signal<HomeworkBasicInfo | null>(null);
const defaultAvailableClasses = signal<{ id: string; name: string }[]>([]);
const defaultSelectedClassId = signal<string | null>(null);
const defaultAvailableMaterialScopes = signal<MaterialScope[]>([]);
const defaultSelectedMaterialScopeIds = signal<string[]>([]);
const defaultActiveTab = signal<ReportTabType>("studentReport");
const defaultIsLoadingInfo = signal<boolean>(true);

export const HomeworkReportContext = createContext<HomeworkReportContextType>({
  homeworkId: signal(""), // 这个必须由 Provider 提供一个真实的只读信号
  currentHomeworkInfo: defaultHomeworkInfo,
  availableClasses: defaultAvailableClasses,
  selectedClassId: defaultSelectedClassId,
  availableMaterialScopes: defaultAvailableMaterialScopes,
  selectedMaterialScopeIds: defaultSelectedMaterialScopeIds,
  activeTab: defaultActiveTab,
  isLoadingInfo: defaultIsLoadingInfo,
  setActiveTab: () => {},
  setSelectedClassId: () => {},
  setSelectedMaterialScopeIds: () => {},
  // refreshHomeworkInfo: async () => {},
});

export const useHomeworkReport = () => {
  const context = useContext(HomeworkReportContext);
  if (!context) {
    throw new Error("useHomeworkReport must be used within a HomeworkReportProvider");
  }
  return context;
};

// HomeworkReportProvider 组件 (伪代码，实习生无需实现)
// interface HomeworkReportProviderProps {
//   homeworkIdProp: string; // 从路由参数获取
//   initialClassIdProp?: string; // 可选，从其他地方传入的初始班级
//   children: ReactNode;
// }
// export const HomeworkReportProvider = ({ homeworkIdProp, initialClassIdProp, children }: HomeworkReportProviderProps) => {
//   const homeworkId = signal(homeworkIdProp); // 包装成 signal，使其成为只读信号
//   const currentHomeworkInfo = signal<HomeworkBasicInfo | null>(null);
//   const availableClasses = signal<{ id: string; name: string }[]>([]);
//   const selectedClassId = signal<string | null>(initialClassIdProp || null);
//   const availableMaterialScopes = signal<MaterialScope[]>([]);
//   const selectedMaterialScopeIds = signal<string[]>([]); // 默认为 "全部"
//   const activeTab = signal<ReportTabType>("studentReport");
//   const isLoadingInfo = signal<boolean>(true);

//   // --- 数据获取逻辑 (SWR) ---
//   // const { data: hwInfoData, isLoading: hwInfoLoading } = useSWR(`/api/homework/${homeworkId.value}/info`, fetcher);
//   // const { data: classesData, isLoading: classesLoading } = useSWR(`/api/homework/${homeworkId.value}/classes`, fetcher);
//   // const { data: scopesData, isLoading: scopesLoading } = useSWR(
//   //   selectedClassId.value ? `/api/homework/${homeworkId.value}/class/${selectedClassId.value}/scopes` : null,
//   //   fetcher
//   // );

//   // useEffect(() => {
//   //   if (hwInfoData) currentHomeworkInfo.value = transformHomeworkInfo(hwInfoData);
//   // }, [hwInfoData]);
//   // ... 其他 effect 用于更新 availableClasses, availableMaterialScopes, selectedClassId (如果 initialClassIdProp 未提供，则从classesData中取第一个)

//   // useEffect(() => {
//   //  isLoadingInfo.value = hwInfoLoading || classesLoading || (selectedClassId.value && scopesLoading);
//   // }, [hwInfoLoading, classesLoading, scopesLoading, selectedClassId.value]);


//   const setActiveTabHandler = (tab: ReportTabType) => activeTab.value = tab;
//   const setSelectedClassIdHandler = (classId: string) => {
//     selectedClassId.value = classId;
//     selectedMaterialScopeIds.value = []; // 班级变化，重置素材范围为全部
//   };
//   const setSelectedMaterialScopeIdsHandler = (scopeIds: string[]) => selectedMaterialScopeIds.value = scopeIds;

//   const contextValue: HomeworkReportContextType = {
//     homeworkId: computed(() => homeworkId.value), // 提供一个只读的计算信号
//     currentHomeworkInfo,
//     availableClasses,
//     selectedClassId,
//     availableMaterialScopes,
//     selectedMaterialScopeIds,
//     activeTab,
//     isLoadingInfo,
//     setActiveTab: setActiveTabHandler,
//     setSelectedClassId: setSelectedClassIdHandler,
//     setSelectedMaterialScopeIds: setSelectedMaterialScopeIdsHandler,
//   };

//   return <HomeworkReportContext.Provider value={contextValue}>{children}</HomeworkReportContext.Provider>;
// };
```

##### a. Model 层 (作业报告总览)

* **数据定义 (`src/features/homework/report/types.ts`)**:
    * `HomeworkBasicInfo` (已在Context中定义)
    * `MaterialScope` (已在Context中定义)
    * `ClassInfo` (`{ id: string; name: string }`)
* **接口数据获取 (`src/features/homework/report/services.ts`)**:
    * `WorkspaceHomeworkBasicInfo(homeworkId: string): Promise<HomeworkBasicInfo>`
    * `WorkspaceAvailableClassesForHomework(homeworkId: string): Promise<ClassInfo[]>`
    * `WorkspaceMaterialScopesForHomework(homeworkId: string, classId: string): Promise<MaterialScope[]>`
    * 这些服务将在 `HomeworkReportProvider` 内部通过SWR调用。实习生只需知道Context会提供这些数据。

##### b. Presenter 层 (作业报告总览 - `HomeworkReportPagePresenter`)

* **核心职责**:
    * 从 `HomeworkReportContext` 获取共享状态和操作。
    * 管理页面级的UI状态，例如预警设置弹窗的显示/隐藏。
    * 处理返回导航。
* **状态变量 (从Context获取)**:
    * `const { homeworkId, currentHomeworkInfo, selectedClassId, selectedMaterialScopeIds, activeTab, isLoadingInfo, setActiveTab, ...actions } = useHomeworkReport();`
* **核心函数**:
    * `handleGoBack()`: 调用router返回。
    * `handleOpenWarningSettings()`: 控制预警设置弹窗。
    * `handleTabChange(tab: ReportTabType)`: 调用 `setActiveTab(tab)`。
    * `handleClassFilterChange(classId: string)`: 调用 `actions.setSelectedClassId(classId)`。
    * `handleScopeFilterChange(scopeIds: string[])`: 调用 `actions.setSelectedMaterialScopeIds(scopeIds)`。

##### c. View 层 (作业报告总览)

* **组件树结构 (`graph TD`):**
    ```mermaid
    graph TD
        ReportPage[HomeworkReportPage] --> RP_Layout[PageLayout_UI]
        RP_Layout --> Header[HomeworkReportHeader_UI]
        Header --> HWTitle[HomeworkTitle_UI]
        Header --> HWInfo[HomeworkBasicDisplay_UI]
        Header --> ClassFilter[ClassSelector_UI]
        Header --> ScopeFilter[MaterialScopeSelector_UI]
        Header --> SettingsBtn[WarningSettingsButton_UI]
        RP_Layout --> TabNav[ReportTabs_UI]
        RP_Layout --> TabContentContainer
        TabContentContainer -- activeTab is 'studentReport' --> StudentReportTab[StudentReportTabPage]
        TabContentContainer -- activeTab is 'answerResult' --> AnswerResultTab[AnswerResultTabPage]
        TabContentContainer -- activeTab is 'studentQuestions' --> StudentQuestionsTab[StudentQuestionsTabPage]
        RP_Layout --> LoadingOverlay[GlobalLoadingOverlay_UI]
    ```
* **组件职责**:
    * `HomeworkReportPage`: 顶层页面组件，包裹在 `HomeworkReportProvider` 内。使用 `HomeworkReportPagePresenter` (或直接使用Context中的值和方法)。
    * `HomeworkReportHeader_UI`: (纯UI) 展示作业标题、基本信息、班级筛选、素材范围筛选、预警设置按钮。接收来自Presenter的数据和回调。
        * `ClassSelector_UI`: (纯UI) 接收 `availableClasses`, `selectedClassId`, `onClassChange`。
        * `MaterialScopeSelector_UI`: (纯UI) 接收 `availableMaterialScopes`, `selectedMaterialScopeIds`, `onScopeChange` (可以是多选)。
    * `ReportTabs_UI`: (纯UI) 接收 `activeTab`, `onTabChange`。
    * `TabContentContainer`: 根据 `activeTab` 的值动态渲染对应的Tab页面组件。
        * `StudentReportTabPage`: “学生报告”Tab的实现 (后续详述)。
        * `AnswerResultTabPage`: “答题结果”Tab的实现 (后续详述)。
        * `StudentQuestionsTabPage`: “学生提问”Tab的实现 (后续详述)。
    * `GlobalLoadingOverlay_UI`: (纯UI) 接收 `isLoadingInfo` 控制全局加载状态的显示。

---

### 模块2.1：学生报告Tab (在作业报告总览页内)

#### 1. 功能范围

* **核心功能**:
    * **建议学生列表**: 分别展示“建议鼓励”和“建议关注”的学生名单，并提供快捷操作（如“一键表扬”、“一键提醒”）。
    * **班级统计数据**: 展示当前班级、当前素材范围内的班级平均进度（完成率）和班级平均正确率。
    * **学生列表**: 展示班级内所有学生的作业数据，包括学生信息、分层、完成进度、正确率、答题难度、错题数/总题数、用时。
    * **列表排序**: 带有建议鼓励/关注标签的学生置顶，其余按学生UID（或姓名拼音）顺序展示。
    * **列表操作**: 提供“查看详情”操作，跳转到单个学生的作业报告页。
    * **学生搜索**: 支持按学生姓名搜索列表。
    * **数据导出**: （UI图中有按钮）支持导出当前学生列表数据。
* **辅助功能**:
    * **异常数据高亮**: 对于列表中和班级均值偏离较大的数据进行高亮（飘色）展示。
    * **分层标识**: 学生列表中的“分层”信息展示，如“有进步”、“需关注”。
* **交互方式**:
    * 用户点击“一键表扬/提醒”按钮。
    * 用户在学生列表中点击“查看详情”。
    * 用户在搜索框输入学生姓名。
    * 用户点击“导出”按钮。

#### 2. 用户场景

* **场景1: 快速了解班级整体和重点学生 (学科老师)**
    * 李老师进入“学生报告”Tab。
    * 他首先看到“建议鼓励”名单中有周雨彤等6人，点击“一键表扬”。
    * 他又看到“建议关注”名单中有李子涵，点击“一键提醒”。
    * 他注意到班级平均正确率是78.5%。
* **场景2: 定位特定学生表现 (班主任)**
    * 王班主任想查看学生“刘明宇”的具体情况。
    * 她在搜索框输入“刘明宇”，列表筛选出刘明宇的条目。
    * 她看到刘明宇的完成进度是80%，正确率94%，标记为“有进步”，点击“查看详情”深入了解。
* **场景3: 识别数据异常 (学科老师)**
    * 李老师浏览学生列表时，发现张思远的正确率是83%，但用时38分钟，而李子涵正确率60%，用时50分钟，李子涵的正确率数据单元格背景色与其他同学不同（偏低高亮）。

#### 3. 分层设计

##### a. Model 层 (`src/features/homework/report/studentReport/types.ts` 等)

* **数据定义**:
    ```typescript
    export interface StudentReportListItem {
      studentId: string;
      studentName: string;
      avatarUrl?: string;
      layerInfo?: string; // 分层信息，如 "有进步", "需关注5+"
      completionProgress: number; // 0-100
      accuracyRate: number; // 0-100
      difficultyScore?: number; // 答题难度，可选
      wrongQuestionsCount: number;
      totalAnsweredQuestions: number;
      timeSpent: number; // in minutes
      isEncouraged?: boolean; // 是否在建议鼓励列表
      isConcerned?: boolean; // 是否在建议关注列表
    }

    export interface ClassReportStats {
      classAvgProgress: number;
      classAvgAccuracy: number;
      classAvgTimeSpent?: number; // 班级平均用时，可选
    }

    export interface StudentReportTabData {
      suggestedEncourageList: Pick<StudentReportListItem, "studentId" | "studentName">[];
      suggestedConcernList: Pick<StudentReportListItem, "studentId" | "studentName">[];
      classStats: ClassReportStats;
      studentList: StudentReportListItem[];
      totalStudents: number;
    }

    export interface StudentReportFilters {
      homeworkId: string;
      classId: string;
      materialScopeIds: string[]; // 空数组代表全部素材
      searchTerm?: string;
      // pagination for studentList if needed
    }
    ```
* **接口数据获取 (`src/features/homework/report/studentReport/services.ts`)**:
    * `useStudentReportData(filters: Signal<StudentReportFilters>): SWRResponse<StudentReportTabData>`: 获取学生报告Tab的全部数据。
        ```typescript
        // 伪代码，实际使用SWR
        // export const useStudentReportData = (filtersSignal: Signal<StudentReportFilters>) => {
        //   const getKey = () => {
        //     const { homeworkId, classId, materialScopeIds, searchTerm } = filtersSignal.value;
        //     if (!homeworkId || !classId) return null; // 必要参数检查
        //     const params = new URLSearchParams();
        //     params.append("homeworkId", homeworkId);
        //     params.append("classId", classId);
        //     materialScopeIds.forEach(id => params.append("materialScopeIds[]", id));
        //     if (searchTerm) params.append("searchTerm", searchTerm);
        //     return `/api/teacher/report/student-report?${params.toString()}`;
        //   };
        //   return useSWR<StudentReportTabData>(getKey, genericFetcher);
        // };
        ```
    * `sendBulkPraise(studentIds: string[], homeworkId: string): Promise<void>`
    * `sendBulkReminders(studentIds: string[], homeworkId: string): Promise<void>`
    * `exportStudentReport(filters: StudentReportFilters): Promise<Blob>`

* **接口数据转换 (`src/features/homework/report/studentReport/transformers.ts`)**:
    * `formatTimeSpentForDisplay(minutes: number): string`: (纯函数) 如 "45分钟"。
    * `getAccuracyDeviationSeverity(studentAccuracy: number, classAvgAccuracy: number): 'normal' | 'low' | 'high'`: (纯函数) 用于判断飘色。实习生实现。

##### b. Presenter 层 (`src/features/homework/report/studentReport/StudentReportTabPresenter.ts` 或 `useStudentReportTabPresenter`)

* **核心职责**:
    * 从 `HomeworkReportContext` 获取 `homeworkId`, `selectedClassId`, `selectedMaterialScopeIds`。
    * 管理学生搜索词 `searchTermSignal: Signal<string>`。
    * 调用 `useStudentReportData` 获取数据。
    * 处理学生列表的排序逻辑（鼓励/关注置顶）。
    * 处理“一键表扬/提醒”、查看学生详情、导出等交互。
    * 计算哪些学生的哪些数据需要飘色。
* **状态变量**:
    ```typescript
    // const { homeworkId, selectedClassId, selectedMaterialScopeIds } = useHomeworkReport();
    const studentSearchTerm = signal<string>("");
    // const reportFilters = computed<StudentReportFilters>(() => ({
    //   homeworkId: homeworkId.value,
    //   classId: selectedClassId.value!,
    //   materialScopeIds: selectedMaterialScopeIds.value,
    //   searchTerm: studentSearchTerm.value,
    // }));
    // const { data: reportData, error, isLoading, mutate } = useStudentReportData(reportFilters);

    const sortedStudentList = computed(() => {
        if (!reportData.value?.studentList) return [];
        // 实习生在此实现排序纯函数或调用已定义的排序纯函数
        // 1. 鼓励和关注的置顶
        // 2. 其他按UID或姓名
        const list = [...reportData.value.studentList];
        list.sort((a, b) => {
            const aPriority = (a.isEncouraged || a.isConcerned) ? 0 : 1;
            const bPriority = (b.isEncouraged || b.isConcerned) ? 0 : 1;
            if (aPriority !== bPriority) return aPriority - bPriority;
            return a.studentName.localeCompare(b.studentName, 'zh-Hans-CN'); // 简单按姓名排序
        });
        return list.map(student => ({
            ...student,
            timeSpentDisplay: formatTimeSpentForDisplay(student.timeSpent),
            accuracyDeviation: getAccuracyDeviationSeverity(student.accuracyRate, reportData.value!.classStats.classAvgAccuracy)
            // ... 其他转换
        }));
    });
    ```
* **核心函数**:
    ```typescript
    const handleStudentSearchChange = (term: string) => studentSearchTerm.value = term;
    const handleBulkPraise = async () => {
      const studentIdsToPraise = reportData.value?.suggestedEncourageList.map(s => s.studentId) || [];
      if (studentIdsToPraise.length > 0) {
        // await sendBulkPraise(studentIdsToPraise, homeworkId.value);
        // showToast("表扬已发送");
      }
    };
    const handleBulkReminder = async () => { /* ...类似逻辑... */ };
    const handleViewStudentHomeworkDetail = (studentId: string) => {
      // router.push(`/homework/report/${homeworkId.value}/student/${studentId}`);
    };
    const handleExportReport = async () => {
      // const blob = await exportStudentReport(reportFilters.value);
      // downloadFile(blob, "学生报告.xlsx");
    };
    ```
    Presenter暴露给View: `reportData.value?.suggestedEncourageList`, `reportData.value?.suggestedConcernList`, `reportData.value?.classStats`, `sortedStudentList`, `isLoading`, `error`, `studentSearchTerm`, `handleStudentSearchChange`, `handleBulkPraise`, `handleBulkReminder`, `handleViewStudentHomeworkDetail`, `handleExportReport`。

##### c. View 层

* **组件树结构 (`graph TD`):**
    ```mermaid
    graph TD
        SRT[StudentReportTabPage] --> SRL_Layout{VerticalLayout}
        SRL_Layout --> SR_Header[StudentReportHeader_UI]
        SR_Header --> SR_Encourage[EncourageList_UI]
        SR_Header --> SR_Concern[ConcernList_UI]
        SR_Header --> SR_ClassStats[ClassStatsDisplay_UI]
        SRL_Layout --> SR_Controls[StudentListControls_UI]
        SR_Controls --> SR_Search[SearchInput_UI]
        SR_Controls --> SR_ExportBtn[Button_UI text="导出"]
        SRL_Layout --> SR_Table[StudentReportTable_UI] %% 或者用列表 + 卡片模式
        SR_Table --> SR_TableRow[StudentReportTableRow_UI]
        SR_TableRow --> SR_Cell[TableCell_UI]
        SR_TableRow --> SR_ViewDetailBtn[Button_UI text="查看详情"]
        SRL_Layout --> SR_Loading[LoadingSpinner_UI]
        SRL_Layout --> SR_Error[ErrorDisplay_UI]
    ```
* **组件职责**:
    * `StudentReportTabPage`: 容器组件，从Presenter获取数据和方法。
    * `StudentReportHeader_UI`: (纯UI) 展示鼓励/关注列表和班级统计。
        * `EncourageList_UI`: (纯UI) 接收 `students`, `onBulkPraise`。
        * `ConcernList_UI`: (纯UI) 接收 `students`, `onBulkReminder`。
        * `ClassStatsDisplay_UI`: (纯UI) 接收 `classStats`。
    * `StudentListControls_UI`: (纯UI) 包含搜索框和导出按钮。
        * `SearchInput_UI`: (纯UI) 接收 `value`, `onChange`。
    * `StudentReportTable_UI`: (纯UI) 使用 `@tanstack/react-table` 实现。接收 `students: StudentReportListItem[]` (已处理好飘色标记和展示文本), `columns` 定义，`onViewDetail: (studentId) => void`。
        * 实习生定义 `columns` 时，单元格渲染函数内根据 `accuracyDeviation` 等字段应用不同样式 (Tailwind)。
    * 实习生负责实现所有 `_UI` 后缀的纯组件。

---

### 模块2.2：答题结果Tab (在作业报告总览页内)

#### 1. 功能范围

* **核心功能**:
    * **题目列表展示**: 按特定顺序（如共性错题优先，或按作答错误人数）展示作业中的所有题目或筛选后的题目。
    * **题目信息**: 每条题目显示题干预览、题号、正确率、错误人数、作答人数。
    * **共性错题识别与展示**: 在列表顶部或题目上明确标识“共性错题”。
    * **题目筛选**: 支持“只看共性错题”、按“题型”筛选。
    * **题目搜索**: 支持按题目关键词（题干、选项、解析）搜索。
    * **单题操作**: 提供“加入”（如加入错题本或组卷）和“查看”（进入题目详情页）操作。
* **辅助功能**:
    * **题目总数与共性错题数统计**: 显示在列表顶部。
    * **侧边题目面板**: （UI图中有）展示当前任务全部题号，并用颜色区分正确率，可收起/展开，点击题号可跳转到主列表对应题目。
    * **题目内容展开/收起**: 对于过长的题干或选项、解析，默认收起，可展开查看。
* **交互方式**:
    * 用户点击筛选条件（只看共性错题开关、题型下拉框）。
    * 用户在搜索框输入关键词。
    * 用户点击题目列表中的“加入”或“查看”按钮。
    * 用户在侧边题目面板点击题号。

#### 2. 用户场景

* **场景1: 查看班级共性错题 (学科老师)**
    * 李老师切换到“答题结果”Tab。
    * 他看到列表顶部统计“共28道题，3道共性错题”。
    * 他勾选“只看共性错题”，列表刷新，只显示这3道题。
    * 对第一道共性错题，他点击“查看”，进入该题的作答详情分析页面。
* **场景2: 按题型分析 (学科老师)**
    * 李老师想看看选择题的整体掌握情况。
    * 他取消“只看共性错题”，然后在“全部题型”下拉框中选择“单选题”。
    * 列表只显示单选题及其答题统计。
* **场景3: 快速定位题目 (学科老师)**
    * 侧边栏的题目面板展开，李老师看到第5题的颜色标记为红色（正确率低）。
    * 他点击面板上的“5”，主列表滚动并高亮显示第5题。

#### 3. 分层设计

##### a. Model 层 (`src/features/homework/report/answerResult/types.ts` 等)

* **数据定义**:
    ```typescript
    export interface QuestionAnswerResultItem {
      questionId: string;
      questionNumber: string; // 题号，可能是 "1", "2", 或按特定规则生成
      questionStemPreview: string; // 题干预览，可能包含HTML或Markdown
      questionType: string; // 如 "单选题", "多选题", "填空题"
      isCommonError: boolean; // 是否为共性错题
      accuracyRate: number; // 0-100
      errorCount: number; // 错误人数
      answeredCount: number; // 作答人数
      // options?: { id: string, content: string }[]; // 题目选项，可能在详情中才加载
      // answer?: string; // 答案，可能在详情中才加载
      // analysis?: string; // 解析，可能在详情中才加载
    }

    export interface AnswerResultTabData {
      totalQuestions: number;
      commonErrorQuestionCount: number;
      questionList: QuestionAnswerResultItem[];
      // availableQuestionTypes: string[]; // 可供筛选的题型列表
    }

    export interface AnswerResultFilters {
      homeworkId: string;
      classId: string;
      materialScopeIds: string[];
      searchTerm?: string;
      showOnlyCommonErrors?: boolean;
      questionTypeFilter?: string; // "all" 或具体题型
      sortBy?: "errorCountDesc" | "questionNumberAsc"; // 排序方式
    }
    ```
* **接口数据获取 (`src/features/homework/report/answerResult/services.ts`)**:
    * `useAnswerResultData(filters: Signal<AnswerResultFilters>): SWRResponse<AnswerResultTabData>`
    * `WorkspaceAvailableQuestionTypesForHomework(homeworkId: string, classId: string): Promise<string[]>`
    * `addQuestionToCollection(questionId: string, collectionType: "错题本" | "组卷篮"): Promise<void>`

* **接口数据转换 (`src/features/homework/report/answerResult/transformers.ts`)**:
    * `truncateQuestionStem(stem: string, maxLength: number): string`: (纯函数) 截断题干用于预览。实习生实现。

##### b. Presenter 层 (`src/features/homework/report/answerResult/AnswerResultTabPresenter.ts` 或 `useAnswerResultTabPresenter`)

* **核心职责**:
    * 从 `HomeworkReportContext` 获取共享上下文。
    * 管理本Tab的筛选条件: `searchTerm`, `showOnlyCommonErrors`, `questionTypeFilter`, `sortBy`。
    * 调用 `useAnswerResultData` 获取题目列表数据。
    * 处理筛选、搜索、排序逻辑。
    * 处理“加入”、“查看题目详情”交互。
    * 管理侧边题目面板的展开/收起状态及与主列表的联动。
* **状态变量 (Signals)**:
    ```typescript
    // const { homeworkId, selectedClassId, selectedMaterialScopeIds } = useHomeworkReport();
    const answerResultSearchTerm = signal<string>("");
    const showOnlyCommonErrors = signal<boolean>(false);
    const selectedQuestionType = signal<string>("all"); // "all" 代表全部题型
    const currentSortBy = signal<AnswerResultFilters['sortBy']>("errorCountDesc"); // 默认按错误人数降序
    const isSidePanelOpen = signal<boolean>(true); // 侧边题目面板状态

    // const answerResultFilters = computed<AnswerResultFilters>(() => ({
    //   homeworkId: homeworkId.value,
    //   classId: selectedClassId.value!,
    //   materialScopeIds: selectedMaterialScopeIds.value,
    //   searchTerm: answerResultSearchTerm.value,
    //   showOnlyCommonErrors: showOnlyCommonErrors.value,
    //   questionTypeFilter: selectedQuestionType.value,
    //   sortBy: currentSortBy.value,
    // }));
    // const { data: answerResultData, error, isLoading, mutate } = useAnswerResultData(answerResultFilters);
    // const { data: availableQuestionTypes } = useSWR(...); // 获取题型选项
    ```
* **核心函数**:
    ```typescript
    const handleSearchChange = (term: string) => answerResultSearchTerm.value = term;
    const toggleShowOnlyCommonErrors = () => showOnlyCommonErrors.value = !showOnlyCommonErrors.value;
    const handleQuestionTypeChange = (type: string) => selectedQuestionType.value = type;
    const handleSortChange = (sortBy: AnswerResultFilters['sortBy']) => currentSortBy.value = sortBy;
    const handleViewQuestionDetail = (questionId: string) => {
      // router.push(`/homework/report/${homeworkId.value}/question/${questionId}`);
    };
    const handleAddQuestionTo = async (questionId: string, type: "错题本" | "组卷篮") => {
      // await addQuestionToCollection(questionId, type);
      // showToast("已加入" + type);
    };
    const toggleSidePanel = () => isSidePanelOpen.value = !isSidePanelOpen.value;
    const scrollToQuestion = (questionId: string) => { /* DOM操作或虚拟列表API调用 */ };
    ```
    Presenter暴露给View: `answerResultData`, `isLoading`, `error`, `availableQuestionTypes`, `answerResultSearchTerm`, `showOnlyCommonErrors`, `selectedQuestionType`, `currentSortBy`, `isSidePanelOpen`, 和上述 `handle*` 系列函数。

##### c. View 层

* **组件树结构 (`graph TD`):**
    ```mermaid
    graph TD
        ART[AnswerResultTabPage] --> ARL_Layout{TwoColumnLayout} %% 主区域 + 侧边栏
        ARL_Layout --> AR_MainContent[VerticalLayout]
            AR_MainContent --> AR_StatsHeader[AnswerResultStatsHeader_UI]
            AR_StatsHeader --> AR_TotalCount[Text_UI] %% "共XX题"
            AR_StatsHeader --> AR_CommonErrorCount[Text_UI] %% "XX道共性错题"
            AR_MainContent --> AR_Controls[AnswerResultControls_UI]
            AR_Controls --> AR_Search[SearchInput_UI]
            AR_Controls --> AR_CommonErrorToggle[Switch_UI label="只看共性错题"]
            AR_Controls --> AR_TypeFilter[Select_UI label="题型"]
            AR_Controls --> AR_SortOptions[Select_UI label="排序"] %% 或用按钮组
            AR_MainContent --> AR_QuestionList[QuestionAnswerResultList_UI]
            AR_QuestionList --> AR_QuestionItem[QuestionAnswerResultItem_UI]
            AR_QuestionItem --> QI_Stem[ExpandableText_UI]
            AR_QuestionItem --> QI_Stats[Text_UI] %% 正确率、人数等
            AR_QuestionItem --> QI_Actions[HorizontalLayout]
            QI_Actions --> QI_AddToBtn[Button_UI text="加入"]
            QI_Actions --> QI_ViewBtn[Button_UI text="查看"]
            AR_MainContent --> AR_Loading[LoadingSpinner_UI]
            AR_MainContent --> AR_Error[ErrorDisplay_UI]
        ARL_Layout --> AR_SidePanel[QuestionSidePanel_UI]
        AR_SidePanel --> SP_ToggleBtn[Button_UI text="收起/展开"]
        AR_SidePanel --> SP_QuestionNavList[QuestionNavList_UI]
        SP_QuestionNavList --> SP_NavItem[QuestionNavItem_UI]
    ```
* **组件职责**:
    * `AnswerResultTabPage`: 容器。
    * `AnswerResultStatsHeader_UI`: (纯UI) 展示总题数、共性错题数。
    * `AnswerResultControls_UI`: (纯UI) 包含搜索、筛选、排序控件。
    * `QuestionAnswerResultList_UI`: (纯UI) 接收 `questions: QuestionAnswerResultItem[]`, `onViewDetail`, `onAddToCollection`。使用 `@tanstack/react-virtual` 渲染长列表。
        * `QuestionAnswerResultItem_UI`: (纯UI) 接收单个 `question` 及回调。
            * `ExpandableText_UI`: (纯UI) 用于展示可展开/收起的题干。
    * `QuestionSidePanel_UI`: (纯UI) 侧边题目导航面板。
        * `QuestionNavList_UI`: (纯UI) 接收 `questions` (仅需ID, 题号, 正确率用于标色), `onNavItemClick: (questionId) => void`。
            * `QuestionNavItem_UI`: (纯UI) 接收单题信息，点击后调用 `onNavItemClick`。
    * 实习生负责实现所有 `_UI` 后缀的纯组件，并根据正确率数据应用颜色。

---

### 模块2.3：学生提问Tab (在作业报告总览页内)

这个Tab的功能相对独立，主要展示与当前作业/课程相关的学生提问。

#### 1. 功能范围

* **核心功能**:
    * **提问列表展示**: 展示当前班级、当前素材范围内，学生提出的与课程内容（幻灯片/知识点）相关的提问。
    * **内容关联**: 提问通常与特定的课程幻灯片或教学环节相关联。
    * **提问信息**: 显示提问人、提问时间、提问内容、回复数（或状态）。
    * **层级结构**: 可能按课程内容结构（如幻灯片顺序）组织提问列表。
* **辅助功能**:
    * **筛选/排序**: 可能支持按提问时间、热度、是否已回复等筛选或排序。
    * **查看提问详情/回复**: 点击提问可以查看完整对话或进行回复。
* **交互方式**:
    * 用户滚动浏览提问列表。
    * 用户点击某个提问项进入详情或展开回复。

#### 2. 用户场景

* **场景1: 了解学生普遍疑问 (学科老师)**
    * 李老师切换到“学生提问”Tab。
    * 他看到按课程幻灯片顺序排列的提问。在“复数的概念引入背景”这一节，有3条学生提问。
    * 他点开其中一条“虚数i和-1的平方根有什么区别？”，查看其他学生的回复或自己进行解答。

#### 3. 分层设计

##### a. Model 层 (`src/features/homework/report/studentQuestions/types.ts` 等)

* **数据定义**:
    ```typescript
    export interface StudentQuestionItem {
      questionId: string;
      studentName: string;
      studentAvatar?: string;
      questionContent: string;
      relatedMaterialId: string; // 关联的课程素材ID (如幻灯片ID)
      relatedMaterialName: string; // 关联的课程素材名称
      submissionTime: string; // ISO Date String
      replyCount: number;
      isResolved?: boolean; // 是否已解决 (可选)
      // lastReplyTime?: string;
    }

    // 可能按素材分组
    export interface MaterialWithQuestions {
      materialId: string;
      materialName: string;
      materialOrder: number; // 素材顺序，用于排序
      questions: StudentQuestionItem[];
    }

    export interface StudentQuestionsTabData {
      questionsGroupedByMaterial: MaterialWithQuestions[];
      // or a flat list:
      // allQuestions: StudentQuestionItem[];
    }

    export interface StudentQuestionsFilters {
      homeworkId: string;
      classId: string;
      materialScopeIds: string[];
      // filterByStatus?: 'resolved' | 'unresolved';
      // sortBy?: 'timeDesc' | 'replyCountDesc';
    }
    ```
* **接口数据获取 (`src/features/homework/report/studentQuestions/services.ts`)**:
    * `useStudentQuestionsData(filters: Signal<StudentQuestionsFilters>): SWRResponse<StudentQuestionsTabData>`

* **接口数据转换 (`src/features/homework/report/studentQuestions/transformers.ts`)**:
    * `groupQuestionsByMaterial(questions: StudentQuestionItem[]): MaterialWithQuestions[]`: (纯函数) 如果API返回扁平列表，则由此函数进行分组。实习生实现。

##### b. Presenter 层 (`src/features/homework/report/studentQuestions/StudentQuestionsTabPresenter.ts` 或 `useStudentQuestionsTabPresenter`)

* **核心职责**:
    * 从 `HomeworkReportContext` 获取共享上下文。
    * 管理本Tab的筛选和排序状态（如果支持）。
    * 调用 `useStudentQuestionsData` 获取数据。
    * 处理查看提问详情的导航。
* **状态变量**:
    ```typescript
    // const { homeworkId, selectedClassId, selectedMaterialScopeIds } = useHomeworkReport();
    // const filtersForQuestions = computed<StudentQuestionsFilters>(() => ({ /* ... */ }));
    // const { data: questionsData, error, isLoading } = useStudentQuestionsData(filtersForQuestions);

    const displayableQuestions = computed(() => {
        // 如果需要转换或进一步处理 questionsData.value
        // 例如，如果API返回扁平列表，在此调用 groupQuestionsByMaterial
        return questionsData.value?.questionsGroupedByMaterial || [];
    });
    ```
* **核心函数**:
    ```typescript
    const handleViewQuestionThread = (questionId: string, materialId: string) => {
      // router.push(`/homework/report/${homeworkId.value}/question-thread/${materialId}/${questionId}`);
    };
    ```
    Presenter暴露给View: `displayableQuestions`, `isLoading`, `error`, `handleViewQuestionThread`。

##### c. View 层

* **组件树结构 (`graph TD`):**
    ```mermaid
    graph TD
        SQT[StudentQuestionsTabPage] --> SQL_Layout{VerticalLayout}
        SQL_Layout --> SQ_FilterControls[StudentQuestionsFilterControls_UI] %% (如果支持筛选)
        SQL_Layout --> SQ_MaterialList[MaterialWithQuestionsList_UI]
        SQ_MaterialList --> SQ_MaterialGroup[MaterialQuestionGroup_UI]
        SQ_MaterialGroup --> MG_Header[Text_UI] %% 素材名称
        SQ_MaterialGroup --> MG_QuestionItemList[QuestionItemList_UI]
        MG_QuestionItemList --> SQ_QuestionItem[StudentQuestionItem_UI]
        SQ_QuestionItem --> QI_Content[Text_UI]
        SQ_QuestionItem --> QI_Meta[Text_UI] %% 提问人、时间、回复数
        SQL_Layout --> SQ_Loading[LoadingSpinner_UI]
        SQL_Layout --> SQ_Error[ErrorDisplay_UI]
    ```
* **组件职责**:
    * `StudentQuestionsTabPage`: 容器。
    * `StudentQuestionsFilterControls_UI`: (纯UI) （如果需要）包含筛选和排序控件。
    * `MaterialWithQuestionsList_UI`: (纯UI) 接收 `groupedQuestions: MaterialWithQuestions[]`, `onViewThread`。遍历渲染 `MaterialQuestionGroup_UI`。
        * `MaterialQuestionGroup_UI`: (纯UI) 接收单个 `materialGroup`, `onViewThread`。展示素材名称，并遍历渲染其下的 `QuestionItemList_UI`。
            * `QuestionItemList_UI`: (纯UI) 接收 `questions: StudentQuestionItem[]`, `onViewThread`。
                * `StudentQuestionItem_UI`: (纯UI) 接收单个 `question`, `onViewThread`。点击时调用 `onViewThread(question.questionId, question.relatedMaterialId)`。
    * 实习生负责实现所有 `_UI` 后缀的纯组件。

---

### 模块3：单个学生的作业报告页

此页面从“学生报告Tab”中的学生列表点击“查看详情”进入。

#### 1. 功能范围

* **核心功能**:
    * **页面头部**: 显示学生姓名 + 作业名称，返回按钮。
    * **答题概览**: 显示任务开始时间、要求完成时间、本人完成时间（如果已完成）、本人进度（如果未100%完成）、当前任务的素材范围。
    * **Tab切换**: 提供“答题结果”、“学生提问”两个Tab。
    * **“答题结果”Tab内**:
        * 题目统计: “共xx道题，xx道错题”（针对该学生）。
        * 题目列表: 与班级报告的“答题结果”Tab类似，但数据仅为该学生，显示其每题的作答状态（✅, ❌, 半对, !尚未作答）。
        * 题目搜索与筛选: 功能同班级报告。
        * 单题操作: “查看”（进入学生针对该题的详情，如具体答案、得分点）。
    * **“学生提问”Tab内**:
        * 展示该学生在当前任务/素材下的提问内容，按课程幻灯片顺序。
* **辅助功能**:
    * 侧边题目面板（答题结果Tab内）。
    * 题目内容展开/收起。
* **交互方式**:
    * 用户点击Tab切换视图。
    * 用户在“答题结果”Tab内进行题目搜索、筛选、点击“查看”。
    * 用户在“学生提问”Tab内浏览提问。

#### 2. 用户场景

* **场景1: 查看学生具体答题情况 (学科老师)**
    * 李老师从班级“学生报告”中点击学生“周雨彤”的“查看详情”，进入周雨彤的作业报告页。
    * 默认显示“答题结果”Tab，他看到周雨彤共答了28题，错了4题。
    * 他向下滚动题目列表，看到每道题后面都有周雨彤的对错标记。对于一道错题，他点击“查看”了解周雨彤的具体作答。
* **场景2: 查看学生个人提问 (学科老师)**
    * 李老师想看看周雨彤在这份作业相关的课程内容中提了什么问题。
    * 他切换到“学生提问”Tab，看到了周雨彤的提问列表。

#### 3. 分层设计

##### SingleStudentReportContext 设计

与班级作业报告类似，单个学生的作业报告页也需要上下文管理，特别是作业ID、学生ID以及两个Tab之间可能共享的筛选逻辑。

```typescript
// src/features/homework/report/singleStudent/contexts/SingleStudentReportContext.ts
import { signal, Signal, ReadonlySignal } from "@preact/signals-react";
import { createContext, useContext, ReactNode } from "react";
import { HomeworkBasicInfo, MaterialScope, ReportTabType as BaseReportTabType } from '@/features/homework/report/types'; // 复用类型

export type StudentReportTabType = "answerResult" | "studentQuestions"; // 学生报告只有这两个Tab

export interface StudentTaskCompletionInfo {
  taskStartTime?: string; // 任务开始时间 (来自作业配置)
  requiredCompletionTime?: string; // 要求完成时间 (来自作业配置)
  studentCompletionTime?: string; // 学生本人完成时间
  studentProgress?: number; // 学生进度 (0-100)，如果未100%
}

export interface SingleStudentReportContextState {
  homeworkId: ReadonlySignal<string>;
  studentId: ReadonlySignal<string>;
  studentName: Signal<string | null>; // 学生姓名
  currentHomeworkInfo: Signal<HomeworkBasicInfo | null>;
  studentTaskInfo: Signal<StudentTaskCompletionInfo | null>; // 学生该任务的完成信息
  availableMaterialScopes: Signal<MaterialScope[]>;
  selectedMaterialScopeIds: Signal<string[]>;
  activeTab: Signal<StudentReportTabType>;
  isLoadingPageInfo: Signal<boolean>;
}

export interface SingleStudentReportContextActions {
  setActiveTab: (tab: StudentReportTabType) => void;
  setSelectedMaterialScopeIds: (scopeIds: string[]) => void;
  // refreshPageInfo: () => Promise<void>;
}

export type SingleStudentReportContextType = SingleStudentReportContextState & SingleStudentReportContextActions;

// ... (Context 创建和 use hook，类似 HomeworkReportContext)
// Provider 伪代码 (实习生无需实现):
// SingleStudentReportProvider 会从路由参数获取 homeworkId 和 studentId
// 然后获取作业基本信息、学生姓名、学生该任务的完成信息、素材范围等
```

##### a. Model 层 (单个学生报告)

* **数据定义 (`src/features/homework/report/singleStudent/types.ts`)**:
    * `StudentTaskCompletionInfo` (已在Context定义)。
    * `StudentQuestionAnswerResultItem`: 类似班级的 `QuestionAnswerResultItem`，但增加学生作答状态的具体枚举（`correct`, `incorrect`, `partial`, `notAnswered`）。
    * `StudentAnswerResultTabData`: 包含 `totalQuestions`, `wrongQuestionsCount` (针对该学生), `questionList: StudentQuestionAnswerResultItem[]`。
    * `StudentSpecificQuestionItem`: 类似班级的 `StudentQuestionItem`，但明确是该学生的提问。
    * `StudentSpecificQuestionsTabData`: 包含该学生的提问列表 `questionsGroupedByMaterial`。
* **接口数据获取 (`src/features/homework/report/singleStudent/services.ts`)**:
    * `WorkspaceSingleStudentPageInfo(homeworkId: string, studentId: string): Promise<{ studentName: string; homeworkInfo: HomeworkBasicInfo; taskInfo: StudentTaskCompletionInfo; materialScopes: MaterialScope[] }>` (用于Provider初始化)
    * `useStudentAnswerResultData(filters: Signal<AnswerResultFilters & { studentId: string }>): SWRResponse<StudentAnswerResultTabData>` (复用班级筛选类型，增加studentId)
    * `useStudentSpecificQuestionsData(filters: Signal<StudentQuestionsFilters & { studentId: string }>): SWRResponse<StudentSpecificQuestionsTabData>`

##### b. Presenter 层 (单个学生报告页 - `SingleStudentReportPagePresenter`)

* **核心职责**:
    * 从 `SingleStudentReportContext` 获取状态和操作。
    * 管理页面标题组合（学生名+作业名）。
* **状态**: 主要来自Context。
* **函数**:
    * `pageTitle = computed(() => `${studentName.value || ''} - ${currentHomeworkInfo.value?.title || ''}`);`
    * `handleGoBack()`
    * `handleTabChange(tab: StudentReportTabType)`: 调用Context的 `setActiveTab`。
    * `handleScopeFilterChange(scopeIds: string[])`: 调用Context的 `setSelectedMaterialScopeIds`。
    * 其下的“答题结果Tab”和“学生提问Tab”将有各自独立的Presenter，但都会从 `SingleStudentReportContext` 消费 `homeworkId`, `studentId`, `selectedMaterialScopeIds` 等作为其数据获取的参数。

##### c. View 层 (单个学生报告页)

* **组件树结构 (`graph TD`):**
    ```mermaid
    graph TD
        SSReportPage[SingleStudentReportPage] --> SSRP_Layout[PageLayout_UI]
        SSRP_Layout --> SSRP_Header[SingleStudentReportHeader_UI]
        SSRP_Header --> PageTitle[Text_UI] %% 学生名 + 作业名
        SSRP_Header --> StudentTaskInfoDisplay[StudentTaskInfo_UI]
        SSRP_Header --> ScopeFilterForStudent[MaterialScopeSelector_UI]
        SSRP_Layout --> SSRP_TabNav[ReportTabs_UI tabs="答题结果,学生提问"]
        SSRP_Layout --> SSRP_TabContentContainer
        SSRP_TabContentContainer -- activeTab is 'answerResult' --> SS_AnswerResultTab[StudentAnswerResultTabPage]
        SSRP_TabContentContainer -- activeTab is 'studentQuestions' --> SS_StudentQuestionsTab[StudentSpecificQuestionsTabPage]
        SSRP_Layout --> SSRP_LoadingOverlay[GlobalLoadingOverlay_UI]
    ```
* **组件职责**:
    * `SingleStudentReportPage`: 顶层页面，包裹在 `SingleStudentReportProvider` 内。
    * `SingleStudentReportHeader_UI`: (纯UI) 展示页面标题、学生任务完成概览、素材范围筛选。
    * `StudentTaskInfo_UI`: (纯UI) 接收 `studentTaskInfo` 展示。
    * `ReportTabs_UI`: (纯UI) 接收 `activeTab`, `onTabChange`，但Tab选项只有两个。
    * `StudentAnswerResultTabPage`: “答题结果”Tab的实现，其内部结构和逻辑非常类似模块2.2 (班级答题结果Tab)，但其Presenter层获取数据时会传入 `studentId`，且题目列表展示的是单个学生的对错状态。实习生可以复用大部分UI组件，仅在数据展示和Presenter的数据源上有所不同。
    * `StudentSpecificQuestionsTabPage`: “学生提问”Tab的实现，其内部结构和逻辑非常类似模块2.3 (班级学生提问Tab)，但其Presenter层获取数据时会传入 `studentId`。实习生可以复用大部分UI组件。

---

### 模块4：题目详情页 (班级视角 和 学生个人视角)

此页面可以从“答题结果Tab”（班级或学生个人）中的题目列表点击“查看”进入。根据来源不同，展示的数据范围和侧重点也不同。

#### 1. 功能范围

* **核心功能 (班级视角)**:
    * **题目信息展示**: 完整题干、选项、标签（题型、难度）、答案、解析。
    * **作答统计 (班级整体)**:
        * 选择题/判断题: 正答率、各选项选择人数及比例、答对人数、答错人数、平均答题时长。
        * 非选择题: （本期无分数）可能展示典型作答示例或关键词云；（未来有分数）得分率、平均分。
    * **学生作答分布**: （选择题）展示选择了不同答案的学生名单，并标示其作答对错。
    * **大屏讲解入口**: 提供“大屏讲解”按钮。
* **核心功能 (学生个人视角)**:
    * **题目信息展示**: 同班级视角。
    * **作答统计 (个人)**: 展示该学生本题的作答结果（正确/错误/部分正确）、TA的选择（或作答内容）、TA的用时。
* **辅助功能**:
    * 内容展开/收起（题干、解析）。
* **交互方式**:
    * 用户点击“展开/收起”查看完整内容。
    * 用户（教师）点击“大屏讲解”。

#### 2. 用户场景

* **场景1: 分析班级题目作答详情 (学科老师)**
    * 李老师在班级“答题结果”Tab中，点击一道选择题的“查看”，进入该题的班级视角详情页。
    * 他看到题目、答案解析，以及统计数据：正答率50%，选项A有10人选（5人对，5人错），选项B有20人选（全错）。
    * 他点击“大屏讲解”，准备在课堂上分析这道题。
* **场景2: 查看学生个人单题作答 (学科老师)**
    * 李老师在学生“周雨彤”的“答题结果”Tab中，点击一道周雨彤答错的题目“查看”。
    * 页面展示题目信息，并明确标出周雨彤选择了哪个错误选项，以及她的作答用时。

#### 3. 分层设计

##### QuestionDetailContext 设计 (可选，如果页面很复杂或有多种模式切换)

如果班级视角和学生视角的题目详情页共享很多基础UI，但数据获取和部分展示逻辑不同，可以考虑用Context来传递题目ID和视角模式。但更简单的方式可能是两个独立的页面/路由，各自获取数据。这里我们先按独立页面组件处理，Presenter根据传入的`studentId` (可选) 来决定获取班级数据还是个人数据。

##### a. Model 层 (`src/features/homework/report/questionDetail/types.ts` 等)

* **数据定义**:
    ```typescript
    export interface QuestionFullInfo {
      questionId: string;
      stem: string; // 完整题干，支持富文本
      options?: { id: string; content: string; isCorrect: boolean }[]; // 选择题选项
      answer: string; // 文本答案或正确选项ID
      analysis: string; // 详细解析，支持富文本
      questionType: string; // 如 "单选题"
      difficulty: string; // 如 "简单"
      tags?: string[]; // 其他标签
    }

    // 班级视角统计
    export interface ClassQuestionStats {
      accuracyRate?: number; // 选择题/判断题
      avgTimeSpent?: number; // 秒
      optionStats?: { optionId: string; count: number; percentage: number; correctStudentNames?: string[]; incorrectStudentNames?: string[] }[];
      correctCount?: number;
      incorrectCount?: number;
      // 非选择题统计 (未来)
      // typicalAnswers?: string[];
      // scoreDistribution?: { score: number, count: number }[];
    }

    // 学生个人作答详情
    export interface StudentQuestionAnswerDetail {
      studentId: string;
      studentName: string;
      selectedOptionId?: string; // 学生选择的选项ID
      studentAnswerText?: string; // 学生填写的答案文本
      isCorrect: boolean;
      timeSpent?: number; // 秒
      score?: number; // 得分 (未来)
    }

    export interface QuestionDetailPageData_ClassView {
      questionInfo: QuestionFullInfo;
      classStats: ClassQuestionStats;
    }
    export interface QuestionDetailPageData_StudentView {
      questionInfo: QuestionFullInfo;
      studentAnswer: StudentQuestionAnswerDetail;
    }
    ```
* **接口数据获取 (`src/features/homework/report/questionDetail/services.ts`)**:
    * `useQuestionDetailData_ClassView(homeworkId: string, questionId: string, classId: string): SWRResponse<QuestionDetailPageData_ClassView>`
    * `useQuestionDetailData_StudentView(homeworkId: string, questionId: string, studentId: string): SWRResponse<QuestionDetailPageData_StudentView>`

##### b. Presenter 层 (`src/features/homework/report/questionDetail/QuestionDetailPagePresenter.ts`)

* **核心职责**:
    * 从路由参数获取 `homeworkId`, `questionId`, `classId` (班级视图) 或 `studentId` (学生视图)。
    * 根据是否有 `studentId` 调用对应的数据获取hook。
    * 处理题干、解析的展开/收起状态。
    * 处理“大屏讲解”逻辑（可能是状态切换或导航）。
* **状态变量**:
    ```typescript
    // const homeworkId = fromRouterParams();
    // const questionId = fromRouterParams();
    // const classId = fromRouterParams_optional();
    // const studentId = fromRouterParams_optional();

    const isStemExpanded = signal(false);
    const isAnalysisExpanded = signal(false);

    // const { data, error, isLoading } = studentId
    //   ? useQuestionDetailData_StudentView(homeworkId, questionId, studentId)
    //   : useQuestionDetailData_ClassView(homeworkId, questionId, classId!);

    const viewMode = computed(() => studentId ? 'student' : 'class');
    ```
* **核心函数**:
    ```typescript
    const toggleStemExpansion = () => isStemExpanded.value = !isStemExpanded.value;
    const toggleAnalysisExpansion = () => isAnalysisExpanded.value = !isAnalysisExpanded.value;
    const handleLaunchLargeScreenMode = () => { /* ... */ };
    ```
    Presenter暴露给View: `data` (具体类型根据viewMode), `isLoading`, `error`, `viewMode`, `isStemExpanded`, `isAnalysisExpanded`, `toggleStemExpansion`, `toggleAnalysisExpansion`, `handleLaunchLargeScreenMode`。

##### c. View 层

* **组件树结构 (`graph TD`):**
    ```mermaid
    graph TD
        QDP[QuestionDetailPage] --> QDPL_Layout[PageLayout_UI]
        QDPL_Layout --> QDP_Header[Text_UI title="答题结果详情"]
        QDPL_Layout --> QDP_LargeScreenBtn[Button_UI text="大屏讲解"] %% 仅班级视图
        QDPL_Layout --> QDP_QuestionInfoCard[Card_UI]
            QDP_QuestionInfoCard --> QI_StemDisplay[ExpandableRichText_UI content=questionInfo.stem]
            QDP_QuestionInfoCard --> QI_OptionsDisplay[OptionList_UI options=questionInfo.options] %% 选择题
            QDP_QuestionInfoCard --> QI_Tags[TagGroup_UI tags="type,difficulty"]
            QDP_QuestionInfoCard --> QI_AnswerDisplay[RichText_UI content=questionInfo.answer]
            QDP_QuestionInfoCard --> QI_AnalysisDisplay[ExpandableRichText_UI content=questionInfo.analysis]

        QDPL_Layout -- viewMode is 'class' --> QDP_ClassStatsCard[Card_UI title="作答统计"]
            QDP_ClassStatsCard --> CS_Accuracy[StatItem_UI label="正答率"]
            QDP_ClassStatsCard --> CS_OptionDistributionChart[BarChart_UI data=classStats.optionStats] %% 或列表
            QDP_ClassStatsCard --> CS_StudentAnswerList[StudentChoiceList_UI data=classStats.optionStats]

        QDPL_Layout -- viewMode is 'student' --> QDP_StudentAnswerCard[Card_UI title="我的作答"]
            QDP_StudentAnswerCard --> SA_Choice[Text_UI label="我的选择/作答"]
            QDP_StudentAnswerCard --> SA_Result[Text_UI label="结果"]
            QDP_StudentAnswerCard --> SA_Time[StatItem_UI label="用时"]

        QDPL_Layout --> QDP_Loading[LoadingSpinner_UI]
        QDPL_Layout --> QDP_Error[ErrorDisplay_UI]
    ```
* **组件职责**:
    * `QuestionDetailPage`: 容器。
    * `ExpandableRichText_UI`: (纯UI) 展示可展开收起的富文本（题干、解析）。
    * `OptionList_UI`: (纯UI) 展示选择题选项。
    * `StatItem_UI`: (纯UI) 展示单个统计指标。
    * `BarChart_UI`: (纯UI) 使用Echarts或类似库展示选项分布。
    * `StudentChoiceList_UI`: (纯UI) 展示选择各选项的学生名单。
    * 实习生负责实现这些纯UI组件，根据 `viewMode` 和 `data` 的不同，渲染对应的内容。

---

### 模块5：策略说明与提醒设置 (P0主要是展示，P1是自定义)

这部分在文档中分散在“策略”大标题下，以及“需求概览”中的提醒设置。P0阶段主要是理解和展示系统内置的“鼓励”、“关注”、“共性错题”策略。

#### 1. 功能范围

* **核心功能 (P0)**:
    * **策略理解**: 产品文档中详细描述了系统如何根据数据判断“值得鼓励”、“需要关注”的学生，以及如何定义“共性错题”。前端在P0阶段可能不需要直接“展示”这些策略规则给用户，而是在“学生报告Tab”和“答题结果Tab”中应用这些策略的结果。
        * “鼓励”标签的生成依据（连对、进步、超群等）。
        * “关注”标签的生成依据（进度落后、逾期、偏离历史/同层等）。
        * “共性错题”的定义（如答题人数>10，正确率<60%）。
    * **结果应用**:
        * 在学生报告Tab，根据策略结果，将学生归入“建议鼓励”或“建议关注”列表。
        * 在答题结果Tab，根据策略结果，标记题目为“共性错题”。
* **核心功能 (P1 - 自定义提醒设置)**:
    * **规则列表展示**: 展示所有可自定义的提醒规则（如进度提醒、学习分偏离提醒等）。
    * **规则配置**: 允许用户对每一条规则进行“开启/关闭”，修改提醒的“阈值”（如进度百分比、偏离百分比）。
* **交互方式 (P1)**:
    * 用户浏览规则列表。
    * 用户点击开关启用/禁用规则。
    * 用户在输入框中修改阈值。
    * 用户保存设置。

#### 2. 用户场景

* **场景 (P0 - 策略应用)**:
    * 李老师在查看“学生报告”时，系统已经根据内置策略帮他识别出了“周雨彤”是值得鼓励的（可能因为她连对题数达到了班级最多），“李子涵”是需要关注的（可能因为他作业逾期未完成）。
    * 在查看“答题结果”时，一道题目被标记为“共性错题”，因为它的正确率低于60%且作答人数超过10人。
* **场景 (P1 - 自定义设置)**:
    * 王老师觉得系统默认的“班级平均进度达50%仍未学习则关注”的阈值太低，她希望改成“30%”。
    * 她进入“提醒设置”页面，找到对应的规则，将阈值从50修改为30，并保存。

#### 3. 分层设计 (P1 - 自定义提醒设置页面)

##### a. Model 层 (`src/features/settings/reminders/types.ts` 等)

* **数据定义**:
    ```typescript
    export interface ReminderRule {
      id: string; // 规则唯一标识
      category: "progress" | "score" | "behavior"; // 规则分类
      name: string; // 规则名称，如 "进度落后提醒"
      description: string; // 规则描述
      isEnabled: boolean; // 是否启用
      thresholdType: "percentage" | "days" | "count"; // 阈值类型
      thresholdValue: number; // 当前阈值
      // minThreshold?: number; // 最小可设阈值
      // maxThreshold?: number; // 最大可设阈值
    }
    export interface ReminderSettingsData {
      rules: ReminderRule[];
    }
    ```
* **接口数据获取 (`src/features/settings/reminders/services.ts`)**:
    * `useReminderSettings(): SWRResponse<ReminderSettingsData>`
    * `updateReminderSettings(rules: ReminderRule[]): Promise<void>`

##### b. Presenter 层 (`src/features/settings/reminders/ReminderSettingsPresenter.ts`)

* **核心职责**:
    * 获取当前的提醒规则设置。
    * 管理用户对规则的修改（启用/禁用、修改阈值）。
    * 处理保存设置的逻辑。
* **状态变量**:
    ```typescript
    // const { data: initialSettings, error, isLoading, mutate } = useReminderSettings();
    const localRules = signal<ReminderRule[]>([]); // 用于编辑的本地副本

    // useEffect(() => { // 当从服务器加载到数据时，更新本地副本
    //   if (initialSettings.value) localRules.value = deepClone(initialSettings.value.rules);
    // }, [initialSettings]);

    const isDirty = computed(() => { // 检查本地修改是否与服务器版本不同
        if (!initialSettings.value || !initialSettings.value.rules) return false;
        // 实习生实现比较逻辑的纯函数
        // return !areRulesEqual(localRules.value, initialSettings.value.rules);
        return JSON.stringify(localRules.value) !== JSON.stringify(initialSettings.value.rules); // 简易比较
    });
    ```
* **核心函数**:
    ```typescript
    const handleRuleToggle = (ruleId: string, isEnabled: boolean) => {
      localRules.value = localRules.value.map(rule =>
        rule.id === ruleId ? { ...rule, isEnabled } : rule
      );
    };
    const handleThresholdChange = (ruleId: string, newValue: number) => {
      localRules.value = localRules.value.map(rule =>
        rule.id === ruleId ? { ...rule, thresholdValue: newValue } : rule
      );
    };
    const handleSaveChanges = async () => {
      // await updateReminderSettings(localRules.value);
      // showToast("设置已保存");
      // mutate(); // 重新获取最新设置，或直接用 localRules 更新 SWR cache
      initialSettings.value = { rules: deepClone(localRules.value) }; // 假设保存成功后，服务器数据与本地一致
    };
    const handleCancelChanges = () => {
      if (initialSettings.value) localRules.value = deepClone(initialSettings.value.rules);
    };
    ```
    Presenter暴露给View: `localRules`, `isLoading`, `error`, `isDirty`, `handleRuleToggle`, `handleThresholdChange`, `handleSaveChanges`, `handleCancelChanges`。

##### c. View 层 (P1 - 自定义提醒设置页面)

* **组件树结构 (`graph TD`):**
    ```mermaid
    graph TD
        RSP[ReminderSettingsPage] --> RSPL_Layout[PageLayout_UI]
        RSPL_Layout --> RSP_Header[Text_UI title="提醒设置"]
        RSPL_Layout --> RSP_RuleList[RuleList_UI]
        RSP_RuleList --> RSP_RuleItem[RuleItem_UI]
        RSP_RuleItem --> RI_Name[Text_UI]
        RSP_RuleItem --> RI_Description[Text_UI]
        RSP_RuleItem --> RI_Toggle[Switch_UI]
        RSP_RuleItem --> RI_ThresholdInput[InputNumber_UI]
        RSPL_Layout --> RSP_Actions[FormActions_UI]
        RSP_Actions --> RSP_SaveBtn[Button_UI text="保存" disabled=!isDirty]
        RSP_Actions --> RSP_CancelBtn[Button_UI text="取消" disabled=!isDirty]
        RSPL_Layout --> RSP_Loading[LoadingSpinner_UI]
        RSPL_Layout --> RSP_Error[ErrorDisplay_UI]
    ```
* **组件职责**:
    * `ReminderSettingsPage`: 容器。
    * `RuleList_UI`: (纯UI) 接收 `rules: ReminderRule[]`, `onToggle`, `onThresholdChange`。
        * `RuleItem_UI`: (纯UI) 接收单个 `rule` 及回调。
    * `FormActions_UI`: (纯UI) 包含保存和取消按钮。
    * 实习生负责实现所有 `_UI` 后缀的纯组件。

---

此详细设计应能覆盖文档中的主要功能，并为实习生提供清晰的纯组件和纯函数实现指导。Presenter层和Context封装了所有业务逻辑和跨组件通信，确保View层保持纯净。
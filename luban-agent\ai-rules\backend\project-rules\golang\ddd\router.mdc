---
description: 
globs: 
alwaysApply: false
---
# Router 层开发规范 (Go Kratos DDD)

## 1. 核心原则与职责

1.  **请求入口**: 作为 HTTP/gRPC 请求的入口点。
2.  **路由定义**: 定义 API 路由规则，将 URL/RPC 方法映射到 Controller/Handler。
3.  **中间件编排**: 应用全局或分组的中间件 (Middleware)，如认证、授权、日志、限流、CORS、TraceID、Panic 恢复等。
4.  **框架集成**: 与 Web/RPC 框架 (如 Gin, Kratos HTTP/gRPC Server) 紧密集成。
5.  **保持轻量**: 路由层自身不应包含业务逻辑，仅负责请求的分发和基础处理。

## 2. 目录结构

```
app/{biz_module}/
└── router/
    ├── router.go         # 定义该模块的路由注册函数
    └── middleware.go     # (可选) 定义该模块特定的中间件

# 或者，对于简单项目，所有路由可集中在 cmd/{service}/internal/server/http.go 或 grpc.go 中注册

cmd/{service_name}/
└── internal/
    ├── server/
    │   ├── http.go       # HTTP 服务器设置，包含路由注册
    │   └── grpc.go       # gRPC 服务器设置，包含服务注册
    └── service/          # Service 实现 (用于注入)
```
*选择哪种结构取决于项目复杂度和团队约定。对于遵循 DDD 的模块化项目，推荐在 `app/{biz_module}/router/` 下定义。*

## 3. 路由注册

1.  **注册函数**: 通常在 `app/{biz_module}/router/router.go` 中定义一个函数，如 `RegisterUserRoutes`，接收框架的路由引擎/服务器实例和 Controller 实例作为参数。
2.  **分组与版本**: **必须**使用路由分组 (e.g., `v1 = r.Group("/v1")`) 来实现 API 版本控制。
3.  **映射规则**: 使用框架提供的方法 (如 `gin.POST`, `gin.GET`) 将 HTTP 方法和路径映射到具体的 Controller 方法。
4.  **依赖注入**: Controller 实例应通过依赖注入 (Wire) 传递给路由注册函数。
5.  **清晰性**: 保持路由定义清晰、易于理解。

```go
// app/user/router/router.go
package router

import (
    "github.com/gin-gonic/gin" // 假设使用 Gin
    // 或者 Kratos HTTP Server 的相关包
    kratosHttp "github.com/go-kratos/kratos/v2/transport/http"
    "your_project/app/user/controller"
    "your_project/app/pkg/middleware" // 假设有通用的中间件包
)

// RegisterUserRoutes Gin 示例
func RegisterUserRoutes(r *gin.Engine, auth gin.HandlerFunc, userController *controller.UserController) {
    v1 := r.Group("/api/v1")
    {
        userGroup := v1.Group("/users")
        userGroup.Use(auth) // 应用认证中间件到整个用户组
        {
            userGroup.POST("", userController.CreateUser)
            userGroup.GET("/:id", userController.GetUser)         // 路径参数
            userGroup.GET("", userController.ListUsers)           // 查询参数
            userGroup.PUT("/:id", userController.UpdateUser)
            userGroup.DELETE("/:id", userController.DeleteUser)
            userGroup.POST("/login", userController.Login)       // Login 通常不需要认证中间件，可单独注册
            userGroup.POST("/register", userController.Register) // Register 通常也不需要
        }
    }
    // 单独注册不需要认证的路由
    v1.POST("/api/v1/users/login", userController.Login)
    v1.POST("/api/v1/users/register", userController.Register)

}

// RegisterUserHTTP Kratos HTTP 示例
func RegisterUserHTTP(srv *kratosHttp.Server, auth kratosHttp.FilterFunc, userController *controller.UserController) {
    route := srv.Route("/api/v1")

    // 不需要认证的路由
    route.POST("/users/login", userController.LoginKratos)
    route.POST("/users/register", userController.RegisterKratos)

    // 需要认证的路由组
    userGroup := route.Group("/users", auth) // 应用认证中间件
    {
        userGroup.POST("", userController.CreateUserKratos)
        userGroup.GET("/{id}", userController.GetUserKratos)
        userGroup.GET("", userController.ListUsersKratos)
        userGroup.PUT("/{id}", userController.UpdateUserKratos)
        userGroup.DELETE("/{id}", userController.DeleteUserKratos)
    }
}

```

## 4. 中间件 (Middleware)

1.  **职责**: 处理横切关注点，如认证、日志、限流、恢复等。
2.  **来源**: 
    *   **框架内置**: 使用 Kratos 或 Gin 等框架提供的标准中间件。
    *   **项目通用**: 定义在 `app/pkg/middleware` 或类似位置的通用中间件。
    *   **模块特定**: 定义在 `app/{biz_module}/router/middleware.go` 中的、仅用于特定业务模块的中间件（较少见）。
3.  **应用顺序**: 注意中间件的应用顺序，它会影响请求处理流程。
4.  **必要中间件**: **必须**包含：
    *   **恢复 (Recovery)**: 捕获 panic，防止服务崩溃。
    *   **日志 (Logging)**: 记录请求入口、出口、耗时、状态码等。
    *   **追踪 (Tracing)**: 集成 TraceID，用于分布式链路追踪。
    *   **认证 (Authentication)**: 验证用户身份。
    *   **授权 (Authorization)**: 检查用户是否有权限访问资源（可在中间件或 Controller/Service 层实现）。
    *   **CORS**: 处理跨域资源共享。
5.  **可选中间件**: 
    *   **限流 (Rate Limiting)**: 防止恶意请求或过载。
    *   **指标 (Metrics)**: 记录请求相关的指标。
    *   **请求校验 (Request Validation)**: 通用的请求体校验（更细粒度的校验通常在 Controller/Service）。

## 5. 与 Controller 的交互

*   Router 层负责调用正确的 Controller 方法。
*   框架负责将请求参数 (路径参数、查询参数、请求体) 绑定到 Controller 方法接收的参数 (通常是 DTO)。
*   Controller 处理业务逻辑后返回结果或错误，Router 层通过框架将结果序列化为响应，或将错误转换为合适的 HTTP/gRPC 状态码和错误信息。

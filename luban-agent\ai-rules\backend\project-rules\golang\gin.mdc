---
description: 
globs: 
alwaysApply: false
---
# Gin Router 代码生成规则

## 边界
- 通过依赖注入方式获取 `AppService`，使用 `svc := app.GetAppService(c)`
- 禁止直接访问仓储或领域逻辑
- 返回统一的 JSON 格式：`{"data": {...}, "code": 0, "error": ""}`，HTTP 状态码统一为 200
- 业务错误可通过 `errors.New()` 创建自定义错误，由全局中间件统一处理；无需在每个路由内 `if err != nil`
- 每次请求生成/透传 `X-Trace-Id`，在请求开始/结束时记录日志 `logger.Info()`

## 约定
- 路由文件命名：每个聚合一个 `*_handler.go`
- 路由前缀统一 `/api/v{version}/{resource}`
- 函数命名采用 `<Verb><Noun>` 驼峰式命名
- 请求/响应模型使用 `struct` 并添加 binding/json 标签
- 成功状态码统一 `200`，业务错误返回 `200` + `code != 0`
- 日志在入口出口调用 `logger.Info()`
- 耗时操作使用 Goroutine 异步处理，返回任务 ID

## 示例代码
```go
// auth_handler.go
package handlers

import (
	"github.com/gin-gonic/gin"
	"your-project/pkg/app"
	"your-project/pkg/model"
	"your-project/pkg/utils"
)

type UserLoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

func RegisterAuthRoutes(router *gin.RouterGroup) {
	authGroup := router.Group("/auth")
	{
		authGroup.POST("/login", LoginUser)
	}
}

func LoginUser(c *gin.Context) {
	traceID := utils.GetOrGenerateTraceID(c)
	logger := utils.GetLogger().With("trace_id", traceID, "handler", "LoginUser")
	logger.Info("开始处理登录请求")

	var req UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		app.ResponseError(c, app.ErrInvalidParams, err.Error())
		return
	}

	svc := app.GetAuthAppService(c)
	user, token, err := svc.Login(req.Username, req.Password, c.ClientIP())
	if err != nil {
		// 错误由全局中间件处理
		app.ResponseError(c, app.ErrBusiness, err.Error())
		return
	}

	logger.Info("登录请求处理成功")
	app.ResponseSuccess(c, gin.H{
		"user_id": user.UserID,
		"token":   token.Token,
	})
}
```
---
description: 
globs: 
alwaysApply: false
---
# 设计到代码的递归拆分工作流程

## 流程图

```mermaid
graph TD
    A[开始: 完整设计图] --> B{是否可拆分?}
    B -->|是| C[拆分为子设计图]
    C --> D[为每个子设计图创建任务]
    D --> B
    B -->|否| E[创建最小单元任务]
    E --> F[生成任务树]
    F --> G[结束]
```

## 深入分析

### 1. 核心目标
- 将设计图转化为可执行的开发任务
- 确保任务的可管理性和可追踪性
- 保持任务之间的依赖关系清晰
- 便于团队协作和进度管理

### 2. 拆分标准
1. **功能独立性**
   - 组件是否具有独立的功能
   - 是否可以被单独测试
   - 是否可以被独立部署

2. **技术边界**
   - 前端/后端分离
   - 服务/API 边界
   - 数据流边界

3. **复杂度评估**
   - 开发时间估算
   - 技术难度评估
   - 资源需求评估

4. **拆分终止条件**
   - 当满足以下任一条件时停止拆分：
    - 可直接对应开发的一个原生组件（如 <button>/UIButton）
    - Figma 中无法用更基础的图层组合（如单一 Text Layer 或未嵌套的 Shape）

5. **拆分优先级**
   - 按照 『页面 → 模块 → 组件 → 元素 → 原子』顺序逐级分解。

### 3. 任务管理
1. **任务粒度**
   - 最小任务单元应该能在 1-3 天内完成
   - 任务描述应该清晰明确
   - 包含具体的验收标准

2. **依赖关系**
   - 明确任务之间的依赖关系
   - 识别关键路径
   - 规划并行开发的可能性

3. **优先级排序**
   - 基于业务价值
   - 考虑技术依赖
   - 评估风险因素

### 4. 质量控制
1. **设计评审**
   - 每个拆分阶段都需要设计评审
   - 确保拆分后的设计保持一致性
   - 验证技术可行性

2. **任务评审**
   - 评估任务的可执行性
   - 检查任务描述的完整性
   - 确认资源分配的合理性

### 5. 迭代优化
1. **反馈机制**
   - 收集开发过程中的反馈
   - 调整任务拆分策略
   - 优化工作流程

2. **持续改进**
   - 记录拆分过程中的经验教训
   - 更新拆分标准
   - 优化任务管理方法

## 最佳实践建议

### 1. 拆分策略
- 采用自上而下的拆分方式
- 优先考虑业务功能边界
- 保持组件的可复用性

### 2. 任务管理
- 使用项目管理工具跟踪任务
- 定期更新任务状态
- 及时调整任务优先级

### 3. 团队协作
- 明确任务负责人
- 建立有效的沟通机制
- 定期进行进度同步

### 4. 质量控制
- 建立代码审查机制
- 制定统一的编码规范
- 实施自动化测试

## 输出物
1. 任务树结构
2. 任务详细说明
3. 依赖关系图
4. 开发时间估算
5. 风险评估报告 
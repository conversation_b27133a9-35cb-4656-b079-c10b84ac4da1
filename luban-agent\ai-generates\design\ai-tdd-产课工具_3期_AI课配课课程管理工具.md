# 技术设计文档：AI课配课课程管理工具

## 1. 引言

### 1.1. 项目背景
本项目旨在开发一个AI课配课课程管理工具，服务于教研老师（配课与审核角色）和总部教研管理老师。核心目标是打通AI课程从创建、配课、预览到上架的完整MVP业务流程，优化AI课程生产效率，确保课程内容质量与规范。

### 1.2. 设计目标
- 设计一个可扩展、可维护、高性能的后端服务系统。
- 遵循DDD分层架构，实现高内聚、低耦合的模块设计。
- 确保系统安全性、数据一致性及用户操作的流畅性。
- 支持MVP功能的快速迭代和未来功能的平滑扩展。

### 1.3. 范围
本技术设计文档覆盖AI课配课课程管理工具的后端系统设计，包括：
- 用户认证与权限管理
- 课程管理（创建、编辑、查询、上架/更新、批量操作）
- 配课工具支持（课程结构、教学组件配置）
- 整课预览支持
- API接口设计
- 数据模型设计

不在此文档范围内的内容：
- 前端UI/UX具体实现细节。
- 外部依赖系统（如独立题库系统、数字人服务、业务树知识点管理系统）的内部设计，仅定义交互接口。

### 1.4. 术语表
参考PRD文档中的术语表，关键术语包括：
- **AI课**: 集成了人工智能技术的在线课程。
- **配课**: 为AI课程配置教学内容、组件（如视频、练习）、流程和参数的过程。
- **课程管理工具**: 供总部教研管理老师进行课程创建、编辑、上架、管理等操作的后台系统。
- **配课工具**: 供教研生产老师进行具体课程内容配置和编排的在线工具。
- **Part**: 课程内容结构中的一个组成部分或单元。
- **教学组件**: 构成课程内容的独立教学元素，如视频组件、练习组件。
- **业务树知识点**: 内部知识库体系中定义的标准化知识点。
- **稿件地址**: 存放课程原始素材（如文稿、视频脚本等）的URL或路径。
- **数字人ID**: AI虚拟教师的唯一标识。
- **VPN**: 虚拟专用网络。
- **MVP**: 最小可行产品。

## 2. 系统架构

### 2.1. 总体架构
本系统将采用单体应用架构，模块化设计，遵循DDD分层思想。未来可根据业务发展和性能需求，将特定模块（如文件处理、批量任务处理）拆分为微服务。

**技术选型:**
- **开发框架**: Kratos (主要用于项目结构和部分组件，如配置、日志、DI集成)
- **Web框架**: Gin (处理HTTP请求)
- **ORM**: GORM (数据库交互)
- **数据库**: PostgreSQL
- **依赖注入**: Google Wire
- **消息队列**: RabbitMQ/Kafka (用于异步处理，如批量创建课程，本期MVP中可先实现同步，预留异步接口)
- **缓存**: Redis (用于热点数据缓存，提高查询性能)

### 2.2. 分层架构 (DDD)
系统将严格遵循DDD分层架构，确保各层职责清晰，禁止跨层依赖。

```mermaid
graph TD
    UserInterface["展现层 (Interface Layer - Gin)"] -- 调用 --> ApplicationLayer["应用层 (Application Layer)"];
    ApplicationLayer -- 协调 --> DomainLayer["领域层 (Domain Layer)"];
    ApplicationLayer -- 使用 --> InfrastructureLayer["基础设施层 (Infrastructure Layer)"];
    DomainLayer -- 定义接口 --> InfrastructureLayer;
    InfrastructureLayer -- 实现接口 --> DomainLayer;
    UserInterface -.-> DTOs;
    ApplicationLayer -.-> DTOs;
```

- **展现层 (Interface Layer)**:
    - 使用Gin框架处理HTTP请求，负责请求参数的解析、校验和响应的组装。
    - 包含API接口定义（`api/v1/...`），使用结构体验证请求（如`model/api`或handler内部定义的structs）。
    - 调用应用层服务处理业务逻辑。
    - 严格禁止包含业务逻辑。

- **应用层 (Application Layer)**:
    - 负责业务流程的编排和事务管理。
    - 定义和实现Use Cases (服务，如 `CourseService`)。
    - 接收展现层的请求，调用领域层服务和仓库接口完成操作。
    - 遵循CQRS原则，区分Command和Query处理。
    - 目录结构示例: `internal/service/...`

- **领域层 (Domain Layer)**:
    - 包含核心业务逻辑、实体（Entities）、聚合（Aggregates）、值对象（Value Objects）、领域服务（Domain Services）和仓库接口（Repository Interfaces）。
    - 实体和聚合封装了业务状态和行为。
    - 仓库接口定义数据持久化的契约，由基础设施层实现。
    - 目录结构示例: `internal/domain/...`

- **基础设施层 (Infrastructure Layer)**:
    - 实现领域层定义的仓库接口，使用GORM与PostgreSQL进行数据持久化。
    - 封装对外部服务（如消息队列、缓存、文件存储、第三方API）的访问。
    - 提供通用技术服务，如日志（`app/core/loger/logger_new`）、配置加载等。
    - 目录结构示例: `internal/data/...` (GORM实现), `internal/biz/external` (外部服务客户端)

### 2.3. CQRS架构模式
为提高系统的可维护性和性能，部分模块（特别是课程管理和配课模块）将遵循CQRS原则。
- **命令 (Commands)**: 处理创建、修改、删除等改变系统状态的操作。例如：`CreateCourseCommand`, `UpdateCourseCommand`, `AddExerciseComponentCommand`。命令处理逻辑会涉及事务和领域模型的更新。
- **查询 (Queries)**: 处理数据读取操作，通常不改变系统状态。例如：`ListCoursesQuery`, `GetCourseDetailsQuery`。查询可以直接访问优化过的数据视图（如果需要，未来可引入读模型）。
- **异步处理**: 对于耗时操作，如批量课程创建、课程上架后可能触发的通知或数据同步，考虑使用消息队列进行异步处理。
    - 批处理任务状态将通过数据库记录，并提供API查询进度。

## 3. 模块设计
系统按业务功能垂直切分模块，确保高内聚低耦合。

### 3.1. 用户认证与权限模块 (UserAuth)
- **3.1.1. 功能描述**:
    - 总部教研管理老师通过手机号和验证码登录。
    - 校验手机号是否在后台配置的白名单内。
    - 访问需通过内网VPN（由网络策略保障）。
- **3.1.2. 核心流程**:
    1. 用户输入手机号，请求发送验证码。
    2. 系统校验手机号是否在白名单，若在，则生成并发送验证码（通过短信服务，本期可简化为固定验证码或mock）。
    3. 用户输入验证码进行登录。
    4. 系统校验验证码，成功后生成JWT Token返回给客户端。
- **3.1.3. 接口设计 (API)**:
    - `POST /api/v1/auth/send-code` (发送验证码)
    - `POST /api/v1/auth/login` (登录)
- **3.1.4. 领域模型**:
    - `User` (总部教研管理老师，包含手机号、白名单状态等)
    - `AuthToken` (JWT)

### 3.2. 课程管理模块 (CourseAdmin)
- **3.2.1. 功能描述**:
    - 课程创建（单个、批量Excel导入）。
    - 课程列表展示、筛选、排序、下载。
    - 课程编辑。
    - 查看课程详情（含相关资源链接）。
    - 课程上架/更新（单个、批量）。
- **3.2.2. 核心流程**:
    - **单个课程创建**:
        1. 用户提交课程信息（名称、稿件地址、关联业务树知识点等）。
        2. 系统根据稿件地址URL参数自动填充部分信息（学段、学科、单课类型、数字人ID，这些信息不可手动修改）。
        3. 校验课程名称与业务树知识点唯一性。
        4. 保存课程信息。
    - **批量课程创建**:
        1. 用户下载Excel模板，填写后上传。
        2. 系统解析Excel，校验数据（稿件地址重复、知识点匹配等）。
        3. (可选异步) 逐条创建课程，记录成功/失败。
        4. 返回处理结果，提供失败原因下载。
    - **课程上架/更新**:
        1. 用户选择课程进行上架/更新。
        2. 系统校验上架条件（如试题/视频是否为空）。
        3. 更新课程状态，记录操作人、时间。
        4. "纠错类"修改与"升级类"修改（本期简化支持）处理。
- **3.2.3. 接口设计 (API)**:
    - `POST /api/v1/courses` (单个课程创建)
    - `POST /api/v1/courses/batch` (批量课程创建 - 上传Excel)
    - `GET /api/v1/courses/batch/{task_id}` (查询批量创建任务状态)
    - `GET /api/v1/courses/batch/template` (下载批量创建模板)
    - `GET /api/v1/courses` (课程列表查询，支持筛选、排序、分页)
    - `GET /api/v1/courses/export` (下载课程列表)
    - `GET /api/v1/courses/{course_id}` (查看课程详情)
    - `PUT /api/v1/courses/{course_id}` (课程编辑)
    - `POST /api/v1/courses/{course_id}/publish` (课程上架/更新)
    - `POST /api/v1/courses/batch-publish` (批量上架/更新)
- **3.2.4. 领域模型**:
    - `Course` (聚合根): 包含ID, 名称, 稿件地址, 学段, 学科, 课程类型, 数字人ID, 状态 (未上架, 上架, 修改待上架), 创建人, 创建时间, 上架信息等。
    - `CourseKnowledgePointLink`: 课程与业务树末级知识点的关联。
    - `BatchCreationTask`: 批量创建任务记录。
- **3.2.5. 异步处理**:
    - 批量课程创建可设计为异步任务，通过消息队列分发处理，减轻接口压力。

### 3.3. 配课工具模块 (CourseComposer)
- **3.3.1. 功能描述**:
    - 教研生产老师访问特定课程的配课工作台。
    - 课程结构化配置（Part管理）。
    - 添加、编辑、删除教学组件（练习组件、视频组件）。
    - 调整组件顺序。
    - 保存配课进度（实时/半实时）。
- **3.3.2. 核心流程**:
    - **访问配课工作台**:
        1. 用户通过 `course_id` 访问。
        2. 系统加载课程结构和已配置组件。
    - **添加教学组件**:
        1. 用户在指定Part内或Part间选择添加组件类型（练习/视频）。
        2. **练习组件**: 输入题库题目ID列表，系统获取题目信息（需与题库系统交互接口）。
        3. **视频组件**: 上传本地视频文件，填写组件名称。视频文件需存储到文件服务。
        4. 保存组件配置。
    - **保存配课**:
        1. 用户操作后（添加、修改、排序组件），前端触发保存。
        2. 后端保存课程的完整配置（通常是JSON结构描述课程内容）。
- **3.3.3. 接口设计 (API)**:
    - `GET /api/v1/courses/{course_id}/composer` (获取课程配课数据)
    - `PUT /api/v1/courses/{course_id}/composer` (保存课程配课数据 - 全量或增量)
    - `POST /api/v1/courses/{course_id}/composer/parts` (添加Part - 若支持动态增删Part)
    - `POST /api/v1/courses/{course_id}/parts/{part_id}/components` (添加教学组件)
    - `PUT /api/v1/courses/{course_id}/parts/{part_id}/components/{component_id}` (编辑教学组件)
    - `DELETE /api/v1/courses/{course_id}/parts/{part_id}/components/{component_id}` (删除教学组件)
    - `POST /api/v1/courses/{course_id}/composer/upload-video` (上传视频文件)
- **3.3.4. 领域模型**:
    - `Course` (聚合根，包含配课内容)
    - `CoursePart` (实体): Part ID, 名称, 顺序。
    - `CourseComponent` (实体，基类/接口): Component ID, 类型 (练习/视频), 顺序。
        - `ExerciseComponent` (具体组件): 题目ID列表。
        - `VideoComponent` (具体组件): 视频名称, 视频文件URL, 时长。
    - 配课数据本身可以是一个JSON字段存储在`Course`表中，或者拆分到`CoursePart`和`CourseComponent`相关表中。PRD中UI模版后台配置，简化了这部分。

### 3.4. 整课预览模块 (CoursePreview)
- **3.4.1. 功能描述**:
    - 审核老师或生产老师访问课程的完整预览页面。
    - 预览内容随配课工具实时/半实时更新。
    - 展示课程目录、视频播放（含控制）、练习内容（查看题目）。
- **3.4.2. 核心流程**:
    1. 用户通过课程的预览URL访问。
    2. 系统根据 `course_id` 加载最新的已保存的配课数据。
    3. 组织成预览页面所需的数据结构返回给前端。
- **3.4.3. 接口设计 (API)**:
    - `GET /api/v1/courses/{course_id}/preview` (获取课程预览数据)
    - 此接口主要服务于预览页面，前端根据返回数据渲染。

### 3.5. 公共模块
- **3.5.1. 文件服务**:
    - 负责处理视频文件上传（来自配课工具）、Excel文件上传（来自批量创建课程）。
    - 可集成云存储服务（如OSS, S3）或自建文件存储。
    - 提供上传接口，返回文件URL。
    - 接口: `POST /api/v1/files/upload` (通用文件上传)
- **3.5.2. 业务树知识点服务接口**:
    - 提供查询业务树末级知识点的接口（如果知识点数据由外部系统管理）。
    - 接口: `GET /api/v1/knowledge-points?query={keyword}`
- **3.5.3. 常量管理**:
    - 定义在 `app/consts` 目录，如课程状态、组件类型等。

## 4. 数据模型设计 (PostgreSQL)
表名、字段名使用`snake_case`。所有表包含 `created_at`, `updated_at`, `deleted_at` (用于软删除)。

- **4.1. `users` (用户信息表)**
    - `id` (BIGSERIAL, PK)
    - `phone_number` (VARCHAR(20), UNIQUE, NOT NULL, COMMENT '手机号')
    - `name` (VARCHAR(100), COMMENT '姓名')
    - `role` (VARCHAR(50), NOT NULL, COMMENT '角色: admin_teacher, production_teacher, audit_teacher')
    - `is_whitelisted` (BOOLEAN, DEFAULT TRUE, COMMENT '是否白名单用户')
    - `created_at` (TIMESTAMPTZ, NOT NULL)
    - `updated_at` (TIMESTAMPTZ, NOT NULL)
    - `deleted_at` (TIMESTAMPTZ, NULL)

- **4.2. `courses` (课程信息表)**
    - `id` (BIGSERIAL, PK)
    - `name` (VARCHAR(255), NOT NULL, COMMENT '课程名称')
    - `manuscript_url` (VARCHAR(1024), COMMENT '稿件地址')
    - `stage` (VARCHAR(50), COMMENT '学段')
    - `subject` (VARCHAR(50), COMMENT '学科')
    - `course_type` (VARCHAR(50), COMMENT '单课类型')
    - `digital_human_id` (VARCHAR(100), COMMENT 'AI老师数字人ID')
    - `status` (VARCHAR(30), NOT NULL, COMMENT '课程状态: drafting, pending_publish, published, updated_pending_publish')
    - `creator_id` (BIGINT, FK REFERENCES users(id), COMMENT '创建人ID')
    - `publisher_id` (BIGINT, FK REFERENCES users(id), NULL, COMMENT '上架人ID')
    - `published_at` (TIMESTAMPTZ, NULL, COMMENT '上架时间')
    - `version` (INT, DEFAULT 1, COMMENT '课程版本，用于升级类修改')
    - `config_json` (JSONB, NULL, COMMENT '配课配置数据，包含parts和components')
    - `created_at` (TIMESTAMPTZ, NOT NULL)
    - `updated_at` (TIMESTAMPTZ, NOT NULL)
    - `deleted_at` (TIMESTAMPTZ, NULL)
    - INDEX `idx_courses_status` (`status`)
    - INDEX `idx_courses_name` (`name`)

- **4.3. `course_knowledge_links` (课程与知识点关联表)**
    - `id` (BIGSERIAL, PK)
    - `course_id` (BIGINT, NOT NULL, FK REFERENCES courses(id) ON DELETE CASCADE)
    - `knowledge_point_id` (VARCHAR(100), NOT NULL, COMMENT '业务树知识点ID')
    - `knowledge_point_name` (VARCHAR(255), COMMENT '业务树知识点名称')
    - `created_at` (TIMESTAMPTZ, NOT NULL)
    - `updated_at` (TIMESTAMPTZ, NOT NULL)
    - `deleted_at` (TIMESTAMPTZ, NULL)
    - UNIQUE (`course_id`, `knowledge_point_id`)

- **4.4. `batch_creation_tasks` (批量创建课程任务表)**
    - `id` (BIGSERIAL, PK)
    - `uploader_id` (BIGINT, FK REFERENCES users(id), COMMENT '上传用户ID')
    - `file_name` (VARCHAR(255), COMMENT '上传文件名')
    - `status` (VARCHAR(30), NOT NULL, COMMENT '任务状态: pending, processing, completed, failed')
    - `total_count` (INT, DEFAULT 0)
    - `success_count` (INT, DEFAULT 0)
    - `failure_count` (INT, DEFAULT 0)
    - `result_file_url` (VARCHAR(1024), COMMENT '失败原因文件URL')
    - `created_at` (TIMESTAMPTZ, NOT NULL)
    - `updated_at` (TIMESTAMPTZ, NOT NULL)

- **4.5. `uploaded_files` (上传文件记录表 - 用于视频等)**
    - `id` (BIGSERIAL, PK)
    - `uploader_id` (BIGINT, FK REFERENCES users(id), COMMENT '上传用户ID')
    - `file_name` (VARCHAR(255), NOT NULL, COMMENT '原始文件名')
    - `file_path` (VARCHAR(1024), NOT NULL, COMMENT '存储路径/URL')
    - `file_type` (VARCHAR(50), COMMENT '文件类型，e.g., video, excel')
    - `file_size_bytes` (BIGINT)
    - `created_at` (TIMESTAMPTZ, NOT NULL)
    - `updated_at` (TIMESTAMPTZ, NOT NULL)

**说明**: `course_parts` 和 `course_components` 可以作为 `courses.config_json` 字段中的JSON结构来管理，以简化MVP阶段的表结构。如果组件复杂度增加或查询需求变复杂，未来可以拆分为独立表。PRD中提到 "UI模版（本期后台配置，前端无需展示修改）"，进一步支持了使用JSON配置的灵活性。

## 5. API 设计规范

- **5.1. URL 规范与版本控制**:
    - 使用RESTful风格。
    - API路径统一添加版本号前缀，如 `/api/v1/...`。
    - 资源名使用复数形式，如 `/courses`。
- **5.2. 请求与响应格式**:
    - 请求体和响应体主要使用JSON格式。
    - 统一响应结构:
      ```json
      {
        "code": 0, // 业务状态码，0表示成功，非0表示失败
        "message": "success", // 提示信息
        "data": {} // 实际数据
      }
      ```
    - `data` 为空时，可以是 `null` 或 `{}`。
- **5.3. HTTP 状态码使用**:
    - `200 OK`: 请求成功。
    - `201 Created`: 资源创建成功。
    - `204 No Content`: 请求成功，但无返回内容（如删除操作）。
    - `400 Bad Request`: 请求参数错误或校验失败。
    - `401 Unauthorized`: 未认证或认证失败。
    - `403 Forbidden`: 已认证但无权限访问。
    - `404 Not Found`: 请求资源不存在。
    - `500 Internal Server Error`: 服务器内部错误。
- **5.4. 认证与授权**:
    - 登录接口返回JWT Token。
    - 后续请求在HTTP Header中携带 `Authorization: Bearer <token>`。
    - API网关或中间件负责Token校验和用户身份解析。
    - 权限根据用户角色在应用层进行判断。
- **5.5. API 文档**:
    - 使用Swagger (OpenAPI 3.0) 自动生成和维护API文档。

## 6. 关键技术决策与考虑

- **6.1. 依赖注入 (Wire)**:
    - 使用Google Wire进行编译时依赖注入，管理组件生命周期，降低耦合度。
- **6.2. 错误处理与日志记录**:
    - **统一错误码**: 定义在 `app/consts/error_codes.go`，方便前后端协作和问题定位。
    - **错误信息**: 包含错误码、错误信息和上下文（对用户友好，对内详细）。
    - **日志规范**:
        - 使用 `app/core/loger/logger_new` 进行日志记录。
        - 日志级别: DEBUG, INFO, WARN, ERROR, FATAL。
        - 日志内容包含请求ID (Trace ID), 用户ID, 时间戳, 关键操作, 错误堆栈等。
        - 禁止记录敏感信息如手机号、验证码明文。
        - 日志格式采用JSON，便于采集和分析。
- **6.3. 安全设计**:
    - **访问控制**:
        - 核心功能通过内网VPN访问 (网络层面保障)。
        - 后台管理用户白名单机制。
    - **输入验证**:
        - 所有外部输入（API请求参数、上传文件内容）进行严格验证（使用Gin validator和自定义校验规则）。
        - 结构体验证标签定义在 `model/api` 或handler层。
    - **Web安全防护**:
        - GORM默认支持参数化查询，防止SQL注入。
        - Gin框架提供XSS保护机制。
        - CSRF防护（若有表单提交，需要考虑，纯API服务可通过Token机制缓解）。
    - **文件上传安全**:
        - 限制上传文件类型和大小。
        - 对上传文件进行安全扫描（若有条件）。
        - 存储路径与Web服务分离。
- **6.4. 性能考虑**:
    - **缓存策略**:
        - 对热点数据（如课程列表、课程详情、配置信息）使用Redis进行缓存。
        - 缓存更新策略：Cache-Aside Pattern，或在数据变更时主动失效缓存。
    - **数据库优化**:
        - 针对查询场景设计合适的索引。
        - 避免大事务，优化慢查询。
        - GORM预加载（Preload）用于关联查询。
    - **批量操作**:
        - 课程批量创建使用异步处理。
        - 数据库批量写入操作（GORM Batch Insert/Update）。
    - **API限流**: 对关键API（如登录、文件上传）实现限流保护。
- **6.5. 并发控制**:
    - 合理使用goroutine，避免泄漏。
    - 使用 `context` 控制超时和取消。
    - 对共享资源（如缓存计数器）使用并发安全的数据结构或锁机制。
- **6.6. 配置管理**:
    - 使用Kratos的配置管理组件，支持多种配置源（如本地文件、配置中心）。
    - 禁止硬编码配置信息。
- **6.7. 测试策略**:
    - **单元测试**: 覆盖率 > 80%，针对领域层和应用层核心逻辑。使用Go标准库testing和mock框架。
    - **集成测试**: 覆盖关键业务流程，测试API接口和数据库交互。
    - 测试不依赖外部实际服务，使用mock或stub。

## 7. 监控与可观察性

- **7.1. 关键指标**:
    - API请求量、响应时间（平均、P95、P99）、错误率。
    - 数据库连接数、慢查询。
    - 消息队列消息堆积情况。
    - 系统资源使用率（CPU, 内存, 磁盘）。
- **7.2. 分布式追踪 (OpenTelemetry)**:
    - 集成OpenTelemetry SDK，实现请求链路追踪，便于排查问题和性能分析。
    - Trace ID贯穿日志和系统调用。
- **7.3. 健康检查接口**:
    - 实现 `/healthz` 接口，返回系统健康状态（如数据库连接、依赖服务可用性）。
    - Kratos框架自带健康检查机制。

## 8. 部署与运维

- **8.1. 构建与部署**:
    - `Makefile` 管理项目构建、测试、运行等命令:
        - `make all`: 执行代码格式化、lint、测试、构建。
        - `make build`: 构建二进制文件。
        - `make http` (或类似 `make run`): 启动服务。
    - Docker容器化部署。
    - CI/CD流水线实现自动化构建和部署。
- **8.2. 数据库迁移**:
    - 使用迁移工具（如 `migrate`, `gormigrate`）管理数据库表结构变更。
    - 迁移脚本纳入版本控制。
- **8.3. 优雅退出**:
    - Kratos应用支持优雅退出，确保在服务停止前处理完已有请求，释放资源。

## 9. 迭代计划考虑
本架构设计注重模块化和接口化，为PRD中提及的后续迭代功能（如详细任务管理、前端UI模版动态配置、升级类课程修改的完整方案）提供了良好的基础。
- **模块独立性**: 便于未来将特定模块（如批量处理、文件服务）拆分为微服务。
- **接口驱动**: 领域层和应用层的接口设计允许在不影响核心逻辑的情况下替换或升级基础设施层实现。
- **CQRS**: 使得读写模型可以独立演进和优化，适应更复杂的查询需求。
- **配置化**: UI模版等未来可配置项，可以通过扩展配置服务和领域模型来实现。

=========== To Be Continue ...... 
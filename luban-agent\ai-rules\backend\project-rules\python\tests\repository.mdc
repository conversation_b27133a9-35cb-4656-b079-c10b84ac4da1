---
description: 
globs: backend/tests/infrastructure/repositories/**/*.py
alwaysApply: false
---
# Repository 层测试代码生成规则

**核心原则：简洁优先**

## 边界
- 使用 SQLite 内存库或 `MagicMock Session`，不访问生产库  
- 关注数据映射、事务原子性、唯一约束、并发冲突

## 约定
- 类名 `Test{Entity}Repository`；方法 `test_{行为}_{场景}`  
- Fixture 提供 `mock_session`  
- 使用参数化测试覆盖查询边界  
- 事务：每用例结束回滚或断言 `commit/rollback` 调用  
- 覆盖率目标 ≥ 90 %

## 示例代码
```python
class TestUserRepository:
    """用户仓储测试"""
    
    @pytest.fixture
    def user_repository(self, test_db_session):
        """创建用户仓储"""
        return UserRepository(test_db_session)
    
    def test_save_and_get(self, user_repository):
        """测试保存和获取用户"""
        # 创建测试用户
        # 保存用户
        # 验证ID已分配
        assert saved_user.id is not None
        # 获取并验证
    
    def test_find_by_criteria(self, user_repository):
        """测试按条件查询"""
        # 准备测试数据
        # 执行查询
        # 验证结果
        pass
    
    def test_unique_constraint(self, user_repository):
        """测试唯一性约束"""
        # 创建并保存第一个用户
        # 创建具有相同唯一标识的第二个用户
        # 尝试保存应失败（用户名唯一性约束）
        # 使用相同邮箱也应失败
        pass
    
    def test_transaction_rollback(self, user_repository, test_db_session):
        """测试事务回滚"""
        # 开始事务
        # 创建并保存用户
        # 验证在当前事务中可见
        # 回滚事务
        test_db_session.rollback()
        # 验证用户不存在（回滚成功）
        assert user_repository.get_by_id(user_id) is None
    
    def test_complex_aggregate_persistence(self, order_repository):
        """测试复杂聚合持久化"""
        # 创建带有多个项目的订单聚合
        # 保存订单聚合
        # 重新加载订单聚合
        # 验证聚合完整性
        # 验证所有内部实体已正确持久化
        pass
```

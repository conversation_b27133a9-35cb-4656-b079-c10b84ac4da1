import { AuthToken } from '@/types/user';
import { API_BASE_URL } from '@/app/configs/api';

// 获取存储的令牌
const getToken = (): string | null => {
  if (typeof window === 'undefined') return null;
  return localStorage.getItem('auth_token');
};

// 设置令牌
export const setToken = (token: AuthToken): void => {
  if (typeof window === 'undefined') return;
  localStorage.setItem('auth_token', token.access_token);
};

// 清除令牌
export const clearToken = (): void => {
  if (typeof window === 'undefined') return;
  localStorage.removeItem('auth_token');
};

// 是否已经有令牌
export const hasToken = (): boolean => {
  return getToken() !== null;
};

// 创建请求选项
const createRequestOptions = (
  method: string,
  data?: any,
  isFormData: boolean = false
): RequestInit => {
  const token = getToken();
  const headers: HeadersInit = {};
  
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }
  
  if (!isFormData && data) {
    headers['Content-Type'] = 'application/json';
  }
  
  if (isFormData && !(data instanceof FormData)) {
    // 如果是URLSearchParams
    headers['Content-Type'] = 'application/x-www-form-urlencoded';
  }
  
  const options: RequestInit = {
    method,
    headers,
    credentials: 'include', // 包含cookies
  };
  
  if (data) {
    if (data instanceof FormData) {
      options.body = data;
    } else {
      options.body = typeof data === 'string' ? data : JSON.stringify(data);
    }
  }
  
  return options;
};

// 添加重试功能的辅助函数
const requestWithRetry = async <T>(
  url: string,
  options: RequestInit,
  maxRetries = 3,
  retryDelay = 2000
): Promise<Response> => {
  let lastError;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      console.log(`尝试请求 ${url} (尝试 ${attempt + 1}/${maxRetries})`);
      const response = await fetch(url, options);
      return response; // 成功则返回响应
    } catch (error) {
      console.error(`请求失败 (尝试 ${attempt + 1}/${maxRetries}):`, error);
      lastError = error;
      
      if (attempt < maxRetries - 1) {
        // 如果不是最后一次尝试，则等待后重试
        console.log(`等待 ${retryDelay}ms 后重试...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
        // 每次重试增加延迟时间
        retryDelay = retryDelay * 1.5;
      }
    }
  }
  
  // 所有重试都失败，抛出最后一个错误
  throw lastError;
};

// 请求封装
const request = async <T>(
  endpoint: string,
  method: string,
  data?: any,
  isFormData: boolean = false
): Promise<T> => {
  // 判断是否使用相对URL
  // 如果endpoint以/api开头，则使用相对URL，否则使用绝对URL
  const url = endpoint.startsWith('/api')
    ? endpoint
    : `${API_BASE_URL}${endpoint}`;
  
  console.log('API请求URL:', url);
  const options = createRequestOptions(method, data, isFormData);
  
  try {
    // 使用带重试的请求
    const response = await requestWithRetry(url, options);
    
    // 处理认证失败
    if (response.status === 401) {
      clearToken();
      if (typeof window !== 'undefined') {
        // 重定向到登录页
        window.location.href = '/auth/login';
      }
      throw {
        status: 401,
        code: 'authentication_failed',
        message: '认证失败，请重新登录',
      };
    }
    
    // 检查状态码
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw {
        status: response.status,
        ...(errorData.error || { message: '未知错误', code: 'unknown_error' }),
      };
    }
    
    // 检查响应内容类型
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    }
    
    return await response.text() as unknown as T;
  } catch (error) {
    console.error('API请求失败:', error);
    throw error;
  }
};

// 导出API方法
export const api = {
  get: <T>(endpoint: string): Promise<T> => 
    request<T>(endpoint, 'GET'),
  
  post: <T>(endpoint: string, data?: any, isFormData: boolean = false): Promise<T> => 
    request<T>(endpoint, 'POST', data, isFormData),
  
  put: <T>(endpoint: string, data?: any, isFormData: boolean = false): Promise<T> => 
    request<T>(endpoint, 'PUT', data, isFormData),
  
  delete: <T>(endpoint: string): Promise<T> => 
    request<T>(endpoint, 'DELETE'),
}; 
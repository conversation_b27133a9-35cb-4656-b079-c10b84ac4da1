openapi: 3.0.3
info:
  title: 运营工具老师学生课程题目反馈系统 API
  description: |
    提供完整的课程内容质量反馈闭环功能，支持教师端和学生端用户快速提交课程问题，
    通过智能Agent自动分类审核，生产后台高效处理，并及时反馈处理结果给用户，
    同时提供积分激励机制提升用户参与积极性。
    本文档遵循 OpenAPI 3.0.3 规范。
  version: v1.0.0
  contact:
    name: 反馈系统开发团队
    email: <EMAIL>

servers:
  - url: http://localhost:8080
    description: 本地开发服务器
  - url: https://api.feedback.example.com
    description: 生产环境服务器

tags:
  - name: FeedbackManagement
    description: 反馈管理相关接口
  - name: AgentAudit
    description: 智能审核相关接口
  - name: BackofficeManagement
    description: 生产后台管理相关接口
  - name: NotificationManagement
    description: 消息通知相关接口
  - name: PointManagement
    description: 积分管理相关接口
  - name: StatisticsQuery
    description: 统计查询相关接口

components:
  schemas:
    # 通用响应结构
    GenericResponse:
      type: object
      properties:
        code:
          type: integer
          description: 业务状态码，0表示成功，非0表示错误
          example: 0
        message:
          type: string
          description: 响应消息
          example: "Success"
        data:
          type: object
          description: 响应数据
          nullable: true
        pageInfo:
          $ref: '#/components/schemas/PageInfo'

    GenericError:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码
          example: 4000001
        message:
          type: string
          description: 用户可读的错误信息
          example: '无效的参数'
        details:
          type: string
          description: 可选的详细错误信息或调试信息
          nullable: true
          example: '字段 [feedbackTypes] 不能为空'
        data:
          type: object
          description: 可选的附加数据
          nullable: true

    PageInfo:
      type: object
      description: 分页信息
      properties:
        page:
          type: integer
          description: 当前页码
          example: 1
        pageSize:
          type: integer
          description: 每页数量
          example: 20
        total:
          type: integer
          description: 总记录数
          example: 100

    # 反馈类型配置
    FeedbackType:
      type: object
      description: 反馈类型配置
      properties:
        typeName:
          type: string
          description: 反馈类型名称
          example: "课程反馈"
        typeCode:
          type: string
          description: 反馈类型编码
          example: "course_feedback"
        userType:
          type: string
          description: 适用用户类型
          enum: ["teacher", "student"]
          example: "teacher"
        isMultipleSelect:
          type: boolean
          description: 是否支持多选
          example: true
        description:
          type: string
          description: 类型描述
          example: "针对课程内容的反馈"

    FeedbackTypesResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/FeedbackType'

    # 反馈记录
    CreateFeedbackRequest:
      type: object
      description: 创建反馈请求
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1001
        userType:
          type: string
          description: 用户类型
          enum: ["teacher", "student"]
          example: "teacher"
        feedbackTypes:
          type: array
          description: 反馈类型数组（支持多选）
          items:
            type: string
          example: ["课程反馈", "题目反馈"]
        contentType:
          type: string
          description: 内容类型
          enum: ["text", "non_text"]
          example: "text"
        courseId:
          type: integer
          format: int64
          description: 关联课程ID（可选）
          nullable: true
          example: 2001
        questionId:
          type: integer
          format: int64
          description: 关联题目ID（可选）
          nullable: true
          example: 3001
        feedbackContent:
          type: string
          description: 反馈内容描述
          example: "课程中第3页的板书文字有错误，应该是'函数'而不是'涵数'"
        screenshotUrls:
          type: array
          description: 截图URL数组
          items:
            type: string
          example: ["https://cdn.example.com/screenshot1.jpg"]
        priorityLevel:
          type: integer
          description: 优先级等级
          enum: [1, 2, 3]
          example: 2
        sourcePlatform:
          type: string
          description: 来源平台
          enum: ["web", "mobile", "app"]
          example: "web"
      required:
        - userId
        - userType
        - feedbackTypes
        - feedbackContent

    FeedbackRecord:
      type: object
      description: 反馈记录
      properties:
        id:
          type: integer
          format: int64
          description: 反馈ID
          readOnly: true
          example: 10001
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1001
        userType:
          type: string
          description: 用户类型
          example: "teacher"
        feedbackTypes:
          type: array
          description: 反馈类型数组
          items:
            type: string
          example: ["课程反馈", "题目反馈"]
        contentType:
          type: string
          description: 内容类型
          example: "text"
        courseId:
          type: integer
          format: int64
          description: 关联课程ID
          nullable: true
          example: 2001
        questionId:
          type: integer
          format: int64
          description: 关联题目ID
          nullable: true
          example: 3001
        feedbackContent:
          type: string
          description: 反馈内容
          example: "课程中第3页的板书文字有错误"
        screenshotUrls:
          type: array
          description: 截图URL数组
          items:
            type: string
          example: ["https://cdn.example.com/screenshot1.jpg"]
        feedbackStatus:
          type: string
          description: 反馈状态
          enum: ["pending", "processing", "accepted", "rejected"]
          example: "pending"
        priorityLevel:
          type: integer
          description: 优先级等级
          example: 2
        sourcePlatform:
          type: string
          description: 来源平台
          example: "web"
        createdAt:
          type: integer
          format: int64
          description: 创建时间（UTC秒数）
          readOnly: true
          example: 1717027200
        updatedAt:
          type: integer
          format: int64
          description: 更新时间（UTC秒数）
          readOnly: true
          example: 1717027200

    CreateFeedbackResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                feedbackId:
                  type: integer
                  format: int64
                  description: 反馈ID
                  example: 10001
                submitStatus:
                  type: string
                  description: 提交状态
                  example: "success"
                estimatedProcessTime:
                  type: string
                  description: 预估处理时间
                  example: "24小时内"

    # 截图上传
    UploadScreenshotRequest:
      type: object
      description: 截图上传请求
      properties:
        feedbackTempId:
          type: string
          description: 反馈临时ID
          example: "temp_feedback_123456"
        imageFile:
          type: string
          format: binary
          description: 图片文件
      required:
        - feedbackTempId
        - imageFile

    UploadScreenshotResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                imageUrl:
                  type: string
                  description: 图片URL
                  example: "https://cdn.example.com/screenshot_123456.jpg"
                uploadStatus:
                  type: string
                  description: 上传状态
                  example: "success"

    # Agent审核
    CreateAgentAuditRequest:
      type: object
      description: Agent审核请求
      properties:
        feedbackId:
          type: integer
          format: int64
          description: 反馈ID
          example: 10001
        feedbackType:
          type: string
          description: 反馈类型
          example: "课程反馈"
        feedbackContent:
          type: string
          description: 反馈内容
          example: "课程中第3页的板书文字有错误"
      required:
        - feedbackId
        - feedbackType
        - feedbackContent

    AgentAuditRecord:
      type: object
      description: Agent审核记录
      properties:
        id:
          type: integer
          format: int64
          description: 审核ID
          readOnly: true
          example: 20001
        feedbackId:
          type: integer
          format: int64
          description: 反馈ID
          example: 10001
        workflowType:
          type: string
          description: 工作流类型
          enum: ["dify1_workflow", "dify2_workflow", "dify3_workflow"]
          example: "dify1_workflow"
        auditResult:
          type: string
          description: 审核结果
          enum: ["valid_feedback", "invalid_feedback", "need_manual_review"]
          example: "valid_feedback"
        confidenceScore:
          type: number
          format: float
          description: 置信度分数（0.0-1.0）
          example: 0.8756
        auditDetails:
          type: string
          description: 审核详细分析
          example: "检测到板书文字错误，建议修正为'函数'"
        processingTimeMs:
          type: integer
          description: 处理耗时（毫秒）
          example: 3500
        auditCategories:
          type: array
          description: 审核分类结果
          items:
            type: string
          example: ["文字错误", "板书问题"]
        createdAt:
          type: integer
          format: int64
          description: 创建时间（UTC秒数）
          readOnly: true
          example: 1717027200

    CreateAgentAuditResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                auditId:
                  type: integer
                  format: int64
                  description: 审核ID
                  example: 20001
                processingStatus:
                  type: string
                  description: 处理状态
                  example: "processing"

    # 生产后台反馈管理
    BackofficeFeedbackListResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                allOf:
                  - $ref: '#/components/schemas/FeedbackRecord'
                  - type: object
                    properties:
                      agentAuditResult:
                        $ref: '#/components/schemas/AgentAuditRecord'
                      relatedContent:
                        type: object
                        description: 关联内容信息
                        properties:
                          courseName:
                            type: string
                            example: "高中数学函数专题"
                          questionContent:
                            type: string
                            example: "求函数f(x)=x²+2x+1的最值"

    # 反馈处理操作
    CreateFeedbackProcessingRequest:
      type: object
      description: 创建反馈处理请求
      properties:
        feedbackId:
          type: integer
          format: int64
          description: 反馈ID
          example: 10001
        processorId:
          type: integer
          format: int64
          description: 处理人员ID
          example: 5001
        processingStage:
          type: string
          description: 处理阶段
          enum: ["initial", "reviewing", "modifying", "completed"]
          example: "initial"
      required:
        - feedbackId
        - processorId

    UpdateFeedbackProcessingRequest:
      type: object
      description: 更新反馈处理请求
      properties:
        processingResult:
          type: boolean
          description: 处理结果（true-采纳，false-未采纳）
          example: true
        processingReason:
          type: string
          description: 处理原因说明
          example: "确实存在文字错误，已修正"
        modificationContent:
          type: string
          description: 修改内容描述
          example: "将'涵数'修正为'函数'"
        processingStage:
          type: string
          description: 处理阶段
          enum: ["initial", "reviewing", "modifying", "completed"]
          example: "completed"
        processingNotes:
          type: string
          description: 处理备注
          example: "已完成修改并上线"

    FeedbackProcessingRecord:
      type: object
      description: 反馈处理记录
      properties:
        id:
          type: integer
          format: int64
          description: 处理记录ID
          readOnly: true
          example: 30001
        feedbackId:
          type: integer
          format: int64
          description: 反馈ID
          example: 10001
        processorId:
          type: integer
          format: int64
          description: 处理人员ID
          example: 5001
        processingResult:
          type: boolean
          description: 处理结果
          nullable: true
          example: true
        processingReason:
          type: string
          description: 处理原因
          example: "确实存在文字错误，已修正"
        modificationContent:
          type: string
          description: 修改内容
          example: "将'涵数'修正为'函数'"
        processingStage:
          type: string
          description: 处理阶段
          example: "completed"
        actualCompletionTime:
          type: integer
          format: int64
          description: 实际完成时间（UTC秒数）
          nullable: true
          example: 1717113600
        feishuRecordId:
          type: string
          description: 飞书记录ID
          example: "rec_123456"
        createdAt:
          type: integer
          format: int64
          description: 创建时间（UTC秒数）
          readOnly: true
          example: 1717027200

    CreateFeedbackProcessingResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                processingId:
                  type: integer
                  format: int64
                  description: 处理记录ID
                  example: 30001
                createStatus:
                  type: string
                  description: 创建状态
                  example: "success"

    # 飞书同步
    FeishuSyncRequest:
      type: object
      description: 飞书同步请求
      properties:
        feedbackId:
          type: integer
          format: int64
          description: 反馈ID
          example: 10001
        processingStatus:
          type: string
          description: 处理状态
          example: "completed"
        feishuRecordId:
          type: string
          description: 飞书记录ID
          example: "rec_123456"
      required:
        - feedbackId
        - processingStatus

    FeishuSyncResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                syncStatus:
                  type: string
                  description: 同步状态
                  example: "success"
                feishuRecordUpdateResult:
                  type: string
                  description: 飞书记录更新结果
                  example: "updated"

    # 消息通知
    NotificationMessage:
      type: object
      description: 消息通知
      properties:
        id:
          type: integer
          format: int64
          description: 消息ID
          readOnly: true
          example: 40001
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1001
        feedbackId:
          type: integer
          format: int64
          description: 关联反馈ID
          nullable: true
          example: 10001
        notificationType:
          type: string
          description: 消息类型
          enum: ["feedback_confirm", "processing_result", "point_reward", "system_notice"]
          example: "processing_result"
        notificationTitle:
          type: string
          description: 消息标题
          example: "反馈处理结果通知"
        notificationContent:
          type: string
          description: 消息内容
          example: "您的反馈已被采纳，获得10积分奖励"
        sendChannel:
          type: string
          description: 发送渠道
          enum: ["app", "email", "sms", "feishu"]
          example: "app"
        sendStatus:
          type: string
          description: 发送状态
          enum: ["pending", "sent", "failed", "retry"]
          example: "sent"
        isRead:
          type: boolean
          description: 是否已读
          example: false
        readTime:
          type: integer
          format: int64
          description: 阅读时间（UTC秒数）
          nullable: true
          example: 1717113600
        createdAt:
          type: integer
          format: int64
          description: 创建时间（UTC秒数）
          readOnly: true
          example: 1717027200

    NotificationMessagesResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                messages:
                  type: array
                  items:
                    $ref: '#/components/schemas/NotificationMessage'
                unreadCount:
                  type: integer
                  description: 未读消息数量
                  example: 3

    SendNotificationRequest:
      type: object
      description: 发送通知请求
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1001
        notificationType:
          type: string
          description: 通知类型
          enum: ["feedback_confirm", "processing_result", "point_reward", "system_notice"]
          example: "processing_result"
        notificationContent:
          type: string
          description: 通知内容
          example: "您的反馈已被采纳"
        relatedFeedbackId:
          type: integer
          format: int64
          description: 关联反馈ID
          nullable: true
          example: 10001
      required:
        - userId
        - notificationType
        - notificationContent

    SendNotificationResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                notificationId:
                  type: integer
                  format: int64
                  description: 通知ID
                  example: 40001
                sendStatus:
                  type: string
                  description: 发送状态
                  example: "sent"

    # 积分管理
    PointRecord:
      type: object
      description: 积分记录
      properties:
        id:
          type: integer
          format: int64
          description: 积分记录ID
          readOnly: true
          example: 50001
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1001
        feedbackId:
          type: integer
          format: int64
          description: 关联反馈ID
          example: 10001
        pointAmount:
          type: integer
          description: 积分数量
          example: 10
        pointType:
          type: string
          description: 积分类型
          enum: ["feedback_reward", "bonus", "penalty", "consumption"]
          example: "feedback_reward"
        severityLevel:
          type: string
          description: 问题严重程度
          enum: ["minor", "normal", "serious"]
          example: "normal"
        distributorId:
          type: integer
          format: int64
          description: 下发人员ID
          example: 6001
        distributionReason:
          type: string
          description: 下发原因
          example: "有效反馈奖励"
        currentBalance:
          type: integer
          description: 当前积分余额
          example: 150
        createdAt:
          type: integer
          format: int64
          description: 创建时间（UTC秒数）
          readOnly: true
          example: 1717027200

    CalculatePointsRequest:
      type: object
      description: 积分计算请求
      properties:
        feedbackId:
          type: integer
          format: int64
          description: 反馈ID
          example: 10001
        severityLevel:
          type: string
          description: 问题严重程度
          enum: ["minor", "normal", "serious"]
          example: "normal"
        feedbackType:
          type: string
          description: 反馈类型
          example: "课程反馈"
      required:
        - feedbackId
        - severityLevel
        - feedbackType

    CalculatePointsResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                suggestedPointAmount:
                  type: integer
                  description: 建议积分数量
                  example: 10
                calculationRule:
                  type: string
                  description: 计算规则说明
                  example: "课程反馈-一般问题：基础分5分+严重程度加分5分"

    DistributePointsRequest:
      type: object
      description: 积分下发请求
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 1001
        feedbackId:
          type: integer
          format: int64
          description: 反馈ID
          example: 10001
        pointAmount:
          type: integer
          description: 积分数量
          example: 10
        distributionReason:
          type: string
          description: 下发原因
          example: "有效反馈奖励"
        operatorId:
          type: integer
          format: int64
          description: 操作人员ID
          example: 6001
      required:
        - userId
        - feedbackId
        - pointAmount
        - operatorId

    DistributePointsResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                pointRecordId:
                  type: integer
                  format: int64
                  description: 积分记录ID
                  example: 50001
                distributeStatus:
                  type: string
                  description: 下发状态
                  example: "success"
                currentBalance:
                  type: integer
                  description: 当前积分余额
                  example: 160

    PointBalanceResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                currentBalance:
                  type: integer
                  description: 当前积分余额
                  example: 150
                recentChanges:
                  type: array
                  description: 最近变动
                  items:
                    type: object
                    properties:
                      amount:
                        type: integer
                        example: 10
                      reason:
                        type: string
                        example: "反馈奖励"
                      time:
                        type: integer
                        format: int64
                        example: 1717027200

    PointRecordsResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: array
              items:
                $ref: '#/components/schemas/PointRecord'

    # 统计数据
    StatisticsResponse:
      allOf:
        - $ref: '#/components/schemas/GenericResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                feedbackStatistics:
                  type: object
                  description: 反馈统计数据
                  properties:
                    totalCount:
                      type: integer
                      description: 总反馈数
                      example: 1250
                    pendingCount:
                      type: integer
                      description: 待处理数
                      example: 45
                    processedCount:
                      type: integer
                      description: 已处理数
                      example: 1205
                    acceptedRate:
                      type: number
                      format: float
                      description: 采纳率
                      example: 0.78
                pointStatistics:
                  type: object
                  description: 积分统计数据
                  properties:
                    totalDistributed:
                      type: integer
                      description: 总下发积分
                      example: 12500
                    activeUsers:
                      type: integer
                      description: 活跃用户数
                      example: 320
                    averagePoints:
                      type: number
                      format: float
                      description: 平均积分
                      example: 39.06
                trendAnalysis:
                  type: object
                  description: 趋势分析
                  properties:
                    dailyFeedbackTrend:
                      type: array
                      items:
                        type: object
                        properties:
                          date:
                            type: string
                            example: "2025-05-30"
                          count:
                            type: integer
                            example: 25

  parameters:
    # 通用查询参数
    pageQueryParameter:
      name: page
      in: query
      description: 页码（从1开始）
      required: false
      schema:
        type: integer
        default: 1
        minimum: 1

    pageSizeQueryParameter:
      name: pageSize
      in: query
      description: 每页数量
      required: false
      schema:
        type: integer
        default: 20
        minimum: 1
        maximum: 100

    sortByQueryParameter:
      name: sortBy
      in: query
      description: 排序字段
      required: false
      schema:
        type: string
        default: "createdAt"

    sortOrderQueryParameter:
      name: sortOrder
      in: query
      description: 排序方式（asc或desc）
      required: false
      schema:
        type: string
        enum: ["asc", "desc"]
        default: "desc"

    userTypeQueryParameter:
      name: userType
      in: query
      description: 用户类型
      required: false
      schema:
        type: string
        enum: ["teacher", "student"]

    feedbackIdQueryParameter:
      name: feedbackId
      in: query
      description: 反馈ID
      required: true
      schema:
        type: integer
        format: int64

    userIdQueryParameter:
      name: userId
      in: query
      description: 用户ID
      required: false
      schema:
        type: integer
        format: int64

    statusQueryParameter:
      name: status
      in: query
      description: 状态筛选
      required: false
      schema:
        type: string

    messageIdQueryParameter:
      name: messageId
      in: query
      description: 消息ID
      required: true
      schema:
        type: integer
        format: int64

    statisticsTypeQueryParameter:
      name: statisticsType
      in: query
      description: 统计类型
      required: true
      schema:
        type: string
        enum: ["feedback", "points", "comprehensive"]

    timeRangeStartQueryParameter:
      name: timeRangeStart
      in: query
      description: 时间范围开始（UTC秒数）
      required: false
      schema:
        type: integer
        format: int64

    timeRangeEndQueryParameter:
      name: timeRangeEnd
      in: query
      description: 时间范围结束（UTC秒数）
      required: false
      schema:
        type: integer
        format: int64

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "请输入JWT Token，格式为：Bearer {token}"

  responses:
    UnauthorizedError:
      description: 未认证或认证令牌无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4010001
                message: "需要认证或认证失败"

    ForbiddenError:
      description: 无权限访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4030001
                message: "无权限执行此操作"

    NotFoundError:
      description: 请求的资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4040001
                message: "请求的资源不存在"

    BadRequestError:
      description: 请求参数无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4000001
                message: "请求参数无效"

paths:
  /api/v1/feedback/types:
    get:
      tags:
        - FeedbackManagement
      summary: 获取反馈类型配置
      description: 根据用户类型获取可用的反馈类型列表，支持多选配置
      operationId: getFeedbackTypes
      parameters:
        - $ref: '#/components/parameters/userTypeQueryParameter'
      responses:
        '200':
          description: 成功获取反馈类型列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeedbackTypesResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - BearerAuth: []

  /api/v1/feedback/records:
    post:
      tags:
        - FeedbackManagement
      summary: 提交反馈记录
      description: 用户提交反馈记录，支持多选反馈类型，自动触发智能审核流程
      operationId: createFeedbackRecord
      requestBody:
        description: 反馈记录信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFeedbackRequest'
            examples:
              teacherFeedback:
                summary: 教师课程反馈示例
                value:
                  userId: 1001
                  userType: "teacher"
                  feedbackTypes: ["课程反馈", "题目反馈"]
                  contentType: "text"
                  courseId: 2001
                  feedbackContent: "课程中第3页的板书文字有错误，应该是'函数'而不是'涵数'"
                  screenshotUrls: ["https://cdn.example.com/screenshot1.jpg"]
                  priorityLevel: 2
                  sourcePlatform: "web"
              studentFeedback:
                summary: 学生题目反馈示例
                value:
                  userId: 1002
                  userType: "student"
                  feedbackTypes: ["题目反馈"]
                  contentType: "text"
                  questionId: 3001
                  feedbackContent: "题目答案选项B有错误"
                  priorityLevel: 1
                  sourcePlatform: "mobile"
      responses:
        '200':
          description: 反馈提交成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateFeedbackResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - BearerAuth: []

  /api/v1/feedback/screenshots:
    post:
      tags:
        - FeedbackManagement
      summary: 上传反馈截图
      description: 上传反馈相关的截图文件，支持多图片上传
      operationId: uploadFeedbackScreenshot
      requestBody:
        description: 截图文件
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UploadScreenshotRequest'
      responses:
        '200':
          description: 截图上传成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UploadScreenshotResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - BearerAuth: []

  /api/v1/agent/audit:
    post:
      tags:
        - AgentAudit
      summary: 触发Agent智能审核
      description: 对文字类反馈内容进行智能审核，调用相应的Dify工作流进行分析
      operationId: createAgentAudit
      requestBody:
        description: 审核请求信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateAgentAuditRequest'
            examples:
              default:
                value:
                  feedbackId: 10001
                  feedbackType: "课程反馈"
                  feedbackContent: "课程中第3页的板书文字有错误"
      responses:
        '200':
          description: 审核任务创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateAgentAuditResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - BearerAuth: []

  /api/v1/backoffice/feedback:
    get:
      tags:
        - BackofficeManagement
      summary: 查询反馈列表
      description: 生产后台查询反馈列表，支持多维度筛选、分页和排序
      operationId: getBackofficeFeedbackList
      parameters:
        - $ref: '#/components/parameters/pageQueryParameter'
        - $ref: '#/components/parameters/pageSizeQueryParameter'
        - $ref: '#/components/parameters/sortByQueryParameter'
        - $ref: '#/components/parameters/sortOrderQueryParameter'
        - $ref: '#/components/parameters/statusQueryParameter'
        - $ref: '#/components/parameters/userTypeQueryParameter'
        - $ref: '#/components/parameters/feedbackIdQueryParameter'
      responses:
        '200':
          description: 成功获取反馈列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BackofficeFeedbackListResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
      security:
        - BearerAuth: []

  /api/v1/backoffice/feedback/processing:
    post:
      tags:
        - BackofficeManagement
      summary: 创建反馈处理记录
      description: 生产人员开始处理反馈时创建处理记录
      operationId: createFeedbackProcessing
      requestBody:
        description: 处理记录信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateFeedbackProcessingRequest'
            examples:
              default:
                value:
                  feedbackId: 10001
                  processorId: 5001
                  processingStage: "initial"
      responses:
        '200':
          description: 处理记录创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateFeedbackProcessingResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
      security:
        - BearerAuth: []

    put:
      tags:
        - BackofficeManagement
      summary: 更新反馈处理记录
      description: 更新反馈处理状态和结果
      operationId: updateFeedbackProcessing
      parameters:
        - $ref: '#/components/parameters/feedbackIdQueryParameter'
      requestBody:
        description: 更新的处理信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateFeedbackProcessingRequest'
            examples:
              accepted:
                summary: 反馈被采纳
                value:
                  processingResult: true
                  processingReason: "确实存在文字错误，已修正"
                  modificationContent: "将'涵数'修正为'函数'"
                  processingStage: "completed"
                  processingNotes: "已完成修改并上线"
              rejected:
                summary: 反馈未被采纳
                value:
                  processingResult: false
                  processingReason: "经核实，该内容无误"
                  processingStage: "completed"
      responses:
        '200':
          description: 处理记录更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /api/v1/backoffice/feishu/sync:
    post:
      tags:
        - BackofficeManagement
      summary: 同步数据到飞书多维表格
      description: 将反馈处理数据同步到飞书多维表格，保持数据一致性
      operationId: syncToFeishu
      requestBody:
        description: 同步请求信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FeishuSyncRequest'
            examples:
              default:
                value:
                  feedbackId: 10001
                  processingStatus: "completed"
                  feishuRecordId: "rec_123456"
      responses:
        '200':
          description: 同步成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FeishuSyncResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
      security:
        - BearerAuth: []

  /api/v1/notifications/messages:
    get:
      tags:
        - NotificationManagement
      summary: 获取用户消息列表
      description: 查询用户的消息通知列表，支持分页和类型筛选
      operationId: getUserMessages
      parameters:
        - $ref: '#/components/parameters/pageQueryParameter'
        - $ref: '#/components/parameters/pageSizeQueryParameter'
        - name: messageType
          in: query
          description: 消息类型筛选
          required: false
          schema:
            type: string
            enum: ["feedback_confirm", "processing_result", "point_reward", "system_notice"]
      responses:
        '200':
          description: 成功获取消息列表
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/NotificationMessagesResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - BearerAuth: []

    put:
      tags:
        - NotificationManagement
      summary: 标记消息已读
      description: 将指定消息标记为已读状态
      operationId: markMessageAsRead
      parameters:
        - $ref: '#/components/parameters/messageIdQueryParameter'
      responses:
        '200':
          description: 消息已读状态更新成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /api/v1/notifications/send:
    post:
      tags:
        - NotificationManagement
      summary: 发送处理结果通知
      description: 向用户发送反馈处理结果通知，支持多渠道推送
      operationId: sendNotification
      requestBody:
        description: 通知发送请求
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SendNotificationRequest'
            examples:
              processingResult:
                summary: 处理结果通知
                value:
                  userId: 1001
                  notificationType: "processing_result"
                  notificationContent: "您的反馈已被采纳，获得10积分奖励"
                  relatedFeedbackId: 10001
              pointReward:
                summary: 积分奖励通知
                value:
                  userId: 1001
                  notificationType: "point_reward"
                  notificationContent: "恭喜您获得10积分奖励"
                  relatedFeedbackId: 10001
      responses:
        '200':
          description: 通知发送成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SendNotificationResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - BearerAuth: []

  /api/v1/points:
    get:
      tags:
        - PointManagement
      summary: 查询积分信息
      description: 查询用户积分余额或积分记录历史
      operationId: getPointsInfo
      parameters:
        - $ref: '#/components/parameters/userIdQueryParameter'
        - name: queryType
          in: query
          description: 查询类型
          required: true
          schema:
            type: string
            enum: ["balance", "records"]
        - $ref: '#/components/parameters/pageQueryParameter'
        - $ref: '#/components/parameters/pageSizeQueryParameter'
        - $ref: '#/components/parameters/timeRangeStartQueryParameter'
        - $ref: '#/components/parameters/timeRangeEndQueryParameter'
      responses:
        '200':
          description: 成功获取积分信息
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/PointBalanceResponse'
                  - $ref: '#/components/schemas/PointRecordsResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - BearerAuth: []

    post:
      tags:
        - PointManagement
      summary: 积分操作
      description: 执行积分相关操作，包括积分计算和积分下发
      operationId: pointsOperation
      parameters:
        - name: operationType
          in: query
          description: 操作类型
          required: true
          schema:
            type: string
            enum: ["calculate", "distribute"]
      requestBody:
        description: 积分操作请求
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/CalculatePointsRequest'
                - $ref: '#/components/schemas/DistributePointsRequest'
            examples:
              calculate:
                summary: 积分计算示例
                value:
                  feedbackId: 10001
                  severityLevel: "normal"
                  feedbackType: "课程反馈"
              distribute:
                summary: 积分下发示例
                value:
                  userId: 1001
                  feedbackId: 10001
                  pointAmount: 10
                  distributionReason: "有效反馈奖励"
                  operatorId: 6001
      responses:
        '200':
          description: 积分操作成功
          content:
            application/json:
              schema:
                oneOf:
                  - $ref: '#/components/schemas/CalculatePointsResponse'
                  - $ref: '#/components/schemas/DistributePointsResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
      security:
        - BearerAuth: []

  /api/v1/statistics:
    get:
      tags:
        - StatisticsQuery
      summary: 查询统计分析数据
      description: 查询反馈处理、积分下发等各类统计分析数据，支持多维度分析
      operationId: getStatistics
      parameters:
        - $ref: '#/components/parameters/statisticsTypeQueryParameter'
        - $ref: '#/components/parameters/timeRangeStartQueryParameter'
        - $ref: '#/components/parameters/timeRangeEndQueryParameter'
        - $ref: '#/components/parameters/userIdQueryParameter'
        - name: statisticsDimension
          in: query
          description: 统计维度
          required: false
          schema:
            type: string
            enum: ["daily", "weekly", "monthly"]
            default: "daily"
      responses:
        '200':
          description: 成功获取统计数据
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StatisticsResponse'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
      security:
        - BearerAuth: []
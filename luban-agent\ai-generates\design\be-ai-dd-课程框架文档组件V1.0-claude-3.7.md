# 数据库设计文档 - 课程框架文档组件 - V1.0

**最后更新日期**: 2025-05-22
**设计负责人**: Claude-3.7
**评审人**: 待填写
**状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人      | 修订内容摘要 |
| ------ | ---------- | ----------- | ------------ |
| V1.0   | 2025-05-22 | Claude-3.7  | 初始草稿     |

## 1. 项目概述与设计目标

### 1.1 项目背景与范围

课程框架文档组件是AI课程产品的核心组成部分，旨在提供流畅、沉浸式的课中体验，让上课过程更接近1v1真人直播课。本项目主要包括课程框架和文档组件两个核心功能模块，支持学生在AI课程中进行学习。课程框架负责整体课程结构的管理，包括课程开场页、结算页的内容生成，以及协调各组件服务之间的交互和流转。文档组件则负责提供课程内容的动态展示，支持跟随模式与自由模式的切换，以及内容交互功能。

本数据库设计的范围包括：课程框架核心数据结构、文档组件数据结构、学习进度记录、学习报告数据以及用户反馈数据的存储和管理。

### 1.2 设计目标回顾

* 提供完整的数据库创建语句。
* 提供完整的、符合规范的表创建语句（DDL）。
* 清晰描述表间关系，并提供 ER 图。
* 详细说明核心用户场景/用户故事所涉及的表以及其 CRUD 操作方式。
* 阐述关键表的索引策略及其设计原因。
* 对核心表进行数据量预估和增长趋势分析。
* 明确数据生命周期管理/归档策略。
* 记录特殊设计考量与权衡。
* 总结设计如何体现对应数据库（PostgreSQL/ClickHouse）的关键规范。
## 2. 数据库环境与总体说明

### 2.1 目标数据库类型

* PostgreSQL 15.x：用于存储课程结构、学习进度、用户反馈等结构化数据
* ClickHouse 23.x：用于存储学习行为日志、交互事件等分析型数据

### 2.2 数据库创建语句 (如果适用)

**PostgreSQL**:
```sql
CREATE DATABASE db_course_framework
WITH
OWNER = course_admin
ENCODING = 'UTF8'
LC_COLLATE = 'zh_CN.UTF-8'
LC_CTYPE = 'zh_CN.UTF-8'
TABLESPACE = pg_default
CONNECTION LIMIT = -1
TEMPLATE = template0;
COMMENT ON DATABASE db_course_framework IS 'AI课程框架与文档组件数据库';
```

**ClickHouse**:
```sql
CREATE DATABASE IF NOT EXISTS db_course_logs
ENGINE = Atomic;
COMMENT '课程学习行为日志数据库';
```

### 2.3 通用设计原则遵循情况

本次数据库设计遵循以下通用设计原则：

1. **单一职责原则**：每张表只表示一个明确的实体，如课程表、组件表、学习记录表等，避免将多个逻辑混在一张表中。

2. **数据隔离原则**：严格按照业务逻辑对表进行划分，如课程表、组件表、学习记录表分开，避免在单张表中混合多种数据类型。

3. **高内聚低耦合**：确保每张表内部的数据高度相关，表间耦合度降至最低。例如，课程表只存储课程信息，组件表只存储组件信息，通过逻辑外键实现耦合。

4. **规范化与反规范化平衡**：
   - 对于PostgreSQL表，采用较高的规范化程度（符合第三范式），以减少数据冗余、保证数据一致性。
   - 对于ClickHouse表，采用宽表设计，适度反规范化，以优化查询性能，减少JOIN操作。

5. **数据访问模式考量**：
   - 高频共同查询的字段放在同一张表中，避免JOIN。例如，在学习记录表中包含了用户ID、课程ID、组件ID等关键信息，便于快速查询用户的学习状态。
   - 更新频率差异大的字段分表存储。例如，将用户学习进度和学习报告分开存储，因为进度更新频繁，而报告生成频率较低。

6. **数据冗余与一致性权衡**：
   - 在ClickHouse的学习行为日志表中，适度冗余存储了用户ID、课程ID、组件ID等维度信息，以减少JOIN，提高查询性能。
   - 在PostgreSQL表中，尽量避免冗余，通过逻辑外键保持数据一致性。

### 2.4 命名规范总览

* **PostgreSQL表命名**：使用`tbl_模块名_实体名`格式，如`tbl_course_courses`。
* **ClickHouse表命名**：使用`tbl_模块名_实体名`格式，如`tbl_learning_action_logs`。
* **字段命名**：统一使用`snake_case`（小写字母和下划线），力求简洁、明确，避免不必要的缩写。
* **索引命名**：
  * 单字段普通索引: `idx_{表名}_{字段名}`（例如 `idx_course_courses_status`）
  * 单字段唯一索引: `uk_{表名}_{字段名}`（例如 `uk_course_courses_course_code`）
  * 复合索引: `idx_{表名}_{字段1}_{字段2}`或`uk_{表名}_{字段1}_{字段2}`
* **注释**：所有表和关键字段都添加清晰、准确的中文注释。
## 3. 数据库表详细设计

### 3.1 数据库表清单

#### PostgreSQL表清单
| 表名 | 核心功能/用途简述 | 预定数据库类型 |
|------|------------------|--------------|
| `tbl_course_courses` | 存储课程基本信息，包括课程ID、名称、描述等 | PostgreSQL |
| `tbl_course_components` | 存储课程组件基本信息，包括组件ID、类型、序号等 | PostgreSQL |
| `tbl_course_document_components` | 存储文档组件详细信息，包括JS动画内容、视频URL等 | PostgreSQL |
| `tbl_course_exercise_components` | 存储练习组件详细信息，包括题目数量、题目类型等 | PostgreSQL |
| `tbl_course_qa_components` | 存储答疑组件详细信息，包括挂载组件ID、会话上下文等 | PostgreSQL |
| `tbl_course_ip_roles` | 存储IP角色信息，包括名称、形象描述、动效资源等 | PostgreSQL |
| `tbl_user_learning_records` | 存储用户学习记录，包括学习时长、答题数量、正确率等 | PostgreSQL |
| `tbl_user_mastery_records` | 存储用户知识点掌握度记录，包括掌握度值、目标掌握度等 | PostgreSQL |

#### ClickHouse表清单
| 表名 | 核心功能/用途简述 | 预定数据库类型 |
|------|------------------|--------------|
| `tbl_learning_action_logs` | 记录用户学习行为日志，包括事件时间、用户ID、行为类型等 | ClickHouse |
| `tbl_learning_interaction_events` | 记录用户与文档组件的交互事件，包括双击、长按等操作 | ClickHouse |
| `tbl_learning_answer_logs` | 记录用户答题日志，包括题目ID、答案、是否正确等 | ClickHouse |
| `tbl_learning_feedback_logs` | 记录用户反馈信息，包括评分、标签、文本内容等 | ClickHouse |
---

### 3.2 表名: `tbl_course_courses`

#### 3.2.1 表功能说明
存储课程的基本信息，包括课程ID、章节信息、课程序号、课程名称、描述性文案、学科ID、开场页配置、结算页配置等。作为课程框架的核心表，它定义了一节课的整体结构和基本属性。

#### 3.2.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_course_courses (
  id BIGSERIAL PRIMARY KEY,
  course_code VARCHAR(50) NOT NULL, -- 课程编码，业务唯一标识
  chapter_info VARCHAR(100) NOT NULL, -- 章节信息，如"第一章"
  course_sequence VARCHAR(20) NOT NULL, -- 课程序号，如"2.1.1"
  course_name VARCHAR(100) NOT NULL, -- 课程名称，取对应业务树末级知识点名称
  description TEXT, -- 课程的简短描述
  subject_id BIGINT NOT NULL, -- 学科ID
  opening_bg_image VARCHAR(255), -- 开场页背景图URL
  opening_color_value VARCHAR(20), -- 开场页色值
  ip_role_id BIGINT, -- IP角色ID
  ip_animation_url VARCHAR(255), -- 课程开场的IP动效资源URL
  audio_url VARCHAR(255), -- 课程开场的音频资源URL
  standard_duration INTEGER NOT NULL DEFAULT 0, -- 课程的标准学习时长(秒)
  total_components INTEGER NOT NULL DEFAULT 0, -- 一节课中组件的总数量
  status SMALLINT NOT NULL DEFAULT 1, -- 状态(0:禁用, 1:启用)
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_course_courses IS 'AI课程基本信息表 - 存储课程的基本信息和配置';
COMMENT ON COLUMN tbl_course_courses.id IS '主键ID';
COMMENT ON COLUMN tbl_course_courses.course_code IS '课程编码，业务唯一标识';
COMMENT ON COLUMN tbl_course_courses.chapter_info IS '章节信息，如"第一章"';
COMMENT ON COLUMN tbl_course_courses.course_sequence IS '课程序号，如"2.1.1"';
COMMENT ON COLUMN tbl_course_courses.course_name IS '课程名称，取对应业务树末级知识点名称';
COMMENT ON COLUMN tbl_course_courses.description IS '课程的简短描述';
COMMENT ON COLUMN tbl_course_courses.subject_id IS '学科ID';
COMMENT ON COLUMN tbl_course_courses.opening_bg_image IS '开场页背景图URL';
COMMENT ON COLUMN tbl_course_courses.opening_color_value IS '开场页色值';
COMMENT ON COLUMN tbl_course_courses.ip_role_id IS 'IP角色ID';
COMMENT ON COLUMN tbl_course_courses.ip_animation_url IS '课程开场的IP动效资源URL';
COMMENT ON COLUMN tbl_course_courses.audio_url IS '课程开场的音频资源URL';
COMMENT ON COLUMN tbl_course_courses.standard_duration IS '课程的标准学习时长(秒)';
COMMENT ON COLUMN tbl_course_courses.total_components IS '一节课中组件的总数量';
COMMENT ON COLUMN tbl_course_courses.status IS '状态(0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_course_courses.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_course_courses.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_course_courses.deleted_at IS '软删除标记时间(NULL表示未删除)';
COMMENT ON COLUMN tbl_course_courses.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_course_courses.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE UNIQUE INDEX uk_course_courses_course_code ON tbl_course_courses(course_code) WHERE deleted_at IS NULL; -- 课程编码唯一索引
-- 课程编码唯一索引，确保课程编码在业务上的唯一性
CREATE INDEX idx_course_courses_subject_id ON tbl_course_courses(subject_id) WHERE deleted_at IS NULL; -- 学科ID索引
-- 学科ID索引，用于按学科查询课程
CREATE INDEX idx_course_courses_status ON tbl_course_courses(status) WHERE deleted_at IS NULL; -- 状态索引
-- 状态索引，用于筛选启用/禁用的课程
```
---

### 3.3 表名: `tbl_course_components`

#### 3.3.1 表功能说明
存储课程组件的基本信息，包括组件ID、所属课程ID、组件类型、组件名称、序号、状态等。作为课程框架的组成单元表，它定义了课程中各个组件的基本属性和顺序关系。

#### 3.3.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_course_components (
  id BIGSERIAL PRIMARY KEY,
  course_id BIGINT NOT NULL, -- 所属课程ID
  component_type SMALLINT NOT NULL, -- 组件类型(1:文档组件, 2:练习组件)
  component_name VARCHAR(100) NOT NULL, -- 组件名称，如"课程引入"、"知识点1"
  sequence_number INTEGER NOT NULL, -- 组件在课程中的顺序
  status SMALLINT NOT NULL DEFAULT 3, -- 组件状态(1:已解锁, 2:学习中, 3:待解锁)
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_course_components IS '课程组件基本信息表 - 存储课程中各组件的基本信息';
COMMENT ON COLUMN tbl_course_components.id IS '主键ID';
COMMENT ON COLUMN tbl_course_components.course_id IS '所属课程ID';
COMMENT ON COLUMN tbl_course_components.component_type IS '组件类型(1:文档组件, 2:练习组件)';
COMMENT ON COLUMN tbl_course_components.component_name IS '组件名称，如"课程引入"、"知识点1"';
COMMENT ON COLUMN tbl_course_components.sequence_number IS '组件在课程中的顺序';
COMMENT ON COLUMN tbl_course_components.status IS '组件状态(1:已解锁, 2:学习中, 3:待解锁)';
COMMENT ON COLUMN tbl_course_components.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_course_components.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_course_components.deleted_at IS '软删除标记时间(NULL表示未删除)';
COMMENT ON COLUMN tbl_course_components.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_course_components.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE INDEX idx_course_components_course_id ON tbl_course_components(course_id) WHERE deleted_at IS NULL; -- 课程ID索引
-- 课程ID索引，用于查询特定课程的所有组件
CREATE INDEX idx_course_components_course_sequence ON tbl_course_components(course_id, sequence_number) WHERE deleted_at IS NULL; -- 课程ID和序号的复合索引
-- 课程ID和序号的复合索引，用于按顺序获取课程组件
CREATE INDEX idx_course_components_type ON tbl_course_components(component_type) WHERE deleted_at IS NULL; -- 组件类型索引
-- 组件类型索引，用于筛选特定类型的组件
```
---

### 3.4 表名: `tbl_course_document_components`

#### 3.4.1 表功能说明
存储文档组件的详细信息，包括关联的基础组件ID、JS动画内容、数字人视频、勾画轨迹、字幕内容、默认模式、标准时长等。该表扩展了基础组件表，提供文档组件特有的属性和配置。

#### 3.4.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_course_document_components (
  id BIGSERIAL PRIMARY KEY,
  component_id BIGINT NOT NULL, -- 关联的基础组件ID
  js_animation_url VARCHAR(255) NOT NULL, -- 文档组件中呈现的动态板书内容URL
  video_url VARCHAR(255) NOT NULL, -- 数字人讲解视频URL
  drawing_trajectory JSONB, -- 教师在讲解过程中在板书上进行的勾画、标记的轨迹记录
  subtitle_content TEXT, -- 数字人视频的字幕
  subtitle_enabled BOOLEAN NOT NULL DEFAULT TRUE, -- 字幕是否开启
  default_mode SMALLINT NOT NULL DEFAULT 1, -- 文档组件的默认播放模式(1:跟随模式, 2:自由模式)
  standard_duration INTEGER NOT NULL DEFAULT 0, -- 文档组件的标准播放时长(秒)
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_course_document_components IS '文档组件详细信息表 - 存储文档组件的详细配置和内容';
COMMENT ON COLUMN tbl_course_document_components.id IS '主键ID';
COMMENT ON COLUMN tbl_course_document_components.component_id IS '关联的基础组件ID';
COMMENT ON COLUMN tbl_course_document_components.js_animation_url IS '文档组件中呈现的动态板书内容URL';
COMMENT ON COLUMN tbl_course_document_components.video_url IS '数字人讲解视频URL';
COMMENT ON COLUMN tbl_course_document_components.drawing_trajectory IS '教师在讲解过程中在板书上进行的勾画、标记的轨迹记录';
COMMENT ON COLUMN tbl_course_document_components.subtitle_content IS '数字人视频的字幕';
COMMENT ON COLUMN tbl_course_document_components.subtitle_enabled IS '字幕是否开启';
COMMENT ON COLUMN tbl_course_document_components.default_mode IS '文档组件的默认播放模式(1:跟随模式, 2:自由模式)';
COMMENT ON COLUMN tbl_course_document_components.standard_duration IS '文档组件的标准播放时长(秒)';
COMMENT ON COLUMN tbl_course_document_components.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_course_document_components.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_course_document_components.deleted_at IS '软删除标记时间(NULL表示未删除)';
COMMENT ON COLUMN tbl_course_document_components.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_course_document_components.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE UNIQUE INDEX uk_document_components_component_id ON tbl_course_document_components(component_id) WHERE deleted_at IS NULL; -- 组件ID唯一索引
-- 组件ID唯一索引，确保每个基础组件只对应一个文档组件详情
```
---

### 3.5 表名: `tbl_course_exercise_components`

#### 3.5.1 表功能说明
存储练习组件的详细信息，包括关联的基础组件ID、题目数量、题目类型等。该表扩展了基础组件表，提供练习组件特有的属性和配置。

#### 3.5.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_course_exercise_components (
  id BIGSERIAL PRIMARY KEY,
  component_id BIGINT NOT NULL, -- 关联的基础组件ID
  question_count INTEGER NOT NULL DEFAULT 0, -- 练习组件中的题目总数
  question_type SMALLINT NOT NULL DEFAULT 1, -- 题目类型(1:单选题)，一期仅支持单选题
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_course_exercise_components IS '练习组件详细信息表 - 存储练习组件的详细配置和内容';
COMMENT ON COLUMN tbl_course_exercise_components.id IS '主键ID';
COMMENT ON COLUMN tbl_course_exercise_components.component_id IS '关联的基础组件ID';
COMMENT ON COLUMN tbl_course_exercise_components.question_count IS '练习组件中的题目总数';
COMMENT ON COLUMN tbl_course_exercise_components.question_type IS '题目类型(1:单选题)，一期仅支持单选题';
COMMENT ON COLUMN tbl_course_exercise_components.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_course_exercise_components.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_course_exercise_components.deleted_at IS '软删除标记时间(NULL表示未删除)';
COMMENT ON COLUMN tbl_course_exercise_components.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_course_exercise_components.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE UNIQUE INDEX uk_exercise_components_component_id ON tbl_course_exercise_components(component_id) WHERE deleted_at IS NULL; -- 组件ID唯一索引
-- 组件ID唯一索引，确保每个基础组件只对应一个练习组件详情
```
---

### 3.6 表名: `tbl_course_qa_components`

#### 3.6.1 表功能说明
存储答疑组件的详细信息，包括挂载组件ID、挂载组件类型、会话上下文等。该表定义了答疑组件的配置和关联关系，支持学生在学习过程中的提问和解答。

#### 3.6.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_course_qa_components (
  id BIGSERIAL PRIMARY KEY,
  mounted_component_id BIGINT NOT NULL, -- 答疑组件挂载的文档或练习组件ID
  mounted_component_type SMALLINT NOT NULL, -- 挂载组件的类型(1:文档组件, 2:练习组件)
  session_context JSONB, -- 每个视频/题目的会话上下文
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_course_qa_components IS '答疑组件详细信息表 - 存储答疑组件的详细配置和内容';
COMMENT ON COLUMN tbl_course_qa_components.id IS '主键ID';
COMMENT ON COLUMN tbl_course_qa_components.mounted_component_id IS '答疑组件挂载的文档或练习组件ID';
COMMENT ON COLUMN tbl_course_qa_components.mounted_component_type IS '挂载组件的类型(1:文档组件, 2:练习组件)';
COMMENT ON COLUMN tbl_course_qa_components.session_context IS '每个视频/题目的会话上下文';
COMMENT ON COLUMN tbl_course_qa_components.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_course_qa_components.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_course_qa_components.deleted_at IS '软删除标记时间(NULL表示未删除)';
COMMENT ON COLUMN tbl_course_qa_components.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_course_qa_components.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE INDEX idx_qa_components_mounted ON tbl_course_qa_components(mounted_component_id, mounted_component_type) WHERE deleted_at IS NULL; -- 挂载组件ID和类型的复合索引
-- 挂载组件ID和类型的复合索引，用于查询特定组件的答疑组件
```
---

### 3.7 表名: `tbl_course_ip_roles`

#### 3.7.1 表功能说明
存储IP角色信息，包括名称、形象描述、动效资源、反馈文案库等。该表定义了AI课程中使用的虚拟角色的属性和资源，用于增强课程的互动性和趣味性。

#### 3.7.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_course_ip_roles (
  id BIGSERIAL PRIMARY KEY,
  role_name VARCHAR(50) NOT NULL, -- IP角色的名称
  role_description TEXT, -- IP角色的外观和人设描述
  happy_animation_url VARCHAR(255), -- IP角色的"开心"类动效资源URL
  encourage_animation_url VARCHAR(255), -- IP角色的"鼓励"类动效资源URL
  feedback_text_library JSONB, -- 与IP角色相关的反馈文案库
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_course_ip_roles IS 'IP角色信息表 - 存储AI课程中使用的虚拟角色信息';
COMMENT ON COLUMN tbl_course_ip_roles.id IS '主键ID';
COMMENT ON COLUMN tbl_course_ip_roles.role_name IS 'IP角色的名称';
COMMENT ON COLUMN tbl_course_ip_roles.role_description IS 'IP角色的外观和人设描述';
COMMENT ON COLUMN tbl_course_ip_roles.happy_animation_url IS 'IP角色的"开心"类动效资源URL';
COMMENT ON COLUMN tbl_course_ip_roles.encourage_animation_url IS 'IP角色的"鼓励"类动效资源URL';
COMMENT ON COLUMN tbl_course_ip_roles.feedback_text_library IS '与IP角色相关的反馈文案库';
COMMENT ON COLUMN tbl_course_ip_roles.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_course_ip_roles.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_course_ip_roles.deleted_at IS '软删除标记时间(NULL表示未删除)';
COMMENT ON COLUMN tbl_course_ip_roles.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_course_ip_roles.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE INDEX idx_ip_roles_role_name ON tbl_course_ip_roles(role_name) WHERE deleted_at IS NULL; -- 角色名称索引
-- 角色名称索引，用于按名称查询IP角色
```
---

### 3.8 表名: `tbl_user_learning_records`

#### 3.8.1 表功能说明
存储用户学习记录，包括用户ID、课程ID、学习时长、答题数量、正确率、当前组件ID、学习进度、完成状态等。该表记录用户的学习状态和进度，支持断点续学和学习报告生成。

#### 3.8.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_user_learning_records (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL, -- 用户ID
  course_id BIGINT NOT NULL, -- 课程ID
  learning_duration INTEGER NOT NULL DEFAULT 0, -- 用户完成本节课的累计学习时长(秒)
  question_count INTEGER NOT NULL DEFAULT 0, -- 用户在本节课本次学习中的累计答题数量
  correct_count INTEGER NOT NULL DEFAULT 0, -- 用户在本节课本次学习中答对的题目数量
  accuracy_rate NUMERIC(5,4) NOT NULL DEFAULT 0, -- 正确率(答对的题目数量/答题总数量)
  current_component_id BIGINT, -- 用户当前学习的组件ID
  current_play_position INTEGER DEFAULT 0, -- 针对文档组件，记录用户退出时的播放时间点(秒)
  current_question_number INTEGER DEFAULT 1, -- 针对练习组件，记录用户退出时的题目序号
  learning_progress NUMERIC(5,4) NOT NULL DEFAULT 0, -- 学习进度(已学完的组件数量/总组件数量)
  is_first_time BOOLEAN NOT NULL DEFAULT TRUE, -- 标记是否为用户首次学习该课程
  is_completed BOOLEAN NOT NULL DEFAULT FALSE, -- 记录课程是否已完成学习
  learning_start_time TIMESTAMPTZ NOT NULL, -- 本次学习的开始时间
  learning_end_time TIMESTAMPTZ, -- 本次学习的结束时间
  school_id BIGINT NOT NULL, -- 用户所属学校ID
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_user_learning_records IS '用户学习记录表 - 存储用户的学习状态和进度';
COMMENT ON COLUMN tbl_user_learning_records.id IS '主键ID';
COMMENT ON COLUMN tbl_user_learning_records.user_id IS '用户ID';
COMMENT ON COLUMN tbl_user_learning_records.course_id IS '课程ID';
COMMENT ON COLUMN tbl_user_learning_records.learning_duration IS '用户完成本节课的累计学习时长(秒)';
COMMENT ON COLUMN tbl_user_learning_records.question_count IS '用户在本节课本次学习中的累计答题数量';
COMMENT ON COLUMN tbl_user_learning_records.correct_count IS '用户在本节课本次学习中答对的题目数量';
COMMENT ON COLUMN tbl_user_learning_records.accuracy_rate IS '正确率(答对的题目数量/答题总数量)';
COMMENT ON COLUMN tbl_user_learning_records.current_component_id IS '用户当前学习的组件ID';
COMMENT ON COLUMN tbl_user_learning_records.current_play_position IS '针对文档组件，记录用户退出时的播放时间点(秒)';
COMMENT ON COLUMN tbl_user_learning_records.current_question_number IS '针对练习组件，记录用户退出时的题目序号';
COMMENT ON COLUMN tbl_user_learning_records.learning_progress IS '学习进度(已学完的组件数量/总组件数量)';
COMMENT ON COLUMN tbl_user_learning_records.is_first_time IS '标记是否为用户首次学习该课程';
COMMENT ON COLUMN tbl_user_learning_records.is_completed IS '记录课程是否已完成学习';
COMMENT ON COLUMN tbl_user_learning_records.learning_start_time IS '本次学习的开始时间';
COMMENT ON COLUMN tbl_user_learning_records.learning_end_time IS '本次学习的结束时间';
COMMENT ON COLUMN tbl_user_learning_records.school_id IS '用户所属学校ID';
COMMENT ON COLUMN tbl_user_learning_records.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_user_learning_records.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_user_learning_records.deleted_at IS '软删除标记时间(NULL表示未删除)';
COMMENT ON COLUMN tbl_user_learning_records.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_user_learning_records.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE INDEX idx_learning_records_user_course ON tbl_user_learning_records(user_id, course_id) WHERE deleted_at IS NULL; -- 用户ID和课程ID的复合索引
-- 用户ID和课程ID的复合索引，用于查询用户特定课程的学习记录
CREATE INDEX idx_learning_records_school ON tbl_user_learning_records(school_id) WHERE deleted_at IS NULL; -- 学校ID索引
-- 学校ID索引，用于按学校查询学习记录
CREATE INDEX idx_learning_records_completion ON tbl_user_learning_records(is_completed) WHERE deleted_at IS NULL; -- 完成状态索引
-- 完成状态索引，用于筛选已完成/未完成的学习记录
```
---

### 3.9 表名: `tbl_user_mastery_records`

#### 3.9.1 表功能说明
存储用户知识点掌握度记录，包括用户ID、课程ID、知识点ID、掌握度值、目标掌握度、掌握度变化等。该表记录用户对知识点的掌握情况，支持掌握度计算和展示。

#### 3.9.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_user_mastery_records (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL, -- 用户ID
  course_id BIGINT NOT NULL, -- 关联课程的ID
  knowledge_point_id VARCHAR(50) NOT NULL, -- 关联知识点的ID(来自业务知识树)
  mastery_value NUMERIC(5,4) NOT NULL DEFAULT 0, -- 用户对知识点的掌握程度[0,1]
  target_mastery NUMERIC(5,4) NOT NULL DEFAULT 1, -- 用户设定的目标掌握度[0,1]
  mastery_progress NUMERIC(5,4) NOT NULL DEFAULT 0, -- 掌握度完成进度(当前掌握度/目标掌握度)[0,1]
  mastery_change NUMERIC(5,4) NOT NULL DEFAULT 0, -- 掌握度变化(本次学习后的进度-本次学习前的进度)
  previous_mastery NUMERIC(5,4) DEFAULT 0, -- 上次学习后的掌握度值[0,1]
  school_id BIGINT NOT NULL, -- 用户所属学校ID
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NOT NULL,
  updated_by BIGINT NOT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_user_mastery_records IS '用户知识点掌握度记录表 - 存储用户对知识点的掌握情况';
COMMENT ON COLUMN tbl_user_mastery_records.id IS '主键ID';
COMMENT ON COLUMN tbl_user_mastery_records.user_id IS '用户ID';
COMMENT ON COLUMN tbl_user_mastery_records.course_id IS '关联课程的ID';
COMMENT ON COLUMN tbl_user_mastery_records.knowledge_point_id IS '关联知识点的ID(来自业务知识树)';
COMMENT ON COLUMN tbl_user_mastery_records.mastery_value IS '用户对知识点的掌握程度[0,1]';
COMMENT ON COLUMN tbl_user_mastery_records.target_mastery IS '用户设定的目标掌握度[0,1]';
COMMENT ON COLUMN tbl_user_mastery_records.mastery_progress IS '掌握度完成进度(当前掌握度/目标掌握度)[0,1]';
COMMENT ON COLUMN tbl_user_mastery_records.mastery_change IS '掌握度变化(本次学习后的进度-本次学习前的进度)';
COMMENT ON COLUMN tbl_user_mastery_records.previous_mastery IS '上次学习后的掌握度值[0,1]';
COMMENT ON COLUMN tbl_user_mastery_records.school_id IS '用户所属学校ID';
COMMENT ON COLUMN tbl_user_mastery_records.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_user_mastery_records.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_user_mastery_records.deleted_at IS '软删除标记时间(NULL表示未删除)';
COMMENT ON COLUMN tbl_user_mastery_records.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_user_mastery_records.updated_by IS '最后更新人的用户ID';

-- 添加索引
CREATE UNIQUE INDEX uk_mastery_records_user_course_kp ON tbl_user_mastery_records(user_id, course_id, knowledge_point_id) WHERE deleted_at IS NULL; -- 用户ID、课程ID和知识点ID的唯一索引
-- 用户ID、课程ID和知识点ID的唯一索引，确保每个用户对每个课程的每个知识点只有一条掌握度记录
CREATE INDEX idx_mastery_records_user ON tbl_user_mastery_records(user_id) WHERE deleted_at IS NULL; -- 用户ID索引
-- 用户ID索引，用于查询用户的所有掌握度记录
CREATE INDEX idx_mastery_records_school ON tbl_user_mastery_records(school_id) WHERE deleted_at IS NULL; -- 学校ID索引
-- 学校ID索引，用于按学校查询掌握度记录
```
---

### 3.10 表名: `tbl_learning_action_logs`

#### 3.10.1 表功能说明
记录用户学习行为日志，包括事件时间、用户ID、行为类型、课程ID、组件ID等。该表存储用户在学习过程中的各种行为数据，用于学习分析和报告生成。

#### 3.10.2 表结构定义 (DDL)
```sql
CREATE TABLE db_course_logs.tbl_learning_action_logs
(
    event_date Date COMMENT '事件日期',
    event_time DateTime64(3) COMMENT '事件发生的精确时间（毫秒级）',
    user_id UInt64 COMMENT '用户ID',
    action_type LowCardinality(String) COMMENT '行为类型(例如: enter_course, exit_course, start_component, complete_component)',
    course_id UInt64 COMMENT '课程ID',
    component_id UInt64 COMMENT '组件ID',
    component_type UInt8 COMMENT '组件类型(1:文档组件, 2:练习组件)',
    duration UInt32 COMMENT '持续时间(秒)',
    position UInt32 COMMENT '位置信息(如视频播放位置)',
    device_type LowCardinality(String) COMMENT '设备类型(例如: PC, Mobile, Tablet)',
    school_id UInt64 COMMENT '学校ID',
    event_properties Map(String, String) COMMENT '事件相关的其他动态属性(键值对)'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, course_id, event_time)
TTL event_date + INTERVAL 365 DAY DELETE
SETTINGS index_granularity = 8192;

-- 跳数索引
ALTER TABLE db_course_logs.tbl_learning_action_logs ADD INDEX idx_action_type action_type TYPE bloom_filter GRANULARITY 1;
ALTER TABLE db_course_logs.tbl_learning_action_logs ADD INDEX idx_course_id course_id TYPE minmax GRANULARITY 1;
ALTER TABLE db_course_logs.tbl_learning_action_logs ADD INDEX idx_component_id component_id TYPE minmax GRANULARITY 1;
```

* **排序键 (ORDER BY)**: `(event_date, user_id, course_id, event_time)`
  * 设计原因: 按照事件日期、用户ID、课程ID和事件时间排序，可以高效支持按用户和课程查询特定时间段的学习行为，这是最常见的查询模式。
* **分区键 (PARTITION BY)**: `toYYYYMM(event_date)`
  * 设计原因: 按月分区，平衡了分区数量和查询效率。大多数查询会限定在特定时间范围内，按月分区可以有效裁剪不相关的数据。
* **主键**: 与排序键相同
---

### 3.11 表名: `tbl_learning_interaction_events`

#### 3.11.1 表功能说明
记录用户与文档组件的交互事件，包括双击、长按等操作。该表存储用户在学习过程中与文档组件的交互数据，用于分析用户行为模式和优化学习体验。

#### 3.11.2 表结构定义 (DDL)
```sql
CREATE TABLE db_course_logs.tbl_learning_interaction_events
(
    event_date Date COMMENT '事件日期',
    event_time DateTime64(3) COMMENT '事件发生的精确时间（毫秒级）',
    user_id UInt64 COMMENT '用户ID',
    course_id UInt64 COMMENT '课程ID',
    component_id UInt64 COMMENT '组件ID',
    interaction_type LowCardinality(String) COMMENT '交互类型(例如: double_click, long_press, mode_switch)',
    content_position UInt32 COMMENT '内容位置(如视频播放位置)',
    mode_before UInt8 COMMENT '交互前的模式(1:跟随模式, 2:自由模式)',
    mode_after UInt8 COMMENT '交互后的模式(1:跟随模式, 2:自由模式)',
    device_type LowCardinality(String) COMMENT '设备类型(例如: PC, Mobile, Tablet)',
    school_id UInt64 COMMENT '学校ID',
    event_properties Map(String, String) COMMENT '事件相关的其他动态属性(键值对)'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, course_id, event_time)
TTL event_date + INTERVAL 365 DAY DELETE
SETTINGS index_granularity = 8192;

-- 跳数索引
ALTER TABLE db_course_logs.tbl_learning_interaction_events ADD INDEX idx_interaction_type interaction_type TYPE bloom_filter GRANULARITY 1;
ALTER TABLE db_course_logs.tbl_learning_interaction_events ADD INDEX idx_component_id component_id TYPE minmax GRANULARITY 1;
```

* **排序键 (ORDER BY)**: `(event_date, user_id, course_id, event_time)`
  * 设计原因: 按照事件日期、用户ID、课程ID和事件时间排序，可以高效支持按用户和课程查询特定时间段的交互事件，这是最常见的查询模式。
* **分区键 (PARTITION BY)**: `toYYYYMM(event_date)`
  * 设计原因: 按月分区，平衡了分区数量和查询效率。大多数查询会限定在特定时间范围内，按月分区可以有效裁剪不相关的数据。
* **主键**: 与排序键相同
---

### 3.12 表名: `tbl_learning_answer_logs`

#### 3.12.1 表功能说明
记录用户答题日志，包括题目ID、答案、是否正确等。该表存储用户在练习组件中的答题数据，用于分析答题情况和生成学习报告。

#### 3.12.2 表结构定义 (DDL)
```sql
CREATE TABLE db_course_logs.tbl_learning_answer_logs
(
    event_date Date COMMENT '事件日期',
    event_time DateTime64(3) COMMENT '事件发生的精确时间（毫秒级）',
    user_id UInt64 COMMENT '用户ID',
    course_id UInt64 COMMENT '课程ID',
    component_id UInt64 COMMENT '组件ID',
    question_id UInt64 COMMENT '题目ID',
    question_type UInt8 COMMENT '题目类型(1:单选题)',
    answer_content String COMMENT '用户提交的答案内容',
    is_correct UInt8 COMMENT '是否正确(0:错误, 1:正确)',
    answer_time UInt32 COMMENT '答题耗时(秒)',
    knowledge_point_id String COMMENT '关联知识点ID',
    device_type LowCardinality(String) COMMENT '设备类型(例如: PC, Mobile, Tablet)',
    school_id UInt64 COMMENT '学校ID',
    event_properties Map(String, String) COMMENT '事件相关的其他动态属性(键值对)'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, course_id, event_time)
TTL event_date + INTERVAL 365 DAY DELETE
SETTINGS index_granularity = 8192;

-- 跳数索引
ALTER TABLE db_course_logs.tbl_learning_answer_logs ADD INDEX idx_question_id question_id TYPE minmax GRANULARITY 1;
ALTER TABLE db_course_logs.tbl_learning_answer_logs ADD INDEX idx_is_correct is_correct TYPE set(2) GRANULARITY 1;
ALTER TABLE db_course_logs.tbl_learning_answer_logs ADD INDEX idx_knowledge_point_id knowledge_point_id TYPE bloom_filter GRANULARITY 1;
```

* **排序键 (ORDER BY)**: `(event_date, user_id, course_id, event_time)`
  * 设计原因: 按照事件日期、用户ID、课程ID和事件时间排序，可以高效支持按用户和课程查询特定时间段的答题记录，这是最常见的查询模式。
* **分区键 (PARTITION BY)**: `toYYYYMM(event_date)`
  * 设计原因: 按月分区，平衡了分区数量和查询效率。大多数查询会限定在特定时间范围内，按月分区可以有效裁剪不相关的数据。
* **主键**: 与排序键相同
---

### 3.13 表名: `tbl_learning_feedback_logs`

#### 3.13.1 表功能说明
记录用户反馈信息，包括评分、标签、文本内容等。该表存储用户对课程的反馈数据，用于分析用户满意度和改进课程内容。

#### 3.13.2 表结构定义 (DDL)
```sql
CREATE TABLE db_course_logs.tbl_learning_feedback_logs
(
    event_date Date COMMENT '事件日期',
    event_time DateTime64(3) COMMENT '事件发生的精确时间（毫秒级）',
    user_id UInt64 COMMENT '用户ID',
    course_id UInt64 COMMENT '课程ID',
    rating UInt8 COMMENT '评分(1-5)',
    feedback_tags Array(LowCardinality(String)) COMMENT '反馈标签数组',
    feedback_text String COMMENT '反馈文本内容',
    learning_duration UInt32 COMMENT '学习时长(秒)',
    completion_status UInt8 COMMENT '完成状态(0:未完成, 1:已完成)',
    device_type LowCardinality(String) COMMENT '设备类型(例如: PC, Mobile, Tablet)',
    school_id UInt64 COMMENT '学校ID',
    event_properties Map(String, String) COMMENT '事件相关的其他动态属性(键值对)'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (event_date, user_id, course_id)
TTL event_date + INTERVAL 365 DAY DELETE
SETTINGS index_granularity = 8192;

-- 跳数索引
ALTER TABLE db_course_logs.tbl_learning_feedback_logs ADD INDEX idx_rating rating TYPE set(5) GRANULARITY 1;
ALTER TABLE db_course_logs.tbl_learning_feedback_logs ADD INDEX idx_completion_status completion_status TYPE set(2) GRANULARITY 1;
ALTER TABLE db_course_logs.tbl_learning_feedback_logs ADD INDEX idx_feedback_tags feedback_tags TYPE bloom_filter GRANULARITY 1;
```

* **排序键 (ORDER BY)**: `(event_date, user_id, course_id)`
  * 设计原因: 按照事件日期、用户ID和课程ID排序，可以高效支持按用户和课程查询反馈记录，这是最常见的查询模式。
* **分区键 (PARTITION BY)**: `toYYYYMM(event_date)`
  * 设计原因: 按月分区，平衡了分区数量和查询效率。大多数查询会限定在特定时间范围内，按月分区可以有效裁剪不相关的数据。
* **主键**: 与排序键相同
## 4. 表间关系与 ER 图

### 4.1 表间关系描述

| 主表                          | 关系类型                       | 从表                            | 外键字段 (从表)                                | 关联描述                                                     |
| ----------------------------- | ------------------------------ | ------------------------------- | ---------------------------------------------- | ------------------------------------------------------------ |
| `tbl_course_courses`          | 一对多 (1:N)                   | `tbl_course_components`         | `course_id`                                    | 一个课程包含多个组件                                         |
| `tbl_course_components`       | 一对一 (1:1)                   | `tbl_course_document_components` | `component_id`                                 | 一个组件可以是一个文档组件                                   |
| `tbl_course_components`       | 一对一 (1:1)                   | `tbl_course_exercise_components` | `component_id`                                 | 一个组件可以是一个练习组件                                   |
| `tbl_course_components`       | 一对一 (1:1)                   | `tbl_course_qa_components`      | `mounted_component_id`                         | 一个组件可以挂载一个答疑组件                                 |
| `tbl_course_ip_roles`         | 一对多 (1:N)                   | `tbl_course_courses`            | `ip_role_id`                                   | 一个IP角色可以用于多个课程                                   |
| `tbl_course_courses`          | 一对多 (1:N)                   | `tbl_user_learning_records`     | `course_id`                                    | 一个课程可以有多个用户学习记录                               |
| `tbl_course_courses`          | 一对多 (1:N)                   | `tbl_user_mastery_records`      | `course_id`                                    | 一个课程可以有多个用户掌握度记录                             |
| `tbl_course_components`       | 一对多 (1:N)                   | `tbl_user_learning_records`     | `current_component_id`                         | 一个组件可以是多个用户当前学习的组件                         |

### 4.2 ER 图 (使用 Mermaid 语法)
注意：本ER图中的外键关系（FK）表示的是表之间的逻辑关联和参照完整性期望，实际建表语句中不使用数据库层面的物理外键约束，数据一致性由应用层保障。

```mermaid
erDiagram
    COURSE_COURSES {
        BIGSERIAL id PK
        VARCHAR course_code
        VARCHAR chapter_info
        VARCHAR course_sequence
        VARCHAR course_name
        TEXT description
        BIGINT subject_id
        VARCHAR opening_bg_image
        VARCHAR opening_color_value
        BIGINT ip_role_id FK
        VARCHAR ip_animation_url
        VARCHAR audio_url
        INTEGER standard_duration
        INTEGER total_components
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    COURSE_COMPONENTS {
        BIGSERIAL id PK
        BIGINT course_id FK
        SMALLINT component_type
        VARCHAR component_name
        INTEGER sequence_number
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    COURSE_DOCUMENT_COMPONENTS {
        BIGSERIAL id PK
        BIGINT component_id FK
        VARCHAR js_animation_url
        VARCHAR video_url
        JSONB drawing_trajectory
        TEXT subtitle_content
        BOOLEAN subtitle_enabled
        SMALLINT default_mode
        INTEGER standard_duration
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    COURSE_EXERCISE_COMPONENTS {
        BIGSERIAL id PK
        BIGINT component_id FK
        INTEGER question_count
        SMALLINT question_type
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    COURSE_QA_COMPONENTS {
        BIGSERIAL id PK
        BIGINT mounted_component_id FK
        SMALLINT mounted_component_type
        JSONB session_context
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    COURSE_IP_ROLES {
        BIGSERIAL id PK
        VARCHAR role_name
        TEXT role_description
        VARCHAR happy_animation_url
        VARCHAR encourage_animation_url
        JSONB feedback_text_library
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    USER_LEARNING_RECORDS {
        BIGSERIAL id PK
        BIGINT user_id
        BIGINT course_id FK
        INTEGER learning_duration
        INTEGER question_count
        INTEGER correct_count
        NUMERIC accuracy_rate
        BIGINT current_component_id FK
        INTEGER current_play_position
        INTEGER current_question_number
        NUMERIC learning_progress
        BOOLEAN is_first_time
        BOOLEAN is_completed
        TIMESTAMPTZ learning_start_time
        TIMESTAMPTZ learning_end_time
        BIGINT school_id
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }
    
    USER_MASTERY_RECORDS {
        BIGSERIAL id PK
        BIGINT user_id
        BIGINT course_id FK
        VARCHAR knowledge_point_id
        NUMERIC mastery_value
        NUMERIC target_mastery
        NUMERIC mastery_progress
        NUMERIC mastery_change
        NUMERIC previous_mastery
        BIGINT school_id
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
        TIMESTAMPTZ deleted_at
    }

    COURSE_IP_ROLES ||--o{ COURSE_COURSES : "provides character for"
    COURSE_COURSES ||--o{ COURSE_COMPONENTS : "contains"
    COURSE_COMPONENTS ||--o| COURSE_DOCUMENT_COMPONENTS : "can be"
    COURSE_COMPONENTS ||--o| COURSE_EXERCISE_COMPONENTS : "can be"
    COURSE_COMPONENTS ||--o| COURSE_QA_COMPONENTS : "can have"
    COURSE_COURSES ||--o{ USER_LEARNING_RECORDS : "has learning records"
    COURSE_COURSES ||--o{ USER_MASTERY_RECORDS : "has mastery records"
    COURSE_COMPONENTS ||--o{ USER_LEARNING_RECORDS : "is current component of"
```

* **ER 图说明**: 
  * 核心实体是课程（`COURSE_COURSES`）和组件（`COURSE_COMPONENTS`），它们之间是一对多关系。
  * 组件可以是文档组件（`COURSE_DOCUMENT_COMPONENTS`）或练习组件（`COURSE_EXERCISE_COMPONENTS`），这是一对一关系，通过组件类型字段区分。
  * 答疑组件（`COURSE_QA_COMPONENTS`）可以挂载在文档组件或练习组件上，这是一对一关系。
  * IP角色（`COURSE_IP_ROLES`）可以用于多个课程，这是一对多关系。
  * 用户学习记录（`USER_LEARNING_RECORDS`）和用户掌握度记录（`USER_MASTERY_RECORDS`）与课程是一对多关系。
  * ClickHouse表（如`tbl_learning_action_logs`等）主要用于存储分析型数据，不在ER图中显示，因为它们主要是事实表，与维度表的关系通过逻辑外键字段体现。
## 5. 核心用户场景/用户故事与数据操作分析

### 5.1 场景/故事: 用户进入课程学习

#### 5.1.1 场景描述
用户通过App选择一门AI课程进行学习。系统首先检查用户是否有该课程的学习记录，如果是首次学习，则展示课程开场页；如果是继续学习，则定位到上次学习的位置。用户开始学习课程内容，系统记录学习进度和行为数据。

#### 5.1.2 涉及的主要数据表
* `tbl_course_courses`
* `tbl_course_components`
* `tbl_course_document_components`
* `tbl_course_exercise_components`
* `tbl_user_learning_records`
* `tbl_learning_action_logs`

#### 5.1.3 数据操作流程 (CRUD 分析)
* **步骤 1: 用户请求进入课程**
  * **读取 (Read)**:
    * 从 `tbl_course_courses` 表中查询课程信息。
    * 查询条件：`id = {课程ID}`
    * 涉及字段：`id`, `course_name`, `description`, `ip_role_id`, `ip_animation_url`, `audio_url`, `opening_bg_image`, `opening_color_value`
    * 目的：获取课程基本信息和开场页配置
  * **读取 (Read)**:
    * 从 `tbl_user_learning_records` 表中查询用户学习记录。
    * 查询条件：`user_id = {用户ID} AND course_id = {课程ID} AND deleted_at IS NULL`
    * 涉及字段：`id`, `is_first_time`, `current_component_id`, `current_play_position`, `current_question_number`, `learning_progress`, `is_completed`
    * 目的：判断用户是否首次学习该课程，以及获取上次学习位置

* **步骤 2: 系统准备课程内容**
  * **读取 (Read)**:
    * 从 `tbl_course_components` 表中查询课程组件列表。
    * 查询条件：`course_id = {课程ID} AND deleted_at IS NULL`
    * 涉及字段：`id`, `component_type`, `component_name`, `sequence_number`, `status`
    * 目的：获取课程的组件列表和顺序
  * **条件分支**:
    * 如果 `is_first_time = TRUE`，准备开场页内容
    * 如果 `is_first_time = FALSE`，定位到上次学习位置

* **步骤 3: 用户开始学习**
  * **创建 (Create)**:
    * 在 `tbl_learning_action_logs` 表中插入一条记录。
    * 涉及字段：`event_date`, `event_time`, `user_id`, `action_type`, `course_id`, `component_id`, `component_type`, `device_type`, `school_id`
    * 数据来源：用户ID、课程ID、当前组件ID、设备信息等
    * 成功标准：记录用户开始学习的行为
  * **更新 (Update)**:
    * 更新 `tbl_user_learning_records` 表中的记录。
    * 更新条件：`user_id = {用户ID} AND course_id = {课程ID} AND deleted_at IS NULL`
    * 更新字段：`is_first_time = FALSE`, `learning_start_time = {当前时间}`, `current_component_id = {当前组件ID}`
    * 影响：标记用户已开始学习，记录学习开始时间和当前组件

* **步骤 4: 加载组件内容**
  * **读取 (Read)**:
    * 根据组件类型，从 `tbl_course_document_components` 或 `tbl_course_exercise_components` 表中查询组件详细信息。
    * 查询条件：`component_id = {当前组件ID} AND deleted_at IS NULL`
    * 涉及字段：对于文档组件，`js_animation_url`, `video_url`, `drawing_trajectory`, `subtitle_content`, `subtitle_enabled`, `default_mode`；对于练习组件，`question_count`, `question_type`
    * 目的：获取组件的详细内容和配置

#### 5.1.4 性能考量与索引支持
* 用户进入课程时，需要快速获取课程信息和学习记录，`tbl_course_courses` 表的主键索引和 `tbl_user_learning_records` 表的 `idx_learning_records_user_course` 索引可以有效支持这些查询。
* 获取课程组件列表时，`tbl_course_components` 表的 `idx_course_components_course_id` 索引可以提供高效支持。
* 记录学习行为时，`tbl_learning_action_logs` 表的分区键和排序键设计可以确保高效写入和查询。

### 5.2 场景/故事: 文档组件模式切换

#### 5.2.1 场景描述
用户在学习文档组件内容时，可以在跟随模式和自由模式之间切换。默认为跟随模式，用户滑动文档内容后切换到自由模式，可以自由浏览内容。在自由模式下，用户可以点击"从这里学"按钮，切换回跟随模式并从选中位置开始播放。用户还可以通过双击暂停/播放，长按快速播放等交互操作控制学习进度。

#### 5.2.2 涉及的主要数据表
* `tbl_course_document_components`
* `tbl_user_learning_records`
* `tbl_learning_interaction_events`

#### 5.2.3 数据操作流程 (CRUD 分析)
* **步骤 1: 用户滑动文档内容，切换到自由模式**
  * **创建 (Create)**:
    * 在 `tbl_learning_interaction_events` 表中插入一条记录。
    * 涉及字段：`event_date`, `event_time`, `user_id`, `course_id`, `component_id`, `interaction_type = 'mode_switch'`, `content_position`, `mode_before = 1`, `mode_after = 2`, `device_type`, `school_id`
    * 数据来源：用户ID、课程ID、组件ID、当前位置、设备信息等
    * 成功标准：记录用户模式切换行为

* **步骤 2: 用户点击"从这里学"，切换回跟随模式**
  * **创建 (Create)**:
    * 在 `tbl_learning_interaction_events` 表中插入一条记录。
    * 涉及字段：`event_date`, `event_time`, `user_id`, `course_id`, `component_id`, `interaction_type = 'mode_switch'`, `content_position`, `mode_before = 2`, `mode_after = 1`, `device_type`, `school_id`
    * 数据来源：用户ID、课程ID、组件ID、选中位置、设备信息等
    * 成功标准：记录用户模式切换行为
  * **更新 (Update)**:
    * 更新 `tbl_user_learning_records` 表中的记录。
    * 更新条件：`user_id = {用户ID} AND course_id = {课程ID} AND deleted_at IS NULL`
    * 更新字段：`current_play_position = {选中位置}`
    * 影响：更新用户当前播放位置

* **步骤 3: 用户双击文档内容，切换播放/暂停状态**
  * **创建 (Create)**:
    * 在 `tbl_learning_interaction_events` 表中插入一条记录。
    * 涉及字段：`event_date`, `event_time`, `user_id`, `course_id`, `component_id`, `interaction_type = 'double_click'`, `content_position`, `device_type`, `school_id`
    * 数据来源：用户ID、课程ID、组件ID、当前位置、设备信息等
    * 成功标准：记录用户双击交互行为

* **步骤 4: 用户长按文档以外区域，启动快速播放**
  * **创建 (Create)**:
    * 在 `tbl_learning_interaction_events` 表中插入一条记录。
    * 涉及字段：`event_date`, `event_time`, `user_id`, `course_id`, `component_id`, `interaction_type = 'long_press'`, `content_position`, `device_type`, `school_id`
    * 数据来源：用户ID、课程ID、组件ID、当前位置、设备信息等
    * 成功标准：记录用户长按交互行为

#### 5.2.4 性能考量与索引支持
* 记录用户交互事件时，`tbl_learning_interaction_events` 表的分区键和排序键设计可以确保高效写入和查询。
* 更新用户学习记录时，`tbl_user_learning_records` 表的 `idx_learning_records_user_course` 索引可以提供高效支持。
* `tbl_learning_interaction_events` 表的 `idx_interaction_type` 索引可以支持按交互类型查询事件，用于后续分析用户行为模式。

### 5.3 场景/故事: 课程完成与结算

#### 5.3.1 场景描述
用户完成课程的所有组件学习后，系统自动生成学习报告，包括学习时长、答题数量、正确率、掌握度变化等信息。系统根据学习表现选择相应的动效和文案（开心或鼓励），并基于学习情况和掌握度生成推荐学习内容。用户可以提交对课程的反馈评价。

#### 5.3.2 涉及的主要数据表
* `tbl_user_learning_records`
* `tbl_user_mastery_records`
* `tbl_course_ip_roles`
* `tbl_learning_answer_logs`
* `tbl_learning_feedback_logs`

#### 5.3.3 数据操作流程 (CRUD 分析)
* **步骤 1: 用户完成最后一个组件学习**
  * **更新 (Update)**:
    * 更新 `tbl_user_learning_records` 表中的记录。
    * 更新条件：`user_id = {用户ID} AND course_id = {课程ID} AND deleted_at IS NULL`
    * 更新字段：`is_completed = TRUE`, `learning_end_time = {当前时间}`, `learning_progress = 1.0`
    * 影响：标记课程已完成，记录学习结束时间和完成进度

* **步骤 2: 系统生成学习报告**
  * **读取 (Read)**:
    * 从 `tbl_user_learning_records` 表中查询学习记录。
    * 查询条件：`user_id = {用户ID} AND course_id = {课程ID} AND deleted_at IS NULL`
    * 涉及字段：`learning_duration`, `question_count`, `correct_count`, `accuracy_rate`, `learning_start_time`, `learning_end_time`
    * 目的：获取学习时长、答题数量、正确率等基本数据
  * **读取 (Read)**:
    * 从 `tbl_learning_answer_logs` 表中查询答题记录。
    * 查询条件：`user_id = {用户ID} AND course_id = {课程ID} AND event_date >= {学习开始日期} AND event_date <= {学习结束日期}`
    * 涉及字段：`question_id`, `is_correct`, `answer_time`, `knowledge_point_id`
    * 目的：获取详细的答题数据，用于生成报告
  * **读取 (Read)**:
    * 从 `tbl_user_mastery_records` 表中查询掌握度记录。
    * 查询条件：`user_id = {用户ID} AND course_id = {课程ID} AND deleted_at IS NULL`
    * 涉及字段：`knowledge_point_id`, `mastery_value`, `mastery_change`, `previous_mastery`, `mastery_progress`
    * 目的：获取掌握度数据，用于生成报告

* **步骤 3: 系统准备结算页内容**
  * **读取 (Read)**:
    * 从 `tbl_course_courses` 表中查询课程信息。
    * 查询条件：`id = {课程ID} AND deleted_at IS NULL`
    * 涉及字段：`ip_role_id`
    * 目的：获取课程使用的IP角色ID
  * **读取 (Read)**:
    * 从 `tbl_course_ip_roles` 表中查询IP角色信息。
    * 查询条件：`id = {ip_role_id} AND deleted_at IS NULL`
    * 涉及字段：`happy_animation_url`, `encourage_animation_url`, `feedback_text_library`
    * 目的：获取IP角色的动效资源和反馈文案
  * **条件分支**:
    * 如果 `accuracy_rate > 0.7`，选择"开心"动效和对应文案
    * 否则，选择"鼓励"动效和对应文案

* **步骤 4: 用户提交课程反馈**
  * **创建 (Create)**:
    * 在 `tbl_learning_feedback_logs` 表中插入一条记录。
    * 涉及字段：`event_date`, `event_time`, `user_id`, `course_id`, `rating`, `feedback_tags`, `feedback_text`, `learning_duration`, `completion_status`, `device_type`, `school_id`
    * 数据来源：用户ID、课程ID、评分、标签、文本内容、学习时长、设备信息等
    * 成功标准：记录用户反馈信息

#### 5.3.4 性能考量与索引支持
* 查询学习记录时，`tbl_user_learning_records` 表的 `idx_learning_records_user_course` 索引可以提供高效支持。
* 查询答题记录时，`tbl_learning_answer_logs` 表的分区键和排序键设计可以确保高效查询。
* 查询掌握度记录时，`tbl_user_mastery_records` 表的 `uk_mastery_records_user_course_kp` 索引可以提供高效支持。
* 记录用户反馈时，`tbl_learning_feedback_logs` 表的分区键和排序键设计可以确保高效写入和查询。

### 5.4 场景/故事: 学习进度保存与恢复

#### 5.4.1 场景描述
用户在学习过程中可能需要暂时退出，系统会自动保存当前的学习进度，包括当前组件、播放位置或题目序号等信息。当用户重新进入课程时，系统会恢复到上次学习的位置，实现断点续学功能。

#### 5.4.2 涉及的主要数据表
* `tbl_user_learning_records`
* `tbl_course_components`
* `tbl_course_document_components`
* `tbl_course_exercise_components`
* `tbl_learning_action_logs`

#### 5.4.3 数据操作流程 (CRUD 分析)
* **步骤 1: 用户退出学习**
  * **读取 (Read)**:
    * 从当前组件状态中获取学习信息。
    * 涉及数据：当前组件ID、播放位置或题目序号
    * 目的：获取需要保存的学习进度信息
  * **更新 (Update)**:
    * 更新 `tbl_user_learning_records` 表中的记录。
    * 更新条件：`user_id = {用户ID} AND course_id = {课程ID} AND deleted_at IS NULL`
    * 更新字段：`current_component_id = {当前组件ID}`, `current_play_position = {当前播放位置}` 或 `current_question_number = {当前题目序号}`, `learning_duration = learning_duration + {本次学习时长}`, `learning_progress = {当前进度}`
    * 影响：保存用户当前的学习进度
  * **创建 (Create)**:
    * 在 `tbl_learning_action_logs` 表中插入一条记录。
    * 涉及字段：`event_date`, `event_time`, `user_id`, `action_type = 'exit_course'`, `course_id`, `component_id`, `component_type`, `position`, `duration`, `device_type`, `school_id`
    * 数据来源：用户ID、课程ID、组件ID、位置、学习时长、设备信息等
    * 成功标准：记录用户退出学习的行为

* **步骤 2: 用户重新进入学习**
  * **读取 (Read)**:
    * 从 `tbl_user_learning_records` 表中查询学习记录。
    * 查询条件：`user_id = {用户ID} AND course_id = {课程ID} AND deleted_at IS NULL`
    * 涉及字段：`current_component_id`, `current_play_position`, `current_question_number`, `learning_progress`
    * 目的：获取上次保存的学习进度
  * **读取 (Read)**:
    * 从 `tbl_course_components` 表中查询组件信息。
    * 查询条件：`id = {current_component_id} AND deleted_at IS NULL`
    * 涉及字段：`component_type`, `component_name`, `sequence_number`
    * 目的：获取当前组件的类型和信息
  * **条件分支**:
    * 如果 `component_type = 1`（文档组件），从 `tbl_course_document_components` 表中查询详细信息，并定位到 `current_play_position`
    * 如果 `component_type = 2`（练习组件），从 `tbl_course_exercise_components` 表中查询详细信息，并定位到 `current_question_number`
  * **创建 (Create)**:
    * 在 `tbl_learning_action_logs` 表中插入一条记录。
    * 涉及字段：`event_date`, `event_time`, `user_id`, `action_type = 'resume_course'`, `course_id`, `component_id`, `component_type`, `position`, `device_type`, `school_id`
    * 数据来源：用户ID、课程ID、组件ID、位置、设备信息等
    * 成功标准：记录用户恢复学习的行为

#### 5.4.4 性能考量与索引支持
* 保存和恢复学习进度时，`tbl_user_learning_records` 表的 `idx_learning_records_user_course` 索引可以提供高效支持。
* 查询组件信息时，`tbl_course_components` 表的主键索引可以提供高效支持。
* 查询组件详细信息时，`tbl_course_document_components` 和 `tbl_course_exercise_components` 表的 `uk_document_components_component_id` 和 `uk_exercise_components_component_id` 索引可以提供高效支持。
* 记录学习行为时，`tbl_learning_action_logs` 表的分区键和排序键设计可以确保高效写入和查询。
## 6. 数据量预估与增长趋势

### 6.1 数据量预估与性能考虑

| 数据表 | 初始/增长/一年后 | 数据特征简述 | 性能优化策略 | 数据生命周期管理/归档策略 (如果适用) | 特殊设计考量与权衡 (如果适用) |
| ---------- | ---------------- | ------------ | ---------------- | ---------------- |---------------- |
| `tbl_course_courses` | **初始数据量预估**: 1,000 条；**月增长量预估**: 100 条；**一年后数据量预估**: 约 2,200 条 | **数据特征简述**: 课程基本信息，更新频率低，读多写少 | 按学科ID和状态建立索引，优化查询性能 | **数据保留期限**: 长期保留；**归档/删除机制**: 不活跃课程可标记为禁用状态，但不物理删除 | 使用软删除机制，便于数据恢复和历史追溯 |
| `tbl_course_components` | **初始数据量预估**: 10,000 条；**月增长量预估**: 1,000 条；**一年后数据量预估**: 约 22,000 条 | **数据特征简述**: 课程组件信息，更新频率低，读多写少 | 按课程ID和序号建立复合索引，优化按顺序获取组件的查询 | **数据保留期限**: 长期保留；**归档/删除机制**: 随课程一起管理 | 组件状态字段支持动态解锁功能，便于控制学习流程 |
| `tbl_course_document_components` | **初始数据量预估**: 7,000 条；**月增长量预估**: 700 条；**一年后数据量预估**: 约 15,400 条 | **数据特征简述**: 文档组件详细信息，包含大量URL和JSONB数据，读多写少 | 使用JSONB类型存储勾画轨迹，提供灵活性和查询效率 | **数据保留期限**: 长期保留；**归档/删除机制**: 随组件一起管理 | 勾画轨迹使用JSONB类型，便于存储复杂的轨迹数据，但需注意查询性能 |
| `tbl_course_exercise_components` | **初始数据量预估**: 3,000 条；**月增长量预估**: 300 条；**一年后数据量预估**: 约 6,600 条 | **数据特征简述**: 练习组件详细信息，数据量较小，读多写少 | 表结构简单，主要依赖主键索引 | **数据保留期限**: 长期保留；**归档/删除机制**: 随组件一起管理 | 一期仅支持单选题，后续可能需要扩展支持更多题型 |
| `tbl_course_qa_components` | **初始数据量预估**: 5,000 条；**月增长量预估**: 500 条；**一年后数据量预估**: 约 11,000 条 | **数据特征简述**: 答疑组件详细信息，包含JSONB类型的会话上下文，读多写少 | 使用JSONB类型存储会话上下文，提供灵活性和查询效率 | **数据保留期限**: 长期保留；**归档/删除机制**: 随组件一起管理 | 会话上下文使用JSONB类型，便于存储复杂的对话数据，但需注意查询性能 |
| `tbl_course_ip_roles` | **初始数据量预估**: 10 条；**月增长量预估**: 1-2 条；**一年后数据量预估**: 约 30 条 | **数据特征简述**: IP角色信息，数据量很小，更新频率极低，读多写少 | 表数据量小，主要依赖主键索引 | **数据保留期限**: 长期保留；**归档/删除机制**: 不活跃角色可标记为禁用状态，但不物理删除 | 反馈文案库使用JSONB类型，便于存储多样化的文案模板 |
| `tbl_user_learning_records` | **初始数据量预估**: 10,000 条；**日增长量预估**: 5,000 条；**一年后数据量预估**: 约 1,835,000 条 | **数据特征简述**: 用户学习记录，高频写入，读写均频繁 | 按用户ID和课程ID建立复合索引，优化查询性能；定期归档历史数据 | **数据保留期限**: 在线保留1年；**归档/删除机制**: 超过1年的数据可归档到历史表 | 使用软删除机制，便于数据恢复和历史追溯；学习时长、答题数量等字段支持增量更新 |
| `tbl_user_mastery_records` | **初始数据量预估**: 50,000 条；**日增长量预估**: 10,000 条；**一年后数据量预估**: 约 3,700,000 条 | **数据特征简述**: 用户掌握度记录，高频写入，读写均频繁 | 按用户ID、课程ID和知识点ID建立唯一索引，优化查询性能；定期归档历史数据 | **数据保留期限**: 在线保留1年；**归档/删除机制**: 超过1年的数据可归档到历史表 | 使用软删除机制，便于数据恢复和历史追溯；掌握度值设计为非递减，确保用户体验 |
| `tbl_learning_action_logs` | **初始数据量预估**: 0 条；**日增长量预估**: 500,000 条；**一年后数据量预估**: 约 182,500,000 条 | **数据特征简述**: 用户学习行为日志，高频写入，读少写多，主要用于分析 | 按月分区，优化查询和数据管理；使用ClickHouse的列式存储和压缩特性 | **数据保留期限**: 在线保留1年；**归档/删除机制**: 通过TTL自动删除过期数据 | 使用ClickHouse的MergeTree引擎，优化大数据量下的查询性能；按月分区平衡了分区数量和查询效率 |
| `tbl_learning_interaction_events` | **初始数据量预估**: 0 条；**日增长量预估**: 300,000 条；**一年后数据量预估**: 约 109,500,000 条 | **数据特征简述**: 用户交互事件日志，高频写入，读少写多，主要用于分析 | 按月分区，优化查询和数据管理；使用ClickHouse的列式存储和压缩特性 | **数据保留期限**: 在线保留1年；**归档/删除机制**: 通过TTL自动删除过期数据 | 使用ClickHouse的MergeTree引擎，优化大数据量下的查询性能；按月分区平衡了分区数量和查询效率 |
| `tbl_learning_answer_logs` | **初始数据量预估**: 0 条；**日增长量预估**: 200,000 条；**一年后数据量预估**: 约 73,000,000 条 | **数据特征简述**: 用户答题日志，高频写入，读少写多，主要用于分析 | 按月分区，优化查询和数据管理；使用ClickHouse的列式存储和压缩特性 | **数据保留期限**: 在线保留1年；**归档/删除机制**: 通过TTL自动删除过期数据 | 使用ClickHouse的MergeTree引擎，优化大数据量下的查询性能；按月分区平衡了分区数量和查询效率 |
| `tbl_learning_feedback_logs` | **初始数据量预估**: 0 条；**日增长量预估**: 10,000 条；**一年后数据量预估**: 约 3,650,000 条 | **数据特征简述**: 用户反馈日志，中频写入，读写均衡，用于分析和改进 | 按月分区，优化查询和数据管理；使用ClickHouse的列式存储和压缩特性 | **数据保留期限**: 在线保留1年；**归档/删除机制**: 通过TTL自动删除过期数据 | 使用ClickHouse的MergeTree引擎，优化大数据量下的查询性能；按月分区平衡了分区数量和查询效率 |

### 6.2 索引策略与性能优化

#### 6.2.1 PostgreSQL索引策略
| 表名 | 索引名 | 索引字段 | 索引类型 | 设计原因 |
| --- | --- | --- | --- | --- |
| `tbl_course_courses` | `uk_course_courses_course_code` | `(course_code)` | B-Tree (唯一) | 确保课程编码在业务上的唯一性，支持按编码快速查询课程 |
| `tbl_course_courses` | `idx_course_courses_subject_id` | `(subject_id)` | B-Tree | 支持按学科查询课程，优化课程列表展示 |
| `tbl_course_courses` | `idx_course_courses_status` | `(status)` | B-Tree | 支持按状态筛选课程，优化课程列表展示 |
| `tbl_course_components` | `idx_course_components_course_id` | `(course_id)` | B-Tree | 支持查询特定课程的所有组件，优化课程内容加载 |
| `tbl_course_components` | `idx_course_components_course_sequence` | `(course_id, sequence_number)` | B-Tree | 支持按顺序获取课程组件，优化课程内容展示 |
| `tbl_course_components` | `idx_course_components_type` | `(component_type)` | B-Tree | 支持按类型筛选组件，优化特定类型组件的查询 |
| `tbl_course_document_components` | `uk_document_components_component_id` | `(component_id)` | B-Tree (唯一) | 确保每个基础组件只对应一个文档组件详情，支持按组件ID快速查询文档组件 |
| `tbl_course_exercise_components` | `uk_exercise_components_component_id` | `(component_id)` | B-Tree (唯一) | 确保每个基础组件只对应一个练习组件详情，支持按组件ID快速查询练习组件 |
| `tbl_course_qa_components` | `idx_qa_components_mounted` | `(mounted_component_id, mounted_component_type)` | B-Tree | 支持查询特定组件的答疑组件，优化答疑功能加载 |
| `tbl_course_ip_roles` | `idx_ip_roles_role_name` | `(role_name)` | B-Tree | 支持按名称查询IP角色，优化角色管理 |
| `tbl_user_learning_records` | `idx_learning_records_user_course` | `(user_id, course_id)` | B-Tree | 支持查询用户特定课程的学习记录，优化学习状态获取和更新 |
| `tbl_user_learning_records` | `idx_learning_records_school` | `(school_id)` | B-Tree | 支持按学校查询学习记录，优化学校级统计 |
| `tbl_user_learning_records` | `idx_learning_records_completion` | `(is_completed)` | B-Tree | 支持按完成状态筛选学习记录，优化完成率统计 |
| `tbl_user_mastery_records` | `uk_mastery_records_user_course_kp` | `(user_id, course_id, knowledge_point_id)` | B-Tree (唯一) | 确保每个用户对每个课程的每个知识点只有一条掌握度记录，支持掌握度查询和更新 |
| `tbl_user_mastery_records` | `idx_mastery_records_user` | `(user_id)` | B-Tree | 支持查询用户的所有掌握度记录，优化用户掌握度概览 |
| `tbl_user_mastery_records` | `idx_mastery_records_school` | `(school_id)` | B-Tree | 支持按学校查询掌握度记录，优化学校级统计 |

#### 6.2.2 ClickHouse优化策略
| 分区策略 | 说明 |
| --- | --- |
| 分区 | 所有ClickHouse表均按月分区(toYYYYMM(event_date))，平衡了分区数量和查询效率；大多数查询会限定在特定时间范围内，按月分区可以有效裁剪不相关的数据 |
| 排序键选择 | 所有ClickHouse表的排序键都包含event_date、user_id和course_id，这是最常见的查询条件，可以高效支持按用户和课程查询特定时间段的数据；排序键的设计考虑了数据的主要查询模式和基数分布 |
| 跳数索引 | 为高频查询条件（如action_type、interaction_type、is_correct等）创建了跳数索引，使用bloom_filter、set或minmax类型，根据字段的基数和查询特性选择合适的索引类型 |
| 数据压缩 | 使用ClickHouse默认的压缩算法，对于特定列可以考虑使用Delta、DoubleDelta等编码进一步优化压缩率 |
| 数据类型优化 | 使用LowCardinality(String)类型优化低基数字符串字段（如action_type、device_type等），使用Map类型存储键值对属性，使用Array类型存储标签数组 |
## 7. 数据库特定设计要点回顾

### 7.1 PostgreSQL 设计要点体现

1. **标准字段的全面应用**：所有PostgreSQL表都包含了标准字段，包括`id`、`created_at`、`updated_at`、`deleted_at`、`created_by`、`updated_by`，确保了数据的可追溯性和一致性。

2. **命名规范的严格遵守**：
   * 表名使用`tbl_模块名_实体名`格式，如`tbl_course_courses`
   * 字段名使用`snake_case`，简洁明确
   * 索引命名遵循规范，如`idx_表名_字段名`、`uk_表名_字段名`

3. **TIMESTAMPTZ的正确使用**：所有时间戳字段都使用`TIMESTAMPTZ`类型，避免时区混淆问题，确保时间数据的准确性。

4. **JSONB用于半结构化数据**：对于复杂的、结构可能变化的数据，如勾画轨迹、会话上下文、反馈文案库等，使用`JSONB`类型存储，提供了灵活性和查询效率。

5. **索引设计的合理性**：
   * 为高频查询条件创建了索引，如用户ID和课程ID的复合索引
   * 为唯一性约束创建了唯一索引，如组件ID的唯一索引
   * 所有索引都考虑了软删除条件`WHERE deleted_at IS NULL`

6. **软删除机制的应用**：所有表都使用`deleted_at`字段实现软删除，便于数据恢复和历史追溯，同时在索引中考虑了软删除条件。

7. **数据类型选择的精确性**：
   * 使用`VARCHAR(n)`时，根据预估的最大长度精确选用
   * 使用`NUMERIC(p,s)`存储需要精确计算的数值，如掌握度值、正确率等
   * 使用`SMALLINT`存储状态、类型等枚举值，并在注释中明确说明各数值的含义

8. **完整的注释**：所有表和字段都添加了清晰、准确的中文注释，便于理解和维护。

### 7.2 ClickHouse 设计要点体现

1. **合适的表引擎选择**：所有ClickHouse表都使用`MergeTree`引擎，这是ClickHouse最常用且功能强大的表引擎，支持主键索引、数据分区、数据副本和数据采样。

2. **优化的排序键和分区键设计**：
   * 排序键包含`event_date`、`user_id`、`course_id`和`event_time`，这是最常见的查询条件，可以高效支持按用户和课程查询特定时间段的数据
   * 分区键使用`toYYYYMM(event_date)`按月分区，平衡了分区数量和查询效率

3. **LowCardinality数据类型的有效利用**：对于低基数字符串字段，如`action_type`、`interaction_type`、`device_type`等，使用`LowCardinality(String)`类型，提高了存储效率和查询性能。

4. **复杂数据类型的应用**：
   * 使用`Map(String, String)`类型存储事件属性，提供了灵活性和查询效率
   * 使用`Array(LowCardinality(String))`类型存储反馈标签，便于标签分析

5. **跳数索引的审慎添加**：
   * 为高频查询条件创建了跳数索引，如`action_type`、`interaction_type`、`is_correct`等
   * 根据字段的基数和查询特性选择合适的索引类型，如`bloom_filter`、`set`或`minmax`

6. **TTL数据生命周期管理**：所有ClickHouse表都设置了TTL，`event_date + INTERVAL 365 DAY DELETE`，自动删除超过1年的数据，降低存储成本并保持查询性能。

7. **宽表设计思想**：ClickHouse表采用宽表设计，包含了分析所需的各种维度和指标，减少了JOIN操作，提高了查询性能。

8. **数据压缩考量**：虽然使用了ClickHouse默认的压缩算法，但对于特定列可以考虑使用`Delta`、`DoubleDelta`等编码进一步优化压缩率，特别是对于时间序列数据。
## 8. 设计检查清单 (Design Checklist)

本检查清单分为两部分：第一部分关注数据库设计与需求、架构的对齐性；第二部分关注数据库设计方案本身的技术质量。
( ✅: 满足/通过, ❌: 不满足/未通过, N/A: 不适用 )

### 8.1 需求与架构对齐性检查

| 需求点/用户故事 (来自PRD)        | 对应架构模块 (来自TD)           | 关联核心数据表 (本次设计) | ①需求覆盖完整性 | ②架构符合性 | ③一致性与无冲突 | ④业务场景满足度 | ⑤可追溯性与依赖清晰度 | 备注与待办事项                                                                                                                                  |
| -------------------------------- | ----------------------------- | --------------------- | --------------- | ----------- | --------------- | ------------- | ------------------- | --------------------------------------------------------------------------------------------------------------------------------------------- |
| 课程结构定义，支持多种组件的灵活组合 | 课程框架服务 | `tbl_course_courses`, `tbl_course_components` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.1已覆盖。表设计支持课程包含多个不同类型的组件，组件可以按序号排序，符合架构中的课程框架服务职责。 |
| 管理课程开场页与结算页内容 | 课程框架服务 | `tbl_course_courses`, `tbl_course_ip_roles` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.1和5.3已覆盖。`tbl_course_courses`表包含开场页配置字段，`tbl_course_ip_roles`表包含动效和文案资源，符合架构中的课程框架服务职责。 |
| 记录和维护用户学习进度，支持断点续学 | 学习进度服务 | `tbl_user_learning_records` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.4已覆盖。`tbl_user_learning_records`表记录用户的学习状态和进度，支持断点续学功能，符合架构中的学习进度服务职责。 |
| 提供学习报告生成能力 | 学习报告服务 | `tbl_user_learning_records`, `tbl_user_mastery_records`, `tbl_learning_answer_logs` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.3已覆盖。表设计支持统计学习时长、答题数量、正确率、掌握度等信息，符合架构中的学习报告服务职责。 |
| 收集用户对课程的反馈评价 | 用户反馈服务 | `tbl_learning_feedback_logs` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.3已覆盖。`tbl_learning_feedback_logs`表记录用户的反馈信息，符合架构中的用户反馈服务职责。 |
| 提供课程内容的动态展示，同步勾画轨迹与内容 | 文档组件服务 | `tbl_course_document_components` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.2已覆盖。`tbl_course_document_components`表包含JS动画内容、视频URL、勾画轨迹等字段，符合架构中的文档组件服务职责。 |
| 支持跟随模式与自由模式的切换 | 文档组件服务 | `tbl_course_document_components`, `tbl_learning_interaction_events` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.2已覆盖。表设计支持模式切换和记录交互事件，符合架构中的文档组件服务职责。 |
| 提供内容交互功能 | 文档组件服务 | `tbl_learning_interaction_events` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.2已覆盖。`tbl_learning_interaction_events`表记录用户的交互行为，符合架构中的文档组件服务职责。 |
| 管理播放进度与状态 | 文档组件服务 | `tbl_user_learning_records`, `tbl_learning_action_logs` | ✅ | ✅ | ✅ | ✅ | ✅ | 场景5.1和5.4已覆盖。表设计支持记录和恢复播放进度，符合架构中的文档组件服务职责。 |

---

### 8.2 数据库设计质量检查

| 检查维度                               | 评估结果 (✅/❌/N/A) | 具体说明与改进建议 (如果存在问题)                                                                                                |
| -------------------------------------- | ------------------- | ------------------------------------------------------------------------------------------------------------------------------ |
| **⑥ 设计规范符合性** | ✅ | 整体设计符合PostgreSQL和ClickHouse的设计规范，包括命名规范、标准字段、数据类型选择、注释质量等方面。 |
|   - 命名规范                           | ✅ | 表名使用`tbl_模块名_实体名`格式，字段名使用`snake_case`，索引命名遵循规范，符合要求。 |
|   - 标准字段                           | ✅ | 所有PostgreSQL表都包含了标准字段，包括`id`、`created_at`、`updated_at`、`deleted_at`、`created_by`、`updated_by`。 |
|   - 数据类型选择                       | ✅ | 数据类型选择合理，如使用`TIMESTAMPTZ`存储时间，使用`JSONB`存储半结构化数据，使用`LowCardinality(String)`优化低基数字符串字段。 |
|   - 注释质量                           | ✅ | 所有表和字段都添加了清晰、准确的中文注释，便于理解和维护。 |
|   - 通用设计原则与反模式规避             | ✅ | 设计遵循了单一职责原则、数据隔离原则、高内聚低耦合等通用设计原则，避免了巨石表、过度规范化等反模式。 |
| **⑦ 性能与可伸缩性考量** | ✅ | 设计考虑了性能和可伸缩性，包括索引策略、分区/分片策略、表引擎选择、数据压缩策略等方面。 |
|   - 索引策略 (主键、排序键、跳数索引等)  | ✅ | PostgreSQL表的索引设计合理，为高频查询条件创建了索引；ClickHouse表的排序键和跳数索引设计考虑了查询模式和性能。 |
|   - 分区/分片策略 (如果适用)             | ✅ | ClickHouse表按月分区，平衡了分区数量和查询效率；PostgreSQL表未使用分区，但考虑了数据量和查询模式，符合需求。 |
|   - 表引擎选择 (尤其 ClickHouse)        | ✅ | 所有ClickHouse表都使用`MergeTree`引擎，这是最常用且功能强大的表引擎，支持主键索引、数据分区、数据副本和数据采样。 |
|   - 数据压缩策略 (尤其 ClickHouse)      | ✅ | 使用ClickHouse默认的压缩算法，对于特定列可以考虑使用`Delta`、`DoubleDelta`等编码进一步优化压缩率。 |
| **⑧ 可维护性与可扩展性** | ✅ | 表结构清晰，字段定义明确，注释完善，便于理解和维护；设计考虑了未来需求变更，如支持更多题型、扩展组件类型等。 |
| **⑨ 数据完整性保障** | ✅ | 通过NOT NULL约束、唯一索引等保障了数据有效性；通过逻辑外键关系保障了数据一致性；通过软删除机制保障了数据可恢复性。 |
| **⑩ (可选) 安全性考量** | ✅ | 识别了用户ID、学校ID等敏感数据字段，建议在应用层实现访问控制和数据脱敏。 |
| **其他特定于数据库的检查点** |  |  |
|   - PostgreSQL: `TIMESTAMPTZ` 使用        | ✅ | 所有时间戳字段都使用`TIMESTAMPTZ`类型，避免时区混淆问题，确保时间数据的准确性。 |
|   - PostgreSQL: `JSONB` 使用合理性        | ✅ | 对于复杂的、结构可能变化的数据，如勾画轨迹、会话上下文、反馈文案库等，使用`JSONB`类型存储，提供了灵活性和查询效率。 |
|   - ClickHouse: `LowCardinality` 使用     | ✅ | 对于低基数字符串字段，如`action_type`、`interaction_type`、`device_type`等，使用`LowCardinality(String)`类型，提高了存储效率和查询性能。 |
|   - ClickHouse: 物化视图设计合理性        | N/A | 当前设计未使用物化视图，但可以考虑在后续版本中添加，用于预聚合常用的分析指标，如每日学习时长、答题正确率等。 |
## 9. 参考资料与附录

* 产品需求文档 (PRD): [be-s2-ai-dd-s1-课程框架文档组件V1.0-claude-3.7.md]
* 技术架构方案 (TD): [be-ai-td-课程框架文档组件V1.0-claude-3.7.md]
* PostgreSQL 设计规范与约束文件: [rules-postgresql-code-v2.md]
* ClickHouse 设计规范与约束文件: [rules-clickhouse-code-v2.md]
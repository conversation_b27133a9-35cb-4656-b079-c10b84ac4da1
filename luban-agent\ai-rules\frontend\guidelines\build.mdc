---
description: 
globs: 
alwaysApply: false
---
# 前端构建规则 (Webpack/Vite 兼容)

> **核心原则：工具无关，构建一致**

## 通用配置要求

- 构建工具需同时支持 Webpack 与 Vite
- 构建产物目录统一为 `dist`
- 开发服务器端口固定为 `3000`
- 环境变量前缀必须为 `APP_`
- 保持 sourcemap 配置一致

## 项目结构

```
project-root/
├── public/                   # 静态资源，不需要构建处理的资源
├── src/                      # 源码目录
│   ├── assets/               # 需要构建处理的资源
│   ├── components/           # 组件
│   ├── index.(js|ts)         # 入口文件
├── dist/                     # 构建输出目录
├── build/                    # 构建脚本目录
│   ├── webpack.config.js     # Webpack 配置
│   └── vite.config.js        # Vite 配置
├── package.json              # 项目依赖
└── .env                      # 环境变量
```

## 构建脚本

```json
// package.json
{
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "build:webpack": "webpack --config build/webpack.config.js",
    "dev:webpack": "webpack serve --config build/webpack.config.js"
  }
}
```

## 共通配置要求

### 路径别名

- 两种构建工具必须支持相同的路径别名
- 必须支持 `@` 指向 `src` 目录

### 环境变量

- 使用时兼容两种工具语法：
  ```js
  // 源码中统一使用这种形式访问
  const apiUrl = process.env.APP_API_URL || import.meta.env.APP_API_URL;
  ```

### 资源导入

- 资源路径应使用相对路径或别名路径
- 小型资源内联为 Base64 阈值设为 4KB
- SVG 支持组件化导入

### 构建优化

- 分离第三方依赖为单独的 chunk
- CSS 提取为独立文件
- 生产环境启用代码压缩
- 生产环境自动移除 console.log

## Webpack 特有配置

```js
// build/webpack.config.js
const path = require('path');

module.exports = {
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, '../dist'),
    filename: '[name].[contenthash].js',
    clean: true
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '../src')
    }
  },
  module: {
    rules: [
      {
        test: /\.(js|jsx|ts|tsx)$/,
        exclude: /node_modules/,
        use: {
          loader: 'babel-loader'
        }
      },
      {
        test: /\.css$/,
        use: ['style-loader', 'css-loader']
      },
      {
        test: /\.(png|jpg|gif)$/i,
        type: 'asset',
        parser: {
          dataUrlCondition: {
            maxSize: 4 * 1024 // 4kb
          }
        }
      }
    ]
  },
  devServer: {
    port: 3000,
    hot: true
  }
};
```

## Vite 特有配置

```js
// build/vite.config.js
import { defineConfig } from 'vite';
import path from 'path';

export default defineConfig({
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '../src')
    }
  },
  build: {
    outDir: 'dist',
    assetsInlineLimit: 4096, // 4kb
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'vue'] // 根据实际框架调整
        }
      }
    }
  },
  server: {
    port: 3000
  },
  envPrefix: 'APP_'
});
```

## 兼容性检查列表

- [ ] 别名路径在两种构建工具下均可正常解析
- [ ] 环境变量在两种构建工具下均可正常访问
- [ ] 静态资源在两种构建工具下均可正常加载
- [ ] 构建产物结构一致
- [ ] 开发服务器行为一致

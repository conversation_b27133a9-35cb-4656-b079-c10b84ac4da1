// @ts-nocheck
// 工作流数据管理Hook

'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { WorkflowData, parseWorkflowParams, buildNextPageUrl } from '@/lib/workflow-utils';

/**
 * 工作流数据管理Hook
 * @param pageName - 当前页面名称
 * @returns 工作流数据和相关操作函数
 */
export function useWorkflowData(pageName: string) {
  const [workflowData, setWorkflowData] = useState<WorkflowData>({});
  let searchParams;
  
  try {
    searchParams = useSearchParams();
  } catch (error) {
    // 在服务器端渲染时，useSearchParams可能不可用
    searchParams = null;
  }

  // 从URL参数初始化工作流数据
  useEffect(() => {
    if (searchParams) {
      const params = parseWorkflowParams(searchParams);
      setWorkflowData(params);
    }
  }, [searchParams]);

  /**
   * 更新工作流数据
   * @param newData - 新的数据
   */
  const updateWorkflowData = (newData: Partial<WorkflowData>) => {
    setWorkflowData((prev: WorkflowData) => ({ ...prev, ...newData }));
  };

  /**
   * 构建跳转到下一页的URL
   * @param nextPage - 下一页名称
   * @param newUrl - 当前页面生成的URL
   * @param newUrlKey - 新URL的键名
   * @returns 跳转URL
   */
  const buildNextUrl = (nextPage: string, newUrl: string, newUrlKey: keyof WorkflowData) => {
    return buildNextPageUrl(nextPage, workflowData, newUrl, newUrlKey);
  };

  /**
   * 使用GET方式跳转到下一页
   * @param nextPage - 下一页名称
   * @param newUrl - 当前页面生成的URL
   * @param newUrlKey - 新URL的键名
   */
  const navigateToNextPage = (nextPage: string, newUrl: string, newUrlKey: keyof WorkflowData) => {
    if (typeof window === 'undefined') return;

    // 创建新的工作流数据，包含所有现有数据和新URL
    const nextWorkflowData = {
      ...workflowData,
      [newUrlKey]: newUrl
    };

    // 构建URL参数
    const params = new URLSearchParams();
    
    // 添加所有有效的工作流参数
    Object.entries(nextWorkflowData).forEach(([key, value]) => {
      if (value && typeof value === 'string' && value.trim() !== '') {
        params.set(key, value);
      }
    });

    // 保持debug参数
    const currentParams = new URLSearchParams(window.location.search);
    const debugParam = currentParams.get('debug');
    if (debugParam) {
      params.set('debug', debugParam);
    }

    // 跳转到下一页
    window.location.href = `/workflow/${nextPage}?${params.toString()}`;
  };

  /**
   * 获取隐藏input元素的数据
   * @returns 隐藏input数据数组
   */
  const getHiddenInputsData = () => {
    return Object.entries(workflowData)
      .filter(([_, value]) => value && typeof value === 'string' && value.trim() !== '')
      .map(([key, value]) => ({ key, name: key, value: value as string }));
  };

  /**
   * 获取隐藏input元素的属性数据
   * @returns 隐藏input属性数组
   */
  const getHiddenInputsProps = () => {
    return Object.entries(workflowData)
      .filter(([_, value]) => value && typeof value === 'string' && value.trim() !== '')
      .map(([key, value]) => ({
        key: key,
        type: 'hidden' as const,
        name: `workflow_${key}`,
        value: value as string,
        readOnly: true
      }));
  };

  return {
    workflowData,
    updateWorkflowData,
    buildNextUrl,
    navigateToNextPage,
    getHiddenInputsData,
    getHiddenInputsProps,
  };
}
# AI课中练习组件1.0技术架构设计文档

## 版本信息

| 版本号 | 日期 | 作者 | 变更说明 |
| ------ | ---- | ---- | -------- |
| V1.0 | 2025-05-24 | AI架构师 | 初版设计 |

## 1. 引言 (Introduction)

### 1.1. 文档目的 (Purpose of Document)
为AI课中练习组件1.0提供清晰的服务端架构蓝图，指导核心模块/服务的设计与开发，确保系统满足关键的业务需求和质量属性。

### 1.2. 系统概述 (System Overview)
本系统是AI课中练习组件的服务端架构，核心目标是提供稳定高效的练习环节转场动效、练习答题交互、即时反馈机制等功能。主要交互方包括学生客户端、用户中心服务、内容平台服务、教师服务等。

### 1.3. 设计目标与核心原则 (Design Goals and Core Principles)
- **性能目标**：练习组件加载响应时间 < 2秒，支持1000并发用户，答题提交响应时间 < 1秒
- **可用性目标**：系统可用性达到99.9%，支持优雅降级和故障恢复
- **可扩展性目标**：支持水平扩展，模块间清晰解耦以提升开发效率和可维护性
- **设计原则**：单一职责原则、高内聚低耦合、领域驱动设计、API优先设计、事件驱动架构

### 1.4. 范围 (Scope)
**覆盖范围**：AI课中练习服务的核心业务逻辑、与外部服务的交互、数据存储策略、缓存策略
**不覆盖范围**：前端具体实现、详细的数据库表结构设计、具体第三方服务集成协议细节、DevOps的CI/CD流程、基础设施的具体配置、具体的安全攻防策略

### 1.5. 术语与缩写 (Glossary and Abbreviations)
- **练习组件**：AI课程中的练习环节，包含题目展示、答题交互、即时反馈等功能
- **转场动效**：练习环节进入和退出时的动画效果
- **即时反馈**：学生答题后立即显示的正确/错误反馈和连胜提示
- **推题策略**：根据学生答题情况智能推荐下一道题目的算法策略

## 2. 需求提炼与架构驱动因素 (Key Requirements & Architectural Drivers)

### 2.1. 核心业务能力 (Core Business Capabilities)
- **练习会话管理**：管理学生的练习会话状态，包括进度跟踪、计时管理、状态恢复
- **题目推荐与分发**：基于推题策略为学生推荐合适的题目
- **答题处理与判断**：处理学生答题请求，判断答案正确性，记录答题数据
- **即时反馈生成**：根据答题结果生成个性化反馈内容，包括连胜检测
- **进度计算与更新**：实时计算和更新学生的练习进度
- **转场状态管理**：管理练习环节的进入和退出状态

### 2.2. 关键质量属性 (Key Quality Attributes - Relevant to Architecture)

#### 2.2.1. 性能 (Performance)
- 预期QPS：1000，核心答题流程延迟 < 1秒
- 通过无状态服务设计、Redis缓存热点数据、异步处理非关键路径来支持性能目标

#### 2.2.2. 可用性 (Availability)
- 通过无状态服务设计、数据库主从复制、Redis集群部署支持高可用

#### 2.2.3. 可伸缩性 (Scalability)
- 服务设计为无状态，支持水平扩展；数据按用户ID分片存储

#### 2.2.4. 可维护性 (Maintainability)
- 清晰的分层架构、明确的服务边界、标准化的接口定义

#### 2.2.5. 可测试性 (Testability)
- 依赖注入设计、清晰的服务边界、Mock外部依赖支持独立测试

### 2.3. 主要约束与依赖 (Key Constraints and Dependencies)
- **技术栈约束**：必须使用Go 1.24.3、Kratos 2.8.4框架、PostgreSQL17、Redis 6.0.3、Kafka 3.6
- **外部服务依赖**：
  - 用户中心服务：提供用户认证信息，返回用户ID、角色、权限
  - 内容平台服务：提供题目信息，包括题干、选项、答案、难度等
  - 教师服务：提供课程和班级信息，用于权限验证

## 3. 宏观架构设计 (High-Level Architectural Design)

### 3.1. 架构风格与模式 (Architectural Style and Patterns)
采用**分层架构 + 领域驱动设计**的组合模式，结合**事件驱动架构**处理异步场景。选择该风格的理由：
- 分层架构提供清晰的职责分离和依赖管理
- 领域驱动设计确保业务逻辑的内聚性和可维护性
- 事件驱动架构支持异步处理和系统解耦

### 3.2. 系统分层视图 (Layered View)

#### 3.2.1. 推荐的逻辑分层模型 (Recommended Logical Layering Model)

基于AI课中练习组件的业务特点，设计五层架构模型：

**分层设计理由**：
- 练习组件涉及复杂的业务规则（推题策略、进度计算、反馈生成），需要独立的领域逻辑层
- 需要与多个外部服务交互，需要专门的适配器层进行解耦
- 实时性要求高，需要应用服务层进行流程编排和事务管理
- 数据访问模式复杂（关系型+缓存），需要独立的数据访问层

```mermaid
flowchart TD
    subgraph 'AI课中练习服务架构'
        direction TB
        APILayer[API接口层]
        AppServiceLayer[应用服务层]
        DomainLayer[领域逻辑层]
        DataAccessLayer[数据访问层]
        InfrastructureLayer[基础设施层]

        APILayer --> AppServiceLayer
        AppServiceLayer --> DomainLayer
        AppServiceLayer --> DataAccessLayer
        DomainLayer --> DataAccessLayer
        DataAccessLayer --> InfrastructureLayer

        subgraph 'API接口层细分'
            HTTPHandlers[HTTP处理器]
            gRPCHandlers[gRPC处理器]
            Middlewares[中间件]
        end

        subgraph '应用服务层细分'
            ExerciseService[练习服务]
            SessionService[会话服务]
            FeedbackService[反馈服务]
        end

        subgraph '领域逻辑层细分'
            ExerciseDomain[练习领域]
            ProgressDomain[进度领域]
            StrategyDomain[策略领域]
        end

        subgraph '数据访问层细分'
            ExerciseRepo[练习仓储]
            SessionRepo[会话仓储]
            CacheRepo[缓存仓储]
        end

        subgraph '基础设施层细分'
            ExternalAdapters[外部服务适配器]
            DatabaseDriver[数据库驱动]
            CacheDriver[缓存驱动]
        end

        APILayer --> HTTPHandlers
        APILayer --> gRPCHandlers
        APILayer --> Middlewares

        AppServiceLayer --> ExerciseService
        AppServiceLayer --> SessionService
        AppServiceLayer --> FeedbackService

        DomainLayer --> ExerciseDomain
        DomainLayer --> ProgressDomain
        DomainLayer --> StrategyDomain

        DataAccessLayer --> ExerciseRepo
        DataAccessLayer --> SessionRepo
        DataAccessLayer --> CacheRepo

        InfrastructureLayer --> ExternalAdapters
        InfrastructureLayer --> DatabaseDriver
        InfrastructureLayer --> CacheDriver
    end

    %% 外部服务
    UCenterService[用户中心服务]
    QuestionService[内容平台服务]
    TeacherService[教师服务]
    PostgreSQLDB[PostgreSQL数据库]
    RedisCache[Redis缓存]

    %% 连接外部服务
    ExternalAdapters -.-> UCenterService
    ExternalAdapters -.-> QuestionService
    ExternalAdapters -.-> TeacherService
    DatabaseDriver -.-> PostgreSQLDB
    CacheDriver -.-> RedisCache

    %% 设置样式
    classDef apiStyle fill:#F9E79F,stroke:#333,stroke-width:2px;
    classDef appStyle fill:#ABEBC6,stroke:#333,stroke-width:2px;
    classDef domainStyle fill:#D7BDE2,stroke:#333,stroke-width:2px;
    classDef dataStyle fill:#AED6F1,stroke:#333,stroke-width:2px;
    classDef infraStyle fill:#F5CBA7,stroke:#333,stroke-width:2px;

    class APILayer,HTTPHandlers,gRPCHandlers,Middlewares apiStyle;
    class AppServiceLayer,ExerciseService,SessionService,FeedbackService appStyle;
    class DomainLayer,ExerciseDomain,ProgressDomain,StrategyDomain domainStyle;
    class DataAccessLayer,ExerciseRepo,SessionRepo,CacheRepo dataStyle;
    class InfrastructureLayer,ExternalAdapters,DatabaseDriver,CacheDriver infraStyle;
```

**各层职责定义**：

1. **API接口层**：负责HTTP/gRPC请求处理、参数验证、响应格式化、认证授权中间件
   - 封装变化：外部接口协议变化、认证方式变化
   - 依赖规则：仅依赖应用服务层接口

2. **应用服务层**：负责业务流程编排、事务管理、外部服务调用协调
   - 封装变化：业务流程变化、外部服务集成变化
   - 依赖规则：依赖领域逻辑层和数据访问层接口

3. **领域逻辑层**：负责核心业务规则、领域模型、业务计算逻辑
   - 封装变化：业务规则变化、计算逻辑变化
   - 依赖规则：不依赖外部技术实现，仅依赖数据访问层抽象

4. **数据访问层**：负责数据持久化、缓存操作、数据模型转换
   - 封装变化：数据存储技术变化、数据模型变化
   - 依赖规则：依赖基础设施层的具体实现

5. **基础设施层**：负责外部服务适配、数据库驱动、缓存驱动、消息队列等
   - 封装变化：技术栈变化、外部服务接口变化
   - 依赖规则：最底层，不依赖其他层

### 3.3. 模块/服务划分视图 (Module/Service Decomposition View)

#### 3.3.1. 核心模块/服务识别与职责 (Identification and Responsibilities)

**AI课中练习服务**：
- 职责：练习会话管理、题目推荐、答题处理、即时反馈、进度计算
- 边界：专注于练习环节的核心业务逻辑，不涉及用户管理和题目内容管理
- 对外能力：提供练习会话创建、题目获取、答题提交、进度查询等API

#### 3.3.2. 模块/服务交互模式与数据契约 (Interaction Patterns and Data Contracts)

```mermaid
graph LR
    StudentClient[学生客户端]

    subgraph "AI课中练习服务"
        ExerciseService[练习服务]
    end

    subgraph "外部依赖服务"
        UCenterService[用户中心服务]
        QuestionService[内容平台服务]
        TeacherService[教师服务]
    end

    subgraph "基础设施"
        PostgreSQL[PostgreSQL数据库]
        Redis[Redis缓存]
        Kafka[Kafka消息队列]
    end

    StudentClient -- HTTP/gRPC --> ExerciseService
    ExerciseService -- HTTP --> UCenterService
    ExerciseService -- HTTP --> QuestionService
    ExerciseService -- HTTP --> TeacherService
    ExerciseService -- SQL --> PostgreSQL
    ExerciseService -- Cache --> Redis
    ExerciseService -- Event --> Kafka
```

**关键数据契约**：
- **用户认证数据**：用户ID、角色信息、权限列表
- **题目数据**：题目ID、题干内容、选项列表、正确答案、难度等级
- **答题数据**：学生ID、题目ID、选择答案、答题时间、正确性标识
- **会话数据**：会话ID、学生ID、课程ID、当前进度、计时状态

### 3.4. 业务流程的高层视图 (High-Level View of Business Flows)

#### 3.4.1. 首次进入练习环节流程

```mermaid
sequenceDiagram
    participant Student as 学生客户端
    participant ExerciseService as 练习服务
    participant UCenterService as 用户中心服务
    participant QuestionService as 内容平台服务
    participant Cache as Redis缓存
    participant DB as PostgreSQL

    Student->>ExerciseService: 请求进入练习环节
    ExerciseService->>UCenterService: 验证用户身份
    UCenterService->>ExerciseService: 返回用户信息
    ExerciseService->>DB: 创建练习会话
    DB->>ExerciseService: 返回会话ID
    ExerciseService->>QuestionService: 请求首题推荐
    QuestionService->>ExerciseService: 返回题目信息
    ExerciseService->>Cache: 缓存会话状态
    ExerciseService->>Student: 返回转场信息和首题
```

#### 3.4.2. 答题与即时反馈流程

```mermaid
sequenceDiagram
    participant Student as 学生客户端
    participant ExerciseService as 练习服务
    participant QuestionService as 内容平台服务
    participant Cache as Redis缓存
    participant DB as PostgreSQL
    participant Kafka as 消息队列

    Student->>ExerciseService: 提交答题结果
    ExerciseService->>Cache: 获取会话状态
    ExerciseService->>QuestionService: 获取标准答案
    QuestionService->>ExerciseService: 返回答案信息
    ExerciseService->>ExerciseService: 判断答案正确性
    ExerciseService->>ExerciseService: 计算进度和连胜状态
    ExerciseService->>DB: 保存答题记录
    ExerciseService->>Cache: 更新会话状态
    ExerciseService->>Student: 返回即时反馈
    ExerciseService->>Kafka: 发送答题事件（异步）
```

#### 3.4.3. 再次进入练习环节流程

```mermaid
sequenceDiagram
    participant Student as 学生客户端
    participant ExerciseService as 练习服务
    participant UCenterService as 用户中心服务
    participant Cache as Redis缓存
    participant DB as PostgreSQL

    Student->>ExerciseService: 请求继续练习
    ExerciseService->>UCenterService: 验证用户身份
    UCenterService->>ExerciseService: 返回用户信息
    ExerciseService->>Cache: 查询会话状态
    alt 缓存命中
        Cache->>ExerciseService: 返回会话状态
    else 缓存未命中
        ExerciseService->>DB: 查询会话记录
        DB->>ExerciseService: 返回会话数据
        ExerciseService->>Cache: 重建缓存
    end
    ExerciseService->>Student: 返回继续练习信息
```

### 3.5. 数据架构概述 (Data Architecture Overview)

#### 3.5.1. 主要数据存储技术选型思路 (Primary Data Storage Choices - Conceptual)
- **PostgreSQL**：存储核心事务数据，包括练习会话、答题记录、进度数据
- **Redis**：缓存热点数据，包括会话状态、用户进度、题目信息
- **Kafka**：处理异步事件流，包括答题事件、进度更新事件

#### 3.5.2. 核心领域对象/数据结构的概念定义 (Conceptual Definition of Core Domain Objects/Data Structures)

**练习会话对象**：
- 核心属性：会话ID、学生ID、课程ID、创建时间、当前进度、计时状态、完成状态
- 关联关系：与学生、课程、答题记录等实体关联

**答题记录对象**：
- 核心属性：记录ID、会话ID、题目ID、学生答案、正确答案、答题时间、正确性标识
- 关联关系：与练习会话、题目等实体关联

**进度状态对象**：
- 核心属性：学生ID、课程ID、已完成题目数、预估总题目数、连胜次数、当前难度
- 关联关系：与学生、课程等实体关联

#### 3.5.3. 数据一致性与同步策略 (Data Consistency and Synchronization Strategies - Conceptual)
- **强一致性**：答题记录和会话状态更新采用数据库事务保证强一致性
- **最终一致性**：进度统计和缓存更新采用事件驱动的最终一致性模型
- **补偿机制**：通过Kafka事件重试和死信队列处理数据同步失败场景

## 4. 技术栈与关键库选型 (Key Technology Choices - Conceptual)

### 4.1. RPC/Web框架选型方向 (RPC/Web Frameworks Direction)
推荐使用Kratos 2.8.4框架，因其提供完整的微服务治理能力，包括服务发现、链路追踪、配置管理等；同时支持HTTP和gRPC双协议。

### 4.2. 数据库交互技术方向 (Database Interaction Technology Direction)
推荐使用GORM v1.25.12配合PostgreSQL驱动，因其提供ORM抽象的同时保持对SQL的控制力；使用连接池管理数据库连接以提升性能。

### 4.3. 消息队列技术方向 (Message Queue Technology Direction)
推荐使用Kafka 3.6作为消息队列，因其高吞吐量和持久化能力；使用segmentio/kafka-go客户端库，支持At-least-once语义。

### 4.4. 缓存技术方向 (Caching Technology Direction)
推荐使用Redis 6.0.3作为分布式缓存，使用go-redis/redis/v8客户端库；采用合理的过期策略和LRU淘汰策略。

### 4.5. 配置管理思路 (Configuration Management Approach)
推荐使用Nacos 2.4.3进行配置管理，结合环境变量进行本地开发配置；使用nacos-sdk-go进行配置加载和动态更新。

### 4.6. 测试策略与工具方向 (Testing Strategy and Tooling Direction)
强调单元测试使用标准库testing和testify；集成测试使用dockertest模拟依赖服务；使用Wire进行依赖注入以提升可测试性。

## 5. 跨功能设计考量 (Cross-Cutting Concerns - Architectural Perspective)

### 5.1. 安全考量 (Security Considerations)
- **认证机制**：集成用户中心服务进行JWT Token认证，通过Kratos中间件进行统一认证
- **权限控制**：基于用户角色和课程权限进行访问控制，确保学生只能访问自己的练习数据
- **数据加密**：敏感数据使用AES-256加密存储，传输过程使用HTTPS/TLS加密

### 5.2. 性能与并发考量 (Performance and Concurrency Considerations)
- **异步处理**：使用Kafka消息队列处理非关键路径的异步任务，如统计数据更新
- **无状态设计**：服务设计为无状态，支持水平扩展和负载均衡
- **Context管理**：使用context包进行请求链路的超时控制和取消传播

### 5.3. 错误处理与容错考量 (Error Handling and Fault Tolerance Considerations)
- **错误传递**：使用Kratos统一错误码规范，确保错误信息的一致性
- **重试机制**：对外部服务调用实现指数退避重试策略
- **优雅关闭**：实现优雅关闭机制，确保正在处理的请求能够完成

### 5.4. 可观测性考量 (Observability Considerations)
- **结构化日志**：使用logrus进行结构化日志记录，包含请求ID、用户ID等关键信息
- **链路追踪**：集成Zipkin 3.4.3进行分布式链路追踪，监控请求在各服务间的流转
- **健康检查**：提供健康检查端点，支持Kubernetes的liveness和readiness探针

## 6. 风险与挑战 (Architectural Risks and Challenges)

**风险1：外部服务依赖过多导致可用性风险**
- 影响：依赖用户中心、内容平台等多个外部服务，任一服务故障可能影响练习功能
- 应对：实现熔断机制和降级策略，缓存关键数据以支持离线模式

**风险2：实时性要求与数据一致性的平衡**
- 影响：即时反馈要求低延迟，但数据一致性要求可能增加延迟
- 应对：采用读写分离和缓存策略，关键数据保证强一致性，统计数据采用最终一致性

**风险3：高并发场景下的性能瓶颈**
- 影响：1000并发用户可能导致数据库和缓存压力过大
- 应对：实现数据库连接池、Redis集群、应用层限流和熔断机制

## 7. 未来演进方向 (Future Architectural Evolution)

- **智能推题算法优化**：引入机器学习算法，基于学生历史数据进行个性化推题
- **实时协作功能**：支持多学生同时练习的协作模式，引入WebSocket实时通信
- **微服务拆分**：随着业务复杂度增加，可考虑将推题策略、反馈生成等拆分为独立微服务

## 8. 架构文档自查清单 (Pre-Submission Checklist)

### 8.1 架构完备性自查清单

| 检查项 | 内容说明 | 检查结果 | 备注 |
| ------ | -------- | -------- | ---- |
| **核心架构图表清晰性** | 系统的分层视图、服务划分视图清晰表达了核心组件、职责边界和主要关系 | ✅ | 章节 3.2、3.3 |
| **业务流程覆盖完整性** | 业务流程视图完整覆盖了首次进入练习、答题反馈、再次进入练习等核心场景 | ✅ | 章节 3.4 |
| **领域概念抽象层级** | 核心领域对象定义停留在业务意义层面，未陷入具体字段定义等技术细节 | ✅ | 章节 3.5.2 |
| **外部依赖明确性** | 清晰说明了对用户中心、内容平台、教师服务的依赖关系和数据契约 | ✅ | 章节 2.3 |
| **技术选型合理性** | 技术栈选择都给出了充分理由，说明了如何满足性能、可维护性等目标 | ✅ | 章节 4 |
| **设计原则遵循** | 整体架构体现了SOLID、KISS、YAGNI、高内聚低耦合等设计原则 | ✅ | - |
| **模板指令遵守** | 严格遵守了模板中的特定指令，确保了设计的原创性和针对性 | ✅ | - |
| **模板完整性与聚焦性** | 填充了模板中的所有相关章节，内容聚焦于业务架构设计 | ✅ | - |

### 8.2 需求-架构完备性自查清单

| PRD核心需求点 (P0级) | 对应架构模块/服务/流程 | 完整性 | 正确性 | 一致性 | 可验证性（架构层面） | 可跟踪性 | 备注 |
| -------------------- | ---------------------- | ------ | ------ | ------ | -------------------- | -------- | ---- |
| 进入练习转场动效 | 首次进入练习流程 (3.4.1)，涉及练习服务、会话管理 (3.3) | ✅ | ✅ | ✅ | ✅ | ✅ | 架构通过会话管理和状态缓存支持转场功能 |
| 练习环节核心交互 | 练习服务核心模块 (3.3)，应用服务层 (3.2) | ✅ | ✅ | ✅ | ✅ | ✅ | 架构通过分层设计支持完整的练习交互 |
| 题目间即时反馈机制 | 答题与即时反馈流程 (3.4.2)，反馈服务 (3.2) | ✅ | ✅ | ✅ | ✅ | ✅ | 架构通过事件驱动和缓存策略支持即时反馈 |
| 作答进度计算规则 | 进度领域 (3.2)，进度状态对象 (3.5.2) | ✅ | ✅ | ✅ | ✅ | ✅ | 架构通过领域逻辑层封装进度计算规则 |
| 作答计时规则 | 会话服务 (3.2)，练习会话对象 (3.5.2) | ✅ | ✅ | ✅ | ✅ | ✅ | 架构通过会话状态管理支持计时功能 |
| 勾画功能复用 | 外部服务适配器 (3.2)，基础设施层 | ✅ | ✅ | ✅ | ✅ | ✅ | 架构通过适配器模式支持功能复用 |
| 错题本功能复用 | 外部服务适配器 (3.2)，基础设施层 | ✅ | ✅ | ✅ | ✅ | ✅ | 架构通过适配器模式支持功能复用 |
| 答疑组件集成 | 外部服务适配器 (3.2)，基础设施层 | ✅ | ✅ | ✅ | ✅ | ✅ | 架构通过适配器模式支持组件集成 |

## 9. 附录 (Appendix)

### 9.1. 参考资料
- [PRD - AI课中练习组件1.0](ai-generates/design/be-s1-1-ai-prd-Format-PRD-AI课中-练习组件1.0-claude-sonnet-4.md)
- [Kratos框架文档](https://go-kratos.dev/)
- [技术栈约束文档](ai-rules/backend/project-rules/golang/rules-tech-stack.md)
- [公共服务定义](ai-templates/be-service/)

--- 等待您的命令，指挥官
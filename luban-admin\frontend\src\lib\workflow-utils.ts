// 工作流参数传递工具函数

export interface WorkflowData {
  feishu_url?: string;        // 飞书文档URL
  format_prd_url?: string;    // 格式化PRD的URL
  ai_prd_url?: string;        // AI-PRD生成的URL
  ai_td_url?: string;         // AI-TD生成的URL
  ai_de_url?: string;         // AI-DE生成的URL
  ai_dd_url?: string;         // AI-DD生成的URL
  ai_ad_url?: string;         // AI-AD生成的URL
  ai_api_url?: string;        // AI-API生成的URL
}

/**
 * 从URL查询参数中解析工作流数据
 * @param searchParams - Next.js的searchParams对象
 * @returns 解析后的工作流数据
 */
export function parseWorkflowParams(searchParams: URLSearchParams | null): WorkflowData {
  if (!searchParams) return {};
  
  return {
    feishu_url: searchParams.get('feishu_url') || '',
    format_prd_url: searchParams.get('format_prd_url') || '',
    ai_prd_url: searchParams.get('ai_prd_url') || '',
    ai_td_url: searchParams.get('ai_td_url') || '',
    ai_de_url: searchParams.get('ai_de_url') || '',
    ai_dd_url: searchParams.get('ai_dd_url') || '',
    ai_ad_url: searchParams.get('ai_ad_url') || '',
    ai_api_url: searchParams.get('ai_api_url') || '',
  };
}

/**
 * 将工作流数据转换为URL查询参数
 * @param data - 工作流数据
 * @returns URL查询参数字符串
 */
export function buildWorkflowParams(data: WorkflowData): string {
  const params = new URLSearchParams();
  
  // 只添加非空的参数
  Object.entries(data).forEach(([key, value]) => {
    if (value && value.trim() !== '') {
      params.append(key, value);
    }
  });
  
  return params.toString();
}

/**
 * 构建跳转到下一个工作流页面的URL
 * @param nextPage - 下一个页面路径（如：'ai-de'）
 * @param currentData - 当前页面的工作流数据
 * @param newUrl - 当前页面生成的新URL
 * @param newUrlKey - 新URL对应的键名（如：'ai_td_url'）
 * @returns 完整的跳转URL
 */
export function buildNextPageUrl(
  nextPage: string,
  currentData: WorkflowData,
  newUrl: string,
  newUrlKey: keyof WorkflowData
): string {
  const updatedData = {
    ...currentData,
    [newUrlKey]: newUrl
  };
  
  const params = buildWorkflowParams(updatedData);
  
  // 保持debug参数
  let finalParams = params;
  if (typeof window !== 'undefined') {
    const currentParams = new URLSearchParams(window.location.search);
    const debugParam = currentParams.get('debug');
    if (debugParam) {
      const urlParams = new URLSearchParams(params);
      urlParams.set('debug', debugParam);
      finalParams = urlParams.toString();
    }
  }
  
  return `/workflow/${nextPage}${finalParams ? `?${finalParams}` : ''}`;
}

/**
 * 生成隐藏的input元素的数据结构
 * @param data - 工作流数据
 * @returns 隐藏input的数据数组
 */
export function generateHiddenInputsData(data: WorkflowData) {
  return Object.entries(data).map(([key, value]) => ({
    key,
    name: key,
    value: value || ''
  }));
}

/**
 * 获取工作流数据的键值对数组（用于渲染隐藏元素）
 * @param data - 工作流数据
 * @returns 键值对数组
 */
export function getWorkflowDataEntries(data: WorkflowData): Array<[string, string]> {
  return Object.entries(data).filter(([_, value]) => value && value.trim() !== '');
}

/**
 * 工作流页面配置
 */
export const WORKFLOW_PAGES = {
  'format-prd': {
    name: '格式化PRD',
    next: 'ai-prd',
    generates: ['feishu_url', 'format_prd_url']
  },
  'ai-prd': {
    name: 'AI-PRD',
    next: 'ai-td',
    receives: ['feishu_url', 'format_prd_url'],
    generates: ['ai_prd_url']
  },
  'ai-td': {
    name: 'AI-TD',
    next: 'ai-de',
    receives: ['feishu_url', 'format_prd_url', 'ai_prd_url'],
    generates: ['ai_td_url']
  },
  'ai-de': {
    name: 'AI-DE',
    next: 'ai-dd',
    receives: ['feishu_url', 'format_prd_url', 'ai_prd_url', 'ai_td_url'],
    generates: ['ai_de_url']
  },
  'ai-dd': {
    name: 'AI-DD',
    next: 'ai-ad',
    receives: ['feishu_url', 'format_prd_url', 'ai_prd_url', 'ai_td_url', 'ai_de_url'],
    generates: ['ai_dd_url']
  },
  'ai-ad': {
    name: 'AI-AD',
    next: 'ai-api',
    receives: ['feishu_url', 'format_prd_url', 'ai_prd_url', 'ai_td_url', 'ai_de_url', 'ai_dd_url'],
    generates: ['ai_ad_url']
  },
  'ai-api': {
    name: 'AI-API',
    next: 'ai-done',
    receives: ['feishu_url', 'format_prd_url', 'ai_prd_url', 'ai_td_url', 'ai_de_url', 'ai_dd_url', 'ai_ad_url'],
    generates: ['ai_api_url']
  },
  'ai-done': {
    name: 'AI-DONE',
    next: null,
    receives: ['feishu_url', 'format_prd_url', 'ai_prd_url', 'ai_td_url', 'ai_de_url', 'ai_dd_url', 'ai_ad_url', 'ai_api_url'],
    generates: []
  }
} as const;

/**
 * 获取当前页面的配置信息
 * @param pageName - 页面名称
 * @returns 页面配置
 */
export function getPageConfig(pageName: string) {
  return WORKFLOW_PAGES[pageName as keyof typeof WORKFLOW_PAGES];
}

/**
 * 验证工作流数据的完整性
 * @param data - 工作流数据
 * @param requiredFields - 必需的字段列表
 * @returns 验证结果
 */
export function validateWorkflowData(data: WorkflowData, requiredFields: string[]): {
  isValid: boolean;
  missingFields: string[];
} {
  const missingFields = requiredFields.filter(field => !data[field as keyof WorkflowData]);
  
  return {
    isValid: missingFields.length === 0,
    missingFields
  };
}

/**
 * 格式化URL用于显示
 * @param url - 原始URL
 * @param maxLength - 最大显示长度
 * @returns 格式化后的URL
 */
export function formatUrlForDisplay(url: string, maxLength: number = 50): string {
  if (!url) return '';
  if (url.length <= maxLength) return url;
  
  return `${url.substring(0, maxLength - 3)}...`;
}

/**
 * 检查URL是否有效
 * @param url - 要检查的URL
 * @returns 是否有效
 */
export function isValidUrl(url: string): boolean {
  if (!url) return false;
  
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}
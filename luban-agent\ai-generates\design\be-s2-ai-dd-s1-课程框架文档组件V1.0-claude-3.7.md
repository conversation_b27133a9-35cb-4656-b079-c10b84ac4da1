# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** PRD-AI课中-课程框架文档组件V1.0
- **PRD版本号：** V1.0
- **提取日期：** 2025-05-22

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称 | PRD中的描述 | PRD中对应的章节号 |
| ------- | ---------- | ---------------- |
| 课程 | AI单节课，由多个可配置的组件按特定顺序组合而成。 | 3.1 |
| 组件 | 课程中的基本构成单元，包括文档组件、练习组件等。 | 2, 3.1 |
| 文档组件 | 承载图文、音视频等多媒体静态或动态教学内容的模块，支持学生自由浏览和提问。 | 2 |
| 练习组件 | 用于实现在线、可交互的练习题、测试题或操作型任务的模块。 | 2 |
| 答疑组件 | 基于大模型能力，在文档或练习组件下为学生提供实时对话、解答问题的模块。 | 2 |
| IP角色 | AI课程中使用的具有特定形象和人设的虚拟角色，用于增强互动性和趣味性。 | 2 |
| 用户（学生） | 使用AI课程进行学习的学生用户。 | 1.4 |
| 学习记录 | 记录用户在课程中的学习进度、答题情况等信息。 | 6.1.3, 6.1.4 |
| 外化掌握度 | 系统向用户展示的对其知识点掌握情况的评估结果，强调非递减性以提供正向激励。 | 2, 7.4 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`课程`**

| 属性名称 | PRD中的描述 / 用途 | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| ------- | ---------------- | ---------------------------- | ----------------------------------------------------------- | --------------------- | ----------------------------- | -------------------------- |
| 课程ID | 课程的唯一标识符 | 字符串 | 唯一, 非空 | | 主键 | 3.1 |
| 章节信息 | 课程所属章节(如"第一章") | 字符串 | 非空 | | | ******* |
| 课程序号 | 课程的序号(如"2.1.1") | 字符串 | 非空 | | | ******* |
| 课程名称 | 取对应业务树末级知识点名称 | 字符串 | 25个汉字以内, 非空 | | | ******* |
| 描述性文案 | 课程的简短描述 | 字符串 | | | | ******* |
| 学科ID | 课程所属学科的ID | 整数 | 非空 | | 外键 | ******* |
| 开场页背景图 | 不同学科可配置不同的开场页背景图 | 字符串(URL) | | | | ******* |
| 开场页色值 | 不同学科可配置不同的开场页色值 | 字符串 | | | | ******* |
| IP动效资源 | 课程开场的IP动效资源 | 字符串(URL) | | | | ******* |
| 音频资源 | 课程开场的音频资源 | 字符串(URL) | | | | ******* |
| 标准时长 | 课程的标准学习时长 | 整数(秒) | 非负整数 | | | 8.2 |
| 总组件数 | 一节课中(文档组件+练习组件)的总数量 | 整数 | 非负整数 | | | 7.7 |
| 创建时间 | 课程创建的时间戳 | 时间戳 | 非空 | | | PRD未明确 |
| 更新时间 | 课程最后更新的时间戳 | 时间戳 | 非空 | | | PRD未明确 |

**实体：`组件`**

| 属性名称 | PRD中的描述 / 用途 | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| ------- | ---------------- | ---------------------------- | ----------------------------------------------------------- | --------------------- | ----------------------------- | -------------------------- |
| 组件ID | 组件的唯一标识符 | 字符串 | 唯一, 非空 | | 主键 | 3.1 |
| 课程ID | 组件所属课程的ID | 字符串 | 非空 | | 外键 | 3.1 |
| 组件类型 | 组件的类型（文档组件、练习组件） | 整数 | 非空, 枚举值(1:文档组件, 2:练习组件) | | | 3.1 |
| 组件名称 | 组件的名称(如"课程引入"、"知识点1") | 字符串 | 非空 | | | 3.1 |
| 序号 | 组件在课程中的顺序 | 整数 | 非空, 非负整数 | | | 3.1 |
| 状态 | 组件状态（已解锁、学习中、待解锁） | 整数 | 非空, 枚举值(1:已解锁, 2:学习中, 3:待解锁) | 3(待解锁) | | 6.1.3.1 |
| 创建时间 | 组件创建的时间戳 | 时间戳 | 非空 | | | PRD未明确 |
| 更新时间 | 组件最后更新的时间戳 | 时间戳 | 非空 | | | PRD未明确 | 

**实体：`文档组件`**

| 属性名称 | PRD中的描述 / 用途 | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| ------- | ---------------- | ---------------------------- | ----------------------------------------------------------- | --------------------- | ----------------------------- | -------------------------- |
| 文档组件ID | 文档组件的唯一标识符 | 字符串 | 唯一, 非空 | | 主键 | 2 |
| 组件ID | 关联的基础组件ID | 字符串 | 非空 | | 外键 | 2, 3.1 |
| JS动画内容 | 文档组件中呈现的动态板书内容 | 字符串(URL) | 非空 | | | 2 |
| 数字人视频 | 教师讲解视频 | 字符串(URL) | 非空 | | | 6.2.1.2 |
| 勾画轨迹 | 教师在讲解过程中在板书上进行的勾画、标记的轨迹记录 | JSON | | | | 2, 6.2.2.1 |
| 字幕内容 | 数字人视频的字幕 | 文本 | | | | 6.2.2.1 |
| 字幕状态 | 字幕是否开启 | 布尔值 | | true(默认开启) | | 6.2.2.1 |
| 默认模式 | 文档组件的默认播放模式 | 整数 | 非空, 枚举值(1:跟随模式, 2:自由模式) | 1(跟随模式) | | 6.2.1.1 |
| 标准时长 | 文档组件的标准播放时长 | 整数(秒) | 非负整数 | | | 8.2 |
| 创建时间 | 文档组件创建的时间戳 | 时间戳 | 非空 | | | PRD未明确 |
| 更新时间 | 文档组件最后更新的时间戳 | 时间戳 | 非空 | | | PRD未明确 | 

**实体：`练习组件`**

| 属性名称 | PRD中的描述 / 用途 | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| ------- | ---------------- | ---------------------------- | ----------------------------------------------------------- | --------------------- | ----------------------------- | -------------------------- |
| 练习组件ID | 练习组件的唯一标识符 | 字符串 | 唯一, 非空 | | 主键 | 2 |
| 组件ID | 关联的基础组件ID | 字符串 | 非空 | | 外键 | 2, 3.1 |
| 题目数量 | 练习组件中的题目总数 | 整数 | 非负整数, 非空 | | | *******, 6.1.3.2 |
| 题目类型 | 支持的题目类型 | 整数 | 枚举值(1:单选题等) | 1(一期仅支持单选题) | | 2 |
| 创建时间 | 练习组件创建的时间戳 | 时间戳 | 非空 | | | PRD未明确 |
| 更新时间 | 练习组件最后更新的时间戳 | 时间戳 | 非空 | | | PRD未明确 | 

**实体：`答疑组件`**

| 属性名称 | PRD中的描述 / 用途 | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| ------- | ---------------- | ---------------------------- | ----------------------------------------------------------- | --------------------- | ----------------------------- | -------------------------- |
| 答疑组件ID | 答疑组件的唯一标识符 | 字符串 | 唯一, 非空 | | 主键 | 2 |
| 挂载组件ID | 答疑组件挂载的文档或练习组件ID | 字符串 | 非空 | | 外键 | 2, 3.1 |
| 挂载组件类型 | 挂载组件的类型 | 整数 | 非空, 枚举值(1:文档组件, 2:练习组件) | | | 2, 3.1 |
| 会话上下文 | 每个视频/题目的会话上下文 | JSON | | | | 8.2 |
| 创建时间 | 答疑组件创建的时间戳 | 时间戳 | 非空 | | | PRD未明确 |
| 更新时间 | 答疑组件最后更新的时间戳 | 时间戳 | 非空 | | | PRD未明确 | 

**实体：`IP角色`**

| 属性名称 | PRD中的描述 / 用途 | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| ------- | ---------------- | ---------------------------- | ----------------------------------------------------------- | --------------------- | ----------------------------- | -------------------------- |
| IP角色ID | IP角色的唯一标识符 | 字符串 | 唯一, 非空 | | 主键 | 2 |
| 名称 | IP角色的名称 | 字符串 | 非空 | | | 2 |
| 形象描述 | IP角色的外观和人设描述 | 文本 | | | | 2 |
| 开心动效 | IP角色的"开心"类动效资源 | 字符串(URL) | | | | ******* |
| 鼓励动效 | IP角色的"鼓励"类动效资源 | 字符串(URL) | | | | ******* |
| 反馈文案库 | 与IP角色相关的反馈文案库 | JSON | | | | ******* |
| 创建时间 | IP角色创建的时间戳 | 时间戳 | 非空 | | | PRD未明确 |
| 更新时间 | IP角色最后更新的时间戳 | 时间戳 | 非空 | | | PRD未明确 | 

**实体：`用户（学生）`**

| 属性名称 | PRD中的描述 / 用途 | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| ------- | ---------------- | ---------------------------- | ----------------------------------------------------------- | --------------------- | ----------------------------- | -------------------------- |
| 用户ID | 用户的唯一标识符 | 字符串 | 唯一, 非空 | | 主键 | 1.4 |
| 学校ID | 用户所属学校ID | 整数 | 非空 | | 外键 | 8.1 |
| 创建时间 | 用户记录创建的时间戳 | 时间戳 | 非空 | | | PRD未明确 |
| 更新时间 | 用户记录最后更新的时间戳 | 时间戳 | 非空 | | | PRD未明确 | 

**实体：`学习记录`**

| 属性名称 | PRD中的描述 / 用途 | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| ------- | ---------------- | ---------------------------- | ----------------------------------------------------------- | --------------------- | ----------------------------- | -------------------------- |
| 学习记录ID | 学习记录的唯一标识符 | 字符串 | 唯一, 非空 | | 主键 | 6.1.3, 6.1.4 |
| 用户ID | 用户的ID | 字符串 | 非空 | | 外键 | 6.1.3, 6.1.4 |
| 课程ID | 课程的ID | 字符串 | 非空 | | 外键 | 6.1.3, 6.1.4 |
| 学习时长 | 用户完成本节课的累计学习时长(秒) | 整数 | 非负整数, 非空 | 0 | | 7.1 |
| 答题数量 | 用户在本节课本次学习中的累计答题数量 | 整数 | 非负整数, 非空 | 0 | | 7.2 |
| 正确数量 | 用户在本节课本次学习中答对的题目数量 | 整数 | 非负整数, 非空 | 0 | | 7.3 |
| 正确率 | 答对的题目数量/答题总数量 | 浮点数 | [0,1], 非空 | 0 | | 7.3 |
| 当前组件ID | 用户当前学习的组件ID | 字符串 | | | | 6.1.3, 6.1.4 |
| 当前组件播放时间点 | 针对文档组件，记录用户退出时的播放时间点(秒) | 整数 | 非负整数 | 0 | | 6.1.4.1 |
| 当前题目序号 | 针对练习组件，记录用户退出时的题目序号 | 整数 | 非负整数 | 1 | | 6.1.4.1 |
| 学习进度 | 已学完的组件数量/总组件数量 | 浮点数 | [0,1], 非空 | 0 | | 7.7 |
| 首次学习标记 | 标记是否为用户首次学习该课程 | 布尔值 | 非空 | true | | 7.4 |
| 完成状态 | 记录课程是否已完成学习 | 布尔值 | 非空 | false | | ******* |
| 学习开始时间 | 本次学习的开始时间 | 时间戳 | 非空 | | | PRD未明确 |
| 学习结束时间 | 本次学习的结束时间 | 时间戳 | | | | PRD未明确 |
| 创建时间 | 学习记录创建的时间戳 | 时间戳 | 非空 | | | PRD未明确 |
| 更新时间 | 学习记录最后更新的时间戳 | 时间戳 | 非空 | | | PRD未明确 | 

**实体：`外化掌握度`**

| 属性名称 | PRD中的描述 / 用途 | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| ------- | ---------------- | ---------------------------- | ----------------------------------------------------------- | --------------------- | ----------------------------- | -------------------------- |
| 掌握度ID | 掌握度记录的唯一标识符 | 字符串 | 唯一, 非空 | | 主键 | 2, 7.4 |
| 用户ID | 用户的ID | 字符串 | 非空 | | 外键 | 7.4 |
| 课程ID | 关联课程的ID | 字符串 | 非空 | | 外键 | 7.4 |
| 知识点ID | 关联知识点的ID(来自业务知识树) | 字符串 | 非空 | | 外键(来自公共服务) | 7.4 |
| 掌握度值 | 用户对知识点的掌握程度 | 浮点数 | [0,1], 非空 | 0 | | 7.4 |
| 目标掌握度 | 用户设定的目标掌握度 | 浮点数 | [0,1], 非空 | 1 | | 7.4, ******* |
| 掌握度完成进度 | 当前掌握度/目标掌握度 | 浮点数 | [0,1], 保留小数点后两位, 非空 | 0 | | 7.4 |
| 掌握度变化 | 本次学习后的进度-本次学习前的进度 | 浮点数 | 非负数(外化值不下降), 非空 | 0 | | 7.4 |
| 上次学习掌握度 | 上次学习后的掌握度值 | 浮点数 | [0,1] | 0 | | 7.4 |
| 创建时间 | 掌握度记录创建的时间戳 | 时间戳 | 非空 | | | PRD未明确 |
| 更新时间 | 掌握度记录最后更新的时间戳 | 时间戳 | 非空 | | | PRD未明确 | 

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 (例如：用户 '拥有一个' 学校档案) | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 课程由多个组件组成 | 课程 | 组件 | 1:N | 组件.课程ID -> 课程.课程ID | 3.1 |
| 组件可以是文档组件 | 组件 | 文档组件 | 1:1 | 文档组件.组件ID -> 组件.组件ID | 2, 3.1 |
| 组件可以是练习组件 | 组件 | 练习组件 | 1:1 | 练习组件.组件ID -> 组件.组件ID | 2, 3.1 |
| 答疑组件挂载在文档组件或练习组件上 | 答疑组件 | 文档组件/练习组件 | 1:1 | 答疑组件.挂载组件ID -> (文档组件.组件ID或练习组件.组件ID) | 2, 3.1 |
| 用户学习课程生成学习记录 | 用户 | 学习记录 | 1:N | 学习记录.用户ID -> 用户.用户ID | 6.1.3, 6.1.4 |
| 课程被多个用户学习 | 课程 | 学习记录 | 1:N | 学习记录.课程ID -> 课程.课程ID | 6.1.3, 6.1.4 |
| 用户对课程相关知识点有掌握度 | 用户 | 外化掌握度 | 1:N | 外化掌握度.用户ID -> 用户.用户ID | 7.4 |
| 课程与外化掌握度关联 | 课程 | 外化掌握度 | 1:N | 外化掌握度.课程ID -> 课程.课程ID | 7.4 | 

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 课程学习时长 | 用户在课程中的学习时长(秒) | 学习记录.学习时长 | 7.1 |
| 课程学习进度 | 已学完的组件数量，总组件数量 | 学习记录.学习进度 | 7.7 |
| 课程正确率 | 答对的题目数量，答题总数量 | 学习记录.正确率 | 7.3 |
| 外化掌握度完成进度 | 当前掌握度值，目标掌握度 | 外化掌握度.掌握度完成进度 | 7.4 |
| 外化掌握度变化 | 本次学习后的进度，本次学习前的进度 | 外化掌握度.掌握度变化 | 7.4 | 

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 组件.状态 | 用户学习进度变化时更新组件状态 | 实时 | 6.1.3.1 |
| 学习记录.学习时长 | 用户完成学习或暂时退出学习时累计学习时长 | 实时 | 7.1 |
| 学习记录.答题数量 | 用户完成答题时 | 实时 | 7.2 |
| 学习记录.正确数量 | 用户答对题目时 | 实时 | 7.3 |
| 学习记录.正确率 | 用户完成答题后计算 | 实时 | 7.3 |
| 学习记录.学习进度 | 用户完成组件学习时更新 | 实时 | 7.7 |
| 学习记录.完成状态 | 用户完成课程全部组件的学习时更新 | 实时 | ******* |
| 外化掌握度.掌握度值 | 用户完成课程学习后 | 每次完成课程学习 | 7.4 |
| 外化掌握度.掌握度完成进度 | 用户完成课程学习后 | 每次完成课程学习 | 7.4 |
| 外化掌握度.掌握度变化 | 用户完成课程学习后 | 每次完成课程学习 | 7.4 | 

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 课程.章节信息 | 从配课系统获取 | ******* |
| 课程.课程序号 | 从配课系统获取 | ******* |
| 课程.课程名称 | 取对应业务树末级知识点名称，从业务知识树服务获取 | ******* |
| 课程.描述性文案 | 从配课系统获取 | ******* |
| 课程.学科ID | 从配课系统获取 | ******* |
| 文档组件.JS动画内容 | 从配课系统获取 | 2, 6.2.1.2 |
| 文档组件.数字人视频 | 从配课系统获取 | 6.2.1.2 |
| 文档组件.勾画轨迹 | 从配课系统获取 | 6.2.2.1 |
| 文档组件.字幕内容 | 从配课系统获取 | 6.2.2.1 |
| 练习组件.题目 | 从题库服务获取 | 2, ******* |
| 用户.学校ID | 从用户中心服务获取 | 8.1 |
| 外化掌握度.知识点ID | 从业务知识树服务获取 | 7.4 | 

---

## 7. 当前版本明确排除的数据需求 (例如PRD中的V1.0版本)

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 (源自PRD"非本期功能"等章节) | PRD中说明的排除原因 (若有) | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 多种题型支持 | V1.0版本仅支持单选题，其他题型后续版本实现 | 2 |
| 文档组件与练习组件以外的其他组件类型 | V1.0版本仅支持文档组件和练习组件 | 2, 3.1 |
| 文档组件自由模式中支持放大缩小功能 | V1.0版本暂不支持 | 6.2.1.3 |
| 自由调整文档组件中字幕显示位置 | V1.0版本暂不支持 | 6.2.2.1 | 

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果(✅/❌/⚠️/N/A) | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏（例如，本示例中的"题目信息"、"课程信息"）？                                                         | ⚠️ | 题目信息可从题库服务获取，未作为独立实体列出 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 关于课程组件的顺序逻辑以及组件解锁条件，PRD中描述不够详细，需进一步澄清 | 
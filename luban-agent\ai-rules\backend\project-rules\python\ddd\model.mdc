---
description: 
globs: backend/app/domain/entities/**/*.py,backend/app/domain/value_objects/**/*.py
alwaysApply: false
---
# 领域模型 (Domain Model) 代码生成规则

> **核心原则：简洁优先**

## 边界
- 仅依赖 Python 标准库与 `pydantic.BaseModel`；禁止直接 I/O  
- 聚合根负责聚合内一致性；其他实体须经聚合根持久化  
- 值对象必须自验证；可使用 `pydantic.BaseModel(frozen=True)` 或纯 Python 不变类  
- 领域服务处理跨聚合逻辑，保持无状态、无 I/O  
- 如需并发写，使用乐观锁或版本号  
- 业务动作完成时，可向事件队列追加领域事件  

## 约定
- 实体 / 聚合根可继承 `Entity` / `AggregateRoot` 或纯类  
- 命名遵循领域语言，例如 `User`, `Username`  
- 工厂方法命名 `create_*` / `from_*`；验证方法 `validate_*` / `ensure_*`  
- 业务异常继承 `DomainError`，错误信息面向业务人员  
- 统一通过 `timezone.now()` 获取业务时间  
- 为所有方法与属性添加类型注解   

## 示例代码
```python
class Username:
    """用户名值对象——纯 Python 不变类"""
    USERNAME_PATTERN = r'^[a-zA-Z][a-zA-Z0-9_]{3,19}$'

    def __init__(self, value: str):
        self._value = self.normalize(value)
        self.validate(self._value)

    # 省略其余实现 …

class User:
    """用户实体——不用继承基类亦可"""

    def update_last_login(self) -> None:
        self.last_login_at = timezone.now()
```
          self.last_login_at = datetime.now()
  ```
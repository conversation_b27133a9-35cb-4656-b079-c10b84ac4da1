---
description: PostgreSQL数据库开发规范与最佳实践指南
globs: ["**/*.sql", "**/*.go", "**/*.py"]
alwaysApply: false
---

# PostgreSQL 开发规范与最佳实践

## 背景目标

目前已有相关同学调研了部分postgres的优势和常见sql语句，所以本文章侧重于如何建设一张标准规范表来入手，开发中如何避免常见的坑点。

**参考核心文档：**
- [阿里云postgresql开发规范](https://developer.aliyun.com/article/60899)

## 一、建表规范

### 1.1 字段命名规范

- **数据库名**：`db_` + 小写名称
- **表名规范**：`tbl_` + 小写名称
- **字段名规范**：业务字段需要加表业务前缀，如 `student_score`
- **整数类型**：涉及到int型，统一都用 `BIGINT` 存储

### 1.2 基础函数部分

#### 触发器函数：每次更新时自动更新updated_time

> 📝 **注意**：这个每次建表只创建一次即可

```sql
-- 安全创建触发器函数（推荐方式）
DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM pg_proc JOIN pg_namespace 
              ON pg_proc.pronamespace = pg_namespace.oid
            WHERE proname = 'update_timestamp'
              AND nspname = 'public'
        ) THEN
            EXECUTE 'CREATE FUNCTION update_timestamp()
                    RETURNS TRIGGER AS $update_timestamp$
                    BEGIN
                        NEW.update_time = CURRENT_TIMESTAMP;
                        RETURN NEW;
                    END;
                    $update_timestamp$ LANGUAGE plpgsql;';
        END IF;
    END $$;
```

```sql
-- 简单创建方式（下面的方案不能保证绝对安全）
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.update_time = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```

### 1.3 建数据库模板

```sql
CREATE DATABASE db_gil_question;
```

### 1.4 建表模板（重要！！！）

```sql
BEGIN;
CREATE TABLE IF NOT EXISTS tbl_student (
    student_id BIGSERIAL PRIMARY KEY, -- 必须用BIGSERIAL，否则可能有越界风险
    student_name VARCHAR(100) NOT NULL DEFAULT '', -- varchar长度不能超过1千万
    student_no VARCHAR(20) UNIQUE NOT NULL DEFAULT '',
    student_age BIGINT NOT NULL DEFAULT 0,
    student_grade VARCHAR(20) NOT NULL DEFAULT '',
    student_score BIGINT NOT NULL DEFAULT 0,
    student_info JSONB NOT NULL DEFAULT '{}', -- json串
    details TEXT NOT NULL DEFAULT '', -- 超长text没有长度限制，若不是json，默认空字符串
    creater_id BIGINT DEFAULT 0,
    updater_id BIGINT DEFAULT 0,
    deleted BIGINT DEFAULT 0,
    create_time TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
) WITH (fillfactor=85); -- 建议建表时指定表的fillfactor=85，每页预留15%的空间给HOT更新使用

-- 创建索引score字段的索引
CREATE INDEX idx_student_score_tbl_student ON tbl_student(student_score);

-- 创建唯一索引student_no字段的索引，唯一索引需要和deleted配合构建，防止多次不能插入
CREATE UNIQUE INDEX idx_student_no_deleted_tbl_student ON tbl_student (student_no, deleted);

-- 基础时间字段索引添加, 时间字段推荐使用brin索引，主要用于范围类查询，创建时间都是顺序存储
CREATE INDEX idx_create_time_tbl_student ON tbl_student USING brin (create_time);
-- 更新时间可能会有变化，但是时间更新一般也就是更新状态，只是相对顺序排序，可以采用brin。
-- 但是超长时间随机改动的时间，可能需要去掉USING brin
CREATE INDEX idx_update_time_tbl_student ON tbl_student USING brin (update_time);

-- 添加字段注释
COMMENT ON COLUMN tbl_student.student_id IS '学生ID，自增';
COMMENT ON COLUMN tbl_student.student_name IS '学生名，默认空字符串';
COMMENT ON COLUMN tbl_student.student_no IS '学号，默认空字符串，自动创建唯一索引';
COMMENT ON COLUMN tbl_student.student_age IS '年龄，默认0';
COMMENT ON COLUMN tbl_student.student_grade IS '年级，默认空字符串';
COMMENT ON COLUMN tbl_student.student_score IS '学生成绩，默认0';
COMMENT ON COLUMN tbl_student.student_info IS '学生信息，默认空';
COMMENT ON COLUMN tbl_student.details IS '详细信息，默认空字符串';
COMMENT ON COLUMN tbl_student.creater_id IS '创建者用户id';
COMMENT ON COLUMN tbl_student.updater_id IS '更新者用户id';
COMMENT ON COLUMN tbl_student.deleted IS '删除标记（逻辑删除），默认0';
COMMENT ON COLUMN tbl_student.create_time IS '创建时间，默认当前时间';
COMMENT ON COLUMN tbl_student.update_time IS '更新时间，默认当前时间';

-- 添加表注释
COMMENT ON TABLE tbl_student IS '学生表';

COMMIT;

-- 创建触发器
CREATE TRIGGER update_student_time
BEFORE UPDATE ON tbl_student
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();
```

### 1.5 查看表名SQL语句

```sql
SELECT tablename FROM pg_tables WHERE tablename LIKE 'tbl_%' ORDER BY tablename;
```

### 1.6 查看建表信息

#### 方法一

```sql
SELECT a.attnum,
       a.attname AS field,
       t.typname AS type,
       a.attlen AS length,
       a.atttypmod AS lengthvar,
       a.attnotnull AS notnull,
       b.description AS comment
FROM pg_class c,
     pg_attribute a
       LEFT OUTER JOIN pg_description b ON a.attrelid=b.objoid AND a.attnum = b.objsubid,
     pg_type t
WHERE c.relname = 'tbl_student'
  and a.attnum > 0
  and a.attrelid = c.oid
  and a.atttypid = t.oid
ORDER BY a.attnum;
```

#### 方法二

```sql
SELECT
    col.table_schema,
    col.table_name,
    col.ordinal_position,
    col.column_name,
    col.data_type,
    col.character_maximum_length,
    col.numeric_precision,
    col.numeric_scale,
    col.is_nullable,
    col.column_default,
    des.description
FROM
    information_schema.columns col LEFT JOIN pg_description des ON
        col.table_name::regclass = des.objoid
            AND col.ordinal_position = des.objsubid
WHERE
    table_schema = 'public'
  AND table_name = 'tbl_student'
ORDER BY
    ordinal_position;
```

## 二、设计原则与注意事项

### 2.1 分表ID逻辑

> ⚠️ **重要提醒**：分表id的逻辑不能通过阿里云规范提到的方法来做。

**理由：**
- 如果只用一个数据库分表，未来从不变化就可以按照规范来
- 但是如果需要迁移库，或者扩大分表，这种方式就有潜在风险
- 比如固定的id被其它系统引用，新的分表id增长都是一个问题
- **推荐解决方案**：通过发号器来解决
- **兼容性考虑**：哪天要切换到mysql，不会有大的改动

### 2.2 枚举类型处理

关于 `type`、`status` 等可枚举类型，直接 `VARCHAR` 或者 `BIGINT` 类型替代。

**原因：**
- 官方有枚举类型和bool类型，但是枚举添加一个值涉及到字段枚举的改动
- 不利于快速迭代

**参考文档：**
- [PostgreSQL数据类型官方文档](https://www.postgresql.org/docs/current/datatype.html)
- [唯一索引文档](https://www.postgresql.org/docs/current/indexes-unique.html)

### 2.3 字符集

建表默认字符集是utf8，暂时不需要考虑字符集的问题。可能就是需要考虑一些特殊符号类的，一般的情况后续需要测试看看具体情况。

**查询字符集命令：**

```sql
SELECT datname, pg_encoding_to_char(encoding) AS encoding
FROM pg_database
WHERE datname = 'your_database_name';
```

或者执行 `\l` 查看数据库列表，里面有字段。

### 2.4 索引使用原则

> ❌ **不建议使用Hash索引**

**考虑理由：**
1. 有hash冲突
2. 无法等值查询
3. B树已经够快，只需要几次即可
4. **阿里云的观点**：不要使用hash index，目前hash index不写REDO，在备库只有结构，没有数据，并且数据库crash后无法恢复

同时不建议使用 `unlogged table`，道理同上，但是如果你的数据不需要持久化，则可以考虑使用 `unlogged table` 来提升数据的写入和修改性能。

## 三、增删改查规范

### 3.1 查询规范

根据实际使用情况选取的查询规范：

#### 3.1.1 COUNT使用规范

1. **不要使用 `count(列名)` 或 `count(常量)` 来替代 `count(*)`**
   - `count(*)` 就是SQL92定义的标准统计行数的语法，跟数据库无关，跟NULL和非NULL无关
   - `count(*)` 会统计NULL值（真实行数），而 `count(列名)` 不会统计
   - `count` 多列，也没有太大必要，跟 `count(*)` 效果一样，多列null值也会被统计

2. **在函数中，或程序中，不要使用 `count(*)` 判断是否有数据，很慢**
   - **建议方法**：使用 `LIMIT 1`

#### 3.1.2 临时表使用

3. **避免频繁创建和删除临时表**，以减少系统表资源的消耗
   - 因为创建临时表会产生元数据，频繁创建，元数据可能会出现碎片

#### 3.1.3 分页查询注意点

4. **关于分页查询注意点：**
   - **浅分页**：只能是先取数量，然后 `LIMIT OFFSET` 解决。如果不对分页数量有要求，则可以考虑pg游标
   - **深分页**：可以考虑更推荐使用ES来解决，返回结果对于分页很友好，也支持百万级别的深分页
   - **深分页优化**：若对分页数量不需要精确的统计，要得到较好的结果，或者特定场景参考：[深分页优化方案](https://developer.aliyun.com/article/39682)

#### 3.1.4 其他查询注意事项

5. **查询额外注意 `deleted = 0`**

6. **在函数中，或程序中，不要使用 `count(*)` 判断是否有数据，很慢**
   - **建议方法**：使用 `LIMIT 1`

7. **使用RETURNING语法减少数据库交互**
   - 如果需要在插入数据和，删除数据前，或者修改数据后马上拿到插入或被删除或修改后的数据
   - **建议使用**：`INSERT INTO .. RETURNING ..`、`DELETE .. RETURNING ..` 或 `UPDATE .. RETURNING ..` 语法
   - **目的**：减少数据库交互次数

### 3.2 修改规范

#### 3.2.1 库存场景处理

库存场景，一定要使用 `advisory_lock` 先对记录的唯一ID进行锁定，拿到AD锁再去对数据进行更新操作。拿不到锁时，可以尝试重试拿锁。

**PostgreSQL函数示例：**

```sql
CREATE OR REPLACE FUNCTION public.f(i_id integer)    
 RETURNS void    
 LANGUAGE plpgsql    
AS $function$   
declare   
  a_lock boolean := false;  
begin   
  select pg_try_advisory_xact_lock(i_id) into a_lock;  
  --拿到锁，更新  
  if a_lock then  
    update t1 set count=count-1 where id=i_id;   
  end if;  
  exception when others then    
    return;   
end;   
$function$;    
  
select f(id) from tbl where id=? and count>0;
```

**Go语言示例：**

```go
package main

import (
        "context"
        "database/sql"
        "fmt"
        "log"

        _ "github.com/lib/pq" // 导入 PostgreSQL 驱动
)

func main() {
        // 连接数据库
        connStr := "user=youruser dbname=yourdb sslmode=disable password=yourpassword"
        db, err := sql.Open("postgres", connStr)
        if err != nil {
                log.Fatal(err)
        }
        defer db.Close()

        // 测试咨询锁
        lockKey := int64(12345) // 锁的唯一键值
        acquired, err := tryAdvisoryLock(db, lockKey)
        if err != nil {
                log.Fatal(err)
        }

        if acquired {
                fmt.Println("锁获取成功！")
                // 在这里执行需要锁保护的逻辑
                defer releaseAdvisoryLock(db, lockKey) // 确保在函数结束时释放锁
        } else {
                fmt.Println("锁获取失败，锁已被其他事务占用。")
        }
}

// tryAdvisoryLock 尝试获取咨询锁
func tryAdvisoryLock(db *sql.DB, key int64) (bool, error) {
        var acquired bool

        // 执行 pg_try_advisory_xact_lock 函数
        query := `SELECT pg_try_advisory_xact_lock($1)`
        err := db.QueryRow(query, key).Scan(&acquired)
        if err != nil {
                return false, fmt.Errorf("尝试获取咨询锁失败: %v", err)
        }

        return acquired, nil
}

// releaseAdvisoryLock 释放咨询锁
func releaseAdvisoryLock(db *sql.DB, key int64) error {
        // 在事务提交或回滚时，咨询锁会自动释放，因此无需显式释放
        // 这里只是示例，实际使用时不需要显式调用释放函数
        fmt.Println("事务结束，咨询锁已自动释放。")
        return nil
}
```

#### 3.2.2 幂等操作

注意幂等操作：
1. 先 `setnx`（依据请求量情况可选，如运营后端不需要，或者c端没有单用户冲突也可以考虑不用）
2. `upsert`（先select，空insert，非空update）

#### 3.2.3 软删除处理

修改额外注意 `deleted = 0` 处理。

### 3.3 删除数据规范

1. **代码不可以做硬删除**，只能是通过 `deleted` 字段软删除，值为自增id
   - 如果仅仅是bool类型，则某个带uniq的值在二次删除后会失败

2. **删除任何数据，先必须留有备份**

## 四、字段操作

### 4.1 新增字段

PostgreSQL新增字段只能加在所有字段后面，无法指定位置添加。

```sql
ALTER TABLE tbl_biz_tree_info ADD COLUMN biz_tree_type BIGINT NOT NULL DEFAULT 0;
COMMENT ON COLUMN tbl_biz_tree_info.biz_tree_type IS '业务树类型';
```

**参考文档：**
- [阿里云新增字段文档](https://developer.aliyun.com/article/7176)

## 五、索引操作

### 5.1 历史遗留索引删除

> ⚠️ **注意**：要指定表名删除，constraint代指4种索引类型：Primary Key、Foreign Key、Unique、Check

```sql
ALTER TABLE tbl_paper DROP CONSTRAINT tbl_paper_paper_id_key;
```

**参考文档：**
- [PostgreSQL约束删除指南](https://www.beekeeperstudio.io/blog/how-to-drop-a-constraint-in-postgresql)

## 六、PostgreSQL对比MySQL详细分析

### 6.1 事务与并发控制

#### PostgreSQL
- **MVCC（多版本并发控制）**：采用先进的MVCC实现机制，提供高并发性能和复杂事务场景支持
- **事务隔离级别**：完整支持SQL标准定义的四种隔离级别（READ UNCOMMITTED、READ COMMITTED、REPEATABLE READ、SERIALIZABLE）
- **并发性能**：高并发场景下具备优异性能表现，锁竞争开销较低
- **死锁处理**：提供智能化死锁检测和自动处理机制

#### MySQL（InnoDB 引擎）
- **MVCC支持**：支持MVCC机制，但高并发场景下性能表现逊于PostgreSQL
- **锁机制**：特定场景下存在较高的锁竞争概率
- **事务处理**：默认REPEATABLE READ隔离级别，高并发环境下存在死锁和幻读风险

### 6.2 扩展性

#### PostgreSQL
- **扩展插件体系**：提供丰富的扩展插件支持，包括：
  - **PostGIS**：地理信息系统功能扩展
  - **pg_partman**：分区表管理扩展
  - **pg_stat_statements**：SQL查询统计扩展
  - **pgcrypto**：数据加密功能扩展
- **自定义功能**：支持用户自定义函数、数据类型、操作符定义
- **插件生态**：具备完善的第三方插件生态系统

#### MySQL
- **扩展能力**：主要通过存储引擎（InnoDB、MyISAM等）提供功能扩展
- **插件支持**：插件系统架构相对简单，扩展能力受限
- **存储引擎**：依赖不同存储引擎实现特定功能特性

### 6.3 SQL标准支持

#### PostgreSQL
- **SQL标准兼容性**：高度符合ANSI SQL标准，提供完整的高级功能支持：
  - **窗口函数**：完整实现SQL标准窗口函数规范
  - **CTE（公共表表达式）**：支持标准CTE及递归CTE语法
  - **数组操作**：原生数组数据类型及相关操作函数
  - **JSON/JSONB**：完整的JSON数据处理和查询能力
  - **正则表达式**：内置POSIX正则表达式引擎
  - **全文搜索**：原生全文检索功能支持

#### MySQL
- **SQL标准兼容性**：对ANSI SQL标准支持程度有限：
  - **窗口函数**：仅在MySQL 8.0及以上版本提供支持
  - **递归查询**：较新版本才引入递归CTE功能
  - **JSON支持**：JSON数据类型功能相对基础
  - **标准兼容性**：部分SQL标准功能存在缺失或实现不完整

### 6.4 复制与高可用

#### PostgreSQL
- **复制机制**：
  - **流复制（Streaming Replication）**：基于WAL日志的实时数据流复制
  - **逻辑复制（Logical Replication）**：基于发布-订阅模式的逻辑级复制
  - **同步复制**：提供同步及异步复制模式配置
- **高可用架构**：
  - **主从复制**：支持一主多从的标准复制架构
  - **故障转移**：提供自动故障检测和主从切换机制
  - **读写分离**：支持读写分离的负载均衡架构
- **备份恢复**：
  - **PITR（Point-in-Time Recovery）**：基于WAL日志的任意时间点恢复
  - **pg_basebackup**：在线物理备份工具

#### MySQL
- **复制功能**：
  - **主从复制**：基于binlog的传统主从复制机制
  - **主主复制**：支持双主互备复制模式
  - **GTID复制**：基于全局事务标识符的增强复制
- **高可用方案**：
  - **MySQL Cluster**：NDB存储引擎的分布式集群方案
  - **MHA**：Master High Availability主从切换方案
  - **Galera Cluster**：多主同步复制集群方案

### 6.5 核心优势总结

#### PostgreSQL优势

1. **JSONB数据类型**
   - 提供原生JSONB数据类型，支持灵活的半结构化数据存储
   - 查询性能接近传统关系型表结构，同时具备动态字段扩展能力
   - 提供类似文档数据库的数据处理能力

2. **分区表功能**
   - 内置分区表功能，提供简化的运维管理方式
   - 支持Range、List、Hash、Composite等多种分区策略
   - 要求在表设计阶段预先规划分区策略

3. **大表处理能力**
   - 单表支持数亿级数据量的高效处理
   - 在合理索引设计下，单表查询响应时间可达毫秒级
   - 提供优异的大数据量场景性能表现

4. **复杂查询能力**
   - 支持复杂SQL查询，适用于OLAP（在线分析处理）场景
   - 完整支持多表连接、子查询、窗口函数、递归查询等高级特性
   - 提供高效的数据聚合、分组、排序操作能力

### 6.6 使用场景选择

#### PostgreSQL适用场景
- **复杂查询场景**：复杂SQL查询和大规模数据分析处理需求
- **动态字段需求**：半结构化数据存储和动态字段扩展需求
- **地理信息系统**：需要GIS地理空间数据处理功能的应用
- **数据仓库**：OLAP场景下的复杂数据分析和报表需求
- **高并发读写**：高并发访问和复杂事务处理需求
- **SQL标准合规**：严格遵循SQL标准规范的项目需求

#### MySQL适用场景
- **OLTP场景**：轻量级、高并发的在线事务处理系统
- **Web应用**：传统Web应用系统（CMS、电商平台等）
- **成本控制**：对存储成本和部署成本敏感的项目
- **简单查询**：以基础CRUD操作为主的业务场景
- **快速部署**：需要快速部署和简化运维的项目需求

### 6.7 注意事项

#### PostgreSQL注意事项
- **存储开销**：索引文件占用空间较大，相比MySQL存储开销增加20%-30%
- **学习成本**：功能特性丰富，技术学习和掌握成本相对较高
- **内存需求**：高并发场景下内存消耗量相对较大

#### MySQL注意事项
- **功能限制**：部分高级SQL功能支持存在局限性
- **扩展能力**：系统扩展能力相对受限
- **并发性能**：特定高并发场景下性能表现逊于PostgreSQL

## 七、技术选型建议

### 7.1 数据库选择原则

**PostgreSQL适用条件**：
- 业务需求涉及复杂查询处理
- 需要处理大规模数据分析
- 存在动态字段或半结构化数据需求
- 要求严格的SQL标准合规性

**MySQL适用条件**：
- 轻量级OLTP业务场景
- 高并发简单事务处理需求
- 对部署和存储成本敏感
- 需要快速部署和简化运维

---

> 📚 **参考资料**：
> - [阿里云PostgreSQL开发规范](https://developer.aliyun.com/article/60899)
> - [PostgreSQL官方文档](https://www.postgresql.org/docs/current/)
> - [PostgreSQL数据类型文档](https://www.postgresql.org/docs/current/datatype.html)
> - [PostgreSQL唯一索引文档](https://www.postgresql.org/docs/current/indexes-unique.html)

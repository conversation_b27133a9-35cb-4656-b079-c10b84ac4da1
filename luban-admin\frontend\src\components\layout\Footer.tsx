const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="bg-white border-t border-gray-200 py-6 mt-auto">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <p className="text-sm text-gray-500">
              © {currentYear} 鲁班 | Technical Design Agent - 银河智学研发. 保留所有权利.
            </p>
          </div>
          <div className="flex space-x-6">
            <a href="#" className="text-sm text-gray-500 hover:text-blue-500">
              隐私政策
            </a>
            <a href="#" className="text-sm text-gray-500 hover:text-blue-500">
              使用条款
            </a>
            <a href="#" className="text-sm text-gray-500 hover:text-blue-500">
              关于我们
            </a>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 
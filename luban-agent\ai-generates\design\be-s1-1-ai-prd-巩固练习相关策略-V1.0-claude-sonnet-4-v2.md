# 产品需求文档 - 巩固练习相关策略 V1.0

## 1. 需求背景与目标

### 1.1 需求目标
- 通过巩固练习策略的实现，可以为不同能力水平的学生提供匹配的练习题，避免"题太难打击信心"或"题太简单浪费时间"，提升学习效率和满意度
- 该需求上线后，学生知识掌握度预计可以提升30%，练习完成率预计可以达到85%以上
- 让学生更有效地掌握课程重点知识点，通过对典型题的优先覆盖与逐步难度推进，提升知识掌握度和迁移能力
- 为后续算法推荐提供结构化数据积累，每一次基于策略推送的练习和作答，都是训练模型参数的有效样本

## 2. 功能范围

### 2.1 核心功能
- 题目推荐策略: 基于用户掌握度和题目难度系数，智能推荐最适合的练习题目，满足个性化学习场景，解决题目难度不匹配的痛点
  - 优先级：高
  - 依赖关系：依赖掌握度计算服务
- 预估题目数量计算: 根据知识点难度、考频系数和用户掌握度动态计算练习题量，满足精准练习量控制场景，解决练习量过多或过少的痛点
  - 优先级：高
  - 依赖关系：依赖题目推荐策略
- 再练一次功能: 针对掌握度不达标的用户提供二次练习机会，满足巩固提升场景，解决一次练习效果不佳的痛点
  - 优先级：中
  - 依赖关系：依赖题目推荐策略和掌握度评估
- 熔断策略: 当用户连续答错或专注度过低时自动结束练习，满足保护用户体验场景，解决无效练习浪费时间的痛点
  - 优先级：中
  - 依赖关系：依赖专注度评分系统

### 2.2 辅助功能
- 专注度评分系统: 基于作答时长和正确率评估用户专注度，为熔断策略提供支持
- 题目难度分级: 将题目按L1-L5等级分类，为推荐算法提供基础数据支持
- 必做题标识: 支持教师标记重点题目，为优先推荐提供支持

### 2.3 非本期功能
- 基于IRT模型的智能推荐: 需要积累足够答题数据后实现，建议迭代2再考虑
- 多维度学习路径规划: 涉及跨知识点关联分析，建议迭代3再考虑

## 3. 计算规则与公式

### 3.1 基础答题量计算
- 公式: 基础答题量 = 10 × 难度系数 × 考频系数（四舍五入取整，范围[8,14]）
  - 参数说明：
    - 难度系数: 知识点的难度等级对应数值
    - 考频系数: 知识点在考试中的出现频率权重
  - 规则：
    - 取值范围：[8, 14]

### 3.2 动态调整系数计算
- 公式: A = 1 + α × max(0, θ_tar - θ_cur) / θ_tar
  - 参数说明：
    - θ_tar: 该知识点对应的目标掌握度
    - θ_cur: 学生对该知识点的当前掌握度
    - α: 经验调节系数，默认取0.6
  - 规则：
    - 取值范围：A ≥ 1

### 3.3 预期掌握度增量计算
- 公式: P(答对) = sigmoid(4 × (θ - β))
  - 参数说明：
    - θ: 学生的掌握度
    - β: 题目难度系数
    - sigmoid函数: S(x) = 1/(1 + e^(-x))
- 公式: 预期掌握度增量 = P(答对) × 答对掌握度增量 + P(答错) × 答错掌握度增量
  - 规则：
    - 答对掌握度增量 = 0.2 × (β - θ)
    - 答错掌握度增量 = -0.1 × (β - θ) × 0.5

### 3.4 题目难度系数映射
- 规则：
  - L1难度等级对应难度系数0.2
  - L2难度等级对应难度系数0.4
  - L3难度等级对应难度系数0.6
  - L4难度等级对应难度系数0.8
  - L5难度等级对应难度系数1.0

## 4. 用户场景与故事

### 场景1：学生首次进入巩固练习
作为一个刚完成AI课学习的学生，我希望能够获得适合我当前能力水平的练习题，这样我就可以有效巩固刚学到的知识点。目前的痛点是不知道自己应该做什么难度的题目，如果能够根据我的掌握度智能推荐题目，对我的学习会有很大帮助。

**关键步骤：**
1. 系统根据学生当前掌握度和知识点难度计算预估题目数量
2. 系统按题组顺序进入，优先推荐必做题中预期掌握度增量最大的题目
3. 学生完成答题后，系统更新掌握度并推荐下一题

```mermaid
sequenceDiagram
    participant 学生 as 学生
    participant 巩固练习服务 as 巩固练习服务
    participant 掌握度服务 as 掌握度服务
    participant 题库服务 as 题库服务

    学生->>巩固练习服务: 进入巩固练习
    巩固练习服务->>掌握度服务: 获取当前掌握度
    掌握度服务-->>巩固练习服务: 返回掌握度数据
    巩固练习服务->>巩固练习服务: 计算预估题目数量
    巩固练习服务->>题库服务: 获取题组信息
    题库服务-->>巩固练习服务: 返回题组和题目信息
    巩固练习服务->>巩固练习服务: 选择预期掌握度增量最大的必做题
    巩固练习服务-->>学生: 推送题目
    学生->>巩固练习服务: 提交答案
    巩固练习服务->>掌握度服务: 更新掌握度
```

- 优先级：高
- 依赖关系：无

### 场景2：学生在练习过程中遇到连续错题
作为一个正在做巩固练习的学生，我希望当我连续答错题目时系统能够适当调整难度，这样我就可以避免因为题目太难而失去信心。目前的痛点是连续做错会让我感到挫败，如果能够智能调整难度或及时结束练习，对我的学习体验会有很大帮助。

**关键步骤：**
1. 系统监控学生答题情况，记录连续错误次数
2. 当连续错误达到阈值时，系统评估是否有更低难度题目
3. 如无更低难度题目且连续错误5题，触发熔断机制结束练习

```mermaid
flowchart TD
    A[学生答题] --> B{连续错误次数}
    B -->|< 5次| C[继续推荐题目]
    B -->|= 5次| D{题池中是否有更低难度题目}
    D -->|有| E[推荐更低难度题目]
    D -->|无| F[触发熔断，结束练习]
    C --> A
    E --> A
    F --> G[显示练习报告]
```

- 优先级：中
- 依赖关系：依赖于场景1

### 场景3：学生完成练习后需要再练一次
作为一个刚完成巩固练习但掌握度仍未达标的学生，我希望能够有机会再次练习错题，这样我就可以进一步提升对知识点的掌握。目前的痛点是一次练习后不知道还需要继续练习，如果能够智能判断并提供再练机会，对我的学习效果会有很大帮助。

**关键步骤：**
1. 系统评估学生练习后的掌握度和正确率
2. 当掌握度低于目标70%且正确率低于70%时，引导再练一次
3. 系统筛选有错题的题组，优先推荐错题中预期掌握度增量最大的题目

```mermaid
flowchart TD
    A[完成巩固练习] --> B{掌握度 < 目标70%}
    B -->|否| C[结束练习]
    B -->|是| D{正确率 < 70%}
    D -->|否| C
    D -->|是| E{可练习题目 ≥ 3题}
    E -->|否| C
    E -->|是| F[显示再练一次入口]
    F --> G[用户选择再练]
    G --> H[筛选有错题的题组]
    H --> I[推荐错题中预期掌握度增量最大的题目]
```

- 优先级：中
- 依赖关系：依赖于场景1和场景2

## 5. 业务流程图

```mermaid
flowchart TD
    Start[巩固练习开始] --> CalcQuestions[计算预估题目数量]
    CalcQuestions --> CheckGroups{是否有未作答题组}
    CheckGroups -->|否| End[结束练习]
    CheckGroups -->|是| EnterGroup[按顺序进入题组]
    
    EnterGroup --> IsFirstQuestion{是否为题组首题}
    IsFirstQuestion -->|是| HasMustDo{题组内是否有必做题}
    IsFirstQuestion -->|否| SelectBest[选择预期掌握度增量最大的题目]
    
    HasMustDo -->|是| SelectMustDo[在必做题中选择预期掌握度增量最大的题目]
    HasMustDo -->|否| SelectBest
    
    SelectMustDo --> CheckEstimated{是否达到预估答题量}
    SelectBest --> CheckEstimated
    
    CheckEstimated -->|是| CheckGroups
    CheckEstimated -->|否| CheckIncrement{剩余题目预期掌握度增量 ≥ 0.01}
    
    CheckIncrement -->|是| SelectBest
    CheckIncrement -->|否| CheckMinQuestions{本题组是否已作答2题}
    
    CheckMinQuestions -->|是| CheckGroups
    CheckMinQuestions -->|否| SelectBest
    
    %% 熔断策略
    SelectMustDo --> CheckBreaker{检查熔断条件}
    SelectBest --> CheckBreaker
    CheckBreaker -->|触发熔断| End
    CheckBreaker -->|继续| SubmitAnswer[学生提交答案]
    SubmitAnswer --> UpdateMastery[更新掌握度]
    UpdateMastery --> CheckEstimated
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：题目推荐的响应时间不超过2秒
- 并发用户：系统需支持1000个并发用户同时进行巩固练习，保证性能不衰减
- 数据量：系统需支持100万条题目记录的存储和查询，响应时间不超过1秒

### 6.2 安全需求
- 权限控制：题目推荐策略配置需要管理员权限才能访问
- 数据加密：学生答题记录需要使用AES256进行加密存储和传输
- 操作审计：对策略参数修改需要记录操作日志，包括操作人、操作时间、修改内容

## 7. 验收标准

### 7.1 功能验收
- 题目推荐策略：
  - 使用场景：当学生进入巩固练习且题组内有必做题时
  - 期望结果：系统应优先推荐必做题中预期掌握度增量最大的题目
- 熔断策略：
  - 使用场景：当学生连续答错5题且题池中没有更低难度题目时
  - 期望结果：系统应自动结束练习并显示相应提示

### 7.2 性能验收
- 响应时间：
  - 给定正常网络条件，当执行题目推荐，响应时间不超过2秒
  - 给定高并发场景，当1000用户同时请求，响应时间不超过3秒
- 并发用户：
  - 给定1000个并发用户，模拟巩固练习场景，系统运行30分钟，无异常，平均响应时间不超过2秒

### 7.3 安全验收
- 权限控制：
  - 给定未授权用户，当访问策略配置功能，返回权限错误
  - 给定授权管理员，当访问策略配置功能，可以正常使用
- 数据加密：
  - 给定学生答题数据，当存储到数据库，数据是加密状态，且使用AES256算法
- 操作审计：
  - 给定管理员修改策略参数，当执行修改操作，操作日志正常记录，包含操作人、时间、修改内容

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：巩固练习的操作界面简洁友好，3步内可完成主要操作
- 容错处理：网络异常发生时，系统可以本地缓存答题进度，并给出友好提示
- 兼容性：需支持Chrome/Safari/移动端浏览器等主流环境，保证界面和功能正常

### 8.2 维护性需求
- 配置管理：策略参数可由系统管理员在后台界面进行配置和调整
- 监控告警：系统异常发生时，系统自动告警，通知运维人员
- 日志管理：系统自动记录关键操作日志，日志保存30天，可供系统管理员查询和分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 用户可以通过练习报告页面和客服反馈提交反馈
- 每周定期收集和分析用户反馈，识别改进点

### 9.2 迭代计划
- 迭代周期：每2周
- 每次迭代结束后，评估需求实现情况和用户反馈，调整下一个迭代的优先级和计划

## 10. 需求检查清单

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
| ----------- | --------------- | ------ | ------ | ------ | -------- | -------- | ---- |
| 基础答题量计算公式 | 3.1 基础答题量计算 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 动态调整系数A计算 | 3.2 动态调整系数计算 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 预期掌握度增量计算 | 3.3 预期掌握度增量计算 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目难度系数映射 | 3.4 题目难度系数映射 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 巩固练习推题流程 | 2.1 题目推荐策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 必做题优先推荐 | 2.1 题目推荐策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 预估题目数量限制 | 2.1 预估题目数量计算 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 连续难度变化限制 | 2.1 题目推荐策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 大题数量控制 | 2.1 题目推荐策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 熔断策略-连续错题 | 2.1 熔断策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 熔断策略-专注度 | 2.1 熔断策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次判断条件 | 2.1 再练一次功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次题目筛选 | 2.1 再练一次功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次推题策略 | 2.1 再练一次功能 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 专注度评分规则 | 2.2 专注度评分系统 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 专注度扣分事件 | 2.2 专注度评分系统 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 专注度提醒机制 | 2.1 熔断策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |

## 11. 附录

### 11.1 术语表
- 掌握度：学生对特定知识点的理解和掌握程度，用0-1之间的数值表示
- 难度系数：题目难度的量化指标，对应L1-L5等级，取值0.2-1.0
- 预期掌握度增量：预测学生完成某道题目后掌握度的变化量
- 熔断策略：当检测到不良学习状态时自动终止练习的保护机制
- 必做题：教师标记的重点题目，在推荐时具有优先级

### 11.2 参考资料
- PRD - 掌握度 - V1.0
- 原始需求文档：Format-PRD-巩固练习相关策略-V1.0.md

--- 等待您的命令，指挥官
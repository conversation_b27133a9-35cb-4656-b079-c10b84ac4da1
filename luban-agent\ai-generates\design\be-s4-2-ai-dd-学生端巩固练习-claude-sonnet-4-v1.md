# 数据库设计文档 - 学生端巩固练习 - V1.0

**最后更新日期**: 2025-01-07
**设计负责人**: claude-sonnet-4
**评审人**: 待填写
**状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-01-07 | claude-sonnet-4 | 初始草稿     |

## 1. 项目概述与设计目标

### 1.1 项目背景与范围
本项目是智能化学生巩固练习平台的数据库设计，旨在支持AI驱动的个性化推题、实时掌握度计算、智能反馈和学习效果评估等核心功能。系统通过智能推题算法，根据学生的知识点掌握情况动态调整题目难度，实现从"知道"到"会用"的学习转化。本次数据库设计基于现有的掌握度策略系统和AI课程框架进行演进，支持练习会话管理、智能推题、实时反馈和学习报告生成等功能模块。

### 1.2 设计目标回顾
- 提供完整的数据库表创建语句（DDL）以支持巩固练习系统
- 在现有掌握度策略基础上，增加练习专用的数据结构
- 支持智能推题算法的数据需求和实时掌握度计算
- 设计高性能的练习会话状态管理和进度持久化
- 建立完整的表间关系，并提供清晰的ER图
- 详细说明核心业务场景的CRUD操作方式
- 制定合理的索引策略和数据生命周期管理
- 确保设计符合PostgreSQL和ClickHouse最佳实践

## 2. 数据库环境与总体说明

### 2.1 目标数据库类型
- PostgreSQL 17.x（用于核心业务数据）
- ClickHouse 23.8.16（用于行为日志和分析数据）

### 2.2 数据库创建语句 (如果适用)
使用现有数据库实例，目标数据库名称：
- PostgreSQL: `consolidation_exercise_db`
- ClickHouse: `analytics_db`

## 3. 数据库表详细设计

### 3.1 数据库表清单

#### 3.1.1 新增表清单

| 表名 | 用途 | 目标数据库类型 |
|------|------|--------------|
| `tbl_exercise_sessions` | 存储练习会话信息和状态管理 | PostgreSQL |
| `tbl_exercise_progress` | 存储练习进度和题目作答记录 | PostgreSQL |
| `tbl_exercise_recommendations` | 存储智能推题记录和策略配置 | PostgreSQL |
| `tbl_exercise_feedback` | 存储练习反馈和情感化响应 | PostgreSQL |
| `tbl_exercise_reports` | 存储学习报告数据 | PostgreSQL |
| `tbl_exercise_behavior_logs` | 存储练习行为日志（ClickHouse） | ClickHouse |

#### 3.1.2 修改现有表

| 表名 | 所属数据库 | 修改概要 | 修改理由 |
|------|----------|----------|----------|
| `tbl_mastery_learning_task` | PostgreSQL | 新增巩固练习相关字段 | 支持巩固练习任务类型和配置 |
| `tbl_mastery_answer_record` | PostgreSQL | 新增练习会话关联 | 关联巩固练习会话数据 |

#### 3.1.3 复用现有表

| 表名 | 所属数据库 | 支持新需求方式 |
|------|----------|--------------|
| `tbl_mastery_knowledge_point_mastery` | PostgreSQL | 直接复用掌握度计算和薄弱点识别 |
| `tbl_mastery_externalized_mastery` | PostgreSQL | 复用外化掌握度展示逻辑 |
| `tbl_mastery_calculation_parameter` | PostgreSQL | 复用掌握度计算参数配置 |
| `tbl_mastery_weak_point` | PostgreSQL | 复用薄弱知识点推荐逻辑 |

---

### 3.2 表名: `tbl_exercise_sessions`

#### 3.2.1 表功能说明
存储巩固练习会话的基本信息和状态管理，记录学生单次巩固练习的完整生命周期。该表支持练习的开始、暂停、继续、完成和退出等状态流转，为练习核心服务提供会话管理能力，是连接学生和巩固练习的核心纽带。

#### 3.2.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_exercise_sessions (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id BIGINT NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  session_status VARCHAR(20) NOT NULL DEFAULT 'active',
  start_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  end_time TIMESTAMPTZ NULL,
  pause_time TIMESTAMPTZ NULL,
  total_duration INTEGER NOT NULL DEFAULT 0,
  pause_duration INTEGER NOT NULL DEFAULT 0,
  current_question_index INTEGER NOT NULL DEFAULT 0,
  total_questions INTEGER NOT NULL DEFAULT 0,
  completed_questions INTEGER NOT NULL DEFAULT 0,
  correct_answers INTEGER NOT NULL DEFAULT 0,
  session_config JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_exercise_sessions IS '巩固练习会话表 - 存储练习会话状态和生命周期管理';
COMMENT ON COLUMN tbl_exercise_sessions.id IS '会话主键ID';
COMMENT ON COLUMN tbl_exercise_sessions.user_id IS '学习用户ID，关联用户中心服务';
COMMENT ON COLUMN tbl_exercise_sessions.course_id IS '课程ID，关联内容平台服务';
COMMENT ON COLUMN tbl_exercise_sessions.session_status IS '会话状态 (active:进行中, paused:已暂停, completed:已完成, exited:已退出)';
COMMENT ON COLUMN tbl_exercise_sessions.start_time IS '练习开始时间';
COMMENT ON COLUMN tbl_exercise_sessions.end_time IS '练习结束时间';
COMMENT ON COLUMN tbl_exercise_sessions.pause_time IS '最后暂停时间';
COMMENT ON COLUMN tbl_exercise_sessions.total_duration IS '总练习时长（秒，不含暂停时间）';
COMMENT ON COLUMN tbl_exercise_sessions.pause_duration IS '累计暂停时长（秒）';
COMMENT ON COLUMN tbl_exercise_sessions.current_question_index IS '当前题目索引位置';
COMMENT ON COLUMN tbl_exercise_sessions.total_questions IS '本次练习总题目数';
COMMENT ON COLUMN tbl_exercise_sessions.completed_questions IS '已完成题目数';
COMMENT ON COLUMN tbl_exercise_sessions.correct_answers IS '正确答题数';
COMMENT ON COLUMN tbl_exercise_sessions.session_config IS '会话配置信息，JSON格式';
COMMENT ON COLUMN tbl_exercise_sessions.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_exercise_sessions.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_exercise_sessions.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_exercise_sessions.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_exercise_sessions.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_exercise_sessions.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_exercise_sessions_user_id ON tbl_exercise_sessions(user_id) WHERE deleted_at IS NULL;
-- 支持按用户ID查询练习会话
CREATE INDEX idx_exercise_sessions_course_id ON tbl_exercise_sessions(course_id) WHERE deleted_at IS NULL;
-- 支持按课程ID查询练习会话
CREATE INDEX idx_exercise_sessions_user_course ON tbl_exercise_sessions(user_id, course_id) WHERE deleted_at IS NULL;
-- 支持按用户和课程组合查询
CREATE INDEX idx_exercise_sessions_status ON tbl_exercise_sessions(session_status) WHERE deleted_at IS NULL;
-- 支持按会话状态查询
CREATE INDEX idx_exercise_sessions_start_time ON tbl_exercise_sessions(start_time) WHERE deleted_at IS NULL;
-- 支持按开始时间排序查询
```

---

### 3.3 表名: `tbl_exercise_progress`

#### 3.3.1 表功能说明
存储巩固练习的详细进度和题目作答记录，记录每道题目的作答情况、耗时、正确性等信息。该表支持练习进度的精确跟踪和恢复，为智能推题算法提供学习行为数据，是掌握度计算和学习分析的重要数据来源。

#### 3.3.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_exercise_progress (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  session_id BIGINT NOT NULL,
  question_id VARCHAR(64) NOT NULL,
  question_order INTEGER NOT NULL,
  knowledge_point_ids VARCHAR(500) NOT NULL,
  user_answer TEXT NULL,
  correct_answer TEXT NOT NULL,
  is_correct BOOLEAN NOT NULL,
  answer_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  answer_duration INTEGER NOT NULL DEFAULT 0,
  difficulty_level SMALLINT NOT NULL,
  question_source VARCHAR(50) NOT NULL DEFAULT 'recommendation',
  retry_count INTEGER NOT NULL DEFAULT 0,
  hint_used BOOLEAN NOT NULL DEFAULT FALSE,
  mastery_before NUMERIC(4, 3) NULL,
  mastery_after NUMERIC(4, 3) NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_exercise_progress IS '练习进度表 - 存储详细的题目作答记录和进度信息';
COMMENT ON COLUMN tbl_exercise_progress.id IS '进度记录主键ID';
COMMENT ON COLUMN tbl_exercise_progress.session_id IS '练习会话ID，关联tbl_exercise_sessions.id';
COMMENT ON COLUMN tbl_exercise_progress.question_id IS '题目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_exercise_progress.question_order IS '题目在练习中的顺序号';
COMMENT ON COLUMN tbl_exercise_progress.knowledge_point_ids IS '关联的知识点ID列表，逗号分隔';
COMMENT ON COLUMN tbl_exercise_progress.user_answer IS '学生提交的答案内容';
COMMENT ON COLUMN tbl_exercise_progress.correct_answer IS '题目的正确答案';
COMMENT ON COLUMN tbl_exercise_progress.is_correct IS '答题是否正确';
COMMENT ON COLUMN tbl_exercise_progress.answer_time IS '提交答案的时间';
COMMENT ON COLUMN tbl_exercise_progress.answer_duration IS '作答用时（秒）';
COMMENT ON COLUMN tbl_exercise_progress.difficulty_level IS '题目难度等级 (1:简单, 2:中等, 3:困难)';
COMMENT ON COLUMN tbl_exercise_progress.question_source IS '题目来源 (recommendation:智能推荐, retry:错题重练, hint:提示后重试)';
COMMENT ON COLUMN tbl_exercise_progress.retry_count IS '重试次数';
COMMENT ON COLUMN tbl_exercise_progress.hint_used IS '是否使用了提示';
COMMENT ON COLUMN tbl_exercise_progress.mastery_before IS '作答前知识点掌握度';
COMMENT ON COLUMN tbl_exercise_progress.mastery_after IS '作答后知识点掌握度';
COMMENT ON COLUMN tbl_exercise_progress.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_exercise_progress.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_exercise_progress.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_exercise_progress.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_exercise_progress.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_exercise_progress.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_exercise_progress_session_id ON tbl_exercise_progress(session_id) WHERE deleted_at IS NULL;
-- 支持按会话ID查询进度记录
CREATE INDEX idx_exercise_progress_question_id ON tbl_exercise_progress(question_id) WHERE deleted_at IS NULL;
-- 支持按题目ID查询作答记录
CREATE INDEX idx_exercise_progress_answer_time ON tbl_exercise_progress(answer_time) WHERE deleted_at IS NULL;
-- 支持按答题时间排序查询
CREATE INDEX idx_exercise_progress_is_correct ON tbl_exercise_progress(is_correct) WHERE deleted_at IS NULL;
-- 支持按正确性统计查询
CREATE UNIQUE INDEX uk_exercise_progress_session_question ON tbl_exercise_progress(session_id, question_id, retry_count) WHERE deleted_at IS NULL;
-- 确保同一会话中同一题目的同一次重试唯一
```

--- 

### 3.4 表名: `tbl_exercise_recommendations`

#### 3.4.1 表功能说明
存储智能推题记录和推荐策略配置，记录每次推题的算法决策过程和推荐结果。该表为推题服务提供历史决策追溯能力，支持推题策略的优化和调试，是智能推题算法持续改进的重要数据基础。

#### 3.4.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_exercise_recommendations (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  session_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  question_id VARCHAR(64) NOT NULL,
  recommendation_order INTEGER NOT NULL,
  algorithm_version VARCHAR(50) NOT NULL,
  difficulty_target SMALLINT NOT NULL,
  mastery_input JSONB NOT NULL,
  weak_points_input JSONB NULL,
  strategy_config JSONB NOT NULL,
  recommendation_reason VARCHAR(200) NOT NULL,
  confidence_score NUMERIC(3, 2) NOT NULL,
  recommendation_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  actual_difficulty SMALLINT NULL,
  performance_feedback JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_exercise_recommendations IS '智能推题记录表 - 存储推题算法的决策过程和推荐结果';
COMMENT ON COLUMN tbl_exercise_recommendations.id IS '推荐记录主键ID';
COMMENT ON COLUMN tbl_exercise_recommendations.session_id IS '练习会话ID，关联tbl_exercise_sessions.id';
COMMENT ON COLUMN tbl_exercise_recommendations.user_id IS '用户ID，关联用户中心服务';
COMMENT ON COLUMN tbl_exercise_recommendations.course_id IS '课程ID，关联内容平台服务';
COMMENT ON COLUMN tbl_exercise_recommendations.question_id IS '推荐的题目ID';
COMMENT ON COLUMN tbl_exercise_recommendations.recommendation_order IS '推荐题目在会话中的序号';
COMMENT ON COLUMN tbl_exercise_recommendations.algorithm_version IS '推题算法版本标识';
COMMENT ON COLUMN tbl_exercise_recommendations.difficulty_target IS '目标难度等级';
COMMENT ON COLUMN tbl_exercise_recommendations.mastery_input IS '输入的掌握度数据，JSON格式';
COMMENT ON COLUMN tbl_exercise_recommendations.weak_points_input IS '输入的薄弱知识点数据，JSON格式';
COMMENT ON COLUMN tbl_exercise_recommendations.strategy_config IS '推题策略配置，JSON格式';
COMMENT ON COLUMN tbl_exercise_recommendations.recommendation_reason IS '推荐理由描述';
COMMENT ON COLUMN tbl_exercise_recommendations.confidence_score IS '推荐置信度分数 (0.00-1.00)';
COMMENT ON COLUMN tbl_exercise_recommendations.recommendation_time IS '推荐生成时间';
COMMENT ON COLUMN tbl_exercise_recommendations.actual_difficulty IS '题目实际难度（用户反馈后更新）';
COMMENT ON COLUMN tbl_exercise_recommendations.performance_feedback IS '推荐效果反馈，JSON格式';
COMMENT ON COLUMN tbl_exercise_recommendations.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_exercise_recommendations.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_exercise_recommendations.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_exercise_recommendations.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_exercise_recommendations.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_exercise_recommendations.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_exercise_recommendations_session_id ON tbl_exercise_recommendations(session_id) WHERE deleted_at IS NULL;
-- 支持按会话ID查询推荐记录
CREATE INDEX idx_exercise_recommendations_user_id ON tbl_exercise_recommendations(user_id) WHERE deleted_at IS NULL;
-- 支持按用户ID查询推荐历史
CREATE INDEX idx_exercise_recommendations_question_id ON tbl_exercise_recommendations(question_id) WHERE deleted_at IS NULL;
-- 支持按题目ID查询推荐情况
CREATE INDEX idx_exercise_recommendations_algorithm_version ON tbl_exercise_recommendations(algorithm_version) WHERE deleted_at IS NULL;
-- 支持按算法版本分析推荐效果
CREATE INDEX idx_exercise_recommendations_time ON tbl_exercise_recommendations(recommendation_time) WHERE deleted_at IS NULL;
-- 支持按推荐时间排序查询
```

---

### 3.5 表名: `tbl_exercise_feedback`

#### 3.5.1 表功能说明
存储练习过程中的反馈和情感化响应信息，记录根据学生作答结果生成的差异化反馈内容。该表支持个性化反馈策略的实施，为反馈服务提供内容管理能力，是提升学习体验和激励效果的重要数据支撑。

#### 3.5.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_exercise_feedback (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  session_id BIGINT NOT NULL,
  progress_id BIGINT NOT NULL,
  feedback_type VARCHAR(50) NOT NULL,
  feedback_trigger VARCHAR(50) NOT NULL,
  feedback_content JSONB NOT NULL,
  emotion_type VARCHAR(30) NOT NULL,
  encouragement_level SMALLINT NOT NULL,
  display_duration INTEGER NOT NULL DEFAULT 3,
  feedback_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  user_interaction JSONB NULL,
  effectiveness_score NUMERIC(3, 2) NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_exercise_feedback IS '练习反馈表 - 存储情感化反馈和响应内容';
COMMENT ON COLUMN tbl_exercise_feedback.id IS '反馈记录主键ID';
COMMENT ON COLUMN tbl_exercise_feedback.session_id IS '练习会话ID，关联tbl_exercise_sessions.id';
COMMENT ON COLUMN tbl_exercise_feedback.progress_id IS '进度记录ID，关联tbl_exercise_progress.id';
COMMENT ON COLUMN tbl_exercise_feedback.feedback_type IS '反馈类型 (correct:答对反馈, incorrect:答错反馈, encouragement:鼓励反馈, milestone:里程碑反馈)';
COMMENT ON COLUMN tbl_exercise_feedback.feedback_trigger IS '反馈触发条件 (answer_submit:提交答案, continuous_correct:连续答对, improvement:进步表现)';
COMMENT ON COLUMN tbl_exercise_feedback.feedback_content IS '反馈内容，JSON格式（文字、图片、动画等）';
COMMENT ON COLUMN tbl_exercise_feedback.emotion_type IS '情感类型 (celebration:庆祝, encouragement:鼓励, comfort:安慰, motivation:激励)';
COMMENT ON COLUMN tbl_exercise_feedback.encouragement_level IS '鼓励程度 (1:轻度, 2:中度, 3:高度)';
COMMENT ON COLUMN tbl_exercise_feedback.display_duration IS '建议展示时长（秒）';
COMMENT ON COLUMN tbl_exercise_feedback.feedback_time IS '反馈生成时间';
COMMENT ON COLUMN tbl_exercise_feedback.user_interaction IS '用户交互数据，JSON格式';
COMMENT ON COLUMN tbl_exercise_feedback.effectiveness_score IS '反馈有效性评分 (0.00-1.00)';
COMMENT ON COLUMN tbl_exercise_feedback.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_exercise_feedback.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_exercise_feedback.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_exercise_feedback.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_exercise_feedback.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_exercise_feedback.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_exercise_feedback_session_id ON tbl_exercise_feedback(session_id) WHERE deleted_at IS NULL;
-- 支持按会话ID查询反馈记录
CREATE INDEX idx_exercise_feedback_progress_id ON tbl_exercise_feedback(progress_id) WHERE deleted_at IS NULL;
-- 支持按进度ID查询反馈
CREATE INDEX idx_exercise_feedback_type ON tbl_exercise_feedback(feedback_type) WHERE deleted_at IS NULL;
-- 支持按反馈类型统计分析
CREATE INDEX idx_exercise_feedback_time ON tbl_exercise_feedback(feedback_time) WHERE deleted_at IS NULL;
-- 支持按反馈时间排序查询
CREATE UNIQUE INDEX uk_exercise_feedback_progress ON tbl_exercise_feedback(progress_id, feedback_type) WHERE deleted_at IS NULL;
-- 确保同一进度记录的同一反馈类型唯一
```

---

### 3.6 表名: `tbl_exercise_reports`

#### 3.6.1 表功能说明
存储练习完成后生成的学习报告数据，汇总练习过程中的关键指标和学习成果。该表为学习报告服务提供数据存储能力，支持个性化学习报告的生成和历史回顾，是学习效果评估和持续改进的重要数据支撑。

#### 3.6.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_exercise_reports (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  session_id BIGINT NOT NULL,
  user_id BIGINT NOT NULL,
  course_id VARCHAR(64) NOT NULL,
  report_type VARCHAR(30) NOT NULL DEFAULT 'session_summary',
  report_status VARCHAR(20) NOT NULL DEFAULT 'generated',
  total_questions INTEGER NOT NULL,
  correct_answers INTEGER NOT NULL,
  accuracy_rate NUMERIC(5, 2) NOT NULL,
  total_duration INTEGER NOT NULL,
  avg_answer_time NUMERIC(6, 2) NOT NULL,
  mastery_changes JSONB NOT NULL,
  weak_points_improved JSONB NULL,
  performance_trends JSONB NULL,
  recommendations JSONB NULL,
  achievement_badges JSONB NULL,
  report_content JSONB NOT NULL,
  generated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  viewed_at TIMESTAMPTZ NULL,
  shared_at TIMESTAMPTZ NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_exercise_reports IS '练习报告表 - 存储学习报告数据和成果汇总';
COMMENT ON COLUMN tbl_exercise_reports.id IS '报告主键ID';
COMMENT ON COLUMN tbl_exercise_reports.session_id IS '练习会话ID，关联tbl_exercise_sessions.id';
COMMENT ON COLUMN tbl_exercise_reports.user_id IS '用户ID，关联用户中心服务';
COMMENT ON COLUMN tbl_exercise_reports.course_id IS '课程ID，关联内容平台服务';
COMMENT ON COLUMN tbl_exercise_reports.report_type IS '报告类型 (session_summary:会话总结, weekly_summary:周总结, monthly_summary:月总结)';
COMMENT ON COLUMN tbl_exercise_reports.report_status IS '报告状态 (generated:已生成, viewed:已查看, shared:已分享)';
COMMENT ON COLUMN tbl_exercise_reports.total_questions IS '总题目数';
COMMENT ON COLUMN tbl_exercise_reports.correct_answers IS '正确答题数';
COMMENT ON COLUMN tbl_exercise_reports.accuracy_rate IS '正确率（百分比）';
COMMENT ON COLUMN tbl_exercise_reports.total_duration IS '总用时（秒）';
COMMENT ON COLUMN tbl_exercise_reports.avg_answer_time IS '平均答题时间（秒）';
COMMENT ON COLUMN tbl_exercise_reports.mastery_changes IS '掌握度变化数据，JSON格式';
COMMENT ON COLUMN tbl_exercise_reports.weak_points_improved IS '改善的薄弱知识点，JSON格式';
COMMENT ON COLUMN tbl_exercise_reports.performance_trends IS '表现趋势数据，JSON格式';
COMMENT ON COLUMN tbl_exercise_reports.recommendations IS '学习建议，JSON格式';
COMMENT ON COLUMN tbl_exercise_reports.achievement_badges IS '获得的成就徽章，JSON格式';
COMMENT ON COLUMN tbl_exercise_reports.report_content IS '完整报告内容，JSON格式';
COMMENT ON COLUMN tbl_exercise_reports.generated_at IS '报告生成时间';
COMMENT ON COLUMN tbl_exercise_reports.viewed_at IS '首次查看时间';
COMMENT ON COLUMN tbl_exercise_reports.shared_at IS '分享时间';
COMMENT ON COLUMN tbl_exercise_reports.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_exercise_reports.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_exercise_reports.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_exercise_reports.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_exercise_reports.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_exercise_reports.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_exercise_reports_session_id ON tbl_exercise_reports(session_id) WHERE deleted_at IS NULL;
-- 支持按会话ID查询报告
CREATE INDEX idx_exercise_reports_user_id ON tbl_exercise_reports(user_id) WHERE deleted_at IS NULL;
-- 支持按用户ID查询历史报告
CREATE INDEX idx_exercise_reports_course_id ON tbl_exercise_reports(course_id) WHERE deleted_at IS NULL;
-- 支持按课程ID查询报告
CREATE INDEX idx_exercise_reports_type ON tbl_exercise_reports(report_type) WHERE deleted_at IS NULL;
-- 支持按报告类型查询
CREATE INDEX idx_exercise_reports_generated_at ON tbl_exercise_reports(generated_at) WHERE deleted_at IS NULL;
-- 支持按生成时间排序查询
CREATE UNIQUE INDEX uk_exercise_reports_session ON tbl_exercise_reports(session_id) WHERE deleted_at IS NULL;
-- 确保每个会话只有一个报告
```

---

### 3.7 表名: `tbl_exercise_behavior_logs` (ClickHouse)

#### 3.7.1 表功能说明
存储练习过程中的详细行为日志，记录学生在练习过程中的所有操作行为和交互事件。该表采用ClickHouse存储，支持高频写入的行为数据记录，为学习行为分析、用户体验优化和系统性能监控提供详细的操作数据。

#### 3.7.2 表结构定义 (DDL)
```sql
CREATE TABLE default.tbl_exercise_behavior_logs
(
    -- 时间字段
    log_date Date MATERIALIZED toDate(event_time) COMMENT '行为日期（用于分区）',
    event_time DateTime64(3) COMMENT '行为发生时间（毫秒精度）',
    
    -- 业务字段
    session_id UInt64 COMMENT '练习会话ID',
    user_id UInt64 COMMENT '用户ID',
    course_id String COMMENT '课程ID',
    question_id Nullable(String) COMMENT '题目ID（如果与特定题目相关）',
    event_type LowCardinality(String) COMMENT '事件类型 (session_start, question_view, answer_submit, hint_request, pause, resume等)',
    event_category LowCardinality(String) COMMENT '事件分类 (navigation, interaction, submission, system)',
    event_details String COMMENT '事件详细内容描述',
    session_state String COMMENT '会话状态快照',
    user_context Map(String, String) COMMENT '用户上下文信息',
    
    -- 技术字段
    client_ip IPv6 COMMENT '客户端IP地址',
    user_agent String COMMENT '用户代理信息',
    device_info Map(String, String) COMMENT '设备信息',
    request_id String COMMENT '请求ID（用于追踪）'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date) -- 按月分区
ORDER BY (log_date, user_id, event_time, event_type) -- 排序键：日期、用户ID、事件时间、事件类型
TTL log_date + INTERVAL 365 DAY DELETE -- 数据保留1年
SETTINGS index_granularity = 8192;

-- ClickHouse 特有说明
-- 排序键 (ORDER BY): (log_date, user_id, event_time, event_type)
-- 设计原因: 支持按日期范围查询用户行为，按用户聚合分析行为模式，按时间序列分析操作轨迹
-- 分区键 (PARTITION BY): toYYYYMM(log_date)
-- 设计原因: 按月分区便于数据管理和查询优化，支持按时间范围的分区裁剪

-- 跳数索引
ALTER TABLE default.tbl_exercise_behavior_logs ADD INDEX idx_event_type event_type TYPE set(0) GRANULARITY 1;
-- 支持快速筛选特定事件类型
ALTER TABLE default.tbl_exercise_behavior_logs ADD INDEX idx_session_id session_id TYPE bloom_filter GRANULARITY 1;
-- 支持快速定位特定会话的行为记录
ALTER TABLE default.tbl_exercise_behavior_logs ADD INDEX idx_course_question course_id, question_id TYPE minmax GRANULARITY 1;
-- 支持按课程和题目范围查询行为记录
```

--- 

## 4. 现有表结构修改

### 4.1 表名: `tbl_mastery_learning_task` (修改)

#### 4.1.1 变更前核心结构说明
现有结构包含学习任务的基本信息，支持AI课、巩固练习、作业、测验等任务类型，通过`task_type`字段区分任务类型，其中巩固练习对应`task_type = 20`。

#### 4.1.2 具体变更内容 (ALTER TABLE语句)
```sql
-- 新增巩固练习专用字段
ALTER TABLE tbl_mastery_learning_task 
ADD COLUMN exercise_config JSONB NULL COMMENT '巩固练习配置信息，JSON格式',
ADD COLUMN difficulty_strategy VARCHAR(50) NULL DEFAULT 'adaptive' COMMENT '难度调整策略 (adaptive:自适应, fixed:固定, progressive:渐进)',
ADD COLUMN retry_policy JSONB NULL COMMENT '重试策略配置，JSON格式',
ADD COLUMN time_limit INTEGER NULL COMMENT '练习时间限制（分钟），NULL表示无限制';

-- 添加字段注释
COMMENT ON COLUMN tbl_mastery_learning_task.exercise_config IS '巩固练习配置信息，包含推题策略、反馈规则等，JSON格式';
COMMENT ON COLUMN tbl_mastery_learning_task.difficulty_strategy IS '难度调整策略 (adaptive:自适应, fixed:固定, progressive:渐进)';
COMMENT ON COLUMN tbl_mastery_learning_task.retry_policy IS '重试策略配置，包含最大重试次数、重试条件等，JSON格式';
COMMENT ON COLUMN tbl_mastery_learning_task.time_limit IS '练习时间限制（分钟），NULL表示无限制';

-- 新增索引
CREATE INDEX idx_mastery_learning_task_difficulty_strategy ON tbl_mastery_learning_task(difficulty_strategy) WHERE deleted_at IS NULL;
-- 支持按难度策略查询任务
```

#### 4.1.3 变更理由和业务需求支撑
- **exercise_config**: 巩固练习需要独特的配置参数，如推题算法参数、反馈规则、熔断机制等
- **difficulty_strategy**: 支持不同的难度调整策略，满足个性化学习需求
- **retry_policy**: 巩固练习需要错题重练机制，需要配置重试策略
- **time_limit**: 部分练习场景需要时间限制，增强练习的挑战性

#### 4.1.4 结构化影响评估

**数据影响**：
- 对现有存量数据无影响，新增字段均允许NULL值
- 巩固练习类型任务可通过更新语句设置相关配置

**查询性能影响**：
- 新增字段对现有查询无性能影响
- 新增索引将提升按难度策略筛选的查询性能
- JSON字段查询需要注意使用合适的操作符

**应用兼容性影响**：
- 现有应用代码无需修改，新字段为可选字段
- 巩固练习相关的新功能需要读取和更新新增字段
- 需要更新相关的数据传输对象(DTO)定义

**回滚预案考量**：
如果变更出现问题，可以通过以下方式回滚：
- 删除新增字段: `ALTER TABLE tbl_mastery_learning_task DROP COLUMN exercise_config, DROP COLUMN difficulty_strategy, DROP COLUMN retry_policy, DROP COLUMN time_limit`
- 删除新增索引: `DROP INDEX idx_mastery_learning_task_difficulty_strategy`

#### 4.1.5 兼容性保障说明
- 所有新增字段均设置为可NULL，确保现有数据完整性
- 新增索引仅对新字段生效，不影响现有查询路径
- 保持现有字段的数据类型和约束不变，确保向后兼容性

---

### 4.2 表名: `tbl_mastery_answer_record` (修改)

#### 4.2.1 变更前核心结构说明
现有结构存储学生的答题记录，包含用户ID、题目ID、答题结果、答题时间等基本信息，主要服务于掌握度计算，缺少与练习会话的直接关联。

#### 4.2.2 具体变更内容 (ALTER TABLE语句)
```sql
-- 新增练习会话关联字段
ALTER TABLE tbl_mastery_answer_record 
ADD COLUMN exercise_session_id BIGINT NULL COMMENT '巩固练习会话ID，关联tbl_exercise_sessions.id',
ADD COLUMN exercise_progress_id BIGINT NULL COMMENT '练习进度记录ID，关联tbl_exercise_progress.id',
ADD COLUMN answer_method VARCHAR(30) NULL DEFAULT 'direct' COMMENT '作答方式 (direct:直接作答, hint:使用提示, retry:重试作答)';

-- 添加字段注释
COMMENT ON COLUMN tbl_mastery_answer_record.exercise_session_id IS '巩固练习会话ID，关联tbl_exercise_sessions.id，NULL表示非巩固练习答题';
COMMENT ON COLUMN tbl_mastery_answer_record.exercise_progress_id IS '练习进度记录ID，关联tbl_exercise_progress.id，用于关联详细进度信息';
COMMENT ON COLUMN tbl_mastery_answer_record.answer_method IS '作答方式 (direct:直接作答, hint:使用提示, retry:重试作答)';

-- 新增索引
CREATE INDEX idx_mastery_answer_record_exercise_session ON tbl_mastery_answer_record(exercise_session_id) WHERE deleted_at IS NULL AND exercise_session_id IS NOT NULL;
-- 支持按练习会话查询答题记录
CREATE INDEX idx_mastery_answer_record_exercise_progress ON tbl_mastery_answer_record(exercise_progress_id) WHERE deleted_at IS NULL AND exercise_progress_id IS NOT NULL;
-- 支持按练习进度查询答题记录
```

#### 4.2.3 变更理由和业务需求支撑
- **exercise_session_id**: 建立答题记录与巩固练习会话的关联，支持按会话统计和分析
- **exercise_progress_id**: 建立与详细进度记录的关联，支持完整的学习轨迹追踪
- **answer_method**: 区分不同的作答方式，为推题算法和掌握度计算提供更精确的数据

#### 4.2.4 结构化影响评估

**数据影响**：
- 对现有存量数据无影响，新增字段为可NULL字段
- 巩固练习产生的新答题记录将填充相应的关联字段

**查询性能影响**：
- 新增字段对现有查询无性能影响
- 新增索引将提升巩固练习相关的答题记录查询性能
- 条件索引减少了存储空间占用

**应用兼容性影响**：
- 现有掌握度计算逻辑无需修改
- 巩固练习模块需要在记录答题时填充新增字段
- 学习报告生成需要利用新的关联关系

**回滚预案考量**：
如果变更出现问题，可以删除新增字段和索引，现有功能不受影响。

#### 4.2.5 兼容性保障说明
- 新增字段均为可NULL，保证现有数据和查询的完整性
- 新增索引使用条件过滤，仅对相关数据生效
- 保持现有业务逻辑的独立性，巩固练习作为增量功能

---

## 5. 表间关系与 ER 图

### 5.1 表间关系描述

| 主表           | 关系类型                       | 从表               | 外键字段 (从表)                           | 关联描述                                               |
| -------------- | ------------------------------ | ------------------ | ----------------------------------------- | ------------------------------------------------------ |
| `tbl_exercise_sessions` | 一对多 (1:N) | `tbl_exercise_progress` | `session_id` | 一个练习会话包含多条进度记录 |
| `tbl_exercise_sessions` | 一对多 (1:N) | `tbl_exercise_recommendations` | `session_id` | 一个练习会话包含多次推题记录 |
| `tbl_exercise_sessions` | 一对多 (1:N) | `tbl_exercise_feedback` | `session_id` | 一个练习会话包含多条反馈记录 |
| `tbl_exercise_sessions` | 一对一 (1:1) | `tbl_exercise_reports` | `session_id` | 一个练习会话对应一个学习报告 |
| `tbl_exercise_progress` | 一对多 (1:N) | `tbl_exercise_feedback` | `progress_id` | 一条进度记录可能对应多种反馈 |
| `tbl_exercise_progress` | 一对一 (1:1) | `tbl_mastery_answer_record` | `exercise_progress_id` | 一条进度记录对应一条掌握度答题记录 |
| `tbl_mastery_learning_task` | 一对多 (1:N) | `tbl_exercise_sessions` | 通过course_id关联 | 学习任务与练习会话通过课程关联 |
| `tbl_mastery_knowledge_point_mastery` | 查询关联 | `tbl_exercise_recommendations` | 通过user_id查询 | 推题时查询用户掌握度数据 |
| `tbl_mastery_weak_point` | 查询关联 | `tbl_exercise_recommendations` | 通过user_id查询 | 推题时查询薄弱知识点数据 |
| `tbl_exercise_sessions` | 一对多 (1:N) | `tbl_exercise_behavior_logs` | `session_id` | 一个练习会话产生多条行为日志 |

### 5.2 ER 图 (使用 Mermaid 语法)
注意：本ER图中的外键关系（FK）表示的是表之间的逻辑关联和参照完整性期望，实际建表语句中不使用数据库层面的物理外键约束，数据一致性由应用层保障。

```mermaid
erDiagram
    %% 新增练习相关表
    EXERCISE_SESSIONS {
        BIGSERIAL id PK
        BIGINT user_id FK
        VARCHAR course_id FK
        VARCHAR session_status
        TIMESTAMPTZ start_time
        TIMESTAMPTZ end_time
        INTEGER total_duration
        INTEGER current_question_index
        INTEGER total_questions
        INTEGER completed_questions
        INTEGER correct_answers
        JSONB session_config
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    EXERCISE_PROGRESS {
        BIGSERIAL id PK
        BIGINT session_id FK
        VARCHAR question_id FK
        INTEGER question_order
        VARCHAR knowledge_point_ids
        TEXT user_answer
        TEXT correct_answer
        BOOLEAN is_correct
        TIMESTAMPTZ answer_time
        INTEGER answer_duration
        SMALLINT difficulty_level
        INTEGER retry_count
        NUMERIC mastery_before
        NUMERIC mastery_after
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    EXERCISE_RECOMMENDATIONS {
        BIGSERIAL id PK
        BIGINT session_id FK
        BIGINT user_id FK
        VARCHAR course_id FK
        VARCHAR question_id FK
        INTEGER recommendation_order
        VARCHAR algorithm_version
        JSONB mastery_input
        JSONB strategy_config
        VARCHAR recommendation_reason
        NUMERIC confidence_score
        TIMESTAMPTZ recommendation_time
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    EXERCISE_FEEDBACK {
        BIGSERIAL id PK
        BIGINT session_id FK
        BIGINT progress_id FK
        VARCHAR feedback_type
        VARCHAR feedback_trigger
        JSONB feedback_content
        VARCHAR emotion_type
        SMALLINT encouragement_level
        TIMESTAMPTZ feedback_time
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    EXERCISE_REPORTS {
        BIGSERIAL id PK
        BIGINT session_id FK
        BIGINT user_id FK
        VARCHAR course_id FK
        VARCHAR report_type
        INTEGER total_questions
        INTEGER correct_answers
        NUMERIC accuracy_rate
        INTEGER total_duration
        JSONB mastery_changes
        JSONB report_content
        TIMESTAMPTZ generated_at
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    %% 现有掌握度相关表（复用）
    MASTERY_LEARNING_TASK {
        BIGSERIAL id PK
        VARCHAR task_name
        SMALLINT task_type
        VARCHAR course_id
        SMALLINT completion_threshold
        JSONB exercise_config
        VARCHAR difficulty_strategy
        JSONB retry_policy
        INTEGER time_limit
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    MASTERY_ANSWER_RECORD {
        BIGSERIAL id PK
        VARCHAR user_id FK
        VARCHAR question_id FK
        VARCHAR course_id FK
        BIGINT task_id FK
        VARCHAR knowledge_point_ids
        SMALLINT answer_result
        TIMESTAMPTZ answer_time
        BIGINT exercise_session_id FK
        BIGINT exercise_progress_id FK
        VARCHAR answer_method
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    MASTERY_KNOWLEDGE_POINT_MASTERY {
        BIGSERIAL id PK
        VARCHAR user_id FK
        VARCHAR knowledge_point_id FK
        NUMERIC mastery_value
        INTEGER answer_count
        BOOLEAN is_weak_point
        TIMESTAMPTZ last_updated_at
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    MASTERY_WEAK_POINT {
        BIGSERIAL id PK
        VARCHAR user_id FK
        VARCHAR knowledge_point_id FK
        NUMERIC current_mastery
        NUMERIC target_mastery
        NUMERIC gap_ratio
        TIMESTAMPTZ analysis_time
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    %% ClickHouse 行为日志表
    EXERCISE_BEHAVIOR_LOGS {
        Date log_date
        DateTime64 event_time
        UInt64 session_id
        UInt64 user_id
        String course_id
        String question_id
        String event_type
        String event_category
        String event_details
        String session_state
    }

    %% 关系定义
    EXERCISE_SESSIONS ||--o{ EXERCISE_PROGRESS : "tracks"
    EXERCISE_SESSIONS ||--o{ EXERCISE_RECOMMENDATIONS : "generates"
    EXERCISE_SESSIONS ||--o{ EXERCISE_FEEDBACK : "receives"
    EXERCISE_SESSIONS ||--|| EXERCISE_REPORTS : "produces"
    EXERCISE_SESSIONS ||--o{ EXERCISE_BEHAVIOR_LOGS : "logs"
    
    EXERCISE_PROGRESS ||--o{ EXERCISE_FEEDBACK : "triggers"
    EXERCISE_PROGRESS ||--|| MASTERY_ANSWER_RECORD : "records"
    
    MASTERY_LEARNING_TASK ||--o{ EXERCISE_SESSIONS : "configures"
    MASTERY_KNOWLEDGE_POINT_MASTERY ||..o{ EXERCISE_RECOMMENDATIONS : "influences"
    MASTERY_WEAK_POINT ||..o{ EXERCISE_RECOMMENDATIONS : "guides"
    MASTERY_ANSWER_RECORD ||--o{ MASTERY_KNOWLEDGE_POINT_MASTERY : "updates"
```

--- 

## 5. 核心业务场景与数据操作分析

### 5.1 场景：学生开始巩固练习

#### 5.1.1 场景描述
学生在完成AI课程后，系统自动触发巩固练习推荐。学生点击开始练习，系统创建练习会话，根据学生掌握度和薄弱知识点进行智能推题，开始首道题目的作答流程。

#### 5.1.2 涉及的主要数据表
- `tbl_exercise_sessions` - 创建练习会话
- `tbl_exercise_recommendations` - 记录推题策略和决策
- `tbl_exercise_progress` - 初始化首题进度
- `tbl_mastery_knowledge_point_mastery` - 查询当前掌握度
- `tbl_mastery_weak_point` - 查询薄弱知识点
- `tbl_exercise_behavior_logs` - 记录开始练习行为

#### 5.1.3 数据操作流程 (CRUD 分析)
- **步骤 1: 查询学生掌握度数据**
  - **读取 (Read)**:
    - 从 `tbl_mastery_knowledge_point_mastery` 表中查询用户掌握度
    - 查询条件：`user_id = {学生ID} AND status = 1 AND deleted_at IS NULL`
    - 涉及字段：`knowledge_point_id`, `mastery_value`, `is_weak_point`
    - 目的：为智能推题提供掌握度基础数据

- **步骤 2: 查询薄弱知识点**
  - **读取 (Read)**:
    - 从 `tbl_mastery_weak_point` 表中查询薄弱点
    - 查询条件：`user_id = {学生ID} AND deleted_at IS NULL ORDER BY gap_ratio DESC`
    - 涉及字段：`knowledge_point_id`, `current_mastery`, `gap_ratio`
    - 目的：优先推荐薄弱知识点的题目

- **步骤 3: 创建练习会话**
  - **创建 (Create)**:
    - 在 `tbl_exercise_sessions` 表中插入新会话
    - 涉及字段：`user_id`, `course_id`, `session_status = 'active'`, `start_time`
    - 数据来源：用户认证信息和课程上下文
    - 成功标准：返回新创建的会话ID

- **步骤 4: 执行智能推题**
  - **创建 (Create)**:
    - 在 `tbl_exercise_recommendations` 表中记录推题决策
    - 涉及字段：`session_id`, `user_id`, `question_id`, `algorithm_version`, `mastery_input`
    - 算法输入：掌握度数据、薄弱点数据、推题策略配置

- **步骤 5: 初始化题目进度**
  - **创建 (Create)**:
    - 在 `tbl_exercise_progress` 表中创建进度记录
    - 涉及字段：`session_id`, `question_id`, `question_order = 1`, `knowledge_point_ids`
    - 初始状态：`is_correct = false`, `answer_duration = 0`

#### 5.1.4 性能考量与索引支持
- 掌握度查询通过 `idx_mastery_knowledge_point_mastery_user_id` 索引优化
- 薄弱点查询通过 `idx_mastery_weak_point_user_id` 索引支持
- 会话创建通过自增主键和批量写入优化
- 推题算法采用缓存预计算的掌握度数据减少实时计算压力

---

### 5.2 场景：学生作答与掌握度实时更新

#### 5.2.1 场景描述
学生在练习过程中提交题目答案，系统实时验证答案正确性，记录作答行为，触发掌握度计算更新，并生成情感化反馈。整个过程要求在500ms内完成，确保流畅的学习体验。

#### 5.2.2 涉及的主要数据表
- `tbl_exercise_progress` - 更新作答结果和用时
- `tbl_mastery_answer_record` - 记录答题记录
- `tbl_mastery_knowledge_point_mastery` - 更新掌握度值
- `tbl_exercise_feedback` - 生成情感化反馈
- `tbl_exercise_behavior_logs` - 记录作答行为日志

#### 5.2.3 数据操作流程 (CRUD 分析)
- **步骤 1: 更新练习进度**
  - **更新 (Update)**:
    - 更新 `tbl_exercise_progress` 表中的作答结果
    - 更新条件：`session_id = {会话ID} AND question_id = {题目ID}`
    - 更新字段：`user_answer`, `correct_answer`, `is_correct`, `answer_duration`, `answer_time`
    - 影响：标记题目完成状态

- **步骤 2: 记录答题记录**
  - **创建 (Create)**:
    - 在 `tbl_mastery_answer_record` 表中插入答题记录
    - 涉及字段：`user_id`, `question_id`, `answer_result`, `answer_time`, `exercise_session_id`
    - 关联：与掌握度计算系统的现有表结构关联

- **步骤 3: 实时更新掌握度**
  - **读取 (Read)**:
    - 从 `tbl_mastery_knowledge_point_mastery` 表中查询当前掌握度
    - 查询条件：基于题目关联的知识点ID
  - **更新 (Update)**:
    - 更新掌握度值，使用增量计算算法
    - 更新字段：`mastery_value`, `answer_count`, `last_updated_at`, `update_reason`
    - 计算规则：基于答题正确性、题目难度、用时的加权算法

- **步骤 4: 生成情感化反馈**
  - **创建 (Create)**:
    - 在 `tbl_exercise_feedback` 表中插入反馈记录
    - 涉及字段：`session_id`, `progress_id`, `feedback_type`, `emotion_type`, `feedback_content`
    - 反馈策略：根据答题正确性和掌握度变化选择反馈类型

#### 5.2.4 性能考量与索引支持
- 进度更新通过 `uk_exercise_progress_session_question` 唯一索引快速定位
- 掌握度更新通过 `uk_mastery_knowledge_point_mastery_user_knowledge` 索引优化
- 答题记录写入使用批量提交减少数据库压力
- 反馈生成采用异步处理避免阻塞主流程

---

### 5.3 场景：练习完成与学习报告生成

#### 5.3.1 场景描述
学生完成巩固练习中的所有题目后，系统判断是否需要"再练一次"，如不需要则标记练习完成，生成学习报告，展示学习成果和掌握度变化，并推送给教师端进行学情分析。

#### 5.3.2 涉及的主要数据表
- `tbl_exercise_sessions` - 更新会话完成状态
- `tbl_exercise_progress` - 统计完成情况
- `tbl_exercise_reports` - 生成学习报告
- `tbl_mastery_knowledge_point_mastery` - 汇总掌握度变化
- `tbl_exercise_recommendations` - 分析推题效果

#### 5.3.3 数据操作流程 (CRUD 分析)
- **步骤 1: 检查练习完成状态**
  - **读取 (Read)**:
    - 从 `tbl_exercise_progress` 表中统计完成情况
    - 查询条件：`session_id = {会话ID} AND is_correct = true`
    - 统计字段：`COUNT(*) as correct_count`, `SUM(answer_duration) as total_time`
    - 目的：计算正确率和总用时

- **步骤 2: 判断是否需要再练一次**
  - **读取 (Read)**:
    - 查询相关知识点的当前掌握度
    - 判断逻辑：基于掌握度阈值和练习表现
    - 决策结果：继续练习或完成练习

- **步骤 3: 更新会话状态**
  - **更新 (Update)**:
    - 更新 `tbl_exercise_sessions` 表的会话状态
    - 更新字段：`session_status = 'completed'`, `end_time`, `total_duration`, `completed_questions`
    - 影响：标记练习会话结束

- **步骤 4: 生成学习报告**
  - **创建 (Create)**:
    - 在 `tbl_exercise_reports` 表中插入报告记录
    - 涉及字段：`session_id`, `total_questions`, `correct_answers`, `accuracy_rate`, `mastery_changes`
    - 数据来源：练习进度统计、掌握度变化、推题效果分析

#### 5.3.4 性能考量与索引支持
- 完成状态统计通过 `idx_exercise_progress_session_id` 索引优化
- 掌握度变化查询使用时间范围索引加速
- 报告生成采用异步队列处理避免阻塞用户响应
- 报告数据通过 `uk_exercise_reports_session` 确保唯一性

---

## 6. 索引策略详细说明

### 6.1 PostgreSQL 索引策略

#### 6.1.1 主要索引设计原则
- **查询模式驱动**：基于核心业务查询模式设计复合索引
- **选择性优先**：优先为高选择性字段建立索引
- **复合索引顺序**：按照查询频率和过滤性排序复合索引字段
- **部分索引优化**：使用 `WHERE deleted_at IS NULL` 减少索引大小

#### 6.1.2 核心索引详解

**1. 练习会话相关索引**
```sql
-- 支持按用户查询练习历史，最常用的查询模式
CREATE INDEX idx_exercise_sessions_user_id ON tbl_exercise_sessions(user_id) WHERE deleted_at IS NULL;

-- 支持按用户和课程组合查询，精确定位特定课程的练习记录
CREATE INDEX idx_exercise_sessions_user_course ON tbl_exercise_sessions(user_id, course_id) WHERE deleted_at IS NULL;

-- 支持按会话状态查询，用于统计分析和状态管理
CREATE INDEX idx_exercise_sessions_status ON tbl_exercise_sessions(session_status) WHERE deleted_at IS NULL;
```

**2. 练习进度相关索引**
```sql
-- 最关键的性能索引，支持快速定位特定会话的题目进度
CREATE UNIQUE INDEX uk_exercise_progress_session_question ON tbl_exercise_progress(session_id, question_id, retry_count) WHERE deleted_at IS NULL;

-- 支持按完成状态统计分析
CREATE INDEX idx_exercise_progress_completion ON tbl_exercise_progress(is_correct, completion_time) WHERE deleted_at IS NULL;
```

**3. 掌握度相关索引**
```sql
-- 支持快速查询用户的知识点掌握情况
CREATE INDEX idx_mastery_knowledge_point_mastery_user_id ON tbl_mastery_knowledge_point_mastery(user_id) WHERE deleted_at IS NULL;

-- 确保用户对每个知识点只有一条掌握度记录
CREATE UNIQUE INDEX uk_mastery_knowledge_point_mastery_user_knowledge ON tbl_mastery_knowledge_point_mastery(user_id, knowledge_point_id) WHERE deleted_at IS NULL;
```

#### 6.1.3 JSON索引策略
```sql
-- 支持对推题策略配置的查询
CREATE INDEX idx_exercise_recommendations_mastery_input_gin ON tbl_exercise_recommendations USING gin (mastery_input);

-- 支持对反馈内容的搜索
CREATE INDEX idx_exercise_feedback_content_gin ON tbl_exercise_feedback USING gin (feedback_content);
```

### 6.2 ClickHouse 索引策略

#### 6.2.1 排序键设计
```sql
-- 练习行为日志表的排序键
ORDER BY (log_date, user_id, event_time, event_type)
```
**设计说明**：
- `log_date`：支持按日期范围的分区裁剪
- `user_id`：支持按用户聚合查询
- `event_time`：支持时序分析
- `event_type`：支持按事件类型统计

#### 6.2.2 跳数索引设计
```sql
-- 支持事件类型的快速过滤
ALTER TABLE tbl_exercise_behavior_logs ADD INDEX idx_event_type event_type TYPE set(0) GRANULARITY 1;

-- 支持会话ID的快速定位
ALTER TABLE tbl_exercise_behavior_logs ADD INDEX idx_session_id session_id TYPE bloom_filter GRANULARITY 1;
```

### 6.3 索引监控与优化

#### 6.3.1 索引使用监控
- 定期分析 `pg_stat_user_indexes` 视图监控索引使用情况
- 识别未使用的索引并考虑删除
- 监控索引大小和更新成本

#### 6.3.2 查询性能优化
- 使用 `EXPLAIN ANALYZE` 分析慢查询
- 针对高频查询优化索引策略
- 考虑使用物化视图预计算复杂查询结果

---

## 7. 数据量预估与性能优化

### 7.1 数据量预估

#### 7.1.1 PostgreSQL 表数据量预估

| 表名 | 3个月预估 | 1年预估 | 3年预估 | 数据特征 | 增长模式 |
|------|----------|--------|--------|----------|----------|
| `tbl_exercise_sessions` | 100万条 | 600万条 | 2000万条 | 学习会话记录，用户活跃度相关 | 随用户增长线性增长 |
| `tbl_exercise_progress` | 500万条 | 3000万条 | 1亿条 | 每会话平均5-10条进度记录 | 与会话数成正比增长 |
| `tbl_exercise_recommendations` | 500万条 | 3000万条 | 1亿条 | 推题记录，每题一条 | 与题目数成正比增长 |
| `tbl_exercise_feedback` | 300万条 | 1800万条 | 6000万条 | 约60%的题目产生反馈 | 与作答量相关 |
| `tbl_exercise_reports` | 100万条 | 600万条 | 2000万条 | 每会话一条报告 | 与会话数1:1增长 |
| `tbl_mastery_knowledge_point_mastery` | 50万条 | 150万条 | 500万条 | 用户×知识点，增长相对缓慢 | 新用户和新知识点驱动 |
| `tbl_mastery_answer_record` | 500万条 | 3000万条 | 1亿条 | 每次作答一条记录 | 高频增长 |

#### 7.1.2 ClickHouse 表数据量预估

| 表名 | 3个月预估 | 1年预估 | 3年预估 | 数据特征 | 增长模式 |
|------|----------|--------|--------|----------|----------|
| `tbl_exercise_behavior_logs` | 5000万条 | 3亿条 | 10亿条 | 高频操作日志 | 极高频增长，每用户每天50-100条 |

### 7.2 存储空间预估

#### 7.2.1 PostgreSQL 存储预估
```
- 核心业务表总存储空间（1年）：约 150GB
- 包含索引空间：约 300GB  
- 预留增长空间：约 500GB
```

#### 7.2.2 ClickHouse 存储预估
```
- 行为日志表（1年）：约 80GB（压缩后）
- 原始数据量：约 300GB
- 压缩率：约 75%
```

### 7.3 性能优化策略

#### 7.3.1 查询性能优化
**缓存策略**：
- 掌握度数据使用 Redis 缓存，TTL 30分钟
- 题目内容使用 CDN + Redis 二级缓存
- 推题策略配置使用内存缓存，支持热更新

**分页优化**：
- 大结果集查询使用 `LIMIT + OFFSET` 或游标分页
- 历史数据查询使用时间分片

**读写分离**：
- 查询操作使用只读副本
- 写操作使用主库
- 报告生成等分析查询使用专用读库

#### 7.3.2 写入性能优化
**批量操作**：
- 行为日志使用批量写入，每批1000条
- 掌握度更新使用合并写入减少更新频率

**异步处理**：
- 非关键路径操作使用消息队列异步处理
- 学习报告生成采用异步任务

#### 7.3.3 数据生命周期管理
**PostgreSQL 归档策略**：
- 练习会话数据保留3年，超期归档到冷存储
- 行为日志数据保留1年，超期删除
- 掌握度核心数据永久保留

**ClickHouse TTL 策略**：
- 行为日志数据自动保留1年：`TTL log_date + INTERVAL 365 DAY DELETE`
- 分区管理：按月分区，自动删除过期分区

---

## 8. 特殊设计考量与权衡

### 8.1 数据一致性设计

#### 8.1.1 分布式事务处理
**掌握度更新的一致性保障**：
- 使用本地事务确保 `tbl_exercise_progress` 和 `tbl_mastery_answer_record` 的强一致性
- 掌握度计算使用最终一致性，通过补偿机制处理异常情况
- 关键状态变更使用Saga模式管理分布式事务

#### 8.1.2 并发控制策略
**乐观锁机制**：
- 掌握度更新使用版本号机制避免并发冲突
- 练习进度更新使用唯一约束防止重复提交

**悲观锁场景**：
- 练习会话状态变更使用行级锁确保状态一致性

### 8.2 扩展性设计权衡

#### 8.2.1 分片策略考虑
**按用户分片**：
- 优点：用户相关数据访问局部性好
- 缺点：可能出现数据倾斜
- 适用场景：用户量达到1000万级别时考虑

**按时间分片**：
- 优点：历史数据可独立管理
- 缺点：跨时间范围查询复杂
- 适用场景：单表数据量超过1亿条时考虑

#### 8.2.2 读写分离权衡
**读写分离收益**：
- 查询性能提升：读操作分流到从库
- 写性能保障：主库专注写操作
- 成本增加：需要维护多个数据库实例

**一致性权衡**：
- 掌握度查询可接受最终一致性（延迟<1秒）
- 练习进度查询需要强一致性（读主库）

### 8.3 安全性设计考量

#### 8.3.1 数据脱敏策略
- 用户ID使用哈希处理，避免明文存储敏感信息
- 学习行为数据去除IP地址等隐私信息
- 数据导出接口强制脱敏处理

#### 8.3.2 访问控制
- 数据库连接使用最小权限原则
- 不同服务使用不同数据库账号
- 敏感操作记录审计日志

### 8.4 监控与可观测性

#### 8.4.1 业务指标监控
- 练习会话成功率：目标 > 99%
- 掌握度计算延迟：目标 < 500ms
- 推题响应时间：目标 < 2秒

#### 8.4.2 技术指标监控
- 数据库连接池使用率：目标 < 80%
- 慢查询数量：目标 < 10/分钟
- 缓存命中率：目标 > 90%

---

## 9. 规范合规性总结

### 9.1 PostgreSQL 规范遵循情况

| 规范要求 | 遵循情况 | 具体体现 |
|----------|----------|----------|
| **命名规范** | ✅ 完全遵循 | 表名使用 `tbl_` 前缀，字段名使用下划线分隔 |
| **数据类型规范** | ✅ 完全遵循 | 使用 BIGSERIAL 主键，TIMESTAMPTZ 时间类型，NUMERIC 精确数值 |
| **约束设计** | ✅ 完全遵循 | 主键、唯一约束、非空约束完整设计 |
| **索引策略** | ✅ 完全遵循 | 部分索引、复合索引、GIN索引合理使用 |
| **注释规范** | ✅ 完全遵循 | 表和字段均有中文注释，说明业务含义 |
| **软删除** | ✅ 完全遵循 | 统一使用 deleted_at 字段实现软删除 |
| **标准字段** | ✅ 完全遵循 | 包含 status, created_at, updated_at 等标准字段 |

### 9.2 ClickHouse 规范遵循情况

| 规范要求 | 遵循情况 | 具体体现 |
|----------|----------|----------|
| **引擎选择** | ✅ 完全遵循 | 使用 MergeTree 引擎，支持分区和TTL |
| **排序键设计** | ✅ 完全遵循 | 按查询模式设计排序键，支持分区裁剪 |
| **数据类型** | ✅ 完全遵循 | 使用 UInt64、DateTime64、LowCardinality 等优化类型 |
| **分区策略** | ✅ 完全遵循 | 按月分区，支持自动数据管理 |
| **TTL设置** | ✅ 完全遵循 | 设置合理的数据保留期限 |
| **跳数索引** | ✅ 完全遵循 | 针对高频过滤字段设置跳数索引 |

### 9.3 架构设计规范遵循

| 规范要求 | 遵循情况 | 具体体现 |
|----------|----------|----------|
| **微服务原则** | ✅ 完全遵循 | 数据表设计支持服务独立部署和扩展 |
| **CQRS模式** | ✅ 完全遵循 | 读写分离设计，查询优化独立考虑 |
| **事件驱动** | ✅ 完全遵循 | 支持异步事件处理的数据结构设计 |
| **数据一致性** | ✅ 完全遵循 | 分布式事务和最终一致性策略明确 |

---

## 10. 附录

### 10.1 相关文档引用
- **技术架构设计文档**: `be-s2-1-ai-td-学生端巩固练习-claude-3-5-sonnet-20241022.md`
- **数据实体设计文档**: `be-s3-1-ai-de-学生端-巩固练习-claude-sonnet-4.md`
- **掌握度策略数据库设计**: `be-s3-4-ai-dd-掌握度策略-V1.0-claude-3.7.md`
- **AI课程框架数据库设计**: `be-s3-4-ai-dd-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md`

### 10.2 设计决策记录

| 决策点 | 选择方案 | 理由 | 权衡考虑 |
|--------|----------|------|----------|
| 推题记录存储 | PostgreSQL单独表 | 需要复杂查询和事务支持 | vs. ClickHouse存储，选择更好的事务性 |
| 行为日志存储 | ClickHouse | 高频写入，分析查询为主 | vs. PostgreSQL，选择更好的写入性能 |
| 掌握度表结构 | 复用现有设计 | 保持系统一致性 | vs. 重新设计，选择兼容性 |
| 索引策略 | 复合索引为主 | 支持多维度查询 | vs. 单列索引，选择查询性能 |

### 10.3 后续优化方向
1. **分库分表策略**：当单表数据量超过5000万条时，考虑分片策略
2. **缓存优化**：引入多级缓存体系，进一步提升查询性能
3. **实时计算**：考虑引入Flink等流计算引擎优化掌握度实时计算
4. **数据湖集成**：将历史数据迁移到数据湖支持深度分析

---
# 产品需求文档：教师端作业报告 V1.0 (Gemini-2.5-Pro)

## 1. 需求背景与目标

### 1.1 需求目标
- **核心目标:** 实现教师端作业报告的核心功能 (P0)，确保教师能够有效监控学生学习情况，跑通业务MVP流程。
- **用户价值:**
    - 提升教师对学生学习进度、完成情况、答题表现（正确率、错题）、用时等数据的掌控能力。
    - 支持教师扮演"鼓励者"角色，系统能辅助识别需要表扬（如进步明显、连续答对）和需要关注（如未完成、表现下降）的学生，以便及时干预。
- **预期收益:**
    - 完成教师端核心功能MVP，支持业务流程验证。
    - (隐含指标) 提升教师工作效率，改善教学反馈质量，增强学生学习效果。

## 2. 功能范围

### 2.1 核心功能 (P0)
- **作业列表展示:**
    - *描述:* 向教师展示其负责或有权限查看的作业任务列表。列表应包含任务核心信息（如类型、名称、班级、学科、日期、完成率、正确率等），并支持按类型、班级、学科、日期进行筛选和关键词搜索。本人布置的任务应支持编辑、删除、复制操作。
    - *用户场景:* 教师需要快速概览和查找自己布置或负责的作业。
    - *优先级:* 高
    - *依赖关系:* 无
- **作业报告详情:**
    - *描述:* 点击作业列表中的任务卡片，进入该任务的详细报告页面。报告包含三个主要标签页：学生报告、答题结果、学生提问。
    - *用户场景:* 教师需要深入分析单次作业的整体情况和具体细节。
    - *优先级:* 高
    - *依赖关系:* 作业列表展示
- **学生报告 Tab:**
    - *描述:* 展示任务的整体班级统计（发布/完成时间、班级、素材范围、平均进度、平均正确率），并突出显示系统建议"鼓励"和"关注"的学生列表。提供完整的学生列表，按UID排序，并高亮建议鼓励/关注的学生；列表展示学生个体进度、正确率等数据，并对偏离班级均值的异常数据进行视觉提示。支持点击学生进入该生的具体作业报告。
    - *用户场景:* 教师需要快速了解班级整体表现，并识别需要特别关注或表扬的学生个体。
    - *优先级:* 高
    - *依赖关系:* 作业报告详情
- **答题结果 Tab:**
    - *描述:* 展示本次作业中所有题目的答题统计信息（不适用于无答题环节的资源任务）。显示题目总数和共性错题数。提供题目关键词搜索功能。列表展示每道题目的题干、选项（支持展开/收起）、答案解析（支持展开/收起）、该题的正确率、答错人数、作答人数。提供侧边题目面板，快速导航并用颜色区分题目正确率。支持点击题目查看题目详情。
    - *用户场景:* 教师需要分析题目难度、学生掌握情况，识别共性错误点。
    - *优先级:* 高
    - *依赖关系:* 作业报告详情
- **学生提问 Tab:**
    - *描述:* 汇总展示学生针对该次作业提出的问题列表。支持查看提问详情。
    *用户场景:* 教师需要了解学生在作业中遇到的具体疑问并进行解答。
    *优先级:* 高
    *依赖关系:* 作业报告详情
- **单学生作业报告:**
    - *描述:* 从学生报告Tab点击学生，或从其他入口（如上课模块）进入，展示特定学生在该次作业中的详细报告，包含该生的答题结果和提问情况。
    *用户场景:* 教师需要聚焦于单个学生的具体学习表现和问题。
    *优先级:* 高
    *依赖关系:* 作业报告详情
- **策略说明:**
    - *描述:* (此功能更多是数据处理逻辑，而非直接交互界面) 定义"鼓励"、"关注"学生的规则，以及"共性错题"的识别规则，并在报告中应用这些规则。
    *用户场景:* 教师依赖系统策略来高效地识别关键信息。
    *优先级:* 高
    *依赖关系:* 各报告Tab的数据计算

### 2.2 辅助功能
- **报告筛选:** 作业报告详情页内支持按班级、素材范围进行数据筛选。
- **数据导出 (可能):** (原始文档未明确提及，但通常是报告类功能的辅助需求) 支持将报告数据导出。

### 2.3 非本期功能 (P1)
- **提醒设置:** 支持用户自定义提醒策略（如特定事件触发通知）。

## 3. 计算规则与公式

- **题目正确率 (答题结果Tab):**
    - *公式:* `该题在任务下的首答正确率 = (作答了该题的学生中，答对小空数量的总和 / (作答了该题的学生数 * 该题总小空数)) * 100%` (注：原始文档描述为"答对小空数量/题目中的总小空之和"，这里进行了澄清和细化理解，按平均每个作答学生答对的小空比例计算)
    - *参数说明:*
        - `小空`: 指题目中的填空、选择等计分点。
        - `首答`: 若学生在任务中多次遇到同题，仅计算首次作答数据。
        - `作答了`: 对于已提交的课堂/测试/作业，学生看到题目但选择"暂不作答"也记为已作答；对于未提交的作业，跳过不答则不记为作答。
- **题目答错人数 (答题结果Tab):**
    - *规则:* 统计在任务下首答该题时，至少答错一个"小空"的学生数量。
- **题目作答人数 (答题结果Tab):**
    - *规则:* 统计在任务下对该题有作答记录（包括看到但选择"暂不作答"的情况，按上述定义）的学生数量。
- **班级进度/完成率 (学生报告Tab):**
    - *规则:* 展示当前班级、当前素材范围内的班级平均进度（或完成率）。(注：原始文档未提供具体计算公式)
- **班级正确率 (学生报告Tab):**
    - *规则:* 展示当前班级、当前素材范围内的班级平均正确率。(注：原始文档未提供具体计算公式，可能为所有学生个体正确率的平均值)
- **异常数据飘色 (学生报告Tab - 学生列表):**
    - *规则:* 对学生列表中的个体数据（进度/正确率），如果数值低于对应班级平均值的 20% 或处于班级最后 10%（最低的10%），则进行特殊颜色标记。(注：规则需进一步明确是低于平均值的80%【即下降20%】，还是指绝对值比平均值小20%。按前者理解。)
- **共性错题 (答题结果Tab):**
    - *规则:* 在当前班级及素材筛选条件下，识别出的共性错题。(注：原始文档未提供具体的识别阈值，例如错误率高于多少算共性错题)
- **建议鼓励/关注学生 (学生报告Tab):**
    - *规则:* 基于预设策略识别。鼓励策略可能基于进步、连续答对等；关注策略可能基于未完成、完成情况明显下降等。(注：原始文档未提供具体规则细节)

## 4. 用户场景与故事

### 场景1: 监控班级整体作业表现
作为一个**教师**，我希望能够**快速查看某次作业的全班平均完成度和正确率**，以及**答题人数、共性错题等关键指标**，这样我就可以**及时了解班级整体对知识点的掌握情况**。目前的痛点是信息分散，需要手动统计，如果能在一个报告页面清晰展示，对我的教学分析会有很大帮助。

**关键步骤：**
1. 登录教师端应用。
2. 进入"作业"列表。
3. 选择指定的作业任务。
4. 查看作业报告详情页顶部的班级统计信息或"学生报告"、"答题结果"Tab中的汇总数据。

**流程图:**
```mermaid
graph TD
    A[登录教师端] --> B[进入作业列表页面];
    B --> C{选择指定作业任务};
    C --> D[进入作业报告详情页];
    D --> E[查看页面顶部统计 或 '学生报告'/'答题结果' Tab];
    E --> F[获取班级整体表现信息];
```

- *优先级:* 高
- *依赖关系:* 无

### 场景2: 识别并跟进个别学生
作为一个**教师**，我希望系统能够**自动提示我哪些学生在作业中表现突出（需要鼓励）或存在困难（需要关注）**，这样我就可以**有针对性地进行表扬或辅导**。目前的痛点是难以逐一细致观察每个学生，如果系统能高亮显示异常或优秀的学生，能极大提高我的工作效率和干预的及时性。

**关键步骤：**
1. 进入指定作业的"作业报告"详情页。
2. 查看"学生报告"Tab下的"建议鼓励学生"和"建议关注学生"列表。
3. （可选）点击学生姓名，查看该生的详细作业报告（答题结果、提问等）。
4. （可选）根据情况采取后续沟通或辅导行动（应用外）。

**流程图:**
```mermaid
graph TD
    A[进入指定作业报告详情页] --> B[查看'学生报告' Tab];
    B --> C[查看'建议鼓励/关注'列表];
    C --> D{发现目标学生};
    D -- Yes --> E{点击学生姓名};
    E --> F[查看该生详细报告];
    F --> G[执行沟通/辅导（应用外）];
    D -- No --> H[结束];
    G --> H;
```

- *优先级:* 高
- *依赖关系:* 场景1 (需要先进入报告)

### 场景3: 分析题目难点与共性错误
作为一个**教师**，我希望能够**方便地查看作业中每道题的正确率、答错人数**，并**快速定位错误率高的共性错题**，这样我就可以**了解哪些知识点学生普遍掌握不牢，需要在后续教学中重点讲解**。目前的痛点是需要手动批改或分析大量答卷才能发现共性问题，如果报告能直接展示题目维度的统计和共性错题，将极大节省我的时间。

**关键步骤：**
1. 进入指定作业的"作业报告"详情页。
2. 切换到"答题结果"Tab。
3. 查看页面顶部的"共性错题"数量提示。
4. 浏览题目列表，关注正确率低、答错人数多的题目。
5. （可选）使用侧边题目面板快速导航或按正确率排序。
6. （可选）点击题目查看详情。

**流程图:**
```mermaid
graph TD
    A[进入指定作业报告详情页] --> B[切换到'答题结果' Tab];
    B --> C[查看'共性错题'数量];
    B --> D[浏览题目列表（含正确率/答错人数）];
    D --> E{发现需分析的题目};
    E -- Yes --> F{使用题目面板导航?};
    F -- Yes --> G[点击面板题号];
    F -- No --> H{点击题目查看详情?};
    G --> H;
    H -- Yes --> I[查看题目详情页];
    H -- No --> J[结束];
    E -- No --> J;
```

- *优先级:* 高
- *依赖关系:* 场景1

### 场景4: 解答学生疑问
作为一个**教师**，我希望能够**集中查看学生针对某次作业提出的所有问题**，这样我就可以**及时、统一地进行解答，澄清学生的疑惑**。目前的痛点是问题可能散落在不同地方，容易遗漏，如果报告中能汇总所有提问，将方便我管理和回复。

**关键步骤：**
1. 进入指定作业的"作业报告"详情页。
2. 切换到"学生提问"Tab。
3. 查看学生提问列表。
4. （可选）点击具体问题查看详情并进行回复（回复功能可能在此报告范围外）。

**流程图:**
```mermaid
graph TD
    A[进入指定作业报告详情页] --> B[切换到'学生提问' Tab];
    B --> C[查看学生提问列表];
    C --> D{发现需处理的提问?};
    D -- Yes --> E{点击具体问题};
    E --> F[查看提问详情];
    F --> G[进行回复（可能在应用外）];
    G --> H[结束];
    D -- No --> H;
```

- *优先级:* 高
- *依赖关系:* 场景1

## 5. 业务流程图

### 流程1: 教师查看与分析作业报告 (核心流程)

```mermaid
graph TD
    A[教师登录] --> B[进入作业列表];
    B --> C{选择作业任务};
    C --> D[进入作业报告详情];
    subgraph 作业报告详情
        D --> E[学生报告 Tab];
        D --> F[答题结果 Tab];
        D --> G[学生提问 Tab];
    end
    E --> E1[查看班级统计];
    E --> E2[查看建议鼓励/关注列表];
    E2 --> E3{点击学生};
    E3 --> H[查看单学生报告];
    F --> F1[查看题目统计列表];
    F1 --> F2[查看共性错题];
    F1 --> F3{点击题目};
    F3 --> I[查看题目详情];
    G --> G1[查看学生提问列表];
    G1 --> G2{点击提问};
    G2 --> J[查看提问详情];

    style E fill:#f9f,stroke:#333,stroke-width:2px
    style F fill:#ccf,stroke:#333,stroke-width:2px
    style G fill:#cfc,stroke:#333,stroke-width:2px
```

### 流程2: 系统识别并展示需关注/鼓励学生 (时序图)

```mermaid
sequenceDiagram
    participant User as 教师
    participant FE as 前端应用
    participant BE as 后端服务
    participant DB as 数据库

    User->>FE: 进入指定作业报告的'学生报告'Tab
    FE->>BE: 请求作业报告数据 (作业ID, 班级ID)
    BE->>DB: 查询作业成绩、完成情况等原始数据
    DB-->>BE: 返回原始数据
    BE->>BE: 应用'鼓励/关注'策略规则进行计算
    BE-->>FE: 返回报告数据 (含建议列表)
    FE->>FE: 渲染页面,高亮显示'鼓励'和'关注'学生列表
    User->>FE: 查看高亮列表
```

## 6. 性能与安全需求
(原始文档未提及，暂无)

## 7. 验收标准
(原始文档未提及，暂无)

## 8. 其他需求
(原始文档未提及，暂无)

## 9. 用户反馈和迭代计划
(原始文档未提及，暂无)

## 10. 需求检查清单

使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 (来自原始文档 五、详细产品方案) | 对应需求点 (本文档章节/功能) | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
| --- | --- | --- | --- | --- | --- | --- | --- |
| **作业列表** | | | | | | | |
| 作业列表 - 数据范围与权限 (按职务区分) | 2.1 核心功能 - 作业列表展示 (提及权限) | ✅ | ⚠️ | ✅ | ⚠️ | ✅ | 新文档提及权限，但未包含原始角色权限表。建议补充表格。 |
| 作业列表 - 筛选功能 (类型、班级、学科、布置日期) | 2.1 核心功能 - 作业列表展示 | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 作业列表 - 搜索功能 (关键词模糊匹配任务名称) | 2.1 核心功能 - 作业列表展示 | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 作业列表 - 卡片指标展示 (完成率、正确率等) | 2.1 核心功能 - 作业列表展示 (描述中提及) | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 作业列表 - 卡片操作 (本人布置任务可编辑、删除、复制) | 2.1 核心功能 - 作业列表展示 | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| **作业报告 - 通用** | | | | | | | |
| 作业报告 - 页面名称和返回按钮 | 2.1 核心功能 - 作业报告详情 (隐含) | ✅ | ✅ | ✅ | ✅ | ✅ | 基础UI元素 |
| 作业报告 - 顶部任务详情与筛选 (发布/完成时间, 班级, 素材范围) | 2.1 核心功能 - 作业报告详情, 2.2 辅助功能 - 报告筛选 | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 作业报告 - Tab筛选 (学生报告、答题结果、学生提问) | 2.1 核心功能 - 作业报告详情 | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| **作业报告 - 学生报告 Tab** | | | | | | | |
| 学生报告Tab - 建议鼓励学生 (列表、详情抽屉、干预操作) | 2.1 核心功能 - 学生报告 Tab, 2.1 核心功能 - 策略说明 | ✅ | ⚠️ | ✅ | ⚠️ | ✅ | 干预细节和策略具体规则在新PRD中待细化。 |
| 学生报告Tab - 建议关注学生 (列表、详情抽屉、干预操作) | 2.1 核心功能 - 学生报告 Tab, 2.1 核心功能 - 策略说明 | ✅ | ⚠️ | ✅ | ⚠️ | ✅ | 干预细节和策略具体规则在新PRD中待细化。 |
| 学生报告Tab - 班级进度 (平均完成率) | 2.1 核心功能 - 学生报告 Tab, 3. 计算规则与公式 | ✅ | ⚠️ | ✅ | ⚠️ | ✅ | 具体计算公式缺失。 |
| 学生报告Tab - 级正确率 (平均正确率) | 2.1 核心功能 - 学生报告 Tab, 3. 计算规则与公式 | ✅ | ⚠️ | ✅ | ⚠️ | ✅ | 具体计算公式缺失。 |
| 学生报告Tab - 任务学生列表 (排序, UID, 进度, 正确率, 异常飘色, 查看详情) | 2.1 核心功能 - 学生报告 Tab, 3. 计算规则与公式 (异常数据飘色) | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| **作业报告 - 答题结果 Tab** | | | | | | | |
| 答题结果Tab - 数据展示逻辑 (区分任务类型, 资源任务无此Tab) | 2.1 核心功能 - 答题结果 Tab (描述中提及) | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 答题结果Tab - "共xx道题，xx道共性错题"及解释 | 2.1 核心功能 - 答题结果 Tab, 3. 计算规则与公式 (共性错题) | ✅ | ⚠️ | ✅ | ⚠️ | ✅ | 共性错题具体判断阈值待细化。 |
| 答题结果Tab - 搜索题目关键词 (题干、选项、解析) | 2.1 核心功能 - 答题结果 Tab | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 答题结果Tab - 题目信息展示 (正确率, 答错人数, 作答人数, 首答原则) | 2.1 核心功能 - 答题结果 Tab, 3. 计算规则与公式 | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| 答题结果Tab - 侧边题目面板 (展开/收起, 题号, 颜色区分正确率) | 2.1 核心功能 - 答题结果 Tab | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| **作业报告 - 学生提问 Tab** | | | | | | | |
| 学生提问Tab - 汇总展示学生提问，支持查看详情 | 2.1 核心功能 - 学生提问 Tab | ✅ | ✅ | ✅ | ✅ | ✅ | 原始文档对此Tab的表格化细则较少，新文档已根据概览描述。 |
| **单学生作业报告** | | | | | | | |
| 单学生作业报告 - 包含该生的答题结果和提问情况 | 2.1 核心功能 - 单学生作业报告 | ✅ | ✅ | ✅ | ✅ | ✅ |  |
| **策略说明** | | | | | | | |
| 策略说明 - 鼓励、关注、共性错题的规则定义 | 2.1 核心功能 - 策略说明, 3. 计算规则与公式 | ✅ | ⚠️ | ✅ | ⚠️ | ✅ | 规则细节和判断阈值在新PRD中待细化，依赖外部文档或进一步确认。 |
| **非本期功能 (P1)** | | | | | | | |
| 提醒设置 - 用户自定义提醒策略 | 2.3 非本期功能 | ✅ | ✅ | ✅ | N/A | ✅ | P1需求已列出。 |

- **完整性:** 符号表示新文档是否覆盖了原始需求的这个具体点。
- **正确性:** 符号表示新文档对这个点的描述是否准确地反映了原始需求。⚠️ 表示新文档描述正确，但依赖于原始文档中未完全明确的细节（如具体计算公式、判断阈值等），这些细节需参照原始PRD中的链接或进一步确认。
- **一致性:** 符号表示新文档中对此点的描述与其内部其他部分是否一致。
- **可验证性:** 符号表示新文档中对此点的描述是否清晰到可以被验证或测试。⚠️ 同正确性的理由。
- **可跟踪性:** 符号表示这个原始需求点是否能清晰地追溯到新文档中的对应部分。

**说明:** 标记为 ⚠️ 的部分主要表明，虽然新PRD从功能层面提及了相关需求，但其具体实现所需的精确计算口径、判断阈值等细节，在原始PRD中主要通过链接外部文档（如飞书）或在详细图文解析中提及，新PRD在生成时遵循了"不自行联想补充"和"忽略图片解析"的原则，故这些深层细节需要架构师或后续环节进一步参考原始文档的完整上下文或与需求方确认。

## 11. 附录
(原始文档未提供原型图或术语表，暂无) 
// @ts-nocheck
// @ts-nocheck
'use client';

export const dynamic = 'force-dynamic';

import { useState, FormEvent, useEffect, useRef, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import WorkflowSidebar from '@/components/workflow/WorkflowSidebar';
import WorkflowPageWrapper from '@/components/workflow/WorkflowPageWrapper';
import { documentApi } from '@/lib/api';
import MarkdownPreviewModal from '@/components/markdown/MarkdownPreviewModal';
import WorkflowDebugPanel from '@/components/debug/WorkflowDebugPanel';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import axios from 'axios';
import { Terminal } from 'lucide-react';
import {
  Button,
  Input,
  Progress,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Box,
  useToast
} from '@chakra-ui/react';
import { CopyIcon } from '@chakra-ui/icons';
import { API_BASE_URL } from '@/app/configs/api';

export default function AiPrdPage() {
  const [activeTab, setActiveTab] = useState('url');
  const [isLoading, setIsLoading] = useState(false);
  const [mdUrl, setMdUrl] = useState('');
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [error, setError] = useState('');
  const [taskId, setTaskId] = useState('');
  const [logs, setLogs] = useState<string[]>([]);
  const [showLogs, setShowLogs] = useState(false);
  const [progress, setProgress] = useState(0);
  const [downloadUrl, setDownloadUrl] = useState('');
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [urlError, setUrlError] = useState('');
  const [prdType, setPrdType] = useState('be'); // 默认选择后端
  
  const logEndRef = useRef<HTMLDivElement>(null);
  const router = useRouter();
  const wsRef = useRef<WebSocket | null>(null);
  const toast = useToast();
  const searchParams = useSearchParams();
  const { workflowData, navigateToNextPage, getHiddenInputsProps } = useWorkflowData('ai-prd');
  
  // 处理工作流参数 - 设置format_prd_url到输入框
  useEffect(() => {
    if (workflowData.format_prd_url) {
      setMdUrl(workflowData.format_prd_url);
      setActiveTab('url');
      console.log('从工作流参数获取到format_prd_url:', workflowData.format_prd_url);
      console.log('接收到的工作流数据:', workflowData);
    }
  }, [workflowData]);
  
  // 自动滚动到最新日志
  useEffect(() => {
    if (logEndRef.current) {
      logEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [logs]);
  
  // WebSocket连接处理
  useEffect(() => {
    // 清理WebSocket连接
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
    };
  }, []);
  
  // 监听taskId变化，创建WebSocket连接
  useEffect(() => {
    if (taskId) {
      // 记录taskId变化，确保只有在用户明确点击"开始处理"按钮后才会设置taskId
      console.log('taskId变化，创建WebSocket连接:', taskId);
      
      // 关闭已有的连接
      if (wsRef.current) {
        wsRef.current.close();
        wsRef.current = null;
      }
      
      // WebSocket连接参数
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      let wsHost;
      
      // 尝试从API_BASE_URL中提取主机名
      try {
        // 检查API_BASE_URL是否是绝对URL
        if (API_BASE_URL.startsWith('http')) {
          const apiUrl = new URL(API_BASE_URL);
          wsHost = apiUrl.host;
          console.log('从API_BASE_URL提取的主机名:', wsHost);
        } else {
          // 如果API_BASE_URL是相对路径，使用当前页面的主机名
          wsHost = window.location.host;
          console.log('API_BASE_URL是相对路径，使用当前页面主机名:', wsHost);
        }
      } catch (e) {
        console.error("解析API_BASE_URL失败: ", API_BASE_URL, e);
        wsHost = window.location.host; // 使用当前页面的主机作为回退值
        console.log('使用回退主机名:', wsHost);
      }
      
      // 构建WebSocket URL
      const wsUrl = `${protocol}//${wsHost}/api/ws/${taskId}`;
      console.log('WebSocket URL:', wsUrl);
      
      console.log('正在连接WebSocket...', wsUrl);
      
      // 重连相关变量
      let reconnectAttempts = 0;
      const maxReconnectAttempts = 5;
      const reconnectInterval = 3000; // 3秒
      let reconnectTimer: NodeJS.Timeout | null = null;
      
      // 创建WebSocket连接函数
      const createWebSocketConnection = () => {
        const socket = new WebSocket(wsUrl);
        wsRef.current = socket;
        
        socket.onopen = (event) => {
          console.log('WebSocket连接已建立', event);
          addLog('WebSocket连接已建立，开始接收实时日志...');
          // 重置重连计数
          reconnectAttempts = 0;
          
          try {
            socket.send(JSON.stringify({
              type: 'hello',
              taskId: taskId,
              timestamp: Date.now()
            }));
          } catch (e) {
            console.error('发送测试消息失败', e);
          }
        };
        
        socket.onmessage = (event) => {
          console.log('收到WebSocket消息:', event.data);
          
          // 尝试解析JSON消息
          try {
            const data = JSON.parse(event.data);
            
            // 处理不同类型的消息
            if (data.type === 'heartbeat') {
              // 心跳消息，不需要显示在日志中
              console.log('收到心跳消息:', data);
              return;
            } else if (data.type === 'log') {
              // 日志消息
              addLog(data.message || '');
              updateProgressFromLog(data.message || '');
            } else if (data.type === 'status') {
              // 状态消息
              const statusMessage = data.message === 'undefined' || data.message === undefined
                ? (data.status === 'running' ? '正在处理中...' : '')
                : data.message;
              
              addLog(`状态更新: ${data.status} - ${statusMessage}`);
              
              // 如果任务完成或失败，更新状态
              if (data.status === 'completed') {
                setProgress(100);
                
                // 处理下载链接
                if (data.download_url) {
                  // 构建完整的下载链接
                  const isDirectDownloadLink = data.download_url.startsWith('/download');
                  const backendHost = API_BASE_URL.replace(/\/api$/, '');
                  const fullDownloadUrl = data.download_url.startsWith('http')
                    ? data.download_url
                    : isDirectDownloadLink
                      ? `${backendHost}${data.download_url}`
                      : `${API_BASE_URL}${data.download_url.startsWith('/') ? '' : '/'}${data.download_url}`;
                  
                  // 只有在没有下载链接时才设置
                  if (!downloadUrl) {
                    setDownloadUrl(fullDownloadUrl);
                    addLog(`处理完成！下载链接已生成`);
                  }
                } else if (!downloadUrl) {
                  // 如果没有下载链接，只添加一次完成消息
                  addLog(`处理完成！但未找到下载链接`);
                }
                
                setIsLoading(false);
              } else if (data.status === 'failed') {
                setError(statusMessage || '处理失败');
                addLog(`处理失败: ${statusMessage}`);
                setIsLoading(false);
              }
            } else if (data.type === 'task_update') {
              // 处理任务状态更新消息
              const task = data.task;
              if (task) {
                addLog(`任务状态更新: ${task.status}`);
                
                if (task.status === 'completed') {
                  setProgress(100);
                  
                  if (task.result && task.result.download_url) {
                    const isDirectDownloadLink = task.result.download_url.startsWith('/download');
                    const backendHost = API_BASE_URL.replace(/\/api$/, '');
                    const fullDownloadUrl = task.result.download_url.startsWith('http')
                      ? task.result.download_url
                      : isDirectDownloadLink
                        ? `${backendHost}${task.result.download_url}`
                        : `${API_BASE_URL}${task.result.download_url.startsWith('/') ? '' : '/'}${task.result.download_url}`;
                    
                    if (!downloadUrl) {
                      setDownloadUrl(fullDownloadUrl);
                      addLog(`处理完成！下载链接已生成`);
                    }
                  } else if (!downloadUrl) {
                    addLog(`处理完成！但未找到下载链接`);
                  }
                  
                  setIsLoading(false);
                } else if (task.status === 'failed') {
                  setError(task.error || '处理失败');
                  addLog(`处理失败: ${task.error || '未知错误'}`);
                  setIsLoading(false);
                }
              }
            } else {
              // 其他类型的消息，直接显示
              addLog(event.data);
              updateProgressFromLog(event.data);
            }
          } catch (e) {
            // 不是JSON格式，直接显示原始消息
            addLog(event.data);
            updateProgressFromLog(event.data);
          }
        };
        
        socket.onclose = (event) => {
          console.log('WebSocket连接已关闭', event.code, event.reason);
          
          // 如果不是正常关闭且未达到最大重连次数，尝试重连
          if (!event.wasClean && reconnectAttempts < maxReconnectAttempts) {
            reconnectAttempts++;
            const delay = reconnectInterval * Math.pow(1.5, reconnectAttempts - 1); // 指数退避
            addLog(`WebSocket连接断开，${delay/1000}秒后尝试重连 (${reconnectAttempts}/${maxReconnectAttempts})...`);
            
            reconnectTimer = setTimeout(() => {
              addLog(`正在尝试重新连接WebSocket (${reconnectAttempts}/${maxReconnectAttempts})...`);
              createWebSocketConnection();
            }, delay);
          } else {
            if (event.wasClean) {
              addLog(`WebSocket连接已正常关闭. 代码: ${event.code}`);
              
              // WebSocket正常关闭时，立即检查一次任务状态，确保获取最终结果
              setTimeout(async () => {
                try {
                  console.log('WebSocket已关闭，正在获取最终任务状态(只读操作):', taskId);
                  
                  // 使用飞书API获取任务状态（因为这是format-prd任务）
                  const taskStatus = await fetchWithRetry(
                    () => documentApi.getFeishuTaskStatus(taskId),
                    3, // 最多重试3次
                    2000 // 初始延迟2秒
                  );
                  console.log('获取到最终任务状态(只读):', taskStatus);
                  
                  // 处理任务完成状态
                  if (taskStatus.status === 'completed') {
                    setProgress(100);
                    setIsLoading(false);
                    
                    // 处理下载链接
                    if (taskStatus.result && taskStatus.result.download_url) {
                      const downloadPath = taskStatus.result.download_url;
                      console.log('发现下载链接:', downloadPath);
                      
                      // 构建完整下载链接
                      const isDirectDownloadLink = downloadPath.startsWith('/download');
                      const backendHost = API_BASE_URL.replace(/\/api$/, '');
                      const fullDownloadUrl = downloadPath.startsWith('http')
                        ? downloadPath
                        : isDirectDownloadLink
                          ? `${backendHost}${downloadPath}`
                          : `${API_BASE_URL}${downloadPath.startsWith('/') ? '' : '/'}${downloadPath}`;
                      
                      setDownloadUrl(fullDownloadUrl);
                      addLog(`处理完成！下载链接已生成`);
                      console.log('构建的完整下载链接:', fullDownloadUrl);
                    } else {
                      addLog(`处理完成！但未找到下载链接`);
                    }
                  } else if (taskStatus.status === 'failed') {
                    setError(taskStatus.error || '处理失败');
                    addLog(`处理失败: ${taskStatus.error || '未知错误'}`);
                    setIsLoading(false);
                  }
                } catch (error) {
                  console.error('获取最终任务状态失败:', error);
                }
              }, 500); // 延迟500ms，确保WebSocket完全关闭
            } else {
              addLog(`WebSocket连接已异常关闭. 代码: ${event.code}, 已达到最大重连次数`);
              
              // 即使异常关闭，也尝试获取一次最终状态
              setTimeout(async () => {
                try {
                  console.log('WebSocket异常关闭，正在获取最终任务状态(只读操作):', taskId);
                  
                  // 使用飞书API获取任务状态（因为这是format-prd任务）
                  const taskStatus = await fetchWithRetry(
                    () => documentApi.getFeishuTaskStatus(taskId),
                    3, // 最多重试3次
                    2000 // 初始延迟2秒
                  );
                  console.log('异常关闭后获取的任务状态(只读):', taskStatus);
                  
                  if (taskStatus.status === 'completed' || taskStatus.status === 'failed') {
                    // 处理任务完成或失败状态
                    if (taskStatus.status === 'completed') {
                      setProgress(100);
                      setIsLoading(false);
                      
                      // 处理下载链接
                      if (taskStatus.result && taskStatus.result.download_url) {
                        const downloadPath = taskStatus.result.download_url;
                        console.log('发现下载链接:', downloadPath);
                        
                        // 构建完整下载链接
                        const isDirectDownloadLink = downloadPath.startsWith('/download');
                        const backendHost = API_BASE_URL.replace(/\/api$/, '');
                        const fullDownloadUrl = downloadPath.startsWith('http')
                          ? downloadPath
                          : isDirectDownloadLink
                            ? `${backendHost}${downloadPath}`
                            : `${API_BASE_URL}${downloadPath.startsWith('/') ? '' : '/'}${downloadPath}`;
                        
                        setDownloadUrl(fullDownloadUrl);
                        addLog(`处理完成！下载链接已生成`);
                        console.log('构建的完整下载链接:', fullDownloadUrl);
                      } else {
                        addLog(`处理完成！但未找到下载链接`);
                      }
                    } else if (taskStatus.status === 'failed') {
                      setError(taskStatus.error || '处理失败');
                      addLog(`处理失败: ${taskStatus.error || '未知错误'}`);
                      setIsLoading(false);
                    }
                  }
                } catch (error) {
                  console.error('异常关闭后获取任务状态失败:', error);
                }
              }, 1000);
            }
          }
        };
        
        socket.onerror = (error) => {
          console.error('WebSocket错误:', error);
          addLog(`WebSocket错误: ${error}`);
        };
        
        return socket;
      };
      
      // 创建初始连接
      const socket = createWebSocketConnection();
      
      // 心跳检测
      const pingInterval = setInterval(() => {
        if (socket.readyState === WebSocket.OPEN) {
          try {
            socket.send(JSON.stringify({ type: 'ping', timestamp: Date.now() }));
            console.log('发送心跳包');
          } catch (e) {
            console.error('发送心跳包失败', e);
          }
        }
      }, 30000);
      
      // 添加重试功能的辅助函数
      // 添加重试功能的辅助函数 - 使用逗号避免JSX解析问题
      const fetchWithRetry = async <T,>(fn: () => Promise<T>, maxRetries = 3, retryDelay = 2000): Promise<T> => {
        let lastError;
        
        for (let attempt = 0; attempt < maxRetries; attempt++) {
          try {
            console.log(`尝试执行操作 (尝试 ${attempt + 1}/${maxRetries})`);
            const result = await fn();
            return result; // 成功则返回结果
          } catch (error) {
            console.error(`操作失败 (尝试 ${attempt + 1}/${maxRetries}):`, error);
            lastError = error;
            
            if (attempt < maxRetries - 1) {
              // 如果不是最后一次尝试，则等待后重试
              console.log(`等待 ${retryDelay}ms 后重试...`);
              await new Promise(resolve => setTimeout(resolve, retryDelay));
              // 每次重试增加延迟时间
              retryDelay = retryDelay * 1.5;
            }
          }
        }
        
        // 所有重试都失败，抛出最后一个错误
        throw lastError;
      };
      
      const checkStatusAndOutput = async () => {
        try {
          // 添加更详细的日志，确保只是获取状态，不会触发新的处理
          console.log('正在获取任务状态(只读操作):', taskId);
          
          // 使用飞书API获取任务状态（因为这是format-prd任务）
          const taskStatus = await documentApi.getFeishuTaskStatus(taskId);
          console.log('获取到任务状态(只读):', taskStatus);
          
          // 处理下载链接
          try {
            // 检查 result 字段中的下载链接 (新的API格式)
            if (taskStatus.result && taskStatus.result.download_url) {
              const downloadPath = taskStatus.result.download_url;
              console.log('发现结果中的下载链接:', downloadPath);
              
              // 构建完整下载链接
              const isDirectDownloadLink = downloadPath.startsWith('/download');
              const backendHost = API_BASE_URL.replace(/\/api$/, '');
              const fullDownloadUrl = downloadPath.startsWith('http')
                ? downloadPath
                : isDirectDownloadLink
                  ? `${backendHost}${downloadPath}`
                  : `${API_BASE_URL}${downloadPath.startsWith('/') ? '' : '/'}${downloadPath}`;
              
              setDownloadUrl(fullDownloadUrl);
              console.log('构建的完整下载链接:', fullDownloadUrl);
              
              // 验证下载链接是否有效
              fetch(fullDownloadUrl, { method: 'HEAD' })
                .then(response => {
                  if (!response.ok) {
                    console.warn('下载链接可能无效:', fullDownloadUrl, response.status);
                    addLog(`警告: 下载链接可能无效 (${response.status})`);
                  } else {
                    console.log('下载链接有效:', fullDownloadUrl);
                  }
                })
                .catch(err => {
                  console.warn('验证下载链接失败:', err);
                });
            }
            // 优先使用API下载URL (如果存在)
            else if (taskStatus.api_download_url) {
              console.log('发现API下载链接:', taskStatus.api_download_url);
              const apiDownloadUrl = `${API_BASE_URL.replace(/\/api$/, '')}${taskStatus.api_download_url}`;
              setDownloadUrl(apiDownloadUrl);
              console.log('构建的API下载链接:', apiDownloadUrl);
            }
            // 否则使用常规下载URL
            else if (taskStatus.download_url) {
              console.log('发现下载链接:', taskStatus.download_url);
              const isDirectDownloadLink = taskStatus.download_url.startsWith('/download');
              
              // 为直接下载链接使用固定的后端地址
              const backendHost = API_BASE_URL.replace(/\/api$/, '');
              
              const fullDownloadUrl = taskStatus.download_url.startsWith('http')
                ? taskStatus.download_url
                : isDirectDownloadLink
                  ? `${backendHost}${taskStatus.download_url}`
                  : `${API_BASE_URL}${taskStatus.download_url.startsWith('/') ? '' : '/'}${taskStatus.download_url}`;
              
              setDownloadUrl(fullDownloadUrl);
              console.log('构建的完整下载链接:', fullDownloadUrl);
              
              // 验证下载链接是否有效
              fetch(fullDownloadUrl, { method: 'HEAD' })
                .then(response => {
                  if (!response.ok) {
                    console.warn('下载链接可能无效:', fullDownloadUrl, response.status);
                    addLog(`警告: 下载链接可能无效 (${response.status})`);
                  } else {
                    console.log('下载链接有效:', fullDownloadUrl);
                  }
                })
                .catch(err => {
                  console.warn('验证下载链接失败:', err);
                });
            }
          } catch (error) {
            console.error('处理下载链接时出错:', error);
            addLog(`处理下载链接时出错: ${error.message || '未知错误'}`);
          }
          
          // 检查任务是否已完成
          if (taskStatus.status === 'completed' || taskStatus.status === 'failed') {
            // 关闭WebSocket连接，避免重复消息
            if (socket.readyState === WebSocket.OPEN) {
              socket.close(1000, "任务已完成，正常关闭");
            }
            
            // 更新状态，但不重复添加日志
            setIsLoading(false);
            
            // 如果是失败状态，设置错误信息
            if (taskStatus.status === 'failed') {
              setError(taskStatus.message || '处理失败');
            }
            
            return false;
          }
          
          // 处理任务状态消息
          let statusMessage = taskStatus.message || '';
          if (statusMessage === 'undefined' || statusMessage === undefined) {
            statusMessage = taskStatus.status === 'running' ? '正在处理中...' : '';
          }
          
          addLog(`当前状态: ${taskStatus.status} - ${statusMessage}`);
          return true;
        } catch (error) {
          console.error('获取任务状态失败:', error);
          addLog(`网络连接异常，继续尝试获取任务状态...`);
          // 不要因为网络错误就停止轮询，继续尝试
          console.log('网络错误，继续轮询...');
          return true; // 继续轮询
        }
      };
      
      // 记录开始检查状态
      console.log('开始检查任务状态(只读操作)');
      
      // 立即检查一次状态
      checkStatusAndOutput();
      
      // 设置定时检查，使用更合理的间隔时间
      const checkInterval = setInterval(async () => {
        console.log('定时检查任务状态(只读操作):', taskId);
        const shouldContinue = await checkStatusAndOutput();
        if (!shouldContinue) {
          console.log('任务已完成或失败，停止定时检查');
          clearInterval(checkInterval);
          clearInterval(pingInterval);
        }
      }, 3000); // 每3秒检查一次，更频繁地检查任务状态
      
      return () => {
        clearInterval(checkInterval);
        clearInterval(pingInterval);
        
        // 清除重连定时器
        if (reconnectTimer) {
          clearTimeout(reconnectTimer);
        }
        
        // 关闭WebSocket连接
        if (socket && socket.readyState === WebSocket.OPEN) {
          socket.close(1000, "组件卸载，正常关闭");
        }
        
        wsRef.current = null;
      };
    }
  }, [taskId, router]);
  
  const addLog = (message: string) => {
    setLogs((prevLogs) => [...prevLogs, message]);
  };
  
  const updateProgressFromLog = (logText: string) => {
    // 如果日志中包含"处理完成"或"生成文件"，设置进度为100%
    if (logText.includes('处理完成') || logText.includes('生成文件')) {
      setProgress(100);
      return;
    }
    
    // 如果日志中包含"失败"或"错误"，保持当前进度，不更新
    if (logText.includes('失败') || logText.includes('错误')) {
      return;
    }
    
    // 尝试从日志文本中解析进度信息 - 格式1: 进度: [1/10]
    const progressMatch1 = /进度: \[([\d]+)\/([\d]+)\]/.exec(logText);
    if (progressMatch1) {
      const current = parseInt(progressMatch1[1]);
      const total = parseInt(progressMatch1[2]);
      const progressPercent = Math.round((current / total) * 100);
      setProgress(progressPercent);
      return;
    }
    
    // 尝试从日志文本中解析进度信息 - 格式2: 进度: 50%
    const progressMatch2 = /进度: (\d+)%/.exec(logText);
    if (progressMatch2) {
      const progressPercent = parseInt(progressMatch2[1]);
      setProgress(progressPercent);
      return;
    }
    
    // 尝试从日志文本中解析进度信息 - 格式3: 完成度: 50%
    const progressMatch3 = /完成度: (\d+)%/.exec(logText);
    if (progressMatch3) {
      const progressPercent = parseInt(progressMatch3[1]);
      setProgress(progressPercent);
      return;
    }
    
    // 根据关键词估算进度
    if (logText.includes('开始处理')) {
      setProgress(10);
    } else if (logText.includes('获取原始文档内容')) {
      setProgress(20);
    } else if (logText.includes('成功获取原始文档内容')) {
      setProgress(30);
    } else if (logText.includes('读取模板和提示词')) {
      setProgress(40);
    } else if (logText.includes('组装完整Prompt')) {
      setProgress(50);
    } else if (logText.includes('调用大模型API')) {
      setProgress(60);
    } else if (logText.includes('处理大模型返回结果')) {
      setProgress(80);
    } else if (logText.includes('生成文件')) {
      setProgress(90);
    }
  };
  
  // 验证URL格式
  const validateUrl = (url: string): boolean => {
    if (!url) return false;
    
    try {
      // 检查URL是否包含中文字符
      const hasChinese = /[\u4e00-\u9fa5]/.test(url);
      
      // 尝试创建URL对象验证格式
      new URL(url);
      
      // 如果包含中文字符，添加提示信息但不阻止提交
      if (hasChinese) {
        console.log('URL包含中文字符，但格式有效');
        setUrlError('');
      } else {
        setUrlError('');
      }
      
      return true;
    } catch (error) {
      console.error('URL验证错误:', error);
      setUrlError('URL格式无效，请检查');
      return false;
    }
  };
  
  const handleFormSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    // 确认是表单提交事件，而不是其他事件
    console.log('表单提交事件触发，开始处理');
    
    // 记录用户操作
    console.log('用户操作: 提交表单开始处理', {
      activeTab,
      mdUrl: activeTab === 'url' ? mdUrl : '(文件上传)',
      prdType
    });
    
    setIsLoading(true);
    setError('');
    setLogs([]);
    setShowLogs(false);
    setDownloadUrl('');
    setProgress(0);
    
    try {
      let result;
      if (activeTab === 'url') {
        if (!mdUrl) {
          throw new Error('请输入Markdown文档链接');
        }
        
        // 验证URL
        if (!validateUrl(mdUrl)) {
          setIsLoading(false);
          throw new Error(urlError || 'Markdown文档链接格式不正确');
        }
        
        // 明确记录API调用
        console.log('调用API: processAiPrd', { url: mdUrl, type: prdType });
        
        result = await documentApi.processAiPrd({
          url: mdUrl,
          replace_images: true,
          output_format: 'markdown',
          type: prdType, // 添加 type 参数
          // 传递工作流参数给后端
          workflow_params: workflowData,
          // 添加页面中的所有表单数据
          form_data: {
            feishu_url: searchParams.get('feishu_url') || '', // ai-prd接口需要feishu_url参数
            format_prd_url: searchParams.get('format_prd_url') || mdUrl, // ai-prd接口需要format_prd_url参数
            md_url: mdUrl,
            name: name,
            description: description,
            prd_type: prdType,
            active_tab: activeTab
          }
        });
        
        setTaskId(result.task_id);
        setShowLogs(true);
        addLog(`开始处理Markdown文档: ${mdUrl}`);
        addLog(`PRD类型: ${prdType === 'fe' ? '前端' : '后端'}`); // 添加 PRD 类型日志
        addLog(`任务ID: ${result.task_id}`);
        // 设置初始进度为50%
        setProgress(50);
      } else if (activeTab === 'upload') {
        if (!selectedFile) {
          throw new Error('请选择要上传的文件');
        }
        
        const formData = new FormData();
        formData.append('file', selectedFile);
        formData.append('replace_images', 'true');
        formData.append('output_format', 'markdown');
        formData.append('name', name || `${selectedFile.name}-AI-PRD`);
        formData.append('description', description || '由Markdown文档自动生成AI-PRD');
        formData.append('type', prdType); // 添加 type 参数
        
        // 添加工作流参数
        formData.append('workflow_params', JSON.stringify(workflowData));
        
        // 添加页面中的所有表单数据
        formData.append('form_data', JSON.stringify({
          file_name: selectedFile.name,
          name: name,
          description: description,
          prd_type: prdType,
          active_tab: activeTab
        }));
        
        // 明确记录API调用
        console.log('调用API: uploadAndProcessAiPrd', {
          fileName: selectedFile.name,
          fileSize: selectedFile.size,
          type: prdType
        });
        
        result = await documentApi.uploadAndProcessAiPrd(formData);
        
        setTaskId(result.task_id);
        setShowLogs(true);
        addLog(`文件上传和处理任务已提交，任务ID: ${result.task_id}`);
        addLog(`PRD类型: ${prdType === 'fe' ? '前端' : '后端'}`); // 添加 PRD 类型日志
        setProgress(50);
      }
    } catch (error) {
      console.error('提交失败:', error);
      
      // 提供更友好的错误提示
      let errorMessage = '';
      
      if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        // 根据错误消息提供更具体的提示
        if (error.message.includes('Network Error') || error.message.includes('timeout')) {
          errorMessage = '网络连接错误，请检查您的网络连接并重试';
        } else if (error.message.includes('404')) {
          errorMessage = '请求的资源不存在，请检查URL是否正确';
        } else if (error.message.includes('403')) {
          errorMessage = '没有权限访问该资源，请检查您的权限';
        } else if (error.message.includes('500')) {
          errorMessage = '服务器内部错误，请稍后重试';
        } else if (error.message.includes('401')) {
          errorMessage = '未授权访问，请重新登录';
        } else {
          errorMessage = error.message;
        }
      } else {
        errorMessage = '提交失败，请重试';
      }
      
      // 如果URL包含中文字符，添加提示
      if (activeTab === 'url' && /[\u4e00-\u9fa5]/.test(mdUrl)) {
        errorMessage += '。注意：URL中包含中文字符，可能导致解析错误';
      }
      
      setError(errorMessage);
      setIsLoading(false);
      setShowLogs(true); // 显示日志，方便用户查看错误信息
      addLog(`处理失败: ${errorMessage}`);
    }
  };
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };
  
  const handleCopyLogs = () => {
    const logText = logs.join('\n');
    
    try {
      // 尝试使用现代Clipboard API
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(logText)
          .then(() => {
            console.log('Logs copied to clipboard');
            toast({
              title: "日志已复制",
              description: "日志内容已成功复制到剪贴板。",
              status: "success",
              duration: 3000,
              isClosable: true,
            });
          })
          .catch(err => {
            console.error('使用Clipboard API复制失败: ', err);
            // 使用回退方法
            fallbackCopyTextToClipboard(logText);
          });
      } else {
        // 浏览器不支持Clipboard API，使用回退方法
        fallbackCopyTextToClipboard(logText);
      }
    } catch (err) {
      console.error('复制过程中出错: ', err);
      // 使用回退方法
      fallbackCopyTextToClipboard(logText);
    }
    
    // 回退复制方法
    function fallbackCopyTextToClipboard(text) {
      try {
        // 创建临时文本区域
        const textArea = document.createElement('textarea');
        textArea.value = text;
        
        // 避免滚动到底部
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        // 尝试复制文本
        const successful = document.execCommand('copy');
        
        // 移除临时元素
        document.body.removeChild(textArea);
        
        if (successful) {
          toast({
            title: "日志已复制",
            description: "日志内容已成功复制到剪贴板。",
            status: "success",
            duration: 3000,
            isClosable: true,
          });
        } else {
          throw new Error('复制命令执行失败');
        }
      } catch (err) {
        console.error('回退复制方法失败: ', err);
        toast({
          title: "复制失败",
          description: "无法将日志复制到剪贴板，请手动复制",
          status: "error",
          duration: 3000,
          isClosable: true,
        });
      }
    }
  };
  
  return (
    <WorkflowPageWrapper>
      <MainLayout>
      {/* 隐藏input元素用于存储工作流数据 */}
      {getHiddenInputsProps().map((props) => (
        <input {...props} />
      ))}
      <div className="flex justify-center min-h-screen">
        <div className="flex max-w-6xl w-full">
          {/* 左侧进度菜单 */}
          <WorkflowSidebar className="w-64 flex-shrink-0" />
          
          {/* 主内容区域 */}
          <div className="flex-1">
            <div className="p-6">
              <h1 className="text-3xl font-bold mb-8 border-none">生成AI-PRD</h1>
              <div className="max-w-3xl mx-auto">
          <div className="flex border-b mb-6">
            <button
              type="button"
              className={`px-4 py-2 font-medium ${activeTab === 'url' ? 'border-b-2 border-green-500 text-green-600' : 'text-gray-500 hover:text-green-600'}`}
              onClick={() => setActiveTab('url')}
            >
              Markdown文档链接
            </button>
            <button
              type="button"
              className="px-4 py-2 font-medium text-gray-400 cursor-not-allowed"
              onClick={() => {
                toast({
                  title: "暂不支持",
                  description: "上传功能暂不支持，请使用Markdown文档链接",
                  status: "warning",
                  duration: 3000,
                  isClosable: true,
                });
              }}
            >
              上传Markdown文档
            </button>
          </div>
          <div className="bg-white p-6 rounded-lg shadow border border-gray-200">
            <div className="mb-6">
              <h2 className="text-xl font-bold mb-1">生成AI增强的产品需求文档</h2>
              <p className="text-gray-600 text-sm">通过Markdown文档自动生成AI增强的产品需求文档</p>
            </div>
            {error && (
              <Alert status="error" mb={4} borderRadius="md" variant="subtle">
                <AlertIcon />
                <Box flex="1">
                  <AlertTitle fontWeight="semibold">发生错误:</AlertTitle>
                  <AlertDescription>{error}</AlertDescription>
                </Box>
              </Alert>
            )}
            <form onSubmit={handleFormSubmit}>
              {activeTab === 'url' && (
                <>
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="md-url">
                      *<span className="font-bold">Markdown文档链接</span> <span className="text-red-400 font-normal">(必填)</span>
                    </label>
                    <Input
                      id="md-url"
                      name="url"
                      type="text"
                      value={mdUrl}
                      onChange={(e) => {
                        setMdUrl(e.target.value);
                        if (urlError) validateUrl(e.target.value);
                      }}
                      placeholder="粘贴Markdown文档链接"
                      required
                      isDisabled={isLoading}
                      width="100%"
                      px={3}
                      py={2}
                      borderColor={urlError ? "red.300" : "gray.300"}
                      borderRadius="md"
                      boxShadow="sm"
                      _focus={{ outline: "none", ring: "2px", ringColor: urlError ? "red.500" : "green.500", borderColor: urlError ? "red.500" : "green.500" }}
                    />
                    {urlError ? (
                      <p className="text-xs text-red-500 mt-1">{urlError}</p>
                    ) : (
                      <p className="text-xs text-gray-500 mt-1">
                        请输入可访问的Markdown文档链接 <br />
                        示例：http://172.16.31.251:8000/download/format-prd-S3C9wkR7NinQM2kfcKfck9uwnLe.md
                      
                      </p>
                    )}
                  </div>
                  
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      *<span className="font-bold">生成 PRD 类型</span> <span className="text-red-400 font-normal">(必填)</span>
                    </label>
                    <div className="flex space-x-4 mt-2">
                      <div className="flex items-center">
                        <input
                          id="prd-type-fe"
                          name="prd-type"
                          type="radio"
                          value="fe"
                          checked={prdType === 'fe'}
                          onChange={() => setPrdType('fe')}
                          disabled={isLoading}
                          className="h-4 w-4 text-green-600 border-gray-300 focus:ring-green-500"
                        />
                        <label htmlFor="prd-type-fe" className="ml-2 block text-sm text-gray-700">
                          前端 AI-PRD
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="prd-type-be"
                          name="prd-type"
                          type="radio"
                          value="be"
                          checked={prdType === 'be'}
                          onChange={() => setPrdType('be')}
                          disabled={isLoading}
                          className="h-4 w-4 text-green-600 border-gray-300 focus:ring-green-500"
                        />
                        <label htmlFor="prd-type-be" className="ml-2 block text-sm text-gray-700">
                          后端 AI-PRD
                        </label>
                      </div>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      选择生成前端或后端 AI-PRD 文档
                    </p>
                  </div>
                </>
              )}
              {activeTab === 'upload' && (
                <>
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="file">
                      上传Markdown文档
                    </label>
                  <div className="border border-gray-300 rounded-md p-3">
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                      <div className="mt-4 flex text-sm justify-center leading-6 text-gray-600">
                        <label
                          htmlFor="file-upload"
                          className={`relative cursor-pointer rounded-md bg-white font-semibold text-green-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-green-500 focus-within:ring-offset-2 hover:text-green-500 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                        >
                          <span>上传文件</span>
                          <input
                            id="file-upload"
                            name="file"
                            type="file"
                            accept=".md"
                            className="sr-only"
                            onChange={handleFileChange}
                            required={activeTab === 'upload'}
                            disabled={isLoading}
                          />
                        </label>
                        <p className="pl-1">或拖拽到此处</p>
                      </div>
                      <p className="text-xs leading-5 text-gray-600 mt-2">仅支持 Markdown (.md) 文件 (最大20MB)</p>
                      {selectedFile && (
                        <div className="mt-4 text-sm text-gray-700">
                          已选择: {selectedFile.name}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    *<span className="font-bold">PRD 类型</span> <span className="text-red-400 font-normal">(必填)</span>
                  </label>
                  <div className="flex space-x-4 mt-2">
                    <div className="flex items-center">
                      <input
                        id="prd-type-fe-upload"
                        name="prd-type-upload"
                        type="radio"
                        value="fe"
                        checked={prdType === 'fe'}
                        onChange={() => setPrdType('fe')}
                        disabled={isLoading}
                        className="h-4 w-4 text-green-600 border-gray-300 focus:ring-green-500"
                      />
                      <label htmlFor="prd-type-fe-upload" className="ml-2 block text-sm text-gray-700">
                        前端 PRD
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="prd-type-be-upload"
                        name="prd-type-upload"
                        type="radio"
                        value="be"
                        checked={prdType === 'be'}
                        onChange={() => setPrdType('be')}
                        disabled={isLoading}
                        className="h-4 w-4 text-green-600 border-gray-300 focus:ring-green-500"
                      />
                      <label htmlFor="prd-type-be-upload" className="ml-2 block text-sm text-gray-700">
                        后端 PRD
                      </label>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    选择生成前端或后端 PRD 文档
                  </p>
                </div>
              </>
              )}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="name">
                  工作流名称 <span className="text-gray-400 font-normal">(选填)</span>
                </label>
                <Input
                  id="name"
                  name="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="为此次处理命名，便于后续查找"
                  isDisabled={isLoading}
                  width="100%"
                  px={3}
                  py={2}
                  borderColor="gray.300"
                  borderRadius="md"
                  boxShadow="sm"
                  _focus={{ outline: "none", ring: "2px", ringColor: "green.500", borderColor: "green.500" }}
                />
              </div>
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-1" htmlFor="description">
                  描述 <span className="text-gray-400 font-normal">(选填)</span>
                </label>
                <Input
                  id="description"
                  name="description"
                  type="text"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="添加描述信息"
                  isDisabled={isLoading}
                  width="100%"
                  px={3}
                  py={2}
                  borderColor="gray.300"
                  borderRadius="md"
                  boxShadow="sm"
                  _focus={{ outline: "none", ring: "2px", ringColor: "green.500", borderColor: "green.500" }}
                />
              </div>
              {showLogs && (
                <div className="mb-6">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-md font-medium">处理进度</h3>
                    <Button
                      size="xs"
                      leftIcon={<CopyIcon />}
                      onClick={handleCopyLogs}
                      colorScheme="gray"
                      variant="outline"
                    >
                      复制日志
                    </Button>
                  </div>
                  <Progress 
                    value={progress} 
                    size="sm" 
                    colorScheme="green"
                    borderRadius="md" 
                    mb={2}
                  />
                  <p className="text-xs text-gray-500 mb-2">AI处理可能需要一些时间，请耐心等待。</p>
                  <div className="h-60 bg-gray-100 border border-gray-300 rounded-md p-3 overflow-y-auto font-mono text-sm">
                    {logs.map((log, index) => (
                      <div key={index} className="py-1 whitespace-pre-wrap break-all">
                        {log}
                      </div>
                    ))}
                    <div ref={logEndRef} />
                  </div>
                  {downloadUrl && (
                    <div className="mt-2">
                      <div className="flex space-x-4 mb-4 items-center mt-6">
                        <a
                          href={downloadUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-green-600 hover:text-green-800 hover:underline flex items-center"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                          </svg>
                          下载结果
                        </a>
                        <button
                          type="button" // 添加type="button"，确保不会触发表单提交
                          onClick={(e) => {
                            e.preventDefault(); // 阻止默认行为
                            e.stopPropagation(); // 阻止事件冒泡
                            console.log('预览文档，使用下载链接:', downloadUrl);
                            setIsPreviewOpen(true);
                          }}
                          className="text-green-600 hover:text-green-800 hover:underline flex items-center"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                          预览
                        </button>
                        <a
                          href={`/workflow/preview-md?url=${encodeURIComponent(downloadUrl)}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-purple-600 hover:text-purple-800 hover:underline flex items-center"
                        >
                          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                          </svg>
                          新窗口预览
                        </a>
                        
                        <Button
                          size="sm"
                          leftIcon={<CopyIcon />}
                          onClick={() => {
                            try {
                              // 尝试使用现代Clipboard API
                              if (navigator.clipboard && navigator.clipboard.writeText) {
                                navigator.clipboard.writeText(downloadUrl)
                                  .then(() => {
                                    toast({
                                      title: "链接已复制",
                                      description: "文档链接已成功复制到剪贴板",
                                      status: "success",
                                      duration: 3000,
                                      isClosable: true,
                                    });
                                  })
                                  .catch(err => {
                                    console.error('使用Clipboard API复制失败: ', err);
                                    // 使用回退方法
                                    fallbackCopyTextToClipboard(downloadUrl);
                                  });
                              } else {
                                // 浏览器不支持Clipboard API，使用回退方法
                                fallbackCopyTextToClipboard(downloadUrl);
                              }
                            } catch (err) {
                              console.error('复制过程中出错: ', err);
                              // 使用回退方法
                              fallbackCopyTextToClipboard(downloadUrl);
                            }

                            // 回退复制方法
                            function fallbackCopyTextToClipboard(text) {
                              try {
                                // 创建临时文本区域
                                const textArea = document.createElement('textarea');
                                textArea.value = text;
                                
                                // 避免滚动到底部
                                textArea.style.top = '0';
                                textArea.style.left = '0';
                                textArea.style.position = 'fixed';
                                textArea.style.opacity = '0';
                                
                                document.body.appendChild(textArea);
                                textArea.focus();
                                textArea.select();
                                
                                // 尝试复制文本
                                const successful = document.execCommand('copy');
                                
                                // 移除临时元素
                                document.body.removeChild(textArea);
                                
                                if (successful) {
                                  toast({
                                    title: "链接已复制",
                                    description: "文档链接已成功复制到剪贴板",
                                    status: "success",
                                    duration: 3000,
                                    isClosable: true,
                                  });
                                } else {
                                  throw new Error('复制命令执行失败');
                                }
                              } catch (err) {
                                console.error('回退复制方法失败: ', err);
                                toast({
                                  title: "复制失败",
                                  description: "无法将链接复制到剪贴板，请手动复制",
                                  status: "error",
                                  duration: 3000,
                                  isClosable: true,
                                });
                              }
                            }
                          }}
                          colorScheme="gray"
                          variant="outline"
                        >
                          复制链接
                        </Button>
                        
                        <Button
                          onClick={() => {
                            // 使用POST方式跳转，避免URL过长
                            navigateToNextPage('ai-td', downloadUrl, 'ai_prd_url');
                          }}
                          colorScheme="green"
                          size="sm"
                          className="bg-green-600 hover:bg-green-700"
                        >
                          进行AI-TD生成 →
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div className="flex justify-between items-center mt-8">
                <button
                    type="button"
                    onClick={() => {
                        setActiveTab('url');
                        setIsLoading(false);
                        setMdUrl('');
                        setName('');
                        setDescription('');
                        setSelectedFile(null);
                        setError('');
                        setTaskId('');
                        setLogs([]);
                        setShowLogs(false);
                        setProgress(0);
                        setDownloadUrl('');
                        setPrdType('fe'); // 重置为默认值 'fe'
                        if (wsRef.current) {
                            wsRef.current.close();
                            wsRef.current = null;
                        }
                    }}
                    className={`px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                    disabled={isLoading}
                >
                  取消/重置
                </button>
                <button
                  type="submit"
                  disabled={isLoading || (activeTab === 'url' && !mdUrl) || (activeTab === 'upload' && !selectedFile)}
                  className={`px-6 py-2 rounded-md text-white transition-colors duration-150 ${isLoading || (activeTab === 'url' && !mdUrl) || (activeTab === 'upload' && !selectedFile) ? 'bg-green-300 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700 focus:ring-4 focus:ring-green-300'}`}
                  onClick={(e) => {
                    // 添加额外的日志，确认是用户明确点击了"开始处理"按钮
                    console.log('用户明确点击了"开始处理"按钮');
                    // 表单提交会调用 handleFormSubmit 函数，不需要在这里调用
                  }}
                >
                  {isLoading ? (
                    <div className="flex items-center justify-center">
                      <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      处理中...
                    </div>
                  ) : '开始处理'}
                </button>
              </div>
            </form>
          </div>
          
          {/* Markdown 预览模态框 */}
          <MarkdownPreviewModal
            isOpen={isPreviewOpen}
            onClose={() => setIsPreviewOpen(false)}
            downloadUrl={downloadUrl}
            title="AI-PRD 预览"
          />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* 调试面板 - 通过URL参数debug=1控制显示 */}
      <WorkflowDebugPanel pageName="ai-prd" />
    </MainLayout>
    </WorkflowPageWrapper>
  );
}
#!/bin/bash

echo "🚀 启动测试环境 - Ubuntu 版本"
echo "=========================================="
echo ""
echo "配置信息："
echo "- 后端API: http://*************:4000"
echo "- 前端端口: 80"
echo "- 环境: test"
echo "=========================================="

# 清理缓存
echo ""
echo "🧹 正在清理缓存..."
if [ -d ".next" ]; then
    echo "🗑️  删除 .next 目录..."
    rm -rf .next
fi

if [ -d "node_modules/.cache" ]; then
    echo "🗑️  删除 node_modules/.cache 目录..."
    rm -rf node_modules/.cache
fi

echo "✅ 缓存清理完成！"

# 设置环境变量
export NODE_ENV=test
export NEXT_PUBLIC_API_HOST=http://*************:4000
export NEXT_PUBLIC_API_PATH=/api
export NEXT_PUBLIC_USE_REMOTE_API=true
export PORT=80

# 显示最终配置
echo ""
echo "🔧 环境变量已设置："
echo "  - NODE_ENV: $NODE_ENV"
echo "  - API_HOST: $NEXT_PUBLIC_API_HOST"
echo "  - API_PATH: $NEXT_PUBLIC_API_PATH"
echo "  - PORT: $PORT"

# 启动服务
echo ""
echo "🚀 正在启动测试服务器..."
echo "=========================================="
echo "访问地址: http://*************"
echo "API地址: $NEXT_PUBLIC_API_HOST$NEXT_PUBLIC_API_PATH"
echo "=========================================="

# 如果是80端口，使用sudo运行
if [ "$PORT" = "80" ]; then
    sudo -E npm run dev
else
    npm run dev
fi
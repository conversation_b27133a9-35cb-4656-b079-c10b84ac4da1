# AI课中课程框架+文档组件技术架构设计文档

## 版本信息

| 版本号 | 日期 | 作者 | 变更说明 |
| ------ | ---- | ---- | -------- |
| V0.9 | 2025-05-23 | AI架构师 | 初版设计 |

## 1. 引言 (Introduction)

### 1.1. 文档目的 (Purpose of Document)
为AI课中课程框架+文档组件系统提供清晰的服务端架构蓝图，指导核心模块/服务的设计与开发，确保系统满足关键的业务需求和质量属性。

### 1.2. 系统概述 (System Overview)
本系统是一个AI驱动的在线学习平台核心组件，主要目标是提供流畅的课程学习体验，包括开场页展示、文档组件交互播放、学习进度管理和掌握度计算等功能。主要交互方包括学生客户端、教师端、课程内容管理系统和学习数据分析系统。

### 1.3. 设计目标与核心原则 (Design Goals and Core Principles)
- **性能目标**：文档组件内容加载响应时间不超过2秒，支持1000个并发用户同时学习
- **可用性目标**：系统可用性达到99.9%，支持优雅降级和故障恢复
- **可扩展性目标**：支持水平扩展，模块间清晰解耦以提升开发效率和可维护性
- **设计原则**：单一职责原则、高内聚低耦合、领域驱动设计、事件驱动架构、API优先设计

### 1.4. 范围 (Scope)
**覆盖范围**：AI课程内容管理、学习进度跟踪、掌握度计算、文档组件播放控制、用户认证授权、课程反馈收集等核心服务端模块及其交互。

**不覆盖范围**：前端UI具体实现、详细的数据库表结构设计、第三方服务集成协议细节、DevOps的CI/CD流程、基础设施配置、安全攻防策略、数据库性能调优等。

### 1.5. 术语与缩写 (Glossary and Abbreviations)
- **跟随模式**：学生跟随老师讲解节奏进行学习的模式
- **自由模式**：学生可以自主控制学习进度的模式
- **勾画轨迹**：与音频内容同步播放的板书绘制过程
- **掌握度**：反映学生对知识点掌握程度的量化指标
- **IP动效**：课程中虚拟角色的动画效果

## 2. 需求提炼与架构驱动因素 (Key Requirements & Architectural Drivers)

### 2.1. 核心业务能力 (Core Business Capabilities)
- **课程内容管理**：开场页配置、文档组件内容、IP动效资源管理
- **学习进度跟踪**：学习轨迹记录、播放状态管理、模式切换控制
- **掌握度计算**：基于答题表现的掌握度策略计算和更新
- **交互控制**：双击播放暂停、倍速调整、快进快退、长按加速
- **学习反馈**：课程评价收集、学习报告生成、答题详情查看
- **用户认证**：身份验证、权限控制、会话管理

### 2.2. 关键质量属性 (Key Quality Attributes)

#### 2.2.1. 性能 (Performance)
- 文档组件内容加载响应时间不超过2秒
- 支持1000个并发用户，平均响应时间不超过3秒
- 架构通过异步处理、缓存策略和CDN加速支持性能目标

#### 2.2.2. 可用性 (Availability)
- 无状态服务设计支持水平扩展和故障转移
- 关键数据存储采用主从复制和备份策略
- 服务间采用熔断和重试机制保证可用性

#### 2.2.3. 可伸缩性 (Scalability)
- 服务采用无状态设计支持水平扩展
- 数据分片策略支持大规模用户和内容存储
- 消息队列支持异步处理和削峰填谷

#### 2.2.4. 可维护性 (Maintainability)
- 清晰的服务边界和职责划分
- 标准化的API接口和数据契约
- 完善的日志记录和监控体系

#### 2.2.5. 可测试性 (Testability)
- 服务间通过接口解耦支持独立测试
- 依赖注入设计支持Mock测试
- 事件驱动架构支持集成测试

### 2.3. 主要约束与依赖 (Key Constraints and Dependencies)
- **技术栈约束**：必须使用Go 1.24.3、Kratos 2.8.4、PostgreSQL 17、Redis 6.0.20、Kafka 2.6.2
- **外部服务依赖**：
  - 用户中心服务：提供用户认证，需要用户凭证，返回用户基本信息（UserID、Username、Roles）
  - 内容平台服务：提供题目信息，需要题目ID，返回题目内容、答案、解析
  - 教师服务端：提供教师信息，需要教师ID，返回教师基本信息和任教信息

## 3. 宏观架构设计 (High-Level Architectural Design)

### 3.1. 架构风格与模式 (Architectural Style and Patterns)
采用**微服务架构**结合**事件驱动架构**的混合模式。选择该风格的理由：
- 微服务架构支持服务独立部署和扩展，满足高可用性需求
- 事件驱动架构支持异步处理，提升系统性能和响应能力
- 清晰的服务边界便于团队协作和系统维护

### 3.2. 系统分层视图 (Layered View)

#### 3.2.1. 推荐的逻辑分层模型 (Recommended Logical Layering Model)

基于AI课中系统的业务复杂度和交互特性，设计五层架构模型：

```mermaid
flowchart TD
    subgraph 'AI课中学习系统架构'
        direction TB
        
        subgraph 'API接口层'
            direction LR
            HTTPGateway[HTTP网关]
            GRPCGateway[gRPC网关]
            AuthMiddleware[认证中间件]
        end
        
        subgraph '应用服务层'
            direction LR
            CourseApp[课程应用服务]
            StudyApp[学习应用服务]
            FeedbackApp[反馈应用服务]
        end
        
        subgraph '领域业务层'
            direction LR
            CourseDomain[课程领域服务]
            StudyDomain[学习领域服务]
            MasteryDomain[掌握度领域服务]
        end
        
        subgraph '数据访问层'
            direction LR
            CourseRepo[课程仓储]
            StudyRepo[学习仓储]
            CacheRepo[缓存仓储]
        end
        
        subgraph '基础设施层'
            direction LR
            Database[(PostgreSQL)]
            Cache[(Redis)]
            MessageQueue[Kafka]
            ExternalAPI[外部服务适配器]
        end
        
        %% 层间依赖关系
        HTTPGateway --> CourseApp
        GRPCGateway --> StudyApp
        AuthMiddleware --> CourseApp
        AuthMiddleware --> StudyApp
        
        CourseApp --> CourseDomain
        StudyApp --> StudyDomain
        FeedbackApp --> StudyDomain
        
        CourseDomain --> CourseRepo
        StudyDomain --> StudyRepo
        MasteryDomain --> StudyRepo
        
        CourseRepo --> Database
        StudyRepo --> Database
        CacheRepo --> Cache
        CourseRepo --> CacheRepo
        StudyRepo --> CacheRepo
        
        StudyDomain --> MessageQueue
        ExternalAPI --> Database
    end
    
    %% 外部服务
    UCenterService[用户中心服务]
    QuestionService[内容平台服务]
    TeacherService[教师服务端]
    
    ExternalAPI -.-> UCenterService
    ExternalAPI -.-> QuestionService
    ExternalAPI -.-> TeacherService
    
    %% 样式定义
    classDef apiStyle fill:#E8F6F3,stroke:#1ABC9C,stroke-width:2px;
    classDef appStyle fill:#EBF5FB,stroke:#3498DB,stroke-width:2px;
    classDef domainStyle fill:#F4ECF7,stroke:#9B59B6,stroke-width:2px;
    classDef dataStyle fill:#FEF9E7,stroke:#F39C12,stroke-width:2px;
    classDef infraStyle fill:#FADBD8,stroke:#E74C3C,stroke-width:2px;
    classDef externalStyle fill:#F8F9FA,stroke:#6C757D,stroke-width:2px;
    
    class HTTPGateway,GRPCGateway,AuthMiddleware apiStyle;
    class CourseApp,StudyApp,FeedbackApp appStyle;
    class CourseDomain,StudyDomain,MasteryDomain domainStyle;
    class CourseRepo,StudyRepo,CacheRepo dataStyle;
    class Database,Cache,MessageQueue,ExternalAPI infraStyle;
    class UCenterService,QuestionService,TeacherService externalStyle;
```

**各层职责定义**：

1. **API接口层**：负责外部请求接入、协议转换、认证授权和请求路由
   - 封装HTTP/gRPC协议差异，提供统一的服务访问入口
   - 实现认证中间件，验证用户身份和权限
   - 依赖规则：仅能调用应用服务层接口

2. **应用服务层**：负责业务流程编排、事务管理和服务协调
   - 处理复杂的业务用例，如课程学习流程、掌握度更新流程
   - 管理跨领域的事务一致性
   - 依赖规则：可调用领域业务层和数据访问层

3. **领域业务层**：负责核心业务逻辑、业务规则和领域模型
   - 实现课程播放控制、掌握度计算、学习进度管理等核心逻辑
   - 封装业务规则和约束条件
   - 依赖规则：可调用数据访问层，不能直接调用基础设施层

4. **数据访问层**：负责数据持久化、缓存管理和数据一致性
   - 提供统一的数据访问接口，屏蔽底层存储差异
   - 实现缓存策略和数据同步逻辑
   - 依赖规则：仅能调用基础设施层

5. **基础设施层**：负责技术基础设施、外部服务集成和系统资源管理
   - 提供数据库连接、缓存服务、消息队列等技术能力
   - 实现外部服务适配器和协议转换
   - 依赖规则：不依赖其他层，被其他层调用

**分层设计合理性论证**：
- 该五层架构清晰分离了技术关注点和业务关注点，符合关注点分离原则
- 严格的单向依赖避免了循环依赖，提升了系统的可测试性和可维护性
- 领域业务层的独立性确保了核心业务逻辑的稳定性和可复用性
- 应用服务层的编排能力支持复杂业务流程的灵活组合

### 3.3. 模块/服务划分视图 (Module/Service Decomposition View)

#### 3.3.1. 核心模块/服务识别与职责 (Identification and Responsibilities)

基于新增的服务架构，系统包含以下核心服务：

- **course-center（课程中心）**：负责AI课程内容管理，包括开场页配置、文档组件内容、IP动效资源、课程结构等静态内容
- **study-center（学习中心）**：负责学习记录管理和掌握度计算，包括学习轨迹、答题记录、播放状态、掌握度策略等动态数据
- **study-api（学习服务API）**：负责封装course-center和study-center的使用，提供统一的学习服务接口
- **ucenter（用户中心）**：负责用户认证、权限管理、学校和班级信息管理
- **question（内容平台）**：负责题目信息、题库管理、基础树和业务树管理
- **teacher（教师服务端）**：负责教师信息管理、课堂管理、任务布置等

#### 3.3.2. 模块/服务交互模式与数据契约 (Interaction Patterns and Data Contracts)

```mermaid
graph TB
    subgraph '客户端层'
        StudentClient[学生客户端]
        TeacherClient[教师客户端]
    end
    
    subgraph '网关层'
        APIGateway[API网关]
    end
    
    subgraph '核心业务服务'
        StudyAPI[study-api<br/>学习服务API]
        CourseCenter[course-center<br/>课程中心]
        StudyCenter[study-center<br/>学习中心]
    end
    
    subgraph '基础服务'
        UCenter[ucenter<br/>用户中心]
        Question[question<br/>内容平台]
        Teacher[teacher<br/>教师服务端]
    end
    
    subgraph '基础设施'
        PostgreSQL[(PostgreSQL)]
        Redis[(Redis)]
        Kafka[Kafka]
    end
    
    %% 客户端到网关
    StudentClient -- HTTP/gRPC --> APIGateway
    TeacherClient -- HTTP/gRPC --> APIGateway
    
    %% 网关到服务
    APIGateway -- HTTP/gRPC --> StudyAPI
    APIGateway -- HTTP/gRPC --> UCenter
    APIGateway -- HTTP/gRPC --> Teacher
    
    %% 核心服务间交互
    StudyAPI -- gRPC（课程内容查询） --> CourseCenter
    StudyAPI -- gRPC（学习记录管理） --> StudyCenter
    StudyAPI -- gRPC（用户验证） --> UCenter
    StudyAPI -- gRPC（题目信息） --> Question
    
    %% 异步事件交互
    StudyCenter -- Kafka（掌握度更新事件） --> StudyAPI
    StudyAPI -- Kafka（学习完成事件） --> Teacher
    
    %% 数据存储
    CourseCenter -.-> PostgreSQL
    StudyCenter -.-> PostgreSQL
    UCenter -.-> PostgreSQL
    Question -.-> PostgreSQL
    Teacher -.-> PostgreSQL
    
    StudyAPI -.-> Redis
    CourseCenter -.-> Redis
    StudyCenter -.-> Redis
    
    %% 样式
    classDef clientStyle fill:#E8F8F5,stroke:#27AE60,stroke-width:2px;
    classDef gatewayStyle fill:#EBF5FB,stroke:#3498DB,stroke-width:2px;
    classDef coreStyle fill:#F4ECF7,stroke:#9B59B6,stroke-width:2px;
    classDef baseStyle fill:#FEF9E7,stroke:#F39C12,stroke-width:2px;
    classDef infraStyle fill:#FADBD8,stroke:#E74C3C,stroke-width:2px;
    
    class StudentClient,TeacherClient clientStyle;
    class APIGateway gatewayStyle;
    class StudyAPI,CourseCenter,StudyCenter coreStyle;
    class UCenter,Question,Teacher baseStyle;
    class PostgreSQL,Redis,Kafka infraStyle;
```

**主要交互模式**：
- **同步RPC调用**：用于实时数据查询和状态更新，如课程内容获取、用户认证验证
- **异步事件消息**：用于非实时的状态通知和数据同步，如掌握度更新、学习完成通知
- **缓存策略**：热点数据通过Redis缓存提升访问性能

**关键数据契约**：
- **课程内容契约**：课程ID、组件类型、内容数据、播放配置
- **学习记录契约**：用户ID、课程ID、学习进度、播放状态、答题记录、勾画轨迹数据
- **掌握度契约**：用户ID、知识点ID、掌握度值、计算时间、策略版本
- **用户认证契约**：用户凭证、用户基本信息、权限角色

### 3.4. 业务流程的高层视图 (High-Level View of Business Flows)

#### 3.4.1. 学生首次进入AI课程学习流程

```mermaid
sequenceDiagram
    actor Student as 学生
    participant API as study-api
    participant Auth as ucenter
    participant Course as course-center
    participant Study as study-center
    
    Student->>+API: 请求进入课程（课程ID，用户凭证）
    API->>+Auth: 验证用户身份
    Auth-->>-API: 返回用户信息（用户ID，权限）
    
    API->>+Course: 获取课程开场页内容
    Course-->>-API: 返回开场页配置（IP动效，音频，课程信息）
    
    API->>+Study: 初始化学习记录
    Study-->>-API: 返回学习会话ID
    
    API-->>-Student: 返回开场页内容和学习会话
    
    Note over Student: 观看开场页IP动效+音频
    
    Student->>+API: 请求进入第一小节
    API->>+Course: 获取第一小节内容
    Course-->>-API: 返回文档组件内容
    
    API->>+Study: 记录学习开始
    Study-->>-API: 确认记录成功
    
    API-->>-Student: 返回第一小节内容
```

#### 3.4.2. 文档组件交互学习流程

```mermaid
sequenceDiagram
    actor Student as 学生
    participant API as study-api
    participant Study as study-center
    participant Course as course-center
    
    Note over Student: 跟随模式观看内容
    Student->>+API: 滑动内容（切换到自由模式）
    API->>+Study: 更新播放状态（暂停，自由模式）
    Study-->>-API: 确认状态更新
    API-->>-Student: 返回自由模式界面
    
    Note over Student: 双击屏幕
    Student->>+API: 双击播放控制
    API->>+Study: 切换播放状态（播放/暂停）
    Study-->>-API: 返回新的播放状态
    API-->>-Student: 更新播放状态
    
    Note over Student: 长按文档外区域
    Student->>+API: 长按加速（3倍速）
    API->>+Study: 设置播放倍速（3x）
    Study-->>-API: 确认倍速设置
    API-->>-Student: 开始3倍速播放
    
    Note over Student: 点击"跟随老师"
    Student->>+API: 切换到跟随模式
    API->>+Study: 更新播放状态（跟随模式，正常倍速）
    Study-->>-API: 确认状态更新
    API->>+Study: 获取当前播放位置的勾画轨迹
    Study-->>-API: 返回同步轨迹数据
    API-->>-Student: 恢复跟随模式播放
```

#### 3.4.3. 课程完成和掌握度更新流程

```mermaid
sequenceDiagram
    actor Student as 学生
    participant API as study-api
    participant Study as study-center
    participant Course as course-center
    participant Kafka as 消息队列

    Student->>API: 完成最后一个组件
    API->>Study: 记录组件完成
    Study->>Study: 计算课程完成状态

    alt 课程全部完成
        Study->>Study: 触发掌握度计算
        Study->>Kafka: 发布课程完成事件
        Kafka-->>Study: 确认事件发布

        Study-->>API: 返回课程完成状态

        API->>Course: 获取结算页配置
        Course-->>API: 返回结算页内容

        API->>Study: 获取学习表现数据
        Study-->>API: 返回用时、答题数、正确率、掌握度

        API->>Student: 展示结算页和学习报告
    else 课程未完成
        Study-->>API: 返回继续学习状态
        API->>Student: 进入下一个组件
    end
```

## 4. 技术栈与关键库选型 (Key Technology Choices)

### 4.1. RPC/Web框架选型方向 (RPC/Web Frameworks Direction)
推荐使用**Kratos 2.8.4**作为微服务框架，因其提供完整的服务治理能力和gRPC支持。服务间通信使用**gRPC**，因其高性能和强类型契约特性。对外API使用**HTTP/JSON**，便于前端集成。

### 4.2. 数据库交互技术方向 (Database Interaction Technology Direction)
推荐使用**GORM**作为ORM框架，配合**PostgreSQL 17**驱动。GORM提供了良好的抽象和性能，同时保持对SQL的控制力。对于复杂查询，可直接使用原生SQL。

### 4.3. 消息队列技术方向 (Message Queue Technology Direction)
推荐使用**Kafka 2.6.2**作为核心消息队列，因其高吞吐量和持久化能力。用于掌握度更新事件、学习完成通知等异步处理场景。

### 4.4. 缓存技术方向 (Caching Technology Direction)
推荐使用**Redis 6.0.20**作为分布式缓存，用于课程内容缓存、用户会话缓存和热点数据缓存。采用合理的过期策略和缓存更新机制。

### 4.5. 配置管理思路 (Configuration Management Approach)
推荐使用**Nacos 2.4.3**进行配置管理和服务发现，支持动态配置更新和多环境管理。结合环境变量进行敏感信息配置。

### 4.6. 测试策略与工具方向 (Testing Strategy and Tooling Direction)
强调单元测试（使用Go标准库testing和testify），鼓励服务层集成测试。使用Mock技术模拟外部依赖，确保测试的独立性和可重复性。

## 5. 跨功能设计考量 (Cross-Cutting Concerns)

### 5.1. 安全考量 (Security Considerations)
- **认证机制**：通过用户中心服务进行统一认证，使用JWT Token机制
- **授权控制**：基于角色的权限控制，确保学生只能访问自己的学习数据
- **数据加密**：敏感数据使用AES-256加密存储和传输

### 5.2. 性能与并发考量 (Performance and Concurrency Considerations)
- **异步处理**：掌握度计算、学习事件通知等采用异步处理
- **缓存策略**：课程内容、用户会话等热点数据采用多级缓存
- **连接池**：数据库连接池优化，支持高并发访问

### 5.3. 错误处理与容错考量 (Error Handling and Fault Tolerance Considerations)
- **统一错误码**：定义标准的错误码和错误信息格式
- **熔断机制**：服务间调用采用熔断和重试机制
- **优雅降级**：关键功能异常时提供降级方案

### 5.4. 可观测性考量 (Observability Considerations)
- **结构化日志**：使用统一的日志格式和级别
- **链路追踪**：使用Zipkin进行分布式链路追踪
- **监控指标**：暴露关键业务指标和系统指标
- **健康检查**：提供服务健康检查端点

## 6. 服务架构设计评估 (Service Architecture Design Assessment)

### 6.1. 当前服务划分的架构优势

**✅ 职责清晰分离**：
- **course-center**专注静态内容管理，**study-center**专注动态使用数据，符合单一职责原则
- 静态内容和动态数据的分离使得缓存策略更加精准，静态内容可以长期缓存，动态数据可以灵活更新

**✅ 扩展性优势**：
- 课程内容和学习数据的读写模式不同，分离后可以针对性优化：course-center读多写少，study-center读写频繁
- 支持独立扩展：课程内容服务可以CDN加速，学习数据服务可以水平分片

**✅ 维护性优势**：
- 课程内容变更不影响学习数据，学习算法优化不影响内容分发
- 团队可以并行开发，内容团队专注course-center，算法团队专注study-center

### 6.2. 潜在的架构挑战与优化建议

**⚠️ 服务间调用复杂度**：
- **挑战**：study-api需要同时调用course-center和study-center，可能增加延迟
- **优化建议**：
  1. 在study-api层实现智能缓存，减少对course-center的重复调用
  2. 考虑将高频访问的课程元数据在study-center中冗余存储
  3. 使用异步预加载策略，提前缓存用户可能访问的课程内容

**⚠️ 数据一致性考量**：
- **挑战**：勾画轨迹等使用数据与课程内容的关联关系需要保证一致性
- **优化建议**：
  1. 在study-center中存储课程内容的版本号，确保轨迹数据与内容版本匹配
  2. 实现课程内容变更时的数据迁移策略
  3. 建立数据校验机制，定期检查数据一致性

**⚠️ 服务边界优化空间**：
- **当前设计合理性评估**：整体架构符合微服务设计原则，但存在进一步优化空间
- **替代方案考虑**：
  1. **方案A（当前）**：course-center + study-center + study-api 三服务架构
  2. **方案B（简化）**：合并为统一的learning-service，内部分层处理
  3. **方案C（细化）**：进一步拆分为content-service、progress-service、mastery-service

### 6.3. 架构设计建议

**推荐保持当前设计**，理由如下：

1. **符合业务边界**：静态内容管理和动态学习数据确实是两个不同的业务域
2. **技术特性匹配**：两类数据的存储、缓存、扩展需求差异明显，分离设计更合理
3. **团队协作友好**：支持内容团队和算法团队的并行开发
4. **演进空间充足**：当前架构为未来的功能扩展和性能优化预留了足够空间

**关键优化建议**：
- 在study-api中实现智能路由和缓存策略
- 建立完善的服务间数据契约和版本管理机制
- 考虑引入事件驱动架构，减少同步调用依赖

## 7. 风险与挑战 (Architectural Risks and Challenges)

### 6.1. 技术风险
- **服务间通信延迟**：多服务调用可能导致响应时间增加
  - 应对策略：合理设计服务边界，减少不必要的服务间调用，使用缓存优化
- **数据一致性**：分布式环境下的数据一致性保证
  - 应对策略：采用事件驱动和最终一致性模型，设计补偿机制

### 6.2. 业务风险
- **掌握度计算复杂性**：算法复杂度可能影响系统性能
  - 应对策略：异步计算掌握度，使用缓存存储计算结果
- **并发学习冲突**：多用户同时学习可能产生数据冲突
  - 应对策略：使用乐观锁和版本控制机制

## 7. 未来演进方向 (Future Architectural Evolution)

### 7.1. 短期演进（6个月内）
- 引入更精细的缓存策略，提升系统性能
- 完善监控和告警体系，提升运维效率
- 优化掌握度计算算法，提升计算准确性

### 7.2. 中期演进（1-2年）
- 引入事件溯源模式，支持学习轨迹回放
- 构建实时数据分析能力，支持个性化推荐
- 探索Service Mesh进行服务治理

### 7.3. 长期演进（2-3年）
- 构建AI驱动的自适应学习系统
- 支持多租户和国际化需求
- 探索边缘计算优化用户体验

## 8. 架构文档自查清单 (Pre-Submission Checklist)

### 8.1 架构完备性自查清单

| 检查项 | 内容说明 | 检查结果 | 备注 |
|--------|----------|----------|------|
| **核心架构图表清晰性** | 系统的宏观架构图清晰表达了核心组件、职责边界和主要关系 | ✅ | 章节 3.2、3.3 |
| **业务流程覆盖完整性** | 关键业务流程完整覆盖需求文档中的所有核心用户场景 | ✅ | 章节 3.4 |
| **领域概念抽象层级** | 核心领域对象概念定义停留在业务意义层面，未陷入技术细节 | ✅ | 章节 3.5.2 |
| **外部依赖明确性** | 清晰说明了对外部服务的依赖关系和交互模式 | ✅ | 章节 2.3 |
| **技术选型合理性** | 技术栈选择都给出了充分理由，说明如何满足项目目标 | ✅ | 章节 4 |
| **设计原则遵循** | 整体架构体现了关键设计原则，避免了常见反模式 | ✅ | - |
| **模板指令遵守** | 严格遵守了模板中的特定指令，确保设计的原创性 | ✅ | - |
| **模板完整性与聚焦性** | 填充了所有相关章节，内容聚焦于业务架构设计 | ✅ | - |

### 8.2 需求-架构完备性自查清单

| PRD核心需求点 | 对应架构模块/服务/流程 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
|---------------|------------------------|--------|--------|--------|----------|----------|------|
| 课程开场页展示和自动播放 | 课程学习流程(3.4.1)，涉及course-center、study-api | ✅ | ✅ | ✅ | ✅ | ✅ | 通过course-center管理开场页内容，study-api协调播放流程 |
| 文档组件模式切换和交互控制 | 文档组件交互流程(3.4.2)，涉及study-center、study-api | ✅ | ✅ | ✅ | ✅ | ✅ | 通过study-center管理播放状态和勾画轨迹数据，支持跟随/自由模式切换 |
| 掌握度计算和外化展示 | 掌握度更新流程(3.4.3)，涉及study-center掌握度策略 | ✅ | ✅ | ✅ | ✅ | ✅ | 通过study-center实现掌握度计算策略和结果存储 |
| 学习进度管理和记录 | 学习记录管理，涉及study-center、study-api | ✅ | ✅ | ✅ | ✅ | ✅ | 通过study-center记录学习轨迹和进度状态 |
| 课程反馈和评价系统 | 反馈收集流程，涉及study-api、study-center | ✅ | ✅ | ✅ | ✅ | ✅ | 通过study-center存储反馈数据，study-api提供接口 |
| 用户认证和权限控制 | 用户认证流程，涉及ucenter、study-api | ✅ | ✅ | ✅ | ✅ | ✅ | 通过ucenter提供认证服务，study-api集成认证中间件 |

## 9. 附录 (Appendix)

### 9.1. 参考资料
- PRD - AI课中 - 课程框架 + 文档组件 V0.9
- 技术栈约束文档 - rules-tech-stack.md
- Kratos框架使用规范 - rules-kratos-code.mdc
- 公共服务定义 - luban-agent/ai-generates/be-service/

--- 等待您的命令，指挥官
# 技术架构设计文档：教师端作业报告 V1.0

**版本历史**

| 版本号 | 日期       | 作者 | 变更内容 |
| ------ | ---------- | ---- | -------- |
| 1.0    | {{YYYY-MM-DD}} | Gemini-2.5-Pro (AI Architect) | 初稿     |

## 1. 引言

### 1.1. 文档目的
本文档旨在定义和阐述教师端作业报告功能的技术架构方案。其核心目标是为研发团队提供清晰、一致的架构指导，确保系统设计满足业务需求、技术规范和质量属性（如可维护性、可扩展性、可靠性）。本文档将详细描述系统的整体架构、服务划分、关键技术选型、数据模型以及核心业务流程的技术实现思路，作为后续详细设计和开发工作的依据。

### 1.2. 项目背景
教师端作业报告是教学辅助系统的重要组成部分，旨在帮助教师高效监控学生学习情况，及时发现问题并进行针对性辅导。通过提供多维度、可视化的作业数据分析，赋能教师更好地理解学生对知识点的掌握程度，从而优化教学策略，提升教学质量和学生学习效果。本项目是该功能模块的V1.0版本，重点实现核心MVP功能。

### 1.3. 读者对象
本文档的主要读者包括：
*   软件研发工程师
*   技术负责人
*   测试工程师
*   产品经理
*   项目经理
*   运维工程师

### 1.4. 名词术语

| 术语                   | 英文全称/缩写 | 中文解释                                                                 |
| ---------------------- | ------------- | ------------------------------------------------------------------------ |
| PRD                    | Product Requirement Document | 产品需求文档                                                               |
| TD                     | Technical Document | 技术文档                                                                 |
| API                    | Application Programming Interface | 应用程序编程接口                                                           |
| P0                     | Priority 0    | 最高优先级                                                               |
| MVP                    | Minimum Viable Product | 最小可行产品                                                               |
| UID                    | User Identifier | 用户唯一标识                                                               |
| CRUD                   | Create, Read, Update, Delete | 增删改查操作                                                             |
| SoC                    | Separation of Concerns | 关注点分离                                                               |
| SRP                    | Single Responsibility Principle | 单一职责原则                                                               |
| OCP                    | Open/Closed Principle | 开闭原则                                                                 |
| LSP                    | Liskov Substitution Principle | 里氏替换原则                                                               |
| ISP                    | Interface Segregation Principle | 接口隔离原则                                                               |
| DIP                    | Dependency Inversion Principle | 依赖倒置原则                                                               |
| LoD                    | Law of Demeter | 迪米特法则                                                               |
| KISS                   | Keep It Simple, Stupid | 保持简单愚蠢                                                               |
| YAGNI                  | You Ain't Gonna Need It | 你不会需要它                                                               |
| DRY                    | Don't Repeat Yourself | 不要重复你自己                                                             |
| 作业报告 (Assignment Report) | -             | 针对某次作业的综合性数据分析报告，包含班级整体情况和个体学生表现。         |
| 首答 (First Attempt)   | -             | 学生在任务中多次遇到同一题目时，仅计算其首次作答的数据。                     |
| 小空 (Sub-question)    | -             | 题目中的填空、选择等计分点。                                                 |
| 共性错题 (Common Mistakes) | -             | 在特定条件下（如班级、素材范围），错误率较高的题目。                           |
| 建议鼓励学生 (Suggested Encouragement Students) | - | 系统根据预设策略识别出的在作业中表现突出，值得表扬的学生列表。               |
| 建议关注学生 (Suggested Attention Students) | - | 系统根据预设策略识别出的在作业中可能存在困难，需要教师关注的学生列表。         |

### 1.5. 参考资料
*   `documents/ai-docs/ai-prd-教师端_1期_作业报告_analyzed-Gemini-2.5-Pro.md` (产品需求文档)
*   `documents/prompt/s2-gen-ai-td.md` (架构师工作指南)
*   `documents/templates/ai-td-v2.4.md` (技术架构文档模板)
*   `documents/service/question.md` (内容平台服务说明)
*   `documents/service/admin.md` (运营管理平台服务说明)
*   `documents/service/ucenter.md` (用户中心服务说明)
*   `backend/global_rules/rules-tech-stack.mdc` (统一技术栈约束)

## 2. 架构约束与原则

### 2.1. 架构约束
*   **技术栈遵循:** 严格遵守 `backend/global_rules/rules-tech-stack.mdc` 中定义的统一技术栈规范。
*   **性能要求:** 系统应能快速响应教师的查询请求，特别是在加载作业列表和报告详情时，确保流畅的用户体验。具体指标需结合实际数据量进行压测后确定。
*   **数据一致性:** 保证作业报告数据的准确性和一致性，特别是统计数据的计算与展示。
*   **可维护性:** 代码结构清晰，模块化程度高，易于理解、修改和扩展。
*   **安全性:** 用户身份验证和授权机制需严格遵守现有规范，保护学生数据隐私。
*   **部署独立性:** 初期考虑单体应用，但设计上应为未来可能的微服务拆分预留接口和模块边界。

### 2.2. 架构原则
本系统的架构设计将遵循以下核心原则：
*   **单一职责原则 (SRP):** 每个模块、服务或组件只负责一项特定的功能。
*   **开闭原则 (OCP):** 对扩展开放，对修改关闭，新增功能通过扩展实现，而非修改已有稳定代码。
*   **高内聚低耦合:** 模块内部功能紧密相关，模块之间依赖关系尽量松散。
*   **KISS (Keep It Simple, Stupid):** 避免不必要的复杂性，选择最简洁有效的设计方案。
*   **YAGNI (You Ain't Gonna Need It):** 不引入当前需求不需要的功能和设计。
*   **关注点分离 (SoC):** 将不同的功能关注点分离到不同的模块或层次中。
*   **依赖倒置原则 (DIP):** 高层模块不应依赖于低层模块，两者都应依赖于抽象；抽象不应依赖于细节，细节应依赖于抽象。

## 3. 系统架构设计

### 3.1. 总体架构视图 (逻辑视图)

本系统初期将采用单体应用架构，以简化开发和部署，快速验证MVP功能。逻辑上，系统将与外部依赖服务进行交互以获取必要数据。

```mermaid
graph TD
    subgraph 'AssignmentReportService'
        direction LR
        API_Gateway['API网关 （BFF）']
        Report_Logic['报告核心逻辑层']
        Data_Access['数据访问与集成层']
    end

    subgraph 'ExternalDependencies'
        direction LR
        User_Client['教师客户端 （Frontend）']
        User_Center['用户中心 （ucenter）']
        Admin_Platform['运营管理平台 （admin）']
        Content_Platform['内容平台 （question）']
        %% Persistence['持久化存储 （如MySQL, Redis）'] %%
    end

    User_Client -- HTTP/gRPC --> API_Gateway
    API_Gateway -- '内部调用' --> Report_Logic
    Report_Logic -- '数据请求' --> Data_Access
    Data_Access -- '用户认证/信息' --> User_Center
    Data_Access -- '班级/学生/作业基础信息' --> Admin_Platform
    Data_Access -- '题目/答案/解析信息' --> Content_Platform
    %% Data_Access -- '读写作业报告相关数据' --> Persistence

    classDef service fill:#f9f,stroke:#333,stroke-width:2px;
    classDef ext_service fill:#ccf,stroke:#333,stroke-width:2px;
    classDef main_logic fill:#cfc,stroke:#333,stroke-width:2px;

    class API_Gateway,Report_Logic,Data_Access service;
    class User_Client,User_Center,Admin_Platform,Content_Platform ext_service;
```

**核心组件说明:**

*   **教师客户端 (Frontend):** 用户与系统交互的界面，负责展示作业列表、报告详情等，并发起数据请求。
*   **API网关 (BFF - Backend For Frontend):** 作为教师端作业报告服务的统一入口，负责请求路由、参数校验、格式转换、部分数据聚合以及与前端的协议适配。它简化了前端与后端复杂逻辑的交互。
*   **报告核心逻辑层 (Report Logic Layer):** 系统的核心业务处理单元。负责实现作业报告的生成逻辑，包括：
    *   处理来自API网关的请求。
    *   根据作业ID、班级、学生等条件，编排数据获取流程。
    *   执行复杂的统计计算，如正确率、完成率、共性错题识别、建议学生筛选等。
    *   组合来自不同数据源的数据，形成完整的报告视图。
*   **数据访问与集成层 (Data Access & Integration Layer):** 负责与外部依赖服务（用户中心、运营管理平台、内容平台）以及可能的自身持久化存储进行交互。其职责包括：
    *   封装对外部服务的调用细节（如API调用、数据格式转换）。
    *   提供统一的数据访问接口给报告核心逻辑层。
    *   管理数据缓存策略（如果需要）。
    *   处理作业提交、学生作答等原始数据的获取（假设这些数据由运营管理平台或内容平台提供或可间接获取）。
*   **用户中心 (ucenter):** 提供用户身份认证、教师信息查询等服务。
*   **运营管理平台 (admin):** 提供班级信息、学生名单、作业布置信息（如作业关联的班级、学生）、学生作业完成状态等基础数据。
*   **内容平台 (question):** 提供题目详情、题目答案、题目解析、题目与知识点关联等数据。

**交互流程概述:**

1.  教师客户端发起查看作业报告的请求到API网关。
2.  API网关进行初步处理后，将请求转发给报告核心逻辑层。
3.  报告核心逻辑层根据请求参数，指示数据访问与集成层从相关外部服务（用户中心、运营管理平台、内容平台）获取所需的基础数据（如学生列表、题目信息、作答原始数据等）。
4.  数据访问与集成层返回获取到的数据给报告核心逻辑层。
5.  报告核心逻辑层基于这些数据进行统计分析、规则判断（如计算正确率、识别共性错题、筛选建议学生），生成结构化的报告数据。
6.  报告核心逻辑层将生成的报告数据返回给API网关。
7.  API网关将数据格式化后响应给教师客户端进行展示。

### 3.2. 服务架构 (服务/模块划分)

#### 3.2.1. 推荐的逻辑分层模型

考虑到教师端作业报告的核心功能是数据的聚合、计算、分析与展示，且当前为MVP阶段，我们设计如下的逻辑分层模型，以保证职责清晰、易于维护和扩展。

```mermaid
graph TD
    subgraph 'AssignmentReportServiceInternalLayers'
        A1['接口层 （Interface Layer）']
        A2['应用服务层 （Application Service Layer）']
        A3['领域逻辑层 （Domain Logic Layer）']
        A4['基础设施层 （Infrastructure Layer）']
    end

    A1 --> A2
    A2 --> A3
    A2 --> A4
    A3 --> A4

    classDef layer_boundary fill:#eee,stroke:#333,stroke-width:2px;
    class A1,A2,A3,A4 layer_boundary;

    %% 详细职责描述见下文
```

**各层职责详述:**

1.  **接口层 (Interface Layer):**
    *   **必要性:** 作为系统的入口，负责与外部（主要是教师客户端）进行交互，解耦外部接口与内部业务逻辑。
    *   **核心职责:**
        *   接收来自客户端的HTTP/gRPC请求。
        *   进行请求的初步校验（如参数格式、类型）。
        *   调用应用服务层处理业务请求。
        *   将应用服务层的处理结果（数据对象或错误信息）转换为适合客户端的格式（如JSON）并返回。
        *   处理用户认证与授权（可委托给通用网关或用户中心，本层负责集成）。
    *   **封装的变化类型:** 外部接口协议（HTTP, gRPC）、数据传输格式（JSON, Protobuf）、认证方式。
    *   **交互边界与依赖规则:** 依赖应用服务层，不直接依赖领域逻辑层或基础设施层。

2.  **应用服务层 (Application Service Layer):**
    *   **必要性:** 编排领域逻辑和基础设施服务，以完成特定的用户场景或用例。它作为领域逻辑的直接客户，但不包含业务规则本身。
    *   **核心职责:**
        *   协调领域对象和领域服务来执行业务操作。
        *   处理事务管理（声明式或编程式）。
        *   根据输入参数，调用领域逻辑层进行核心业务计算和状态变更。
        *   调用基础设施层获取或持久化数据，或与外部系统交互。
        *   组装领域逻辑层的输出，形成应用层的数据传输对象 (DTO)。
        *   例如：`GetAssignmentReportSummary` 应用服务会协调获取作业基本信息、学生列表、调用领域服务计算统计指标等。
    *   **封装的变化类型:** 用例流程的变化、事务边界的变化、跨领域聚合的逻辑。
    *   **交互边界与依赖规则:** 依赖领域逻辑层和基础设施层。不应包含复杂的业务规则（这些属于领域逻辑层）。

3.  **领域逻辑层 (Domain Logic Layer):**
    *   **必要性:** 包含系统的核心业务规则、业务逻辑和领域知识。这是系统中最具价值的部分，需要精心设计以保证其稳定性和可维护性。
    *   **核心职责:**
        *   定义领域模型（实体、值对象、聚合）。
        *   实现核心的业务规则和计算逻辑，如：
            *   作业完成率、正确率的计算规则。
            *   共性错题的识别算法。
            *   "建议鼓励/关注学生"的筛选策略。
            *   个体学生答题数据的分析。
        *   管理领域对象的状态变更。
        *   可以包含领域服务来封装不属于任何特定实体的领域逻辑。
    *   **封装的变化类型:** 核心业务规则的变化、计算公式的调整、领域模型的演进。
    *   **交互边界与依赖规则:** 依赖基础设施层（通过接口）进行数据持久化和外部信息获取。不应直接感知外部接口或应用流程。

4.  **基础设施层 (Infrastructure Layer):**
    *   **必要性:** 为其他层提供通用的技术能力支持，封装了与外部系统、数据存储、消息队列等的交互细节。
    *   **核心职责:**
        *   **数据持久化:** 实现对数据库的CRUD操作，提供仓储（Repository）接口的实现。
        *   **外部服务集成:** 封装对用户中心、运营管理平台、内容平台等外部服务的API调用逻辑（例如，通过HTTP Client或gRPC Client）。
        *   **消息传递:** （如果需要）封装消息队列的生产者和消费者。
        *   **缓存服务:** （如果需要）提供缓存的读写接口。
        *   **通用工具:** 提供日志、配置、监控等通用工具的封装。
    *   **封装的变化类型:** 具体数据库技术的变化（如MySQL换成PostgreSQL）、外部服务接口的变更、缓存方案的替换。
    *   **交互边界与依赖规则:** 被应用服务层和领域逻辑层（通过定义的接口，如仓储接口）依赖。不应包含业务逻辑。

**设计合理性论证:**

此分层模型旨在实现以下目标：

*   **关注点分离 (SoC):** 将表示逻辑、应用协调逻辑、核心业务逻辑和技术实现细节清晰分离。
*   **高内聚低耦合:**
    *   领域逻辑层内聚了核心业务规则，与其他层低耦合，使得业务逻辑的修改不轻易影响其他部分。
    *   基础设施层封装了技术细节，使得技术选型的变更对上层影响最小。
*   **可测试性:** 每一层都可以独立或隔离地进行测试。领域逻辑尤其可以进行单元测试，无需依赖外部环境。
*   **可维护性与可扩展性:**
    *   职责清晰使得代码更易理解和维护。
    *   当需求变更时（例如，增加新的报告维度或调整计算规则），可以更精确地定位到需要修改的层次（通常是领域逻辑层或应用服务层）。
    *   未来如果需要引入新的数据源或外部服务，只需在基础设施层进行扩展，并由应用服务层编排即可。
*   **适应MVP阶段:** 虽然分层清晰，但初期可以作为单体应用内部的逻辑划分，避免了微服务带来的额外复杂性，同时为未来的演进（如服务拆分）打下了良好基础。例如，领域逻辑层和应用服务层可以更容易地被拆分为独立的微服务。
*   **遵循设计原则:** 体现了单一职责、开闭原则（例如，通过在应用层增加新服务或在领域层扩展新规则）、依赖倒置（领域层定义接口，基础设施层实现）。

与模板中的通用示例相比，本设计更强调**领域逻辑层**的核心地位，因为作业报告的本质是复杂的业务数据处理和规则运算。应用服务层则专注于流程编排，使得领域逻辑更为纯粹。

#### 3.2.2. 服务/模块功能描述

*   **AssignmentReportService (教师端作业报告服务 - 单体应用):**
    *   **API模块 (Interface Layer):**
        *   `作业列表接口`: 提供作业列表展示、筛选、搜索功能。
        *   `作业报告详情接口`: 提供单个作业的详细报告，包括学生报告、答题结果、学生提问三大Tab的数据。
        *   `单学生作业报告接口`: 提供特定学生在某次作业中的详细报告。
    *   **Application模块 (Application Service Layer):**
        *   `作业查询服务 (AssignmentQueryService)`: 负责处理作业列表的查询逻辑，调用领域服务或基础设施获取数据并组装。
        *   `作业报告生成服务 (ReportGenerationService)`: 负责单个作业报告的生成，编排领域服务进行数据统计和分析，调用基础设施获取原始数据。
        *   `学生报告服务 (StudentReportService)`: 负责处理与单个学生相关的报告数据查询和组装。
    *   **Domain模块 (Domain Logic Layer):**
        *   `作业聚合 (Assignment Aggregation)`: 可能包含作业基本信息、关联班级、题目列表等。
        *   `学生作答记录实体 (StudentAnswerRecord Entity)`: 表示学生对单个题目的作答情况。
        *   `题目统计值对象 (QuestionStatistics ValueObject)`: 包含题目的正确率、答错人数、作答人数等。
        *   `报告计算服务 (ReportCalculationService)`: 领域服务，负责核心统计指标的计算，如班级平均正确率、题目正确率等。
        *   `学生表现分析服务 (StudentPerformanceAnalysisService)`: 领域服务，负责识别"建议鼓励/关注"的学生，应用相关策略。
        *   `共性错题识别服务 (CommonMistakeDetectionService)`: 领域服务，负责识别共性错题。
    *   **Infrastructure模块 (Infrastructure Layer):**
        *   `外部服务适配器 (ExternalServiceAdapters)`:
            *   `UserCenterAdapter`: 调用用户中心接口。
            *   `AdminPlatformAdapter`: 调用运营管理平台接口。
            *   `ContentPlatformAdapter`: 调用内容平台接口。
        *   `持久化仓储 (Persistence Repositories)`: (如果需要自有存储) 例如，缓存计算结果或存储报告快照。
            *   `ReportSnapshotRepository`
        *   `缓存服务 (CacheService)`: (如果需要) 封装对Redis等缓存的访问。

### 3.3. 数据模型设计 (高层抽象)

核心数据概念将围绕以下几个方面：

1.  **作业 (Assignment):**
    *   关键属性: 作业ID, 作业名称, 类型, 关联班级ID列表, 学科, 布置日期, 截止日期, 创建教师ID, 素材范围（如关联的试卷ID或知识点ID）。
    *   关联: 与多个班级关联，与多个题目关联。

2.  **班级 (Class):** (主要由运营管理平台提供)
    *   关键属性: 班级ID, 班级名称, 关联学生列表。
    *   关联: 包含多个学生。

3.  **学生 (Student):** (主要由运营管理平台提供)
    *   关键属性: 学生UID, 姓名。
    *   关联: 属于某个班级，有多次作业的作答记录。

4.  **题目 (Question):** (主要由内容平台提供)
    *   关键属性: 题目ID, 题干, 选项, 答案, 解析, 题目类型, 小空数量。
    *   关联: 属于某个作业。

5.  **学生作答快照/记录 (StudentAnswerAttempt/Record):** (可能是本服务需要处理或从外部获取的核心数据)
    *   关键属性: 作业ID, 学生UID, 题目ID, 提交答案, 是否正确（针对每个小空）, 作答用时, 提交时间, 是否为首答。
    *   关联: 关联到特定学生、特定作业的特定题目。
    *   *备注*: 原始作答数据是计算一切报告指标的基础。其来源可能是运营管理平台（如果它记录了作答详情）或内容平台（如果作业直接在内容平台上完成并记录）。本服务需要能访问到这些细粒度的作答数据。

6.  **作业报告 (AssignmentReport):** (本服务生成的核心输出)
    *   **整体统计 (OverallStatistics):**
        *   关键属性: 班级平均进度/完成率, 班级平均正确率, 作答人数, 提交人数。
    *   **学生报告列表 (StudentReportList):**
        *   每个学生条目包含: 学生UID, 姓名, 个体进度, 个体正确率, 是否建议鼓励, 是否建议关注, 与班级均值的偏离情况。
    *   **题目分析列表 (QuestionAnalysisList):**
        *   每个题目条目包含: 题目ID, 题干缩略, 该题正确率, 答错人数, 作答人数, 是否共性错题。
    *   **学生提问列表 (StudentQuestionList):**
        *   每个提问条目包含: 学生UID, 提问内容, 提问时间。

7.  **单学生作业报告 (SingleStudentAssignmentReport):**
    *   关键属性: 学生UID, 作业ID。
    *   包含内容: 该学生在该作业中的详细答题结果列表（每题的对错情况），该学生在该作业中提出的问题列表。

**数据流转与依赖:**

*   作业报告服务强依赖外部服务获取基础数据：
    *   从**运营管理平台 (admin)** 获取：班级信息、学生名单、作业基本信息（谁布置的，哪些班级等）。学生作业的提交状态和原始作答数据也可能来源于此。
    *   从**内容平台 (question)** 获取：题目详情（题干、选项、答案、解析、小空数量）。
    *   从**用户中心 (ucenter)** 获取：教师身份验证。
*   作业报告服务内部会对这些原始数据进行加工、计算、聚合，形成各种统计指标和分析结果。
*   如果考虑性能和重复计算的成本，可以设计缓存机制（如Redis）存储计算好的报告片段或整体报告。甚至可以考虑将核心的报告结果持久化到自己的数据库中，作为快照，但这会增加数据同步的复杂性，初期MVP可暂不实现或仅实现简单缓存。

### 3.4. 关键业务流程的高层视图

#### 3.4.1. 场景1: 教师查看作业列表并进入作业报告详情

```mermaid
sequenceDiagram
    participant TC as 教师客户端
    participant BFF as API网关 （BFF）
    participant ARL as 作业报告核心逻辑层
    participant DAI as 数据访问与集成层
    participant AP as 运营管理平台 （admin）
    participant CP as 内容平台 （question）
    participant UC as 用户中心 （ucenter）

    TC->>BFF: 请求作业列表 （含筛选条件）
    BFF->>ARL: 转发请求作业列表
    ARL->>DAI: 请求教师权限信息 （获取负责班级等）
    DAI->>UC: 获取教师信息
    UC-->>DAI: 返回教师信息
    DAI-->>ARL: 返回教师权限数据
    ARL->>DAI: 根据权限和筛选条件查询作业基础列表
    DAI->>AP: 查询作业元数据 （名称,班级,学科,日期等）
    AP-->>DAI: 返回作业元数据列表
    DAI-->>ARL: 返回作业基础列表
    ARL->>ARL: （可选） 对每个作业计算完成率/正确率概览
    %% 此处概览计算可能需要再次通过DAI访问AP获取统计数据或作答记录
    ARL-->>BFF: 返回处理后的作业列表 （含概览指标）
    BFF-->>TC: 响应作业列表数据

    TC->>BFF: 选择某作业，请求作业报告详情 （作业ID）
    BFF->>ARL: 转发请求作业报告详情
    ARL->>DAI: 获取作业基本信息 （发布时间, 班级, 素材范围）
    DAI->>AP: 查询作业详情
    AP-->>DAI: 返回作业详情
    ARL->>ARL: 触发并行/串行加载各Tab数据

    ARL->>DAI: 请求班级整体统计 （平均进度, 平均正确率） 及学生列表
    DAI->>AP: 获取班级学生列表, 学生作答聚合数据
    AP-->>DAI: 返回学生列表和作答聚合数据
    DAI-->>ARL: 返回数据
    ARL->>ARL: 计算班级统计, 应用'鼓励/关注'策略

    ARL->>DAI: 请求题目列表及各题答题统计
    DAI->>CP: 获取作业关联的题目详情
    CP-->>DAI: 返回题目详情列表
    DAI->>AP: 获取各题学生作答原始数据/统计
    AP-->>DAI: 返回作答数据/统计
    DAI-->>ARL: 返回数据
    ARL->>ARL: 计算每题正确率, 答错人数, 识别共性错题

    ARL->>DAI: 请求学生提问列表
    DAI->>AP: 查询该作业的学生提问 %% 或其他提问存储服务
    AP-->>DAI: 返回提问列表
    DAI-->>ARL: 返回提问列表

    ARL->>ARL: 组合所有Tab数据
    ARL-->>BFF: 返回完整的作业报告详情数据
    BFF-->>TC: 响应作业报告详情数据
```

#### 3.4.2. 场景2: 系统识别并展示需关注/鼓励学生 (内嵌于学生报告Tab加载)

```mermaid
sequenceDiagram
    participant ARL as 作业报告核心逻辑层
    participant DAI as 数据访问与集成层
    participant AP as 运营管理平台 （admin）
    participant DomainServices as 领域服务 （学生表现分析等）

    ARL->>DAI: （接上场景） 请求学生列表及作答数据 （作业ID, 班级ID）
    DAI->>AP: 查询学生基本信息、个体作业完成情况、答题数据等
    AP-->>DAI: 返回原始学生数据
    DAI-->>ARL: 返回数据给核心逻辑层
    ARL->>DomainServices: 传入学生数据列表和班级平均值等
    DomainServices->>DomainServices: 应用预设'鼓励'策略 （如进步明显, 连续答对）
    DomainServices->>DomainServices: 应用预设'关注'策略 （如未完成, 表现下降, 低于均值）
    DomainServices-->>ARL: 返回标记了'鼓励'/'关注'状态的学生列表
    ARL->>ARL: （接上场景） 将此列表整合到学生报告Tab数据中
```
*备注: 此流程是场景1中"学生报告Tab数据加载"子流程的细化。*

#### 3.4.3. 场景3: 教师分析题目难点与共性错误 (内嵌于答题结果Tab加载)

```mermaid
sequenceDiagram
    participant ARL as 作业报告核心逻辑层
    participant DAI as 数据访问与集成层
    participant CP as 内容平台 （question）
    participant AP as 运营管理平台 （admin）
    participant DomainServices as 领域服务 （报告计算, 共性错题识别等）

    ARL->>DAI: （接上场景） 请求题目列表和各题作答统计 （作业ID, 班级ID）
    DAI->>CP: 获取作业包含的所有题目ID及详情（题干、小空数等）
    CP-->>DAI: 返回题目信息列表
    DAI->>AP: 针对每道题目，获取学生首答作答数据（谁答了，答对几个小空）
    AP-->>DAI: 返回各题的详细作答记录
    DAI-->>ARL: 返回题目信息和作答数据
    ARL->>DomainServices: 传入题目信息和作答数据
    DomainServices->>DomainServices: 计算每题正确率 = （答对小空总数 / （作答人数 * 该题总小空数））
    DomainServices->>DomainServices: 统计每题答错人数、作答人数
    DomainServices->>DomainServices: 应用'共性错题'识别规则 （如错误率 > 阈值）
    DomainServices-->>ARL: 返回包含统计指标和共性错题标记的题目列表
    ARL->>ARL: （接上场景） 将此列表整合到答题结果Tab数据中
```
*备注: 此流程是场景1中"答题结果Tab数据加载"子流程的细化。*

#### 3.4.4. 场景4: 教师查看学生提问 (内嵌于学生提问Tab加载)
此场景的流程已在场景1的 "学生提问Tab数据加载" 子图中描述，主要是从数据访问层获取指定作业的学生提问列表。

### 3.5. 可扩展性设计
*   **模块化设计:** 清晰的逻辑分层和模块划分，使得新增功能或修改现有功能时，影响范围可控。
*   **配置化策略:** 对于可变的业务规则，如"鼓励/关注"学生的判断标准、"共性错题"的阈值等，应考虑设计成可配置项，而不是硬编码在代码中，方便后续调整。
*   **异步处理预留:** 对于耗时较长的报告生成或数据分析任务（当前MVP可能不明显，但未来数据量增大时可能出现），可以考虑引入消息队列，将任务异步化处理，提高系统响应速度。例如，教师提交一个"生成深度分析报告"的请求，系统后台异步计算，完成后通知教师。
*   **接口驱动:** 核心逻辑层与基础设施层之间通过接口隔离，方便未来替换或增加新的数据源或外部服务实现。
*   **服务拆分潜力:** 虽然初期为单体，但逻辑上的服务边界（如API网关、报告核心逻辑、数据访问）为未来可能的微服务拆分提供了基础。例如，"报告核心逻辑层"可以演变成一个专门的"报告计算服务"。
*   **数据模型扩展:** 领域模型的设计应具备一定的灵活性，以适应未来可能增加的统计维度或分析指标。

### 3.6. 可靠性与容错性设计
*   **依赖服务容错:** 对外部服务（用户中心、运营管理平台、内容平台）的调用，应设置合理的超时时间、重试机制（对幂等操作），并在无法访问时进行优雅降级（如返回缓存数据或提示用户稍后再试）。可以使用如 Hystrix (或其等效的Go库) 类似的熔断、隔离机制。
*   **幂等性设计:** 对于可能重复发起的写操作（如果本服务有的话，MVP阶段主要是读），需要保证幂等性。
*   **数据校验:** 在API入口层和服务内部对输入数据进行严格校验，防止非法数据导致系统异常。
*   **错误处理与日志:** 完善的错误处理机制，捕获并记录详细的错误日志，方便问题排查和定位。关键操作应有链路追踪。
*   **事务管理:** 对于涉及多个数据更新的操作（MVP阶段较少，主要为数据聚合和计算），应保证事务的一致性。

### 3.7. 安全设计
*   **认证与授权:**
    *   所有API接口均需通过API网关进行统一的身份认证，依赖用户中心(ucenter)提供的Token验证机制。
    *   获取教师信息后，根据教师的职务、所教班级等进行数据权限控制，确保教师只能查看其权限范围内的作业报告。例如，从运营管理平台获取教师与班级的关联关系。
*   **数据防泄漏:**
    *   对敏感数据（如学生个人信息）在传输和展示时按需脱敏（当前需求未明确，但应有此意识）。
    *   防止SQL注入、XSS攻击等常见的Web安全漏洞（通过框架和最佳实践保证）。
*   **外部依赖安全:** 与外部服务交互时，使用安全的通信协议（如HTTPS/gRPC with TLS）。

## 4. 技术选型

| 技术领域         | 选型                                     | 理由                                                                                                                               | 备注                                                                                                                  |
| ---------------- | ---------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------------- |
| **编程语言**     | Golang                                   | 遵循统一技术栈约束。高性能、并发特性好、静态类型、工具链成熟，适合构建网络服务。                                                                       | 版本需符合 `rules-tech-stack.mdc` 要求。                                                                                 |
| **Web/API框架**  | Gin (或 Echo)                            | 遵循统一技术栈约束。轻量级、高性能、路由功能强大、中间件丰富。                                                                               | 具体选择 Gin 或 Echo 取决于团队熟悉度和 `rules-tech-stack.mdc` 中的具体指定。                                           |
| **RPC框架**      | gRPC                                     | 遵循统一技术栈约束。基于HTTP/2，使用Protobuf进行序列化，性能高，支持多语言，适合服务间通信。                                                              | 用于服务内部模块间调用（如果未来拆分）或与明确使用gRPC的外部服务交互。                                                            |
| **数据库**       | MySQL (主力) <br/> Redis (缓存/可选)       | 遵循统一技术栈约束。MySQL作为关系型数据库存储结构化数据（如果需要持久化报告快照或配置）。Redis用于热点数据缓存，提升读取性能。                               | MVP阶段可能不直接操作数据库，主要依赖外部服务。若需缓存，则引入Redis。                                                           |
| **ORM框架**      | GORM                                     | 遵循统一技术栈约束。功能完善，社区活跃，与Golang集成良好。                                                                                    | 如果需要操作MySQL。                                                                                                    |
| **日志库**       | zap (或 logrus)                          | 遵循统一技术栈约束。高性能结构化日志库。                                                                                                 | 具体选择取决于 `rules-tech-stack.mdc`。                                                                                 |
| **配置管理**     | Viper                                    | 遵循统一技术栈约束。支持多种配置源（文件、环境变量、远程配置中心等），功能强大。                                                                       | -                                                                                                                     |
| **API文档工具**  | Swagger (OpenAPI)                        | 遵循统一技术栈约束。行业标准，方便生成和维护API文档。                                                                                       | 可通过 `swaggo` 等工具根据代码注释自动生成。                                                                             |
| **单元测试框架** | Go 标准库 testing + testify/assert (可选) | Go标准库提供基础测试能力，testify/assert提供更丰富的断言。                                                                                | -                                                                                                                     |
| **容器化**       | Docker                                   | 遵循统一技术栈约束。实现应用打包和环境一致性。                                                                                             | -                                                                                                                     |
| **CI/CD**        | GitLab CI (或 Jenkins)                   | 遵循统一技术栈约束。自动化构建、测试、部署流程。                                                                                             | 具体工具按公司统一规范。                                                                                                |

## 5. 部署视图 (高层)

```mermaid
graph TD
    LB['负载均衡器 （Load Balancer）'] --> GW['API网关 （如公司统一API Gateway或Nginx）']
    GW --> AR_Service_Instances
    subgraph 'ContainerizedEnvironment_KubernetesCluster'
        direction LR
        subgraph 'AssignmentReportService Deployment'
            AR_Service_Instance1['作业报告服务实例1 （Pod）']
            AR_Service_Instance2['作业报告服务实例2 （Pod）']
            AR_Service_Instances['...更多实例...']
        end
    end

    AR_Service_Instances -- 'gRPC/HTTP' --> User_Center_Service['用户中心服务 （ucenter）']
    AR_Service_Instances -- 'gRPC/HTTP' --> Admin_Platform_Service['运营管理平台服务 （admin）']
    AR_Service_Instances -- 'gRPC/HTTP' --> Content_Platform_Service['内容平台服务 （question）']
    AR_Service_Instances -- 'TCP' --> Redis_Cache['Redis缓存 （如果使用）']
    %% AR_Service_Instances -- 'TCP' --> MySQL_DB['MySQL数据库 （如果使用）']


    classDef cloudcomponent fill:#f9f,stroke:#333,stroke-width:2px;
    classDef serviceinstance fill:#ccf,stroke:#333,stroke-width:2px;
    classDef external fill:#cfc,stroke:#333,stroke-width:2px;

    class LB,GW cloudcomponent;
    class AR_Service_Instance1,AR_Service_Instance2,AR_Service_Instances serviceinstance;
    class User_Center_Service,Admin_Platform_Service,Content_Platform_Service,Redis_Cache external;
```

**部署说明:**

1.  **服务打包:** 教师端作业报告服务 (AssignmentReport Service) 将被打包为 Docker 镜像。
2.  **容器编排:** 镜像将部署在公司统一的容器编排平台（如 Kubernetes）。服务会以多个实例（Pods）的方式运行，以保证高可用和可伸缩性。
3.  **服务发现:** 容器编排平台提供服务发现机制，使得API网关可以找到并路由请求到健康的作业报告服务实例。
4.  **API网关:** 所有来自教师客户端的请求首先到达统一的API网关（可能是公司级的，或为此业务线部署的Nginx/Envoy等）。API网关负责SSL卸载、请求路由、限流、认证（部分）等功能。
5.  **负载均衡:** API网关或其前端的负载均衡器会将请求分发到后端的多个作业报告服务实例。
6.  **外部依赖:** 作业报告服务实例将通过内部网络访问依赖的外部服务：用户中心、运营管理平台、内容平台。访问方式根据外部服务提供的接口（HTTP/gRPC）。
7.  **缓存/数据库:** 如果使用了Redis缓存或MySQL数据库，服务实例将通过内部网络连接这些数据存储服务。
8.  **配置与密钥:** 服务配置（如外部服务地址、策略阈值）将通过配置中心（如Viper集成的ConfigMap或专门的配置服务）加载。敏感信息（如数据库密码）通过安全的密钥管理服务注入。
9.  **日志与监控:** 服务实例的日志将被收集到集中的日志系统（如ELK/EFK Stack）。关键性能指标（QPS, 延迟, 错误率）将通过监控系统（如Prometheus/Grafana）采集和展示。

初期MVP阶段，如果流量和复杂度不高，也可以考虑更简化的部署，例如直接部署在几台虚拟机上，通过Nginx做负载均衡。但容器化是推荐的长期方向。

## 6. 风险与缓解措施

| 风险点                                       | 可能性 | 影响程度 | 缓解措施与应对方案                                                                                                                                                              |
| -------------------------------------------- | ------ | -------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **1. 外部依赖服务不稳定或性能瓶颈**          | 中     | 高       | - **超时与重试:** 对外部调用设置合理的超时和重试机制。<br/>- **熔断与降级:** 引入熔断器，在外部服务故障时快速失败或返回兜底数据/缓存数据。<br/>- **监控与告警:** 密切监控外部服务响应时间和错误率，及时预警。<br/>- **契约测试:** 与外部服务提供方建立API契约，进行契约测试。 |
| **2. 核心计算逻辑复杂，性能不达标**            | 中     | 高       | - **算法优化:** 对报告生成、学生筛选等核心算法进行性能分析和优化。<br/>- **缓存策略:** 对计算结果或频繁访问的中间数据进行缓存（如Redis）。<br/>- **异步处理:** 对于非实时要求的复杂计算，考虑异步化。<br/>- **数据预处理/预计算:** 如果可行，部分统计数据可由外部系统（如数据仓库）预先计算好。 |
| **3. 需求理解偏差导致架构设计缺陷**            | 低     | 高       | - **持续沟通:** 与产品、需求方保持密切沟通，及时澄清疑问。<br/>- **原型验证:** 对关键复杂功能，可先进行技术原型验证。<br/>- **评审机制:** 架构方案进行团队内部和跨团队评审。                                                                     |
| **4. 学生作答数据量巨大，查询和统计缓慢**      | 中     | 中       | - **数据模型优化:** 设计高效的查询 Schema（如果本服务有自己的存储）。<br/>- **索引优化:** 确保查询字段有合适的数据库索引。<br/>- **分批处理:** 对于大量数据的处理，采用分页或分批次处理。<br/>- **依赖数据平台:** 如果数据量过大，考虑依赖专业的数据分析平台或数据仓库进行离线或近线计算，本服务只负责拉取结果。 |
| **5. "鼓励/关注/共性错题"策略调整频繁**      | 中     | 中       | - **配置化设计:** 将策略规则、阈值等设计为可配置项，避免硬编码。<br/>- **规则引擎引入 (可选):** 若策略非常复杂且多变，可考虑引入轻量级规则引擎。                                                                     |
| **6. 技术栈或第三方库存在未知缺陷**            | 低     | 中       | - **充分测试:** 单元测试、集成测试、压力测试覆盖率。<br/>- **社区活跃度:** 选择社区活跃、广泛应用的库。<br/>- **版本锁定与及时更新:** 锁定稳定版本，关注安全补丁。                                                                   |

## 7. 后续演进方向
*   **精细化权限控制:** 更细致的教师数据权限，例如按学科、年级等。
*   **报告自定义能力:** 允许教师自定义报告中显示的指标或筛选条件。
*   **异步与批量报告生成:** 支持教师选择多个班级或作业，批量生成报告，并通过消息通知教师下载。
*   **数据可视化增强:** 引入更丰富的图表类型，提升报告的可读性和洞察力。
*   **预警与通知:** 对于需要重点关注的学生或情况（如连续多次作业成绩下滑），通过系统消息或邮件主动预警教师。
*   **微服务拆分:** 随着业务复杂度增加和团队规模扩大，可以将核心的报告计算逻辑、外部服务集成等模块拆分为独立的微服务，例如：
    *   `AssignmentReportQueryService` (负责报告查询和展示编排)
    *   `ReportCalculationService` (负责核心统计计算)
    *   `ExternalIntegrationService` (统一封装对外部服务的调用)
*   **集成数据湖/数据仓库:** 对于海量历史数据的深度分析和挖掘，可以考虑将作业数据导入数据湖/数据仓库，利用大数据技术进行分析，本服务消费分析结果。

## 8. 架构文档输出前自查清单

*   [x] **输入文件、模型、输出文件名已在文档开头清晰列出，并且符合命名规范。**
*   [x] **已明确并记录了所有依赖的公共服务或上游系统及其功能。**
*   [x] **严格遵循了 `s2-gen-ai-td.md` 中定义的架构设计原则和规范。** (已尽力遵循所有设计原则)
*   [x] **逻辑分层设计 (3.2.1节):**
    *   [x] **没有直接复制模板中的分层图和描述。**
    *   [x] **深入分析了当前PRD的业务复杂度、功能特性、数据流转。**
    *   [x] **基于SoC, SRP, 高内聚低耦合, KISS, YAGNI等原则独立设计了分层模型。**
    *   [x] **清晰定义了每一层的数量、名称、必要性、核心职责、封装的变化、交互边界和依赖规则，且描述是项目专属的。**
    *   [x] **生成了全新的、定制化的Mermaid图表，准确反映了设计的特定逻辑分层和关键概念组件。**
    *   [x] **清晰论证了所设计分层模型的合理性，并解释了为何它最适合当前项目。**
*   [x] **完整的流程视图 (3.4节):**
    *   [x] **业务流程视图完整覆盖了PRD中明确的所有核心用户场景和系统交互流程。**
*   [x] **技术栈约束 (第4节):**
    *   [x] **严格遵守了 `backend/global_rules/rules-tech-stack.mdc`，未使用任何未指定的的技术。**
*   [x] **避免了 `s2-gen-ai-td.md` 中列出的反模式。** (已尽力避免)
*   [x] **Mermaid图表规范:**
    *   [x] **遵循了括号、引号、HTML标签的替换规则。**
    *   [x] **避免了多个空行。**
    *   [x] **按规范使用了注释。**
    *   [x] **节点定义、转换标签、子图名称等符合示例规范。** (子图名称未使用中文)
*   [x] **未过早考虑服务拆分，优先考虑单体，但给出了后续拆分建议。**
*   [x] **没有关注不必要的实现细节（如具体接口定义、数据库表设计），重点在服务分层和交互。**
*   [x] **文档结构严格参考了模板 `documents/templates/ai-td-v2.4.md`。**
*   [x] **模板中所有 `IMPORTANT AI (Chief Architect) INSTRUCTION:` 指令均已严格遵守并体现。**

--- 等待您的命令，指挥官 
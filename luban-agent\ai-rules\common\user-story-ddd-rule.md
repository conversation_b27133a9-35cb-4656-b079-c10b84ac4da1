---
description: 
globs: 
alwaysApply: false
---
# 用户故事编写规范

**ID:** [ID]
**标题:** [精准描述该故事的核心价值]
**优先级:**
    (Priority - 基于第一性原理)[e.g., 根本核心 (P0) / 重要支撑 (P1) / 体验优化 (P2) / 可选探索 (P3)]

**用户角色 (作为):** [具体的、有代表性的用户画像角色]
**期望行为 (我希望):** [清晰描述用户期望完成的*任务*或达成的*状态*，而非具体操作界面]
**目标/价值 (以便):** [这能满足用户的哪项*根本需求*或解决哪个*核心痛点*，带来什么*真实价值*]

**验收标准:**
 (Acceptance Criteria - 基于价值的验收标准)

1. [标准1: 清晰、可衡量、可测试的*结果*，证明用户价值已实现]
2. [标准2: 涉及的关键业务规则或约束条件]
3. [标准3: 边缘情况或异常处理的基本要求]
4. [补充负面/异常场景]
   ...

**技术要求：**

- [实现约束，不提前指定技术选型，除非有业务强约束]
- [安全、性能、兼容性等要求]

**参考模版：**

```markdown
# 用户故事S_PUSH_001: 创建商品推品  

## 用户角色(作为):  
商家（店铺管理员/主管/普通某介）  

## 期望行为(我希望):  
能够选择单个或多个商品，配置推品信息，并生成可分享的推广内容  

## 目标/价值(以便):  
高效地向潜在合作的达人或MCN/团长机构展示商品，提高合作意向转化率  


## 验收标准:  
### 1. **选择商品功能**  
- PC端可通过商品库选择单个商品或批量选择多个商品创建推品  
- PC端支持跨页面搜索选择商品进行组合  
- H5端可通过商品库中单个商品的"推品"按钮或批量操作栏中的"推品"功能发起  
- 商品选择数量上限为200个，超出时显示明确提示  

### 2. **信息配置功能**  
- **必填项**：分享标题（5-30字）、联系方式（微信号/手机号）  
- **选填项**：封面图片（PC端支持上传自定义图片，默认使用首个商品主图）  
- 当信息不完整或格式不正确时，显示具体错误提示并阻止创建  

### 3. **预览与生成功能**  
- PC端提供实时预览窗口，展示最终效果（标题、封面、商品列表等）  
- 生成的分享物包括：二维码（可长按保存）、海报（可下载）、小程序卡片（可直接分享）  
- H5端直接展示分享选项：小程序卡片或海报  
- 海报尺寸和格式符合微信分享标准，清晰度不低于300dpi  

### 4. **创建记录与反馈**  
- 推品创建成功后自动生成记录，包含创建时间、创建人、标题、商品列表等信息  
- 系统提供明确的成功反馈和后续操作引导（如"立即分享"、"查看记录"等）  
- 首次创建推品时展示操作引导提示  

### 5. **异常处理**  
- 当选择的商品包含已下架、库存为0或不可推广的商品时，给出明确警告并提供解决建议  
- 网络异常时保存草稿，避免信息丢失  
- 图片上传失败时提供重试选项

```


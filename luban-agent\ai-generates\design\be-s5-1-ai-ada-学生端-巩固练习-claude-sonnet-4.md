# 场景分析与接口识别 (需求范围：PRD《学生端巩固练习 V1.0》)

* **当前日期**: 2025-06-04
* **模型**: claude-sonnet-4
* **关联PRD文档**: be-s1-2-ai-prd-学生端-巩固练习-claude-sonnet-4.md
* **关联TD文档**: be-s2-1-ai-td-学生端-巩固练习-claude-sonnet-4.md
* **关联DE文档**: be-s3-2-ai-de-学生端-巩固练习-claude-sonnet-4.md
* **关联DD文档**: be-s4-2-ai-dd-学生端-巩固练习-claude-sonnet-4.md

## 1. 整体需求概述

* **核心业务目标**: 通过巩固练习功能的实现，提升学生在AI课后的知识掌握程度和学习效果，将知识从"知道"转变为"会用"。系统预期可以提升学生练习完成率30%，知识点掌握度达到85%以上，通过再练一次机制确保每个学生都能达到掌握知识的目标。

* **主要功能模块/用户旅程**: 
  - 巩固练习入口管理：提供清晰的练习状态展示和智能入口变化
  - 练习环节执行：完整的答题体验，包括题目展示、多题型作答支持、实时进度管理
  - 题目间转场反馈：丰富的情感化反馈机制，包括正确/错误作答反馈、连胜激励
  - 学习报告生成：详细的学习成果报告，展示掌握度变化、答题统计等
  - 辅助功能：勾画组件、错题本组件、再练一次环节

* **关键业务假设与约束**: 
  - 系统需支持1000名学生同时进行巩固练习，核心业务流程P95响应时间<2秒
  - 与用户中心服务、内容平台服务、教师端服务等外部系统集成
  - 基于掌握度计算的智能推题策略和再练一次推荐机制
  - 采用PostgreSQL存储核心业务数据，ClickHouse存储分析数据
  - 进入练习时自动创建或恢复会话，无需客户端主动创建
  - 掌握度变化仅在练习完成后展示，作答过程中不显示

## 2. 用户场景及对应接口分析 (针对需求范围内的所有核心场景)

### 2.1 场景/用户故事：学生进入巩固练习

* **2.1.1 场景描述**: 初中、高中学生在完成AI课程后进入巩固练习，系统自动检查是否有未完成的练习会话，如有则恢复，如无则自动创建新的练习会话。学生可以查看练习状态和预估题目数量，点击"进入练习"或"继续练习"直接开始答题。

* **2.1.2 主要参与者/系统**: 学生客户端、巩固练习服务、用户中心服务、内容平台服务、教师端服务、掌握度计算服务、推题策略服务

* **2.1.3 场景所需接口初步识别**:
    | 接口初步用途/名称 (服务于本场景)                | 建议HTTP方法 | 建议资源路径                                   | 核心输入数据 (概念性描述)                         | 核心输出数据 (概念性描述)                              | 对应PRD/本场景环节        | 备注 (必要性、特殊考虑等)                               |
    | :---------------------------------------------- | :------------- | :---------------------------------------------------- | :------------------------------------------------ | :----------------------------------------------------- | :------------------------ | :------------------------------------------------------ |
    | `获取练习入口信息并自动创建/恢复会话`                      | `GET`          | `/api/v1/exercise_sessions/enter`             | `学生ID, 课程ID`                                          | `会话ID, 练习状态, 当前题目信息, 预估题目数, 入口按钮类型`                                  | `学科页面练习卡片展示和进入`          | `核心入口接口，自动处理会话创建/恢复逻辑。`                                          |
    | `获取下一题`                                | `GET`         | `/api/v1/exercise_sessions/next_question`                                         | `会话ID`                                    | `题目内容, 题目难度, 题目类型, 选项信息, 题目序号, 进度信息, 是否有下一题`                                                        | `进入练习后获取题目`                           | `基于掌握度的智能推题，刚进入练习时获取第一题。当返回"是否有下一题"为false时表示练习结束。`                                                         |

* **2.1.4 本场景核心数据实体交互分析**:
    * **本场景涉及的核心数据实体**: ExerciseSession(练习会话), Student(学生), Course(课程), Question(题目), KnowledgePointMastery(知识点掌握度)
    * **实体间主要交互关系**: "获取练习入口信息"接口会自动检查ExerciseSession表中是否有未完成的会话，如有则恢复，如无则创建新记录；会话创建时会查询KnowledgePointMastery表获取掌握度信息用于推题策略；题目获取会结合推题策略返回适合的题目内容。

* **2.1.5 本场景初步技术与非功能性考量 (可选)**:
    * **安全性考虑**: 所有接口需要JWT Token认证，验证学生身份和课程访问权限
    * **性能预期**: 练习入口接口响应需在1秒内，题目获取接口响应需在2秒内
    * **事务性要求**: 会话创建/恢复涉及多个服务调用，需要保证数据一致性
    * **依赖关系**: 依赖用户中心服务的身份验证，依赖内容平台服务的题目内容，依赖掌握度策略服务的掌握度数据

---

### 2.2 场景/用户故事：学生作答题目并获得反馈

* **2.2.1 场景描述**: 学生在练习过程中提交题目答案，系统判断答案正确性，记录作答详情，提供即时的情感化反馈（正确/错误/连胜/难度调整），并自动推进到下一题或完成练习。支持勾画标记和错题收藏功能。注意：作答过程中不显示掌握度变化。

* **2.2.2 主要参与者/系统**: 学生客户端、巩固练习服务、掌握度计算服务、推题策略服务、内容平台服务

* **2.2.3 场景所需接口初步识别**:
    | 接口初步用途/名称 (服务于本场景)                | 建议HTTP方法 | 建议资源路径                                   | 核心输入数据 (概念性描述)                         | 核心输出数据 (概念性描述)                              | 对应PRD/本场景环节        | 备注 (必要性、特殊考虑等)                               |
    | :---------------------------------------------- | :------------- | :---------------------------------------------------- | :------------------------------------------------ | :----------------------------------------------------- | :------------------------ | :------------------------------------------------------ |
    | `提交题目答案并获取反馈`                      | `POST`          | `/api/v1/exercise_sessions/submit_answer`             | `会话ID, 题目ID, 学生答案, 作答时长`                                          | `答案正确性, 反馈类型, 连胜状态, 下一题信息或完成状态`                                  | `学生提交答案后`          | `核心作答接口，包含反馈和下一题推荐逻辑。`                                          |
    | `保存勾画记录`                          | `POST`         | `/api/v1/exercise_sessions/drawings`                                              | `会话ID, 题目ID, 勾画工具类型, 坐标数据`                                    | `保存状态确认`    | `学生进行勾画操作时` | `辅助功能，异步保存即可。`                                          |
    | `添加错题到错题本`                                | `POST`         | `/api/v1/wrong_questions`                                         | `学生ID, 题目ID, 错因标签, 添加方式`                                    | `添加状态确认`                                                        | `答错题目自动添加或手动添加`                           | `支持自动和手动两种添加方式。`                                                         |

* **2.2.4 本场景核心数据实体交互分析**:
    * **本场景涉及的核心数据实体**: AnswerRecord(作答记录), ExerciseSession(练习会话), DrawingRecord(勾画记录), WrongQuestion(错题记录), KnowledgePointMastery(知识点掌握度)
    * **实体间主要交互关系**: "提交题目答案"接口会在AnswerRecord表中创建作答记录，更新ExerciseSession的进度，后台异步更新KnowledgePointMastery的掌握度；如果答错会在WrongQuestion表中自动添加错题记录；勾画操作会在DrawingRecord表中保存勾画数据。

* **2.2.5 本场景初步技术与非功能性考量 (可选)**:
    * **安全性考虑**: 答案提交需要防止重复提交和恶意刷题，需要验证会话有效性
    * **性能预期**: 答案提交接口响应需在2秒内，勾画保存可以异步处理
    * **事务性要求**: 答案提交涉及作答记录保存、进度更新等，需要保证数据一致性
    * **依赖关系**: 依赖掌握度计算服务的后台计算能力，依赖推题策略服务的下一题推荐

---

### 2.3 场景/用户故事：学生中途退出后继续练习

* **2.3.1 场景描述**: 学生在练习过程中点击退出按钮，系统自动保存当前进度。学生稍后重新进入时，通过场景2.1的"获取练习入口信息并自动创建/恢复会话"接口自动恢复到上次退出的题目，继续计时和作答，确保练习进度不丢失。

* **2.3.2 主要参与者/系统**: 学生客户端、巩固练习服务、缓存服务(Redis)

* **2.3.3 场景所需接口初步识别**:
    | 接口初步用途/名称 (服务于本场景)                | 建议HTTP方法 | 建议资源路径                                   | 核心输入数据 (概念性描述)                         | 核心输出数据 (概念性描述)                              | 对应PRD/本场景环节        | 备注 (必要性、特殊考虑等)                               |
    | :---------------------------------------------- | :------------- | :---------------------------------------------------- | :------------------------------------------------ | :----------------------------------------------------- | :------------------------ | :------------------------------------------------------ |
    | `退出练习会话`                      | `PUT`          | `/api/v1/exercise_sessions/exit`             | `会话ID`                                          | `退出状态确认`                                  | `学生点击退出时`          | `自动保存进度，无需额外参数。`                                          |

* **2.3.4 本场景核心数据实体交互分析**:
    * **本场景涉及的核心数据实体**: ExerciseSession(练习会话), SessionProgress(会话进度缓存)
    * **实体间主要交互关系**: "退出练习会话"接口会更新ExerciseSession表的当前题目索引和状态，同时在Redis中缓存进度信息；重新进入时通过场景2.1的"获取练习入口信息并自动创建/恢复会话"接口自动恢复会话状态，无需额外的继续练习接口。

* **2.3.5 本场景初步技术与非功能性考量 (可选)**:
    * **安全性考虑**: 进度保存需要验证会话归属，防止恶意修改他人进度
    * **性能预期**: 退出接口响应需在1秒内
    * **事务性要求**: 进度保存需要保证数据库和缓存的一致性
    * **依赖关系**: 依赖Redis缓存服务的高可用性，需要缓存失效策略

---

### 2.4 场景/用户故事：练习完成后生成学习报告

* **2.4.1 场景描述**: 学生完成所有练习题目后，系统自动跳转到学习报告页面，展示IP角色反馈、掌握度变化动效、学习时长、答题数量、正确率等统计数据，并根据掌握度情况判断是否推荐再练一次。

* **2.4.2 主要参与者/系统**: 学生客户端、学习报告服务、掌握度计算服务、巩固练习服务

* **2.4.3 场景所需接口初步识别**:
    | 接口初步用途/名称 (服务于本场景)                | 建议HTTP方法 | 建议资源路径                                   | 核心输入数据 (概念性描述)                         | 核心输出数据 (概念性描述)                              | 对应PRD/本场景环节        | 备注 (必要性、特殊考虑等)                               |
    | :---------------------------------------------- | :------------- | :---------------------------------------------------- | :------------------------------------------------ | :----------------------------------------------------- | :------------------------ | :------------------------------------------------------ |
    | `获取学习报告`                      | `GET`          | `/api/v1/study_reports`             | `会话ID, 学生ID`                                          | `掌握度变化, 学习统计, IP反馈, 再练推荐, 题目详情列表`                                  | `练习完成后查看报告`          | `报告生成和查看合并，包含掌握度变化展示。`                                          |

* **2.4.4 本场景核心数据实体交互分析**:
    * **本场景涉及的核心数据实体**: StudyReport(学习报告), ExerciseSession(练习会话), AnswerRecord(作答记录), KnowledgePointMastery(知识点掌握度)
    * **实体间主要交互关系**: "获取学习报告"接口会查询AnswerRecord表统计作答情况，查询KnowledgePointMastery表计算掌握度变化，在StudyReport表中创建或获取报告记录；如果掌握度未达标会自动触发再练一次推荐。

* **2.4.5 本场景初步技术与非功能性考量 (可选)**:
    * **安全性考虑**: 报告查看需要验证学生身份，确保只能查看自己的报告
    * **性能预期**: 报告查看接口响应需在2秒内
    * **事务性要求**: 报告生成涉及多个数据统计，需要保证数据一致性
    * **依赖关系**: 依赖掌握度计算服务的准确性

---

### 2.5 场景/用户故事：学生查看错题本和学习历史

* **2.5.1 场景描述**: 学生通过客户端查看自己的错题本，浏览历史练习记录和学习报告。系统需要快速检索学生的错题记录、练习历史和学习成果，支持按时间、课程等维度进行筛选和排序。

* **2.5.2 主要参与者/系统**: 学生客户端、巩固练习服务、学习报告服务

* **2.5.3 场景所需接口初步识别**:
    | 接口初步用途/名称 (服务于本场景)                | 建议HTTP方法 | 建议资源路径                                   | 核心输入数据 (概念性描述)                         | 核心输出数据 (概念性描述)                              | 对应PRD/本场景环节        | 备注 (必要性、特殊考虑等)                               |
    | :---------------------------------------------- | :------------- | :---------------------------------------------------- | :------------------------------------------------ | :----------------------------------------------------- | :------------------------ | :------------------------------------------------------ |
    | `获取学生错题本`                      | `GET`          | `/api/v1/wrong_questions`             | `学生ID, 分页参数, 筛选条件`                                          | `错题列表, 错因标签, 添加时间, 分页信息`                                  | `学生查看错题本时`          | `支持分页和多维度筛选。`                                          |
    | `获取练习历史记录`                          | `GET`         | `/api/v1/exercise_sessions/history`                                              | `学生ID, 分页参数, 时间范围, 课程筛选`                                    | `练习会话列表, 练习状态, 完成时间, 分页信息`    | `学生查看练习历史时` | `支持按时间和课程筛选。`                                          |
    | `删除错题记录`                                | `DELETE`         | `/api/v1/wrong_questions`                                         | `错题ID, 学生ID`                                    | `删除状态确认`                                                        | `学生管理错题本时`                           | `支持错题本管理功能。`                                                         |
    | `手动添加错题`                                | `POST`         | `/api/v1/wrong_questions/manual`                                         | `学生ID, 题目ID, 错因标签`                                    | `添加状态确认`                                                        | `学生手动收藏题目时`                           | `支持手动添加正确题目到错题本。`                                                         |

* **2.5.4 本场景核心数据实体交互分析**:
    * **本场景涉及的核心数据实体**: WrongQuestion(错题记录), ExerciseSession(练习会话), StudyReport(学习报告), Question(题目)
    * **实体间主要交互关系**: 错题本查询会从WrongQuestion表中按学生ID筛选记录，关联Question表获取题目信息；练习历史查询会从ExerciseSession表中按学生ID和时间范围筛选；删除和添加操作会直接修改WrongQuestion表。

* **2.5.5 本场景初步技术与非功能性考量 (可选)**:
    * **安全性考虑**: 所有查询需要验证学生身份，确保数据隔离
    * **性能预期**: 列表查询接口响应需在1秒内，支持分页减少数据传输量
    * **事务性要求**: 错题删除和添加操作需要保证数据一致性
    * **依赖关系**: 依赖内容平台服务获取题目详细信息

---

## 3. 汇总接口清单与初步技术考量

### 3.1 最终汇总的接口需求清单

| 序号 | 最终接口用途/名称 (全局视角)                        | 建议HTTP方法 | 建议资源路径 (草案)                                    | 核心输入数据 (概念性，已整合)                           | 核心输出数据 (概念性，已整合)                             | 服务的主要PRD场景/模块                                 | 初步技术/非功能性备注                                                                 |
| :--- | :-------------------------------------------------- | :------------- | :----------------------------------------------------- | :---------------------------------------------------- | :------------------------------------------------------- | :----------------------------------------------------- | :------------------------------------------------------------------------------------ |
| 1    | `获取练习入口信息并自动创建/恢复会话`                                  | `GET`         | `/api/v1/exercise_sessions/enter`                                               | `学生ID, 课程ID`                        | `会话ID, 练习状态, 当前题目信息, 预估题目数, 入口按钮类型`                           | `巩固练习入口管理 (PRD 2.1)`                                   | `核心入口接口，自动处理会话创建/恢复，1秒内响应。`                                         |
| 2    | `获取下一题`                                | `GET`          | `/api/v1/exercise_sessions/next_question`  | `会话ID`                                                | `题目内容, 题目难度, 题目类型, 选项信息, 题目序号, 进度信息, 是否有下一题`        | `练习环节执行 (PRD 2.1), 智能推题策略`         | `基于掌握度的实时推题，进入练习后获取第一题，当返回"是否有下一题"为false时表示练习结束可获取学习报告，2秒内响应。`                                                        |
| 3    | `提交题目答案并获取反馈`                         | `POST`          | `/api/v1/exercise_sessions/submit_answer` | `会话ID, 题目ID, 学生答案, 作答时长`                                    | `答案正确性, 反馈类型, 连胜状态, 下一题信息或完成状态`                            | `练习环节执行, 题目间转场反馈 (PRD 2.1)`                           | `核心作答接口，包含反馈和推题逻辑，防重复提交，2秒内响应。`                                              |
| 4    | `保存勾画记录`                                | `POST`         | `/api/v1/exercise_sessions/drawings`                                         | `会话ID, 题目ID, 勾画工具类型, 坐标数据, 颜色信息`                                    | `保存状态确认`                                                        | `勾画组件功能 (PRD 2.2)`                           | `辅助功能，异步保存，可以适当延迟响应。`                                                         |
| 5    | `添加错题到错题本`                                | `POST`         | `/api/v1/wrong_questions`                                         | `学生ID, 题目ID, 错因标签, 添加方式(自动/手动), 作答记录ID`                                    | `添加状态确认`                                                        | `错题本组件功能 (PRD 2.2)`                           | `支持自动和手动添加，需要去重处理，防止重复添加。`                                                         |
| 6    | `退出练习会话`                                | `PUT`         | `/api/v1/exercise_sessions/exit`                                         | `会话ID`                                    | `退出状态确认`                                                        | `中途退出保存 (PRD 4.场景2)`                           | `自动保存进度，支持缓存和数据库双重保障，1秒内响应。`                                                         |
| 7    | `获取学习报告`                                | `GET`         | `/api/v1/study_reports`                                         | `会话ID, 学生ID`                                    | `掌握度变化, 学习统计, IP反馈, 再练推荐, 题目详情列表`                                                        | `学习报告生成和展示 (PRD 4.场景3)`                           | `报告生成和查看合并，包含掌握度变化展示，2秒内响应。`                                                         |
| 8   | `获取学生错题本`                                | `GET`         | `/api/v1/wrong_questions`                                         | `学生ID, 分页参数, 筛选条件`                                    | `错题列表, 错因标签, 添加时间, 分页信息`                                                        | `错题本组件功能 (PRD 2.2)`                           | `支持分页和多维度筛选，1秒内响应。`                                                         |
| 9   | `获取练习历史记录`                                | `GET`         | `/api/v1/exercise_sessions/history`                                         | `学生ID, 分页参数, 时间范围, 课程筛选`                                    | `练习会话列表, 练习状态, 完成时间, 分页信息`                                                        | `学习历史查看 (PRD 4.场景4)`                           | `支持按时间和课程筛选，1秒内响应。`                                                         |
| 10   | `删除错题记录`                                | `DELETE`         | `/api/v1/wrong_questions`                                         | `错题ID, 学生ID`                                    | `删除状态确认`                                                        | `错题本管理 (PRD 2.2)`                           | `支持错题本管理功能，需要权限验证。`                                                         |
| 11   | `手动添加错题`                                | `POST`         | `/api/v1/wrong_questions/manual`                                         | `学生ID, 题目ID, 错因标签`                                    | `添加状态确认`                                                        | `错题本组件功能 (PRD 2.2)`                           | `支持手动添加正确题目到错题本。`                                                         |

### 3.2 整体非功能性需求初步考虑 (针对整个服务/模块的接口)

* **整体安全性**: 所有接口默认需要JWT Token认证，验证学生身份和课程访问权限。核心作答接口需要防重复提交和恶意刷题保护。错题本和学习历史接口需要确保数据隔离，学生只能访问自己的数据。

* **整体性能预期**: 核心查询接口(练习入口、题目获取)P95响应时间不超过2秒，作答提交接口P95响应时间不超过2秒。系统需支持1000名学生同时进行练习，每秒处理100次作答提交请求。缓存策略优化热点数据访问。

* **数据一致性策略**: 练习会话管理、作答记录保存要求强一致性，确保数据不丢失。掌握度计算可接受最终一致性，通过异步事件机制保证数据最终同步。进度保存需要缓存和数据库双重保障。

* **API版本管理策略初步想法**: 采用URL路径版本控制 `/api/v1/...`，支持向后兼容的接口演进。新增字段不影响现有客户端，删除字段通过版本升级管理。

* **服务集成策略**: 与用户中心服务、内容平台服务、教师端服务、掌握度策略服务等外部系统集成，采用gRPC同步调用和Kafka异步事件相结合的方式。实现服务熔断和降级机制，确保核心功能可用性。

* **会话管理策略**: 进入练习时自动创建或恢复会话，无需客户端主动创建。再练一次本质上是基于掌握度判断自动创建的新练习会话，与正常进入练习的逻辑相同。

## 4. 自检清单 (Self-Checklist for Holistic Analysis)

| 检查类别           | 检查项                                                                                                   | 评估结果 (✅/⚠️/❌) | 具体说明/备注 (若为⚠️或❌，请说明原因或需澄清点)                                                                                                                                   |
| :----------------- | :------------------------------------------------------------------------------------------------------- | :------------------- | :-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **需求覆盖度** | 1.1 本次分析是否覆盖了指定需求范围（PRD《学生端巩固练习 V1.0》）中所有涉及接口交互的核心用户场景和功能模块？                 | ✅                    | 已覆盖巩固练习入口管理
、练习环节执行、题目间转场反馈、学习报告生成、错题本组件等所有核心功能模块。                                                                                                                                                                                         |
|                    | 1.2 是否存在PRD中描述的、需要API支持但未在本分析中识别出对应接口的场景或功能？                               | ✅                   | 所有PRD中描述的用户场景都已识别对应接口，包括5个核心场景的11个接口需求，已根据反馈优化了会话管理逻辑。                                                                                                                                                                                         |
| **接口必要性** | 2.1 汇总清单中的每一个接口是否都能明确追溯到PRD中的一个或多个具体需求点/用户场景，且绝对必要？                 | ✅                    | 每个接口都明确标注了对应的PRD场景和模块，具有明确的业务价值和必要性。已移除不必要的接口，如单独的会话创建接口。                                                                                                                                                                                         |
|                    | 2.2 是否存在任何可以合并或移除的冗余接口？（是否已达到最小化接口集的目标？）                                     | ✅                    | 接口设计遵循单一职责原则，已合并会话创建/恢复逻辑，再练一次复用正常练习逻辑，达到最小化接口集目标。                                                                                                                                                                                         |
| **信息完整性** | 3.1 对于每个分析的场景（2.X节），其描述、参与者、所需接口、**核心数据实体交互分析**和**初步技术考量**是否清晰完整？ | ✅                    | 每个场景都包含完整的描述、参与者系统、接口识别、数据实体交互分析和技术考量，信息完整且结构清晰。                                                                                                                                                                                         |
|                    | 3.2 对于汇总接口清单（3.1节）中的每个接口，其用途、建议方法/路径、核心输入/输出是否均已初步定义且逻辑合理？ | ✅                    | 所有11个接口的用途、HTTP方法、资源路径、输入输出数据、服务场景和技术备注都已明确定义，逻辑合理。                                                                                                                                                                                         |
|                    | 3.3 本模板中所有要求填写的信息（如整体概述、各场景分析、汇总清单等）是否均已基于输入文档分析并填写？             | ✅                    | 所有模板要求的信息都已基于PRD、TD、DE、DD文档进行分析并完整填写，并根据反馈进行了优化。                                                                                                                                                                                         |
| **初步技术考量** | 4.1 (若填写) "整体非功能性需求初步考虑"中的内容是否合理，并基于PRD/TD有初步依据？                                | ✅                      | 整体非功能性需求考虑了安全性、性能、数据一致性、版本管理、服务集成、会话管理等方面，基于PRD的性能要求和TD的架构设计。                                                                                                                                                                                         |
| **输出质量** | 5.1 本次分析的产出（即本文档）是否已达到可供后续步骤（接口使用文档、接口定义文档）使用的清晰度和完整度？           | ✅                    | 文档结构清晰，接口定义完整，已根据反馈优化了会话管理逻辑，包含了后续接口设计和开发所需的所有关键信息。                                                                                                                                                                                         |

---
**确认声明**: 我已完成上述自检，并尽我所能确保本次对**PRD《学生端巩固练习 V1.0》整体需求范围**的分析结果的质量和准确性。已根据反馈优化了接口设计，确保符合实际用户场景和业务逻辑。

--- API需求分析产出完毕，等待您的命令，指挥官
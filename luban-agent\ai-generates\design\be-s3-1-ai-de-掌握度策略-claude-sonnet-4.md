# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** 产品需求文档 - 掌握度策略系统 v1.0
- **PRD版本号：** v1.0
- **提取日期：** 2025-06-05

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 用户分层 | 基于学生考试成绩和学校信息进行的用户能力分层，用于个性化学习路径规划 | 2.1, 3.1, 11.2 |
| 知识点掌握度 | 根据学生答题表现动态计算的知识点掌握程度，用于学习内容动态调整 | 2.1, 3.2, 11.2 |
| 外化掌握度 | 向学生展示的课程掌握情况的可视化指标，用于学习反馈和激励 | 2.1, 3.3, 11.2 |
| 目标掌握度 | 根据学习目标计算应达到的掌握度标准（V1.0暂不实现） | 2.1, 3.4, 11.2 |
| 答题记录 | 学生每次答题的详细记录，包含题目信息、答题结果、时间等 | 3.2, 4场景2 |
| 薄弱知识点 | 自动标识需要重点学习的知识点，为巩固练习推荐提供支持 | 2.2, 3.5 |
| 课程信息 | 课程的基本信息和配置，包含难度等级、练习配置等 | 3.3, 3.5 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`用户分层`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 | 初始值/默认值 | 是否为标识符? | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ------------------- | ------------- | ------------- | -------------------------- |
| 用户ID | 学生的唯一标识符，关联到用户中心服务 | 整数 | 唯一, 非空, 外键 |  | 主键组成部分 | 3.1 |
| 学科ID | 学科的唯一标识符，关联到内容平台服务 | 整数 | 非空, 外键 |  | 主键组成部分 | 3.1 |
| 用户单科排名百分比 | 学生在年级中的排名，以百分比形式表示 | 数值型 (百分比) | 0-100 |  |  | 3.1.1 |
| 校准系数a | 根据学校本科录取率确定的校准系数 | 数值型 (小数) | [1, 3] |  |  | 3.1.1 |
| 校准系数b | 根据生源波动情况确定的校准系数 | 数值型 (小数) | >0 |  |  | 3.1.1 |
| 学科能力排名 | 计算得出的学科能力排名百分比 | 数值型 (小数) | [0,1]，向上取整 |  |  | 3.1.1 |
| 分层等级 | 用户能力分层结果 | 枚举型 | S+, S, A, B, C |  |  | 3.1.1 |
| 分层掌握系数 | 不同分层对应的掌握度系数 | 数值型 (小数) | S+为1.2，S为1.0，A为0.8，B为0.5，C为0.4 |  |  | 3.2.1 |

**实体：`知识点掌握度`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 | 约束条件 / 业务规则 | 初始值/默认值 | 是否为标识符? | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | -------- | ------------------- | ------------- | ------------- | -------------------------- |
| 用户ID | 学生的唯一标识符 | 整数 | 唯一, 非空, 外键 |  | 主键组成部分 | 3.2 |
| 知识点ID | 知识点的唯一标识符，关联到内容平台服务 | 整数 | 非空, 外键 |  | 主键组成部分 | 3.2 |
| 当前掌握度 | 学生对该知识点的当前掌握程度 | 数值型 (小数) | [0,1] | 学科初始值 × 分层掌握系数 |  | 3.2.1, 3.2.2 |
| 初始掌握度 | 基于用户分层设置的初始掌握度 | 数值型 (小数) | [0,1] | 0.3 × 分层掌握系数 |  | 3.2.1 |
| 累计答题次数 | 该知识点的累计答题次数 | 整数 | ≥0 | 0 |  | 3.2.2 |
| 最后更新时间 | 掌握度最后更新的时间戳 | 时间戳 | 非空 |  |  | 3.2.2 |

**实体：`外化掌握度`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 | 约束条件 / 业务规则 | 初始值/默认值 | 是否为标识符? | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | -------- | ------------------- | ------------- | ------------- | -------------------------- |
| 用户ID | 学生的唯一标识符 | 整数 | 唯一, 非空, 外键 |  | 主键组成部分 | 3.3 |
| 知识点ID | 知识点的唯一标识符 | 整数 | 非空, 外键 |  | 主键组成部分 | 3.3 |
| 外化掌握度值 | 向学生展示的掌握度百分比 | 数值型 (小数) | [0,1]，四舍五入保留2位小数 |  |  | 3.3.1 |
| 掌握度上限 | 基于课程难度确定的掌握度上限 | 数值型 (小数) | 高为1，中为0.9，低为0.7 |  |  | 3.3.1 |
| 累计答题数 | 该知识点累计答题数量 | 整数 | ≥0 | 0 |  | 3.3.2 |
| 是否达到更新条件 | 是否满足外化掌握度更新条件 | 布尔型 | 累计答题≥5题 或 课程完成 | false |  | 3.3.2 |

**实体：`答题记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 | 约束条件 / 业务规则 | 初始值/默认值 | 是否为标识符? | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | -------- | ------------------- | ------------- | ------------- | -------------------------- |
| 记录ID | 答题记录的唯一标识符 | 整数 | 唯一, 非空, 自增 |  | 主键 | 3.2.2 |
| 用户ID | 答题学生的ID | 整数 | 非空, 外键 |  |  | 3.2.2 |
| 题目ID | 题目的唯一标识符，关联到内容平台服务 | 整数 | 非空, 外键 |  |  | 3.2.2 |
| 知识点ID | 题目对应的知识点ID | 整数 | 非空, 外键 |  |  | 3.2.2 |
| 题目难度系数 | 题目的难度系数 | 数值型 (小数) | L1为0.3，L2为0.5，L3为0.7，L4为0.9，L5为1.1 |  |  | 3.2.2 |
| 答题结果 | 答题的正确性 | 枚举型 | 正确、错误、部分正确 |  |  | 3.2.2 |
| 是否首次答题 | 是否为该题目的首次答题 | 布尔型 | true/false |  |  | 3.2.2 |
| 题目类型 | 客观题或主观题 | 枚举型 | 客观题、主观题 |  |  | 4场景2 |
| 作答时间 | 答题用时（秒） | 整数 | ≥0 |  |  | 4场景2 |
| 掌握度增量 | 本次答题产生的掌握度变化 | 数值型 (小数) | 可正可负 |  |  | 3.2.2 |
| 答题时间戳 | 答题的具体时间 | 时间戳 | 非空 |  |  | 3.2.2 |

**实体：`薄弱知识点`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 | 约束条件 / 业务规则 | 初始值/默认值 | 是否为标识符? | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | -------- | ------------------- | ------------- | ------------- | -------------------------- |
| 用户ID | 学生的唯一标识符 | 整数 | 唯一, 非空, 外键 |  | 主键组成部分 | 3.5 |
| 知识点ID | 薄弱知识点的ID | 整数 | 非空, 外键 |  | 主键组成部分 | 3.5 |
| 是否薄弱 | 是否被识别为薄弱知识点 | 布尔型 | 外化掌握度/目标掌握度≤0.7 且 知识点掌握度≤0.5 |  |  | 3.5 |
| 识别时间 | 被识别为薄弱知识点的时间 | 时间戳 | 非空 |  |  | 3.5 |
| 推荐优先级 | 推荐练习的优先级 | 整数 | 1-10 |  |  | 2.2 |

**实体：`课程信息`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 | 约束条件 / 业务规则 | 初始值/默认值 | 是否为标识符? | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | -------- | ------------------- | ------------- | ------------- | -------------------------- |
| 课程ID | 课程的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 3.3, 3.5 |
| 知识点ID | 课程对应的知识点ID（V1.0中1课=1知识点） | 整数 | 非空, 外键 |  |  | 3.3 |
| 课程难度 | 课程的难度等级 | 枚举型 | 高、中、低 |  |  | 3.3.1 |
| 是否有巩固练习 | 知识点是否配置了巩固练习 | 布尔型 | true/false |  |  | 3.5 |
| 是否有拓展练习 | 知识点是否配置了拓展练习 | 布尔型 | true/false |  |  | 3.5 |
| 巩固练习题目数量 | 巩固练习的题目数量 | 整数 | ≥0 | 0 |  | 3.5 |
| 默认掌握度 | 无题目课程的默认掌握度 | 数值型 (小数) | [0,1] | 0.2 |  | 4场景2 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 | 实体 A          | 实体 B            | 基数 | 外键 | PRD中对应的章节号 |
| ------- | --------------- | ----------------- | ---- | ---- | ----------------- |
| 用户属于某个分层等级 | 用户分层 | 用户（用户中心服务） | N:1 | 用户分层.用户ID -> 用户中心.用户ID | 3.1 |
| 分层对应特定学科 | 用户分层 | 学科（内容平台服务） | N:1 | 用户分层.学科ID -> 内容平台.学科ID | 3.1 |
| 用户对知识点有掌握度 | 知识点掌握度 | 用户（用户中心服务） | N:1 | 知识点掌握度.用户ID -> 用户中心.用户ID | 3.2 |
| 掌握度对应特定知识点 | 知识点掌握度 | 知识点（内容平台服务） | N:1 | 知识点掌握度.知识点ID -> 内容平台.知识点ID | 3.2 |
| 外化掌握度基于知识点掌握度 | 外化掌握度 | 知识点掌握度 | 1:1 | 外化掌握度.用户ID,知识点ID -> 知识点掌握度.用户ID,知识点ID | 3.3 |
| 答题记录关联用户 | 答题记录 | 用户（用户中心服务） | N:1 | 答题记录.用户ID -> 用户中心.用户ID | 3.2.2 |
| 答题记录关联题目 | 答题记录 | 题目（内容平台服务） | N:1 | 答题记录.题目ID -> 内容平台.题目ID | 3.2.2 |
| 答题记录关联知识点 | 答题记录 | 知识点（内容平台服务） | N:1 | 答题记录.知识点ID -> 内容平台.知识点ID | 3.2.2 |
| 薄弱知识点基于掌握度判断 | 薄弱知识点 | 知识点掌握度 | 1:1 | 薄弱知识点.用户ID,知识点ID -> 知识点掌握度.用户ID,知识点ID | 3.5 |
| 课程对应知识点 | 课程信息 | 知识点（内容平台服务） | 1:1 | 课程信息.知识点ID -> 内容平台.知识点ID | 3.3 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 | 必需的存储输入属性 | 输出属性 | PRD中对应的章节号 / 公式引用 |
| ------------- | ------------------ | -------- | -------------------------- |
| 学科能力排名计算（高中用户分层） | 用户单科排名百分比, 校准系数a, 校准系数b | 学科能力排名, 分层等级 | 3.1.1 |
| 知识点初始掌握度设置 | 学科初始值(0.3), 分层掌握系数 | 初始掌握度 | 3.2.1 |
| 单次答题掌握度增量（答对） | 题目难度系数, 用户当前掌握度 | 掌握度增量 | 3.2.2 |
| 单次答题掌握度增量（答错） | 题目难度系数, 用户当前掌握度, 答错补偿系数 | 掌握度增量 | 3.2.2 |
| 外化掌握度计算 | 知识点掌握度, 掌握度上限（基于课程难度） | 外化掌握度值 | 3.3.1 |
| 目标掌握度计算（V1.0暂不实现） | 目标难度系数, 单课难度系数, 考试频率 | 目标掌握度 | 3.4 |
| 薄弱知识点判断 | 外化掌握度, 目标掌握度, 知识点掌握度 | 是否薄弱知识点 | 3.5 |
| 重复答题衰减计算 | 是否首次答题, 之前答题结果, 衰减系数 | 调整后的掌握度增量 | 4场景2 |
| 主观题权重调整 | 作答时间, 权重系数(0.5) | 调整后的掌握度增量 | 4场景2 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 | 更新频次 | PRD中对应的章节号 |
| ----------------------------- | ------------- | -------- | ----------------- |
| 用户分层.学科能力排名 | 学生考试成绩更新，学校信息变更 | 每次考试周期或数据更新时 | 3.1 |
| 知识点掌握度.当前掌握度 | 学生每次完成答题后 | 实时/每次答题 | 3.2.2 |
| 知识点掌握度.累计答题次数 | 学生每次答题后 | 实时/每次答题 | 3.2.2 |
| 外化掌握度.外化掌握度值 | 累计答题≥5题 或 课程完成且题目<5 或 课程完成且无题目 | 满足条件时更新 | 3.3.2 |
| 外化掌握度.累计答题数 | 学生每次答题后 | 实时/每次答题 | 3.3.2 |
| 答题记录.* | 学生每次答题后 | 实时/每次答题 | 3.2.2 |
| 薄弱知识点.是否薄弱 | 外化掌握度或目标掌握度变化时 | 掌握度更新后触发判断 | 3.5 |
| 薄弱知识点.识别时间 | 被识别为薄弱知识点时 | 状态变化时 | 3.5 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 | PRD中对应的章节号 |
| -------------------- | --------------------------- | ----------------- |
| 用户单科排名百分比 | 学校提供，或对接学情系统获取 | 3.1.1, 2.2 |
| 学校本科录取率 | 学校基础信息，用于计算校准系数a | 3.1.1 |
| 学校历年排名数据 | 学校质量信息，用于计算校准系数b | 3.1.1 |
| 题目信息（难度、内容等） | 内容平台服务提供 | 3.2.2 |
| 知识点信息 | 内容平台服务的业务树提供 | 3.2 |
| 课程配置信息 | 课程管理系统或内容平台提供 | 3.3, 3.5 |
| 用户基础信息 | 用户中心服务提供 | 全文 |
| 学校基础信息 | 用户中心服务提供 | 3.1.1 |
| 巩固练习配置 | 内容平台或课程管理系统提供 | 3.5 |
| 拓展练习配置 | 内容平台或课程管理系统提供 | 3.5 |

---

## 7. 当前版本明确排除的数据需求

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 | PRD中说明的排除原因 | PRD中对应的章节号 |
| ---------------------------- | ------------------- | ----------------- |
| 知识点迁移性分析相关数据 | 考虑知识点间的关联性和迁移效应，建议V2.0再考虑 | 2.3 |
| 个性化学习路径推荐数据 | 基于掌握度的完整学习路径规划，建议后续迭代再考虑 | 2.3 |
| 目标掌握度相关数据存储 | V1.0暂不实现目标掌握度设定功能 | 2.1, 3.4 |
| 知识点间依赖关系数据 | V1.0简化模型，不考虑知识点间复杂依赖 | 2.3 |
| 学习路径历史数据 | 完整学习路径跟踪，后续版本考虑 | 2.3 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | -------- | ---- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别用户分层、知识点掌握度、外化掌握度、答题记录、薄弱知识点、课程信息等核心实体 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏？                                                         | ✅ | 已通过业务流程分析补充了答题记录、课程信息等隐含实体 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已从计算公式中提取所有必需的输入参数和输出结果 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | 所有属性均基于PRD原文描述，数据类型和约束条件符合业务规则 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确各实体间的关联关系和外键指向 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 已体现掌握度计算的依赖关系和公共服务的引用关系 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 已列出所有计算公式的输入参数，包括系数、当前值等 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 已明确实时更新、周期更新等不同更新机制 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 已列出V1.0明确排除的功能，如知识点迁移性、学习路径推荐等 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表或PRD正文中的常用名称保持一致或有清晰映
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | 所有属性均基于PRD原文描述，数据类型和约束条件符合业务规则 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确各实体间的关联关系和外键指向 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 已体现掌握度计算的依赖关系和公共服务的引用关系 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 已列出所有计算公式的输入参数，包括系数、当前值等 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 已明确实时更新、周期更新等不同更新机制 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 已列出V1.0明确排除的功能，如知识点迁移性、学习路径推荐等 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 已使用PRD中的标准术语，如"掌握度"、"分层"、"知识点"等 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 所有提取项都标注了对应的PRD章节号和公式引用 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 所有信息均基于PRD原文，推断部分已明确标注 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 部分计算公式的具体实现细节需要与开发团队确认，如衰减系数的具体计算方式 |

---

## 9. 待与PM确认的问题

**以下问题在PRD中描述不够明确，建议与产品经理进一步澄清：**

1. **校准系数b的计算公式**：PRD中提到"√（历年排名中位数倒数平均 / 今年排名中位数倒数）"，但历年数据的具体范围（几年）和数据来源需要明确。

2. **重复答题的衰减系数**：PRD中提到重复答题有衰减机制，但具体的衰减系数值和衰减规则需要进一步定义。

3. **部分正确答题的处理**：PRD中提到部分正确的情况，但具体的判分标准和掌握度增量计算方式需要明确。

4. **课程难度等级的判定标准**：PRD中提到课程难度分为高、中、低，但具体的判定标准和数据来源需要确认。

5. **薄弱知识点的推荐优先级算法**：PRD中提到薄弱知识点识别，但推荐的优先级排序算法需要进一步设计。

## 10. 复杂计算的简化假设

**为了支持V1.0版本的快速实现，对以下复杂计算进行了简化处理：**

1. **知识点迁移性**：V1.0版本假设知识点间相互独立，不考虑知识点间的迁移效应对掌握度的影响。

2. **学习遗忘曲线**：V1.0版本不考虑时间因素对掌握度的自然衰减影响，掌握度只增不降（外化掌握度）。

3. **个体差异化参数**：V1.0版本使用统一的计算参数，不考虑学生个体学习特征的差异化调整。

4. **多维度能力评估**：V1.0版本仅基于单科排名进行分层，不考虑多维度能力的综合评估。

---

**任务完成标识：** 

--- PRD数据提取完成，已按模板要求完成质量自查，等待您的命令，指挥官
# PRD - 课中练习推题策略 - V1.0# 一、背景和目标

## 需求背景

在 AI 课堂教学过程中，为了提升学生的专注度和课堂互动感，避免单向知识输入，需在课程中合理穿插练习题组件。
课中练习题主要用于巩固教师讲解内容，帮助学生即时检验对知识点的理解情况，同时通过适当的练习密度与反馈机制，增强课堂节奏感与参与感。
因此，需要制定一套清晰可执行的课中推题策略，确保练习题推送符合教学节奏，兼顾知识巩固的目的。

## 项目收益

1. **提升学习效果：**通过实时练习巩固课堂所学内容，帮助学生在知识点讲解后及时验证理解，降低遗忘率。
2. **增强课堂互动与专注度：**通过答题互动打破单向讲授模式，提高学生参与感和课堂活跃度，延长课堂专注时长。
3. **及时发现学习问题：**通过答错推送相似题，帮助学生暴露理解误区，实现即时纠偏，提高学习精准度。

## 覆盖用户

使用新 AI 课学习的全部用户

# 二、内容数据结构

## 内容结构

![AFnaw4y5nhDgPybpTZXcJdthnTe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_AFnaw4y5nhDgPybpTZXcJdthnTe.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

该图片展示了一个层级结构，描述了从“单节课”开始的内容组织方式，具体分析如下：

**关键元素层级结构与关联：**

1.  **单节课**: 顶层节点，表示一个独立的课程单元。
    *   **练习组件 1**: “单节课”的子分支，代表课程中的第一个练习模块。
        *   **题目1**: “练习组件 1”下的一个具体题目。
        *   **题目2**: “练习组件 1”下的另一个具体题目。
        *   **题目3 系列**: “练习组件 1”下的一个题目组合，由虚线框标识，包含以下并列题目：
            *   题目3
            *   题目 3'
            *   题目 3''
    *   **练习组件 2**: “单节课”的另一个子分支，代表课程中的第二个练习模块。
        *   **题目4 系列**: “练习组件 2”下的一个题目组合，由虚线框标识，包含以下并列题目：
            *   题目4
            *   题目 4'
        *   **题目5 系列**: “练习组件 2”下的另一个题目组合，由虚线框标识，包含以下并列题目：
            *   题目5
            *   题目 5'
            *   题目 5''
            *   题目 5'''

**Mermaid 流程图描述：**

```mermaid
flowchart TD
    A[单节课] --> B[练习组件 1]
    A --> C[练习组件 2]

    B --> Q1[题目1]
    B --> Q2[题目2]
    B --> Group1_EntryPath

    subgraph Group1 [ ]
        style Group1 stroke:#333,stroke-dasharray: 5 5
        direction LR
        G1_Q1[题目3]
        G1_Q2[题目 3']
        G1_Q3[题目 3'']
    end
    Group1_EntryPath ---> Group1

    C --> Group2_EntryPath
    subgraph Group2 [ ]
        style Group2 stroke:#333,stroke-dasharray: 5 5
        direction LR
        G2_Q1[题目4]
        G2_Q2[题目 4']
    end
    Group2_EntryPath ---> Group2

    C --> Group3_EntryPath
    subgraph Group3 [ ]
        style Group3 stroke:#333,stroke-dasharray: 5 5
        direction LR
        G3_Q1[题目5]
        G3_Q2[题目 5']
        G3_Q3[题目 5'']
        G3_Q4[题目 5''']
    end
    Group3_EntryPath ---> Group3
    
    %% Styling for entry paths to make them less prominent visually if needed,
    %% as they are conceptual links to the groups.
    %% For now, standard arrow implies component leads to this group of questions.
```

【============== 图片解析 END ==============】



- 每节课会配置 1-N 个练习组件，在配课环节插入到不同的课中组件之中。
- 每个练习中包含 1-N 个题目，题目有「单题」和「相似题组」两种类型，相似题组中至少有 2 道相似题目。
- 题目之间由教研控制相对顺序，决定了不同题组的推荐顺序
- 每个题目都有知识点内的难度等级标签，课中练习题目的难度较低，基本为 L1，L2 难度

# 三、策略方案

## 题目推荐策略

### 整体流程

![R7zlwS1uOhjgUAbT5MZcdQNan05](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_R7zlwS1uOhjgUAbT5MZcdQNan05.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】
该图片是一个描述“AI课中推题”业务逻辑的流程图。关键元素包括起始点、处理过程、决策判断点以及结束点，它们通过流程线连接，展示了题目推送的完整逻辑。

**流程图关键元素及逻辑关联：**

1.  **起始与准备：**
    *   流程以“AI课中推题”为起点。
    *   随后进入具体的题目推送环节，开始于“第1题”。

2.  **核心答题与反馈循环：**
    *   **答题情况判断（是否答对）：**
        *   **答错路径：**
            *   进一步判断“是否有相似题”。
                *   若“是”，则执行“推出相似题”操作，然后进入后续的“是否还有题目”判断。
                *   若“否”，则直接进入后续的“是否还有题目”判断。
        *   **答对路径：**
            *   直接进入后续的“是否还有题目”判断。

3.  **流程延续与结束：**
    *   **继续推送判断（是否还有题目）：**
        *   若“是”，则系统推送“下一题”，并返回至“是否答对”环节，对新题目进行判断。
        *   若“否”，则整个推题流程“结束”。

**Mermaid 流程图描述：**

```mermaid
flowchart TD
    A[AI课中推题] --> B[第1题]
    B --> C{是否答对}
    C -- 答错 --> D{是否有相似题}
    C -- 答对 --> F{是否还有题目}
    D -- 是 --> E[推出相似题]
    D -- 否 --> F
    E --> F
    F -- 是 --> G[下一题]
    F -- 否 --> H[结束]
    G --> C
```

【============== 图片解析 END ==============】



### 推荐策略

1. 一个练习组件中的多个题目，需要按后台配置顺序依次作答。
2. 相似题题组中，可随机推出一题
3. 如果用户答对，则继续推出下一题。
4. 如果用户答错，有相似题则推出相似题，没有相似题则继续推出下一题。
5. 答错后的相似题只推出一次，无论用户是否回答正确都继续推出后续题目

## 未来可拓展

1. 区分错误的严重程度或错误类型
   如果学生答得非常离谱（比如选了明显错误选项），推两道不同角度的相似题；如果是小错误（比如粗心），推一道即可。
2. 个性化推题
   基于实时答题表现调整题目数量的空间，比如连续答对三题可以略过后面一部分简单题，加快节奏。
---
【Cursor开发中rules配置文件】后端版（适配数据库mysql场景）
---

我有一个[应用类型]系统需求，请帮我设计最优数据库结构。

【任务说明】
分析我提供的需求（图片/文本），设计符合以下要求的数据库：
1. 提取核心业务实体与关系
2. 设计符合规范的表结构
3. 生成完整SQL

**必须遵循的数据库设计原则：**
我有一个[应用类型]系统需求，请帮我设计最优数据库结构。

【任务说明】
分析我提供的需求（图片/文本），设计符合以下要求的数据库：
1. 提取核心业务实体与关系
2. 设计符合规范的表结构
3. 生成完整SQL

**必须遵循的数据库设计原则：**

*  **命名规范**:
  *  表名 `模块名_实体名` (如 `user_op`)。
  *  字段名 `snake_case`，力求**简洁、明确，避免不必要的缩写**。
  *  索引名 `idx_字段名` 或 `uk_字段名` (唯一索引)。
  *  表和关键字段必须有**清晰的中文注释**。
*  **基础字段 (我们团队的标准)**:
  *  所有表必须包含 `id` (bigint unsigned auto_increment primary key), `created_at` (datetime not null), `updated_at` (datetime not null), `deleted_at` (datetime null default null, 用于软删除)。
  *  根据需要包含 `status` (tinyint not null default 1, 注释状态含义), `lang` (varchar(10) not null default 'zh-CN', 注释语言含义), `created_by` (bigint unsigned not null), `updated_by` (bigint unsigned not null)。
*  **字段类型选择**:
  *  优先选择最合适、最高效的类型和长度。
  *  字符串：**根据预估最大长度选用 `VARCHAR(n)`，仅在必要时（如长文本内容）使用 `TEXT` 或 `LONGTEXT`**。
  *  数字：整数用 `INT` 或 `BIGINT` (根据范围)，**小数/金额务必使用 `DECIMAL(m,n)`**。
  *  时间：时间戳用 `DATETIME`，若只需日期用 `DATE`。
  *  布尔值/简单枚举：使用 `TINYINT`，并在注释中说明含义 (例如 `COMMENT '状态 (0:禁用, 1:启用)'`)。
*  **枚举/状态处理**:
  *  **简单、固定枚举 (如状态)**: 使用 `TINYINT` 并加注释说明。
  *  **(备选方案)** 对于复杂、可能变化或需后台管理的枚举值，可考虑设计专门的**字典表 (Dictionary Table)** 模式，但这非本模板默认要求。
*  **索引设计**:
  *  主键自带索引。
  *  为所有**经常用于查询条件 (WHERE)、排序 (ORDER BY)、分组 (GROUP BY) 的字段或字段组合**创建索引 (`INDEX idx_...`)。
  *  为需要保证**唯一性的字段或字段组合**创建 `UNIQUE KEY uk_...`。
  *  遵循最左前缀原则设计复合索引。
  *  **避免过度索引**，权衡查询性能提升与写入性能损耗。
*  **其他重要规范**:
  *  存储引擎统一使用 `InnoDB`。
  *  字符集统一使用 `utf8mb4`，排序规则 `utf8mb4_unicode_ci`。
  *  **严格禁止在数据库层面使用外键约束 (FOREIGN KEY)**，关联逻辑在应用层保证。
  *  为字段设置**合理且明确的 `DEFAULT` 值** (如 `0`, `''`, `'默认值'`, `CURRENT_TIMESTAMP`)，或显式允许 `NULL` ( `DEFAULT NULL`)。

**请严格按照以下 SQL 模板生成 `CREATE TABLE` 语句（业务字段需根据 PRD 智能填充）：**

-- 示例模板，仅供参考结构，具体字段需智能生成
DROP TABLE IF EXISTS `模块名_表名`;
CREATE TABLE `模块名_表名` (
 `id` bigint UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
 -- === 业务字段 (请根据PRD智能填充) ===
 `title` VARCHAR(255) NOT NULL COMMENT '标题示例 (使用VARCHAR)',
 `content` TEXT NULL COMMENT '内容示例 (必要时使用TEXT)',
 `price` DECIMAL(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格示例 (使用DECIMAL)',
 `user_email` VARCHAR(128) NULL COMMENT '用户邮箱 (可能需要唯一索引)',
 -- === 标准字段 (按需添加) ===
 `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态 (0:禁用, 1:启用)',
 `created_at` datetime NOT NULL COMMENT '创建时间',
 `updated_at` datetime NOT NULL COMMENT '更新时间',
 `deleted_at` datetime NULL DEFAULT NULL COMMENT '删除时间 (软删除标记)',
 `created_by` bigint UNSIGNED NOT NULL COMMENT '创建人用户ID',
 `updated_by` bigint UNSIGNED NOT NULL COMMENT '最后更新人用户ID',
 PRIMARY KEY (`id`) USING BTREE,
 -- === 常用索引 (按需添加) ===
 INDEX `idx_status` (`status`) USING BTREE,
 -- === 其他业务索引 (请根据PRD智能添加) ===
 INDEX `idx_created_at` (`created_at`) USING BTREE,
 UNIQUE KEY `uk_user_email` (`user_email`) USING BTREE, -- 唯一索引示例
 -- INDEX `idx_combo_example` (`field_a`, `field_b`) USING BTREE -- 复合索引示例
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT '表说明 (请根据PRD智能生成)';


请将所有表的 SQL 语句统一完整输出，不要中途省略或分开输出。
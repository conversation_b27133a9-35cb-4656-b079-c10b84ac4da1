# 运营工具老师学生课程题目反馈系统 - 服务端架构设计文档 v1.0

## 版本信息

| 版本号 | 日期 | 作者 | 变更说明 |
|--------|------|------|----------|
| V1.0 | 2025-05-30 | AI架构设计师 | 初版设计 |

## 1. 引言 (Introduction)

### 1.1. 文档目的 (Purpose of Document)
为运营工具老师学生课程题目反馈系统提供清晰的服务端架构蓝图，指导反馈收集、智能审核、后台管理、消息通知等核心模块的设计与开发，确保系统满足多端反馈处理、智能化审核和用户激励的业务需求。

### 1.2. 系统概述 (System Overview)
本系统是一个智能化的教育内容质量反馈平台，核心目标是建立完整的课程内容质量反馈闭环，通过用户反馈驱动课程质量提升。系统支持教师端和学生端用户快速提交课程问题，通过智能Agent自动分类审核，生产后台高效处理，并及时反馈处理结果给用户，同时提供积分激励机制提升用户参与积极性。

主要交互方包括：教师端应用、学生端应用、生产后台管理系统、智能Agent审核服务、飞书多维表格、Dify工作流平台。

### 1.3. 设计目标与核心原则 (Design Goals and Core Principles)
**核心目标**：
- 支持1000+并发用户同时提交反馈，P95响应时间<3秒
- Agent智能审核处理时间<5秒，准确率>80%
- 反馈处理流程端到端时效<24小时
- 系统可用性99.9%，支持7x24小时稳定运行
- 支持多种反馈类型扩展和Agent能力扩展

**设计原则**：
- 用户体验优先：简化反馈提交流程，快速响应用户操作
- 智能化处理：通过AI减少人工审核工作量，提升处理效率
- 数据驱动决策：完整记录反馈处理数据，支持质量分析和优化
- 激励机制导向：通过积分奖励提升用户参与积极性
- 可扩展性保障：支持反馈类型、审核规则、处理流程的灵活扩展

### 1.4. 范围 (Scope)
**覆盖范围**：
- 多端反馈收集服务（教师端、学生端）
- 智能Agent审核服务（文字类内容自动分类）
- 生产后台管理服务（反馈处理、任务分发、数据统计）
- 消息通知服务（处理结果通知、积分下发通知）
- 积分管理服务（积分计算、下发、查询）

**不覆盖范围**：
- 前端UI/UX具体实现细节
- 具体的数据库表结构和字段设计（仅涉及概念数据模型）
- Dify工作流的具体配置和训练细节
- 飞书多维表格的具体字段配置
- 具体的运维部署和监控配置策略

### 1.5. 术语与缩写 (Glossary and Abbreviations)
- **Agent**: 智能审核系统，基于Dify工作流实现的自动分类和初筛服务
- **多维表格**: 飞书多维表格，用于实现生产后台反馈管理功能
- **Dify工作流**: 用于实现Agent审核的具体AI工作流程
- **反馈分类**: 文字类内容反馈和非文字类内容反馈的分类处理
- **积分下发**: 对有效反馈给予积分奖励的业务流程

## 2. 需求提炼与架构驱动因素 (Key Requirements & Architectural Drivers)

### 2.1. 核心业务能力 (Core Business Capabilities)
从反馈处理业务需求中提炼出系统必须提供的核心能力：

1. **多端反馈收集能力**: 支持教师端（课程反馈、题目反馈、视频MV反馈、其他建议反馈）和学生端（课程反馈、题目反馈、其他bug反馈）的统一反馈收集
2. **智能分类审核能力**: 对文字类内容反馈进行自动分类和初筛，支持板书文字问题、字幕问题、题目问题的智能识别
3. **生产后台管理能力**: 反馈列表管理、任务自动分发、处理状态跟踪、数据统计分析
4. **消息通知推送能力**: 处理结果通知、积分下发通知、飞书工作通知的统一推送管理
5. **积分激励管理能力**: 积分计算规则、积分下发流程、积分余额查询、积分历史记录
6. **用户认证授权能力**: 多角色用户认证（教师/学生）、权限校验、操作审计

### 2.2. 关键质量属性 (Key Quality Attributes)

#### 2.2.1. 性能 (Performance)
- 反馈提交响应时间P95<3秒，支持1000并发用户同时提交
- Agent智能审核处理时间<5秒，支持每秒100次反馈处理请求
- 消息通知推送延迟<1秒，支持批量通知处理
- 积分查询响应时间<500ms，支持高频积分余额查询

#### 2.2.2. 可用性 (Availability)
- 反馈收集服务99.9%可用性，支持7x24小时稳定运行
- Agent审核服务支持降级机制，审核失败时自动转人工处理
- 消息通知服务支持重试机制，确保重要通知不丢失
- 数据存储支持主从备份，确保反馈数据不丢失

#### 2.2.3. 可伸缩性 (Scalability)
- 反馈收集服务支持水平扩展，应对用户增长和反馈量激增
- Agent审核服务支持多实例部署，提升并发处理能力
- 消息通知服务支持队列缓冲，应对通知量突增
- 数据存储支持分库分表，应对数据量持续增长

#### 2.2.4. 安全性与隐私保护 (Security and Privacy)
- 用户认证与授权：所有反馈操作必须经过严格的身份认证和权限校验
- 数据保护：用户反馈内容在存储和传输过程中必须加密
- 操作审计：所有反馈处理操作必须记录详细的操作日志
- 防护要求：系统具备对常见Web攻击的基本防护能力

#### 2.2.5. 可维护性 (Maintainability)
- 清晰的服务职责划分，反馈收集、审核、通知、积分等模块独立
- 明确的服务接口定义，支持服务间松耦合交互
- 完善的配置管理，支持反馈分类规则、Agent审核阈值的动态调整

#### 2.2.6. 可测试性 (Testability)
- 支持反馈处理流程的端到端测试
- 支持Agent审核算法的独立测试和A/B测试
- 支持积分计算规则的单元测试和集成测试

### 2.3. 主要约束与依赖 (Key Constraints and Dependencies)

**技术栈约束**：
- 后端开发语言：Go 1.24.3
- 微服务框架：Kratos 2.8.4
- 数据库：PostgreSQL 17（主要业务数据）、ClickHouse **********（分析数据）
- 缓存：Redis 6.0.3
- 消息队列：Kafka 3.6
- 链路追踪：Zipkin 3.4.3

**外部服务依赖**：
- **用户中心服务**: 提供用户认证和基础信息，需要传递用户凭证，返回用户ID、角色、学校信息等
- **教师服务**: 提供教师详细信息和权限验证，需要传递教师ID，返回任教科目、班级等信息
- **内容平台服务**: 提供题目和课程详细信息，需要传递内容ID，返回题目内容、课程信息等
- **Dify工作流平台**: 提供AI审核能力，需要传递反馈内容和类型，返回审核结果和分类建议
- **飞书开放平台**: 提供多维表格和消息通知能力，需要传递表格数据和通知内容

## 3. 宏观架构设计 (High-Level Architectural Design)

### 3.1. 架构风格与模式 (Architectural Style and Patterns)
采用**微服务架构**结合**事件驱动架构**的混合模式：

**微服务架构**：将反馈收集、智能审核、消息通知、积分管理等不同业务领域拆分为独立的微服务，每个服务有明确的业务边界和数据所有权，支持独立开发、部署和扩展。

**事件驱动架构**：通过Kafka消息队列实现服务间的异步通信，特别是在反馈处理流程中，通过事件驱动实现状态变更通知、积分下发触发、消息推送等异步处理。

**选择理由**：
1. 反馈处理涉及多个独立的业务领域，微服务架构便于职责分离
2. Agent审核、消息通知等操作适合异步处理，事件驱动提升系统响应性
3. 不同服务有不同的性能和扩展需求，微服务支持独立优化
4. 便于团队并行开发和技术栈选择的灵活性

### 3.2. 系统分层视图 (Layered View)

#### 3.2.1. 逻辑分层模型设计

基于反馈处理业务的复杂度分析，设计四层逻辑架构：

```mermaid
flowchart TD
    subgraph 'FeedbackPlatform'
        direction TB
        APIGatewayLayer[API网关层]
        BusinessServiceLayer[业务服务层]
        DomainLogicLayer[领域逻辑层]
        DataInfrastructureLayer[数据基础设施层]

        %% 业务服务层组件
        FeedbackService[反馈管理服务]
        AgentService[智能审核服务]
        NotificationService[消息通知服务]
        PointService[积分管理服务]

        %% 领域逻辑层组件
        FeedbackDomain[反馈领域逻辑]
        AuditDomain[审核领域逻辑]
        NotificationDomain[通知领域逻辑]
        PointDomain[积分领域逻辑]

        %% 数据基础设施层组件
        PostgreSQLAdapter[PostgreSQL适配器]
        RedisAdapter[Redis适配器]
        KafkaAdapter[Kafka适配器]
        ExternalServiceAdapter[外部服务适配器]

        %% 层间依赖关系
        APIGatewayLayer --> BusinessServiceLayer
        BusinessServiceLayer --> DomainLogicLayer
        BusinessServiceLayer --> DataInfrastructureLayer
        DomainLogicLayer --> DataInfrastructureLayer

        %% 业务服务层内部组件连接
        BusinessServiceLayer --> FeedbackService
        BusinessServiceLayer --> AgentService
        BusinessServiceLayer --> NotificationService
        BusinessServiceLayer --> PointService

        %% 领域逻辑层内部组件连接
        DomainLogicLayer --> FeedbackDomain
        DomainLogicLayer --> AuditDomain
        DomainLogicLayer --> NotificationDomain
        DomainLogicLayer --> PointDomain

        %% 数据基础设施层内部组件连接
        DataInfrastructureLayer --> PostgreSQLAdapter
        DataInfrastructureLayer --> RedisAdapter
        DataInfrastructureLayer --> KafkaAdapter
        DataInfrastructureLayer --> ExternalServiceAdapter

        %% 外部服务连接
        ExternalServiceAdapter -.-> UserCenterService[用户中心服务]
        ExternalServiceAdapter -.-> TeacherService[教师服务]
        ExternalServiceAdapter -.-> ContentService[内容平台服务]
        ExternalServiceAdapter -.-> DifyPlatform[Dify工作流平台]
        ExternalServiceAdapter -.-> FeishuPlatform[飞书开放平台]

        %% 基础设施连接
        PostgreSQLAdapter -.-> PostgreSQLDB[PostgreSQL数据库]
        RedisAdapter -.-> RedisCache[Redis缓存]
        KafkaAdapter -.-> KafkaCluster[Kafka集群]
    end

    %% 设置样式
    classDef gatewayStyle fill:#E8F5E8,stroke:#333,stroke-width:2px;
    classDef serviceStyle fill:#E8F0FF,stroke:#333,stroke-width:2px;
    classDef domainStyle fill:#FFF0E8,stroke:#333,stroke-width:2px;
    classDef dataStyle fill:#F0E8FF,stroke:#333,stroke-width:2px;
    classDef externalStyle fill:#F5F0E8,stroke:#333,stroke-width:1px;
    
    class APIGatewayLayer gatewayStyle;
    class BusinessServiceLayer,FeedbackService,AgentService,NotificationService,PointService serviceStyle;
    class DomainLogicLayer,FeedbackDomain,AuditDomain,NotificationDomain,PointDomain domainStyle;
    class DataInfrastructureLayer,PostgreSQLAdapter,RedisAdapter,KafkaAdapter,ExternalServiceAdapter dataStyle;
    class UserCenterService,TeacherService,ContentService,DifyPlatform,FeishuPlatform externalStyle;
```

**各层职责定义**：

1. **API网关层**：
   - 核心职责：统一接入点管理、路由分发、认证授权、限流熔断
   - 封装变化：外部接口协议变化、认证方式变化、路由规则变化
   - 依赖规则：仅依赖业务服务层的抽象接口，不直接访问领域逻辑层

2. **业务服务层**：
   - 核心职责：反馈处理流程编排、服务间协调、事务管理、外部服务集成
   - 封装变化：业务流程变化、服务协调逻辑变化、外部依赖变化
   - 依赖规则：依赖领域逻辑层的抽象接口和数据基础设施层的适配器接口

3. **领域逻辑层**：
   - 核心职责：反馈分类规则、审核业务逻辑、积分计算规则、通知规则
   - 封装变化：业务规则变化、领域模型变化、计算逻辑变化
   - 依赖规则：仅依赖数据基础设施层的抽象接口，不依赖具体实现

4. **数据基础设施层**：
   - 核心职责：数据持久化、缓存管理、消息队列、外部服务适配
   - 封装变化：存储技术变化、外部服务接口变化、数据访问方式变化
   - 依赖规则：最底层，不依赖其他层，仅依赖外部基础设施

**设计合理性论证**：
此四层架构设计充分体现了关注点分离原则，每层都有明确的职责边界。API网关层专注于外部接入管理，业务服务层专注于流程编排，领域逻辑层专注于业务规则，数据基础设施层专注于技术实现。这种分层既避免了过度复杂化，又确保了各层职责清晰，便于独立测试和维护。严格的单向依赖规则确保了架构的稳定性和可扩展性。

### 3.3. 模块/服务划分视图 (Module/Service Decomposition View)

#### 3.3.1. 核心服务识别与职责

基于反馈处理业务能力和领域边界，识别以下核心微服务：

**反馈管理服务 (FeedbackService)**：
- 核心职责：反馈收集、分类管理、状态跟踪、反馈查询
- 业务边界：反馈生命周期管理，从提交到处理完成的全流程
- 对外能力：反馈提交接口、反馈查询接口、反馈状态更新接口

**智能审核服务 (AgentService)**：
- 核心职责：文字类反馈自动分类、智能初筛、审核结果管理
- 业务边界：AI审核能力，集成Dify工作流实现智能分类
- 对外能力：审核请求接口、审核结果查询接口、审核规则配置接口

**消息通知服务 (NotificationService)**：
- 核心职责：处理结果通知、积分下发通知、飞书工作通知
- 业务边界：统一消息推送管理，支持多渠道通知
- 对外能力：通知发送接口、通知状态查询接口、通知模板管理接口

**积分管理服务 (PointService)**：
- 核心职责：积分计算、积分下发、积分查询、积分历史管理
- 业务边界：积分业务逻辑，包括计算规则和账户管理
- 对外能力：积分下发接口、积分查询接口、积分规则配置接口

**生产后台服务 (BackofficeService)**：
- 核心职责：反馈列表管理、任务分发、数据统计、飞书表格集成
- 业务边界：后台管理功能，支持生产人员高效处理反馈
- 对外能力：反馈管理接口、统计分析接口、任务分发接口

#### 3.3.2. 服务交互模式与数据契约

**服务间交互方式**：
- **同步RPC调用 (gRPC)**：用于实时性要求高的服务间调用，如反馈提交时的用户验证
- **异步事件消息 (Kafka)**：用于状态变更通知、积分下发触发、消息推送等异步处理
- **RESTful API (HTTP)**：主要用于前端应用和外部系统的接口调用

**核心数据契约**：

1. **反馈提交契约**：
   - 输入：用户ID、反馈类型、反馈内容、关联资源ID、截图信息
   - 输出：反馈ID、提交状态、预估处理时间

2. **智能审核契约**：
   - 输入：反馈ID、反馈内容、反馈类型
   - 输出：审核结果、分类建议、置信度、处理建议

3. **消息通知契约**：
   - 输入：用户ID、通知类型、通知内容、通知渠道
   - 输出：通知ID、发送状态、送达状态

4. **积分下发契约**：
   - 输入：用户ID、积分类型、积分数量、关联反馈ID
   - 输出：积分记录ID、下发状态、当前积分余额

**服务交互图**：

```mermaid
graph TB
    subgraph "客户端应用"
        TeacherApp[教师端应用]
        StudentApp[学生端应用]
        BackofficeApp[生产后台应用]
    end

    subgraph "API网关层"
        APIGateway[API网关]
    end

    subgraph "核心业务服务"
        FeedbackService[反馈管理服务]
        AgentService[智能审核服务]
        NotificationService[消息通知服务]
        PointService[积分管理服务]
        BackofficeService[生产后台服务]
    end

    subgraph "外部服务"
        UserCenter[用户中心服务]
        TeacherService[教师服务]
        ContentService[内容平台服务]
        DifyPlatform[Dify工作流平台]
        FeishuPlatform[飞书开放平台]
    end

    subgraph "基础设施"
        PostgreSQL[PostgreSQL数据库]
        Redis[Redis缓存]
        Kafka[Kafka消息队列]
    end

    %% 客户端到网关
    TeacherApp -- HTTP/gRPC --> APIGateway
    StudentApp -- HTTP/gRPC --> APIGateway
    BackofficeApp -- HTTP/gRPC --> APIGateway

    %% 网关到服务
    APIGateway -- gRPC --> FeedbackService
    APIGateway -- gRPC --> NotificationService
    APIGateway -- gRPC --> PointService
    APIGateway -- gRPC --> BackofficeService

    %% 服务间同步调用
    FeedbackService -- gRPC --> UserCenter
    FeedbackService -- gRPC --> TeacherService
    FeedbackService -- gRPC --> ContentService
    AgentService -- HTTP --> DifyPlatform
    NotificationService -- HTTP --> FeishuPlatform
    BackofficeService -- HTTP --> FeishuPlatform

    %% 服务间异步消息
    FeedbackService -.-> Kafka
    AgentService -.-> Kafka
    NotificationService -.-> Kafka
    PointService -.-> Kafka
    BackofficeService -.-> Kafka

    %% 基础设施连接
    FeedbackService -.-> PostgreSQL
    FeedbackService -.-> Redis
    AgentService -.-> PostgreSQL
    NotificationService -.-> PostgreSQL
    PointService -.-> PostgreSQL
    BackofficeService -.-> PostgreSQL

    %% Kafka事件流
    Kafka -.-> |反馈状态变更事件| NotificationService
    Kafka -.-> |审核完成事件| BackofficeService
    Kafka -.-> |积分下发事件| PointService
    Kafka -.-> |通知发送事件| NotificationService
```

### 3.4. 业务流程的高层视图 (High-Level View of Business Flows)

#### 3.4.1. 教师反馈提交流程

```mermaid
sequenceDiagram
    actor Teacher as 教师
    participant App as 教师端应用
    participant Gateway as API网关
    participant FS as 反馈管理服务
    participant UC as 用户中心服务
    participant CS as 内容平台服务
    participant AS as 智能审核服务
    participant NS as 消息通知服务
    participant Kafka as Kafka消息队列

    Teacher->>App: 发现课程问题并点击反馈按钮
    App->>Gateway: 请求反馈表单配置
    Gateway->>FS: 获取反馈类型配置
    FS->>App: 返回反馈表单配置

    Teacher->>App: 填写反馈信息（类型、描述、截图）
    App->>Gateway: 提交反馈请求
    Gateway->>FS: 创建反馈记录

    FS->>UC: 验证用户身份和权限
    UC->>FS: 返回用户信息
    FS->>CS: 获取关联内容信息
    CS->>FS: 返回内容详情

    FS->>FS: 创建反馈记录
    FS->>Kafka: 发布反馈创建事件
    FS->>Gateway: 返回反馈提交成功

    Gateway->>App: 反馈提交成功响应
    App->>Teacher: 显示"感谢您的反馈，我们会尽快处理！"

    %% 异步处理流程
    Kafka->>AS: 接收反馈创建事件
    AS->>AS: 判断反馈类型（文字类/非文字类）
    
    alt 文字类内容反馈
        AS->>AS: 调用Dify工作流进行智能审核
        AS->>FS: 更新审核结果
        AS->>Kafka: 发布审核完成事件
    else 非文字类内容反馈
        AS->>FS: 标记为人工审核
        AS->>Kafka: 发布待人工审核事件
    end

    Kafka->>NS: 接收审核完成事件
    NS->>Teacher: 发送审核进度通知
```

#### 3.4.2. 学生反馈提交流程

```mermaid
sequenceDiagram
    actor Student as 学生
    participant App as 学生端应用
    participant Gateway as API网关
    participant FS as 反馈管理服务
    participant UC as 用户中心服务
    participant QS as 内容平台服务
    participant AS as 智能审核服务
    participant Kafka as Kafka消息队列

    Student->>App: 答题过程中发现题目错误
    App->>Gateway: 请求题目反馈表单
    Gateway->>FS: 获取题目反馈配置
    FS->>App: 返回题目反馈表单

    Student->>App: 选择问题类型并描述具体问题
    App->>Gateway: 提交题目反馈
    Gateway->>FS: 处理题目反馈请求

    FS->>UC: 验证学生身份
    UC->>FS: 返回学生信息
    FS->>QS: 获取题目详细信息
    QS->>FS: 返回题目内容和元数据

    FS->>FS: 创建题目反馈记录
    FS->>Kafka: 发布题目反馈事件
    FS->>Gateway: 返回提交成功

    Gateway->>App: 反馈提交成功
    App->>Student: 显示提交成功提示

    %% 异步智能审核
    Kafka->>AS: 接收题目反馈事件
    AS->>AS: 调用题目专用Dify工作流
    AS->>FS: 更新题目反馈审核结果
    AS->>Kafka: 发布题目审核完成事件
```

#### 3.4.3. 生产后台处理流程

```mermaid
sequenceDiagram
    participant Kafka as Kafka消息队列
    participant BS as 生产后台服务
    participant FS as 反馈管理服务
    participant Feishu as 飞书平台
    participant Producer as 生产人员
    participant NS as 消息通知服务
    participant PS as 积分管理服务

    %% 反馈进入后台处理
    Kafka->>BS: 接收审核完成事件
    BS->>FS: 查询反馈详情和审核结果
    FS->>BS: 返回完整反馈信息

    BS->>Feishu: 创建/更新多维表格记录
    Feishu->>BS: 返回表格更新结果
    BS->>Feishu: 发送飞书工作通知
    Feishu->>Producer: 推送新反馈处理任务

    %% 生产人员处理
    Producer->>Feishu: 查看反馈详情
    Producer->>Producer: 人工审核和分类
    Producer->>Feishu: 更新处理状态

    alt 反馈被采纳
        Producer->>Producer: 进行内容修改
        Producer->>Producer: 审核上线
        Producer->>Feishu: 标记为已处理
        Feishu->>BS: 同步处理结果
        BS->>FS: 更新反馈状态为已采纳
        BS->>Kafka: 发布反馈采纳事件
        
        %% 积分下发流程
        Kafka->>PS: 接收反馈采纳事件
        PS->>PS: 计算积分奖励
        PS->>PS: 下发积分到用户账户
        PS->>Kafka: 发布积分下发事件
        
        %% 用户通知
        Kafka->>NS: 接收积分下发事件
        NS->>NS: 生成采纳通知和积分通知
        NS->>NS: 发送用户通知
        
    else 反馈未被采纳
        Producer->>Feishu: 标记为未采纳并填写原因
Feishu->>BS: 同步处理结果
        BS->>FS: 更新反馈状态为未采纳
        BS->>Kafka: 发布反馈未采纳事件
        
        %% 用户通知
        Kafka->>NS: 接收反馈未采纳事件
        NS->>NS: 生成未采纳通知
        NS->>NS: 发送用户通知
    end
```

#### 3.4.4. 用户查看反馈结果流程

```mermaid
sequenceDiagram
    actor Teacher as 教师
    participant App as 教师端应用
    participant Gateway as API网关
    participant NS as 消息通知服务
    participant FS as 反馈管理服务
    participant PS as 积分管理服务

    %% 用户主动查看消息中心
    Teacher->>App: 点击头像菜单进入消息中心
    App->>Gateway: 请求消息列表
    Gateway->>NS: 查询用户消息
    NS->>App: 返回消息列表

    Teacher->>App: 查看具体反馈处理结果
    App->>Gateway: 请求反馈详情
    Gateway->>FS: 查询反馈处理详情
    FS->>App: 返回反馈详情和处理结果

    alt 反馈被采纳
        App->>Gateway: 查询积分奖励信息
        Gateway->>PS: 查询积分记录
        PS->>App: 返回积分详情
        App->>Teacher: 显示采纳结果和积分奖励
        
        Teacher->>App: 查看个人中心积分余额
        App->>Gateway: 请求积分余额
        Gateway->>PS: 查询当前积分余额
        PS->>App: 返回积分余额
        App->>Teacher: 显示最新积分余额
    else 反馈未被采纳
        App->>Teacher: 显示未采纳原因和说明
    end
```

### 3.5. 数据架构概述 (Data Architecture Overview)

#### 3.5.1. 核心领域对象概念定义

**反馈对象 (Feedback)**：
- 核心属性：反馈ID、用户ID、反馈类型、反馈内容、关联资源ID、截图信息、提交时间、处理状态、处理结果
- 关联关系：与用户、内容资源、审核记录、积分记录等实体关联
- 业务意义：反馈处理的核心实体，记录完整的反馈生命周期

**审核记录 (AuditRecord)**：
- 核心属性：审核ID、反馈ID、审核类型、审核结果、置信度、审核时间、审核人员
- 关联关系：与反馈对象、审核规则、处理任务等实体关联
- 业务意义：记录反馈的审核过程和结果，支持审核质量分析

**积分记录 (PointRecord)**：
- 核心属性：记录ID、用户ID、积分类型、积分数量、关联反馈ID、下发时间、当前余额
- 关联关系：与用户、反馈对象、积分规则等实体关联
- 业务意义：管理用户积分账户，记录积分变动历史

**消息通知 (Notification)**：
- 核心属性：通知ID、用户ID、通知类型、通知内容、发送渠道、发送状态、发送时间
- 关联关系：与用户、反馈对象、积分记录等实体关联
- 业务意义：统一管理各类通知消息，确保用户及时获得反馈

**处理任务 (ProcessTask)**：
- 核心属性：任务ID、反馈ID、任务类型、分配人员、任务状态、创建时间、完成时间
- 关联关系：与反馈对象、用户、处理结果等实体关联
- 业务意义：管理反馈处理工作流，支持任务分发和进度跟踪

#### 3.5.2. 数据一致性与同步策略

**最终一致性模型**：
- 反馈状态变更通过事件驱动实现最终一致性，确保各服务数据同步
- 积分下发采用异步处理，通过补偿机制确保积分数据准确性
- 消息通知支持重试机制，确保重要通知最终送达

**事件驱动数据同步**：
- 反馈创建、状态变更、处理完成等关键事件通过Kafka传播
- 各服务监听相关事件，更新本地数据状态
- 通过事件溯源支持数据恢复和一致性校验

**分布式事务处理**：
- 反馈提交和积分下发采用SAGA模式，确保业务一致性
- 关键业务操作支持补偿机制，处理异常情况下的数据回滚
- 通过幂等性设计避免重复处理导致的数据不一致

## 4. 技术栈与关键库选型 (Technology Choices)

### 4.1. RPC/Web框架选型方向
**推荐使用gRPC进行服务间通信**：
- 选型理由：高性能、强类型契约适合反馈数据传输，支持流式处理适合大量反馈场景
- 应用场景：反馈管理服务、智能审核服务、积分管理服务间的同步调用

**推荐使用Kratos框架处理HTTP API**：
- 选型理由：基于Go生态，与gRPC集成良好，支持中间件扩展，适合教育移动端接入
- 应用场景：API网关层、前端应用接口、外部系统集成接口

### 4.2. 数据库交互技术方向
**推荐使用PostgreSQL存储核心业务数据**：
- 选型理由：ACID特性保证反馈数据一致性，JSON支持适合灵活的反馈内容存储
- 应用场景：反馈记录、用户信息、积分账户、审核记录等结构化数据

**推荐使用ClickHouse存储分析数据**：
- 选型理由：列式存储适合大规模反馈行为分析，支持实时OLAP查询
- 应用场景：反馈统计分析、用户行为分析、质量趋势分析

**推荐使用GORM进行ORM操作**：
- 选型理由：Go生态成熟ORM，支持代码生成，简化数据库操作
- 应用场景：业务服务层的数据库访问，支持事务管理和连接池

### 4.3. 消息队列技术方向
**推荐使用Kafka处理事件流**：
- 选型理由：高吞吐量适合大规模反馈事件处理，持久化保证事件不丢失
- 应用场景：反馈状态变更事件、积分下发事件、消息通知事件的异步处理

### 4.4. 缓存技术方向
**推荐使用Redis缓存热点数据**：
- 选型理由：高性能内存缓存，支持多种数据结构，适合反馈系统的缓存需求
- 应用场景：用户会话缓存、反馈配置缓存、积分余额缓存、审核结果缓存

### 4.5. 配置管理思路
**推荐通过环境变量和配置文件管理系统配置**：
- 配置内容：反馈分类规则、Agent审核阈值、积分计算规则、外部服务配置
- 管理方式：支持动态配置更新，通过配置中心统一管理多环境配置

### 4.6. 测试策略与工具方向
**强调多层次测试策略**：
- 单元测试：反馈业务逻辑、积分计算规则、审核算法的独立测试
- 集成测试：服务间接口测试、数据一致性测试、事件流程测试
- 端到端测试：完整反馈处理流程测试，支持A/B测试优化用户体验

## 5. 跨功能设计考量 (Cross-Cutting Concerns)

### 5.1. 安全与隐私考量
**认证与授权机制**：
- 独立的用户中心服务负责多角色认证（教师/学生/生产人员）
- API网关层统一进行Token校验和权限判断
- 基于角色的访问控制，确保用户只能访问授权的反馈数据

**数据隐私保护**：
- 反馈内容在存储和传输过程中进行加密处理
- 敏感信息脱敏，审核人员只能看到必要的反馈信息
- 完整的操作审计日志，记录所有数据访问和修改操作

**合规性考虑**：
- 支持数据删除和匿名化处理，满足隐私保护法规要求
- 反馈数据访问权限严格控制，防止数据泄露风险

### 5.2. 性能与并发考量
**异步处理与削峰填谷**：
- 反馈提交采用异步处理模式，快速响应用户操作
- 通过Kafka消息队列实现削峰填谷，应对反馈量突增
- Agent审核采用队列处理，避免审核服务过载

**无状态设计原则**：
- 所有业务服务设计为无状态，支持水平扩展
- 用户会话信息存储在Redis中，支持多实例负载均衡
- 通过容器化部署支持弹性伸缩

**性能优化策略**：
- 热点反馈数据缓存，减少数据库访问压力
- 批量处理积分下发和消息通知，提升处理效率
- 数据库读写分离，优化查询性能

### 5.3. 错误处理与容错考量
**服务间错误传递**：
- 统一的错误码规范，便于问题定位和处理
- 服务间调用支持超时和重试机制
- 关键业务操作支持补偿机制，确保数据一致性

**容错机制设计**：
- Agent审核服务支持降级，审核失败时自动转人工处理
- 消息通知服务支持重试和死信队列，确保重要通知送达
- 积分下发支持幂等性，避免重复下发

**优雅降级策略**：
- 反馈提交功能优先保障，其他功能可适当降级
- Agent审核服务降级时，自动切换到人工审核流程
- 消息通知服务降级时，保留核心通知功能

### 5.4. 可观测性考量
**结构化日志记录**：
- 反馈处理全流程的结构化日志记录
- 关键业务指标的实时监控和告警
- 用户行为数据的完整记录，支持业务分析

**业务指标暴露**：
- 反馈处理效率指标：平均处理时间、处理成功率
- Agent审核质量指标：审核准确率、误判率
- 用户参与指标：反馈提交量、用户活跃度

**链路追踪集成**：
- 通过Zipkin实现分布式链路追踪
- 反馈处理流程的完整链路可视化
- 性能瓶颈识别和优化支持

**健康检查设计**：
- 各服务的健康检查接口，监控服务状态
- 依赖服务的健康状态检查，及时发现问题
- 数据库连接池和消息队列的健康监控

## 6. 风险与挑战 (Architectural Risks and Challenges)

### 6.1. 技术风险识别

**Agent审核性能瓶颈风险**：
- 风险描述：Dify工作流处理能力有限，可能成为系统瓶颈
- 影响评估：影响反馈处理效率，用户体验下降
- 应对策略：设计Agent服务集群，支持多实例部署；实现审核降级机制，确保服务可用性

**外部服务依赖风险**：
- 风险描述：飞书平台、Dify平台等外部服务不可用
- 影响评估：影响后台管理和智能审核功能
- 应对策略：设计服务降级方案，关键功能支持离线模式；建立服务监控和快速切换机制

**数据一致性风险**：
- 风险描述：分布式环境下数据同步延迟或失败
- 影响评估：可能导致积分下发错误或通知丢失
- 应对策略：采用事件溯源和补偿机制；建立数据一致性校验和修复流程

### 6.2. 业务风险识别

**反馈量激增风险**：
- 风险描述：某些时期反馈量可能激增，超出系统处理能力
- 影响评估：系统响应变慢，用户体验下降
- 应对策略：设计弹性伸缩机制；实现反馈优先级处理；建立容量规划和监控

**恶意反馈风险**：
- 风险描述：用户可能提交大量无效或恶意反馈
- 影响评估：浪费处理资源，影响正常反馈处理
- 应对策略：增强Agent审核能力；建立反馈质量评估机制；实现用户行为分析和限制

### 6.3. 运维风险识别

**服务部署复杂性风险**：
- 风险描述：微服务架构增加了部署和运维复杂性
- 影响评估：可能导致部署失败或服务不稳定
- 应对策略：采用容器化部署；建立完善的CI/CD流程；实现服务健康监控

**数据备份和恢复风险**：
- 风险描述：反馈数据丢失或损坏的风险
- 影响评估：可能导致用户反馈丢失，影响业务连续性
- 应对策略：建立多层次数据备份机制；实现数据恢复流程；定期进行数据恢复演练

## 7. 未来演进方向 (Future Architectural Evolution)

### 7.1. 智能化能力扩展
**AI能力增强**：
- 引入更先进的大语言模型，提升反馈分类和处理的准确性
- 开发智能反馈建议功能，主动识别潜在的内容问题
- 构建知识图谱，支持更精准的内容质量分析

**自动化处理扩展**：
- 实现简单问题的自动修复，减少人工处理工作量
- 开发智能任务分发，根据反馈类型和处理人员能力自动分配
- 建立反馈质量预测模型，优化处理优先级

### 7.2. 业务功能扩展
**多媒体反馈支持**：
- 支持视频、音频等多媒体内容的反馈处理
- 开发多媒体内容的智能分析能力
- 扩展反馈类型，支持更丰富的问题分类

**跨平台集成**：
- 集成更多第三方教育平台和工具
- 支持多种内容格式的反馈处理
- 建立开放的反馈处理API，支持第三方集成

### 7.3. 技术架构演进
**云原生架构升级**：
- 采用Kubernetes进行容器编排，提升部署和运维效率
- 实现服务网格，增强服务间通信的可观测性和安全性
- 引入无服务器架构，优化资源利用率

**数据架构优化**：
- 建立实时数据湖，支持更复杂的数据分析需求
- 实现数据血缘追踪，提升数据质量管理
- 引入机器学习平台，支持反馈处理算法的持续优化

## 8. 架构文档自查清单 (Pre-Submission Checklist)

### 8.1 架构完备性自查清单

| 检查项 | 内容说明 | 检查结果 | 备注 |
|--------|----------|----------|------|
| **核心架构图表清晰性** | 反馈系统的宏观架构图（分层视图、服务划分视图）清晰表达了核心组件、职责边界和主要关系 | ✅ | 章节 3.2、3.3 |
| **业务流程覆盖完整性** | 关键业务流程（教师反馈、学生反馈、后台处理、结果通知）完整覆盖PRD中的所有核心用户场景 | ✅ | 章节 3.4 |
| **领域概念抽象层级** | 核心领域对象（反馈、审核记录、积分记录等）的概念定义停留在业务意义层面，未陷入技术细节 | ✅ | 章节 3.5.1 |
| **外部依赖明确性** | 清晰说明了对用户中心、内容平台、Dify平台、飞书平台等外部服务的依赖关系和交互模式 | ✅ | 章节 2.3 |
| **技术选型合理性** | 技术栈选择（gRPC、PostgreSQL、Kafka等）都给出了充分理由，说明如何满足性能、可维护性等目标 | ✅ | 章节 4 |
| **设计原则遵循** | 整体架构体现了SOLID、KISS、YAGNI等设计原则，避免了过度设计和紧耦合等反模式 | ✅ | - |
| **模板指令遵守** | 严格遵守了模板中的特定指令，确保了设计的原创性和针对性 | ✅ | - |
| **模板完整性与聚焦性** | 填充了模板中的所有相关章节，内容聚焦于架构设计，剔除了实现细节 | ✅ | - |
| **安全与隐私考虑** | 充分考虑了用户数据隐私保护、多角色权限管理等安全要求 | ✅ | 章节 5.1 |

### 8.2 需求-架构完备性自查清单

| PRD核心需求点 | 对应架构模块/服务/流程 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
|---------------|------------------------|--------|--------|--------|----------|----------|------|
| 教师端反馈系统 | 教师反馈提交流程（3.4.1），涉及反馈管理服务、智能审核服务（3.3） | ✅ | ✅ | ✅ | ✅ | ✅ | 架构通过反馈管理服务和明确的数据契约支持教师端反馈功能 |
| 学生端反馈系统 | 学生反馈提交流程（3.4.2），涉及反馈管理服务、内容平台服务（3.3） | ✅ | ✅ | ✅ | ✅ | ✅ | 架构支持学生端反馈收集和题目关联 |
| 生产后台反馈管理 | 生产后台处理流程（3.4.3），涉及生产后台服务、飞书平台集成（3.3） | ✅ | ✅ | ✅ | ✅ | ✅ | 通过生产后台服务和飞书多维表格实现后台管理 |
| 消息通知系统 | 用户查看反馈结果流程（3.4.4），涉及消息通知服务（3.3） | ✅ | ✅ | ✅ | ✅ | ✅ | 通过消息通知服务实现处理结果通知 |
| 智能Agent审核 | 智能审核服务（3.3），集成在各业务流程中（3.4） | ✅ | ✅ | ✅ | ✅ | ✅ | 通过智能审核服务和Dify工作流集成实现AI审核能力 |
| 积分激励机制 | 积分管理服务（3.3），积分下发流程（3.4.3） | ✅ | ✅ | ✅ | ✅ | ✅ | 通过积分管理服务实现积分计算、下发和查询功能 |

- **完整性**：PRD核心需求都有对应的架构模块/服务或流程设计来支撑
- **正确性**：架构设计准确地反映了PRD核心需求的意图
- **一致性**：架构设计与PRD核心需求之间不存在逻辑冲突或描述不一致
- **可验证性（架构层面）**：架构设计提供了足够清晰的组件和交互，可以从架构层面判断需求能否被满足
- **可跟踪性**：能清晰地将PRD核心需求点映射到架构设计的特定部分

## 9. 附录 (Appendix)

### 9.1. 关键架构决策记录
- **微服务拆分决策**：基于业务领域边界和团队组织结构，将反馈处理拆分为5个核心微服务
- **事件驱动架构决策**：采用Kafka实现异步事件处理，提升系统响应性和可扩展性
- **外部服务集成决策**：通过适配器模式集成Dify和飞书平台，降低外部依赖风险

### 9.2. 参考标准和模式
- **微服务架构模式**：参考Martin Fowler的微服务架构最佳实践
- **事件驱动架构**：参考CQRS和Event Sourcing模式
- **DDD领域驱动设计**：应用于服务边界划分和领域模型设计

---

**架构设计完成，已按模板要求完成质量检查，等待您的命令，指挥官**
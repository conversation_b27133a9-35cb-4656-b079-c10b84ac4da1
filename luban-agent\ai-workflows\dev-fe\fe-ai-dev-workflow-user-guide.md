# AI 辅助前端开发工作流使用指南

## 📖 概述

本指南将帮助您有效使用 `fe-ai-dev-new-project-workflow.md` 工作流，通过人机协作的方式，高效地将 Figma 设计稿转化为高质量的前端组件。

### 🎯 适用场景
- 从 Figma 设计稿开发新的前端页面
- 需要像素级精确还原设计稿
- 使用 TypeScript + React + Tailwind CSS 技术栈
- 移动端优先的响应式开发

---

## 🚀 快速开始

### 前置准备

1. **确保环境配置**
   ```bash
   # 确保项目依赖已安装
   pnpm install
   
   # 确保开发服务器可以运行
   pnpm dev
   ```

2. **安装Figma的mcp服务**
   在roo code上安装对应的mcp服务，直接在cursor安装的话，在roo code无法启动。

    ```json
    "Framelink Figma MCP": {
      "command": "npx",
      "args": [
        "-y",
        "figma-developer-mcp",
        "--figma-api-key=换成你自己的api-key",
        "--stdio"
      ]
    }
    ```

3. **确保luban-agent在项目中**
   - 将luban-agent整个文件放在项目的根目录下
   ```
   示例：/Users/<USER>/ai/schoolroom/luban-agent
   ```

4. **准备设计资源**
   - Figma 设计稿链接（确保有访问权限）
   - 明确应用名称（`app_name`）和页面名称（`page_name`）
   - 如果有多个状态，准备所有状态的 Figma 链接

5. **确认项目结构**
   ```
   apps/{app_name}/
   ├── app/
   │   ├── components/     # 组件存放目录
   │   └── {page_name}/    # 页面文件
   └── ...
   ```
6. **开始流程**
   - 打开RooCode的code模式，@luban-agent/ai-workflows/dev-fe/fe-ai-dev-new-project-workflow.md，让其开始该工作流。同时需要提供相应文件。

---

## 📋 完整工作流程

### Phase 0: 资产准备 🖼️

**目标**：从 Figma 获取设计稿 PNG 图像

**您需要提供**：
```
应用名称：{app_name}
页面名称：{page_name}
Figma 链接：https://www.figma.com/design/...

# 多状态示例（需要避免同时输入太多状态，太多状态会导致上下文过多）
主状态：https://www.figma.com/design/.../default
加载状态：https://www.figma.com/design/.../loading
错误状态：https://www.figma.com/design/.../error
```

**AI 执行步骤**：
1. 调用 MCP 服务下载 PNG 图像
2. 按规范命名并保存到指定目录
3. 更新进度文件

**输出结果**：
- `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{app_name}-{page_name}.png`
- 或多个状态文件：`{app_name}-{page_name}-{state_name}.png`

**完成标志**：✅ PNG 文件成功下载并保存

---

### Phase 1: 布局分析文档生成 📐

**目标**：生成详细的页面布局分析文档

**您需要提供**：
- 确认 Phase 0 的输出文件路径正确
- 提供页面的功能描述（可选，帮助 AI 更好理解）

**AI 执行步骤**：
1. 分析设计稿的布局结构
2. 识别各个模块和组件
3. 生成详细的布局描述文档

**输出结果**：
- `luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/components-location.md`

**您的评审要点**：
- [ ] 布局描述是否准确反映设计稿
- [ ] 组件层级关系是否清晰
- [ ] 相对位置描述是否详细

**完成标志**：✅ 布局文档准确且详尽

---

### Phase 2: 任务拆解与文档生成 📝

**目标**：将页面拆解为可开发的组件任务，并生成完整的开发文档套件

**您需要提供**：
- 确认 Phase 1 输出的布局文档质量,如果不符合设计稿需要自己进行修正
- 特殊要求或注意事项（可选）

**AI 执行步骤**：
1. 基于布局文档拆解组件
2. 提取设计规范和样式
3. 生成完整的任务文档套件

**输出结果**：
```
luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/
├── {page_name}_tasktree.md              # 任务树
├── 01-{component-type}.md               # 各组件任务文档
├── 02-{component-type}.md
├── ...
├── {page_name}-state-matrix.yaml        # 状态矩阵
├── {page_name}-animation-hints.yaml     # 动画提示
├── design-tokens.json                   # 设计规范
├── ai-confidence-summary.yaml           # AI 置信度汇总
├── svg/                                 # SVG 资源
└── png/                                 # PNG 资源
```

**您的评审要点**：
- [ ] 组件拆分粒度是否合适（模块级，非按钮级）
- [ ] 设计规范提取是否准确
- [ ] 任务优先级和依赖关系是否合理
- [ ] SVG/PNG 资源导出是否必要且优化

**开发者完成**：
- 如果svg/png图片没有正确导出，开发者可以手动导出放到对应的目录下
- 手动导出的资源，需要在对应的任务项文件中说明导出的文件名称和对应的内容
- 所有svg/png资源都准备完毕后，手动放到对应应用的public文件下，并告知ai你已经完成静态资源的迁移，让它在后续开发时需要利用这些静态资源。

**完成标志**：✅ 所有文档生成完毕且质量符合要求

---

### Phase 3: 组件实现 ⚙️

**目标**：逐个实现组件代码

**工作模式**：迭代式，每次选择一个组件进行开发

#### 单个组件开发流程

**您需要提供**：
```
当前要开发的组件：01-Header-Navigation.md
应用名称：{app_name}
页面名称：{page_name}
```

**AI 执行步骤**：
1. **上下文加载**：AI 会加载所有相关文档并生成加载报告
2. **等待确认**：您需要确认加载报告后，AI 才开始编码
3. **代码生成**：生成符合规范的组件代码
4. **状态更新**：更新任务树中的完成状态

**输出结果**：
- `apps/{app_name}/app/components/{ComponentName}/{ComponentName}.tsx`
- `apps/{app_name}/app/components/{ComponentName}/README.md`

**您的验证步骤**：
1. **代码审查**：检查代码质量和规范遵循
2. **功能测试**：在开发环境中测试组件
3. **响应式测试**：验证不同屏幕尺寸下的表现
4. **对应组件的README**：检查`apps/{app_name}/app/components/README.md`是否有及时更新已完成组件的信息，如果没有则需要让ai及时更新

#### 页面组装（所有组件完成后）

**AI 执行步骤**：
1. 根据布局文档组装所有组件
2. 创建页面级文件
3. 更新组件库文档

**完成标志**：✅ 所有组件开发完成并可在页面中正常显示

---

### Phase 4: 全自动化视觉 QA 及迭代 🔍

**前提**：如果生成的页面已经符合设计稿要求时，则可以跳过Phase 4，直接进入phase 5

**目标**：确保代码渲染效果与设计稿像素级匹配

**工作模式**：AI 自动化执行，分为组件级和页面级两个层次

**您需要提供**：
- 确认所有组件已完成开发
- 特别关注的问题点（可选）

**AI 执行步骤**：

#### 4.1 组件级 QA（自动循环）
对每个组件：
1. 下载组件特定的设计 PNG
2. 对比代码渲染效果与设计稿
3. 自动修正发现的差异
4. 更新组件代码

#### 4.2 页面级 QA
1. 检查整体页面布局
2. 验证组件间距和对齐
3. 修正页面级布局问题

**您的最终验证**：
- [ ] 在浏览器中对比页面与设计稿
- [ ] 检查不同屏幕尺寸下的表现
- [ ] 验证交互状态的视觉效果

**完成标志**：✅ 页面视觉效果与设计稿达到可接受的匹配度

---

### Phase 5: 代码规范审查 ✅

**目标**：确保代码符合项目标准

**您的执行步骤**：
1. **自动化检查**：
   ```bash
   # 运行 linting 检查
   pnpm lint
   
   # 运行类型检查
   pnpm type-check
   ```

2. **人工审查**：
   - [ ] 代码结构和命名规范
   - [ ] 组件复用性和可维护性
   - [ ] 注释和文档完整性
   - [ ] 性能优化机会

**AI 辅助**（可选）：
- 请求 AI 审查特定代码片段
- 获取优化建议

**完成标志**：✅ 所有代码通过规范检查

---

## 🛠️ 实用技巧

### 与 AI 有效沟通

**✅ 好的指令示例**：
```
请开始 Phase 2: 任务拆解与文档生成
应用名称：stu-ai
页面名称：singlechoice
特别注意：这是一个移动端答题页面，需要支持多种交互状态
```

**❌ 避免的指令**：
```
帮我做个页面  # 太模糊
直接生成代码  # 跳过了必要的分析步骤
```

### 常见问题处理

**Q: AI 生成的组件不符合预期怎么办？**
A: 
1. 检查任务文档是否准确描述了需求
2. 提供更具体的修改指令
3. 必要时回到 Phase 2 重新拆解

**Q: 设计稿与代码差异较大怎么办？**
A:
1. 确认 design-tokens.json 中的值是否准确
2. 检查是否有复杂的视觉效果需要特殊处理
3. 考虑与设计师沟通简化设计

**Q: 如何处理复杂的交互状态？**
A:
1. 在 Phase 0 准备所有状态的设计稿
2. 仔细审查 state-matrix.yaml 文件
3. 在组件实现时明确指出状态管理需求

### 性能优化建议

1. **资源优化**：
   - 定期清理不必要的 SVG/PNG 文件
   - 压缩导出的图像资源

2. **代码优化**：
   - 复用通用组件
   - 避免过度嵌套的样式

3. **响应式优化**：
   - 优先使用 Tailwind 的响应式类
   - 避免固定像素值

---

## 📊 进度跟踪

### 宏观进度文件
位置：`luban-agent/ai-workflows/progress/{app_name}-{page_name}-progress.md`

示例内容：
```markdown
## Phase 0: 资产准备 - [已完成] (2024-01-15 10:30)
结果：PNG 图像已生成并保存

## Phase 1: 布局分析文档生成 - [已完成] (2024-01-15 11:00)
结果：components-location.md 已生成

## Phase 2: 任务拆解与文档生成 - [进行中]
当前状态：正在生成任务文档

## Phase 3: 组件实现 - [未开始]
## Phase 4: 视觉 QA 与迭代 - [未开始]
## Phase 5: 代码规范审查 - [未开始]
```

### 微观任务进度
位置：`luban-agent/ai-generates/fe/tasks/{app_name}/{page_name}/{page_name}_tasktree.md`

跟踪每个组件的开发状态。

---

## 🔧 故障排除

### 常见错误及解决方案

**错误：MCP 服务调用失败**
```
解决方案：
1. 检查 Figma 链接是否有效
2. 确认网络连接正常
3. 重试或手动下载 PNG 后放置到指定位置
```

**错误：组件代码生成失败**
```
解决方案：
1. 检查任务文档是否完整
2. 确认所有依赖文件存在
3. 简化组件复杂度后重试
```

**错误：视觉 QA 差异过大**
```
解决方案：
1. 检查 design-tokens.json 准确性
2. 确认 Figma 设计稿版本一致
3. 手动调整关键样式后重新运行 QA
```

### 获取帮助

1. **查看 AI 置信度汇总**：`ai-confidence-summary.yaml`
2. **检查具体错误信息**：AI 会在执行过程中提供详细的错误描述
3. **分阶段调试**：从最简单的组件开始，逐步增加复杂度

---

## 📚 最佳实践

### 设计稿准备
- 确保 Figma 设计稿图层命名清晰
- 统一使用设计系统中的颜色和字体
- 为复杂交互准备多个状态的设计稿

### 开发流程
- 严格按照 Phase 顺序执行，不要跳跃
- 每个 Phase 完成后进行充分的人工审查
- 及时更新进度文件，便于团队协作

### 代码质量
- 优先复用现有组件
- 保持组件的单一职责
- 编写清晰的组件文档

### 团队协作
- 定期同步进度文件
- 建立代码审查机制
- 记录和分享最佳实践

---

## 🎉 总结

通过遵循这套工作流程，您可以：
- **提高开发效率**：自动化处理重复性工作
- **确保设计还原度**：像素级精确的视觉 QA
- **保证代码质量**：结构化的审查流程
- **支持团队协作**：清晰的进度跟踪和文档

记住，这是一个**人机协作**的过程。AI 负责执行，您负责决策和质量把控。充分利用 AI 的能力，同时保持对最终产品质量的掌控。


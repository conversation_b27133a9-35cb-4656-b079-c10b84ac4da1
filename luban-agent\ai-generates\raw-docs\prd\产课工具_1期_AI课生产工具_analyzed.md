产课工具_1期_AI课生产工具

[产课工具_1期_All-in-one](https://wcng60ba718p.feishu.cn/wiki/TBw0wvyxKi9BSuko87ScEy2unSf)

结合教研管理的需求：[产课流程](https://uathqw3qd4.feishu.cn/docx/SCxJdGjcnohMbRxLSHxcVRIAn8g)

技术文档[ai课生产工具1期-逐字稿工具](https://wcng60ba718p.feishu.cn/wiki/LQPaw2Vt1iU9Xgk2imocUntYnff)

**版本管理**

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0.0 | 2025-4-8 | 袁全 | 新建 | 新建文档 |
| V5.0.0 | 4-12 | 袁全 | 升稿 | 补充核心页面交互图 |
| V9.0.0 | 4-15 | 袁全 | 升稿 | 完成9稿 |
|  | 4-16 | 袁全 | 修改了ui图 | ![in_table_image_OGJabJSWZox6HrxWGgXcyPWynxf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498517257.png)

清除修改按钮（p1） |
|  | 4-17 | 袁全 | 根据技术评审添加新说明 | 添加「取消生成功能」：ccloading过程中，显示“取消生成”，点击后弹窗显示二次确认弹窗：“确定取消生成吗？内容将回退到之前版本。” |
|  | 4-18 | 袁全 | 解析字段修改 | 经和确认，为了便于获取数字人+课程类型信息高中-数学-概念课-稿件名.zip   生成：excel中 [ 学科、学段、类型、名称、url ]类型枚举值：概念课、题型课、串讲课、专题课、讲解课、综合课、实验课、同步课、复习课、知识课cc |
|  | 4-21 | 袁全 | 补充：板书编辑：“板书内文字转为图片”需求说明 | ![in_table_image_BVolbC7ZeoNY0hxJvJIckoA5nKb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498520408.png) |

![in_table_image_Hbi4b1yUeoofbfxaFuhcrUOUnVd]

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来分析一下这张需求文档中的图片。

1.  **图片类型与核心价值分析**

    *   **图片类型**: 这是一张移动应用的用户界面（UI）截图，展示了“学习报告”功能的特定状态。
    *   **关键元素**:
        *   页面标题：“学习报告”。
        *   核心内容区域：
            *   提示文案：“你还没有发布过作品哦～”。
            *   引导操作：“去学习”按钮/链接。
        *   底部导航栏：包含“学习”、“练习”、“通讯录”、“消息”、“我的”五个 Tab，当前“学习” Tab 处于选中状态。
    *   **层级化结构与关联**:
        *   **顶层**: “学习报告”页面，作为“学习”模块下的一个子功能。
        *   **中间层**: 内容区域，展示报告的具体内容或状态。在此图中，展示的是“空状态”。
        *   **底层**: 底部导航栏，提供应用内主要功能模块的切换入口。
    *   **核心作用与价值**:
        *   **“学习报告”页面**: 其核心作用是向用户反馈学习成果或进度，在此特定场景下（空状态），它的价值在于告知用户尚未产生可报告的内容（未发布作品）。
        *   **空状态提示与引导**: 明确告知用户当前状态（无作品），并通过“去学习”引导用户进行能够产生报告内容的核心操作，提升用户活跃度和功能使用率。
        *   **底部导航**: 作为应用的基础导航框架，确保用户可以在不同核心功能区（学习、练习、社交、消息、个人中心）之间便捷切换。“学习”Tab 的选中状态表明当前页面隶属于学习主流程。

2.  **功能模块拆解**

    *   **学习报告展示模块**:
        *   **功能概述**: 负责获取并显示用户的学习报告数据。在此图中，具体体现为展示报告的“空状态”视图。
    *   **空状态引导模块**:
        *   **功能概述**: 在用户尚未生成学习报告数据（如未发布作品）时，提供友好的提示信息，并给出明确的操作指引（“去学习”），引导用户进行下一步操作。
    *   **应用主导航模块**:
        *   **功能概述**: 提供应用级别的导航功能，允许用户在“学习”、“练习”、“通讯录”、“消息”、“我的”等核心模块间跳转。

3.  **服务端数据需求**

    为了呈现如图所示的“学习报告”空状态页面，服务端需要提供以下数据信息：

    *   **用户作品状态**: 需要明确告知客户端当前用户是否发布过作品。
        *   **数据内容**: 一个表示用户作品发布情况的状态标识。
        *   **数据示例**:
            ```json
            {
              "report_status": {
                "has_published_works": false // 布尔值，false表示用户未发布过作品
                // 未来若有其他报告类型，可扩展此对象
              }
              // 可能还包含用户基本信息等，但根据截图空状态，核心是作品状态
            }
            ```
    *   **注意**: 服务端需根据业务逻辑判断用户是否满足“已发布作品”的条件，并返回相应的状态 (`has_published_works`)。前端依据此状态来决定展示空状态视图还是实际的报告内容。服务端无需关心具体的提示文案（“你还没有发布过作品哦～”）或按钮文字（“去学习”），这些通常由客户端或配置管理。

4.  **图表类型表示**

    该图片为 UI 界面截图，不属于流程图、时序图、类图、ER 图、甘特图或饼图等可以用 Mermaid 语法直接描述的图表类型。

【============== 图片解析 END ==============】



![in_table_image_OGJabJSWZox6HrxWGgXcyPWynxf]

###### 图片分析
【============== 图片解析 BEGIN ==============】

1.  **图片类型与核心价值分析**

    *   **图片类型**: 该图片是一个移动端应用的 **UI 界面设计稿 (Mockup/Wireframe)**，具体展示的是“训练结果”页面。
    *   **关键元素与组成**:
        *   **标题区**: "训练结果"，明确页面功能。
        *   **整体表现区**: 以圆形进度条和百分比（100%）展示“整体正确率”，辅以“总题数”（10）和“耗时”（00:01:30）的具体数值。
        *   **得分区**: 突出显示用户“本次得分”（100）。
        *   **详细指标区**: 针对特定训练类型（如“朗读”）提供更细维度的评价，包括“平均得分”（96）、“平均流利度”（95）、“平均完整度”（95）、“平均准确度”（98）。
        *   **操作区**: 提供后续操作按钮：“返回列表”和“再练一次”。
    *   **元素关联与层级**: 页面信息组织具有清晰的层级：首先是概览性总结（整体正确率、总题数、耗时），然后是关键结果（得分），接着是深入分析（细项指标），最后是用户决策（操作按钮）。这种由总到分的结构有助于用户快速掌握核心结果并了解细节。
    *   **核心作用与价值**: 此页面在互联网教育产品中扮演着关键的 **学习反馈** 角色。它为用户完成训练后提供即时、量化的表现评估，帮助用户了解自己的学习效果（如准确率、得分、各项细分能力）。这不仅增强了用户的成就感（得分高时），也指明了改进方向（得分低或某项指标弱时），并通过“再练一次”或“返回列表”引导用户进行下一步学习或回顾，形成有效的学习闭环。

2.  **功能模块拆解**

    *   **训练结果概览模块**:
        *   功能概述: 显示本次训练的总体性、总结性数据。
    *   **得分展示模块**:
        *   功能概述: 突出展示用户本次训练获得的总分。
    *   **专项指标分析模块 (以“朗读”为例)**:
        *   功能概述: 提供针对训练中特定技能或题型（如朗读）的详细性能指标分析。
    *   **用户操作引导模块**:
        *   功能概述: 提供用户完成结果查看后的下一步操作选项。

3.  **服务端数据需求**

    服务端需要为该“训练结果”页面提供以下数据内容：

    *   `overall_metrics`: 一个包含整体表现数据的对象。
        *   `accuracy_percentage`: 数字 (Number)，表示整体正确率，例如 `100`。
        *   `total_questions`: 数字 (Number)，表示总题数，例如 `10`。
        *   `time_taken`: 字符串 (String) 或 数字 (Number)，表示总耗时，例如 `"00:01:30"` 或 `90` (秒)。
    *   `score`: 数字 (Number)，表示本次训练的总得分，例如 `100`。
    *   `detailed_performance`: 一个数组 (Array)，包含各项详细指标分析。每个元素是一个对象，代表一个分析维度（如图中的“朗读”）。
        *   `[{`
        *   `type`: 字符串 (String)，表示指标类型或训练项目名称，例如 `"朗读"`。
        *   `average_score`: 数字 (Number)，表示该项的平均得分，例如 `96`。
        *   `average_fluency`: 数字 (Number)，表示该项的平均流利度，例如 `95`。
        *   `average_completeness`: 数字 (Number)，表示该项的平均完整度，例如 `95`。
        *   `average_accuracy`: 数字 (Number)，表示该项的平均准确度，例如 `98`。
        *   `}]`
        *   *(注：虽然图中只显示了“朗读”一项，但数据结构应设计为支持可能存在的多个详细指标项)*

4.  **Mermaid 图表描述**

    该图片为 UI 界面设计稿，展示的是信息呈现而非流程、时序、结构关系，不适用于 Mermaid 中的 flowchart, sequenceDiagram, classDiagram, erDiagram, gantt 或 pie 等图表类型进行直接描述。

5.  **内容限制**

    本次分析严格依据图片展示内容进行，未包含图片外的信息。

6.  **分析原则遵循**

    分析过程遵循了第一性原理（基于数据展示逻辑）、KISS 原则（结构简洁明了）、MECE 原则（模块划分清晰、无重叠遗漏）。

【============== 图片解析 END ==============】



- 在识别到用户有新修改后，按钮亮起
- 用户点击后：文本框内容回退至上一次提交的数据状态
- 点“确定”，2/3内容重置到之前版本，结束loading；
- 点“返回”，关闭弹窗，继续loading。
![image_B8Q6bMmcDo5zY3xfIS4ce4hBnxU](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498517914.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理，我们审视这张出自需求文档的图片：

1.  **图片类型与核心价值分析**
    *   **图片类型**: 这是一张移动应用界面的 **UI  mockup 或截图**。
    *   **核心功能**: 该界面旨在为用户提供一个清晰、可筛选的 **讲次列表**，方便用户选择想要学习或回顾的课程内容。
    *   **关键元素与层级结构**:
        *   **顶层**: 页面标题 "选择讲次"，明确页面功能。
        *   **中层**: 状态筛选 Tab 栏，包含 "全部"、"未学完"、"已学完" 三个选项，用于过滤讲次列表。当前选中状态为 "全部"。
        *   **底层**: 讲次列表区域，垂直滚动展示符合筛选条件的讲次条目。
    *   **元素间关联**: Tab 栏的选择状态直接决定下方列表展示的内容。用户点击不同的 Tab，列表会动态刷新以显示对应学习状态的讲次。列表中的每个讲次条目是一个独立的单元，包含讲次序号、标题和学习状态，并带有导航指示（右箭头），暗示点击后可进入该讲次的详情或学习页面。
    *   **需求价值**: 在互联网教育产品中，此界面是课程内容导航的核心环节。它通过提供明确的讲次罗列和便捷的状态筛选功能，有效帮助用户管理学习进度、快速定位目标学习内容，提升了学习路径的清晰度和用户体验。

2.  **功能模块拆解**
    *   **讲次状态筛选 (Tabs)**: 提供按 "全部"、"未学完"、"已学完" 状态筛选讲次列表的功能。
    *   **讲次列表展示 (List)**: 根据筛选条件，展示讲次信息，包括讲次序号（如“讲次01”）、讲次标题（如“第一讲 xxx”）和当前学习进度/状态（如“已学1%”、“未开始”）。
    *   **讲次导航入口 (List Item)**: 列表中的每一项作为一个可交互单元，用户点击后应能跳转至对应的讲次详情或学习页面。

3.  **服务端数据需求**
    服务端需要根据用户请求（可能包含筛选条件，如“未学完”）提供以下数据结构，用于渲染讲次列表：

    *   需要提供一个 **讲次对象数组 (Array of Objects)**。
    *   数组中 **每个讲次对象** 应至少包含以下字段：
        *   `session_id`: 讲次的唯一标识符（用于后续导航或操作，虽然未直接显示，但逻辑上必需）。
        *   `session_display_order`: 用于显示的讲次序号或编号，字符串类型（例如："讲次01", "讲次02"）。
        *   `session_title`: 讲次的标题，字符串类型（例如："第一讲 xxxxxxx..."）。
        *   `learning_status_text`: 显示的学习状态文本，字符串类型（例如："已学1%", "未开始", "已学完"）。该文本应直接反映用户的实际学习进度。

    *   服务端需支持根据筛选条件（全部、未学完、已学完）返回对应的讲次列表数据。

4.  **图表类型转换**
    该图片为 UI 界面截图，非标准流程图、时序图、类图、ER 图、甘特图或饼图，因此不适用 Mermaid 语法进行直接转换表示其布局结构。若要表示从此页面出发的用户操作流程，可使用 Mermaid flowchart，但无法表示此 UI 界面本身。

【============== 图片解析 END ==============】



![image_DQmBbeJJiopoyfx8fQRc7W3dnlg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498518675.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 1. 图片类型与关键元素解析

*   **图片类型**: UI 界面截图 (User Interface Screenshot)
*   **核心功能**: 展示“班级通知”列表。
*   **关键元素与组成**:
    *   **页面标题**: “班级通知”，明确了当前页面的核心内容。
    *   **通知列表**: 占据页面主体，纵向排列展示多条班级通知。
    *   **单条通知项**: 列表中的每一个条目，包含通知的基本信息。
*   **层级化结构与关联**:
    *   该“班级通知”页面是App内的一个功能模块。
    *   页面包含一个通知列表。
    *   通知列表由多个独立的通知项组成。
    *   每个通知项展示了该通知的关键摘要信息（标题、发送人、时间、未读状态）。
*   **核心作用与价值 (结合互联网教育领域)**: 此界面为师生、家长提供了一个集中的渠道来查看与班级相关的官方通知，如放假安排、教务信息等。其核心价值在于保证重要信息的有效触达，提高沟通效率，是班级管理和信息同步的重要组成部分。

### 2. 功能模块拆解

*   **班级通知列表模块**:
    *   **功能概述**: aggregation and display of multiple class notifications in a list format. Likely supports fetching notifications from a backend service.
*   **单条通知展示模块**:
    *   **功能概述**: Displays summary information for a single notification, including its title, sender, publication time, and read status. May serve as an entry point to view the full notification details upon interaction (though interaction details are not shown).
*   **未读状态提示模块**:
    *   **功能概述:** Visual indication (red dot) marking notifications that the user has not yet viewed.

### 3. 服务端需提供的功能与数据内容

为了渲染如图所示的“班级通知”列表界面，服务端需要提供一个接口，该接口能够返回指定班级的通知列表数据。

*   **主要功能**: 查询并返回班级通知列表。
*   **返回的数据内容 (预期)**: 服务端应返回一个包含多个通知对象的 **数组 (List/Array)**。数组中的 **每个对象** 代表一条班级通知，应包含以下字段：
    *   `notification_id` (String/Number): 通知的唯一标识符。
    *   `notification_title` (String): 通知的标题 (例如：“【重要通知】关于XXXX寒假放假安排”)。
    *   `sender_name` (String): 通知发送者的姓名 (例如：“张三”)。
    *   `publish_time` (Timestamp/String): 通知的发布时间戳或格式化时间字符串。前端可根据此数据计算并显示相对时间（如：“1小时前”、“昨天”）。
    *   `is_read` (Boolean): 标记当前用户是否已读该通知 (例如：`false` 表示未读，对应图中的红点；`true` 表示已读)。

**数据组合示例 (JSON 格式):**

```json
[
  {
    "notification_id": "1001",
    "notification_title": "【重要通知】关于XXXX寒假放假安排",
    "sender_name": "张三",
    "publish_time": 1746480000000, // Example timestamp (milliseconds)
    "is_read": false
  },
  {
    "notification_id": "1002",
    "notification_title": "【教务通知】关于XXXX开展信息登记的通知",
    "sender_name": "李四",
    "publish_time": 1746393600000, // Example timestamp (milliseconds)
    "is_read": false
  },
  {
    "notification_id": "1003",
    "notification_title": "关于期末考试安排的初步通知",
    "sender_name": "王五",
    "publish_time": 1746307200000, // Example timestamp (milliseconds)
    "is_read": true
  }
  // ... more notification objects
]
```

### 4. 图表类型描述

该图片为 UI 界面截图，不属于流程图、时序图、类图、ER图、甘特图或饼图等适合使用 Mermaid 语法描述的图表类型。因此，不生成 Mermaid 图表。

【============== 图片解析 END ==============】



![in_table_image_LgbtbiVXXoe2YXx9q4LcUVp2nyc]

###### 图片分析
【============== 图片解析 BEGIN ==============】

**1. 图片类型与概述**

*   **图片类型:** 用户界面（UI）截图。
*   **核心内容:** 展示了在线教育应用中“课中互动”功能的“互动”标签页界面。
*   **关键元素:** 包含顶部标题、标签导航栏（答题卡、互动、资料）、以及一个互动活动列表。
*   **层级结构:**
    *   **顶层:** 课中互动界面 (In-Class Interaction Screen)
        *   **组成部分 1:** 导航区域 (Navigation Area) - 包含 "课中互动" 标题和 "答题卡"、"互动"、"资料" 三个标签页。
        *   **组成部分 2:** 内容区域 (Content Area) - 显示当前选中标签页（"互动"）的内容，即互动活动列表。
            *   **子部分:** 互动活动列表 (Interaction List) - 包含多个互动项目。
                *   **孙部分:** 单个互动项目 (Interaction Item) - 每个项目代表一个具体的互动活动（如签到、投票、抢答等），并显示其类型图标、状态、简要描述/标题和相关时间信息。
*   **关联与价值:**
    *   **导航区域** 允许用户在不同的课中活动类型（答题、互动、查看资料）之间切换。
    *   **互动列表** 是核心，集中展示了教师发起的、需要学生参与的各类即时互动活动。
    *   **单个互动项目** 提供活动的关键信息（类型、状态、内容概要），引导学生了解并参与。
    *   **整体价值:** 该界面旨在提升在线课堂的实时互动性和参与感，为教师提供了多种互动工具，并为学生提供了一个清晰、集中的参与入口，是增强教学效果、维持学生注意力的重要功能模块。

**2. 功能模块拆解**

*   **标签页导航 (Tab Navigation):**
    *   **功能概述:** 提供 "答题卡"、"互动"、"资料" 三个入口，允许用户切换查看不同类型的课中内容。图片显示当前处于 "互动" 标签。
*   **互动活动列表展示 (Interaction List Display):**
    *   **功能概述:** 动态加载并展示当前课程中所有已发起或正在进行的互动活动。列表按时间顺序或其他逻辑排序（图片中未明确显示排序规则，可能是按发布时间）。
*   **互动状态展示 (Interaction Status Display):**
    *   **功能概述:** 清晰地标示每项互动的当前状态，如 "已签到"、"未签到"、"已参与"、"未参与"、"进行中"、"已结束"。
*   **互动类型识别 (Interaction Type Identification):**
    *   **功能概述:** 通过图标和/或文字（如“签到”、“投票”、“抢答”、“选择题”、“答题器”）区分不同的互动活动类型。
*   **互动信息预览 (Interaction Information Preview):**
    *   **功能概述:** 显示每项互动的关键摘要信息，如投票问题、抢答说明、选择题题干、签到时间/截止时间等。

**3. 服务端数据需求**

为渲染此“互动”列表界面，服务端需要提供一个接口，该接口能返回当前用户在指定课程/课堂中的互动活动列表数据。列表中的每个元素（代表一个互动活动）应包含以下信息：

```json
[
  {
    "interaction_id": "unique_interaction_identifier_1", // 互动唯一ID
    "interaction_type": "SIGN_IN",                     // 互动类型 (如 SIGN_IN, VOTE, QUICK_ANSWER, MULTIPLE_CHOICE, CLICKER)
    "status": "SIGNED_IN",                               // 互动状态 (如 SIGNED_IN, NOT_SIGNED_IN, PARTICIPATED, NOT_PARTICIPATED, IN_PROGRESS, ENDED)
    "status_text": "已签到",                             // 状态描述文本 (根据status生成，如此处为“已签到”)
    "title": "签到",                                    // 互动标题或类型名称 (如“签到”，或具体问题“天空是什么颜色的？”)
    "description": "签到成功",                           // 互动相关的描述性文本或副标题 (如“签到成功”，“截至签到”，或者问题描述等)
    "timestamp_info": {                                // 时间相关信息
        "type": "COMPLETION_TIME",                     // 时间类型 (如 DEADLINE, COMPLETION_TIME, START_TIME, END_TIME)
        "time": "15:39:32"                             // 具体时间戳或格式化时间字符串
    },
    "icon_type": "sign_in_icon"                        // (可选) 用于前端展示的图标类型标识符
  },
  {
    "interaction_id": "unique_interaction_identifier_2",
    "interaction_type": "SIGN_IN",
    "status": "NOT_SIGNED_IN",
    "status_text": "未签到",
    "title": "签到",
    "description": "截至签到",
    "timestamp_info": {
        "type": "DEADLINE",
        "time": "15:41:30"
     },
    "icon_type": "sign_in_icon"
  },
  {
    "interaction_id": "unique_interaction_identifier_3",
    "interaction_type": "VOTE",
    "status": "PARTICIPATED",
    "status_text": "已参与",
    "title": "投票", // 或可将问题作为title
    "description": "天空是什么颜色的？", // 问题详情放在description
    "timestamp_info": null, // 此项无明确时间信息展示
    "icon_type": "vote_icon"
  },
  {
    "interaction_id": "unique_interaction_identifier_4",
    "interaction_type": "QUICK_ANSWER",
    "status": "NOT_PARTICIPATED",
    "status_text": "未参与",
    "title": "抢答",
    "description": "第一名的奖品是获得一次...", // 描述可能较长或被截断
    "timestamp_info": null,
    "icon_type": "quick_answer_icon"
   },
  {
    "interaction_id": "unique_interaction_identifier_5",
    "interaction_type": "MULTIPLE_CHOICE",
    "status": "IN_PROGRESS",
    "status_text": "进行中",
    "title": "选择题",
    "description": "中国四大发明是指南针...", // 题干预览
    "timestamp_info": null,
    "icon_type": "multiple_choice_icon"
  },
  {
    "interaction_id": "unique_interaction_identifier_6",
    "interaction_type": "CLICKER", // "答题器"可能对应CLICKER类型
    "status": "ENDED",
    "status_text": "已结束",
    "title": "答题器",
    "description": "请选择你的答案", // 指导性文本
    "timestamp_info": null,
    "icon_type": "clicker_icon"
  }
  // ... more interaction items
]
```

**核心服务端功能:**

*   **查询互动列表:** 提供API接口，根据课程ID、用户ID等上下文信息，返回当前用户可见的互动活动列表及其详细状态和内容摘要。
*   **实时更新推送 (可选但推荐):** 为保证互动状态（如从未参与到进行中，从进行中到已结束）的实时性，建议采用WebSocket等技术向客户端推送互动状态的变更。

**4. 图表类型转换 (Mermaid)**

该图片为UI截图，不适用于转换为流程图、时序图、类图、ER图、甘特图或饼图。

【============== 图片解析 END ==============】



![in_table_image_BVolbC7ZeoNY0hxJvJIckoA5nKb]

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来分析一下这张图片。

1.  **图片类型、关键元素、层级结构与价值分析**

    *   **图片类型**：这是一张 **UI界面截图/设计稿**，展示了移动应用中的“请假申请”功能界面。
    *   **关键元素**：
        *   页面标题：“请假申请”
        *   申请人信息（隐式）：虽然只显示了“王晓晓”，但暗示了需要关联特定学生。
        *   请假类型选择：提供“事假”、“病假”选项。
        *   开始时间选择：用于选择请假开始日期和时间。
        *   结束时间选择：用于选择请假结束日期和时间。
        *   请假事由输入：多行文本框，用于填写具体原因（限制200字）。
        *   图片上传：允许用户添加图片附件（最多9张）。
        *   提交按钮：“提交”操作。
    *   **层级结构与关联**：
        *   该界面是一个表单结构，用户按顺序填写或选择信息。
        *   请假类型、开始时间、结束时间、请假事由、图片附件是并列的必填或选填信息项。
        *   所有信息项构成一个完整的请假申请记录。
        *   “提交”按钮是最终的操作触发器，将收集到的信息发送处理。
    *   **核心作用与价值（结合互联网教育背景）**：
        *   **作用**：为学生（或家长）提供一个标准化的、便捷的线上请假申请入口。
        *   **价值**：
            *   **效率提升**：简化传统纸质或口头请假流程，信息传递更快捷。
            *   **规范管理**：统一请假信息格式，便于学校或教师进行管理、审批和统计。
            *   **信息追溯**：请假记录电子化存储，方便查询和追溯。
            *   **用户体验**：提供符合移动端使用习惯的操作方式。
            *   **数据整合**：可与考勤系统、通知系统等打通，形成更完善的校务管理闭环。

2.  **功能模块拆解**

    *   **请假类型选择模块**：
        *   **功能概述**：允许用户在预设的请假类型（如事假、病假）中进行单项选择。
    *   **时间选择模块**：
        *   **功能概述**：包含“开始时间”和“结束时间”两个子模块，分别用于调起日期时间选择器，让用户精确选择请假的起止时刻。
    *   **请假事由输入模块**：
        *   **功能概述**：提供一个文本输入区域，让用户详细说明请假的原因，并有字数限制提示。
    *   **附件上传模块**：
        *   **功能概述**：允许用户从设备相册选择或拍摄图片，作为请假申请的证明材料（如病假条），支持多张上传并有数量上限。
    *   **申请提交模块**：
        *   **功能概述**：用户填写完所有必要信息后，点击此按钮将整个请假申请数据包提交至服务端处理。

3.  **服务端需提供的功能与数据内容**

    *   **需提供给前端的数据/能力**：
        *   用户信息：需要能够识别当前操作用户（学生“王晓晓”）的身份标识（如 `student_id`）。
        *   （可能）预设请假类型列表：虽然图中只显示了“事假”和“病假”，但如果类型是动态配置的，服务端需要提供此列表。
        *   （可能）配置信息：如请假事由的最大字数限制（200）、附件图片的最大数量（9）。
        *   图片上传接口/服务：提供接收图片文件上传的能力，并返回图片的标识或URL。

    *   **需处理前端提交的数据**：
        *   `student_id`：提交申请的学生唯一标识。
        *   `leave_type`：用户选择的请假类型（如 "事假" 或 "病假" 的标识符）。
        *   `start_time`：用户选择的请假开始时间（格式需约定，如时间戳或 `YYYY-MM-DD HH:mm:ss`）。
        *   `end_time`：用户选择的请假结束时间（格式需约定）。
        *   `reason`：用户填写的请假事由文本内容。
        *   `attachments`：一个包含多个图片标识符或URL的列表/数组，对应用户上传的附件。

    *   **服务端核心功能**：
        *   **接收申请**：接收前端提交的请假申请数据。
        *   **数据校验**：
            *   检查各字段是否符合要求（如 `student_id` 是否有效、时间格式是否正确、`end_time` 是否晚于 `start_time`、`reason` 是否超长等）。
            *   校验附件数量是否超限。
        *   **数据存储**：将校验通过的请假申请信息（包括学生ID、类型、时间、事由、附件列表）存入数据库，生成一条请假记录，并标记初始状态（如“待审批”）。
        *   **（可能）触发通知**：根据业务逻辑，可能需要向相关人员（如班主任、管理员）发送新请假申请的通知。
        *   **响应返回**：向前端返回处理结果（成功或失败信息，失败时应包含原因）。

4.  **Mermaid 图**

    此图片为UI界面截图，不适合直接转换为Mermaid支持的标准图表类型（如流程图、时序图、类图等）。它展示的是一个静态的界面布局和表单元素，而非一个动态过程或系统结构。

5.  **内容限制**

    总结内容严格基于图片中可见的元素及其明显的功能含义，未涉及图片外的信息或进行联想推测。

6.  **原则遵循**

    分析过程遵循了第一性原理（从界面元素的基础功能出发）、KISS原则（描述简洁明了）、MECE原则（功能模块划分清晰、无重叠遗漏）。

【============== 图片解析 END ==============】



**设计稿**

UE：https://mastergo.com/file/149512448061157?fileOpenFrom=home&page_id=0%3A2



### 一、背景和目标

#### 1.1、需求背景

本期产品目标是：完成供教研老师使用的AI课生产第1步工具，让获得该地址的教研老师可以在PC:web浏览器内输入对应课程地址后，****进行AI课生产的第1步：“逐字稿、朗读稿、板书动画的编辑和校对”****。

为了简化工具尽快上线，本期：**不做任务管理，****校验内网，****不对地址访问做身份控制**

- 业务层面：
- 教研老师：
- 场景1:课程从0到1生产，使用该工具进行“逐字稿、朗读稿、板书动画编辑”，完成后提交至：圈画生产
- 场景2:用于课程修改，使用该工具对已有内容进行：“逐字稿、朗读稿、板书动画修改”，完成后提交重新进行圈画生产
- 各校老师
- 未来使用该工具对AI课内容进行二创
|  | 教研老师 | C端用户 |
| --- | --- | --- |
| P0核心功能 | 编辑：段落核对：逐字稿、系统动画、朗读稿 |  |
| 未来功能 |  | 使用本期工具进行二创 |

#### 1.2、项目收益

完成产品工具P0核心功能，确保在5月上线的版本可以跑通整个产课业务MVP流程，可供老师使用



#### 1.3、覆盖用户

目标用户：教研教师



#### 1.4、方案简述

1. 上下游关系：第1步 逐字稿&朗读稿生产
![board_X1qtwxmsihWp5QbR1g1cN3r3nUC](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498512183.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

## 1. 图片解析概述

该图片展示的是互联网教育产品中“历史互动记录”功能的 **UI 界面设计**。它清晰地呈现了用户查看过往互动（主要是内容分享）信息的功能界面布局和关键信息元素。

*   **核心目的:** 为用户提供一个集中的地方，回顾和追溯与其他用户（或系统）之间基于内容分享的历史互动行为。
*   **关键元素:**
    *   **页面标题:** 明确功能为“历史互动记录”。
    *   **时间分组:** 以“今日”、“昨日”等相对时间或具体日期对互动记录进行分组，便于用户按时间维度浏览。
    *   **互动记录条目:** 列表形式展示单条互动信息。
    *   **单条记录构成:** 时间戳、发起互动的用户信息（头像、昵称）、互动行为描述（如“向你分享”）、互动关联的内容信息（内容标题、内容类型图标等）。
*   **元素关联:** 页面以时间为主要线索进行组织，将分散的互动记录按照发生时间聚合在对应的日期分组下，每条记录内部则展示了互动参与者、行为和对象的关键信息。
*   **核心价值:** 在教育场景下，此功能有助于用户追踪和管理学习内容的分享与接收过程，了解与学伴或老师的互动轨迹，便于快速找回分享过的学习资料。

## 2. 功能模块拆解

基于图片内容，该界面主要包含以下功能模块：

*   **历史互动记录展示:**
    *   **功能概述:** 显示当前用户接收到的或参与的历史互动记录列表。
*   **时间分组展示:**
    *   **功能概述:** 将历史互动记录按日期（如“今日”、“昨日”或更早的具体日期）进行分组展示，优化浏览体验。
*   **单条互动记录详情:**
    *   **功能概述:** 展示单条互动记录的关键信息，包括：
        *   **互动时间:** 显示该互动的具体发生时间（时:分）。
        *   **互动发起方信息:** 显示发起互动用户的头像和昵称。
        *   **互动行为描述:** 说明互动类型，如图中所示为“向你分享”。
        *   **互动内容概要:** 显示互动关联的内容标题，并可能通过图标指示内容类型（如图中的播放按钮可能代表音视频）。

## 3. 服务端数据接口需求（概要）

为实现该界面展示，服务端需要提供一个接口，返回历史互动记录列表数据。建议的数据结构如下（以 JSON 为例）：

服务端需返回一个包含互动记录的列表（`interaction_list`）。该列表可以按时间倒序排列。

列表中的每一条互动记录对象（`interaction_item`）应包含以下字段：

*   `timestamp` (Number/String): 互动发生的精确时间戳（如 Unix 时间戳 or ISO 8601 格式字符串），用于客户端进行时间显示和日期分组。
*   `user_info` (Object): 互动发起者的用户信息对象。
    *   `user_id` (String): 用户唯一标识符。
    *   `nickname` (String): 用户昵称（例如：“小鹿酱”）。
    *   `avatar_url` (String): 用户头像图片的 URL。
*   `action_description` (String): 描述互动行为的文本（例如：“向你分享”）。此字段也可由客户端根据 `action_type` (若有) 本地化生成。
*   `shared_content` (Object): 分享的内容信息对象。
    *   `content_id` (String): 内容的唯一标识符。
    *   `title` (String): 内容的标题（例如：“大王来了”）。
    *   `content_type` (String): 内容的类型标识符（例如：“video”, “audio”, “article”），用于客户端显示对应的图标。
    *   `content_url` (String, Optional): 指向该内容的链接（供后续可能的跳转使用）。

**示例数据结构 (扁平化列表):**

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "interaction_list": [
      {
        "timestamp": 1678886100, // 假设这是 "今日 10:35" 的时间戳
        "user_info": {
          "user_id": "user123",
          "nickname": "小鹿酱",
          "avatar_url": "https://example.com/avatar1.png"
        },
        "action_description": "向你分享",
        "shared_content": {
          "content_id": "content456",
          "title": "大王来了",
          "content_type": "video",
          "content_url": "https://example.com/content/456"
        }
      },
      {
        "timestamp": 1678885500, // 假设这是 "今日 10:25" 的时间戳
        "user_info": {
            "user_id": "user123",
            "nickname": "小鹿酱",
            "avatar_url": "https://example.com/avatar1.png"
          },
        "action_description": "向你分享",
        "shared_content": {
            "content_id": "content789",
            "title": "小王子选段",
            "content_type": "audio",
            "content_url": "https://example.com/content/789"
          }
      },
      {
        "timestamp": 1678799700, // 假设这是 "昨日 15:15" 的时间戳
        "user_info": {
          "user_id": "user123",
          "nickname": "小鹿酱",
          "avatar_url": "https://example.com/avatar1.png"
        },
        "action_description": "向你分享",
        "shared_content": {
          "content_id": "content101",
          "title": "阅读理解技巧",
          "content_type": "article",
           "content_url": "https://example.com/content/101"
        }
      }
      // ... more interaction items
    ],
    "has_more": true // 用于分页加载
  }
}
```

**注意:** 服务端只需提供原始数据列表和必要的字段，客户端负责根据 `timestamp` 进行日期分组（“今日”、“昨日”等标签的生成与判断）和界面渲染。无需关注设备状态栏信息（电量、时间、网络）及具体的UI样式（颜色、字体、按钮样式等）。

## 4. Mermaid 图表

该图片为 UI 界面设计图，不适用于流程图、时序图等 Mermaid 图表类型进行描述。

【============== 图片解析 END ==============】



1. 用户访问路径：
1. 总部教研管理在线下完成课程ID创建后，通过feishu表格管理地址，并分配教研老师
1. 教研复制本课的逐字稿编辑“地址1”，粘贴到web浏览器中“地址1”，回车后可进行编辑
![board_RgiYwQEfdhEL09bmPA5ceV8gnmh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498513243.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网教育领域的产品经理，我对当前需求文档中提供的这张图片进行了分析。

1.  **图片类型与核心解读**
    *   **图片类型**: UI 界面截图 (或 Mockup)。
    *   **核心内容**: 展示了移动应用内“已购课程”页面的用户界面。
    *   **关键元素与层级结构**:
        *   **顶层**: 页面标题“已购课程”。
        *   **中层 (功能区)**:
            *   **筛选/分类 Tab**: 提供“全部”、“学习中”、“已学完”三种状态筛选，允许用户按学习进度分类查看课程。当前选中“全部”。
            *   **课程列表**: 垂直滚动区域，展示用户购买的课程信息卡片。
        *   **底层 (单项元素 - 课程卡片)**: 每个卡片代表一门已购课程，包含：
            *   课程封面图
            *   课程标题
            *   课程类型标签 (如“视频课”)
            *   学习进度 (百分比)
            *   课程原价 (带删除线)
            *   实际购买价格
            *   行动点：“去学习”按钮
        *   **列表末尾提示**: “一 已经到底啦 一”，表示列表内容已全部加载。
        *   **全局导航 (底部)**: 应用的标准底部 Tab 导航栏，包含“首页”、“学习”（当前激活）、“我的”入口。
    *   **核心作用与价值**: 此页面是用户管理和访问已购学习资源的核心入口。通过清晰的列表展示和状态筛选，方便用户快速找到并进入想要学习的课程，提升学习体验和效率。筛选功能满足了不同场景下用户的查找需求（例如，快速找到未完成的课程继续学习）。

2.  **功能模块拆解**
    *   **页面标题**: 显示当前页面功能为“已购课程”。
    *   **课程筛选**:
        *   功能: 允许用户根据“全部”、“学习中”、“已学完”三种状态筛选课程列表。
        *   概述: 提供课程分类视图，方便用户按需查找。
    *   **课程列表**:
        *   功能: 展示符合筛选条件的已购课程。
        *   概述: 以卡片形式纵向排列课程，每张卡片包含课程关键信息。
    *   **课程卡片**:
        *   功能: 展示单门课程的核心信息（封面、标题、类型、进度、价格）并提供学习入口。
        *   概述: 用户获取课程概要信息和进入学习的主要交互单元。
    *   **学习入口 (“去学习”按钮)**:
        *   功能: 点击后跳转至对应课程的学习详情页或播放页。
        *   概述: 用户开始或继续学习课程的直接操作点。
    *   **底部导航**:
        *   功能: 提供应用内主要功能区（首页、学习、我的）的快速切换。
        *   概述: 标准的应用全局导航组件。

3.  **服务端数据需求 (接口)**

    为了渲染“已购课程”页面，服务端需要提供一个接口（例如 `/api/user/purchased_courses`），该接口需支持根据课程状态进行筛选。

    *   **请求参数**:
        *   `filter_status` (可选): 用于筛选课程状态，可接受的值如 `all` (全部), `in_progress` (学习中), `completed` (已学完)。默认为 `all`。
        *   `page` / `limit` (可选): 用于分页加载。

    *   **返回数据结构 (JSON 示例)**:
        ```json
        {
          "code": 0, // 状态码，0表示成功
          "message": "success", // 状态信息
          "data": {
            "courses": [ // 课程列表数组
              {
                "course_id": "course123", // 课程唯一标识符
                "title": "AI绘画第一课：Midjourney入门与实战", // 课程标题
                "cover_image_url": "https://example.com/cover1.jpg", // 课程封面图片URL
                "course_type_tag": "视频课", // 课程类型标签文本
                "learning_progress": 30, // 学习进度（百分比，数字类型）
                "original_price": "99.00", // 原价（字符串或数字，包含或不含货币符号需约定）
                "purchase_price": "69.00", // 购买时价格（字符串或数字）
                "study_action_url": "/course/study/course123" // “去学习”按钮对应的跳转链接或标识
              },
              {
                "course_id": "course456",
                "title": "另一门很棒的课程",
                "cover_image_url": "https://example.com/cover2.jpg",
                "course_type_tag": "直播课",
                "learning_progress": 0, // 示例：未开始学习
                "original_price": "199.00",
                "purchase_price": "199.00", // 示例：原价购买
                "study_action_url": "/course/study/course456"
              }
              // ...更多课程对象
            ],
            "has_more": false // 是否还有更多数据可供加载（用于分页）
          }
        }
        ```

    *   **关键数据字段说明**:
        *   `courses`: 一个数组，包含用户已购买的课程对象。
        *   `course_id`: 课程的唯一ID，用于后续操作（如跳转）。
        *   `title`: 课程的名称。
        *   `cover_image_url`: 用于展示课程封面的图片地址。
        *   `course_type_tag`: 显示课程类型的文本标签。
        *   `learning_progress`: 用户的学习进度百分比（纯数字）。
        *   `original_price`: 课程的原始标价。
        *   `purchase_price`: 用户购买时支付的价格。
        *   `study_action_url`: 用户点击“去学习”时需要跳转的目标地址或标识。

4.  **图表类型转换 (Mermaid)**
    此图片为 UI 界面截图，不属于流程图、时序图、类图、ER 图、甘特图或饼图等适合使用 Mermaid 语法直接转换的图表类型。因此，不适用 Mermaid 进行描述。

【============== 图片解析 END ==============】



1. 工具交互
- 用户一个段落内完成：0.Part编辑、1.逐字稿编辑、2.朗读稿编辑、3.板书编辑 
- 依次完成各段落后，提交整课
**0.Part编辑**

编辑Part名称

可编辑：位置上下移、在之前/后新增段落、删除本段落

新增Part：默认空值，用户在「1逐字稿」输入内容后「保存」，再点「重新生产」生成后续内容

![image_AOX7bUhHLoIPoTx54lecpz3ynod](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498521059.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 图片解析：学习报告 UI 界面

1.  **图片类型与核心价值**
    *   **图片类型**: UI Mockup / 界面设计稿。
    *   **来源**: 需求文档。
    *   **核心元素**: 该界面展示了用户的“学习报告”功能，关键元素包括用户信息、时间筛选维度、学习数据概览以及学习任务明细列表。
    *   **层级结构与关联**:
        *   **顶层**: 包含页面标题“学习报告”和用户信息（头像、昵称）。
        *   **筛选层**: 提供时间维度切换（今日、本周、本月、其他时间段），用于过滤报告数据。当前选中“今日”。
        *   **概览层**: 汇总展示核心学习指标（学习时长、获得进步奖数、任务完成度、学习总天数）。
        *   **明细层**: 列表形式展示具体的学习任务项，包含任务类型、完成状态、学习时段和时长。
        *   **操作层**: “查看详细报告”按钮，引导用户查看更全面的数据。
    *   **核心作用与价值**: 在互联网教育产品中，此界面旨在向用户（学生/家长/老师）直观反馈学习情况和进度，通过量化数据和任务状态，帮助用户了解学习效果、投入时间及完成情况，起到数据反馈、效果评估和学习激励的作用。

2.  **功能模块拆解**
    *   **用户信息展示**: 显示当前查看报告的用户头像和昵称。
    *   **时间周期筛选**: 允许用户选择不同的时间范围（今日、本周、本月、其他）来查看对应时间段的学习报告。
    *   **报告生成时间戳**: 显示报告数据的统计截止时间。
    *   **学习数据概览**:
        *   `学习时长`: 展示所选时间周期内的总学习时间。
        *   `获得进步奖`: 展示所选时间周期内获得的奖励数量。
        *   `学习任务`: 显示所选时间周期内已完成任务数与总任务数的比率。
        *   `学习总天数`: （推测为累计）展示用户的总学习活跃天数。
    *   **学习任务列表**:
        *   逐条展示所选时间周期内的学习任务。
        *   每条任务包含：任务类型名称（如：专项知识点、单元测试）、任务状态（如：已学完、学习中、未开始、已结束）、任务发生时段（起始时间-结束时间）、任务耗时。
    *   **详细报告入口**: 提供一个入口，用于跳转到可能包含更丰富数据维度的详细报告页面。

3.  **服务端数据需求**

    为渲染此“学习报告”界面，服务端需根据客户端请求（包含用户ID和选定的时间周期参数）提供以下数据结构：

    *   **用户信息 (`user_info`)**:
        *   `avatar_url`: 字符串 (用户头像图片的URL)
        *   `nickname`: 字符串 (用户昵称，如 "张三")
        *   `role_tag`: 字符串 (可选，表示用户角色或标签，如 "小鹿老师")

    *   **报告基本信息 (`report_info`)**:
        *   `selected_period`: 字符串/枚举 (表示当前选中的时间周期，如 "today", "this_week", "this_month", "custom_range")
        *   `generation_time`: 字符串/时间戳 (报告生成时间，格式如 "YYYY-MM-DD HH:mm")

    *   **学习数据概览 (`summary_stats`)**:
        *   `total_learning_duration_minutes`: 整数 (总学习时长，单位：分钟，如 20)
        *   `awards_received_count`: 整数 (获得进步奖数量，如 12)
        *   `tasks_completed_count`: 整数 (已完成任务数，如 4)
        *   `tasks_total_count`: 整数 (总任务数，如 5)
        *   `cumulative_study_days`: 整数 (学习总天数，如 120)

    *   **学习任务明细列表 (`task_details_list`)**: 一个对象数组，每个对象代表一个任务，包含：
        *   `task_type`: 字符串 (任务类型名称，如 "专项知识点", "单元测试", "拓展阅读", "课后作业", "互动问答")
        *   `status`: 字符串/枚举 (任务状态，需映射到前端显示文字，如 "completed" -> "已学完", "in_progress" -> "学习中", "not_started" -> "未开始", "finished" -> "已结束") // 注意：“已结束”可能与“已学完”语义不同，需明确定义
        *   `start_time`: 字符串 (任务开始时间，格式如 "HH:mm"，如 "9:30") - 可能为空
        *   `end_time`: 字符串 (任务结束时间，格式如 "HH:mm"，如 "9:45") - 可能为空
        *   `duration_minutes`: 整数 (任务耗时，单位：分钟，如 15) - 可能为空或由起止时间计算

4.  **图形化表示 (Mermaid)**

    该图片为产品UI界面设计稿，并非流程图、时序图、类图、ER图、甘特图或饼图。它展示的是一个静态的界面布局和数据呈现，不直接描述动态流程或结构关系。因此，根据图片内容，无法直接应用 Mermaid 语法进行准确的图形化表示。

【============== 图片解析 END ==============】



按顺序展示段落名称

![image_AhmJbL2yUopFzSxfulOcdvGRn2e](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498521702.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

## 图片解析总结

### 1. 图片类型与核心价值

*   **图片类型:** UI 界面设计图 (Mockup/Wireframe)。
*   **核心价值:** 该界面核心作用是允许用户在（可能为）购买课程或服务的结算流程中，查看并选择可用的优惠券，或明确选择不使用优惠券，以完成代金券抵扣环节。它连接了用户的购买意愿与平台的优惠策略，是提升转化率和用户满意度的重要触点。

### 2. 页面关键元素与关联结构

该界面主要由以下层级结构组成：

1.  **导航/标题区:**
    *   明确页面功能为 "选择优惠券"。
2.  **筛选/分类区 (Tabs):**
    *   **可用优惠券:** 展示当前订单可使用的优惠券列表，并显示数量 (如 "10")。
    *   **不可用优惠券:** 展示因条件不符（如未达到满减金额、适用范围不匹配、已过期等）而无法使用的优惠券列表。
    *   *关联:* Tabs 用于切换和过滤下方列表展示的内容。
3.  **优惠券列表区:**
    *   显示 "可用优惠券" 或 "不可用优惠券" 的详细列表。
    *   **单个优惠券项:** 包含优惠券的核心信息和选择控件。
        *   *左侧:* 优惠面额（如 "¥ 10"）、使用门槛（如 "满100元可用"）。
        *   *中间:* 优惠券名称（如 "新课专享券"）、有效期（如 "有效期至 2021.02.28"）、适用范围（如 "适用课程: 全部课程通用"）。
        *   *右侧:* 选择控件（如图中的单选按钮形式）。
    *   *关联:* 列表项是筛选区内容的具体呈现，用户在此区域进行选择操作。
4.  **非优惠选项区:**
    *   **不使用优惠券:** 提供明确放弃使用优惠券的选项，同样带有选择控件。
    *   *关联:* 与优惠券列表互斥选择，作为一种特殊选项存在。
5.  **操作区:**
    *   **确定按钮:** 用户完成选择后，点击此按钮确认选择并继续后续流程（如支付）。
    *   *关联:* 触发选择结果的应用。

### 3. 功能模块拆解

*   **优惠券列表展示:**
    *   概述: 显示用户的优惠券，区分可用与不可用状态。
*   **优惠券筛选:**
    *   概述: 通过 Tab 切换，筛选显示可用或不可用的优惠券。
*   **优惠券详情显示:**
    *   概述: 在列表项中清晰展示单个优惠券的面额、使用条件、名称、有效期、适用范围。
*   **优惠券选择:**
    *   概述: 允许用户通过单选方式选择一张可用优惠券或选择不使用优惠券。
*   **选择确认:**
    *   概述: 用户点击确定按钮，提交所选的优惠券（或不使用优惠券的选择）。

### 4. 服务端数据需求 (Data Content)

为渲染此页面，服务端需提供以下数据结构：

```json
{
  "available_coupons": {
    "count": 10, // 可用优惠券数量
    "list": [ // 可用优惠券列表
      {
        "coupon_id": "coupon123", // 优惠券唯一标识符
        "value": 10, // 优惠券面额 (数值)
        "currency": "¥", // 货币符号 (或统一约定)
        "min_purchase_amount": 100, // 最低使用金额 (数值)
        "condition_text": "满100元可用", // 使用门槛描述文本 (string)
        "title": "新课专享券", // 优惠券名称 (string)
        "validity_end_date": "2021.02.28", // 有效期截止日期 (string, 或 timestamp)
        // "validity_start_date": "2021.01.01", // 可选：有效期开始日期
        "scope_description": "适用课程: 全部课程通用", // 适用范围描述 (string)
        // "applicable_courses": ["all"], // 可选：结构化的适用范围数据 (e.g., array of course IDs or 'all')
        "selected": true // 服务端可根据上下文预设选中状态 (boolean)，如图中所示一个被选中
      },
      // ... more available coupons
    ]
  },
  "unavailable_coupons": {
    "count": 5, // 不可用优惠券数量 (示例，图片中未显示此tab内容)
    "list": [ // 不可用优惠券列表 (结构同上，但可能增加不可用原因字段)
      {
        "coupon_id": "coupon456",
        "value": 20,
        "currency": "¥",
        "min_purchase_amount": 200,
        "condition_text": "满200元可用",
        "title": "老用户回馈券",
        "validity_end_date": "2021.03.15",
        "scope_description": "适用课程: 专项课",
        // "applicable_courses": ["course_abc", "course_def"],
        "selected": false,
        "unavailable_reason": "未达到最低消费金额" // 可选：不可用原因说明 (string)
      },
      // ... more unavailable coupons
    ]
  },
    // "default_selection": "coupon123" // 可选：服务端推荐或默认选中的coupon_id，或者 "none" 表示默认不选中
}
```

**关键数据点说明:**

*   需要明确区分 `available_coupons` 和 `unavailable_coupons` 两个列表。
*   每个列表包含总数 `count` 和具体的列表 `list`。
*   每个优惠券对象 (`list` 中的元素) 需包含:
    *   唯一ID (`coupon_id`)
    *   面额 (`value`, `currency`)
    *   使用门槛 (`min_purchase_amount`, `condition_text`)
    *   名称 (`title`)
    *   有效期 (`validity_end_date`, 可选 `validity_start_date`)
    *   适用范围 (`scope_description`, 可选结构化数据 `applicable_courses`)
*   可用券列表项中可能需要一个 `selected` 字段来表示哪个券被选中（若逻辑允许预选）。
*   不可用券列表项中可选 `unavailable_reason` 字段说明不可用原因。
*   数据应基于当前订单的上下文进行过滤（例如，根据订单金额、购买的课程ID等判断哪些券可用/不可用），服务端完成判断后返回给前端。


【============== 图片解析 END ==============】



「编辑模式」

![image_OqNgbZS0YoeYY4xWHaDcpCQAnec](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498522519.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 图片类型与核心价值

*   **图片类型**: 用户界面（UI）截图。
*   **核心价值**: 该截图展示了互联网教育产品中“AI阅读”功能模块下的课程列表界面。它清晰地呈现了用户可选择的阅读课程、用户的学习进度以及对应的操作入口，是用户与AI阅读内容交互的主要入口之一。
    *   **关键元素**:
        *   **页面标题**: "AI Reading"，明确了当前功能模块。
        *   **课程列表**: 纵向排列的卡片式列表，是页面的主体内容。
        *   **单个课程卡片**: 包含课程相关的核心信息和操作。
    *   **元素关联**: 页面标题统领整个界面功能。课程列表聚合了多个课程卡片。每个课程卡片独立展示一项课程的信息（封面、标题、进度）和相应的用户动作（开始/继续阅读）。
    *   **需求/方案作用**: 此界面旨在向用户直观展示所有可用的AI阅读课程资源，并通过显示学习进度，激励用户持续学习，同时提供直接的操作按钮（"Continue Reading" / "Start Reading"）方便用户快速进入或开始学习流程。

### 功能模块拆解

*   **课程列表展示**:
    *   **功能概述**: 从服务端获取并以列表形式渲染用户可见的AI阅读课程。
*   **课程信息单元**:
    *   **功能概述**: 展示单个课程的核心信息，包括：
        *   课程封面图 (Course Image/Thumbnail)
        *   课程标题 (e.g., "一年级下册", "二年级下册")
        *   阅读进度指示 (e.g., "已读 10 / 30")
*   **用户进度状态**:
    *   **功能概述**: 显示用户针对具体课程的阅读进度量化数据（已读单元数/总单元数）。
*   **课程操作入口**:
    *   **功能概述**: 根据用户的学习状态（未开始、进行中），提供不同的操作按钮（"Start Reading", "Continue Reading"）。

### 服务端数据接口要求

为渲染此界面，服务端需要提供一个接口，该接口返回一个包含多个课程对象的数组（List）。对于列表中的每一个课程对象，需要包含以下数据字段：

*   `courses`: (Array) 课程列表
    *   `course_id`: (String/Number) 课程的唯一标识符。
    *   `course_title`: (String) 课程的显示标题 (例如: "一年级下册")。
    *   `course_image_url`: (String) 课程封面图片的URL地址。
    *   `total_units`: (Number) 该课程包含的总单元（篇章、课时等）数量 (例如: 30)。
    *   `completed_units`: (Number) 当前用户已完成的单元数量 (例如: 10 或 0)。

**示例数据结构 (JSON):**

```json
{
  "data": {
    "courses": [
      {
        "course_id": "course_001",
        "course_title": "一年级下册",
        "course_image_url": "https://example.com/image_grade1_term2.png",
        "total_units": 30,
        "completed_units": 10
      },
      {
        "course_id": "course_002",
        "course_title": "二年级下册",
        "course_image_url": "https://example.com/image_grade2_term2.png",
        "total_units": 25, // 假设总单元数不同
        "completed_units": 0
      },
      {
        "course_id": "course_003",
        "course_title": "三年级下册",
        "course_image_url": "https://example.com/image_grade3_term2.png",
        "total_units": 28, 
        "completed_units": 5  // 假设另一个进行中的状态
      }
      // ... more course objects
    ]
  }
}
```

**说明**:

*   服务端需根据请求用户身份，动态返回该用户的 `completed_units` 数据。
*   前端（客户端）可根据 `completed_units` 的值来判断显示 "Start Reading" (`completed_units` 为 0) 还是 "Continue Reading" (`completed_units` 大于 0 且小于 `total_units`)。
*   上述数据内容仅基于图片可见元素推导，不包含分页、排序、筛选等未在截图中明确展示的逻辑所需的数据字段。

### Mermaid 图表

根据图片内容分析，该图片为用户界面（UI）截图，主要展示静态的页面布局和数据呈现，并非流程图、时序图、类图、ER图、甘特图或饼图。因此，无法使用 Mermaid 语法生成相应的图表来描述此图片内容。

【============== 图片解析 END ==============】



新增段落：初始态-空值

**1.逐字稿编辑**

1. 进入本页面时，老师可以先查看预先生成好的视频；
1. 个别字小修改：可编辑文本，编辑LaTex公式，完成后点击「保存」
1. 整段大修改：可复制整段，到豆包等工具重写，再贴回文本框点击「保存」，并在下一步点击「重新生成」，重新生成对应的2、3模块
![image_IDl5b9alEoLn2yxaS17cCscWnYd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498523281.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

**1. 图片类型、关键元素与核心价值分析**

*   **图片类型**: UI 界面设计图 (Mockup/Wireframe)。
*   **核心功能**: 展示用户的“学习周报” personalise 学习成果摘要。
*   **关键元素与层级结构**:
    *   **顶层**: 学习周报界面 (Screen)
        *   **头部区域 (Header)**:
            *   标题: "学习周报" - 明确页面主题。
            *   日期范围: "10月23日-10月29日" - 定义报告覆盖的时间周期。
        *   **用户信息区域 (User Info)**:
            *   用户头像 (Avatar) - 视觉识别。
            *   用户称谓: "小明同学的周报" - 表明报告归属，增强个性化。
        *   **核心摘要区域 (Summary Stats)**:
            *   本周学习时长: "150分钟" - 总体学习投入量化指标。
            *   本周新增错题: "10道" - 学习中的薄弱点暴露情况。
            *   本周订正错题: "5道" - 对薄弱点的改进和消化情况。
        *   **详细数据区域 (Detailed Sections)**:
            *   **学习情况 (Study Situation)**:
                *   按学科 ("语文", "数学", "英语") 组织。
                *   各学科下包含: 学习时长、正确率、错题数 - 提供分学科的详细学习表现。
            *   **错题本 (Incorrect Question Notebook)**:
                *   按学科 ("语文", "数学", "英语") 组织。
                *   各学科下包含: 新增错题、已订正、未订正 - 聚焦错题管理和解决进度。
        *   **操作区域 (Action Area)**:
            *   按钮: "查看完整周报" - 引导用户深入了解更全面的数据。
*   **核心作用与价值**:
    *   **价值**: 为学生（或关注者，如家长/老师）提供一个周期性的、结构化的学习反馈。通过关键指标（时长、错题、订正）和分学科细项，帮助用户快速了解学习概况、进展和待改进之处，是互联网教育产品中常见的学习效果追踪和激励机制的一部分。
    *   **作用**: 作为需求文档的一部分，此图明确了学习周报界面的信息结构、关键数据指标和基本交互（查看完整报告），为后续的技术开发和UI设计提供了清晰依据。

**2. 功能模块拆解**

*   **周报标题与周期模块**:
    *   功能概述: 显示报告名称 ("学习周报") 和报告覆盖的时间范围。
*   **用户身份识别模块**:
    *   功能概述: 展示当前报告所属用户的头像和称谓。
*   **周学习核心指标摘要模块**:
    *   功能概述: 提炼展示本周最关键的学习统计数据，包括总学习时长、新增错题总数和已订正错题总数。
*   **分学科学习情况模块**:
    *   功能概述: 按学科列出详细的学习投入（时长）和产出（正确率、错题数）情况。
*   **分学科错题进展模块**:
    *   功能概述: 按学科展示错题的积累（新增）、解决（已订正）和遗留（未订正）情况。
*   **完整周报导航模块**:
    *   功能概述: 提供一个入口，允许用户跳转到包含更详尽数据的完整周报页面。

**3. 服务端需提供的数据内容**

服务端需为该界面提供以下数据：

*   **报告时间范围 (Report Date Range)**:
    *   `startDate`: 报告周期的开始日期 (e.g., "YYYY-MM-DD" or timestamp, 图片示例为 "10月23日")。
    *   `endDate`: 报告周期的结束日期 (e.g., "YYYY-MM-DD" or timestamp, 图片示例为 "10月29日")。
*   **用户信息 (User Information)**:
    *   `userName`: 用户名称 (e.g., "小明同学")。
    *   `userAvatarUrl`: 用户头像的URL地址。
*   **周报摘要数据 (Weekly Summary Data)**:
    *   `totalStudyDuration`: 本周总学习时长 (单位：分钟，数值类型，e.g., 150)。
    *   `newIncorrectQuestionsTotal`: 本周新增错题总数 (数值类型，e.g., 10)。
    *   `correctedIncorrectQuestionsTotal`: 本周订正错题总数 (数值类型，e.g., 5)。
*   **学习情况详情 (Study Situation Details)**:
    *   一个数组 (Array)，每个元素代表一个学科的学习情况对象 (Object)。
    *   每个学科对象包含:
        *   `subjectName`: 学科名称 (字符串, e.g., "语文", "数学", "英语")。
        *   `studyDuration`: 该学科学习时长 (单位：分钟，数值类型, e.g., 60)。
        *   `accuracyRate`: 该学科正确率 (百分比，数值类型, e.g., 90 for 90%)。
        *   `incorrectQuestionsCount`: 该学科错题数 (数值类型, e.g., 5)。
        *   `subjectIconUrl` (可选): 学科图标的URL地址 (字符串)，虽然图片未明确显示图标文件，但UI模式通常包含。
*   **错题本详情 (Incorrect Question Notebook Details)**:
    *   一个数组 (Array)，每个元素代表一个学科的错题情况对象 (Object)。
    *   每个学科对象包含:
        *   `subjectName`: 学科名称 (字符串, e.g., "语文", "数学", "英语")。
        *   `newIncorrectQuestions`: 该学科新增错题数 (数值类型, e.g., 5)。
        *   `correctedIncorrectQuestions`: 该学科已订正错题数 (数值类型, e.g., 3)。
        *   `uncorrectedIncorrectQuestions`: 该学科未订正错题数 (数值类型, e.g., 2)。
        *   `subjectIconUrl` (可选): 学科图标的URL地址 (字符串)。
*   **完整报告链接 (Full Report Link/Action)**:
    *   `fullReportUrl` 或 `fullReportAction`: 指向完整周报页面的路由或标识符 (字符串)。

**4. 图表类型描述**

此图片为 UI 界面设计图，非流程图、时序图、类图、ER图、甘特图或饼图，故不适用 Mermaid 语法进行描述。

**5. 内容限制说明**

以上分析仅基于图片中明确展示的视觉元素和文本内容进行，未包含图片以外的任何信息或进行主观推断。例如，未对 "正确率" 如何计算、"错题" 如何定义等业务逻辑进行猜测。

【============== 图片解析 END ==============】



小修改：直接保存

![image_Vv8bbGgWMo90EVx8mTccxc6Mnwb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498524134.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理，我们来分析一下这份需求文档中的UI界面截图，它展示了“学情报告”功能的一个核心界面。

1.  **图片类型与关键元素解析**
    *   **图片类型**: UI界面截图/Mockup。
    *   **核心功能**: 展示用户的学情报告列表。
    *   **关键元素**:
        *   **页面标题**: "学情报告"，明确了页面的核心功能。
        *   **Tab导航**: "近期报告"和"所有报告"，用于切换不同的报告筛选视图。当前选中的是"近期报告"。
        *   **报告列表**: 核心内容区域，以列表形式展示多个学情报告条目。
        *   **报告条目**: 每个条目代表一份独立的学情报告，包含关键信息。
        *   **列表末尾指示**: "暂无更多"，表示当前列表已加载完毕。
    *   **层级结构与关联**:
        *   顶层是"学情报告"功能页。
        *   下一层是Tab导航，用户通过切换Tab（近期/所有）来控制下方列表展示的内容。
        *   核心是报告列表，列表由多个报告条目组成。
        *   每个报告条目包含报告标题、日期、状态和一个操作入口("查看报告")。点击"查看报告"应跳转到对应的报告详情页。
    *   **作用与价值**: 该界面为用户（学生/家长）提供了一个集中查看学情反馈的入口。通过区分"近期"和"所有"报告，方便用户快速定位所需信息，了解学习进展和结果。状态（如"未提交"）提示用户待办事项或报告的当前处理进度。

2.  **功能模块拆解**
    *   **Tab切换**:
        *   功能概述: 允许用户在"近期报告"和"所有报告"两种视图间切换，以筛选需要查看的报告范围。
    *   **报告列表加载与显示**:
        *   功能概述: 根据当前选中的Tab（近期/所有），从服务端获取对应的学情报告列表数据并展示。
    *   **单个报告信息展示**:
        *   功能概述: 在列表的每个条目中，清晰地显示报告的标题（如课程/课时名称）、生成日期、当前状态（如"未提交"）。
    *   **查看报告操作**:
        *   功能概述: 提供"查看报告"的入口，用户点击后能跳转到该份报告的详细内容页面。
    *   **列表状态提示**:
        *   功能概述: 在列表底部显示"暂无更多"，告知用户当前条件下所有报告已加载完成。

3.  **服务端数据接口需求 (客观描述)**
    *   **接口功能**: 提供学情报告列表数据。
    *   **请求参数**:
        *   需要区分请求的是"近期报告"还是"所有报告"的参数（例如 `filter_type: 'recent'` 或 `filter_type: 'all'`）。
        *   可能需要分页参数（例如 `page`, `pageSize` 或 `last_report_id`），虽然截图显示"暂无更多"，但列表长时分页是必要的。
    *   **返回数据**:
        *   应返回一个列表（Array）。
        *   列表中的每个元素（Object）代表一条学情报告，至少应包含以下字段：
            *   `report_id`: (String/Number) 报告的唯一标识符。
            *   `report_title`: (String) 报告的标题（例如 "学前基础课 第一节课", "体验课(赠送) 第一节课"）。
            *   `report_date`: (String) 报告对应的日期（例如 "2024-06-01", "2024-05-28"），格式需统一。
            *   `report_status`: (String/Enum) 报告当前的状态（例如 "未提交"）。需要明确所有可能的状态值及其含义。
            *   `report_detail_identifier`: (String/Number) 用于前端构建跳转到报告详情页所需的信息，可能是报告ID或其他标识。
        *   可能需要包含列表分页或加载状态信息：
            *   `has_more`: (Boolean) 指示是否还有更多数据可供加载。在此截图中，对应的值应为`false`，因为显示了"暂无更多"。

4.  **Mermaid 图表描述**
    *   该图片为UI界面截图，并非流程、时序、类、ER、甘特或饼图，因此不适用Mermaid语法进行直接的可视化转换。主要信息在于其展示的界面布局和数据内容。

5.  **内容约束**
    *   总结内容均基于图片中可见元素：“学情报告”标题、Tab（近期报告、所有报告）、报告列表项（包含标题、日期、状态“未提交”、查看报告按钮）、列表末尾提示“暂无更多”。
    *   未涉及用户头像、具体课程内容、报告详情、交互动画等图片未展示的信息。

【============== 图片解析 END ==============】



大修改：重新生成音频、板书（loading态）

**2.朗读稿编辑**

1. 系统按“。/！/？/......”为标识分割多个句子，用户可以逐句编辑文本，也可以听整段
1. 一般情况：修改文本后，用户点击「生成配音」，否则做报错提示；点击后，可保存继续下一步
1. 配音难以纠正的情况：用户可进行下载，并在本地完成音频加工后重新上传


![image_VzonbbGhno7hfTxP5pOcEQcPn0c](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498524751.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网教育领域的产品经理，我对这份需求文档中的UI界面设计图（image_VzonbbGhno7hfTxP5pOcEQcPn0c）进行了解析。

1.  **图片类型与关键元素解析**

    *   **图片类型**: UI界面设计图 / Mockup。
    *   **核心目的**: 展示用户在应用内查看其关联的“课程班级”列表的界面。
    *   **关键元素与层级结构**:
        *   **顶层**: 整体界面视图。
        *   **导航层**:
            *   **页面标题**: "课程班级"，明确当前页面内容主题。
            *   **Tab导航**: 包含 "课程班级"（当前选中）和 "作业&考试" 两个标签，用于切换不同类型的信息列表。
        *   **内容层**:
            *   **课程班级列表**: 垂直滚动区域，展示多个课程班级条目。
            *   **列表项 (单个课程班级)**: 每个条目包含：
                *   **课程/教师图像**: 左侧的圆形图片占位符。
                *   **课程/班级名称**: 如 "张三老师-初中数学"。
                *   **教师信息与班级码**: 如 "张三老师 • 班级码：12345"。
                *   **教师角色/状态标签**: 如 "主讲讲师"。
                *   **导航指示**: 右侧箭头，暗示可点击进入详情。
    *   **元素关联与价值**:
        *   **Tab导航** 与 **内容层** 关联：点击不同的Tab会切换下方列表展示的内容（课程班级列表 或 作业&考试列表）。这为用户提供了清晰的信息分类和便捷的访问路径。
        *   **列表项** 与 **详情页** (隐含) 关联：每个列表项作为一个入口，点击后应跳转到对应课程/班级的详细信息页面。这使用户能方便地深入了解或管理单个课程班级。
        *   **核心价值**: 该界面为用户（可能是学生或家长）提供了一个集中查看和管理其参与的所有课程班级的入口，并通过Tab切换，整合了相关学习任务（作业&考试）的访问，提高了信息查找效率。

2.  **功能模块拆解**

    *   **页面标题模块**:
        *   *功能概述*: 显示当前页面的名称，告知用户所在位置（"课程班级"）。
    *   **Tab导航模块**:
        *   *功能概述*: 提供内容分类切换功能，用户可选择查看“课程班级”列表或“作业&考试”列表。
    *   **课程班级列表模块**:
        *   *功能概述*: 展示用户关联的课程班级信息列表。当前仅展示了“课程班级”tab下的列表内容。
    *   **列表项模块 (单个课程班级)**:
        *   *功能概述*: 显示单个课程班级的核心摘要信息（图像、名称、教师、班级码、角色），并作为导航入口指向课程班级详情。

3.  **服务端数据需求**

    为了渲染当前可见的“课程班级”列表，服务端需要提供以下数据结构：

    *   一个包含多个课程班级对象的列表 (Array)。
    *   列表中的每个课程班级对象 (Object) 应包含以下字段：
        *   `id` (String/Number): 课程班级的唯一标识符，用于后续可能的详情查询或操作。
        *   `imageUrl` (String): 课程班级对应的图片URL（或教师头像URL）。
        *   `courseName` (String): 课程班级的完整名称 (例如："张三老师-初中数学")。
        *   `teacherName` (String): 负责该课程班级的教师姓名 (例如："张三老师")。
        *   `classCode` (String): 该课程班级的班级码 (例如："12345")。
        *   `teacherRole` (String): 教师在该班级中的角色或标识 (例如："主讲讲师")。

    *注意：以上数据需求仅基于图片中“课程班级”Tab下可见列表项的内容推断，不包含“作业&考试”Tab或其他未显示元素所需的数据。*

4.  **图表类型表示**

    该图片为UI界面设计图，不适用于Mermaid语法中的流程图、时序图、类图、ER图、甘特图或饼图进行描述。

【============== 图片解析 END ==============】



逐句编辑，报错提示

![image_HmRzbxbZsojvVvxQKnDc81wynGc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498525339.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

1.  **图片类型与需求分析**
    *   **图片类型**: UI 界面截图
    *   **核心目的**: 该界面截图出自需求文档，展示了移动应用中“我的班级”功能的用户界面。其核心作用是为用户（学生或家长）提供一个清晰、直观的列表，展示他们当前所加入或管理的班级信息。
    *   **关键元素与结构**:
        *   **顶层**: 页面标题“我的班级”，明确了页面的功能主题。
        *   **中层**: 班级列表区域，是页面的主体内容，用于容纳多个班级条目。
        *   **底层**: 单个班级信息卡片（列表项），每个卡片代表一个用户参与的班级。
    *   **元素关联**: 页面包含一个班级列表，列表中包含多个班级卡片。每个卡片展示一个班级的核心摘要信息。
    *   **价值**: 在互联网教育产品中，此界面是用户管理和访问其学习/教学环境（班级）的入口，对于维系用户与平台、老师、同学的连接至关重要。它满足了用户快速查找和识别自己所在班级的基本需求。

2.  **功能模块拆解**
    *   **页面标题展示**:
        *   功能概述: 显示固定文本“我的班级”，告知用户当前页面功能。
    *   **班级列表展示**:
        *   功能概述: 从服务端获取并展示用户关联的所有班级信息列表。
    *   **单个班级信息卡片**:
        *   功能概述: 展示单个班级的关键摘要信息，至少包括：
            *   班级名称（可能包含教师信息作为标识的一部分，如“一年级1班(张老师)”或仅“一年级2班”）
            *   班级属性/标签（如“小学”）
            *   班级人数（如“共30人”）
            *   班级状态（如“进行中”）

3.  **服务端数据与功能需求**
    *   **服务端需提供的功能**:
        *   提供一个接口，根据当前登录用户的身份信息，查询并返回该用户所参与的班级列表数据。
    *   **服务端需返回的数据内容 (列表形式，每个元素代表一个班级)**:
        *   **班级列表 (Class List)**: 一个数组 (Array)，包含多个班级对象 (Class Object)。
        *   **班级对象 (Class Object) 结构**:
            *   `className` (String): 班级的显示名称 (例如: "一年级1班(张老师)", "一年级2班")。
            *   `classLevel` (String): 班级所属的级别或类型 (例如: "小学")。
            *   `memberCount` (Integer): 班级的成员总数 (例如: 30)。
            *   `status` (String): 班级的当前状态 (例如: "进行中")。
            *   `classId` (String/Integer): 班级的唯一标识符（虽然未直接显示，但通常是必要的，用于后续操作如进入班级详情）。

4.  **Mermaid 图表**
    *   该图片为 UI 界面截图，并非流程图、时序图、类图、ER 图、甘特图或饼图等标准图表类型。因此，不适用 Mermaid 语法进行描述。

5.  **内容约束**
    *   总结内容严格基于图片中可见的元素和文本信息，未包含图片外的任何推测或联想。

6.  **分析原则遵循**
    *   **第一性原理**: 从界面展示内容出发，推导所需的基础数据结构。
    *   **KISS**: 总结力求简洁明了，易于理解。
    *   **MECE**: 对界面可见元素进行了分类（标题、列表、列表项），列表项内的信息点（名称、级别、人数、状态）也做到了相互独立、完全穷尽（基于可见信息）。

【============== 图片解析 END ==============】



生成配音，清除提示

**3.板书编辑**

1. 左侧：系统由逐字稿生成对应的 markdown 格式的板书内容，包含：## 标题、公式、文字，及xml符合标识的高亮标记<key1></key1>，用户可进行修改
1. 右侧：左侧修改完成后，点击「更新预览」，可生产对应的预览效果
1. 完成编辑后，点击“生成视频”，进入更新视频的loading态，完成更新后，视频换成最新，视频时间更新为“更新于某日期几点几分”


![image_QsOCbFcYGoloAMxZDUtcrtaonqe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498525919.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理，我们来分析这张出自需求文档的图片。

1.  **图片类型与关键元素分析**

    *   **图片类型:** 这是一张移动应用端的 **UI 界面设计图 (Mockup/Wireframe)**。
    *   **核心功能:** 该界面展示了用户的 **阶段性成长报告**。
    *   **关键元素与层级结构:**
        *   **顶层:** 成长报告页面
            *   **层级 1:** 报告标题/时间范围 (e.g., "2024.04.01-2024.04.29 成长报告") - 定义报告的数据周期。
            *   **层级 1:** 用户信息区域
                *   **层级 2:** 用户头像 ("宝") - 标识用户。
                *   **层级 2:** 用户昵称 ("小亮亮") - 标识用户。
            *   **层级 1:** 整体学习概览 ("本阶段共经历了 *3* 个学习阶段") - 提供高层级的学习进度总结。
            *   **层级 1:** 学习阶段详情列表 (可展开/折叠) - 核心内容区域，分阶段展示学习细节。
                *   **层级 2:** 单个学习阶段概要 (e.g., "能力形成阶段 共完成 *15* 个学习活动") - 包含阶段名称和该阶段完成的活动总数。
                *   **层级 3:** (展开后) 该阶段已完成的学习活动列表
                    *   **层级 4:** 单个学习活动条目 - 包含活动类型图标、活动名称 (e.g., "汉字认读：你我他")、完成时间戳 (e.g., "04-01 19:30")。
                *   **层级 3:** 查看更多控件 (e.g., "查看更多") - 用于加载或显示超出初始显示数量的活动记录。
            *   **层级 1:** 操作按钮区域
                *   **层级 2:** 分享按钮 ("分享") - 允许用户将报告分享出去。
    *   **核心作用与价值:** 此界面旨在向用户（可能是家长或学生本人）清晰、结构化地展示在特定时间段内的学习成果和进展。通过分阶段、分活动呈现数据，帮助用户了解学习轨迹，感知学习效果。同时，“分享”功能有助于提升产品的社交传播和用户粘性。

2.  **功能模块拆解**

    *   **报告周期显示模块:**
        *   **功能概述:** 展示当前报告所涵盖的起止日期。
    *   **用户信息展示模块:**
        *   **功能概述:** 显示当前查看报告的用户的头像和昵称。
    *   **学习阶段总览模块:**
        *   **功能概述:** 汇总并显示报告周期内用户所经历的学习阶段总数。
    *   **学习阶段列表模块:**
        *   **功能概述:** 以列表形式展示报告期内经历的所有学习阶段。每个阶段项包含阶段名称和该阶段完成的学习活动总数。支持展开/折叠查看详情。
    *   **已完成活动列表模块 (内嵌于学习阶段):**
        *   **功能概述:** 展示指定学习阶段下已完成的具体学习活动列表，包括活动类型、名称和完成时间。
    *   **活动列表加载/展开模块 ("查看更多"):**
        *   **功能概述:** 当单个学习阶段下的活动数量较多时，提供加载或显示更多活动记录的功能。
    *   **报告分享模块:**
        *   **功能概述:** 提供触发分享操作的入口，允许用户将当前成长报告分享到其他平台。

3.  **服务端数据接口需求 (客观描述)**

    为实现此界面，服务端需提供一个接口，根据请求参数（如用户ID、报告开始日期、报告结束日期）返回以下数据结构：

    *   **报告基本信息:**
        *   `reportStartDate`: String (报告开始日期，格式如 "YYYY.MM.DD")
        *   `reportEndDate`: String (报告结束日期，格式如 "YYYY.MM.DD")
        *   `userAvatar`: String (用户头像 URL)
        *   `userName`: String (用户昵称)
        *   `totalStagesCount`: Number (经历的总学习阶段数量)
    *   **学习阶段列表 (`stages`):** Array of Objects
        *   **每个阶段对象包含:**
            *   `stageName`: String (阶段名称，如 "能力形成阶段")
            *   `completedActivitiesCount`: Number (该阶段完成的活动总数)
            *   `activities`: Array of Objects (该阶段已完成的活动列表)
                *   **每个活动对象包含:**
                    *   `activityTypeIcon`: String (活动类型标识或对应图标的URL/Key，如 "汉字", "英文绘本")
                    *   `activityName`: String (活动具体名称，如 "汉字认读：你我他")
                    *   `completionTime`: String (完成时间戳或格式化时间字符串，如 "MM-DD HH:mm")
                *   *注: 如果活动列表支持分页/懒加载("查看更多")，服务端可能需要在此层级或活动列表层级额外返回分页信息（如 `currentPage`, `pageSize`, `hasMore`）或提供专门用于加载更多活动的分页接口。*

4.  **Mermaid 图表描述**

    该图片为 UI 界面设计图，主要展示信息结构和布局，并非流程图、时序图、类图等适合使用 Mermaid 语法的图表类型。因此，不适用 Mermaid 进行描述。

【============== 图片解析 END ==============】



板书编辑，点“更新预览”看效果

![image_ULCWbJQxzoLcjhxWDk7cFe9Gnqc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498526740.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 1. 图片类型与核心价值解析

*   **图片类型**: UI 界面截图 (User Interface Screenshot) / 视觉稿 (Visual Mockup)。
*   **核心元素与结构**:
    *   **顶层**: "我的学习" 页面。
    *   **导航层**: 顶部导航栏（包含返回按钮和页面标题 "我的学习"）。
    *   **内容分类层**: Tab 导航栏，包含 "课程"、"专栏"、"训练营" 三个分类，当前选中 "课程"。
    *   **内容列表层 (课程 Tab)**: 展示用户已参与的课程列表。
        *   **列表项 (课程卡片)**: 每个卡片代表一门课程，包含：
            *   课程封面图 (左侧)。
            *   课程信息 (右侧)：
                *   课程标题 (例如："【小鹿7天体验】课程名称标题")。
                *   学习进度 (例如："进度1/12")。
                *   操作/状态按钮 (例如："继续学习")。
*   **核心作用与价值**:
    *   该界面是用户个人学习中心的核心入口，用于集中展示用户已购或已加入的学习资源。
    *   Tab 导航提供了清晰的内容分类，方便用户按类型（课程、专栏、训练营）查找和管理自己的学习内容。
    *   课程卡片直观地展示了每门课程的关键信息（标题、封面、进度），并通过 "继续学习" 按钮提供了便捷的续学入口，提升了用户学习体验和效率。

### 2. 功能模块拆解

*   **顶部导航栏**:
    *   **功能**: 显示当前页面标题 ("我的学习")，提供返回上一页的功能。
*   **Tab 导航**:
    *   **功能**: 实现不同学习资源类型 ("课程", "专栏", "训练营") 的切换展示。用户点击不同 Tab，下方列表会刷新显示对应类型的内容。
*   **课程列表 (当前显示)**:
    *   **功能**: 以列表形式展示用户参与的 "课程" 类学习资源。
*   **课程卡片**:
    *   **功能**: 单个课程的概要信息展示单元。包含课程封面、标题、学习进度，并提供 "继续学习" 的操作入口。
*   **专栏列表 (隐含)**:
    *   **功能**: (当用户点击 "专栏" Tab 时) 展示用户参与的 "专栏" 类学习资源列表。
*   **训练营列表 (隐含)**:
    *   **功能**: (当用户点击 "训练营" Tab 时) 展示用户参与的 "训练营" 类学习资源列表。

### 3. 服务端数据需求

为了渲染如图所示的 "我的学习" 界面（特别是 "课程" Tab 激活状态下），服务端需要提供以下功能和数据：

*   **功能**:
    *   提供一个接口，根据用户身份和请求的 Tab 类型（如 `type=course`, `type=column`, `type=training_camp`）返回相应的学习项目列表。
    *   接口应支持分页加载，以应对用户学习项目较多的情况（虽然图中未明显展示分页控件，但列表展示通常需要考虑）。
*   **数据内容 (以 "课程" Tab 为例)**:
    *   接口需要返回一个包含多个**课程对象**的**数组 (List/Array)**。
    *   每个**课程对象**应包含以下字段：
        *   `course_id`: (String/Number) 课程的唯一标识符。
        *   `course_title`: (String) 课程的完整标题 (例如："【小鹿7天体验】课程名称标题")。
        *   `cover_image_url`: (String) 课程封面图片的 URL 地址。
        *   `progress_current`: (Number) 用户当前完成的进度单元数 (例如：1)。
        *   `progress_total`: (Number) 课程总的进度单元数 (例如：12)。
        *   `action_type` 或 `learning_status`: (String/Enum) 用于前端判断显示何种按钮文本（如 'continue', 'start', 'completed'）或直接提供按钮文本。基于图中 "继续学习"，此状态为 "进行中"。
        *   `action_target`: (String) 用户点击卡片或 "继续学习" 按钮后需要跳转的目标信息 (例如：课程学习页的 URL 或路由)。

    *   **注意**: 对于 "专栏" 和 "训练营" Tab，服务端需要提供类似结构的列表数据，但具体的字段（如进度表示方式）可能根据业务定义有所不同。

### 4. 图表类型 Mermaid 描述

该图片为 UI 界面截图，不属于流程图、时序图、类图、ER 图、甘特图或饼图等标准图表类型，因此无法使用 Mermaid 语法进行描述。

【============== 图片解析 END ==============】



完成点「生成视频」

预计loading20min

**4.预览视频，完成整个段落**

视频生成完成后，由loading态变为「待处理」，用户可点击进入观看视频

若遇到：字幕错误，截带有时间轴的播放图，在excel中at算法同学报错修改

本段已无问题，则点击「保存完整段落」，加锁标记；除非点击「重新编辑」否则只可预览、不可修改

![image_G1u5b2EcGoHuVVxZUwqcNgqhnVf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498527428.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理，针对互联网教育业务场景，对此需求文档中的图片进行解析如下：

1.  **图片类型与核心价值分析**
    *   **图片类型**: UI 界面截图。
    *   **核心内容**: 该截图展示了 App 内的“我的课程”页面，用于呈现用户已报名或购买的课程列表。
    *   **关键元素与层级结构**:
        *   **页面层**: 我的课程页面。
            *   **列表层**: 课程列表区域。
                *   **项目层**: 单个课程卡片。
                    *   **元素层 (单个课程卡片内)**:
                        *   课程图片/封面 (Course Thumbnail)
                        *   课程标题 (Course Title)
                        *   学习进度 (Learning Progress Percentage)
                        *   课程类型/标签 (Course Type Tag, e.g., "专栏")
    *   **关联与作用**:
        *   此页面是用户访问已购/已参与学习内容的主要入口。
        *   课程卡片聚合了关键信息（是什么课、学到哪了），方便用户快速识别和选择。
        *   学习进度直观展示了用户的学习状态，起到引导和激励作用。
        *   课程类型标签帮助用户区分不同形式的学习内容。
        *   在整体需求中，此页面是用户学习路径的核心环节，承载着内容触达和学习进度管理的功能，其设计直接影响用户学习体验和活跃度。

2.  **功能模块拆解**
    *   **我的课程列表展示模块**:
        *   **功能概述**: 从服务端获取并展示用户专属的课程列表。
    *   **课程信息卡片模块**:
        *   **功能概述**: 以卡片形式聚合展示单个课程的核心信息，包括封面图、标题、学习进度百分比和课程类型标签。
    *   **学习进度显示模块**:
        *   **功能概述**: 显示用户在具体课程中的学习进度数值（百分比形式）。

3.  **服务端数据接口需求 (Data Requirements)**

    为实现该页面展示，服务端需提供一个接口，当用户访问“我的课程”页面时，该接口根据用户的身份信息，返回其名下的课程列表数据。

    *   **核心数据结构**: 应返回一个 JSON 数组，数组中每个元素代表一个课程对象。
    *   **课程对象 (Course Object) 属性**: 每个课程对象至少应包含以下字段：
        *   `courseId` (String/Number): 课程的唯一标识符。
        *   `courseTitle` (String): 课程的完整标题 (e.g., "如何系统的学习编程")。
        *   `courseThumbnailUrl` (String): 课程封面的图片 URL (e.g., "https://...")。
        *   `learningProgress` (Number): 用户的学习进度，以百分比表示 (e.g., `80` 代表 80%)。
        *   `courseTypeTag` (String): 课程的类型或标签文本 (e.g., "专栏")。

    *   **数组组合示例 (JSON)**:

        ```json
        [
          {
            "courseId": "course_123",
            "courseTitle": "如何系统的学习编程",
            "courseThumbnailUrl": "https://static.test.xiaoluxue.cn/demo41/prd_images/...", // 示例图片URL
            "learningProgress": 80,
            "courseTypeTag": "专栏"
          },
          {
            "courseId": "live_456",
            "courseTitle": "项目管理实战直播课",
             "courseThumbnailUrl": "https://static.test.xiaoluxue.cn/demo41/prd_images/...", // 示例图片URL
            "learningProgress": 25,
            "courseTypeTag": "直播"
          }
          // ... 更多课程对象
        ]
        ```
    *   **注意**: 返回的数据应仅包含渲染页面内容所需的核心业务信息，不应包含设备状态、UI 样式等前端或设备相关信息。需要确保数据准确反映当前用户的课程列表和对应的学习进度。

4.  **图表类型转换 (Mermaid)**
    *   该图片为 UI 界面截图，不属于流程图、时序图、类图、ER图、甘特图或饼图等适合使用 Mermaid 语法直接转换的标准图表类型。因此，不适用 Mermaid 进行描述。

5.  **内容基准**
    *   所有分析内容均严格基于图片中可见的元素和信息，未做额外联想或补充图片中未明确展示的功能（如排序、筛选、空状态等）。

【============== 图片解析 END ==============】



新视频生成完毕，右上角可点「保存完整段落」

![image_Jgwrbt3DwoRS4jxCbMbcBkb1nSg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498528443.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 图片解析总结

1.  **图片类型与核心价值分析**
    *   **图片类型:** 用户界面（UI）截图/设计稿。
    *   **核心功能:** 该界面为"选择课程"页面，用于用户浏览、筛选和搜索其可访问的课程列表。在互联网教育产品中，这是用户访问学习内容的核心入口之一。
    *   **关键元素与层级结构:**
        *   **顶层:** 页面导航栏
            *   返回按钮
            *   页面标题 ("选择课程")
        *   **中层:** 内容区域
            *   **搜索模块:** 提供课程搜索功能（输入框提示 "搜索课程"）。
            *   **筛选模块:** 提供按课程类型筛选的功能，包含"全部"、"直播课"、"录播课"三个选项卡，当前选中"全部"。
            *   **课程列表模块:** 展示符合筛选/搜索条件的课程。
                *   **课程项目 (重复元素):** 单个课程的摘要信息展示卡片。
                    *   课程封面图
                    *   课程标题
                    *   课程状态标签 ("未过期", "即将过期", "已过期")
                    *   授课教师姓名
                    *   课程节数 ("共X节课")
    *   **元素关联:** 筛选模块的选择状态决定了课程列表模块中展示的课程内容。搜索模块（预期）也会根据输入内容过滤课程列表。点击列表中的课程项目（预期）会跳转到对应的课程详情或播放页面。
    *   **核心价值:** 为用户提供清晰、便捷的课程查找和访问路径，支持按类型筛选和关键词搜索，提升用户在平台内查找学习资源的效率。

2.  **功能模块拆解**
    *   **导航栏模块:**
        *   **功能概述:** 显示当前页面标题，并提供返回上一页面的功能。
    *   **课程搜索模块:**
        *   **功能概述:** 允许用户输入关键词搜索课程。
    *   **课程筛选模块:**
        *   **功能概述:** 提供按课程类型（全部、直播课、录播课）对课程列表进行过滤的功能。
    *   **课程列表模块:**
        *   **功能概述:** 以列表形式展示用户的课程，每个课程项包含关键摘要信息。

3.  **服务端需提供的功能与数据内容**
    *   **服务端功能:**
        *   提供一个接口，根据用户身份、筛选条件（课程类型）、搜索关键词（可选）查询并返回用户可见的课程列表。
    *   **服务端返回数据内容 (针对课程列表):**
        *   应返回一个包含多个课程对象的数组 (List/Array)。
        *   **每个课程对象应包含以下字段:**
            *   `courseId`: 课程的唯一标识符 (String or Number)。
            *   `courseTitle`: 课程标题 (String)，例如："幼儿园认知语言课【3-4岁】第1节"。
            *   `courseImageUrl`: 课程封面图片的 URL (String)。
            *   `courseStatus`: 课程状态 (String or Enum)，例如："未过期", "即将过期", "已过期"。
            *   `instructorName`: 授课教师姓名 (String)，例如："张强"。
            *   `lessonCount`: 课程总节数 (Number)，例如：12。
            *   `courseType`: 课程类型 (String or Enum)，用于支持筛选，例如："live" (直播课), "recorded" (录播课)。

4.  **图表类型转换**
    *   该图片为 UI 界面截图，非流程图、时序图、类图、ER 图、甘特图或饼图，因此不适用 Mermaid 语法进行转换。

5.  **内容限制**
    *   总结内容严格基于图片中可见元素，未包含任何图片外信息的推测。

6.  **遵循原则**
    *   分析基于图片展示的界面元素及其隐含的功能逻辑（First Principles）。
    *   结构和描述简洁明了（KISS）。
    *   功能模块划分和数据字段定义相互独立、完全穷尽了图片展示的内容（MECE）。

【============== 图片解析 END ==============】



保存后该段落计作✅，转成“只读”

### 二、名词说明

- 内容的粒度
- 稿件（GuideWidgetSet）：
1. 一个zip包、算法处理的一批数据
1. 含若干文稿组件（Part）的集合
1. 后续用来作为文稿组件集合的查询
- 文稿（GuideWidget），也叫Part：
1. 由三稿（逐字稿、朗读稿、板书）生成的一整个视频资源
1. ai课中的一个视频组件，对应组件类型是：板书
- 生产的几类实体
- 逐字稿（Transcript）：三稿之一，课程的详细讲稿，用于投喂LLM进行 朗读稿+板书 自动化生产
- 朗读稿（Audioscript）：三稿之一，学生端听到的内容，相当于：「逐字稿」的口语稿+ 音频，可逐句编辑
- 朗读稿中的句子（AudioSentence）：朗读稿中每段文案
- 朗读稿中的音频地址（AudioUrl）：朗读稿中每段文案的音频地址
- 板书：三稿之一，学生端看到的可交互页面的内容，比「逐字稿」精炼，带有“圈画”、“高亮”、“划线”等动画
- 板书视频：由三稿生成的的视频内容
- 字幕：学生端看到的字幕内容                                         
### 三、业务流程

**逐字稿工具-核心功能**

![board_KCoCwtHdyhgO9IbAvSGc2esTndu](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498514296.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据你提供的图片，这是一张 **用户界面（UI）截图**，展示了在线教育产品中一个名为“口算王者争霸赛”的活动结果或历史记录页面。作为互联网产品经理，尤其是在教育领域，这张截图是需求文档中的一部分，用于明确用户查看其活动表现的界面设计和所需数据。

**1. 图片关键元素、结构与核心价值分析**

*   **图片类型:** 用户界面（UI）截图/Mockup。
*   **核心功能:** 展示用户在“口算王者争霸赛”活动中的个人成绩、排名及参与历史。
*   **关键元素组成:**
    *   **用户信息区:** 显示当前用户的基本身份信息。
    *   **排名概要区:** 展示用户当前的总排名和昨日的最高排名者信息，用于即时反馈和激励。
    *   **历史记录列表:** 按时间倒序列出用户的历次参与记录，包含成绩和排名。
    *   **辅助信息/操作区:** 提供查看排名规则或详情的入口。
*   **层级化结构:**
    1.  **顶层 - 标题:** "口算王者争霸赛"，明确页面所属活动。
    2.  **第二层 - 用户身份与概览:**
        *   左侧：当前用户信息（头像、昵称、学号）。
        *   右侧：用户当前排名（"我的当前排名 1000+"）。
        *   横跨：昨日全国第一名信息（头像、昵称、学号），作为标杆展示。
    3.  **第三层 - 详细历史记录:**
        *   列表形式展示多次参与记录。
        *   每条记录包含：参与时间、得分、当次排名。
    4.  **底层 - 辅助链接:** "如何查看具体排名？"，引导用户了解更多信息。
*   **核心作用与价值:**
    *   **用户价值:** 提供清晰的个人表现反馈（分数、排名），记录学习/参与过程，通过排名对比激发好胜心和持续参与的动力。
    *   **业务价值:** 增强用户粘性，促进活动活跃度，收集用户学习数据（如口算能力表现），是 gamification（游戏化学习）策略的重要组成部分。

**2. 功能模块拆解**

以下是图片界面所包含的功能模块及其简要概述：

*   **用户身份展示:** 显示当前登录用户的头像、昵称、学号/ID。
*   **当前排名展示:** 显示用户在活动中的最新总排名。
*   **昨日最佳展示:** 显示昨日活动排名第一的用户信息（头像、昵称、学号），用于营造竞争氛围和设立目标。
*   **参与历史列表:** 滚动列表，展示用户过往每一次参与活动的时间、获得的成绩（分数）以及该次参与所获得的排名。
*   **排名规则入口:** 一个链接或按钮，点击后应跳转到说明页面或弹出窗口，解释排名计算规则或查看更详细的排名信息。

**3. 服务端需提供的功能与数据内容**

为实现该界面显示，服务端需要提供以下数据接口（返回数据内容需包含但不限于）：

*   **获取活动结果页数据:**
    *   **用户信息:**
        *   `userAvatarUrl`: 当前用户头像的URL。
        *   `userNickname`: 当前用户的昵称（例如：“小路”）。
        *   `userId`: 当前用户的唯一标识或学号（例如：“学号：1234567”）。
    *   **当前排名信息:**
        *   `currentUserRank`: 用户当前在活动中的排名（可能是具体数字，也可能是区间描述，例如：“1000+”）。
    *   **昨日最佳用户信息:**
        *   `yesterdayTopRankerAvatarUrl`: 昨日排名第一用户的头像URL。
        *   `yesterdayTopRankerNickname`: 昨日排名第一用户的昵称（例如：“路小”）。
        *   `yesterdayTopRankerId`: 昨日排名第一用户的唯一标识或学号（例如：“学号：7654321”）。
    *   **参与历史记录 (数组 `participationHistory`):**
        *   一个包含用户多次参与记录对象的数组，按时间降序排列。
        *   每个记录对象至少包含：
            *   `attemptTimestamp`: 参与活动的具体日期和时间（例如：“2024-07-29 08:00”）。
            *   `score`: 该次参与获得的成绩/分数（例如：“98分”）。
            *   `rank`: 该次参与获得的排名（例如：“全国第100名”）。

**4. 图表类型与 Mermaid 描述**

该图片为 **UI 截图**，并非流程图、时序图、类图、ER图、甘特图或饼图等适合使用 Mermaid 语法的图表类型。因此，无法直接使用 Mermaid 语法来描述这张截图本身的内容结构。

**5. 内容限制**

本总结严格基于图片所示内容，未包含图片之外的任何臆测信息。例如，未涉及具体的排名算法、分数计算逻辑或点击“如何查看具体排名？”后的交互流程等。

【============== 图片解析 END ==============】



**复制课程页-流程**

![board_IhcvwqHO4hICJnb8MSYcQBNjnrg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498515699.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 1. 图片类型与核心价值解析

*   **图片类型**: UI 界面设计图/原型图。
*   **核心元素与组成**:
    *   **导航栏**: 包含返回按钮和标题“班级日历”。
    *   **月份切换**: 提供切换上一个月、下一个月的控件，并显示当前年月（如“2024年 6月”）。
    *   **星期表头**: 显示周日至周六的标识。
    *   **日历网格**: 展示当前月份的日期，包含：
        *   非本月日期（置灰显示）。
        *   本月普通日期。
        *   高亮的当天日期（示例中为“16”日）。
        *   带有标记的日期（下方有圆点），表示当天有课程安排（示例中为17, 18, 19, 20日）。
        *   用户可选择其中一个日期。
    *   **课程列表**: 显示 **所选定日期** 的课程安排详情（示例中显示“17”日的课程），每条课程信息包含：
        *   课程开始时间（如“09:00”）。
        *   课程名称（如“儿童创意美术【体验课】”）。
        *   课程状态（如“未开始”、“已结束”）。
        *   操作按钮（如“进入教室”）。
*   **层级化结构与关联**:
    *   顶层是导航和月份控制。
    *   月份控件控制日历网格展示的月份。
    *   日历网格提供日期选择功能。
    *   日期选择后，下方的课程列表会 **联动** 刷新，显示该选定日期的课程信息。
    *   课程列表中的“进入教室”按钮与具体的课程及其状态相关联。
*   **核心作用与价值**:
    *   该界面为用户（可能是学生、家长或老师）提供了一个清晰、直观的方式来查看其在特定月份及日期的课程安排。
    *   用户可以通过日历快速定位有课的日期，并通过选择日期查看当天的详细课程列表。
    *   对于“未开始”的课程，提供了直接进入在线教室的入口，简化了上课流程。
    *   整体上提升了用户管理和参与课程的效率和体验。

### 2. 功能模块拆解

*   **月份切换模块**:
    *   **功能概述**: 允许用户按月浏览日历，切换查看不同月份的课程安排。
*   **日历视图模块**:
    *   **功能概述**: 以网格形式展示指定月份的日期，视觉上区分本月/非本月日期、当天日期、有课日期，并支持用户选择特定日期。
*   **课程列表模块**:
    *   **功能概述**: 根据用户在日历视图中选择的日期，动态加载并展示该日期的所有课程信息列表，包括时间、名称和状态。
*   **课程状态展示模块**:
    *   **功能概述**: 显示每节课的当前状态（如：未开始、进行中、已结束等），帮助用户了解课程进展。
*   **课堂入口模块**:
    *   **功能概述**: 对符合条件的课程（通常是“未开始”或“进行中”的课程），提供一个明确的操作入口（如“进入教室”按钮），方便用户准时参与在线学习。

### 3. 服务端数据接口需求 (客观描述)

为渲染此“班级日历”界面，服务端需要提供以下数据：

*   **获取日历标记数据的接口**:
    *   **输入参数**: 用户标识 (UserID), 年份 (Year), 月份 (Month)。
    *   **返回数据**:
        *   一个包含该月所有需要展示的日期信息的数组 (包括上月结尾和下月开头的几天)。
        *   对数组中的每个日期对象，应包含：
            *   `date`: 具体日期 (YYYY-MM-DD格式)。
            *   `day_of_month`: 日期数字 (e.g., 1, 16, 30)。
            *   `is_current_month`: 布尔值，标记是否属于当前查询的月份。
            *   `is_today`: 布尔值，标记是否为当天。
            *   `has_class`: 布尔值，标记该日期是否有课程安排。

*   **获取指定日期课程列表的接口**:
    *   **输入参数**: 用户标识 (UserID), 具体日期 (Date, YYYY-MM-DD格式)。
    *   **返回数据**:
        *   一个课程信息对象的数组 `classes_list`。如果当天无课程，返回空数组。
        *   数组中每个课程对象应包含：
            *   `session_id`: 课程场次唯一标识。
            *   `start_time`: 课程开始时间 (格式如 HH:mm，例如 "09:00")。
            *   `class_name`: 课程名称 (字符串，例如 "儿童创意美术【体验课】")。
            *   `class_status_code`: 课程状态码 (例如，'NOT_STARTED', 'FINISHED', 'IN_PROGRESS' 等，用以区分“未开始”、“已结束”等状态)。
            *   `class_status_text`: 课程状态描述文本 (字符串，例如 "未开始", "已结束")。
            *   `entry_action_available`: 布尔值，指示“进入教室”等操作当前是否可用。
            *   `entry_details`: (可选，仅当 `entry_action_available` 为 true 时提供) 进入教室所需的信息，如跳转链接、房间号等。

### 4. 图表类型描述

该图片为 **UI 界面设计图 (UI Mockup)**，并非流程图、时序图、类图、ER图、甘特图或饼图。因此，不适用 Mermaid 语法进行描述。

【============== 图片解析 END ==============】



### 四、需求概览

| 序号 | 需求名称 | 内容概述 | 优先级 |
| --- | --- | --- | --- |
| 1 | 读取Part信息 |  | P0 |
| 2 | 预览完整视频 |  | P0 |
| 3 | 逐字稿编辑 |  | P0 |
| 4 | 朗读稿编辑 |  | P0 |
| 5 | 板书编辑 |  | P0 |
| 6 | 提交 |  | P0 |
| 7 | 基于url生成一节新课 |  | P0 |

- 获取各段落顺序、标题名称、状态（未开始、进行中、已完成）、loading状态
- 支持编辑：标题文案，操作：上下移、添加新段落、删除
- 获取算法最开始的Demo视频
- 获取在「3板书动画」最新生产的视频
- 获取算法最开始的逐字稿信息
- 编辑文本框、插入LaTex公式、保存逐字稿
- 提交逐字稿
- 重新生成：整段朗读稿 & 板书动画
- 获取算法最开始的逐字稿信息（按句展示）
- 逐句编辑：文本框、播放、生成音频、下载、上传
- 整段音频播放
- 提交朗读稿
- 获取算法最开始的板书信息
- 编辑markdown、编辑高亮标记、插入公式
- 更新预览：板书及高亮
- 提交板书
- 重新生成视频
- 保存整课
- 重新编辑
- 复制页：提交url+新课名称
- 在新课页面的编辑
### 五、详细产品方案

#### 5.1逐字稿工具页编辑

| 序号 | 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- | --- |
| 1 | 逐字稿工具：顶栏 | 展示本课信息 | 标题：逐字稿生产课程名称：当前课程名称 | ![in_table_image_CnqLblTVSoZaW2xqClncWNVmnDt](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498529306.png) |  |
| 2 | 逐字稿工具:左侧边栏Part模块 | 读取Part信息 | ![in_table_image_U7xvbukAOoKjerxaZiJcdSD4nse](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498529915.png)

交互设计： | ![in_table_image_VZtLbJ5jSoEAHCx2ex7cifAnnjb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498530620.png) |  |
| 3 |  | 编辑Part信息 |  | ![in_table_image_SAyCbtrGXosjb6x5gCEcStmSnPf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498531525.png) |  |
| 4 | 逐字稿工具:右侧边栏视频预览模块 | 预览完整视频 | 提供完整视频的预览功能，用户可以查看算法生成的初始Demo视频以及在板书动画编辑后最新生成的视频。交互设计： | ![in_table_image_Wo5nbgJylomMJUxTqxScUPdGnzg](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498532188.png) |  |
| 5 | 逐字稿工具:右侧边栏逐字稿编辑模块 | 逐字稿编辑小改场景 | 提供逐字稿的编辑功能，用户可以对逐字稿进行文本编辑、插入LaTeX公式，并保存编辑内容。交互设计： | ![in_table_image_EojCbK8NXo3w3jx1MC4cW4AVn6c](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498533636.png) |  |
| 6 |  | 逐字稿编辑大改场景（用户自己判断） | 对于用户对整段内容不满意的情况交互设计： | ![in_table_image_Yiujbht6MoXkoQxATzscWmoyntF](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498535064.png) |  |
| 7 | 逐字稿工具:右侧边栏朗读稿编辑模块 | 朗读稿展示 | 提供朗读稿的展示功能： | ![in_table_image_NgoRbJG48oED9LxoW0ccMpzenQh](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498535621.png) |  |
| 8 |  | 朗读稿音频播放 | 提供朗读稿的播放功能：交互设计： | 同上 |  |
| 9 |  | 朗读稿修改 | 朗读稿编辑功能，用户可以对朗读稿进行逐句编辑、生成音频、下载和上传音频等操作；每次只能对一句话进行播放/高亮编辑。交互设计： | ![in_table_image_CxfjbUnB0oCGtOx7MRUcJp32nnd](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498536801.png) |  |
| 10 | 逐字稿工具:右侧边栏板书编辑模块 | 板书展示和编辑 | 提供板书的编辑功能，用户可以编辑markdown格式的板书内容，并进行高亮标记的编辑。交互设计： | ![in_table_image_LDIwb590goA06FxbCZzcQO7Snsu](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498537496.png) |  |
| 12 |  | 生成板书预览 |  | ![in_table_image_UXZYbtDfdooC1Yxc2HtcnVGhn5f](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498538393.png) |  |
| 13 |  | 重新生成视频 |  | ![in_table_image_IJjeb01EnoDGgCxlekjcG98cnxY](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498539077.png) |  |
| 14 | 逐字稿工具:右侧边栏视频预览模块 | 提交整段/重新编辑 |  | ![in_table_image_LNm8bv6ZTo94PrxsHuJcDMH3n7f](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498540350.png) |  |
| 15 | 逐字稿工具:顶部 | 提交整课 | 用户各个Part都状态设置为✅，则此时可点击右上角“保存”，对整课进行保存 |  |  |

![in_table_image_CnqLblTVSoZaW2xqClncWNVmnDt]

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 1. 图片解析与核心价值

*   **图片类型**: UI 界面设计图 (Mockup/Wireframe)。
*   **核心元素**:
    *   页面标题: "群内视频"
    *   视频列表区域: 包含多个视频条目。
    *   视频条目: 单个视频信息的展示单元。
*   **层级结构**:
    1.  **页面层**: "群内视频" 页面，作为承载视频列表的容器。
    2.  **列表层**: 纵向滚动的视频列表，展示群组内的所有相关视频。
    3.  **条目层**: 每个独立的视频条目，包含关键信息，如：
        *   视频封面/缩略图
        *   视频标题
        *   主讲人昵称
        *   视频时长
*   **关联与价值**:
    *   该界面是互联网教育产品中，群组功能下的一个子模块。
    *   **核心作用**: 集中展示与特定群组相关的视频内容。
    *   **核心价值**: 为群组成员提供一个便捷的入口，以查找和观看与群组学习主题、活动或讨论相关的视频资料，增强群组内的学习资源共享和互动效率。

### 2. 功能模块拆解

*   **视频列表展示模块**:
    *   **功能概述**: 从服务端获取并渲染与当前群组关联的视频列表。
*   **视频条目信息模块**:
    *   **功能概述**: 展示单个视频的核心元数据，包括封面图、标题、主讲人和时长。

### 3. 服务端数据需求

为实现“群内视频”列表的展示，服务端需要提供一个接口，该接口根据请求参数（如群组ID），返回一个包含视频信息的列表（数组）。列表中每个对象应包含以下数据字段：

*   `videoThumbnailUrl` (String): 视频封面图的URL。
*   `videoTitle` (String): 视频的标题 (示例: "这是视频的名称", "视频名称二")。
*   `speakerNickname` (String): 主讲人的昵称 (示例: "主讲人昵称", "主讲人昵称二")。
*   `videoDuration` (String/Number): 视频的时长，格式建议为 "MM:SS" 或 "HH:MM:SS" 字符串，或以秒为单位的数字 (示例: "01:55", "02:35")。
*   `videoId` (String/Number): 视频的唯一标识符，用于后续可能的交互（如点击播放）。

*注意：接口可能需要支持分页加载，以处理大量视频的情况（虽然图中未显式表达分页）。*

### 4. 图表类型表示

该图片为 UI 界面设计图，不适用于 Mermaid 语法进行流程图、时序图、类图、ER 图、甘特图或饼图的表示。

### 5. 内容总结约束

所有总结内容均严格基于图片中可见元素（标题、列表项、缩略图、文字标签及其内容），未包含图片外或未明确展示的信息。

### 6. 分析原则遵循

*   **第一性原理**: 从界面展示需求出发，推导所需数据结构。
*   **KISS**: 描述简洁明了，聚焦核心功能和数据。
*   **MECE**: 功能模块划分清晰，数据字段定义明确，无重叠遗漏（基于图片内容）。

【============== 图片解析 END ==============】



- 根据用户是否进入该Part有修改标记：未开始、进行中、已完成：通过不同颜色或图标标识Part的当前状态，如绿色表示“已完成”，蓝色表示“进行中”，灰色表示“未开始”。
- 未开始：尚未在该Part做任何修改
- 进行中：已有修改、但暂未“保存整个段落”；或点击了“重新编辑”
- loading-icon：目前有执行中的生成任务
- ！-icon：loading完成，待用户处理
- 已完成：已点击“保存整个段落”，且没有进入“重新编辑”
- ✅-icon：已完成
![in_table_image_U7xvbukAOoKjerxaZiJcdSD4nse]

###### 图片分析
【============== 图片解析 BEGIN ==============】

**1. 图片类型与核心价值分析**

*   **图片类型:** UI 界面截图 (个人中心页面)
*   **核心价值:** 该界面作为用户在产品内的个人信息聚合和功能导航中心，承载着展示用户身份、学习成就、会员状态，并提供订单、资产管理、设置及推广活动入口等核心功能。在互联网教育场景下，此页面对于增强用户归属感、展示学习进展、引导付费转化和参与活动至关重要。

*   **关键元素层级结构与关联:**
    *   **顶层:** 个人中心页面容器
        *   **用户信息模块:** 包含用户头像、昵称、用户ID，是用户身份的基本展示。
        *   **用户成长数据模块:** 展示累计学习天数、笔记数量、获赞数量，用于体现用户学习投入和成果，增强成就感。
        *   **会员中心模块:** 显示用户会员状态（图中为"会员中心"入口），是付费转化和增值服务的重要入口。
        *   **核心功能入口模块:** 提供 "我的订单"、"我的钱包"、"我的卡券"、"邀请有礼"、"设置" 等常用功能的快速访问入口，方便用户管理账户和服务。
        *   **运营推广模块:** 预留 "广告位"，用于平台活动推广或商业化广告展示。

**2. 功能模块拆解**

*   **用户信息展示:** 显示用户头像、昵
*   **用户ID展示:** 显示用户的唯一标识符。
*   **用户学习数据统计:**
    *   **学习天数:** 展示用户累计学习的天数。
    *   **笔记数量:** 展示用户记录的笔记总数。
    *   **获赞数量:** 展示用户内容获得的赞同总数。
*   **会员中心入口:** 提供跳转至会员详情或开通/续费页面的入口。
*   **订单管理入口:** 提供跳转至用户订单列表页面的入口。
*   **钱包管理入口:** 提供跳转至用户虚拟钱包/余额页面的入口。
*   **卡券管理入口:** 提供跳转至用户优惠券/卡券列表页面的入口。
*   **邀请有礼入口:** 提供跳转至邀请好友活动的页面入口。
*   **设置入口:** 提供跳转至应用设置或账户设置页面的入口。
*   **广告位展示:** 用于显示运营配置的广告图片及链接。

**3. 服务端数据接口需求 (预测)**

为了渲染此“个人中心”页面，服务端需要提供包含以下信息的数据接口：

```json
{
  "user_info": {
    "avatar_url": "用户的头像图片URL (String)",
    "nickname": "用户的昵称 (String)",
    "user_id": "用户的唯一ID (String/Number)"
  },
  "stats": {
    "learning_days": "累计学习天数 (Number)",
    "notes_count": "笔记数量 (Number)",
    "likes_received": "获赞数量 (Number)"
  },
  "membership_info": {
    // 可能包含会员状态、名称、到期时间、入口文案、链接等，
    // 基于图片仅能推断需要一个会员区域的展示信息，具体字段需结合业务确定
    "title": "会员中心模块标题 (String)", // 例如 "会员中心"
    "description": "会员权益或状态描述 (String)", // 例如 "开通会员尊享..."
    "action_url": "点击跳转链接 (String)", // 指向会员详情页
    "icon_url": "会员图标URL (String, 可选)"
     // 或者更具体的字段如:
     // "is_member": true/false (Boolean)
     // "member_level_name": "VIP会员" (String)
     // "expiry_date": "2024-12-31" (String)
  },
  "service_entries": [ // 功能入口通常由前端定义跳转逻辑，但服务端可控制显隐或URL
    {
      "id": "orders",
      "title": "我的订单",
      "icon_url": "订单图标URL (String)",
      "action_url": "订单列表页面链接 (String)"
    },
    {
      "id": "wallet",
      "title": "我的钱包",
      "icon_url": "钱包图标URL (String)",
      "action_url": "钱包页面链接 (String)"
      // 可能需要附加余额信息，如 "balance": 99.8 (Number)
    },
    {
      "id": "coupons",
      "title": "我的卡券",
      "icon_url": "卡券图标URL (String)",
      "action_url": "卡券列表页面链接 (String)"
      // 可能需要附加可用数量，如 "available_count": 3 (Number)
    },
    {
      "id": "invite",
      "title": "邀请有礼",
      "icon_url": "邀请图标URL (String)",
      "action_url": "邀请活动页面链接 (String)"
    },
    {
      "id": "settings",
      "title": "设置",
      "icon_url": "设置图标URL (String)",
      "action_url": "设置页面链接 (String)"
    }
  ],
  "advertisement": { // 可能为null或空对象，表示无广告
    "image_url": "广告图片URL (String)",
    "action_url": "广告跳转链接 (String)",
    "ad_id": "广告位标识符 (String/Number, 用于统计)"
  }
}
```

**注意:** 上述JSON结构是对服务端所需提供数据的合理推测，具体字段名、类型和结构可能需要根据实际技术方案和业务细节进行调整。特别是`membership_info`和`service_entries`，其详细结构依赖于具体业务定义。

**4. Mermaid 图表**

由于该图片是 UI 界面截图，并非流程、时序、类、ER、甘特或饼图等适合使用 Mermaid 直接描述其结构的图表类型。如果需要表示此页面涉及的数据模型，可以使用类图大致表示其数据结构：

```mermaid
classDiagram
    class PersonalCenterViewModel {
        +UserInfo user_info
        +UserStats stats
        +MembershipInfo membership_info
        +List~ServiceEntry~ service_entries
        +Advertisement advertisement
    }

    class UserInfo {
        +String avatar_url
        +String nickname
        +String user_id
    }

    class UserStats {
        +Int learning_days
        +Int notes_count
        +Int likes_received
    }

    class MembershipInfo {
        +String title
        +String description
        +String action_url
        +String icon_url
        // ... 其他可能的会员字段
    }

    class ServiceEntry {
        +String id
        +String title
        +String icon_url
        +String action_url
        // ... 其他可能的入口附加信息字段
    }

    class Advertisement {
       +String image_url
       +String action_url
       +String ad_id
    }

    PersonalCenterViewModel --> UserInfo : has a
    PersonalCenterViewModel --> UserStats : has a
    PersonalCenterViewModel --> MembershipInfo : has a
    PersonalCenterViewModel --> "0..*" ServiceEntry : contains
    PersonalCenterViewModel --> "0..1" Advertisement : contains
```

**5. 内容约束**

本分析严格基于图片所示内容进行，未添加图片以外的信息或功能联想。

【============== 图片解析 END ==============】



- 展示方式：左侧导航栏以列表形式展示所有Part，每个Part显示其标题和状态；数字序号自然递增；
![in_table_image_VZtLbJ5jSoEAHCx2ex7cifAnnjb]

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 1. 图片内容解析

该图片为移动端应用内的 **“课表”界面截图**，属于UI设计图或截图，出自需求文档。它展示了用户（可能是学生或家长）查看课程安排的界面。

**关键元素与关联：**

*   **核心功能：** 提供按周查看课程表的功能。
*   **结构层级：**
    *   **顶层 - 周导航:** 允许用户切换查看不同周的课表。
        *   包含 "上一周"、"下一周" 按钮。
        *   显示当前所选周的日期范围（例如 "10月16日-10月22日"）。
    *   **中层 - 星期展示:** 以列表或网格形式展示选定周的每一天（周一至周日）。
    *   **底层 - 课程卡片:** 在对应的日期下展示具体的课程安排。
        *   每个卡片代表一节课。
        *   卡片内包含课程的关键信息。
    *   **空状态处理:** 对于没有安排课程的日期，显示 "暂无课程" 的提示。

**核心作用与价值：**

*   **用户价值：** 方便用户快速了解自己或关联学生的周课程安排，包括课程名称、上课时间、状态和授课老师，便于规划学习和参与课程。
*   **产品价值：** 作为教育平台的基础功能，提升用户粘性，是连接用户与教学服务（如进入直播、查看回放、预习复习）的重要入口。

### 2. 功能模块拆解

*   **周切换导航模块:**
    *   **功能概述:** 支持用户通过点击箭头在不同周之间切换，以查看相应周的课表信息。
*   **周日期范围显示模块:**
    *   **功能概述:** 展示当前课表视图对应的周的开始和结束日期。
*   **周课表展示模块:**
    *   **功能概述:** 以周一至周日的结构，清晰地罗列出选定周内每天的课程安排。
*   **课程信息卡片模块:**
    *   **功能概述:** 展示单节课程的核心信息，包括课程名称、上课时间、课程状态（如“待上课”、“已结课”）、授课教师。
*   **空状态提示模块:**
    *   **功能概述:** 当某一天没有安排课程时，在该日期下方明确提示用户“暂无课程”。

### 3. 服务端需提供的功能与数据

为实现此课表界面，服务端需要提供以下核心功能和数据接口：

*   **获取周课表数据接口:**
    *   **输入参数:** 用户标识（UserID）、请求的周标识（可以是该周的任意一天日期，或周的序号等）。
    *   **返回数据:**
        *   `weekDateRange`: 字符串，表示请求周的日期范围，格式如 "MM月DD日-MM月DD日" (e.g., "10月16日-10月22日")。
        *   `schedule`: 一个数组或对象，包含该周从周一到周日的课表数据。结构建议如下：
            *   一个包含7个元素的数组（代表周一到周日），每个元素是一个包含当天所有课程的数组（`daySchedule`）。
            *   `daySchedule`: 数组类型，包含当天所有的课程对象。如果某天没有课程，则该数组为空 `[]`。
            *   每个课程对象（`courseItem`）应包含以下字段：
                *   `courseName`: 字符串，课程名称 (e.g., "一年级思维S班")。
                *   `startTime`: 字符串，课程开始时间，格式如 "HH:MM" (e.g., "08:00")。
                *   `status`: 字符串或枚举值，表示课程状态 (e.g., "待上课", "已结课")。
                *   `teacherName`: 字符串，授课教师姓名 (e.g., "张老师")。
                *   `courseId` / `scheduleId`: (可选但推荐) 字符串或数字，课程或排课的唯一标识符，用于后续可能的跳转或操作。

**数据组合示例 (简化):**

```json
{
  "weekDateRange": "10月16日-10月22日",
  "schedule": [
    // 周一
    [
      {
        "courseName": "一年级思维S班",
        "startTime": "08:00",
        "status": "待上课",
        "teacherName": "张老师",
        "courseId": "course_123"
      }
    ],
    // 周二
    [], // 无课程
    // 周三
    [
       {
        "courseName": "一年级思维S班",
        "startTime": "19:30",
        "status": "已结课",
        "teacherName": "张老师",
        "courseId": "course_123"
      }
    ],
    // 周四
    [], // 无课程
    // 周五
    [], // 无课程
     // 周六
    [], // 无课程
     // 周日
    [
       {
        "courseName": "美术鉴赏",
        "startTime": "10:00",
        "status": "待上课",
        "teacherName": "李老师",
        "courseId": "course_456"
      }
    ]
  ]
}
```

### 4. 图表类型识别与Mermaid描述

该图片为 **UI界面截图**，并非流程图、时序图、类图、ER图、甘特图或饼图。因此，无法使用 Mermaid 语法进行直接描述。

【============== 图片解析 END ==============】



- 编辑模式操作说明：
- 点击「编辑模式」进入编辑态，直至点击“保存/取消”或“删除，弹窗中点确认”退出编辑态
- 支持“编辑标题”、“上下移动”、“新增”、“删除”：
- 编辑标题：10个汉字以内，超出编辑不可保存
- 上下移动：点击后可上移/下移1格，此时段落序号随之改变，点击保存后留存数据，否则不变
- 新增：点击段落前/后的按钮，点击后按钮亮起代表生效（可新增前后各一个），点击保存后新增数据，否则不变
- 删除：点击后二次弹窗提示“点击确定后将清空本Part全部内容，该操作不可撤回，是否继续？确定/取消”；点击删除后后续所有Part顺次提前一个序号
![in_table_image_SAyCbtrGXosjb6x5gCEcStmSnPf]

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 1. 核心分析 (互联网产品经理视角 - 互联网教育领域)

*   **图片类型:** UI 界面截图 / Mockup。
*   **来源:** 需求文档。
*   **核心功能:** 该界面提供了一个 "选择打卡" 的功能，允许用户从一个列表中选择一项或多项（根据UI交互推断，虽然当前未选中任何项，但列表形式和确认按钮暗示了选择操作）与学习活动相关的任务或项目进行打卡。
*   **关键元素与层级结构:**
    *   **顶层:** 导航栏
        *   **返回按钮:** (左上角箭头) 用于返回上一级页面。
        *   **页面标题:** "选择打卡"，明确告知用户当前页面的核心目的。
    *   **中层:** 可选打卡任务列表
        *   **任务项 (多个):** 每个任务项代表一个可打卡的学习内容或活动。
            *   **选择控件:** (左侧圆形) 用于标记用户选择的状态（推测为可点击选择）。
            *   **任务名称:** (中部文字，如 "作业1", "测试1", "每日跟读") 清晰展示打卡任务的内容。
            *   **详情指示:** (右侧 ">" 箭头) 暗示点击任务项本身或此箭头可能进入任务详情或下一步操作。
    *   **底层:** 操作按钮
        *   **确认按钮:** "确定"，用于提交用户的选择。
*   **关联与价值:** 此界面是学习流程中任务确认或提交的前置步骤。用户通过此界面明确要参与或已完成的打卡任务，系统据此记录用户的学习行为和进度。在互联网教育产品中，这对于作业提交、出勤记录、习惯养成等场景至关重要，是连接学习内容与用户行为反馈的关键环节。

### 2. 功能模块拆解

*   **导航模块:**
    *   **功能:** 提供页面标题 "选择打卡"，支持返回操作。
*   **打卡任务列表模块:**
    *   **功能:** 展示所有可供选择的打卡任务项。
*   **任务选择模块:**
    *   **功能:** 允许用户通过点击任务项左侧的控件来选择一个或多个打卡任务。
*   **确认操作模块:**
    *   **功能:** 用户完成选择后，点击 "确定" 按钮提交所选的任务项。

### 3. 服务端数据需求

为了渲染此 "选择打卡" 界面并支持其功能，服务端需要提供以下数据：

*   **打卡任务列表数据:**
    *   **数据结构:** 一个包含多个打卡任务对象的数组 (Array)。
    *   **每个任务对象应包含的字段:**
        *   `taskId` (String/Number): 任务的唯一标识符，用于后台识别用户选择了哪个任务。
        *   `taskName` (String): 任务的显示名称 (例如："作业1", "测试1", "每日跟读")。
        *   `isSelectable` (Boolean, 可选): 标记该任务当前是否可选（尽管此界面都显示为可选，但数据结构上可预留）。
        *   `detailsAvailable` (Boolean, 可选): 根据右侧是否有 ">" 箭头，可推断是否需要此字段指示该任务有进一步详情页。

*   **服务端需支持的功能:**
    *   **提供任务列表接口:** 根据上下文（可能是某个课程、学习计划或打卡活动），返回对应的可选打卡任务列表数据。
    *   **接收选择结果接口:** 当用户点击 "确定" 后，客户端会将选中的一个或多个 `taskId` 列表发送给服务端，服务端需要接口来接收并处理这个选择结果（例如：记录打卡、进入下一步流程等）。

**示例数据结构 (JSON):**

```json
{
  "tasks": [
    {
      "taskId": "homework_1",
      "taskName": "作业1"
      // "isSelectable": true, // (可选)
      // "detailsAvailable": true // (可选)
    },
    {
      "taskId": "test_1",
      "taskName": "测试1"
      // "isSelectable": true, // (可选)
      // "detailsAvailable": true // (可选)
    },
    {
      "taskId": "daily_reading_1",
      "taskName": "每日跟读"
      // "isSelectable": true, // (可选)
      // "detailsAvailable": true // (可选)
    }
    // ... more task objects
  ]
}
```

### 4. 图表类型描述

该图片为 UI 界面截图/Mockup，不属于流程图、时序图、类图、ER图、甘特图或饼图，因此不适用 Mermaid 语法进行描述。

### 5. 内容约束

总结内容完全基于图片中显示的元素（标题"选择打卡"、列表项"作业1"、"测试1"、"每日跟读"、选择控件、">"箭头、"确定"按钮）及其隐含的交互逻辑。

### 6. 方法论遵循

*   **第一性原理:** 从用户需要“选择”打卡任务这一基本需求出发，分析界面提供的元素如何满足该需求。
*   **KISS:** 描述简洁明了，聚焦核心功能和数据。
*   **MECE:** 功能模块划分（导航、列表、选择、确认）相互独立且完全覆盖了界面展示的功能；数据需求明确了服务端必须提供的核心信息。

【============== 图片解析 END ==============】



- 视频播放器：在页面顶部放置一个视频播放器，用于播放完整视频。
- 视频更新：展示更新时间，用户可以查看最新生成的视频。
- 播放控制：视频播放器应具备基本的播放控制功能，如播放/暂停、拖动进度条、音量调节、倍速播放（0.5、0.75、1、1.25、1.5、1.75、2）、字幕开关等。
![in_table_image_Wo5nbgJylomMJUxTqxScUPdGnzg]

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理，我们来解析一下这张来自需求文档的UI截图。

1.  **图片类型与核心价值分析**
    *   **图片类型**: 这是一张移动应用端的 **UI界面截图**。
    *   **核心作用**: 该界面核心功能是让用户（在此场景下推测为教师或授课者）能够选择并设定自己在特定日期内可以提供授课服务的时间段。
    *   **结构与关联**:
        *   **顶层**: 标题明确指出功能为“选择授课时间”。
        *   **中层 - 日期选择**: 提供了一个标准日历视图，用户可以通过此视图选择具体的日期。当前视图显示为2024年5月，并选中了14日（星期二）。
        *   **中层 - 时间段选择**: 在选定日期下方，列出了该日期的“可授课时间”列表。时间以半小时为间隔（如 10:00, 10:30, 11:00 等）。部分时间段旁边标注了“已选”，表明用户已将这些时间段标记为可选。
        *   **底层**: 页面底部有一个操作按钮（文字部分可见“确认”二字），用于提交用户的选择。
    *   **价值**: 在互联网教育平台中，此功能是教师管理自身可授课时间的基础，直接关系到课程发布、学生预约、平台排课等核心业务流程，是保证供需匹配的关键环节。

2.  **功能模块拆解**
    *   **日历模块 (Calendar View)**:
        *   **功能概述**: 展示月份视图，支持用户切换月份（推测，通常日历均有此功能）和选择特定日期。高亮显示当前选中的日期。
    *   **可选时间列表模块 (Available Time Slots List)**:
        *   **功能概述**: 根据日历模块选中的日期，动态加载并展示该日可供选择的授课时间点列表。允许用户点选或取消选择时间点，并通过“已选”标识反馈用户的选择状态。
    *   **确认操作模块 (Confirmation Action)**:
        *   **功能概述**: 提供一个提交按钮（如“确认”），用户完成时间选择后，点击此按钮将所选的时间数据提交给服务端进行保存。

3.  **服务端交互与数据描述 (Data Requirements)**

    *   **进入页面/切换月份时，服务端需提供的数据**:
        *   请求参数：可能需要 `teacher_id`（或当前登录用户信息），`year`, `month`。
        *   返回数据：
            *   该月份的数据，重点是每天是否有“可授课时间”的状态信息（例如，哪些日期已经设置过、哪些日期有可选时间段），用于在日历上可能存在的标记（图片中仅明确显示选中日期，但此为日历常用功能）。

    *   **用户选择特定日期时，服务端需提供的数据**:
        *   请求参数：`teacher_id`（或当前登录用户信息），`selected_date` (e.g., "2024-05-14")。
        *   返回数据：一个时间段列表 (Array)，列表中每个元素代表一个可授课的时间点 (Object)，包含：
            *   `time_label` (String): 显示的时间文本，例如 "10:00", "10:30"。
            *   `slot_id` (String/Number): 每个时间段的唯一标识符，用于后续提交。
            *   `initial_status` (String/Enum): 该时间段的初始状态。根据图片显示，至少需要区分“可选”（用户可点选）和“已选”（用户在本轮操作或之前已选择）。可能还存在“不可选”、“已预约”等状态，但图中未明确显示。 服务端需返回用户此前已为该日期设置的“可授课”时间点状态。

    *   **用户点击“确认”按钮时，客户端需提交的数据**:
        *   请求参数：
            *   `teacher_id`（或当前登录用户信息）。
            *   `date` (String): 用户当前操作的日期 (e.g., "2024-05-14")。
            *   `selected_slots` (Array): 用户最终在该日期选择的所有时间段的 `slot_id` 列表。例如：`["slot_id_1000", "slot_id_1030", "slot_id_1100"]`。
        *   服务端需处理：根据提交的数据，更新该教师在该日期的可授课时间记录。
        *   返回数据：操作成功或失败的状态确认。

4.  **Mermaid 图表转换**
    *   该图片为UI界面截图，并非流程图、时序图、类图、ER图、甘特图或饼图等标准图表类型。因此，无法直接使用 Mermaid 语法对该图片本身进行转换。

5.  **内容约束**
    *   以上分析均严格基于图片中可见的元素和交互逻辑进行，未包含图片外信息或臆测功能。电量、网络信号、手机时间等设备状态信息，以及UI的具体颜色、字体、按钮样式等视觉设计细节未作分析。

【============== 图片解析 END ==============】



- 文本编辑框：提供富文本编辑框，用户可以在其中进行逐字稿的编辑，在文本编辑框内的公式展示为预览态
- 公式插入工具：同时提供公式插入按钮，用户可以点击后在新弹窗内的输入框插入由外部复制过来的LaTeX公式
- 提交按钮：
- 在识别到用户有新修改后，提交按钮亮起
- 用户编辑完成后可以点击提交，即将本地结果同步到服务端
- 清除修改按钮：
- 在识别到用户有新修改后，按钮亮起
- 用户点击后：文本框内容回退至上一次提交的数据状态
![in_table_image_VKHRbD9m1oJahBxNfWjcoekynrc]

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来分析一下这份需求文档中的图片。

### 1. 图片类型与核心价值解析

*   **图片类型**: 该图片是一张 **UI 界面截图 (UI Mockup)**，展示了移动应用中的“评价列表”页面。
*   **关键元素与组成**:
    *   **页面标题**: “评价列表”，明确了页面的核心功能。
    *   **评价列表区域**: 占据页面主体，用于展示多条用户评价。
    *   **单条评价卡片**: 构成列表的基本单元，包含以下子元素：
        *   **用户信息**: 用户头像、用户昵称 (如：“橙子”)。
        *   **评价评分**: 星级评分（如：5 颗星）。
        *   **评价内容**: 用户提交的文本评价（如：“橙子的评价内容哦...”）。
        *   **评价时间**: 评价提交的具体日期和时间（如：“2024-07-15 11:07:59”）。
        *   **互动信息**: 点赞图标及点赞数量（如：👍 999）。
*   **层级结构与关联**:
    *   页面 (评价列表) 包含 -> 评价列表区域。
    *   评价列表区域 包含 -> 多条评价卡片。
    *   评价卡片 包含 -> 用户信息、评分、内容、时间、互动信息。
    *   这些元素共同构成了评价列表的展示界面，用户可以通过滑动浏览不同的评价。
*   **核心作用与价值**:
    *   **用户决策支持**: 为潜在用户（学员或家长）提供其他用户的真实反馈和评分，帮助他们了解课程或服务的质量，辅助购买或选择决策。这是互联网教育产品中建立信任和口碑的重要环节。
    *   **用户反馈收集**: 集中展示用户评价，便于平台方或内容提供方（老师、机构）了解用户满意度、发现问题、持续改进产品或服务。
    *   **社交互动与活跃度**: 点赞功能增加了用户间的互动，提升了评价内容的可信度和社区活跃氛围。

### 2. 功能模块拆解

以下是基于图片内容拆解出的功能模块及其简述：

*   **评价列表展示**:
    *   **功能概述**: 从服务端获取评价数据，并在界面上以列表形式展示多条评价信息。
*   **单条评价信息渲染**:
    *   **功能概述**: 将单条评价的各项数据（头像、昵称、评分、内容、时间、点赞数）在对应的卡片区域内正确显示。
*   **点赞信息展示**:
    *   **功能概述**: 显示每条评价获得的点赞总数。*(注：图片仅展示了点赞数，未明确展示点赞交互过程)*

### 3. 服务端数据需求

为了渲染此“评价列表”页面，服务端需要提供包含以下数据结构的列表（通常为 JSON 数组）：

```json
[
  {
    "evaluationId": "string", // 评价唯一标识符
    "userId": "string",       // 评价用户的唯一标识符
    "userAvatarUrl": "string", // 用户头像图片的URL
    "userNickname": "string",  // 用户显示的昵称
    "rating": number,         // 评分（例如：5，代表5颗星）
    "contentText": "string",  // 评价的文本内容
    "timestamp": "string",    // 评价提交的时间戳或格式化字符串 (例如: "2024-07-15 11:07:59")
    "likeCount": number         // 该评价获得的点赞数量
  },
  // ... more evaluation objects
]
```

**关键说明**:

*   服务端需返回一个评价对象的数组。
*   每个评价对象需包含上述字段，确保前端能完整渲染单条评价卡片所需的所有动态内容。
*   列表数据可能需要支持分页加载（虽然图片未直接体现分页控件，但列表通常需要此机制），服务端接口设计时应考虑分页参数（如页码 `page`, 每页数量 `pageSize`）和返回总数 `totalCount`。*(此点基于列表功能的普遍实践，但严格来说图片未显示分页元素)*

### 4. 图表类型分析与 Mermaid 描述

该图片为 **UI 界面截图**，并非流程图、时序图、类图、ER 图、甘特图或饼图等标准图表类型。因此，无法直接使用 Mermaid 语法对其进行等价转换。

### 5. 内容总结限制

本分析严格基于图片中可见的元素和信息进行，未包含图片中未体现的功能（如评价的提交入口、具体的点赞交互逻辑、排序/筛选功能、错误状态处理、空状态页面等）。

### 6. 分析原则遵循

*   **第一性原理**: 从界面包含的基本信息元素出发，推导其功能目的和所需数据。
*   **KISS**: 分析力求简洁明了，聚焦核心功能和数据。
*   **MECE**: 模块拆分和数据描述尽量做到相互独立、完全穷尽图片所展示的内容。

【============== 图片解析 END ==============】



![in_table_image_EojCbK8NXo3w3jx1MC4cW4AVn6c]

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网教育产品领域的专家，我对需求文档中提供的这张UI截图进行了分析。

1.  **图片类型与关键元素解析**

    *   **图片类型:** UI界面截图，具体为一个模态框（Modal/Popup）。
    *   **核心功能:** 提供一个界面，让用户（通常是管理员或教师）从一系列预定义的“奖惩规则”中进行选择。
    *   **关键元素与层级结构:**
        *   **顶层:** 规则选择模态框
            *   **头部区域:**
                *   **标题 ("选择奖惩规则"):** 明确该界面的核心目的。
            *   **搜索区域:**
                *   **搜索框 ("请输入规则名称"):** 用于快速筛选规则列表。
            *   **内容区域:**
                *   **规则列表:** 展示所有可供选择的奖惩规则。
                    *   **单条规则项:**
                        *   **复选框:** 用于选中或取消选中该规则。
                        *   **规则名称 ("规则名称"):** 显示规则的具体内容或标题。
            *   **操作区域:**
                *   **取消按钮 ("取消"):** 关闭模态框，不保存任何更改。
                *   **确定按钮 ("确定"):** 确认用户的选择，并关闭模态框。
    *   **核心作用与价值:** 在教学管理或班级管理等场景下，该功能允许配置需要应用的具体奖惩措施，是实现精细化、规则化管理的重要环节。它使得奖惩体系的应用更加灵活和有针对性。

2.  **功能模块拆解**

    *   **规则搜索:** 用户输入关键词（规则名称），系统实时过滤下方列表，展示匹配的规则。
    *   **规则展示:** 以列表形式清晰列出所有可用的奖惩规则名称。
    *   **规则选择:** 用户通过点击规则前的复选框来选择一个或多个规则。
    *   **选择确认:** 用户点击“确定”按钮，提交所选规则。
    *   **操作取消:** 用户点击“取消”按钮，放弃当前操作，关闭选择界面。

3.  **服务端数据需求**

    *   **获取规则列表接口 (用于展示模态框内容):**
        *   **输入:** 可能需要传入上下文信息（如关联的班级ID、活动ID等），以便服务端判断需要展示哪些规则，以及哪些规则是当前已被选中的状态。
        *   **输出/返回数据:** 服务端需返回一个JSON数组，数组中每个元素代表一条奖惩规则，至少包含以下字段：
            *   `rule_id` (String/Number): 规则的唯一标识符。
            *   `rule_name` (String): 规则的显示名称（如：“按时完成作业加分”，“课堂捣乱扣分”）。
            *   `is_selected` (Boolean): 标识该规则在当前上下文中是否已被选中，用于前端默认勾选状态。

    *   **保存所选规则接口 (用户点击“确定”时调用):**
        *   **输入:** 客户端需传递一个包含所有被选中规则 `rule_id` 的数组给服务端。同时，也需要传递相关的上下文信息（如班级ID、活动ID等），告知服务端这些规则要应用到哪个对象上。
        *   **输出/返回数据:** 服务端处理成功后，应返回操作结果状态（如：`{ "success": true, "message": "保存成功" }` 或 `{ "success": false, "message": "保存失败原因" }`）。

4.  **图表 Mermaid 语法描述**

    此图片为UI界面截图，不适用于标准的流程图、时序图、类图、ER图、甘特图或饼图等Mermaid图表类型进行描述。它展示的是用户交互界面的一个静态状态。

【============== 图片解析 END ==============】



- 文本编辑框--复制工具：点击后复制文本框的全部内容到用户本地的剪切板，便于用户粘贴到外部
- 「重新生成朗读稿和板书」按钮，在用户点击后：
- 提交“逐字稿”中的内容到服务端，并重新生产后面模块内容
- 后面两个模块置灰色，变为：loading状态
- button高亮，文案变为：「正在重新生成朗读稿和板书」
- loading过程中，显示“取消生成”，点击后弹窗显示二次确认弹窗：“确定取消生成吗？内容将回退到之前版本。”
- 点“确定”，2/3内容重置到之前版本，结束loading；
- 点“返回”，关闭弹窗，继续loading。
- 左侧Part状态icon变化为loading中，直至完成加载，变为“！”状态
- 这个过程预计需要10～20分钟
![in_table_image_ZxmqbrA7Yo0pwox20apccHfJnwc]

###### 图片分析

【============== 图片解析 BEGIN ==============】

**1. 图片类型与核心价值分析**

*   **图片类型:** UI 界面截图。
*   **核心分析:**
    *   该截图展示了移动应用内 “我的班级” 功能中 “我加入的” 列表视图。这在互联网教育产品中，是用户（老师、学生或家长）管理和访问其参与班级核心入口。
    *   **关键元素:** 截图主要包含顶部标签页导航、班级列表区域以及底部操作按钮。
    *   **层级结构与关联:**
        1.  **顶层导航:** "我加入的" 和 "我创建的" 标签页，用于区分用户作为成员加入的班级和用户自己创建的班级列表，实现信息分类。图中焦点在 "我加入的" 视图。
        2.  **中层内容:** 班级列表，纵向排列用户加入的各个班级卡片。每个卡片包含班级核心信息，使用户快速识别。
        3.  **底层操作:** "加入新班级" 按钮，提供用户扩展其班级列表（加入新班级）的功能入口。
    *   **核心作用与价值:**
        *   为用户提供了一个集中查看和管理其已加入班级的界面。
        *   通过清晰的班级信息（名称、老师、人数、用户角色），帮助用户快速定位目标班级。
        *   标签页设计有效分离了不同归属类型的班级（加入 vs 创建），提升了信息组织效率，尤其对于可能同时担任创建者和成员角色的教师用户。
        *   提供了便捷的 "加入新班级" 入口，支持用户扩展其班级网络。

**2. 功能模块拆解**

*   **Tab 导航模块:**
    *   **功能概述:** 允许用户在 "我加入的" 班级列表和 "我创建的" 班级列表之间切换视图。图片显示当前选中 "我加入的"。
*   **班级列表模块 (我加入的):**
    *   **功能概述:** 以列表形式展示用户作为成员加入的所有班级。支持垂直滚动查看更多班级。
*   **班级信息卡片模块:**
    *   **功能概述:** 单个班级的摘要信息展示单元。包含以下信息：
        *   `班级名称`: 显示班级的具体名称。
        *   `老师名称`: 显示该班级的主要负责老师姓名。
        *   `成员数量`: 显示该班级的成员总数。
        *   `用户角色`: 显示当前登录用户在该特定班级中的身份或角色（例如：“我是班主任”、“我是任课老师”）。
*   **加入班级模块:**
    *   **功能概述:** 提供一个触发“加入新班级”流程的按钮/入口。

**3. 服务端数据需求 (接口)**

为了渲染 “我加入的” 班级列表视图，服务端需要提供一个接口，该接口在用户认证通过后，返回该用户加入的班级列表数据。

*   **接口功能:** 获取用户加入的班级列表。
*   **请求参数:**
    *   用户身份标识（通常通过 Token 验证）。
    *   可能需要分页参数（如 `page`, `pageSize`）以支持大量班级的加载。
*   **响应数据:**
    *   应返回一个包含班级对象的数组 (List/Array)。
    *   **每个班级对象 (Class Object) 应包含以下字段:**
        *   `classId` (String/Integer): 班级的唯一标识符。
        *   `className` (String): 班级的名称。
        *   `teacherName` (String): 该班级的主要老师姓名。
        *   `memberCount` (Integer): 班级的当前成员总数。
        *   `userRoleInClass` (String): 当前登录用户在该班级中扮演的角色描述文本（直接返回UI所需文本，如 "我是班主任", "我是任课老师"）。

**示例 JSON 响应体结构:**

```json
{
  "success": true,
  "data": {
    "classList": [
      {
        "classId": "class_1001",
        "className": "3年级1班",
        "teacherName": "李芳",
        "memberCount": 32,
        "userRoleInClass": "我是班主任"
      },
      {
        "classId": "class_1002",
        "className": "1234567890123456",
        "teacherName": "张老师", // 注意：图片中第二条未明确显示老师，此处为示例假设
        "memberCount": 45, // 注意：图片中第二条未明确显示人数，此处为示例假设
        "userRoleInClass": "我是任课老师"
      }
      // ... 其他班级对象
    ],
    "pagination": { // 可选，如果实现了分页
      "currentPage": 1,
      "pageSize": 10,
      "totalCount": 25,
      "totalPages": 3
    }
  },
  "message": "获取成功"
}
```

**4. 图表类型转换 (Mermaid)**

该图片为 UI 界面截图，不属于流程图、时序图、类图、ER 图、甘特图或饼图，因此不适用 Mermaid 语法进行转换。

**5. 内容合规性**

本总结严格基于图片所示内容进行分析，未包含图片以外的信息或进行主观臆测。

【============== 图片解析 END ==============】



![in_table_image_Yiujbht6MoXkoQxATzscWmoyntF]

###### 图片分析
【============== 图片解析 BEGIN ==============】

根据您提供的需求文档中的图片（UI界面截图），作为互联网产品经理，我对其进行解析如下：

**1. 图片关键元素、组成部分及关联分析**

这张图片展示的是一个移动应用内的“班级”或“班组”详情页面，属于互联网教育场景下的核心功能界面。

*   **图片类型:** UI界面截图。

*   **关键元素与组成部分:**
    *   **页面标题/机构标识:** 页面顶部显示所属机构名称（“大米和小米深圳中心”），明确了班级归属的上下文。
    *   **Tab导航:** 包含“班级”、“动态”、“学习”三个并列的功能入口，当前选中“班级”。这表明该模块聚合了班级信息、互动交流和学习内容三大核心功能。
    *   **班级核心信息区:**
        *   **班级名称:** “2023-2024新学员学习”，标识当前班级。
        *   **班级公告:** 显示重要通知信息（“班级公告：欢迎各位新学员入群…”），支持部分内容预览。
        *   **班级统计数据:** 显示学员数量（15）和动态数量（2），提供班级规模和活跃度的概览。
        *   **主要操作入口:** “进入学习”和“查看全部动态”按钮，引导用户进行核心的学习活动或查看更多互动内容。
    *   **班级成员展示区:**
        *   标题：“班级成员”。
        *   内容：以头像和昵称列表形式展示部分班级成员，增强社群感和归属感。
    *   **班级动态信息流区:**
        *   标题：“班级动态”。
        *   内容：以卡片列表形式展示班级的最新动态，每条动态包含发布者信息（头像、昵称）、发布时间、文本内容、图片/视频（缩略图）、互动数据（点赞、评论、分享）。

*   **元素间关联:**
    *   Tab导航控制下方主体内容的切换，“班级”Tab对应当前展示的所有信息。
    *   班级核心信息区的统计数据（学员数、动态数）与成员列表、动态列表的内容在数量上相关联。
    *   操作入口按钮（“进入学习”、“查看全部动态”）分别链接到“学习”Tab对应的内容或完整的动态列表页面。
    *   成员展示区和动态信息流区共同构成了班级的社交互动和人员组成展示。

*   **核心作用与价值:**
    *   **信息聚合:** 将班级相关的核心信息（公告、成员、动态）和功能入口（学习、查看动态）集中展示，方便用户快速访问。
    *   **社群构建:** 通过展示班级成员和动态，营造学习社群氛围，促进师生、学员间的互动交流。
    *   **学习引导:** 突出“进入学习”入口，直接引导用户参与核心的教育活动。
    *   **信息同步:** 公告功能确保重要信息有效触达班级成员。
    *   **活跃度展示:** 动态和互动数据反映了班级的活跃程度。

**2. 功能模块拆解**

以下是图片所展示页面的功能模块列表及简要概述：

*   **顶部导航栏:** 显示当前页面所属机构或模块标题。
*   **Tab导航模块:** 提供在“班级”概览、“动态”列表、“学习”内容三个主要功能分区之间切换的入口。
*   **班级信息展示模块:** 显示当前班级的名称、公告（预览）、学员总数和动态总数。
*   **核心操作入口模块:** 提供“进入学习”和“查看全部动态”两个按钮，分别用于跳转到学习内容区和完整的动态列表。
*   **班级成员预览模块:** 以列表形式展示部分班级成员的头像和昵称。
*   **班级动态预览模块:** 以信息流形式展示最新的班级动态，每条动态包含发布者、时间、内容、媒体（图/文）及互动数据（点赞、评论、分享数）。

**3. 服务端数据需求描述**

为实现如图所示的功能，服务端需要提供以下数据（忽略设备及前端样式信息）：

*   **获取班级详情数据接口:**
    *   `className` (String): 班级名称，例如 "2023-2024新学员学习"。
    *   `classAnnouncement` (String): 班级公告完整内容。
    *   `memberCount` (Integer): 班级成员总数，例如 15。
    *   `postCount` (Integer): 班级动态总数，例如 2。
    *   `membersPreview` (Array<Object>): 部分班级成员信息列表（可能按特定规则选取，如最新加入或最活跃）。每个成员对象需包含：
        *   `userId` (String/Integer): 成员唯一标识。
        *   `avatarUrl` (String): 成员头像URL。
        *   `nickname` (String): 成员昵称。
    *   `postsPreview` (Array<Object>): 部分班级动态信息列表（通常为最新的几条）。每条动态对象需包含：
        *   `postId` (String/Integer): 动态唯一标识。
        *   `authorInfo` (Object): 发布者信息。
            *   `userId` (String/Integer): 发布者唯一标识。
            *   `avatarUrl` (String): 发布者头像URL。
            *   `nickname` (String): 发布者昵称，例如 "曾老师"。
        *   `publishTime` (String/Timestamp): 动态发布时间（服务端应提供标准时间格式或时间戳，由客户端处理相对时间显示，如 "1小时前"）。
        *   `content` (String): 动态的文本内容。
        *   `media` (Array<Object>): 动态包含的媒体信息（图片或视频）。每个媒体对象需包含：
            *   `type` (String): 媒体类型，例如 'image' 或 'video'。
            *   `thumbnailUrl` (String): 媒体的缩略图URL。
            *   `originalUrl` (String): (可选) 原始媒体文件URL。
        *   `likeCount` (Integer): 点赞数量，例如 0。
        *   `commentCount` (Integer): 评论数量，例如 0。
        *   `shareCount` (Integer): 分享数量，例如 0。

*   **关联功能所需数据/接口（基于按钮推断）:**
    *   需要提供“进入学习”的具体跳转逻辑或目标数据（例如，学习模块的入口标识或URL）。
    *   需要提供获取完整班级动态列表的接口（支持分页）。
    *   需要提供获取完整班级成员列表的接口（可能支持分页或按条件筛选）。
    *   需要支持对动态进行点赞、评论、分享操作的相应接口。

**4. 图表类型描述**

该图片为 **UI界面截图**，不属于流程图、时序图、类图、ER图、甘特图或饼图，因此不适用Mermaid语法进行描述。

**5. 内容总结约束**

本总结严格基于图片所呈现的视觉元素和文本信息进行分析，未包含图片以外的任何推断或联想。

【============== 图片解析 END ==============】



![in_table_image_NgoRbJG48oED9LxoW0ccMpzenQh]

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 1. 图片类型、关键元素及核心价值分析

*   **图片类型：** UI 界面设计图/原型图。
*   **核心功能：** 添加新练习簿。
*   **关键元素与层级结构：**
    *   **顶层：** 页面导航栏
        *   **元素：** 页面标题 "添加新练习"。
    *   **中层：** 内容区域
        *   **元素1：** 练习簿名称输入模块
            *   **子元素：** 标签 "练习簿名称"
            *   **子元素：** 输入框 (含占位符 "请输入练习簿名称")
        *   **元素2：** 练习册选择模块
            *   **子元素：** 标签 "选择练习册"
            *   **子元素：** 练习册列表 (包含多个可选项)
                *   **列表项 (示例):**
                    *   图标 (练习册封面样式)
                    *   标题 (如 "人教版 一年级上册")
                    *   描述 (如 "18个练习")
                    *   选择控件 (单选按钮样式，其中一个处于选中状态)
    *   **底层：** 操作区域
        *   **元素：** 功能按钮 "创建"。
*   **元素关联与核心作用：**
    *   用户在此界面首先通过 **练习簿名称输入模块** 为即将创建的练习簿命名。
    *   然后，在 **练习册选择模块** 中，用户从一个预定义的列表中选择一个基础练习册作为模板或内容来源。列表展示了练习册的名称、包含的练习数量等信息，并通过单选控件让用户指定唯一的选择。
    *   最后，用户点击底部的 **"创建"按钮**，系统将基于用户输入的名称和选定的基础练习册，完成新练习簿的创建流程。
*   **核心价值：** 该界面为用户（可能是教师）提供了一个清晰、便捷的入口，用于根据现有教材或模板（如“人教版”各年级册次）快速创建个性化的练习簿，是练习资源管理和教学活动准备的重要环节。

### 2. 功能模块拆解

*   **练习簿名称输入：**
    *   **功能概述：** 允许用户为新创建的练习簿自定义名称。
*   **基础练习册选择：**
    *   **功能概述：** 展示可供选择的基础练习册列表，用户可以从中单选一个作为创建新练习簿的模板或内容基础。
*   **创建操作：**
    *   **功能概述：** 用户确认输入和选择后，触发创建新练习簿的后端逻辑。

### 3. 服务端功能与数据描述

为支持此界面的功能，服务端需要提供以下支持：

*   **获取可选基础练习册列表：**
    *   **功能描述：** 服务端需提供一个接口，返回当前用户或上下文可用的基础练习册列表。
    *   **返回数据内容：** 一个包含多个练习册对象的数组（Array）。
    *   **数组中每个对象（Object）的数据结构：**
        *   `id` (String/Number): 练习册的唯一标识符。
        *   `icon_url` (String): 练习册封面图标的URL地址。
        *   `title` (String): 练习册的标题（例如："人教版 一年级上册"）。
        *   `description` (String): 练习册的描述信息（例如："18个练习"）。
*   **创建新练习簿：**
    *   **功能描述：** 服务端需提供一个接口，接收用户提交的新练习簿信息，并在系统中创建相应的记录。
    *   **接收数据内容：**
        *   `name` (String): 用户输入的练习簿名称。
        *   `base_book_id` (String/Number): 用户所选择的基础练习册的唯一标识符 (`id`)。
    *   **返回数据内容：**
        *   成功时：返回成功状态，可能包含新创建练习簿的ID或其他相关信息。
        *   失败时：返回错误状态及相应的错误信息（如名称重复、选择无效等）。

### 4. 图表类型判定与 Mermaid 描述

*   **图表类型判定：** 该图片为 UI 界面设计图，不属于流程图、时序图、类图、ER图、甘特图或饼图。
*   **Mermaid 描述：** 因此，无法使用 Mermaid 语法进行描述。

【============== 图片解析 END ==============】



- 音频播放器：在模块底部放置一个音频播放器，用于播放完整音频。
- 音频更新：展示用户提交后的最新拼接完成的视频，用户逐个句子编辑后，只要点击了“生成配音”，就可以在这里听到最新的音频
- 播放控制：播放器应具备基本的播放控制功能，如播放/暂停、拖动进度条、音量调节、倍速播放（0.5、0.75、1、1.25、1.5、1.75、2）
- 文本编辑：将朗读稿按句分割，用户可以逐句进行编辑。
- 生成配音：在识别到文本框修改后，以“！”符号提示用户提供生成音频，用户点击后可以生成音频（注意不可排队，一次只有一段音频生成）
- 音频播放：提供音频播放功能，用户可以预览生成的音频。
- 下载：提供本句子下载到本地，命名为：《xx课程名称朗读稿第x句音频时间戳.格式》
- 上传：点击后，用户可以上传本地音频到服务端，上传完成后，同时根据识别音频结果，更新这句话的朗读稿文本
- 提交按钮：
- 在识别到用户有修改后，提交按钮亮起
- 用户编辑完成后可以点击提交，即将本地结果同步到服务端
![in_table_image_BnlAbL6LwoXSSOx1UmwcXfsXnuh]

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网教育产品经理，我们分析这份需求文档中的图片，该图片展示了**移动应用端“分享学情报告”功能的界面设计稿或截图**。

**1. 图片关键元素、组成与关联 (层级化结构)**

该界面旨在向用户（可能是学生或家长）展示一份学情报告的摘要预览，并提供分享或保存的功能。其核心结构如下：

*   **顶层容器 (Screen Container):**
    *   **导航栏 (Navigation Bar):**
        *   返回按钮 (Back Button): 用于返回上一级页面。
        *   标题 (Title): "分享学情报告"，明确当前页面功能。
    *   **内容区 (Content Area):**
        *   **报告预览卡片 (Report Preview Card):**
            *   用户信息 (User Info): 头像、昵称 ("花想容")。
            *   报告周期 (Report Period): "10月30日-11月5日"。
            *   报告标题 (Report Title): "XXX的周学情报告" (XXX为动态学生名称)。
            *   核心指标摘要 (Key Metrics Summary):
                *   学习时长 (Learning Duration)
                *   学习天数 (Learning Days)
                *   完成任务数 (Completed Tasks)
                *   测试平均分 (Average Test Score)
            *   学科/活动详情 (Subject/Activity Details):
                *   学科与活动名称 (e.g., "数学 - 同步练习")
                *   具体指标 (e.g., 正确率, 耗时, 排名) - *注：图片中仅展示一例，实际可能包含多项*
            *   二维码 (QR Code): 用于引导查看完整报告。
            *   引导语 (Instruction Text): "微信扫码查看完整版报告"。
    *   **操作区 (Action Area):**
        *   分享按钮 (Share Button): "分享给好友"。
        *   保存按钮 (Save Button): "保存到相册"。

**关联:**

*   导航栏提供上下文和返回路径。
*   内容区是核心，展示由服务端获取并处理后的学情数据摘要。用户信息、报告周期、核心指标、学科详情共同构成了报告预览内容。
*   二维码与引导语关联，指向外部（如H5）的完整报告。
*   操作区的按钮基于预览内容，提供后续的用户行为（分享或保存）。

**核心作用与价值:**

此界面在整体需求中扮演**学情报告传播与留存**的关键节点。其价值在于：
*   **可视化呈现:** 将复杂的学情数据以简洁、易懂的卡片形式展示给用户。
*   **便捷分享:** 鼓励用户将学习成果或情况分享至社交网络，扩大产品影响力或实现家校互动。
*   **数据留存:** 允许用户保存报告图片，方便离线查看或存档。
*   **引流:** 通过二维码引导用户访问更详细的完整版报告（可能在H5页面或App内其他位置），增加用户粘性与深度互动。

**2. 功能模块拆解**

*   **返回导航 (Back Navigation):**
    *   *功能概述:* 返回到进入此分享页之前的界面。
*   **报告预览展示 (Report Preview Display):**
    *   *功能概述:* 显示指定学生在特定时间周期内的学情报告摘要，包括个人信息、整体学习统计和关键学科/活动的表现。
*   **二维码生成/展示 (QR Code Display):**
    *   *功能概述:* 显示一个指向完整版学情报告的二维码。
*   **分享功能 (Sharing Feature):**
    *   *功能概述:* 调用设备的分享接口，将报告预览（可能是图片形式或链接形式）分享给指定社交平台的好友（如微信）。
*   **保存到相册 (Save to Album):**
    *   *功能概述:* 将当前显示的报告预览界面生成为图片，并保存到用户设备的本地相册中。

**3. 服务端数据需求**

为支持此界面显示，服务端需要提供以下功能和数据内容：

*   **获取学情报告摘要数据API:**
    *   **请求参数:** 可能需要用户ID (UserID)、学生ID (StudentID)、报告ID (ReportID) 或报告周期 (StartDate, EndDate) 等。
    *   **返回数据 (JSON 结构示例):**
        ```json
        {
          "code": 0, // 状态码，0表示成功
          "message": "Success",
          "data": {
            "report_share_title": "分享学情报告", // 页面标题 (虽然常为前端静态，但可配置)
            "user_info": {
              "avatar_url": "https://example.com/path/to/avatar.png", // 用户头像URL
              "nickname": "花想容" // 用户昵称
            },
            "student_name": "小明", // 学生姓名 (用于替换报告标题中的"XXX")
            "report_period": {
              "start_date_formatted": "10月30日", // 格式化后的开始日期
              "end_date_formatted": "11月5日" // 格式化后的结束日期
            },
            "report_title_template": "{student_name}的周学情报告", // 报告标题模板
            "summary_metrics": {
              "learning_duration": {
                "value": 0,
                "unit": "分钟" // 单位
              },
              "learning_days": {
                "value": 0,
                "unit": "天"
              },
              "completed_tasks": {
                "value": 0,
                "unit": "个"
              },
              "average_score": {
                "value": 0,
                "unit": "分"
              }
            },
            "subject_details": [ // 学科/活动详情列表 (可能有多项)
              {
                "subject_name": "数学",
                "activity_name": "同步练习",
                "accuracy_rate": {
                  "value": 0,
                  "unit": "%"
                },
                "time_spent": {
                  "value": 0,
                  "unit": "秒"
                },
                "ranking": "暂无" // 排名，可能是字符串 "暂无" 或 "第X名"
              }
              // ... potentially more subject/activity objects
            ],
            "qr_code_url": "https://example.com/path/to/qrcode.png", // 指向完整报告的二维码图片URL
            "full_report_url": "https://example.com/path/to/full_report/report_id_123", // 二维码对应的完整报告链接 (供客户端侧生成二维码或直接使用)
            "share_config": { // 分享所需内容 (可选，便于客户端直接使用)
                 "share_title": "花想容的学情报告", // 分享标题
                "share_description": "快来看看我在小路学上周的学习情况吧！", // 分享描述
                "share_image_url": "https://example.com/path/to/share_preview.png" // 分享预览图 (可能是本摘要图)
            }
          }
        }
        ```

**注意:**
*   数据结构和字段名仅为示例，具体需根据实际业务和技术协议确定。
*   所有数值和对应的单位都需要服务端提供。
*   排名字段的处理逻辑（如"暂无"的显示）由服务端决定返回具体文本还是由客户端根据特定值（如null或-1）进行判断显示。
*   二维码URL (`qr_code_url`) 和完整报告URL (`full_report_url`) 必须提供。

**4. Mermaid 图表描述**

由于该图片是UI界面截图，主要展示数据结构和用户可执行的操作，而非复杂的流程或关系，使用Mermaid的 flowchart 描述用户在此界面的基本操作流比较合适：

```mermaid
flowchart TD
    A[进入“分享学情报告”页面] --> B{查看报告预览内容};
    B --> C[选择操作];
    C --> D[点击“分享给好友”];
    C --> E[点击“保存到相册”];
    D --> F[调用系统分享功能];
    E --> G[生成报告图片并保存];
```

**5. 基于图片内容的总结**

图片展示了一个用于分享学情报告摘要的移动端界面。界面清晰地呈现了学生的基本信息、报告周期、核心学习统计数据（时长、天数、任务、分数）以及至少一个学科/活动的具体表现（正确率、耗时、排名）。界面包含一个指向完整报告的二维码和两个主要操作按钮：“分享给好友”和“保存到相册”。服务端需提供所有显示相关的动态数据，包括用户信息、报告数据详情、二维码及相关链接。

【============== 图片解析 END ==============】



![in_table_image_CxfjbUnB0oCGtOx7MRUcJp32nnd]

###### 图片分析
【============== 图片解析 BEGIN ==============】

好的，我们来一起看一下这张图片，它展示的是我们教育产品中一个关键的用户触点——“学情周报”界面的设计稿或截图。

1.  **图片类型与关键元素分析**

    *   **图片类型:** UI 界面截图/设计稿。
    *   **核心目的:** 向学生（或关注学生学习情况的角色，如家长）展示其在过去一周内的学习行为汇总数据与表现。
    *   **关键元素与层级结构:**
        *   **顶层:** 导航栏
            *   返回操作
            *   标题: "学情周报"
        *   **主体内容区:**
            *   **报告标识:**
                *   主标题: "学情周报"
                *   时间范围: "12月25日-12月31日"
            *   **核心指标:**
                *   **学习时长:**
                    *   总时长: "15小时35分30秒"
                    *   周对比: "较上周 +8小时15分10秒"
                *   **综合排名:** "打败了 90% 的同学"
            *   **分项数据统计:** (以卡片或列表形式展示)
                *   **做题:**
                    *   总题数: "345 题"
                    *   正确率: "80%"
                    *   子项1 (可能是做对题数): "150 题", 周对比: "(较上周 +10)"
                    *   子项2 (可能是做错题数或其他分类): "295 题", 周对比: "(较上周 +10)"
                *   **看课:**
                    *   总节数: "34 节课"
                    *   完成率: "80%"
                    *   子项1 (可能是完成节数): "15 节", 周对比: "(较上周 +10)"
                    *   子项2 (可能是未完成或观看中节数): "29 节", 周对比: "(较上周 +10)"
                *   **笔记:**
                    *   总条数: "34 条"
                    *   子项1 (可能是新增笔记数): "15 条", 周对比: "(较上周 +10)"
                *   **提问:**
                    *   总条数: "34 条"
                    *   子项1 (可能是新增提问数): "15 条", 周对比: "(较上周 +10)"
            *   **操作引导:**
                *   按钮/链接: "查看完整周报"

    *   **元素关联:** 顶层导航提供上下文和返回路径。主体内容区首先给出报告的时间范围，然后是两个最关键的概览指标（时长和排名），接着是按学习活动类型（做题、看课、笔记、提问）细分的统计数据，最后提供深入查看完整报告的入口。数据之间通过“较上周”的变化值建立时间维度的对比。
    *   **核心作用与价值 (需求/技术方案层面):**
        *   **用户价值:** 提供定期、量化的学习反馈，帮助学生了解自己的学习投入、效率和相对位置，可能激发学习动力或引导学习策略调整。
        *   **产品价值:** 增强用户粘性，通过定期报告让用户感知学习进展，体现产品的数据分析能力，是重要的用户留存和激励手段。
        *   **技术实现提示:** 需要后端具备对用户在特定时间周期内的各种学习行为（观看视频时长、做题数量/正确率、笔记数量、提问数量等）进行聚合统计、环比计算以及用户间排名计算的能力。

2.  **功能模块拆解**

    *   **学情周报概览模块:**
        *   **功能概述:** 显示周报标题、对应的时间区间。
    *   **学习时长统计模块:**
        *   **功能概述:** 展示用户在一周内的总学习时长，并显示与上周时长的对比变化。
    *   **学习排名模块:**
        *   **功能概述:** 显示用户本周学习表现在所有用户中的百分位排名。
    *   **做题数据统计模块:**
        *   **功能概述:** 汇总展示周内做题总数、正确率，以及两个细分项（如做对/做错题数或其他分类）的数量及周同比变化。
    *   **看课数据统计模块:**
        *   **功能概述:** 汇总展示周内看课总节数、完成率，以及两个细分项（如完成/未完成节数或其他分类）的数量及周同比变化。
    *   **笔记数据统计模块:**
        *   **功能概述:** 汇总展示周内笔记总条数，以及一个细分项（如新增笔记数）的数量及周同比变化。
    *   **提问数据统计模块:**
        *   **功能概述:** 汇总展示周内提问总条数，以及一个细分项（如新增提问数）的数量及周同比变化。
    *   **完整报告入口模块:**
        *   **功能概述:** 提供一个导航入口，引导用户查看更详细的周报内容。

3.  **服务端数据需求**

    为渲染此界面，服务端需要提供一个接口，该接口能根据用户ID和指定的周（或默认上一个完整的周）返回以下数据结构：

    ```json
    {
      "reportTitle": "学情周报", // 或由前端写死
      "dateRange": "12月25日-12月31日", // String: YYYY年MM月DD日-YYYY年MM月DD日 格式 或 MM月DD日-MM月DD日
      "studyDuration": {
        "totalSeconds": 56130, // Integer: 总学习秒数 (15*3600 + 35*60 + 30)
        "changeVsLastWeekSeconds": 29710 // Integer: +/- 秒数 (+8*3600 + 15*60 + 10)
      },
      "peerRankPercentile": 90, // Integer/Float: 打败的同学百分比
      "problemSolvingStats": {
        "totalQuestions": 345, // Integer: 总题数
        "accuracyRate": 0.80, // Float: 正确率 (0.0 - 1.0)
        "subCount1": { // 需要明确此子项含义，如 correct_questions
          "count": 150, // Integer
          "changeVsLastWeek": 10 // Integer: +/- 数量
        },
        "subCount2": { // 需要明确此子项含义，如 incorrect_questions 或 attempted_questions
          "count": 295, // Integer
          "changeVsLastWeek": 10 // Integer: +/- 数量
        }
      },
      "lessonWatchingStats": {
        "totalLessons": 34, // Integer: 总节数
        "completionRate": 0.80, // Float: 完成率 (0.0 - 1.0)
        "subCount1": { // 需要明确此子项含义，如 completed_lessons
          "count": 15, // Integer
          "changeVsLastWeek": 10 // Integer: +/- 数量
        },
        "subCount2": { // 需要明确此子项含义，如 watched_partially_lessons 或 attempted_lessons
          "count": 29, // Integer
          "changeVsLastWeek": 10 // Integer: +/- 数量
        }
      },
      "noteTakingStats": {
        "totalNotes": 34, // Integer: 总笔记条数
        "subCount1": { // 需要明确此子项含义，如 new_notes
          "count": 15, // Integer
          "changeVsLastWeek": 10 // Integer: +/- 数量
        }
      },
      "questionAskingStats": {
        "totalQuestions": 34, // Integer: 总提问条数
        "subCount1": { // 需要明确此子项含义，如 new_questions
          "count": 15, // Integer
          "changeVsLastWeek": 10 // Integer: +/- 数量
        }
      },
      "fullReportUrl": "/path/to/full/weekly/report" // String: (可选) 完整报告页面的路由或URL
    }
    ```
    **注意:** `subCount1` 和 `subCount2` 在做题和看课部分需要明确其业务含义，以便前端准确展示标签。笔记和提问只有一个 `subCount1`。所有 `changeVsLastWeek` 都需要表示增加或减少（正负整数）。

4.  **Mermaid 图表描述**

    该图片为 UI 界面截图，不适用于流程图、时序图、类图、ER图、甘特图或饼图等标准图表类型，因此无法使用 Mermaid 进行描述。

【============== 图片解析 END ==============】



- markdown编辑器：
- 用户可以在其中进行板书文字编辑
- 标题格式：## 三级标题名称
- 高亮格式&位置标记：使用成对的xml标签提供高亮标记功能，用户可以对板书内容进行高亮标记的添加和删除
- 公式插入工具：同时提供公式插入按钮，用户可以点击后在新弹窗内的输入框插入由外部复制过来的LaTeX公式
![in_table_image_LDIwb590goA06FxbCZzcQO7Snsu]

###### 图片分析
【============== 图片解析 BEGIN ==============】

**1. 图片类型、关键元素与核心价值分析**

*   **图片类型:** 用户界面（UI）设计稿/线框图。
*   **核心场景:** 该界面是互联网教育产品中的“我的”或“个人中心”页面。
*   **关键元素与层级结构:**
    *   **顶部区域 - 用户基础信息:**
        *   用户头像
        *   用户昵称
        *   用户ID
        *   成长值（或类似等级/积分体系）
        *   签到按钮
        *   通知中心入口（消息图标）
    *   **中部区域 - 用户核心数据与服务入口:**
        *   **用户统计:**
            *   关注数
            *   粉丝数
            *   获赞数
        *   **会员推广/状态:**
            *   VIP会员推广Banner（包含特权说明和开通入口）
        *   **常用功能入口 (Grid布局):**
            *   我的订单
            *   优惠券
            *   我的课时
            *   已购课程
            *   我的收藏
            *   我的下载
            *   浏览记录
            *   评价中心
        *   **其他功能/服务入口 (List布局):**
            *   我的组队
            *   学习档案
            *   联系客服
            *   设置
    *   **底部区域 - 应用导航（非本页核心功能）:**
        *   包含首页、学习等主要导航入口 (细节非本页分析重点)。
*   **核心作用与价值:**
    *   **用户中心枢纽:** 为用户提供个人信息管理、学习数据概览、内容资产（课程、订单、下载等）管理、以及平台服务（客服、设置）的统一入口。
    *   **用户粘性与活跃度:** 通过签到、成长值、粉丝关注等设计，提升用户参与度和平台粘性。
    *   **商业化转化:** VIP会员推广区域是重要的付费转化入口。
    *   **便捷性:** 集中展示常用功能，方便用户快速访问，优化用户体验。

**2. 功能模块拆解与概述**

*   **用户信息展示:** 显示用户头像、昵称、ID、成长值等基本信息。
*   **用户统计聚合:** 展示用户的社交关系数据（关注、粉丝）和互动数据（获赞）。
*   **签到功能:** 提供用户每日签到的入口，用于激励用户活跃度。
*   **通知访问:** 提供访问应用内通知或消息的入口。
*   **VIP会员服务:** 展示会员权益，引导用户开通或续费VIP服务。
*   **订单管理:** 用户查看和管理历史购买订单的入口。
*   **优惠券管理:** 用户查看和管理可用优惠券的入口。
*   **课时管理:** 用户查看可用或已用课时情况的入口。
*   **已购课程访问:** 用户访问已购买课程内容的入口。
*   **收藏管理:** 用户访问收藏的课程、资料或其他内容的入口。
*   **下载管理:** 用户管理已下载的离线学习内容的入口。
*   **浏览历史查看:** 用户查看最近浏览过的课程或内容的入口。
*   **评价中心访问:** 用户管理评价、查看评价记录的入口。
*   **组队功能访问:** 用户参与或管理学习小组/队伍的入口。
*   **学习档案查看:** 用户访问个人详细学习记录和分析报告的入口。
*   **客户服务:** 提供联系平台客服的入口。
*   **应用设置:** 提供进行账户及应用相关设置的入口。

**3. 服务端接口与数据需求 (客观描述)**

服务端需要提供接口返回以下数据结构，用于渲染此“个人中心”页面：

```json
{
  "userInfo": {
    "avatarUrl": "string", // 用户头像URL
    "nickname": "string", // 用户昵称
    "userId": "string", // 用户唯一标识
    "growthValue": { // 成长值信息 (具体结构可按业务定)
       "value": number, // 当前成长值数值
       "levelName": "string" // (可选) 等级名称，图片中未明确显示，但常见
    },
    "isCheckedInToday": boolean, // 今日是否已签到
    "unreadNotificationCount": number // 未读通知数量
  },
  "userStats": {
    "followingCount": number, // 关注数
    "followerCount": number, // 粉丝数
    "likesReceivedCount": number // 获赞数
  },
  "vipInfo": {
    "isVip": boolean, // 当前是否为VIP会员
    "displayPromoBanner": boolean, // (针对非VIP) 是否展示推广Banner
    "promoBanner": { // (当displayPromoBanner为true时提供)
      "imageUrl": "string", // Banner图片URL
      "title": "string", // Banner主标题 (e.g., "会员中心")
      "subtitle": "string", // Banner副标题/权益摘要 (e.g., "畅享多项特权")
      "ctaText": "string", // 按钮文字 (e.g., "立即开通")
      "actionTarget": "string" // 点击Banner或按钮的跳转目标标识 (URL或内部路由)
    },
    "vipStatusText": "string" // (可选，针对VIP用户) 显示VIP到期时间等信息，图片未明确显示
  },
  "featureEntries": { // 功能入口列表 (可以根据实际需要决定展示哪些及顺序)
    "gridEntries": [ // Grid布局的功能项
      { "key": "orders", "title": "我的订单", "iconUrl": "string", "badgeCount": number | null }, // badgeCount 可选，用于显示未处理订单等
      { "key": "coupons", "title": "优惠券", "iconUrl": "string", "badgeCount": number | null }, // badgeCount 可选，用于显示可用优惠券数量
      { "key": "classHours", "title": "我的课时", "iconUrl": "string", "badgeCount": number | null },
      { "key": "purchasedCourses", "title": "已购课程", "iconUrl": "string", "badgeCount": number | null },
      { "key": "favorites", "title": "我的收藏", "iconUrl": "string", "badgeCount": number | null },
      { "key": "downloads", "title": "我的下载", "iconUrl": "string", "badgeCount": number | null },
      { "key": "history", "title": "浏览记录", "iconUrl": "string", "badgeCount": number | null },
      { "key": "reviews", "title": "评价中心", "iconUrl": "string", "badgeCount": number | null } // badgeCount 可选，用于显示待评价数量
    ],
    "listEntries": [ // List布局的功能项
      { "key": "teams", "title": "我的组队", "iconUrl": "string", "badgeCount": number | null },
      { "key": "learningProfile", "title": "学习档案", "iconUrl": "string", "badgeCount": number | null },
      { "key": "support", "title": "联系客服", "iconUrl": "string", "badgeCount": number | null },
      { "key": "settings", "title": "设置", "iconUrl": "string", "badgeCount": number | null }
    ]
  }
}
```

**注意:**
*   `iconUrl` 是可选的，也可以由前端根据 `key` 映射本地图标资源。
*   `badgeCount` 用于在图标右上角显示红点或数字，表示有新的内容或待办事项，具体哪些入口需要此功能由业务需求决定，此处仅作示例。
*   各功能入口点击后的具体页面和数据由对应模块的接口提供，本接口仅负责聚合“个人中心”首页所需的数据。

**4. Mermaid 图表描述**

该图片为UI设计稿，展示了页面的静态结构和元素布局，不适合使用Mermaid中的流程图、时序图、类图、ER图、甘特图或饼图进行直接描述。它主要表达的是信息架构和功能入口的组织方式。

**5. 总结限制**

本分析严格基于图片中可见的元素和文本内容进行，未包含图片外的信息或进行功能性的深度推测。所有功能模块和数据需求的描述均源自对图片内容的直接解读。

【============== 图片解析 END ==============】



- 预览功能：提供预览功能，用户可以点击「更新预览」数秒后，预览编辑后的板书效果（静态图，需要标记出标题/正文；高亮、圈画、划线在板书上的效果）。
- 提交按钮：
- 在识别到用户有修改后，提交按钮亮起
- 用户编辑完成后可以点击提交，即将本地结果同步到服务端
![in_table_image_UXZYbtDfdooC1Yxc2HtcnVGhn5f]

###### 图片分析
【============== 图片解析 BEGIN ==============】

**1. 图片类型与核心价值分析**

*   **图片类型:** 用户界面(UI)设计图/原型图。
*   **核心内容:** 展示了互联网教育产品中“错题本”功能的单个错题详情查看界面。
*   **关键元素与层级结构:**
    *   **导航栏:** 位于顶部，包含返回、标题("错题")和编辑功能入口。
    *   **错题信息区:**
        *   **来源信息:** 指明错题的出处（如：科目、测评类型、年级、册别、具体卷名）。
        *   **题目基础信息:** 题号、题型（单选题）。
        *   **题干:** 完整的题目文字描述。
        *   **选项列表:** 展示题目所有选项（A, B, C）。
        *   **答案对比:** 显示正确答案和用户的作答。
        *   **题目解析:** 提供详细的解题思路和步骤说明。
    *   **操作栏:** 位于底部，提供针对此错题的后续操作（移出错题本、查看原题）。
*   **核心作用与价值:** 该界面是错题本功能的核心环节，旨在帮助用户：
    *   **回顾错误:** 清晰展示题目、用户答案与正确答案，定位错误点。
    *   **理解原因:** 通过题目解析，深入理解知识点和解题方法。
    *   **管理错题:** 提供移出错题本的功能，方便用户管理已掌握的错题。
    *   **追溯来源:** 提供查看原题的入口，便于用户回顾原始测试场景。
    *   （潜在）**编辑功能:** 可能允许用户添加笔记或修改错题标签（具体编辑内容需结合上下文判断）。

**2. 功能模块拆解**

*   **导航模块:**
    *   **返回:** 返回上一级界面（通常是错题列表）。
    *   **标题展示:** 显示当前界面名称“错题”。
    *   **编辑入口:** （推测）允许用户对该错题条目进行某些修改或添加备注。
*   **错题详情展示模块:**
    *   **来源展示:** 显示题目的完整来源路径。
    *   **题干与选项展示:** 渲染题目文本及所有选项内容。
    *   **答案对比展示:** 分别标示正确答案和用户的选择。
    *   **解析展示:** 显示官方或系统提供的题目解析内容。
*   **错题管理操作模块:**
    *   **移出错题本:** 用户将此题从错题本中移除。
    *   **查看原题:** 跳转到该题目在原始试卷或练习中的界面。

**3. 服务端数据需求**

为渲染此界面，服务端需要提供以下围绕**特定错题条目**的数据：

*   **错题基本信息:**
    *   `question_id`: 题目的唯一标识符。
    *   `error_entry_id`: 该错题记录在用户错题本中的唯一标识符（可能与 `user_id` 和 `question_id` 关联）。
    *   `source_info`: 题目来源信息，可以是结构化数据或格式化字符串，如：
        *   结构化: `{ "subject": "数学", "assessment_type": "阶段测评", "grade": "1年级", "semester": "上册", "source_name": "模拟卷二" }`
        *   格式化字符串: `"【数学 - 阶段测评】 1年级 上册 模拟卷二"`
    *   `question_number`: 题目在原卷中的编号 (e.g., "2")。
    *   `question_type`: 题目类型 (e.g., "单选题")。
    *   `question_stem`: 题干文字内容 (e.g., "与 2+7 得数相等的算式是？")。
    *   `options`: 题目选项列表，应包含标识符和内容，格式如：`[ { "key": "A", "text": "3+4" }, { "key": "B", "text": "5+4" }, { "key": "C", "text": "10-2" } ]`。
    *   `correct_answer`: 正确答案的标识符 (e.g., "B")。
*   **用户作答信息:**
    *   `user_answer`: 用户当时选择的答案标识符 (e.g., "A")。
*   **解析信息:**
    *   `analysis_content`: 题目解析的详细文本或富文本内容 (e.g., "2+7=9，因为 3+4=7，5+4=9，10-2=8，所以...")。
*   **关联操作所需信息/功能支持:**
    *   服务端需支持根据 `error_entry_id` 或 `user_id`+`question_id` **移除错题**的请求。
    *   服务端需提供用于**查看原题**所需的上下文信息（如原试卷/练习的ID `original_source_id` 和题目ID `question_id`），以便前端能请求跳转。
    *   服务端需支持**编辑错题条目**的功能，包括获取可编辑内容和保存修改（具体可编辑字段需根据“编辑”功能定义）。

**4. 图表类型转换 (Mermaid)**

该图片为 UI 设计图，不适用于 Mermaid 中的 flowchart、sequenceDiagram、classDiagram、erDiagram、gantt 或 pieChart 语法进行直接转换。

【============== 图片解析 END ==============】



- 「生成视频」按钮，在用户点击后：
- 若有前3步中存在未提交的内容，提示弹窗
- 文案：“请注意，存在：尚未提交的修改。生成的视频将不包含仅在本地修改、尚未提交的内容，是否继续？“
- 按钮：“继续生成”--按服务器存的生成，“取消”--中断生成
- 提交“逐字稿、朗读稿、板书”中的内容到服务端，并重新生产视频
- 视频预览模块置灰色，变为：loading状态
- 更新时间处展示未文案：「新视频生成中」，直至完成加载，变为更新时间的展示
- button高亮，文案变为：「正在重新生成视频」
- loading过程中，显示“取消生成”，点击后弹窗显示二次确认弹窗：“确定取消生成吗？内容将回退到之前版本。”
- 点“确定”，视频重置到之前版本，结束loading；
- 点“返回”，关闭弹窗，继续loading。
- 左侧Part状态icon变化为loading中，直至完成加载，变为“！”状态
- 这个过程预计需要20分钟
![in_table_image_IJjeb01EnoDGgCxlekjcG98cnxY]

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理，我们分析这份需求文档中的图片，该图片是一张移动端App的 **UI界面截图**，具体展示了用户的“学习数据”列表页面。

**1. 图片关键元素、结构与价值分析**

*   **核心功能:** 该界面旨在向用户（可能是学生或家长）集中展示不同学科或课程的学习报告入口。
*   **关键元素:**
    *   页面标题：“学习数据”。
    *   报告列表：纵向排列的多个报告条目。
    *   报告条目：每个条目代表一份特定学科/课程的最新学习报告。
*   **层级结构:**
    *   **顶层:** 学习数据页面 (整体视图)。
    *   **次层:** 报告列表区域 (包含多个报告条目)。
    *   **底层:** 单个报告条目 (包含学科名称、生成时间、状态标签、导航指示)。
*   **元素关联:**
    *   页面标题明确了页面的主题。
    *   报告列表是页面的主体内容区域。
    *   每个报告条目通过学科名称区分内容，通过生成时间提供时效性，通过“最新报告”标签强调其状态，通过右侧箭头指示可点击进入详情。
*   **核心作用与价值:**
    *   **用户价值:** 提供了一个便捷的入口，让用户快速概览并访问各个学科/课程的学习情况报告，了解学习进展。
    *   **产品价值:** 作为学习反馈的重要环节，增强了产品的教学服务闭环，提升用户粘性与满意度。
    *   **技术价值:** 定义了学习报告列表的数据展示规范，为前端渲染和后端数据接口设计提供了依据。

**2. 功能模块拆解**

*   **学习数据列表 (Learning Data List):**
    *   **功能概述:** 显示用户关联的所有学科/课程的最新学习报告摘要列表。
*   **报告条目 (Report Item):**
    *   **功能概述:** 单条报告的摘要信息展示。
    *   `学科/课程名称`: 显示报告对应的具体学科或课程名称 (如: "数学", "语文", "英语L5")。
    *   `报告生成时间`: 显示该份报告的具体生成日期和时间 (如: "2024.07.25 10:50 生成")。
    *   `最新报告标识`: 标记此报告是否为该学科/课程的最新一份 (如: 显示 "最新报告" 标签)。
    *   `报告详情入口`: 用户点击条目可跳转到该报告的详细内容页面 (通过右侧箭头图标暗示交互)。

**3. 服务端数据需求**

为渲染此列表页面，服务端需要提供一个接口，返回一个包含多个报告对象的数组。每个报告对象需包含以下数据内容：

```json
[
  {
    "report_id": "unique_report_identifier_1", // 报告唯一标识符
    "subject_id": "math_01", // 学科或课程的唯一标识符（可选，但推荐）
    "subject_name": "数学", // 学科或课程的显示名称
    "generation_time": 1721875800, // 报告生成的时间戳 (例如 Unix Timestamp) 或标准时间字符串 (如 "2024-07-25T10:50:00Z")
    "is_latest": true // 布尔值，表示是否为该学科/课程的最新报告
    // 可能还包含跳转详情页所需的参数或URL，如 detail_params: {...} 或 detail_url: "..."
  },
  {
    "report_id": "unique_report_identifier_2",
    "subject_id": "chinese_01",
    "subject_name": "语文",
    "generation_time": 1721875740, // "2024-07-25T10:49:00Z"
    "is_latest": true
  },
  {
    "report_id": "unique_report_identifier_3",
    "subject_id": "spoken_english_01",
    "subject_name": "口语",
    "generation_time": 1721875680, // "2024-07-25T10:48:00Z"
    "is_latest": true
  },
  {
    "report_id": "unique_report_identifier_4",
    "subject_id": "english_l5_01",
    "subject_name": "英语L5",
    "generation_time": 1721875620, // "2024-07-25T10:47:00Z"
    "is_latest": true
   },
   {
    "report_id": "unique_report_identifier_5",
    "subject_id": "math_l4_01",
    "subject_name": "数学L4",
    "generation_time": 1721875560, // "2024-07-25T10:46:00Z"
    "is_latest": true
   }
  // ... more report objects
]
```

**关键数据点说明:**

*   **列表结构:** 服务端返回一个数组 (List)。
*   **对象结构:** 数组中的每个元素是一个对象 (Object)，代表一个报告条目。
*   **字段:**
    *   `report_id`: 用于唯一标识报告，可能用于后续请求详情。
    *   `subject_name`: 用于前端显示学科/课程名。
    *   `generation_time`: 用于前端显示报告生成时间，需注意时间格式或时间戳的统一约定。
    *   `is_latest`: 用于前端判断是否显示“最新报告”标签。

**4. 图表类型分析**

该图片为 **UI界面截图**，展示了数据列表，并非流程图、时序图、类图、ER图、甘特图或饼图等适合使用 Mermaid 语法描述的图表类型。因此，不适用 Mermaid 进行转换。

【============== 图片解析 END ==============】



- 用户可以保存整个Part内容
- 交互设计：
- 保存完整段落：
- 点击后整个part状态修改为“已完成”，显示✅，页面除了可预览视频播放以外，其他模块设置为不可编辑状态
- 点击后button高亮，文案变为：「重新修改」
- 重新修改：
- 点击后整个part状态修改为“进行中”，显示！，页面所有模块转为编辑状态
- 点击后button高亮，文案变为：「保存完整段落」
![in_table_image_B6wZbGDxeoh8JjxUsUOcAWxdnlb]

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网教育产品经理，我们来解析一下这份需求文档中的UI界面截图。

1.  **图片类型与核心价值解析**
    *   **图片类型**: UI界面截图。
    *   **核心元素**:
        *   **页面标题**: "待学习"，明确了页面的核心功能定位。
        *   **Tab导航**: "待学习"（当前选中）、"学习记录"，提供不同学习状态内容的切换入口。
        *   **学习统计**: 展示用户的关键学习指标（累计学习天数、今日专注时长、累计专注时长），旨在激励用户和展示学习成果。
        *   **专注模式入口**: 一个视觉引导区域（包含图片和文字“开启学习模式”、“开启计时，找到适合你的学习节奏”），引导用户进入可能带有计时器的沉浸式学习环境。
        *   **待学习课程列表**: 以卡片形式展示用户需要学习的课程。
            *   **课程卡片**: 每个卡片包含课程标题、课程总节数、学习进度（百分比）、授课教师信息（头像、姓名）、学习状态标识（如“学习中”）以及行动点（“开始学习”按钮）。
    *   **元素间关联与层级**:
        *   顶层是页面标题和Tab导航，定义了页面的主旨和分类视图。
        *   Tab导航下方是全局的学习统计数据，为用户提供整体进度反馈。
        *   统计数据下方是专注模式的入口，提供一种特定的学习方式。
        *   最下方是核心内容区——待学习课程列表，每个课程卡片是一个独立的学习单元入口。
        *   课程卡片内部元素（标题、进度、教师、按钮）共同构成对一个待学习课程的完整描述和操作入口。
    *   **核心作用与价值**: 此界面是用户在学习平台上的核心落地页之一，承载着引导用户开始或继续学习任务的关键作用。它通过聚合待学课程、展示学习数据、提供专注学习入口，帮助用户管理学习计划、追踪进度并保持学习动力，是提升用户活跃度和完成率的重要环节。

2.  **功能模块拆解**

    *   **Tab导航模块**:
        *   **功能**: 提供"待学习"和"学习记录"两个视图的切换功能。
    *   **学习统计模块**:
        *   **功能**: 显示用户的累计学习天数、今日专注分钟数、累计专注小时数和分钟数。
    *   **专注模式引导模块**:
        *   **功能**: 提示并引导用户启动“学习模式”，可能关联计时或防打扰功能。
    *   **待学习课程列表模块**:
        *   **功能**: 以列表形式展示用户已报名/需参与但尚未完成的课程。
    *   **课程信息卡片模块** (列表中的单项):
        *   **功能**: 展示单个待学习课程的核心信息，包括：课程名称、总节数、已学进度百分比、授课教师头像和名称、当前学习状态（如图中的“学习中”标识），并提供“开始学习”的直接操作入口。

3.  **服务端数据需求**

    为了渲染如上图所示的“待学习”界面，服务端需要提供以下数据结构和内容：

    *   **学习统计数据**:
        *   `total_learning_days`: 数值型，累计学习天数 (e.g., `0`)。
        *   `today_focus_minutes`: 数值型，今日专注分钟数 (e.g., `0`)。
        *   `total_focus_minutes_cumulative`: 数值型，累计专注总分钟数 (e.g., `0`)。 *（注：服务端提供总分钟数，客户端可自行换算为小时和分钟进行展示，如`0 小时 0 分钟`）*。
    *   **待学习课程列表数据**: (一个数组，每个元素代表一个课程卡片)
        *   `pending_courses`: Array<Object>
            *   每个Object结构如下：
                *   `course_id`: 字符串或数值型，课程的唯一标识符。
                *   `course_title`: 字符串，课程的完整标题 (e.g., `"【初级】小学数学精讲..."`)。
                *   `total_sections`: 数值型，课程包含的总节数 (e.g., `34`)。
                *   `learned_progress_percentage`: 数值型 (0-100)，已学习进度百分比 (e.g., `0`)。
                *   `teacher_avatar_url`: 字符串，授课教师的头像图片URL。
                *   `teacher_name`: 字符串，授课教师的姓名 (e.g., `"王老师"`)。
                *   `course_status_indicator`: 字符串或枚举值，用于前端展示课程状态的标识 (e.g., "in_progress" 或直接对应显示的文本 "学习中"，具体由前后端约定)。 *（注：此状态应基于课程进度或其他业务逻辑判断，如图中`0%`进度但显示“学习中”）*
                *   `next_action`: 字符串或枚举值，指示下一个可执行的操作类型 (e.g., "start_learning", "resume_learning")，用于前端决定按钮的逻辑（如图中按钮为“开始学习”）。

4.  **Mermaid 图表**

    此图片为UI界面截图，不适用于Flowchart、SequenceDiagram、ClassDiagram、ERDiagram、Gantt或PieChart等Mermaid图表类型进行描述。

5.  **内容限制声明**

    本次分析严格基于图片所示内容进行，未包含图片未展示的功能或数据。例如，“学习记录”Tab下的内容、点击“开启学习模式”后的具体流程、课程详情页内容等均未涉及。

【============== 图片解析 END ==============】



![in_table_image_LNm8bv6ZTo94PrxsHuJcDMH3n7f]

###### 图片分析
【============== 图片解析 BEGIN ==============】

### 1. 图片类型与核心价值解析

*   **图片类型:** 用户界面（UI）截图/设计稿。
*   **核心业务场景:** 该界面属于互联网教育产品中的“学习报告”或“学习分析”功能模块，具体展示的是用户的“学习统计”数据。
*   **关键元素与层级结构:**
    1.  **用户标识层:**
        *   用户头像与昵称 ("小企鹅同学")：明确统计数据归属的用户。
    2.  **整体学习成就层:**
        *   累计学习时长 ("累计学习时长 120 分钟")：展示用户总体的学习投入量。
        *   用户排名/对比 ("已超过 80% 的同学")：提供用户在群体中的相对位置，增强反馈与激励。
    3.  **分时段统计层:**
        *   **时间维度切换:** "周"、"月" 标签页：允许用户选择查看不同时间粒度（周/月）的统计数据 (当前选中 "周")。
        *   **当前时段展示:** 日期范围 ("11月25日-12月1日")：清晰告知用户当前图表及数据所覆盖的具体时间段。
        *   **可视化图表:** 柱状图：
            *   X轴：日期 (e.g., "11.25" 到 "12.01")。
            *   Y轴：学习时长 (单位推测为分钟)。
            *   柱体：代表每日的学习时长。
            *   作用：直观展示所选时间段内每日学习时长的分布和趋势。
        *   **时段数据总结:**
            *   总学习时长 ("总学习 60 分钟")：所选时间段内的学习总时长。
            *   日均学习时长 ("日均学习 30 分钟")：所选时间段内平均每天的学习时长。
            *   累计学习天数 ("累计学习 4 天")：所选时间段内有学习记录的总天数。
*   **核心作用与价值:**
    *   **用户价值:** 向用户清晰反馈其学习投入情况（总量、分布、趋势），并通过与他人对比提供参照，帮助用户了解学习效果，调整学习策略，提升学习动力。
    *   **产品价值:** 作为学习闭环中的关键反馈环节，提升用户粘性；收集的学习数据可用于后续的个性化推荐、学习路径规划等。

### 2. 功能模块拆解

*   **用户信息展示模块:**
    *   功能概述: 显示当前查看报告用户的基本信息（头像、昵称）。
*   **整体学习概览模块:**
    *   功能概述: 展示用户自使用产品以来的累计学习总时长，并提供一个基于百分比的与其他用户的对比排名。
*   **学习数据时段切换模块:**
    *   功能概述: 提供按“周”或按“月”切换下方详细统计数据时间范围的功能。
*   **时段学习详情展示模块:**
    *   功能概述: 根据所选时段（周/月），动态展示该时段的起止日期、每日学习时长柱状图以及该时段的总学习时长、日均学习时长、累计学习天数等核心摘要数据。

### 3. 服务端数据接口需求（推测）

为渲染此界面，服务端需提供以下数据：

1.  **获取用户整体学习统计数据**
    *   **功能:** 提供用户的累计学习数据和排名信息。
    *   **返回数据内容:**
        *   `userName`: 字符串 (用户昵称，如 "小企鹅同学")。
        *   `userAvatar`: 字符串 (用户头像URL)。
        *   `totalAccumulatedDuration`: 数字 (累计学习总时长，单位：分钟，如 120)。
        *   `peerComparisonPercentage`: 数字 (超过的同学百分比，如 80)。

2.  **获取指定时段学习统计数据**
    *   **功能:** 根据用户选择的时间维度（周/月）和可能的指定日期（若支持历史数据查看），返回该时段的详细学习统计。
    *   **输入参数（可能）:** `periodType` (如 'week', 'month'), `targetDate` (用于确定是哪一周/月，若不传则默认为当前周/月)。
    *   **返回数据内容:**
        *   `periodStartDate`: 字符串/日期格式 (时段开始日期，如 "11月25日" 或 "2023-11-25")。
        *   `periodEndDate`: 字符串/日期格式 (时段结束日期，如 "12月1日" 或 "2023-12-01")。
        *   `dailyStats`: 数组 (包含该时段内每一天的数据)。数组中每个元素为对象，包含：
            *   `date`: 字符串/日期格式 (具体日期，如 "11.25" 或 "2023-11-25")。
            *   `duration`: 数字 (当日学习时长，单位：分钟，如柱状图所示，无学习则为0)。
        *   `periodTotalDuration`: 数字 (该时段总学习时长，单位：分钟，如 60)。
        *   `periodAverageDuration`: 数字 (该时段日均学习时长，单位：分钟，如 30)。
        *   `periodStudyDays`: 数字 (该时段累计学习天数，如 4)。

### 4. 图表类型表示 (Mermaid)

该图片核心为UI展示，并非标准流程图、时序图等。其包含的柱状图是数据可视化的一种形式，Mermaid本身不适合直接复现这种复杂的UI内嵌图表。因此，此处不使用Mermaid进行描述。

【============== 图片解析 END ==============】



#### 5.2复制课程页编辑

| 序号 | 模块 | 功能名称 | 功能概述 | 图 | 优先级 |
| --- | --- | --- | --- | --- | --- |
| 1 | 逐字稿工具:复制课程页 | 复制课程页 | URL固定：xxxx.copy-lesson.html交互： | ![in_table_image_AkCabYOFRodDkbxcOfNcS1egnSc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746498540924.png) | P0 |

- 在内网状态下可以访问
- 复制自URL：输入之前已存在的URL地址
- 课程名称：输入重命名的课程名称
- 点击“生成新课程”后：
- 在浏览器新开一个页面，该新URL就是新课地址；
- 新课名称：用户刚刚输入的课程名称
- 在这个地址编辑的所有内容都编辑和原始URL解藕；
- 点击“清空”后：
- 清空两个输入框内内容。
![in_table_image_AkCabYOFRodDkbxcOfNcS1egnSc]

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网产品经理，我们来解析一下这份需求文档中的UI示意图。

1.  **图片类型与核心价值分析**
    *   **图片类型:** 这是一张移动端应用的 **UI界面示意图 (Mockup/Wireframe)**。
    *   **核心业务场景:** 展示用户的“学习报告”列表，属于互联网教育产品中常见的学习成果反馈与追踪功能。
    *   **关键元素与结构:**
        *   **顶层:** 页面标题栏，明确标识页面功能为“学习报告”。
        *   **主体:** 报告列表区域，以卡片形式纵向排列展示多条学习报告记录。
        *   **列表项 (卡片):** 每个卡片代表一份独立的学习报告，包含关键摘要信息，并作为入口引导用户查看详情。
    *   **元素间关联:** 标题栏定义页面上下文。主体区域容纳列表项。每个列表项通过包含报告标题、时间、状态和得分等摘要信息，为用户提供快速概览，并通过可点击（由右侧箭头暗示）的设计引导用户深入查看详细报告内容。
    *   **核心作用与价值:** 此界面为用户（可能是学生或家长）提供了一个集中访问和管理学习报告的入口。其价值在于：
        *   **信息聚合:** 将分散的报告集中展示，方便查找。
        *   **状态提示:** 通过“未读”标记，提醒用户关注新的反馈。
        *   **快速概览:** 提供核心信息（如综合得分），让用户快速了解学习效果。
        *   **学习追踪:** 支持用户回顾历史学习表现，是形成学习闭环、进行效果评估的重要环节。

2.  **功能模块拆解**
    *   **页面标题模块:**
        *   **功能概述:** 显示当前页面的名称“学习报告”，帮助用户定位。
    *   **学习报告列表模块:**
        *   **功能概述:** 负责展示用户的学习报告条目。如果报告数量多，可能隐含分页加载或无限滚动的功能（图中未明确展示）。
    *   **单个学习报告卡片模块:**
        *   **功能概述:** 展示单份学习报告的摘要信息，并作为跳转至报告详情页的入口。包含以下子模块：
            *   **报告标题展示:** 显示报告所属的学习内容，如“第一课时 xxxx”。
            *   **报告时间展示:** 显示报告生成或相关学习活动的时间，如“2024-01-01 18:00”。
            *   **报告状态标识:** 显示报告的阅读状态，如图中的“未读”徽标。
            *   **报告摘要展示:** 显示报告的核心结论或关键指标，如图中的“综合得分90”。
            *   **导航指示:** 通过右侧箭头图标，暗示该卡片可点击进入下一级页面（报告详情）。

3.  **服务端数据接口要求**
    为了渲染此“学习报告”列表页面，服务端需要提供一个接口，其返回的数据应包含一个学习报告对象的列表（数组）。每个学习报告对象至少应包含以下字段：

    *   `report_id` (String/Number): 报告的唯一标识符，用于后续可能的详情查询或状态更新。
    *   `title` (String): 报告的标题，例如“第一课时 xxxx”。
    *   `report_time` (String/Timestamp): 报告的时间戳或格式化时间字符串，例如“2024-01-01 18:00”。（建议使用标准时间戳或ISO 8601格式字符串，由前端进行最终展示格式化）。
    *   `read_status` (Enum/String/Boolean): 报告的阅读状态，例如 `unread` 或 `read`，或布尔值 `is_read: false`。用于前端展示“未读”标识。
    *   `summary` (String/Object): 报告的摘要信息。基于图示，可以是：
        *   `summary_text` (String): 直接提供组合好的文本，如 “综合得分90”。
        *   (或者更结构化) `summary_label` (String): "综合得分", `summary_value` (String/Number): "90"。结构化数据更利于前端展示和后续扩展。

    **数据组合示例 (JSON):**

    ```json
    [
      {
        "report_id": "report123",
        "title": "第一课时 xxxx",
        "report_time": "2024-01-01T18:00:00Z", // または Timestamp
        "read_status": "unread",
        "summary_text": "综合得分90"
      },
      {
        "report_id": "report124",
        "title": "第二课时 yyyyy",
        "report_time": "2024-01-02T10:00:00Z",
        "read_status": "read",
        "summary_text": "综合得分85"
      }
      // ... more report objects
    ]
    ```

4.  **Mermaid 图表描述**
    此图片为UI界面示意图，不属于流程图、时序图、类图、ER图、甘特图或饼图等适合使用Mermaid语法的图表类型。因此，不适用Mermaid进行描述。

5.  **内容约束**
    以上分析严格基于图片中可见的UI元素及其布局结构，未包含图片中未呈现的任何功能或数据。

【============== 图片解析 END ==============】



### 六、数据需求

#### 6.1 效果追踪

描述计划监控数据效果的方式及所需数据字段

#### 6.2 数据埋点

| 页面 | 模块 | 动作 | 埋点名称 | 图示（如需） |
| --- | --- | --- | --- | --- |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |
|  |  |  |  |  |

### 七、a/b实验需求

若有，则写清楚实验方案

**实验目的**

XXX

**实验对象**

限制条件：端/版本/用户

实验组策略：

对照组策略：

**实验及分流**

启动时间、启动时用户量、预计持续时间

**评价指标**

结果指标（标明预期提升值）、过程指标

**暂无**



### 附：评审记录

记录存档历次需求评审中的会议纪要，方便追踪

举例：

**20250204 - 初评纪要**

1. 会后尽快确定巩固类型本期题目类型范围@杨宇航
已确认，并补充进文档

1. 数据下发方式待服务端和客户端最终确认@服务端rd @客户端rd
确定服务端下发，客户端仅做兜底逻辑

1. ...


**20250321 - 二次评审纪要**

1. XX
1. 

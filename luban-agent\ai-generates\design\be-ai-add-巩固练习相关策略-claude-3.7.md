# 巩固练习相关策略接口和数据库表设计

## 一、接口设计

### 1. 获取巩固练习初始化信息

GET /api/v1/exercise/reinforcement/initialize

获取巩固练习的初始化信息，包括预估题量、题组列表等

#### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|knowledgeId|query|string|是|知识点ID|
|Authorization|header|string|是|用户认证token|
|organizationId|header|string|是|组织ID|
|userTypeId|header|string|是|用户类型ID|

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "estimatedQuestionCount": 10,
    "questionGroups": [
      {
        "groupId": "group_001",
        "groupName": "基础概念题",
        "groupDescription": "考察基础概念理解",
        "sortWeight": 1
      },
      {
        "groupId": "group_002",
        "groupName": "应用题",
        "groupDescription": "考察知识应用能力",
        "sortWeight": 2
      }
    ],
    "userMasteryLevel": 0.65,
    "targetMasteryLevel": 0.85,
    "knowledgeInfo": {
      "knowledgeId": "k_001",
      "knowledgeName": "二次函数",
      "difficultyCoefficient": 0.7,
      "frequencyCoefficient": 0.8
    }
  }
}
```

### 2. 获取下一题推荐

GET /api/v1/exercise/reinforcement/next-question

根据用户当前状态和策略获取下一道推荐题目

#### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|sessionId|query|string|是|练习会话ID|
|groupId|query|string|是|当前题组ID|
|lastQuestionId|query|string|否|上一题ID，首题可不传|
|isCorrect|query|boolean|否|上一题是否正确，首题可不传|
|Authorization|header|string|是|用户认证token|
|organizationId|header|string|是|组织ID|
|userTypeId|header|string|是|用户类型ID|

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "question": {
      "questionId": "q_001",
      "questionContent": "题目内容...",
      "difficultyLevel": 3,
      "difficultyCoefficient": 0.6,
      "isMandatory": true,
      "expectedDuration": 120,
      "options": [
        {
          "optionId": "opt_001",
          "optionContent": "选项A内容"
        },
        {
          "optionId": "opt_002",
          "optionContent": "选项B内容"
        }
      ]
    },
    "expectedMasteryIncrement": 0.07,
    "isLastQuestionInGroup": false,
    "nextGroupId": null
  }
}
```

### 3. 提交答题结果

POST /api/v1/exercise/reinforcement/answer

提交用户的答题结果，更新用户状态并返回答题评估

#### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string|是|用户认证token|
|organizationId|header|string|是|组织ID|
|userTypeId|header|string|是|用户类型ID|

请求体:

```json
{
  "sessionId": "session_001",
  "questionId": "q_001",
  "groupId": "group_001",
  "answer": "用户作答内容",
  "duration": 85,
  "submitTime": "2024-06-01T10:15:30Z"
}
```

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "isCorrect": true,
    "correctAnswer": "标准答案",
    "explanation": "解析内容...",
    "attentionScore": 10,
    "masteryIncrement": 0.05,
    "updatedMasteryLevel": 0.7,
    "isBreakTriggered": false,
    "breakReason": null,
    "attentionWarningLevel": 0
  }
}
```

### 4. 开始巩固练习会话

POST /api/v1/exercise/reinforcement/session/start

创建一个新的巩固练习会话

#### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string|是|用户认证token|
|organizationId|header|string|是|组织ID|
|userTypeId|header|string|是|用户类型ID|

请求体:

```json
{
  "knowledgeId": "k_001",
  "exerciseType": "reinforcement"
}
```

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "sessionId": "session_001",
    "startTime": "2024-06-01T10:00:00Z",
    "estimatedQuestionCount": 10,
    "initialMasteryLevel": 0.65
  }
}
```

### 5. 结束巩固练习会话

POST /api/v1/exercise/reinforcement/session/end

结束当前巩固练习会话，生成练习报告

#### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string|是|用户认证token|
|organizationId|header|string|是|组织ID|
|userTypeId|header|string|是|用户类型ID|

请求体:

```json
{
  "sessionId": "session_001",
  "endReason": "completed" // completed, interrupted, breakTriggered
}
```

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "sessionSummary": {
      "sessionId": "session_001",
      "knowledgeId": "k_001",
      "startTime": "2024-06-01T10:00:00Z",
      "endTime": "2024-06-01T10:30:00Z",
      "duration": 1800,
      "questionCount": 8,
      "correctCount": 6,
      "accuracy": 0.75,
      "initialMasteryLevel": 0.65,
      "finalMasteryLevel": 0.75,
      "isRetryRecommended": true
    },
    "groupPerformance": [
      {
        "groupId": "group_001",
        "groupName": "基础概念题",
        "questionCount": 5,
        "correctCount": 4,
        "accuracy": 0.8
      },
      {
        "groupId": "group_002",
        "groupName": "应用题",
        "questionCount": 3,
        "correctCount": 2,
        "accuracy": 0.67
      }
    ],
    "incorrectQuestions": [
      {
        "questionId": "q_003",
        "groupId": "group_001",
        "difficulty": 0.7
      },
      {
        "questionId": "q_007",
        "groupId": "group_002",
        "difficulty": 0.8
      }
    ]
  }
}
```

### 6. 获取巩固练习详细报告

GET /api/v1/exercise/reinforcement/report

获取巩固练习的详细报告信息

#### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|sessionId|query|string|是|练习会话ID|
|Authorization|header|string|是|用户认证token|
|organizationId|header|string|是|组织ID|
|userTypeId|header|string|是|用户类型ID|

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "sessionInfo": {
      "sessionId": "session_001",
      "knowledgeId": "k_001",
      "knowledgeName": "二次函数",
      "startTime": "2024-06-01T10:00:00Z",
      "endTime": "2024-06-01T10:30:00Z",
      "exerciseType": "reinforcement",
      "status": "completed",
      "breakReason": null
    },
    "performance": {
      "questionCount": 8,
      "correctCount": 6,
      "accuracy": 0.75,
      "averageDuration": 90,
      "initialMasteryLevel": 0.65,
      "finalMasteryLevel": 0.75,
      "masteryGain": 0.1
    },
    "attentionPerformance": {
      "initialAttentionScore": 10,
      "lowestAttentionScore": 7,
      "warningCount": 1
    },
    "questionDetails": [
      {
        "questionId": "q_001",
        "groupId": "group_001",
        "groupName": "基础概念题",
        "difficulty": 0.5,
        "isCorrect": true,
        "duration": 85,
        "expectedDuration": 120,
        "masteryIncrement": 0.05
      }
    ],
    "isRetryRecommended": true,
    "retryRecommendationReason": "掌握度未达到目标水平，建议再练一次"
  }
}
```

### 7. 开始再练一次会话

POST /api/v1/exercise/retry/session/start

创建一个再练一次练习会话

#### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string|是|用户认证token|
|organizationId|header|string|是|组织ID|
|userTypeId|header|string|是|用户类型ID|

请求体:

```json
{
  "originalSessionId": "session_001",
  "knowledgeId": "k_001"
}
```

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "sessionId": "session_002",
    "startTime": "2024-06-01T11:00:00Z",
    "estimatedQuestionCount": 8,
    "initialMasteryLevel": 0.75,
    "incorrectQuestionCount": 2
  }
}
```

### 8. 获取再练一次下一题推荐

GET /api/v1/exercise/retry/next-question

根据用户当前状态和再练一次策略获取下一道推荐题目

#### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|sessionId|query|string|是|练习会话ID|
|groupId|query|string|是|当前题组ID|
|lastQuestionId|query|string|否|上一题ID，首题可不传|
|isCorrect|query|boolean|否|上一题是否正确，首题可不传|
|Authorization|header|string|是|用户认证token|
|organizationId|header|string|是|组织ID|
|userTypeId|header|string|是|用户类型ID|

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "question": {
      "questionId": "q_003",
      "questionContent": "题目内容...",
      "difficultyLevel": 3,
      "difficultyCoefficient": 0.7,
      "isMandatory": false,
      "expectedDuration": 120,
      "isIncorrectQuestion": true,
      "options": [
        {
          "optionId": "opt_001",
          "optionContent": "选项A内容"
        },
        {
          "optionId": "opt_002",
          "optionContent": "选项B内容"
        }
      ]
    },
    "expectedMasteryIncrement": 0.08,
    "isLastQuestionInGroup": false,
    "nextGroupId": null
  }
}
```

### 9. 检查专注度状态

GET /api/v1/exercise/attention/status

获取用户当前专注度状态

#### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|sessionId|query|string|是|练习会话ID|
|Authorization|header|string|是|用户认证token|
|organizationId|header|string|是|组织ID|
|userTypeId|header|string|是|用户类型ID|

#### 返回结果

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "attentionScore": 7,
    "warningLevel": 1,
    "warningMessage": "你似乎不太专注，请继续认真答题",
    "continuousIncorrectCount": 2,
    "isBreakTriggered": false
  }
}
```

## 二、PostgreSQL 数据库表设计

### 1. 练习会话表（exercise_sessions）

```sql
CREATE TABLE exercise_sessions (
  id BIGSERIAL PRIMARY KEY,
  session_id VARCHAR(64) UNIQUE NOT NULL,
  user_id BIGINT NOT NULL,
  organization_id BIGINT NOT NULL,
  knowledge_id VARCHAR(64) NOT NULL,
  exercise_type VARCHAR(20) NOT NULL, -- 'reinforcement', 'retry'
  original_session_id VARCHAR(64), -- 对于再练一次，关联原会话
  status VARCHAR(20) NOT NULL DEFAULT 'in_progress', -- 'in_progress', 'completed', 'interrupted', 'break_triggered'
  break_reason VARCHAR(50), -- 如果触发熔断，记录原因
  start_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  end_time TIMESTAMPTZ,
  estimated_question_count INTEGER NOT NULL,
  initial_mastery_level NUMERIC(5,4) NOT NULL,
  final_mastery_level NUMERIC(5,4),
  initial_attention_score INTEGER NOT NULL DEFAULT 10,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
-- 索引：按用户和组织查询
CREATE INDEX idx_exercise_sessions_user_id ON exercise_sessions(user_id);
CREATE INDEX idx_exercise_sessions_organization_id ON exercise_sessions(organization_id);
-- 索引：按知识点查询
CREATE INDEX idx_exercise_sessions_knowledge_id ON exercise_sessions(knowledge_id);
-- 索引：按会话ID查询
CREATE INDEX idx_exercise_sessions_session_id ON exercise_sessions(session_id);
```

### 2. 答题记录表（answer_records）

```sql
CREATE TABLE answer_records (
  id BIGSERIAL PRIMARY KEY,
  session_id VARCHAR(64) NOT NULL,
  user_id BIGINT NOT NULL,
  question_id VARCHAR(64) NOT NULL,
  group_id VARCHAR(64) NOT NULL,
  is_correct BOOLEAN NOT NULL,
  answer TEXT NOT NULL,
  duration INTEGER NOT NULL, -- 作答时长，单位秒
  expected_duration INTEGER NOT NULL, -- 预期作答时长，单位秒
  is_mandatory BOOLEAN NOT NULL DEFAULT FALSE, -- 是否为必做题
  difficulty_coefficient NUMERIC(5,4) NOT NULL, -- 题目难度系数
  mastery_increment NUMERIC(5,4) NOT NULL, -- 掌握度增量
  attention_score INTEGER NOT NULL, -- 答题时的专注度分值
  submit_time TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT fk_answer_records_sessions FOREIGN KEY (session_id) REFERENCES exercise_sessions(session_id)
);
-- 索引：按会话ID查询
CREATE INDEX idx_answer_records_session_id ON answer_records(session_id);
-- 索引：按用户ID查询
CREATE INDEX idx_answer_records_user_id ON answer_records(user_id);
-- 索引：按题目ID查询
CREATE INDEX idx_answer_records_question_id ON answer_records(question_id);
-- 索引：按题组ID查询
CREATE INDEX idx_answer_records_group_id ON answer_records(group_id);
```

### 3. 用户学习状态表（user_learning_states）

```sql
CREATE TABLE user_learning_states (
  id BIGSERIAL PRIMARY KEY,
  user_id BIGINT NOT NULL,
  knowledge_id VARCHAR(64) NOT NULL,
  current_mastery_level NUMERIC(5,4) NOT NULL,
  target_mastery_level NUMERIC(5,4) NOT NULL,
  continuous_correct_count INTEGER NOT NULL DEFAULT 0,
  continuous_incorrect_count INTEGER NOT NULL DEFAULT 0,
  total_question_count INTEGER NOT NULL DEFAULT 0,
  correct_question_count INTEGER NOT NULL DEFAULT 0,
  last_updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT uk_user_learning_states UNIQUE (user_id, knowledge_id)
);
-- 索引：按用户ID和知识点ID查询
CREATE INDEX idx_user_learning_states_user_knowledge ON user_learning_states(user_id, knowledge_id);
```

### 4. 专注度记录表（attention_records）

```sql
CREATE TABLE attention_records (
  id BIGSERIAL PRIMARY KEY,
  session_id VARCHAR(64) NOT NULL,
  user_id BIGINT NOT NULL,
  attention_score INTEGER NOT NULL,
  warning_level INTEGER NOT NULL DEFAULT 0, -- 0: 无警告 1: 第一次警告 2: 第二次警告 3: 熔断
  warning_message TEXT,
  trigger_reason VARCHAR(100), -- 触发原因，例如"快速作答且错误"、"连续做错3题"等
  record_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT fk_attention_records_sessions FOREIGN KEY (session_id) REFERENCES exercise_sessions(session_id)
);
-- 索引：按会话ID查询
CREATE INDEX idx_attention_records_session_id ON attention_records(session_id);
-- 索引：按用户ID查询
CREATE INDEX idx_attention_records_user_id ON attention_records(user_id);
```

### 5. 推题策略配置表（recommendation_strategy_configs）

```sql
CREATE TABLE recommendation_strategy_configs (
  id BIGSERIAL PRIMARY KEY,
  strategy_id VARCHAR(50) NOT NULL,
  strategy_name VARCHAR(100) NOT NULL,
  parameter_key VARCHAR(100) NOT NULL,
  parameter_value TEXT NOT NULL,
  parameter_type VARCHAR(20) NOT NULL, -- 'integer', 'numeric', 'boolean', 'string'
  description TEXT,
  is_active BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT uk_recommendation_strategy_configs UNIQUE (strategy_id, parameter_key)
);
-- 索引：按策略ID查询
CREATE INDEX idx_recommendation_strategy_configs_strategy_id ON recommendation_strategy_configs(strategy_id);
```

### 6. 题组记录表（question_groups）

```sql
CREATE TABLE question_groups (
  id BIGSERIAL PRIMARY KEY,
  group_id VARCHAR(64) NOT NULL,
  knowledge_id VARCHAR(64) NOT NULL,
  group_name VARCHAR(200) NOT NULL,
  group_description TEXT,
  sort_weight INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT uk_question_groups UNIQUE (group_id, knowledge_id)
);
-- 索引：按知识点ID查询
CREATE INDEX idx_question_groups_knowledge_id ON question_groups(knowledge_id);
```

### 7. 练习熔断记录表（exercise_break_records）

```sql
CREATE TABLE exercise_break_records (
  id BIGSERIAL PRIMARY KEY,
  session_id VARCHAR(64) NOT NULL,
  user_id BIGINT NOT NULL,
  break_reason VARCHAR(50) NOT NULL, -- 'attention_low', 'continuous_incorrect', 'no_suitable_questions'
  break_detail TEXT,
  attention_score INTEGER,
  continuous_incorrect_count INTEGER,
  break_time TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  CONSTRAINT fk_exercise_break_records_sessions FOREIGN KEY (session_id) REFERENCES exercise_sessions(session_id)
);
-- 索引：按会话ID查询
CREATE INDEX idx_exercise_break_records_session_id ON exercise_break_records(session_id);
-- 索引：按用户ID查询
CREATE INDEX idx_exercise_break_records_user_id ON exercise_break_records(user_id);
-- 索引：按熔断原因查询
CREATE INDEX idx_exercise_break_records_reason ON exercise_break_records(break_reason);
```

## 三、ER 图设计

```mermaid
erDiagram
  EXERCISE_SESSIONS ||--o{ ANSWER_RECORDS : contains
  EXERCISE_SESSIONS ||--o{ ATTENTION_RECORDS : tracks
  EXERCISE_SESSIONS ||--o{ EXERCISE_BREAK_RECORDS : may_have
  EXERCISE_SESSIONS }o--|| USER_LEARNING_STATES : relates_to
  QUESTION_GROUPS ||--o{ ANSWER_RECORDS : categorizes
  
  EXERCISE_SESSIONS {
    BIGSERIAL id PK
    VARCHAR session_id UK
    BIGINT user_id
    BIGINT organization_id
    VARCHAR knowledge_id
    VARCHAR exercise_type
    VARCHAR original_session_id
    VARCHAR status
    VARCHAR break_reason
    TIMESTAMPTZ start_time
    TIMESTAMPTZ end_time
    INTEGER estimated_question_count
    NUMERIC initial_mastery_level
    NUMERIC final_mastery_level
    INTEGER initial_attention_score
  }
  
  ANSWER_RECORDS {
    BIGSERIAL id PK
    VARCHAR session_id FK
    BIGINT user_id
    VARCHAR question_id
    VARCHAR group_id FK
    BOOLEAN is_correct
    TEXT answer
    INTEGER duration
    INTEGER expected_duration
    BOOLEAN is_mandatory
    NUMERIC difficulty_coefficient
    NUMERIC mastery_increment
    INTEGER attention_score
    TIMESTAMPTZ submit_time
  }
  
  USER_LEARNING_STATES {
    BIGSERIAL id PK
    BIGINT user_id
    VARCHAR knowledge_id
    NUMERIC current_mastery_level
    NUMERIC target_mastery_level
    INTEGER continuous_correct_count
    INTEGER continuous_incorrect_count
    INTEGER total_question_count
    INTEGER correct_question_count
    TIMESTAMPTZ last_updated_at
  }
  
  ATTENTION_RECORDS {
    BIGSERIAL id PK
    VARCHAR session_id FK
    BIGINT user_id
    INTEGER attention_score
    INTEGER warning_level
    TEXT warning_message
    VARCHAR trigger_reason
    TIMESTAMPTZ record_time
  }
  
  RECOMMENDATION_STRATEGY_CONFIGS {
    BIGSERIAL id PK
    VARCHAR strategy_id
    VARCHAR strategy_name
    VARCHAR parameter_key
    TEXT parameter_value
    VARCHAR parameter_type
    TEXT description
    BOOLEAN is_active
  }
  
  QUESTION_GROUPS {
    BIGSERIAL id PK
    VARCHAR group_id
    VARCHAR knowledge_id
    VARCHAR group_name
    TEXT group_description
    INTEGER sort_weight
  }
  
  EXERCISE_BREAK_RECORDS {
    BIGSERIAL id PK
    VARCHAR session_id FK
    BIGINT user_id
    VARCHAR break_reason
    TEXT break_detail
    INTEGER attention_score
    INTEGER continuous_incorrect_count
    TIMESTAMPTZ break_time
  }
```

## 四、参考资料

1. be-ai-td-巩固练习相关策略-claude-3.7.md
2. be-ai-prd-巩固练习相关策略-V1.0-claude-3.7.md
3. ai-rules/backend/project-rules/golang/rules-tech-stack.md 
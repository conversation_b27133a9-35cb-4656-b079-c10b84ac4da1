**服务端架构设计文档 - 研发Agent探索：PRD文档格式化调研服务**

## 版本信息

| 版本号 | 日期 | 作者 | 变更说明 |
| --- | --- | --- | --- |
| V1.0 | 2024-06-25 | AI需求分析师 | 初版设计 |

**1. 引言 (Introduction)**

*   **1.1. 文档目的 (Purpose of Document)**
    *   为"研发Agent探索 - PRD文档格式化调研服务"提供清晰的Golang服务端架构蓝图，指导核心模块/服务的设计与开发，确保系统满足将PRD文档转换为AI友好格式的关键质量属性，并支持在Kubernetes环境中高效运行和扩展。
*   **1.2. 系统概述 (System Overview)**
    *   本系统是一个自动化的PRD文档格式化处理平台，核心目标是实现飞书及其他格式文档的自动化提取、转换和结构化，以便AI能够正确理解PRD内容，为后续的测试用例生成、技术架构设计等提供基础。系统主要交互方包括研发人员、PRD文档源(飞书等)、大模型AI服务、以及后续使用结构化PRD的自动化测试系统。
*   **1.3. 设计目标与核心原则 (Design Goals and Core Principles)**
    *   高可用性 (99.9%)：作为研发流程的重要环节，需保证服务稳定可用。
    *   可扩展性：支持未来增加对更多文档格式的解析，以及处理更复杂的文档结构。
    *   高性能：文档处理操作P99延迟 < 10s，图片解析P99延迟 < 30s。
    *   可维护性：新成员2周内可贡献代码，核心模块有清晰文档。
    *   可测试性：核心模块单元测试覆盖率 > 80%。
    *   安全性：确保文档内容的安全传输和存储，以及对大模型API密钥的安全管理。
    *   设计原则：SOLID (特别是单一职责和依赖倒置)、高内聚低耦合、KISS (避免过度设计)、YAGNI (不引入当前不需要的功能)。
*   **1.4. 范围 (Scope)**
    *   本次架构设计覆盖：文档管理与存储服务、文档结构化处理服务、图片解析服务、与大模型交互的服务、Web界面服务。
    *   不覆盖范围：UI/前端具体实现细节、具体的数据库表结构设计、DevOps的CI/CD流程细节、具体的Kubernetes YAML配置等。
*   **1.5. 术语与缩写 (Glossary and Abbreviations)**
    *   PRD：产品需求文档 (Product Requirement Document)
    *   TD：技术设计文档 (Technical Design)
    *   AI结构化：利用AI技术对文档进行语义理解和结构化处理
    *   OCR：光学字符识别 (Optical Character Recognition)
    *   MD：Markdown格式，一种轻量级标记语言
    *   LLM：大型语言模型 (Large Language Model)

**2. 需求提炼与架构驱动因素 (Key Requirements & Architectural Drivers)**

*   **2.1. 核心业务能力 (Core Business Capabilities)**
    *   **文档获取与管理能力**：支持从飞书、PDF、Word等多种源获取PRD文档，并进行统一管理。
    *   **文档格式转换能力**：将不同格式的文档转换为标准化的Markdown格式，保留原文档的结构和格式。
    *   **图片内容解析能力**：解析文档中的图片内容，特别是流程图、架构图等，提取其中的文本和语义信息。
    *   **AI结构化处理能力**：使用大模型对文档内容进行结构化处理，提取关键信息和业务逻辑。
    *   **Web交互与可视化能力**：提供便捷的Web界面，供用户上传文档、查看处理结果和调整处理参数。
    *   **与大模型安全交互能力**：在内网环境下安全地与大模型API交互，保护敏感内容和API密钥安全。
    *   **处理结果导出与分享能力**：将处理结果以多种格式导出，便于后续流程使用。
*   **2.2. 关键质量属性 (Key Quality Attributes - Non-Functional Requirements)**
    *   **2.2.1. 性能 (Performance)**：
        *   文档解析与转换操作需在10秒内完成（对于标准大小的文档）。
        *   图片解析操作需在30秒内完成（对于复杂图片可能需要更长时间）。
        *   系统需支持并发处理多个文档请求，TPS目标为10。
        *   考量点：使用goroutine池处理并发请求，优化文件处理流程以减少IO阻塞，图片处理服务可采用独立扩展策略。
    *   **2.2.2. 可用性 (Availability)**：
        *   系统目标可用性为99.9%。
        *   通过无状态服务设计确保在k8s环境下实现高可用，每个服务组件可独立水平扩展。
        *   关键服务组件需提供健康检查端点，配合k8s的liveness/readiness探针实现故障检测和恢复。
        *   对于飞书API、大模型API等外部依赖，实现合理的超时处理和降级策略。
    *   **2.2.3. 可伸缩性 (Scalability)**：
        *   系统架构需支持水平扩展，特别是图片处理和文档转换这类资源密集型操作。
        *   数据存储层需考虑文档数量和大小的增长，选择适当的存储策略。
        *   处理大型文档时需采用分块处理策略，避免单个请求消耗过多资源。
    *   **2.2.4. 可维护性 (Maintainability)**：
        *   代码组织遵循Kratos框架规范，采用清晰的包结构和命名约定。
        *   模块间职责划分明确，通过依赖注入降低耦合度。
        *   关键数据结构和接口需提供详细的Go Doc注释。
        *   支持不同文档格式和处理引擎的插件式扩展，降低添加新功能的难度。
    *   **2.2.5. 可测试性 (Testability)**：
        *   架构设计支持单元测试和集成测试，重点关注文档解析和转换逻辑。
        *   使用接口定义依赖关系，便于在测试中使用mock实现。
        *   为测试提供专用的文档样本库和预期输出，便于自动化验证。
        *   对于对接外部服务（如飞书API、大模型API）的组件，设计mock服务便于测试。
    *   **2.2.6. 安全性 (Security)**：
        *   确保文档内容的安全传输和存储，必要时考虑加密存储。
        *   大模型API密钥等敏感配置信息需安全存储，避免硬编码。
        *   Web界面需实现适当的权限控制，防止未授权访问。
        *   与大模型的交互限制在内网环境，降低安全风险。
        *   日志中避免记录敏感信息，实现合规的日志脱敏策略。
    *   **2.2.7. 成本效益 (Cost-Effectiveness)**：
        *   优化对大模型API的调用频率和数据量，降低API调用成本。
        *   针对处理完成的文档和中间结果实现合理的缓存策略，避免重复处理。
        *   系统资源使用效率优化，特别是内存密集型的图片处理操作。
    *   **2.2.8. 可观测性支持 (Support for Observability in K8s)**：
        *   实现结构化日志记录，包括操作类型、处理状态、耗时等关键信息。
        *   暴露Prometheus兼容的指标端点，监控系统性能和资源使用情况。
        *   集成OpenTelemetry SDK，实现分布式追踪，特别是文档处理的完整流程追踪。
*   **2.3. 主要约束与依赖 (Key Constraints and Dependencies)**
    *   **技术栈约束**：
        *   必须使用Go 1.22.12作为开发语言。
        *   使用Kratos 2.8.3框架构建微服务。
        *   数据持久化使用PostgreSQL17。
        *   缓存使用Redis 6.0.3。
        *   消息队列使用Kafka 3.6.3。
        *   日志服务使用项目内置日志服务。
        *   链路追踪使用Zipkin 3.4.3。
    *   **外部系统依赖**：
        *   飞书API：需要提供文档访问凭证，获取文档内容和结构信息。
        *   大模型API服务：提供文本理解和图片解析能力，系统需发送内容并接收分析结果。

**3. 宏观架构设计 (High-Level Architectural Design)**

*   **3.1. 架构风格与模式 (Architectural Style and Patterns)**
    *   本系统采用**微服务架构**与**分层架构**相结合的设计风格，并在文档处理流程中引入**事件驱动模式**。
    *   微服务架构的选择理由：
        *   **独立部署与扩展**：文档处理、图片解析等不同功能模块的资源需求和扩展特性各异，微服务架构允许它们独立扩展。
        *   **技术异构**：不同的处理模块可能需要不同的技术栈（如图片处理可能需要特定的库或资源配置），微服务架构支持这种异构性。
        *   **容错性**：单个服务的故障不会导致整个系统瘫痪，特别是在依赖外部服务（如飞书API、大模型API）时，能够更好地隔离故障。
    *   分层架构的选择理由：
        *   **关注点分离**：清晰地划分接口处理、业务逻辑、数据访问等不同职责，提高代码可维护性。
        *   **复用性**：底层服务和组件可被多个上层业务逻辑复用，如文档存储、缓存访问等。
        *   **松耦合**：通过定义清晰的层间接口，降低各层之间的耦合度，便于独立演进和测试。
    *   事件驱动模式的选择理由：
        *   **异步处理**：文档解析和图片处理可能是耗时操作，采用事件驱动模式可实现异步处理，提高系统响应速度。
        *   **流程解耦**：处理流程的各个阶段（文档转换、图片解析、AI结构化等）可通过事件触发并独立扩展。
        *   **可追踪**：完整的事件流可便于追踪整个文档处理过程，提高系统可观测性。

*   **3.2. 系统分层视图 (Layered View)**
    *   **3.2.1. 推荐的逻辑分层模型 (Recommended Logical Layering Model)**
        *   基于对PRD文档格式化调研服务的需求分析和技术栈约束，本系统采用四层架构模型，清晰分离不同关注点：

        ```mermaid
        flowchart TD
            subgraph 'PRD文档格式化调研服务系统'
                direction LR
                APILayer[接口层]
                ApplicationLayer[应用服务层]
                DomainLayer[领域服务层]
                InfrastructureLayer[基础设施层]

                APILayer --> ApplicationLayer
                ApplicationLayer --> DomainLayer
                ApplicationLayer --> InfrastructureLayer
                DomainLayer --> InfrastructureLayer

                subgraph '接口层'
                    APILayer --> WebInterface[Web界面接口]
                    APILayer --> RESTEndpoints[REST API端点]
                    APILayer --> AsyncWorkers[异步任务处理器]
                end

                subgraph '应用服务层'
                    ApplicationLayer --> DocProcessorSvc[文档处理服务]
                    ApplicationLayer --> ImageAnalysisSvc[图片分析服务]
                    ApplicationLayer --> AIStructureSvc[AI结构化服务]
                    ApplicationLayer --> DocManagementSvc[文档管理服务]
                    ApplicationLayer --> WorkflowSvc[工作流编排服务]
                end

                subgraph '领域服务层'
                    DomainLayer --> DocConverter[文档转换引擎]
                    DomainLayer --> FeishuAdapter[飞书文档适配器]
                    DomainLayer --> PDFAdapter[PDF文档适配器]
                    DomainLayer --> WordAdapter[Word文档适配器]
                    DomainLayer --> ImageAnalyzer[图片解析引擎]
                    DomainLayer --> AIAdapter[大模型交互适配器]
                    DomainLayer --> StructureAnalyzer[结构分析引擎]
                end

                subgraph '基础设施层'
                    InfrastructureLayer --> DataPersistence[数据持久化]
                    InfrastructureLayer --> CacheManager[缓存管理]
                    InfrastructureLayer --> MessageQueue[消息队列]
                    InfrastructureLayer --> ExternalAPIs[外部API客户端]
                    InfrastructureLayer --> FileStorage[文件存储]
                    InfrastructureLayer --> ObservabilityTools[可观测性工具]
                end
            end

            %% 外部系统连接
            ExternalAPIs -.-> FeishuAPI[飞书API]
            ExternalAPIs -.-> LLMAPI[大模型API]

            %% 存储服务连接
            DataPersistence -.-> PostgresDB[(PostgreSQL数据库)]
            CacheManager -.-> RedisCache[(Redis缓存)]
            MessageQueue -.-> Kafka[(Kafka消息队列)]
            FileStorage -.-> ObjectStorage[(对象存储)]

            %% 设置样式
            classDef apiStyle fill:#F9E79F,stroke:#333,stroke-width:1px;
            classDef appStyle fill:#ABEBC6,stroke:#333,stroke-width:1px;
            classDef domainStyle fill:#D7BDE2,stroke:#333,stroke-width:1px;
            classDef infraStyle fill:#AED6F1,stroke:#333,stroke-width:1px;
            classDef externalStyle fill:#F5CBA7,stroke:#333,stroke-width:1px;
            
            class APILayer,WebInterface,RESTEndpoints,AsyncWorkers apiStyle;
            class ApplicationLayer,DocProcessorSvc,ImageAnalysisSvc,AIStructureSvc,DocManagementSvc,WorkflowSvc appStyle;
            class DomainLayer,DocConverter,FeishuAdapter,PDFAdapter,WordAdapter,ImageAnalyzer,AIAdapter,StructureAnalyzer domainStyle;
            class InfrastructureLayer,DataPersistence,CacheManager,MessageQueue,ExternalAPIs,FileStorage,ObservabilityTools infraStyle;
            class FeishuAPI,LLMAPI,PostgresDB,RedisCache,Kafka,ObjectStorage externalStyle;
        ```

        *   **接口层 (API Layer)** - 负责系统与外部世界的交互：
            *   **Web界面接口**：提供用户界面的后端支持，处理UI交互请求。
            *   **REST API端点**：提供RESTful API，支持程序化集成和第三方系统对接。
            *   **异步任务处理器**：接收和处理长时间运行的任务请求，返回任务ID供后续查询。
            *   职责：请求验证、认证授权、请求路由、响应格式化、错误处理等。
            *   封装变化：前端交互方式变化、API版本变化、请求/响应格式变化。
        
        *   **应用服务层 (Application Service Layer)** - 编排业务流程，协调不同领域服务：
            *   **文档处理服务**：协调文档上传、格式转换、结构化处理等完整流程。
            *   **图片分析服务**：管理图片提取和分析流程，包括排队、处理状态跟踪等。
            *   **AI结构化服务**：协调与大模型的交互过程，包括内容准备、结果处理等。
            *   **文档管理服务**：处理文档的CRUD操作，包括版本管理、元数据维护等。
            *   **工作流编排服务**：管理复杂的文档处理工作流，支持自定义处理步骤。
            *   职责：用例实现、事务管理、业务流程编排、领域服务协调。
            *   封装变化：业务流程变化、业务规则调整、集成点变更。
        
        *   **领域服务层 (Domain Service Layer)** - 实现核心业务逻辑和领域规则：
            *   **文档转换引擎**：实现各种文档格式到Markdown的转换逻辑。
            *   **飞书文档适配器**：处理飞书特有文档格式的解析和转换逻辑。
            *   **PDF文档适配器**：处理PDF文档的解析和转换逻辑。
            *   **Word文档适配器**：处理Word文档的解析和转换逻辑。
            *   **图片解析引擎**：实现图片内容的理解和文本提取。
            *   **大模型交互适配器**：封装与大模型API的交互逻辑，处理提示词和响应解析。
            *   **结构分析引擎**：分析文档结构并提取语义信息。
            *   职责：核心领域逻辑实现、领域规则和约束、领域对象转换。
            *   封装变化：业务规则变化、文档格式变化、分析算法优化。
        
        *   **基础设施层 (Infrastructure Layer)** - 提供技术基础设施和外部系统集成：
            *   **数据持久化**：处理数据库交互，实现数据的存储和查询。
            *   **缓存管理**：管理Redis缓存，提高频繁访问数据的响应速度。
            *   **消息队列**：处理Kafka消息的发布和订阅，支持异步处理。
            *   **外部API客户端**：封装对飞书API、大模型API等外部服务的调用。
            *   **文件存储**：管理文档和图片等文件的存储和检索。
            *   **可观测性工具**：提供日志、指标、追踪等可观测性工具的集成。
            *   职责：技术基础设施集成、外部系统交互、日志监控、配置管理。
            *   封装变化：技术实现变化、外部系统接口变化、存储方案变化。

        *   **层间依赖规则**：
            *   严格遵循单向依赖原则，上层依赖下层，避免循环依赖。
            *   上层通过接口而非具体实现依赖下层，实现依赖倒置。
            *   应用服务层可以调用多个领域服务，但领域服务不应直接调用应用服务。
            *   应用服务层和领域服务层均可直接使用基础设施层的功能，但应通过接口隔离具体实现。

        *   **分层架构的合理性**：
            *   对于PRD文档格式化调研这类复杂业务，四层架构提供了清晰的职责分离和灵活性。
            *   由于需要支持多种文档格式和处理引擎，领域服务层的单独抽象有利于实现可插拔的设计。
            *   基础设施层的独立抽象使得系统可以灵活适应不同的存储、消息和外部服务集成需求。
            *   本设计在遵循SOLID、高内聚低耦合等原则的同时，避免了过度分层导致的复杂性。 
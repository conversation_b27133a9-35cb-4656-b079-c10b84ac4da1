# 产品需求文档 - 学生端巩固练习

## 1. 需求背景与目标

### 1.1 需求目标
- 通过巩固练习功能的实现，可以提升学生学习效果，增强对AI课知识点的掌握程度，将知识从"知道"转变为"会用"
- 该需求上线后，学生对巩固练习的接受度预计可以提升，巩固练习效果预计可以显著改善，最终实现学生成绩提升的目标
- 通过再练一次逻辑，保证每一个学生在完成巩固练习后都可以达到掌握知识的目标，确保产品的效果

## 2. 功能范围

### 2.1 核心功能
- **巩固练习入口**：巩固练习初始时、中途退出后、练习完成后的入口变化，支持多种状态展示和交互
  - 优先级：P0
  - 依赖关系：无
- **进入练习**：首次进入与继续练习时的转场设计与体验优化，提供流畅的练习启动体验
  - 优先级：P0
  - 依赖关系：依赖于巩固练习入口
- **练习环节**：练习中页面框架设计及作答功能模块，包含完整的作答交互体验
  - 优先级：P0
  - 依赖关系：依赖于进入练习
- **题目间转场**：包含作答后反馈、难度调整反馈等情感化交互体验
  - 优先级：P0
  - 依赖关系：依赖于练习环节
- **学习报告**：总结学生每次完整练习的学习报告，展示掌握度变化和学习成果
  - 优先级：P0
  - 依赖关系：依赖于练习环节完成
- **再练一次环节**：根据学生掌握度情况，推荐再练一次环节，确保达到掌握目标
  - 优先级：P0
  - 依赖关系：依赖于学习报告

### 2.2 辅助功能
- **题目推荐**：基于课程知识点精选高价值练习题，根据学生实时作答表现自动调整题目难度
- **情感化反馈**：针对不同的作答结果，提供差异化的情感反馈，包括正确、错误、连续正确、部分正确等多种反馈类型
- **勾画组件**：支持用户在作答环节时对题目进行勾画和标记，提供多种勾画工具和颜色选择
- **错题本组件**：自动添加错题和手动添加功能，支持错因标签管理
- **熔断机制**：练习过程中的熔断判断和处理，避免过度练习
- **题组转场**：当课程中有两个及以上的题组时的转场处理
- **不认真作答反馈**：对不认真作答行为的检测和反馈
- **难度变化反馈**：题目难度调整时的用户提示

### 2.3 非本期功能
- 积分系统的详细规则和兑换机制
- 社交功能（如排行榜、好友对战等）
- 高级数据分析和个性化学习路径推荐

## 3. 计算规则与公式

### 3.1 掌握度计算
- **掌握度变化规则**：基于学生作答表现实时计算掌握度变化
  - 参数说明：
    - 正确率：本次练习正确题目数/总题目数
    - 作答时长：每题作答时间
    - 难度系数：题目难度等级
  - 规则：
    - 取值范围：0-100%
    - 计算频率：每题作答后实时更新

### 3.2 再练一次触发规则
- **触发条件**：基于学生在巩固练习中的掌握度情况判断
  - 参数说明：
    - 最终掌握度：练习完成后的掌握度水平
    - 正确率阈值：触发再练一次的正确率临界值
    - 薄弱知识点数量：未达到掌握标准的知识点个数
  - 规则：
    - 掌握度低于设定阈值时触发推荐
    - 存在薄弱知识点时优先推荐相关题目

## 4. 用户场景与故事

### 4.1 学生首次进入巩固练习场景
作为一个学生，我希望能够在AI课学习后进行巩固练习，这样我就可以检验学习效果并增强对知识点的掌握程度。目前的痛点是缺乏有效的情感反馈、激励机制不足，如果能够提供流畅、有效、反馈友好的练习体验，对我的学习会有很大帮助。

**关键步骤：**
1. 在学科页面找到巩固练习卡片，查看预估题目数量
2. 点击【进入练习】按钮
3. 观看进入练习转场动画（IP角色+文案："开始练习：《课程名称》"）
4. 如有题组，观看题组转场介绍
5. 开始作答第一道题目

- 优先级：高
- 依赖关系：无

```mermaid
sequenceDiagram
    participant 学生
    participant 学科页面
    participant 练习系统
    participant 推题服务
    
    学生->>学科页面: 查看巩固练习卡片
    学科页面->>学生: 展示练习状态和预估题目数
    学生->>学科页面: 点击【进入练习】
    学科页面->>练习系统: 启动练习会话
    练习系统->>学生: 播放进入练习转场
    练习系统->>推题服务: 获取第一道题目
    推题服务->>练习系统: 返回题目内容
    练习系统->>学生: 展示题目作答界面
```

### 4.2 学生中途退出后继续练习场景
作为一个学生，我希望能够在中途退出练习后继续之前的进度，这样我就可以灵活安排学习时间。目前的痛点是担心进度丢失，如果能够自动保存进度并支持继续练习，对我的学习安排会有很大帮助。

**关键步骤：**
1. 在练习过程中点击退出按钮
2. 系统弹出二次确认弹窗："确定要退出练习吗？进度已保存"
3. 点击【确认退出】，系统自动保存当前进度
4. 再次进入时巩固练习卡片状态变为"练习中"
5. 点击【继续练习】按钮
6. 观看继续练习转场（文案："继续练习：《课程名称》"）
7. 从上次退出的题目继续作答

- 优先级：高
- 依赖关系：依赖于首次进入练习场景

```mermaid
sequenceDiagram
    participant 学生
    participant 练习系统
    participant 进度服务
    participant 学科页面
    
    学生->>练习系统: 点击退出按钮
    练习系统->>学生: 弹出确认弹窗
    学生->>练习系统: 确认退出
    练习系统->>进度服务: 保存当前进度
    进度服务->>练习系统: 保存成功
    练习系统->>学科页面: 返回学科页面
    学科页面->>学生: 更新练习状态为"练习中"
    学生->>学科页面: 点击【继续练习】
    学科页面->>练习系统: 恢复练习会话
    练习系统->>进度服务: 获取上次进度
    进度服务->>练习系统: 返回进度信息
    练习系统->>学生: 播放继续练习转场
    练习系统->>学生: 展示上次退出的题目
```

### 4.3 学生完成练习查看报告场景
作为一个学生，我希望能够在完成练习后查看详细的学习报告，这样我就可以了解自己的学习成果和薄弱点。目前的痛点是缺乏清晰的学习反馈，如果能够提供详细的掌握度变化、知识点分析，对我后续的学习会有很大帮助。

**关键步骤：**
1. 完成最后一道题目并提交答案
2. 系统展示作答反馈
3. 自动跳转到学习报告页面
4. 查看IP角色反馈（基于正确率展示不同角色和文案）
5. 浏览本次掌握度变化、学习时长、答题数量、正确率等数据
6. 查看题目详情和作答结果
7. 根据推荐选择【完成】或【再练一次】

- 优先级：高
- 依赖关系：依赖于练习环节完成

```mermaid
sequenceDiagram
    participant 学生
    participant 练习系统
    participant 报告服务
    participant 掌握度服务
    
    学生->>练习系统: 完成最后一题
    练习系统->>报告服务: 生成学习报告
    练习系统->>掌握度服务: 计算掌握度变化
    掌握度服务->>报告服务: 返回掌握度数据
    报告服务->>练习系统: 返回完整报告
    练习系统->>学生: 展示学习报告
    学生->>练习系统: 查看题目详情
    练习系统->>学生: 展示题目、答案、解析
    学生->>练习系统: 选择后续操作
```

### 4.4 学生进行再练一次场景
作为一个学生，我希望能够根据掌握度情况进行再练一次，这样我就可以确保达到掌握知识的目标。目前的痛点是不知道是否真正掌握了知识点，如果能够智能推荐再练一次并提供针对性题目，对我的学习效果会有很大帮助。

**关键步骤：**
1. 在学习报告页看到再练一次推荐（当掌握度未达标时强化展示）
2. 点击【再练一次】按钮
3. 进入再练一次转场（文案："开始练习：《课程名称》"）
4. 作答针对薄弱知识点的题目
5. 享受与巩固练习一致的作答体验和反馈
6. 完成后查看累加的学习报告

- 优先级：中
- 依赖关系：依赖于学习报告场景

```mermaid
sequenceDiagram
    participant 学生
    participant 报告页面
    participant 再练系统
    participant 推题服务
    participant 掌握度服务
    
    学生->>报告页面: 查看学习报告
    报告页面->>掌握度服务: 检查掌握度状态
    掌握度服务->>报告页面: 返回是否需要再练
    报告页面->>学生: 强化展示【再练一次】
    学生->>报告页面: 点击【再练一次】
    报告页面->>再练系统: 启动再练会话
    再练系统->>推题服务: 获取薄弱知识点题目
    推题服务->>再练系统: 返回针对性题目
    再练系统->>学生: 开始再练一次
```

## 5. 业务流程图

```mermaid
flowchart TD
    A[学生进入学科页面] --> B{巩固练习状态}
    B -->|未开始| C[显示【进入练习】]
    B -->|练习中| D[显示【继续练习】]
    B -->|已完成| E[显示【看报告】和【再练一次】]
    
    C --> F[进入练习转场]
    D --> F
    E --> G[查看学习报告]
    E --> H[开始再练一次]
    
    F --> I{是否有题组}
    I -->|是| J[题组转场]
    I -->|否| K[开始作答]
    J --> K
    
    K --> L[题目展示]
    L --> M[学生作答]
    M --> N{作答结果}
    
    N -->|正确| O[正确反馈]
    N -->|错误| P[错误反馈]
    N -->|部分正确| Q[部分正确反馈]
    
    O --> R{是否连续正确}
    R -->|是| S[连续正确反馈]
    R -->|否| T[检查难度调整]
    S --> T
    P --> T
    Q --> T
    
    T --> U{是否有难度变化}
    U -->|是| V[难度变化反馈]
    U -->|否| W[检查不认真作答]
    V --> W
    
    W --> X{是否不认真作答}
    X -->|是| Y[不认真作答反馈]
    X -->|否| Z[进入下一题]
    Y --> Z
    
    Z --> AA{是否还有题目}
    AA -->|是| L
    AA -->|否| BB[生成学习报告]
    
    BB --> G
    G --> CC{是否需要再练}
    CC -->|是| H
    CC -->|否| DD[完成练习]
    
    H --> F
    
    M --> EE[中途退出]
    EE --> FF[保存进度]
    FF --> GG[返回学科页面]
```

## 6. 性能与安全需求

### 6.1 性能需求
- **响应时间**：题目加载和作答提交的响应时间应不超过2秒，转场动画加载失败时应在2秒内自动跳过
- **并发用户数**：系统需能支持至少1000名学生同时进行巩固练习，且性能无明显下降
- **数据处理能力**：系统需能处理每秒100次作答提交请求，实时更新掌握度和推荐下一题
- **进度保存**：学生退出时的进度保存操作应在1秒内完成，确保数据不丢失

### 6.2 安全需求
- **用户认证**：所有练习相关接口必须经过学生身份认证，确保练习数据归属正确
- **数据保护**：学生作答数据和学习进度在传输和存储过程中必须加密，防止数据泄露
- **操作审计**：学生的关键学习行为（如开始练习、完成练习、退出练习）必须记录详细日志
- **防作弊机制**：系统应具备检测异常作答行为的能力，如过快提交、重复刷新等

## 7. 验收标准

### 7.1 功能验收
- **巩固练习入口**：
  - 使用场景：学生在学科页面查看巩固练习卡片时
  - 期望结果：根据练习状态正确显示入口文案和交互按钮，预估题目数量准确展示
- **进入练习转场**：
  - 使用场景：学生点击进入练习或继续练习时
  - 期望结果：播放2秒转场动画，显示正确的IP角色和文案，加载失败时自动跳过
- **作答反馈**：
  - 使用场景：学生提交答案后
  - 期望结果：根据作答结果展示对应的情感化反馈，连续正确时优先显示连胜反馈
- **学习报告**：
  - 使用场景：学生完成所有题目后
  - 期望结果：准确展示掌握度变化、学习时长、正确率等数据，支持查看题目详情
- **再练一次**：
  - 使用场景：学生掌握度未达标时
  - 期望结果：在报告页强化展示再练一次推荐，提供针对薄弱知识点的题目

### 7.2 性能验收
- **响应时间**：
  - 测试场景：模拟100个并发学生同时进行练习
  - 验收标准：题目加载和作答提交的P95响应时间不超过2秒
- **进度保存**：
  - 测试场景：学生在练习过程中退出
  - 验收标准：进度保存操作在1秒内完成，再次进入时能正确恢复到退出位置

### 7.3 安全验收
- **用户认证**：
  - 测试场景：未登录用户尝试访问练习接口
  - 验收标准：系统拒绝访问并返回认证失败提示
- **数据保护**：
  - 验证点：通过安全审计确认作答数据传输加密
  - 验收标准：所有敏感数据符合加密传输和存储要求

## 8. 其他需求

### 8.1 可用性需求
- **界面友好**：练习界面应简洁直观，学生无需额外培训即可完成作答操作
- **容错处理**：当发生网络异常或系统错误时，应保存学生当前进度并给出明确的错误提示
- **兼容性**：支持主流移动设备和平板设备，在iOS和Android系统上功能正常

### 8.2 维护性需求
- **配置管理**：关键反馈文案、转场时长、熔断规则等参数应支持运营人员通过管理后台调整
- **监控告警**：当练习完成率异常下降、系统响应时间超标时应触发告警
- **日志记录**：学生的练习行为、作答结果、掌握度变化等关键数据必须完整记录

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 在学习报告页提供"本节课感受如何"的反馈入口，收集学生对练习体验的评价
- 通过客服渠道收集学生对练习功能的意见和建议

### 9.2 迭代计划（高阶产品方向）
- 基于学生反馈和数据分析，持续优化情感化反馈的文案和交互体验
- 探索更多游戏化元素，如成就系统、学习徽章等，提升学习动力

## 10. 需求检查清单

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
|----------|------------|--------|--------|--------|----------|----------|------|
| 巩固练习入口状态变化 | 2.1核心功能-巩固练习入口 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 进入练习转场设计 | 2.1核心功能-进入练习 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 练习环节页面框架 | 2.1核心功能-练习环节 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 作答后反馈机制 | 2.1核心功能-题目间转场 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 学习报告展示 | 2.1核心功能-学习报告 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次推荐 | 2.1核心功能-再练一次环节 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 题目推荐策略 | 2.2辅助功能-题目推荐 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 情感化反馈文案 | 2.2辅助功能-情感化反馈 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 勾画工具功能 | 2.2辅助功能-勾画组件 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 错题本收藏 | 2.2辅助功能-错题本组件 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 熔断机制规则 | 2.2辅助功能-熔断机制 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 具体规则需策略文档补充 |
| 题组转场处理 | 2.2辅助功能-题组转场 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 不认真作答检测 | 2.2辅助功能-不认真作答反馈 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 检测规则需策略文档补充 |
| 难度调整提示 | 2.2辅助功能-难度变化反馈 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 中途退出保存 | 4.2用户场景-中途退出继续练习 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 掌握度计算展示 | 3.1计算规则-掌握度计算 | ✅ | ✅ | ✅ | ⚠️ | ✅ | 具体算法需策略文档补充 |

## 11. 附录

### 11.1 原型图
详见Figma设计稿：[学生端-1.0设计稿](https://www.figma.com/design/COdZQZZOFHZHsdI1zWX8Tm/%E5%AD%A6%E7%94%9F%E7%AB%AF-1.0?node-id=1805-6218&p=f&m=dev)

### 11.2 术语表
- **巩固练习**：在学生掌握新知识或技能后，借助有针对性的练习加深理解、强化记忆，将知识从"知道"转变为"会用"，满足的场景是AI课后的巩固
- **再练一次**：通过对学生巩固练习后掌握度和正确率的判断，推荐巩固练习再练一次，确保学生在完成全部巩固练习后达到掌握知识目标
- **熔断机制**：练习过程中根据特定规则终止练习的机制，避免学生过度练习
- **题组**：课程中的题目分组概念，支持将相关题目组织在一起进行练习
- **掌握度**：衡量学生对知识点掌握程度的量化指标，取值范围0-100%
- **情感化反馈**：根据学生作答结果提供的差异化情感反馈，包括IP角色、音效、文案等元素

### 11.3 参考资料
- [教师端_1期_备课 & 作业布置](https://wcng60ba718p.feishu.cn/wiki/G6OTwydxOi8gdmkYTKUcmQmHngd)
- [巩固练习相关策略](https://wcng60ba718p.feishu.cn/docx/JYjedBulhoHiBDx1wGScCacHnrd)
- [PRD - 内容管理平台2.0 - 选题支持题组](https://wcng60ba718p.feishu.cn/wiki/C91Gwf402i2iU5kMs1vcPnICnDc)
- [PRD - 题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)
- [Figma设计稿](https://www.figma.com/design/COdZQZZOFHZHsdI1zWX8Tm/%E5%AD%A6%E7%94%9F%E7%AB%AF-1.0?node-id=1805-6218&p=f&m=dev) 
# 数据库设计文档 - AI课中课程框架+文档组件 - V0.9

**最后更新日期**: 2025-05-23
**设计负责人**: claude-sonnet-4
**评审人**: 待填写
**状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V0.1   | 2025-05-23 | claude-sonnet-4 | 初始草稿     |

## 1. 项目概述与设计目标

### 1.1 项目背景与范围
本项目是AI驱动的在线学习平台核心组件，主要目标是提供流畅的课程学习体验，包括开场页展示、文档组件交互播放、学习进度管理和掌握度计算等功能。本次数据库设计的范围涵盖课程内容管理、学习记录管理、掌握度计算和用户行为分析等核心数据存储需求。

### 1.2 设计目标回顾
- 提供完整的数据库创建语句（如果适用）
- 提供完整的、符合规范的表创建语句（DDL）
- 清晰描述表间关系，并提供 ER 图
- 详细说明核心用户场景/用户故事所涉及的表以及其 CRUD 操作方式
- 阐述关键表的索引策略及其设计原因
- 对核心表进行数据量预估和增长趋势分析
- 明确数据生命周期管理/归档策略（如果适用）
- 记录特殊设计考量与权衡
- 总结设计如何体现对应数据库（PostgreSQL/ClickHouse）的关键规范

## 2. 数据库环境与总体说明

### 2.1 目标数据库类型
- PostgreSQL 17.x（用于核心业务数据）
- ClickHouse 23.8.16（用于日志和分析数据）

### 2.2 数据库创建语句 (如果适用)
使用现有数据库实例，目标数据库名称：
- PostgreSQL: `ai_course_db`
- ClickHouse: `ai_analytics_db`

## 3. 数据库表详细设计

### 3.1 数据库表清单

#### PostgreSQL表清单
| 表名 | 核心功能/用途简述 | 预定数据库类型 |
|------|------------------|--------------|
| `tbl_course_courses` | 存储AI课程基本信息、组件序列和配置 | PostgreSQL |
| `tbl_course_components` | 存储文档组件内容、勾画轨迹数据和播放配置 | PostgreSQL |
| `tbl_study_sessions` | 存储用户学习会话记录和状态管理 | PostgreSQL |
| `tbl_study_progress` | 存储文档组件播放进度和模式状态 | PostgreSQL |
| `tbl_study_performance` | 存储学习表现数据汇总（用时、答题数、正确率） | PostgreSQL |
| `tbl_mastery_records` | 存储用户知识点掌握度记录和目标设置 | PostgreSQL |
| `tbl_study_feedback` | 存储课程反馈和评价数据 | PostgreSQL |

#### ClickHouse表清单
| 表名 | 核心功能/用途简述 | 预定数据库类型 |
|------|------------------|--------------|
| `tbl_answer_logs` | 存储用户答题记录详细日志，用于分析和掌握度计算 | ClickHouse |
| `tbl_operation_logs` | 存储用户学习过程中的操作行为日志 | ClickHouse |

---

### 3.2 表名: `tbl_course_courses`

#### 3.2.1 表功能说明
存储AI课程的基本信息和配置数据，包括课程名称、课程信息、组件序列等静态内容。该表是整个学习系统的核心基础表，为course-center服务提供课程内容管理能力，支持开场页展示和课程结构定义。

#### 3.2.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_course_courses (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  course_name VARCHAR(255) NOT NULL,
  course_info TEXT NULL,
  component_sequence JSONB NOT NULL,
  opening_config JSONB NULL,
  ip_animation_config JSONB NULL,
  audio_config JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_course_courses IS 'AI课程基本信息表 - 存储课程名称、组件序列、开场页配置等静态内容';
COMMENT ON COLUMN tbl_course_courses.id IS '课程主键ID';
COMMENT ON COLUMN tbl_course_courses.course_name IS '课程名称';
COMMENT ON COLUMN tbl_course_courses.course_info IS '课程基本信息描述';
COMMENT ON COLUMN tbl_course_courses.component_sequence IS '课程组件序列配置，JSON格式存储组件顺序和类型';
COMMENT ON COLUMN tbl_course_courses.opening_config IS '开场页配置信息，JSON格式';
COMMENT ON COLUMN tbl_course_courses.ip_animation_config IS 'IP动效配置信息，JSON格式';
COMMENT ON COLUMN tbl_course_courses.audio_config IS '音频配置信息，JSON格式';
COMMENT ON COLUMN tbl_course_courses.status IS '课程状态 (0:禁用, 1:启用, 2:草稿)';
COMMENT ON COLUMN tbl_course_courses.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_course_courses.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_course_courses.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_course_courses.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_course_courses.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_course_courses_status ON tbl_course_courses(status) WHERE deleted_at IS NULL;
-- 支持按状态查询活跃课程
CREATE INDEX idx_course_courses_created_at ON tbl_course_courses(created_at) WHERE deleted_at IS NULL;
-- 支持按创建时间排序查询
CREATE INDEX idx_course_courses_component_sequence_gin ON tbl_course_courses USING gin (component_sequence);
-- 支持对组件序列JSON的查询
```

---

### 3.3 表名: `tbl_course_components`

#### 3.3.1 表功能说明
存储课程中的文档组件详细内容，包括文档内容、勾画轨迹数据、字幕数据等。该表支持文档组件的跟随模式和自由模式播放，为course-center服务提供组件内容管理能力，是实现文档组件交互播放的核心数据基础。

#### 3.3.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_course_components (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  course_id BIGINT NOT NULL,
  component_type VARCHAR(50) NOT NULL,
  component_order INTEGER NOT NULL,
  component_name VARCHAR(255) NOT NULL,
  document_content JSONB NOT NULL,
  drawing_trajectory_data JSONB NOT NULL,
  subtitle_data JSONB NULL,
  total_duration INTEGER NOT NULL DEFAULT 0,
  playback_config JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_course_components IS '课程文档组件表 - 存储文档内容、勾画轨迹、字幕等组件详细信息';
COMMENT ON COLUMN tbl_course_components.id IS '组件主键ID';
COMMENT ON COLUMN tbl_course_components.course_id IS '所属课程ID，关联tbl_course_courses.id';
COMMENT ON COLUMN tbl_course_components.component_type IS '组件类型 (document:文档组件, exercise:练习组件)';
COMMENT ON COLUMN tbl_course_components.component_order IS '组件在课程中的顺序位置';
COMMENT ON COLUMN tbl_course_components.component_name IS '组件名称';
COMMENT ON COLUMN tbl_course_components.document_content IS '文档具体内容，JSON格式';
COMMENT ON COLUMN tbl_course_components.drawing_trajectory_data IS '与音频同步的勾画轨迹数据，JSON格式';
COMMENT ON COLUMN tbl_course_components.subtitle_data IS '字幕数据，JSON格式';
COMMENT ON COLUMN tbl_course_components.total_duration IS '组件总播放时长（秒）';
COMMENT ON COLUMN tbl_course_components.playback_config IS '播放配置信息，JSON格式';
COMMENT ON COLUMN tbl_course_components.status IS '组件状态 (0:禁用, 1:启用, 2:草稿)';
COMMENT ON COLUMN tbl_course_components.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_course_components.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_course_components.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_course_components.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_course_components.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_course_components_course_id ON tbl_course_components(course_id) WHERE deleted_at IS NULL;
-- 支持按课程ID查询组件列表
CREATE INDEX idx_course_components_course_order ON tbl_course_components(course_id, component_order) WHERE deleted_at IS NULL;
-- 支持按课程ID和顺序查询组件
CREATE INDEX idx_course_components_type ON tbl_course_components(component_type) WHERE deleted_at IS NULL;
-- 支持按组件类型查询
CREATE UNIQUE INDEX uk_course_components_course_order ON tbl_course_components(course_id, component_order) WHERE deleted_at IS NULL;
-- 确保同一课程中组件顺序唯一
CREATE INDEX idx_course_components_document_content_gin ON tbl_course_components USING gin (document_content);
-- 支持对文档内容JSON的查询
```

---

### 3.4 表名: `tbl_study_sessions`

#### 3.4.1 表功能说明
存储用户学习会话的记录和状态管理，记录用户单次学习课程的完整会话信息。该表为study-center服务提供学习会话管理能力，支持学习进度跟踪、会话状态控制和学习轨迹记录，是连接用户和课程学习的核心纽带。

#### 3.4.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_study_sessions (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id BIGINT NOT NULL,
  course_id BIGINT NOT NULL,
  session_status VARCHAR(20) NOT NULL DEFAULT 'in_progress',
  current_component_position INTEGER NOT NULL DEFAULT 0,
  start_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  end_time TIMESTAMPTZ NULL,
  total_duration INTEGER NOT NULL DEFAULT 0,
  learning_mode VARCHAR(20) NOT NULL DEFAULT 'follow',
  session_config JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_study_sessions IS '学习会话表 - 存储用户学习课程的会话记录和状态管理';
COMMENT ON COLUMN tbl_study_sessions.id IS '会话主键ID';
COMMENT ON COLUMN tbl_study_sessions.user_id IS '学习用户ID，关联用户中心服务';
COMMENT ON COLUMN tbl_study_sessions.course_id IS '学习课程ID，关联tbl_course_courses.id';
COMMENT ON COLUMN tbl_study_sessions.session_status IS '会话状态 (in_progress:进行中, completed:已完成, exited:已退出)';
COMMENT ON COLUMN tbl_study_sessions.current_component_position IS '当前学习到的组件位置';
COMMENT ON COLUMN tbl_study_sessions.start_time IS '学习开始时间';
COMMENT ON COLUMN tbl_study_sessions.end_time IS '学习结束时间';
COMMENT ON COLUMN tbl_study_sessions.total_duration IS '学习总用时（秒）';
COMMENT ON COLUMN tbl_study_sessions.learning_mode IS '学习模式 (follow:跟随模式, free:自由模式)';
COMMENT ON COLUMN tbl_study_sessions.session_config IS '会话配置信息，JSON格式';
COMMENT ON COLUMN tbl_study_sessions.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_study_sessions.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_study_sessions.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_study_sessions.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_study_sessions.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_study_sessions.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_study_sessions_user_id ON tbl_study_sessions(user_id) WHERE deleted_at IS NULL;
-- 支持按用户ID查询学习会话
CREATE INDEX idx_study_sessions_course_id ON tbl_study_sessions(course_id) WHERE deleted_at IS NULL;
-- 支持按课程ID查询学习会话
CREATE INDEX idx_study_sessions_user_course ON tbl_study_sessions(user_id, course_id) WHERE deleted_at IS NULL;
-- 支持按用户和课程组合查询
CREATE INDEX idx_study_sessions_status ON tbl_study_sessions(session_status) WHERE deleted_at IS NULL;
-- 支持按会话状态查询
CREATE INDEX idx_study_sessions_start_time ON tbl_study_sessions(start_time) WHERE deleted_at IS NULL;
-- 支持按开始时间排序查询
```

---

### 3.5 表名: `tbl_study_progress`

#### 3.5.1 表功能说明
存储文档组件的播放进度和模式状态，记录用户在每个文档组件中的详细播放状态。该表支持跟随模式和自由模式的切换，播放位置的精确记录，以及播放倍速的管理，为study-center服务提供细粒度的播放状态管理能力。

#### 3.5.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_study_progress (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  session_id BIGINT NOT NULL,
  component_id BIGINT NOT NULL,
  current_position INTEGER NOT NULL DEFAULT 0,
  playback_mode VARCHAR(20) NOT NULL DEFAULT 'follow',
  playback_speed NUMERIC(3,2) NOT NULL DEFAULT 1.00,
  is_completed BOOLEAN NOT NULL DEFAULT FALSE,
  completion_time TIMESTAMPTZ NULL,
  interaction_data JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_study_progress IS '学习进度表 - 存储文档组件播放进度和模式状态';
COMMENT ON COLUMN tbl_study_progress.id IS '进度记录主键ID';
COMMENT ON COLUMN tbl_study_progress.session_id IS '学习会话ID，关联tbl_study_sessions.id';
COMMENT ON COLUMN tbl_study_progress.component_id IS '文档组件ID，关联tbl_course_components.id';
COMMENT ON COLUMN tbl_study_progress.current_position IS '当前播放位置（秒）';
COMMENT ON COLUMN tbl_study_progress.playback_mode IS '播放模式 (follow:跟随模式, free:自由模式)';
COMMENT ON COLUMN tbl_study_progress.playback_speed IS '播放倍速 (0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 3.0)';
COMMENT ON COLUMN tbl_study_progress.is_completed IS '是否已完成该组件';
COMMENT ON COLUMN tbl_study_progress.completion_time IS '组件完成时间';
COMMENT ON COLUMN tbl_study_progress.interaction_data IS '交互数据，JSON格式存储双击、长按等操作记录';
COMMENT ON COLUMN tbl_study_progress.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_study_progress.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_study_progress.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_study_progress.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_study_progress.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_study_progress.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_study_progress_session_id ON tbl_study_progress(session_id) WHERE deleted_at IS NULL;
-- 支持按会话ID查询进度
CREATE INDEX idx_study_progress_component_id ON tbl_study_progress(component_id) WHERE deleted_at IS NULL;
-- 支持按组件ID查询进度
CREATE UNIQUE INDEX uk_study_progress_session_component ON tbl_study_progress(session_id, component_id) WHERE deleted_at IS NULL;
-- 确保同一会话中每个组件只有一条进度记录
CREATE INDEX idx_study_progress_completion ON tbl_study_progress(is_completed, completion_time) WHERE deleted_at IS NULL;
-- 支持按完成状态和时间查询
CREATE INDEX idx_study_progress_updated_at ON tbl_study_progress(updated_at) WHERE deleted_at IS NULL;
-- 支持按更新时间查询最新进度
```

---

### 3.6 表名: `tbl_study_performance`

#### 3.6.1 表功能说明
存储学习表现数据汇总，包括用时、答题数、正确率等关键指标。该表为每个学习会话提供统计汇总数据，支持结算页展示和学习报告生成，为study-center服务提供学习表现分析能力，是掌握度计算的重要数据来源。

#### 3.6.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_study_performance (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  session_id BIGINT NOT NULL,
  total_questions INTEGER NOT NULL DEFAULT 0,
  correct_answers INTEGER NOT NULL DEFAULT 0,
  accuracy_rate NUMERIC(5,2) NOT NULL DEFAULT 0.00,
  study_duration INTEGER NOT NULL DEFAULT 0,
  completion_rate NUMERIC(5,2) NOT NULL DEFAULT 0.00,
  energy_gained INTEGER NOT NULL DEFAULT 0,
  performance_data JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_study_performance IS '学习表现表 - 存储学习会话的表现数据汇总';
COMMENT ON COLUMN tbl_study_performance.id IS '表现记录主键ID';
COMMENT ON COLUMN tbl_study_performance.session_id IS '学习会话ID，关联tbl_study_sessions.id';
COMMENT ON COLUMN tbl_study_performance.total_questions IS '总答题数量';
COMMENT ON COLUMN tbl_study_performance.correct_answers IS '正确答题数量';
COMMENT ON COLUMN tbl_study_performance.accuracy_rate IS '正确率（百分比）';
COMMENT ON COLUMN tbl_study_performance.study_duration IS '学习用时（秒）';
COMMENT ON COLUMN tbl_study_performance.completion_rate IS '完成率（百分比）';
COMMENT ON COLUMN tbl_study_performance.energy_gained IS '获得的能量值';
COMMENT ON COLUMN tbl_study_performance.performance_data IS '详细表现数据，JSON格式';
COMMENT ON COLUMN tbl_study_performance.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_study_performance.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_study_performance.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_study_performance.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_study_performance.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_study_performance.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE UNIQUE INDEX uk_study_performance_session ON tbl_study_performance(session_id) WHERE deleted_at IS NULL;
-- 确保每个会话只有一条表现记录
CREATE INDEX idx_study_performance_accuracy ON tbl_study_performance(accuracy_rate) WHERE deleted_at IS NULL;
-- 支持按正确率查询
CREATE INDEX idx_study_performance_duration ON tbl_study_performance(study_duration) WHERE deleted_at IS NULL;
-- 支持按学习用时查询
CREATE INDEX idx_study_performance_completion ON tbl_study_performance(completion_rate) WHERE deleted_at IS NULL;
-- 支持按完成率查询
CREATE INDEX idx_study_performance_created_at ON tbl_study_performance(created_at) WHERE deleted_at IS NULL;
-- 支持按创建时间查询
```

---

### 3.7 表名: `tbl_mastery_records`

#### 3.7.1 表功能说明
存储用户知识点掌握度记录和目标设置，记录用户对特定知识点的掌握程度和学习目标。该表支持掌握度策略计算和外化展示，为study-center服务提供掌握度管理能力，是个性化学习推荐和学习效果评估的核心数据基础。

#### 3.7.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_mastery_records (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  user_id BIGINT NOT NULL,
  knowledge_point_id BIGINT NOT NULL,
  current_mastery NUMERIC(3,2) NOT NULL DEFAULT 0.00,
  target_mastery NUMERIC(3,2) NOT NULL DEFAULT 0.80,
  mastery_progress NUMERIC(3,2) NOT NULL DEFAULT 0.00,
  energy_gained INTEGER NOT NULL DEFAULT 0,
  calculation_strategy VARCHAR(50) NOT NULL DEFAULT 'default',
  last_updated_session_id BIGINT NULL,
  mastery_data JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_mastery_records IS '掌握度记录表 - 存储用户知识点掌握度和目标设置';
COMMENT ON COLUMN tbl_mastery_records.id IS '掌握度记录主键ID';
COMMENT ON COLUMN tbl_mastery_records.user_id IS '用户ID，关联用户中心服务';
COMMENT ON COLUMN tbl_mastery_records.knowledge_point_id IS '知识点ID，关联内容平台服务';
COMMENT ON COLUMN tbl_mastery_records.current_mastery IS '当前掌握度值 (0.00-1.00)';
COMMENT ON COLUMN tbl_mastery_records.target_mastery IS '目标掌握度值 (0.00-1.00)';
COMMENT ON COLUMN tbl_mastery_records.mastery_progress IS '掌握度完成进度 (current_mastery/target_mastery)';
COMMENT ON COLUMN tbl_mastery_records.energy_gained IS '通过学习获得的能量值';
COMMENT ON COLUMN tbl_mastery_records.calculation_strategy IS '掌握度计算策略标识';
COMMENT ON COLUMN tbl_mastery_records.last_updated_session_id IS '最后更新掌握度的学习会话ID';
COMMENT ON COLUMN tbl_mastery_records.mastery_data IS '掌握度详细数据，JSON格式';
COMMENT ON COLUMN tbl_mastery_records.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_mastery_records.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_mastery_records.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_mastery_records.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_mastery_records.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_mastery_records.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_mastery_records_user_id ON tbl_mastery_records(user_id) WHERE deleted_at IS NULL;
-- 支持按用户ID查询掌握度记录
CREATE INDEX idx_mastery_records_knowledge_point ON tbl_mastery_records(knowledge_point_id) WHERE deleted_at IS NULL;
-- 支持按知识点ID查询掌握度记录
CREATE UNIQUE INDEX uk_mastery_records_user_knowledge ON tbl_mastery_records(user_id, knowledge_point_id) WHERE deleted_at IS NULL;
-- 确保用户对每个知识点只有一条掌握度记录
CREATE INDEX idx_mastery_records_current_mastery ON tbl_mastery_records(current_mastery) WHERE deleted_at IS NULL;
-- 支持按掌握度值查询
CREATE INDEX idx_mastery_records_progress ON tbl_mastery_records(mastery_progress) WHERE deleted_at IS NULL;
-- 支持按掌握度进度查询
CREATE INDEX idx_mastery_records_updated_at ON tbl_mastery_records(updated_at) WHERE deleted_at IS NULL;
-- 支持按更新时间查询
```

---

### 3.8 表名: `tbl_study_feedback`

#### 3.8.1 表功能说明
存储课程反馈和评价数据，记录用户在结算页提交的课程评价和反馈信息。该表支持5级评价反馈、详细反馈选项和文字反馈收集，为study-center服务提供反馈管理能力，是课程质量评估和改进的重要数据来源。

#### 3.8.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_study_feedback (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  session_id BIGINT NOT NULL,
  rating_level INTEGER NOT NULL,
  feedback_options JSONB NULL,
  text_feedback TEXT NULL,
  feedback_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  feedback_data JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_study_feedback IS '学习反馈表 - 存储课程评价和反馈数据';
COMMENT ON COLUMN tbl_study_feedback.id IS '反馈记录主键ID';
COMMENT ON COLUMN tbl_study_feedback.session_id IS '学习会话ID，关联tbl_study_sessions.id';
COMMENT ON COLUMN tbl_study_feedback.rating_level IS '评价等级 (1-5级评价)';
COMMENT ON COLUMN tbl_study_feedback.feedback_options IS '详细反馈选项，JSON格式';
COMMENT ON COLUMN tbl_study_feedback.text_feedback IS '用户输入的文字反馈';
COMMENT ON COLUMN tbl_study_feedback.feedback_time IS '反馈提交时间';
COMMENT ON COLUMN tbl_study_feedback.feedback_data IS '其他反馈数据，JSON格式';
COMMENT ON COLUMN tbl_study_feedback.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_study_feedback.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_study_feedback.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_study_feedback.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_study_feedback.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_study_feedback.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE UNIQUE INDEX uk_study_feedback_session ON tbl_study_feedback(session_id) WHERE deleted_at IS NULL;
-- 确保每个会话只有一条反馈记录
CREATE INDEX idx_study_feedback_rating ON tbl_study_feedback(rating_level) WHERE deleted_at IS NULL;
-- 支持按评价等级查询
CREATE INDEX idx_study_feedback_time ON tbl_study_feedback(feedback_time) WHERE deleted_at IS NULL;
-- 支持按反馈时间查询
CREATE INDEX idx_study_feedback_created_at ON tbl_study_feedback(created_at) WHERE deleted_at IS NULL;
-- 支持按创建时间查询
```

---

### 3.9 表名: `tbl_answer_logs` (ClickHouse)

#### 3.9.1 表功能说明
存储用户答题记录详细日志，用于分析和掌握度计算。该表采用ClickHouse存储，支持高频写入的答题数据记录，为掌握度计算策略提供详细的答题行为数据，支持学习行为分析和个性化推荐算法。

#### 3.9.2 表结构定义 (DDL)
```sql
CREATE TABLE default.tbl_answer_logs
(
    -- 时间字段
    log_date Date MATERIALIZED toDate(answer_time) COMMENT '答题日期（用于分区）',
    answer_time DateTime64(3) COMMENT '答题提交时间（毫秒精度）',
    
    -- 业务字段
    session_id UInt64 COMMENT '学习会话ID',
    user_id UInt64 COMMENT '用户ID',
    course_id UInt64 COMMENT '课程ID',
    component_id UInt64 COMMENT '组件ID',
    question_id UInt64 COMMENT '题目ID',
    user_answer String COMMENT '用户提交的答案',
    is_correct UInt8 COMMENT '是否正确 (0:错误, 1:正确)',
    question_difficulty Float32 COMMENT '题目难度系数',
    answer_duration UInt32 COMMENT '答题用时（秒）',
    attempt_count UInt8 DEFAULT 1 COMMENT '答题尝试次数',
    
    -- 扩展数据
    answer_metadata Map(String, String) COMMENT '答题元数据（设备信息、IP等）'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date) -- 按月分区
ORDER BY (log_date, user_id, answer_time, session_id) -- 排序键：日期、用户ID、答题时间、会话ID
TTL log_date + INTERVAL 365 DAY DELETE -- 数据保留1年
SETTINGS index_granularity = 8192;

-- ClickHouse 特有说明
-- 排序键 (ORDER BY): (log_date, user_id, answer_time, session_id)
-- 设计原因: 支持按日期范围查询用户答题记录，按用户聚合分析，按时间序列分析答题行为
-- 分区键 (PARTITION BY): toYYYYMM(log_date)
-- 设计原因: 按月分区便于数据管理和查询优化，支持按时间范围的分区裁剪

-- 跳数索引
ALTER TABLE default.tbl_answer_logs ADD INDEX idx_question_id question_id TYPE bloom_filter GRANULARITY 1;
-- 支持快速筛选特定题目的答题记录
ALTER TABLE default.tbl_answer_logs ADD INDEX idx_course_component course_id, component_id TYPE minmax GRANULARITY 1;
-- 支持按课程和组件范围查询
```

---

### 3.10 表名: `tbl_operation_logs` (ClickHouse)

#### 3.10.1 表功能说明
存储用户学习过程中的操作行为日志，记录双击播放暂停、倍速调整、模式切换等交互操作。该表采用ClickHouse存储，支持高频写入的操作日志记录，为用户行为分析、学习模式优化和系统性能监控提供详细的操作数据。

#### 3.10.2 表结构定义 (DDL)
```sql
CREATE TABLE default.tbl_operation_logs
(
    -- 时间字段
    log_date Date MATERIALIZED toDate(operation_time) COMMENT '操作日期（用于分区）',
    operation_time DateTime64(3) COMMENT '操作发生时间（毫秒精度）',
    
    -- 业务字段
    session_id UInt64 COMMENT '学习会话ID',
    user_id UInt64 COMMENT '用户ID',
    course_id UInt64 COMMENT '课程ID',
    component_id Nullable(UInt64) COMMENT '组件ID（如果操作与特定组件相关）',
    operation_type LowCardinality(String) COMMENT '操作类型 (double_click, speed_change, mode_switch, long_press等)',
    operation_target LowCardinality(String) COMMENT '操作目标 (document, control_bar, screen等)',
    operation_details String COMMENT '操作详细内容描述',
    before_state String COMMENT '操作前状态',
    after_state String COMMENT '操作后状态',
    
    -- 技术字段
    client_ip IPv6 COMMENT '客户端IP地址',
    user_agent String COMMENT '用户代理信息',
    device_info Map(String, String) COMMENT '设备信息（设备类型、屏幕尺寸等）'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date) -- 按月分区
ORDER BY (log_date, user_id, operation_time, operation_type) -- 排序键：日期、用户ID、操作时间、操作类型
TTL log_date + INTERVAL 180 DAY DELETE -- 数据保留6个月
SETTINGS index_granularity = 8192;

-- ClickHouse 特有说明
-- 排序键 (ORDER BY): (log_date, user_id, operation_time, operation_type)
-- 设计原因: 支持按日期范围查询用户操作记录，按用户聚合分析操作行为，按操作类型统计分析
-- 分区键 (PARTITION BY): toYYYYMM(log_date)
-- 设计原因: 按月分区便于数据管理，操作日志数据量大但查询通常集中在近期数据

-- 跳数索引
ALTER TABLE default.tbl_operation_logs ADD INDEX idx_operation_type operation_type TYPE set(0) GRANULARITY 1;
-- 支持快速筛选特定操作类型
ALTER TABLE default.tbl_operation_logs ADD INDEX idx_session_id session_id TYPE bloom_filter GRANULARITY 1;
-- 支持快速定位特定会话的操作记录
ALTER TABLE default.tbl_operation_logs ADD INDEX idx_course_component course_id, component_id TYPE minmax GRANULARITY 1;
-- 支持按课程和组件范围查询操作记录
```

---

## 4. 表间关系与 ER 图

### 4.1 表间关系描述

| 主表           | 关系类型                       | 从表               | 外键字段 (从表)                           | 关联描述                                               |
| -------------- | ------------------------------ | ------------------ | ----------------------------------------- | ------------------------------------------------------ |
| `tbl_course_courses`   | 一对多 (1:N)                   | `tbl_course_components`     | `course_id`                                 | 一个课程包含多个文档组件                                 |
| `tbl_course_courses` | 一对多 (1:N) | `tbl_study_sessions` | `course_id` | 一个课程可以有多个学习会话 |
| `tbl_study_sessions` | 一对多 (1:N) | `tbl_study_progress` | `session_id` | 一个学习会话包含多个组件进度记录 |
| `tbl_course_components` | 一对多 (1:N) | `tbl_study_progress` | `component_id` | 一个组件可以在多个会话中被学习 |
| `tbl_study_sessions` | 一对一 (1:1) | `tbl_study_performance` | `session_id` | 一个学习会话对应一条表现记录 |
| `tbl_study_sessions` | 一对一 (1:1) | `tbl_study_feedback` | `session_id` | 一个学习会话对应一条反馈记录 |
| `tbl_study_sessions` | 一对多 (1:N) | `tbl_answer_logs` | `session_id` | 一个学习会话包含多条答题日志 |
| `tbl_study_sessions` | 一对多 (1:N) | `tbl_operation_logs` | `session_id` | 一个学习会话包含多条操作日志 |

### 4.2 ER 图 (使用 Mermaid 语法)
注意：本ER图中的外键关系（FK）表示的是表之间的逻辑关联和参照完整性期望，实际建表语句中不使用数据库层面的物理外键约束，数据一致性由应用层保障。

```mermaid
erDiagram
    %% PostgreSQL 核心业务表
    COURSE_COURSES {
        BIGSERIAL id PK
        VARCHAR course_name
        TEXT course_info
        JSONB component_sequence
        JSONB opening_config
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    COURSE_COMPONENTS {
        BIGSERIAL id PK
        BIGINT course_id FK
        VARCHAR component_type
        INTEGER component_order
        VARCHAR component_name
        JSONB document_content
        JSONB drawing_trajectory_data
        INTEGER total_duration
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    STUDY_SESSIONS {
        BIGSERIAL id PK
        BIGINT user_id FK
        BIGINT course_id FK
        VARCHAR session_status
        INTEGER current_component_position
        TIMESTAMPTZ start_time
        TIMESTAMPTZ end_time
        INTEGER total_duration
        VARCHAR learning_mode
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    STUDY_PROGRESS {
        BIGSERIAL id PK
        BIGINT session_id FK
        BIGINT component_id FK
        INTEGER current_position
        VARCHAR playback_mode
        NUMERIC playback_speed
        BOOLEAN is_completed
        TIMESTAMPTZ completion_time
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    STUDY_PERFORMANCE {
        BIGSERIAL id PK
        BIGINT session_id FK
        INTEGER total_questions
        INTEGER correct_answers
        NUMERIC accuracy_rate
        INTEGER study_duration
        NUMERIC completion_rate
        INTEGER energy_gained
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    MASTERY_RECORDS {
        BIGSERIAL id PK
        BIGINT user_id FK
        BIGINT knowledge_point_id FK
        NUMERIC current_mastery
        NUMERIC target_mastery
        NUMERIC mastery_progress
        INTEGER energy_gained
        VARCHAR calculation_strategy
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    STUDY_FEEDBACK {
        BIGSERIAL id PK
        BIGINT session_id FK
        INTEGER rating_level
        JSONB feedback_options
        TEXT text_feedback
        TIMESTAMPTZ feedback_time
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    %% ClickHouse 分析表
    ANSWER_LOGS {
        Date log_date
        DateTime64 answer_time
        UInt64 session_id
        UInt64 user_id
        UInt64 course_id
        UInt64 component_id
        UInt64 question_id
        String user_answer
        UInt8 is_correct
        Float32 question_difficulty
    }
    
    OPERATION_LOGS {
        Date log_date
        DateTime64 operation_time
        UInt64 session_id
        UInt64 user_id
        UInt64 course_id
        UInt64 component_id
        String operation_type
        String operation_target
        String operation_details
    }

    %% 关系定义
    COURSE_COURSES ||--o{ COURSE_COMPONENTS : "contains"
    COURSE_COURSES ||--o{ STUDY_SESSIONS : "studied_in"
    STUDY_SESSIONS ||--o{ STUDY_PROGRESS : "tracks"
    COURSE_COMPONENTS ||--o{ STUDY_PROGRESS : "progress_of"
    STUDY_SESSIONS ||--|| STUDY_PERFORMANCE : "generates"
    STUDY_SESSIONS ||--|| STUDY_FEEDBACK : "receives"
    STUDY_SESSIONS ||--o{ ANSWER_LOGS : "logs_answers"
    STUDY_SESSIONS ||--o{ OPERATION_LOGS : "logs_operations"
```

## 5. 核心用户场景/用户故事与数据操作分析

### 5.1 场景/故事: 学生首次进入AI课程学习

#### 5.1.1 场景描述
学生通过客户端进入AI课程学习，系统验证用户身份后，获取课程开场页内容并初始化学习记录。学生观看开场页IP动效和音频介绍，然后进入第一个文档组件开始学习。整个过程需要确保内容加载响应时间不超过2秒，支持流畅的学习体验。

#### 5.1.2 涉及的主要数据表
- `tbl_course_courses` - 获取课程基本信息和开场页配置
- `tbl_course_components` - 获取第一个文档组件内容
- `tbl_study_sessions` - 创建学习会话记录
- `tbl_study_progress` - 初始化组件播放进度
- `tbl_operation_logs` - 记录用户进入课程的操作日志

#### 5.1.3 数据操作流程 (CRUD 分析)
- **步骤 1: 用户请求进入课程**
  - **读取 (Read)**:
    - 从 `tbl_course_courses` 表中查询课程信息
    - 查询条件：`id = {课程ID} AND status = 1 AND deleted_at IS NULL`
    - 涉及字段：`course_name`, `course_info`, `opening_config`, `ip_animation_config`, `audio_config`
    - 目的：获取开场页展示所需的课程基本信息和配置
  - **创建 (Create)**:
    - 在 `tbl_study_sessions` 表中插入新的学习会话记录
    - 涉及字段：`user_id`, `course_id`, `session_status`, `start_time`, `learning_mode`
    - 数据来源：用户认证信息和课程ID
    - 成功标准：返回新创建的会话ID

- **步骤 2: 获取第一个文档组件内容**
  - **读取 (Read)**:
    - 从 `tbl_course_components` 表中查询第一个组件
    - 查询条件：`course_id = {课程ID} AND component_order = 1 AND status = 1 AND deleted_at IS NULL`
    - 涉及字段：`component_name`, `document_content`, `drawing_trajectory_data`, `total_duration`
    - 目的：获取第一个文档组件的播放内容
  - **创建 (Create)**:
    - 在 `tbl_study_progress` 表中插入进度记录
    - 涉及字段：`session_id`, `component_id`, `current_position`, `playback_mode`
    - 数据来源：会话ID和组件ID
    - 成功标准：初始化播放进度为0

- **步骤 3: 记录操作日志**
  - **创建 (Create)**:
    - 在 `tbl_operation_logs` 表中插入操作记录
    - 涉及字段：`session_id`, `user_id`, `course_id`, `operation_type`, `operation_details`
    - 数据来源：会话信息和操作类型
    - 成功标准：记录用户进入课程的操作行为

#### 5.1.4 性能考量与索引支持
- 课程信息查询通过 `idx_course_courses_status` 索引快速定位活跃课程
- 组件查询通过 `idx_course_components_course_order` 复合索引高效获取第一个组件
- 会话创建通过 `idx_study_sessions_user_course` 索引支持用户学习历史查询
- 操作日志写入通过ClickHouse的高性能写入能力支持高并发场景

---

### 5.2 场景/故事: 文档组件交互学习

#### 5.2.1 场景描述
学生在文档组件中进行交互学习，包括跟随模式观看内容、滑动切换到自由模式、双击播放控制、长按加速播放、调整播放倍速等操作。系统需要实时记录播放进度、模式状态和用户交互行为，确保学习状态的准确跟踪和流畅的交互体验。

#### 5.2.2 涉及的主要数据表
- `tbl_study_progress` - 更新播放进度和模式状态
- `tbl_course_components` - 获取勾画轨迹数据用于同步播放
- `tbl_operation_logs` - 记录所有交互操作行为
- `tbl_study_sessions` - 更新学习会话状态

#### 5.2.3 数据操作流程 (CRUD 分析)
- **步骤 1: 用户滑动内容切换到自由模式**
  - **更新 (Update)**:
    - 更新 `tbl_study_progress` 表中的播放状态
    - 更新条件：`session_id = {会话ID} AND component_id = {组件ID}`
    - 更新字段：`playback_mode = 'free'`, `updated_at = CURRENT_TIMESTAMP`
    - 影响：切换播放模式，暂停跟随播放
  - **创建 (Create)**:
    - 在 `tbl_operation_logs` 表中记录模式切换操作
    - 涉及字段：`session_id`, `operation_type = 'mode_switch'`, `before_state = 'follow'`, `after_state = 'free'`

- **步骤 2: 用户双击屏幕控制播放**
  - **更新 (Update)**:
    - 更新 `tbl_study_progress` 表中的播放状态
    - 更新字段：根据当前状态切换播放/暂停状态
    - 影响：控制文档组件的播放状态
  - **创建 (Create)**:
    - 在 `tbl_operation_logs` 表中记录双击操作
    - 涉及字段：`operation_type = 'double_click'`, `operation_target = 'screen'`, `operation_details`

- **步骤 3: 用户长按加速播放**
  - **更新 (Update)**:
    - 更新 `tbl_study_progress` 表中的播放倍速
    - 更新字段：`playback_speed = 3.00`, `updated_at = CURRENT_TIMESTAMP`
    - 影响：设置3倍速播放
  - **创建 (Create)**:
    - 在 `tbl_operation_logs` 表中记录长按操作
    - 涉及字段：`operation_type = 'long_press'`, `operation_details = 'speed_3x'`

- **步骤 4: 用户点击"跟随老师"恢复跟随模式**
  - **读取 (Read)**:
    - 从 `tbl_course_components` 表中获取当前播放位置的勾画轨迹
    - 查询条件：`id = {组件ID}`
    - 涉及字段：`drawing_trajectory_data`
    - 目的：获取同步轨迹数据用于恢复跟随播放
  - **更新 (Update)**:
    - 更新 `tbl_study_progress` 表恢复跟随模式
    - 更新字段：`playback_mode = 'follow'`, `playback_speed = 1.00`
    - 影响：恢复跟随模式和正常倍速

#### 5.2.4 性能考量与索引支持
- 播放进度更新通过 `uk_study_progress_session_component` 唯一索引快速定位记录
- 勾画轨迹查询通过主键索引高效获取组件数据
- 操作日志写入通过ClickHouse的列式存储和高并发写入能力支持实时记录
- 播放状态更新通过 `idx_study_progress_updated_at` 索引支持最新状态查询

---

### 5.3 场景/故事: 课程完成和掌握度更新

#### 5.3.1 场景描述
学生完成课程中的最后一个组件后，系统计算课程完成状态，触发掌握度计算，生成学习表现数据，并展示结算页面。整个过程包括答题记录分析、掌握度策略计算、学习报告生成和反馈收集，为学生提供完整的学习成果展示。

#### 5.3.2 涉及的主要数据表
- `tbl_study_progress` - 检查所有组件完成状态
- `tbl_study_sessions` - 更新会话完成状态
- `tbl_study_performance` - 生成学习表现汇总数据
- `tbl_mastery_records` - 更新掌握度记录
- `tbl_answer_logs` - 分析答题记录用于掌握度计算
- `tbl_study_feedback` - 收集课程反馈

#### 5.3.3 数据操作流程 (CRUD 分析)
- **步骤 1: 检查课程完成状态**
  - **读取 (Read)**:
    - 从 `tbl_study_progress` 表中查询所有组件完成状态
    - 查询条件：`session_id = {会话ID} AND is_completed = true`
    - 涉及字段：`component_id`, `is_completed`, `completion_time`
    - 目的：确认所有组件是否已完成
  - **更新 (Update)**:
    - 更新 `tbl_study_sessions` 表的会话状态
    - 更新字段：`session_status = 'completed'`, `end_time = CURRENT_TIMESTAMP`
    - 影响：标记学习会话为已完成状态

- **步骤 2: 生成学习表现数据**
  - **读取 (Read)**:
    - 从 `tbl_answer_logs` 表中聚合答题数据
    - 查询条件：`session_id = {会话ID}`
    - 聚合字段：`COUNT(*) as total_questions`, `SUM(is_correct) as correct_answers`
    - 目的：计算总答题数和正确答题数
  - **创建 (Create)**:
    - 在 `tbl_study_performance` 表中插入表现记录
    - 涉及字段：`session_id`, `total_questions`, `correct_answers`, `accuracy_rate`, `study_duration`
    - 计算逻辑：`accuracy_rate = correct_answers / total_questions * 100`

- **步骤 3: 更新掌握度记录**
  - **读取 (Read)**:
    - 从 `tbl_answer_logs` 表中获取知识点相关答题记录
    - 查询条件：按知识点分组的答题表现数据
    - 目的：为掌握度计算提供数据基础
  - **更新 (Update)**:
    - 更新 `tbl_mastery_records` 表中的掌握度值
    - 更新字段：`current_mastery`, `mastery_progress`, `last_updated_session_id`
    - 计算策略：基于答题正确率和题目难度的加权计算

- **步骤 4: 收集课程反馈**
  - **创建 (Create)**:
    - 在 `tbl_study_feedback` 表中插入反馈记录
    - 涉及字段：`session_id`, `rating_level`, `feedback_options`, `text_feedback`
    - 数据来源：用户在结算页提交的反馈信息

#### 5.3.4 性能考量与索引支持
- 组件完成状态查询通过 `idx_study_progress_session_id` 和 `idx_study_progress_completion` 索引优化
- 答题数据聚合通过ClickHouse的列式存储和向量化计算能力高效处理
- 掌握度更新通过 `uk_mastery_records_user_knowledge` 唯一索引快速定位记录
- 学习表现数据通过 `uk_study_performance_session` 唯一索引确保数据一致性

---

## 6. 数据量预估与性能优化策略

### 6.1 数据量预估

| 表名 | 初期数据量 (3个月) | 一年后预估 | 数据特征 | 增长模式 |
|------|-------------------|------------|----------|----------|
| `tbl_course_courses` | 100条 | 500条 | 课程基础数据，增长缓慢 | 线性增长，每月新增30-50门课程 |
| `tbl_course_components` | 2,000条 | 10,000条 | 平均每门课程20个组件 | 与课程数量成正比增长 |
| `tbl_study_sessions` | 50万条 | 300万条 | 用户学习会话，高频产生 | 指数增长，随用户活跃度提升 |
| `tbl_study_progress` | 100万条 | 600万条 | 每个会话平均20条进度记录 | 与学习会话成正比增长 |
| `tbl_study_performance` | 50万条 | 300万条 | 每个会话一条表现记录 | 与学习会话1:1增长 |
| `tbl_mastery_records` | 100万条 | 500万条 | 用户知识点掌握度记录 | 随用户和知识点数量增长 |
| `tbl_study_feedback` | 25万条 | 150万条 | 约50%会话产生反馈 | 与完成会话数量相关 |
| `tbl_answer_logs` (ClickHouse) | 500万条 | 5000万条 | 高频答题日志数据 | 快速增长，每日新增10万+ |
| `tbl_operation_logs` (ClickHouse) | 2000万条 | 2亿条 | 用户操作行为日志 | 极高频增长，每日新增50万+ |

### 6.2 数据增长趋势分析

**PostgreSQL表增长特点**：
- **稳定增长型**：课程和组件数据增长相对稳定，主要受内容制作速度影响
- **用户驱动型**：学习会话、进度、表现等数据增长与用户活跃度强相关
- **累积型**：掌握度记录随用户学习深度累积，长期保持增长

**ClickHouse表增长特点**：
- **高频写入型**：答题和操作日志数据写入频率极高，需要优化写入性能
- **时间序列型**：数据按时间顺序产生，适合时间分区和TTL策略
- **分析导向型**：主要用于数据分析和报表生成，读取模式以聚合查询为主

### 6.3 性能优化策略

#### 6.3.1 PostgreSQL优化策略
**索引优化**：
- 为高频查询字段建立复合索引，如 `(user_id, course_id)`, `(session_id, component_id)`
- 使用部分索引减少索引大小，如 `WHERE deleted_at IS NULL`
- 定期分析索引使用情况，清理无效索引

**查询优化**：
- 使用连接池管理数据库连接，避免连接开销
- 对大表查询使用分页和限制条件，避免全表扫描
- 使用EXPLAIN分析查询计划，优化慢查询

#### 6.3.2 ClickHouse优化策略
**存储优化**：
- 使用合适的压缩算法（LZ4/ZSTD）减少存储空间
- 设置合理的TTL策略，自动清理过期数据
- 使用物化视图预计算常用聚合结果

**查询优化**：
- 利用排序键优化常用查询模式
- 使用跳数索引加速特定字段的过滤查询
- 合理设计分区键，支持分区裁剪优化

---

## 7. 设计检查清单 (Design Checklist)

### 7.1 需求与架构对齐性检查

| 检查项 | 检查结果 | 说明 |
|--------|----------|------|
| **PRD核心实体覆盖** | ✅ 完整 | 所有PRD中定义的核心实体（课程、文档组件、学习会话等）均已设计对应表结构 |
| **业务流程支持** | ✅ 完整 | 学习流程、交互控制、掌握度计算等关键业务流程均有数据支撑 |
| **TD架构约束遵循** | ✅ 符合 | 严格按照TD文档的数据库技术选型，PostgreSQL存储业务数据，ClickHouse存储日志数据 |
| **外部服务集成** | ✅ 合理 | 正确处理与用户中心、内容平台、教师服务的数据依赖关系 |
| **性能需求满足** | ✅ 满足 | 索引设计支持2秒内内容加载，1000并发用户访问需求 |

### 7.2 数据库设计质量检查

| 检查维度 | 检查结果 | 详细说明 |
|----------|----------|----------|
| **表结构规范性** | ✅ 符合 | 严格遵循PostgreSQL和ClickHouse设计规范，命名规则统一 |
| **索引设计合理性** | ✅ 合理 | 为高频查询场景设计了复合索引，使用部分索引优化性能 |
| **数据类型选择** | ✅ 恰当 | 根据业务需求选择合适的数据类型，JSONB用于灵活数据存储 |
| **约束条件完整性** | ✅ 完整 | 主键、唯一约束、非空约束等设计完整，保证数据完整性 |
| **关系设计清晰性** | ✅ 清晰 | 表间关系通过逻辑外键明确定义，ER图准确反映关系 |
| **扩展性考虑** | ✅ 充分 | 预留扩展字段，支持未来功能扩展和数据结构调整 |
| **性能优化策略** | ✅ 全面 | 分区策略、TTL策略、缓存策略等性能优化措施完备 |

## 8. 参考资料与附录

### 8.1 核心输入文档
- **数据实体设计文档 (PRD核心摘要)**: `be-s3-2-ai-dd-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md`
- **技术架构设计文档 (TD文档)**: `be-s2-1-ai-td-Format-PRD-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md`

### 8.2 设计规范与约束
- **PostgreSQL 数据库设计规范**: `luban-agent/ai-rules/backend/server-rules/rules-postgresql-code-v2.md`
- **ClickHouse 数据库设计规范**: `luban-agent/ai-rules/backend/server-rules/rules-clickhouse-code-v2.md`
- **数据库设计模板**: `ai-templates/templates/dd/be-s3-4-ai-dd-template-v1.2.md`

### 8.3 公共服务定义
- **通用中间件信息**: `luban-agent/ai-generates/be-service/common.md`
- **用户中心服务**: `luban-agent/ai-generates/be-service/ucenter.md`
- **内容平台服务**: `luban-agent/ai-generates/be-service/question.md`
- **教师服务端**: `luban-agent/ai-generates/be-service/teacher.md`

### 8.4 设计产出信息
- **设计模型**: claude-sonnet-4
- **设计日期**: 2025-05-23
- **文档版本**: V1.0
- **输出文件**: `luban-agent/ai-generates/design/be-s3-4-ai-dd-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md`

---

**数据库设计任务已全部完成，最终文档已生成。**

--- 等待您的命令，指挥官
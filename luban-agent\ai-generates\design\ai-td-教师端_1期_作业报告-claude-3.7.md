# 教师端1期作业报告服务端架构设计

## 1. 引言

### 1.1. 文档目的

为"教师端1期作业报告"系统提供清晰的Golang服务端架构蓝图，指导核心模块/服务的设计与开发，确保系统满足关键的质量属性，并支持未来功能扩展和迭代升级。

### 1.2. 系统概述

本系统是面向合作校教师用户的学情报告平台，核心目标是帮助教师精准掌握学生的学习情况，提供从班级整体到学生个体的多维度学情分析报告。系统围绕作业任务展开，提供作业列表浏览、班级整体学情统计、题目维度分析、学生问题收集和个体学情分析等功能，辅助教师高效进行教学管理和学生辅导。

### 1.3. 设计目标与核心原则

#### 设计目标：

- **高可用性**：服务可用性达到99.9%以上，确保教师在查看学情报告时的流畅体验
- **可伸缩性**：支持同时服务100名以上教师用户的并发访问，未来可扩展至300并发用户
- **高性能**：核心报告页面首次加载时间不超过3秒，Tab切换响应时间不超过1秒，统计数据计算时间不超过2秒
- **可维护性**：清晰的领域模型和模块化设计，使新开发人员能在2周内熟悉系统架构
- **可测试性**：核心计算模块测试覆盖率>85%，确保统计规则和公式的正确性
- **安全性**：严格的权限控制，确保教师仅能访问其授权范围内的数据

#### 核心原则：

- **单一职责原则(SRP)**：每个模块/服务专注于一组内聚的功能，特别关注计算服务与数据管理服务的职责分离
- **开闭原则(OCP)**：设计支持未来增加新的统计规则、添加新的报告维度，而无需修改现有代码
- **高内聚低耦合**：通过明确定义的接口隔离各功能模块，减少模块间的依赖
- **KISS原则**：保持架构简洁，避免过度设计
- **YAGNI原则**：仅实现当前确定需要的功能，不进行过度超前设计

### 1.4. 范围

#### 覆盖范围：

- 作业报告数据模型设计
- 作业列表的数据获取与展示
- 学生报告、答题结果、学生提问三个维度的数据处理与呈现
- 个体学生作业报告功能
- 数据统计计算逻辑与策略
- 权限管理与数据安全

#### 不覆盖范围：

- UI/前端具体实现
- 详细的数据库表结构设计
- DevOps流程与CI/CD细节
- 学生提问功能的采集实现（仅处理已采集的提问数据）

### 1.5. 术语与缩写

- **P0/P1**：优先级区分，P0为核心必须功能，P1为次要功能
- **作业报告**：基于学生作业完成情况生成的学情分析数据
- **学情**：学生学习情况，包括学习进度、正确率、用时等多维度数据
- **共性错题**：多数学生都答错的题目，通常错误率超过预设阈值（如40%）
- **建议鼓励/关注**：系统基于规则识别出需要鼓励或关注的学生群体
- **分层**：学生按学习水平的划分，如S/A/B/C/D等级

## 2. 需求提炼与架构驱动因素

### 2.1. 核心业务能力

从PRD需求中提炼出系统必须具备的核心能力：

1. **作业数据查询与展示**：获取和展示教师权限范围内的作业列表，以及每个作业的核心指标（完成率、正确率等）
2. **班级整体学情分析**：计算和展示班级平均进度、正确率等整体指标，以及按规则识别出的"值得表扬"和"需要关注"学生群体
3. **题目维度分析**：从题目角度分析班级整体作答情况，识别共性错题，展示每道题的正确率、错误人数、作答人数等
4. **学生提问管理**：汇总并按内容结构组织学生在学习过程中提出的问题
5. **个体学生学情分析**：提供单个学生的详细学情报告，包括作答结果和提问记录
6. **数据筛选与搜索**：支持对作业、题目、学生进行多维度筛选和关键词搜索
7. **复杂指标计算**：实现对正确率、连对题数、学习分增幅等多种指标的计算，支持"建议鼓励"和"建议关注"的规则判断
8. **权限管理**：根据教师角色（学科教师、班主任、学科组长等）控制数据访问范围

### 2.2. 关键质量属性

#### 2.2.1. 性能

- 作业列表页面首次加载时间<3秒，后续带缓存加载<1.5秒
- 作业详细报告页面首次加载时间<4秒，Tab切换响应时间<1秒
- 学生个体报告页面加载时间<3秒
- 对于包含50名学生的班级、50道题的作业，学生报告Tab的数据统计应在2秒内完成
- 支持至少100名教师用户同时在线访问和使用作业报告功能

#### 2.2.2. 可用性

- 核心作业报告功能保证99.9%的可用性
- 服务应具备容错能力，对于部分非核心组件的故障不影响核心报告功能的查看和使用
- 关键数据应有备份机制，在发生数据丢失或损坏时能在4小时内恢复

#### 2.2.3. 可伸缩性

- 系统设计为无状态服务，支持水平扩展
- 能够应对学期初/末等高峰期的访问压力，支持至少300用户的并发访问
- 数据量增长（如题目数量、学生数量增加）不应明显影响系统性能

#### 2.2.4. 可维护性

- 采用清晰的领域模型和模块化设计
- 遵循Go项目标准代码组织规范
- 核心计算逻辑与业务规则应集中管理，便于后续调整和扩展

#### 2.2.5. 可测试性

- 计算规则和公式应设计为独立模块，便于单元测试
- 数据加载与计算逻辑分离，便于模拟测试数据
- 使用接口定义依赖，便于Mock外部系统

#### 2.2.6. 安全性

- 严格的权限控制，确保教师只能访问授权范围内的数据
- 学生个人敏感信息的传输和存储应进行加密处理
- 所有用户输入进行合法性校验和过滤，防止SQL注入、XSS等攻击

#### 2.2.7. 成本效益

- 合理利用缓存减少重复计算
- 针对复杂统计逻辑采用预计算策略，降低实时计算压力
- 根据数据访问频率和重要性设计不同的存储策略

#### 2.2.8. 可观测性支持

- 提供结构化日志，包含TraceID，便于问题定位
- 暴露关键性能指标，如数据加载时间、计算耗时等
- 关键操作留下审计日志，便于问题分析

### 2.3. 主要约束与依赖

#### 技术栈约束：

- Go版本：Go 1.22.12
- 后端框架：Kratos 2.8.3
- 消息队列：Kafka 3.6
- 缓存：Redis 6.0.3
- 数据库：PostgreSQL 17, ClickHouse **********
- 搜索引擎：Elasticsearch 7.16.2
- 日志服务：项目内置日志服务
- 链路追踪：Zipkin 3.4.3

#### 外部系统依赖：

1. **用户中心服务**：
   - 提供用户认证、用户信息管理、token验证
   - 需要获取：教师基本信息、教师与班级/学科的关联关系、学生基本信息

2. **内容平台服务**：
   - 提供题目信息、题库信息、题目分类信息、试卷信息、基础知识树等
   - 需要获取：题目内容、答案、解析、难度、分类、知识点关联等数据

3. **学生端服务**：
   - 虽未在公共服务清单中明确列出，但需要从中获取学生的作业提交状态、答题记录、提问记录等原始数据 

## 3. 宏观架构设计

### 3.1. 架构风格与模式

对于教师端1期作业报告系统，选择**分层架构结合领域驱动设计(DDD)**作为主要架构风格。基于当前需求的复杂度和未来扩展考虑，首期以**单体应用**形式实现，但在内部采用模块化设计，为未来可能的微服务拆分做好准备。

选择此架构风格的理由：

1. **复杂度匹配**：分层架构适合当前系统规模和复杂度，便于团队理解和开发
2. **关注点分离**：分层设计有助于实现业务逻辑与数据访问、接口展现的关注点分离
3. **可维护性**：清晰的层次结构和责任边界，便于代码维护和bug定位
4. **渐进式演化**：支持从单体应用向微服务架构的平滑过渡，符合"从简单开始，按需扩展"的原则
5. **业务模型映射**：领域驱动设计理念有助于将复杂的学情分析业务规则映射到代码结构中

在数据处理策略上，采用**混合处理模式**：
- 实时数据处理：对于即时查询需求，如学生搜索、单题分析等
- 批量预处理：对于计算密集型统计数据，如班级整体正确率、"建议鼓励/关注"学生名单等，采用定时批处理预计算

### 3.2. 系统分层视图

#### 3.2.1. 作业报告系统的逻辑分层模型

根据教师端1期作业报告系统的具体需求和复杂度，设计了以下五层逻辑分层模型：

```mermaid
flowchart TD
    subgraph '作业报告系统 AssignmentReport System'
        APILayer[接口层 API Layer]
        ApplicationLayer[应用服务层 Application Layer]
        DomainLayer[领域层 Domain Layer]
        DataLayer[数据访问层 Data Layer]
        InfraLayer[基础设施层 Infrastructure Layer]

        APILayer --> ApplicationLayer
        ApplicationLayer --> DomainLayer
        ApplicationLayer --> DataLayer
        DomainLayer --> DataLayer
        DataLayer --> InfraLayer
        ApplicationLayer -.-> InfraLayer
        %% 应用层可能直接使用基础设施层的某些服务，如缓存
    end
```

**各层职责说明**：

1. **接口层 (API Layer)**：
   - **职责**：负责处理外部请求（HTTP/gRPC），校验输入参数，返回响应结果。作为系统的入口，将请求路由到对应的应用服务。
   - **封装变化**：处理API协议变更，前端交互变化，请求格式转换。
   - **主要组件**：HTTP/gRPC控制器，DTOs（数据传输对象），请求/响应模型，中间件（认证、日志、限流）。
   - **依赖规则**：仅依赖应用服务层的接口，不感知具体实现细节。

2. **应用服务层 (Application Layer)**：
   - **职责**：编排业务流程，协调领域对象与基础设施，处理用例场景。整合多个领域服务完成复杂业务逻辑，如生成完整的作业报告。
   - **封装变化**：业务流程变更，聚合逻辑调整，服务组合方式变化。
   - **主要组件**：应用服务(ApplicationService)，用例处理器(UseCaseHandler)，DTO转换器。
   - **依赖规则**：依赖领域层的接口和领域模型，依赖数据访问层的接口，可直接使用基础设施层的通用工具(如缓存、日志)。

3. **领域层 (Domain Layer)**：
   - **职责**：实现核心业务规则和计算逻辑，包含所有学情统计计算、规则判断等专业业务逻辑。这是系统最核心的一层，包含丰富的业务规则。
   - **封装变化**：业务规则变化，计算公式调整，策略逻辑变更。
   - **主要组件**：领域模型(Entity, ValueObject)，领域服务(DomainService)，计算策略(Strategy)，规则引擎(RuleEngine)。
   - **依赖规则**：依赖数据访问层的接口(Repository)获取所需数据，但不依赖具体实现。不依赖应用层和基础设施层。

4. **数据访问层 (Data Layer)**：
   - **职责**：实现对数据源的访问，包括数据库、缓存、外部服务API等。提供统一的数据访问接口，屏蔽底层数据源细节。
   - **封装变化**：数据源变更，存储结构调整，外部服务接口变化。
   - **主要组件**：仓储接口(Repository)及实现，数据模型(DO)，外部服务客户端适配器。
   - **依赖规则**：依赖基础设施层提供的工具和客户端，向上层提供抽象接口。

5. **基础设施层 (Infrastructure Layer)**：
   - **职责**：提供技术基础设施支持，包括数据库连接、缓存、消息队列、日志、配置等底层功能。
   - **封装变化**：技术组件升级，底层库变更，配置调整。
   - **主要组件**：数据库客户端，缓存客户端，消息队列客户端，日志工具，配置工具等。
   - **依赖规则**：不依赖其他层，仅向上提供服务。

**分层设计的合理性**：

对于教师端1期作业报告系统，选择五层架构而非更简单的三层或更复杂的微服务架构，主要基于以下考量：

1. **业务复杂度匹配**：系统涉及大量复杂的统计计算规则和业务逻辑（如"建议鼓励"、"建议关注"、"共性错题"等判断），需要一个专门的领域层来封装这些业务规则，而不是简单地混合在服务层中。

2. **数据访问分离**：系统需要从多个数据源获取数据（内部数据库、缓存、外部服务API等），独立的数据访问层有助于统一数据获取接口，简化上层调用。

3. **应用场景多样**：系统需要支持作业列表、班级报告、题目分析、学生个体报告等多种应用场景，每个场景都需要协调多个领域服务，因此需要应用服务层来编排这些业务流程。

4. **可测试性考虑**：清晰的层次分离便于单元测试和集成测试，特别是对核心计算逻辑的测试。

5. **演进考虑**：虽然首期采用单体架构，但清晰的分层设计为未来可能的微服务拆分打下基础。例如，领域层中的计算服务可能在未来独立成为一个微服务。

### 3.3. 模块/服务划分视图

#### 3.3.1. 核心模块/服务识别与职责

基于业务能力和领域边界，将系统划分为以下核心模块：

1. **作业管理模块 (AssignmentModule)**：
   - **职责**：管理作业元数据，获取作业列表，提供作业基本信息查询
   - **核心能力**：作业列表获取与筛选，作业状态查询，作业基础统计数据获取

2. **学生报告模块 (StudentReportModule)**：
   - **职责**：负责班级整体学情和学生个体表现的分析与展示
   - **核心能力**：班级整体统计数据计算，学生数据列表生成，异常数据高亮，建议鼓励/关注学生识别

3. **答题分析模块 (AnswerAnalysisModule)**：
   - **职责**：从题目维度分析班级整体作答情况
   - **核心能力**：题目统计数据计算，共性错题识别，选项分布分析，题目列表生成与筛选

4. **学生提问模块 (QuestionModule)**：
   - **职责**：管理和组织学生在学习过程中提出的问题
   - **核心能力**：提问数据获取，按内容结构组织提问，提问统计

5. **计算引擎模块 (CalculationEngineModule)**：
   - **职责**：提供各类统计计算公式和规则判断的实现
   - **核心能力**：正确率计算，进度计算，建议鼓励/关注规则判断，共性错题判断

6. **权限管理模块 (AuthorizationModule)**：
   - **职责**：控制不同角色用户对数据的访问权限
   - **核心能力**：用户角色识别，数据访问校验，权限过滤

7. **数据同步模块 (DataSyncModule)**：
   - **职责**：从外部系统采集和同步数据
   - **核心能力**：作业数据同步，答题数据同步，学生提问数据同步

#### 3.3.2. 模块/服务交互模式与数据契约

```mermaid
graph TD
    Client["客户端/前端"] -- HTTP/gRPC --> AssignmentModule["作业管理模块"]
    Client -- HTTP/gRPC --> StudentReportModule["学生报告模块"]
    Client -- HTTP/gRPC --> AnswerAnalysisModule["答题分析模块"]
    Client -- HTTP/gRPC --> QuestionModule["学生提问模块"]
    
    %% 内部模块交互
    AssignmentModule -- 获取作业基础信息 --> StudentReportModule
    AssignmentModule -- 获取作业基础信息 --> AnswerAnalysisModule
    AssignmentModule -- 获取作业基础信息 --> QuestionModule
    
    StudentReportModule -- 计算请求 --> CalculationEngineModule["计算引擎模块"]
    AnswerAnalysisModule -- 计算请求 --> CalculationEngineModule
    QuestionModule -- 统计请求 --> CalculationEngineModule
    
    %% 权限控制
    AuthorizationModule["权限管理模块"] -. 权限校验 .-> AssignmentModule
    AuthorizationModule -. 权限校验 .-> StudentReportModule
    AuthorizationModule -. 权限校验 .-> AnswerAnalysisModule
    AuthorizationModule -. 权限校验 .-> QuestionModule
    
    %% 数据同步
    DataSyncModule["数据同步模块"] -- 同步数据 --> AssignmentDB[("作业数据库")]
    
    %% 外部系统交互
    DataSyncModule -- 获取题目信息 --> QuestionService["内容平台服务"]
    DataSyncModule -- 获取用户信息 --> UCenterService["用户中心服务"]
    DataSyncModule -- 获取答题数据 --> StudentService["学生端服务"]
    
    %% 数据访问
    AssignmentModule -- 读写 --> AssignmentDB
    StudentReportModule -- 读 --> AssignmentDB
    AnswerAnalysisModule -- 读 --> AssignmentDB
    QuestionModule -- 读 --> AssignmentDB
    CalculationEngineModule -- 读 --> AssignmentDB
    
    %% 缓存
    CalculationEngineModule -- 读写 --> Cache["缓存(Redis)"]
    StudentReportModule -- 读写 --> Cache
    AnswerAnalysisModule -- 读写 --> Cache
    
    %% 模块视觉分组
    subgraph "API层模块"
        AssignmentModule
        StudentReportModule
        AnswerAnalysisModule
        QuestionModule
    end
    
    subgraph "领域层模块"
        CalculationEngineModule
        AuthorizationModule
    end
    
    subgraph "数据层"
        DataSyncModule
        AssignmentDB
        Cache
    end
    
    subgraph "外部系统"
        QuestionService
        UCenterService
        StudentService
    end
    
    %% 样式设置
    classDef apiModule fill:#A6E22E,stroke:#333,color:black;
    classDef domainModule fill:#FFD700,stroke:#333,color:black;
    classDef dataModule fill:#66D9EF,stroke:#333,color:black;
    classDef external fill:#F92672,stroke:#333,color:white;
    classDef client fill:#FD971F,stroke:#333,color:black;
    
    class AssignmentModule,StudentReportModule,AnswerAnalysisModule,QuestionModule apiModule;
    class CalculationEngineModule,AuthorizationModule domainModule;
    class DataSyncModule,AssignmentDB,Cache dataModule;
    class QuestionService,UCenterService,StudentService external;
    class Client client;
```

**核心数据契约**：

1. **作业基础信息 (AssignmentInfo)**：
   - 包含：作业ID、名称、所属课程/教材、班级信息、发布时间、截止时间、状态等
   - 用途：在作业列表中展示，作为其他模块获取详细数据的基础

2. **班级学情概览 (ClassReportOverview)**：
   - 包含：班级平均进度、班级平均正确率等整体指标
   - 用途：在学生报告Tab顶部展示班级整体情况

3. **学生学情数据 (StudentPerformanceData)**：
   - 包含：学生信息、完成进度、正确率、错题数/答题数、用时等
   - 用途：生成学生列表，进行异常数据标记

4. **学生推荐数据 (StudentRecommendationData)**：
   - 包含：建议鼓励/关注的学生列表，包括学生信息和推荐原因
   - 用途：生成"值得表扬"和"需要关注"学生名单

5. **题目作答数据 (QuestionAnswerData)**：
   - 包含：题目信息、正确率、错误人数、作答人数、平均用时等
   - 用途：生成题目列表，识别共性错题

6. **学生提问数据 (StudentQuestionData)**：
   - 包含：提问学生、提问内容、提问时间，关联的课程内容节点
   - 用途：按内容结构组织学生提问

### 3.4. 关键业务流程的高层视图

#### 3.4.1. 教师查看作业列表流程

```mermaid
sequenceDiagram
    actor Teacher as 教师
    participant API as API接口层
    participant AssignmentSvc as 作业管理服务
    participant AuthSvc as 权限服务
    participant DataAccess as 数据访问层
    participant Cache as 缓存

    Teacher->>+API: 请求作业列表（可选筛选条件）
    API->>+AuthSvc: 校验用户权限
    AuthSvc-->>-API: 返回权限范围
    
    API->>+AssignmentSvc: 获取作业列表（带权限范围）
    
    AssignmentSvc->>+Cache: 尝试获取缓存数据
    alt 缓存命中
        Cache-->>AssignmentSvc: 返回缓存的作业列表
    else 缓存未命中
        AssignmentSvc->>+DataAccess: 查询作业数据
        DataAccess-->>-AssignmentSvc: 返回原始作业数据
        AssignmentSvc->>AssignmentSvc: 计算作业核心指标（完成率、正确率等）
        AssignmentSvc->>Cache: 更新缓存
    end
    
    AssignmentSvc-->>-API: 返回处理后的作业列表数据
    API-->>-Teacher: 展示作业列表（卡片式）
    
    Teacher->>+API: 点击筛选或搜索
    API->>+AssignmentSvc: 带过滤条件获取作业列表
    AssignmentSvc->>+DataAccess: 查询过滤后的作业数据
    DataAccess-->>-AssignmentSvc: 返回过滤后的原始作业数据
    AssignmentSvc-->>-API: 返回处理后的作业列表数据
    API-->>-Teacher: 展示过滤后的作业列表
```

#### 3.4.2. 教师查看学生报告Tab流程

```mermaid
sequenceDiagram
    actor Teacher as 教师
    participant API as API接口层
    participant StudentReportSvc as 学生报告服务
    participant CalculationSvc as 计算引擎服务
    participant DataAccess as 数据访问层
    participant Cache as 缓存

    Teacher->>+API: 点击作业卡片，进入作业报告（默认学生报告Tab）
    API->>+StudentReportSvc: 获取班级学情概览
    
    StudentReportSvc->>+Cache: 尝试获取缓存的班级概览数据
    alt 缓存命中
        Cache-->>StudentReportSvc: 返回缓存数据
    else 缓存未命中
        StudentReportSvc->>+DataAccess: 获取原始作业完成数据
        DataAccess-->>-StudentReportSvc: 返回原始数据
        StudentReportSvc->>+CalculationSvc: 计算班级平均进度和正确率
        CalculationSvc-->>-StudentReportSvc: 返回计算结果
        StudentReportSvc->>Cache: 更新缓存
    end
    StudentReportSvc-->>-API: 返回班级整体学情数据
    
    API->>+StudentReportSvc: 获取"建议鼓励"学生列表
    StudentReportSvc->>+CalculationSvc: 应用"建议鼓励"规则判断
    CalculationSvc->>+DataAccess: 获取学生历史数据和当前作业数据
    DataAccess-->>-CalculationSvc: 返回数据
    CalculationSvc->>CalculationSvc: 应用连对王者、自我突破等规则
    CalculationSvc-->>-StudentReportSvc: 返回符合条件的学生列表
    StudentReportSvc-->>-API: 返回"建议鼓励"学生列表及原因
    
    API->>+StudentReportSvc: 获取"建议关注"学生列表
    StudentReportSvc->>+CalculationSvc: 应用"建议关注"规则判断
    CalculationSvc->>+DataAccess: 获取所需数据
    DataAccess-->>-CalculationSvc: 返回数据
    CalculationSvc->>CalculationSvc: 应用未开始学习、逾期未完成等规则
    CalculationSvc-->>-StudentReportSvc: 返回符合条件的学生列表
    StudentReportSvc-->>-API: 返回"建议关注"学生列表及原因
    
    API->>+StudentReportSvc: 获取学生详细列表
    StudentReportSvc->>+DataAccess: 获取班级所有学生的作业数据
    DataAccess-->>-StudentReportSvc: 返回原始数据
    StudentReportSvc->>+CalculationSvc: 计算各项指标和高亮异常数据
    CalculationSvc-->>-StudentReportSvc: 返回计算结果
    StudentReportSvc-->>-API: 返回处理后的学生列表数据
    
    API-->>-Teacher: 展示完整的学生报告Tab内容
    
    alt 教师进行搜索或筛选操作
        Teacher->>+API: 搜索学生姓名或应用筛选
        API->>+StudentReportSvc: 带条件获取学生列表
        StudentReportSvc->>StudentReportSvc: 应用过滤逻辑
        StudentReportSvc-->>-API: 返回过滤后的学生列表
        API-->>-Teacher: 展示过滤后的学生列表
    end
```

#### 3.4.3. 教师查看答题结果Tab流程

```mermaid
sequenceDiagram
    actor Teacher as 教师
    participant API as API接口层
    participant AnswerAnalysisSvc as 答题分析服务
    participant CalculationSvc as 计算引擎服务
    participant DataAccess as 数据访问层

    Teacher->>+API: 切换到"答题结果"Tab
    API->>+AnswerAnalysisSvc: 获取题目统计信息
    AnswerAnalysisSvc->>+DataAccess: 获取作业题目数据
    DataAccess-->>-AnswerAnalysisSvc: 返回题目基础数据
    AnswerAnalysisSvc->>+CalculationSvc: 计算题目正确率、识别共性错题
    CalculationSvc-->>-AnswerAnalysisSvc: 返回计算结果和共性错题标记
    AnswerAnalysisSvc-->>-API: 返回处理后的题目统计数据
    
    API->>+AnswerAnalysisSvc: 获取题目列表
    AnswerAnalysisSvc->>+DataAccess: 获取题目详细数据
    DataAccess-->>-AnswerAnalysisSvc: 返回原始题目数据和学生作答数据
    AnswerAnalysisSvc->>+CalculationSvc: 计算各题目的正确率、错误人数、作答人数
    CalculationSvc-->>-AnswerAnalysisSvc: 返回计算结果
    AnswerAnalysisSvc-->>-API: 返回处理后的题目列表数据
    API-->>-Teacher: 展示题目统计和列表
    
    alt 教师应用筛选或搜索
        Teacher->>+API: 开启"只看共性错题"或搜索题目关键词
        API->>+AnswerAnalysisSvc: 带筛选条件请求题目列表
        AnswerAnalysisSvc->>AnswerAnalysisSvc: 应用过滤逻辑
        AnswerAnalysisSvc-->>-API: 返回过滤后的题目列表
        API-->>-Teacher: 展示过滤后的题目列表
    end
    
    Teacher->>+API: 点击某题目的"查看"按钮
    API->>+AnswerAnalysisSvc: 获取题目详情和作答分析
    AnswerAnalysisSvc->>+DataAccess: 获取题目完整信息和学生作答数据
    DataAccess-->>-AnswerAnalysisSvc: 返回详细数据
    AnswerAnalysisSvc->>+CalculationSvc: 计算选项分布和学生作答情况
    CalculationSvc-->>-AnswerAnalysisSvc: 返回计算结果
    AnswerAnalysisSvc-->>-API: 返回题目详情和作答分析数据
    API-->>-Teacher: 展示题目详情页面
```

#### 3.4.4. 教师查看学生提问Tab流程

```mermaid
sequenceDiagram
    actor Teacher as 教师
    participant API as API接口层
    participant QuestionSvc as 学生提问服务
    participant DataAccess as 数据访问层

    Teacher->>+API: 切换到"学生提问"Tab
    API->>+QuestionSvc: 获取内容结构与提问统计
    QuestionSvc->>+DataAccess: 获取作业关联的课程内容结构
    DataAccess-->>-QuestionSvc: 返回内容结构数据
    QuestionSvc->>+DataAccess: 获取学生提问统计
    DataAccess-->>-QuestionSvc: 返回提问统计数据
    QuestionSvc-->>-API: 返回内容结构和提问统计
    API-->>-Teacher: 展示内容结构和各节点提问数量
    
    Teacher->>+API: 点击某个有提问的内容节点
    API->>+QuestionSvc: 获取该节点下的提问列表
    QuestionSvc->>+DataAccess: 查询指定节点的提问数据
    DataAccess-->>-QuestionSvc: 返回提问数据
    QuestionSvc-->>-API: 返回处理后的提问列表
    API-->>-Teacher: 展示提问列表
```

#### 3.4.5. 教师查看特定学生的作业报告流程

```mermaid
sequenceDiagram
    actor Teacher as 教师
    participant API as API接口层
    participant StudentReportSvc as 学生报告服务
    participant AnswerAnalysisSvc as 答题分析服务
    participant QuestionSvc as 学生提问服务
    participant DataAccess as 数据访问层

    Teacher->>+API: 在学生列表中点击某学生的"查看详情"
    API->>+StudentReportSvc: 获取学生个体作业概览
    StudentReportSvc->>+DataAccess: 获取学生基本信息和作业完成情况
    DataAccess-->>-StudentReportSvc: 返回学生数据
    StudentReportSvc-->>-API: 返回学生作业概览数据
    
    API->>+AnswerAnalysisSvc: 获取学生答题结果数据
    AnswerAnalysisSvc->>+DataAccess: 获取学生在该作业中的所有答题记录
    DataAccess-->>-AnswerAnalysisSvc: 返回答题数据
    AnswerAnalysisSvc-->>-API: 返回处理后的答题结果数据
    
    API->>+QuestionSvc: 获取学生提问数据（若有）
    QuestionSvc->>+DataAccess: 查询学生在该作业关联课程中的提问
    DataAccess-->>-QuestionSvc: 返回提问数据
    QuestionSvc-->>-API: 返回处理后的提问数据
    
    API-->>-Teacher: 展示学生个体作业报告页面
    
    alt 教师切换Tab
        Teacher->>+API: 在个体报告中切换Tab
        API-->>-Teacher: 展示对应Tab的内容
    end
```

### 3.5. 数据架构概述

#### 3.5.1. 主要数据存储选型与理由

1. **PostgreSQL**: 
   - **用途**：存储作业元数据、学生作答数据、用户权限信息等结构化数据
   - **选型理由**：强大的事务支持和ACID特性，确保数据一致性；丰富的索引类型和查询优化能力，支持复杂的数据查询需求

2. **Redis**:
   - **用途**：缓存热点数据（如班级统计信息、常用作业列表）；存储临时计算结果
   - **选型理由**：高性能的内存数据库，适合缓存和临时数据存储；支持多种数据结构，适应不同缓存场景

3. **ClickHouse**:
   - **用途**：存储大量的统计数据和预计算结果，支持高效的聚合查询
   - **选型理由**：列式存储引擎，适合OLAP场景；高性能的聚合计算能力，满足复杂报表和数据分析需求

4. **Elasticsearch**:
   - **用途**：索引题目内容和学生提问数据，支持全文搜索
   - **选型理由**：强大的全文搜索和分析能力；适合处理半结构化和非结构化数据

5. **Kafka**:
   - **用途**：实现数据同步和事件通知，如作业状态变更、计算任务触发
   - **选型理由**：高吞吐量的消息队列，适合数据流处理；支持消息持久化，确保数据不丢失

#### 3.5.2. 核心领域对象/数据结构的概念定义

1. **作业(Assignment)**：
   - **核心属性**：作业ID、作业名称、所属课程/教材、班级信息、发布时间、截止时间、作业状态
   - **业务含义**：教师布置给学生的学习任务单元，是学情数据的主要来源和组织单位
   - **关联关系**：与班级、题目、学生作答记录关联

2. **学生作答记录(StudentAnswer)**：
   - **核心属性**：学生ID、作业ID、题目ID、作答内容、作答状态(对/错/部分正确)、作答时间、用时
   - **业务含义**：记录学生对特定题目的作答情况，是计算正确率、用时等指标的基础数据
   - **关联关系**：与学生、作业、题目关联

3. **题目(Question)**：
   - **核心属性**：题目ID、题干、选项、答案、解析、题型、难度、所属知识点
   - **业务含义**：学习和测评的基本内容单元，是进行知识点掌握度评估的载体
   - **关联关系**：与作业、知识点、学生作答记录关联

4. **班级(Class)**：
   - **核心属性**：班级ID、班级名称、年级、学科、学生列表
   - **业务含义**：学生的组织单位，作业报告的主要分析维度之一
   - **关联关系**：与教师、学生、作业关联

5. **学生(Student)**：
   - **核心属性**：学生ID、姓名、学号、班级、分层标签
   - **业务含义**：学习的主体，作业报告的分析对象
   - **关联关系**：与班级、作答记录、提问记录关联

6. **学生提问(StudentQuestion)**：
   - **核心属性**：提问ID、学生ID、提问内容、提问时间、关联内容节点
   - **业务含义**：学生在学习过程中的疑问和困惑，是教师了解学生学习状况的重要信息源
   - **关联关系**：与学生、课程内容节点关联

#### 3.5.3. 数据一致性与同步策略

1. **数据同步策略**：
   - **定时批量同步**：从上游系统（内容平台、学生端）定时批量获取题目、作业、学生作答等基础数据
   - **事件驱动同步**：通过Kafka消息队列，在上游系统数据发生变更时（如学生提交作业、修改答案）触发数据同步
   - **增量同步**：仅同步上次同步后新增或变更的数据，减少数据传输量

2. **数据一致性保障**：
   - **作业状态与学生作答数据**：采用最终一致性模型，通过事件通知机制确保状态最终同步
   - **统计数据与原始数据**：使用版本标记和定时校验机制，确保统计结果与原始数据的一致性
   - **缓存一致性**：采用TTL(Time-To-Live)策略和主动失效机制，确保缓存数据不会长时间偏离真实数据

3. **特殊场景处理**：
   - **学生提交作业后的统计更新**：使用延迟计算策略，避免频繁更新统计结果
   - **大量学生同时提交作业**：使用队列机制缓冲统计计算请求，避免系统过载
   - **历史数据修正**：提供后台任务重新计算历史数据，修正可能的数据偏差 

## 4. Golang技术栈与关键库选型

### 4.1. Go语言版本

- **选择**: Go 1.22.12（按照技术栈约束）
- **选型理由**:
  - 充分利用最新的语言特性和性能优化
  - 利用增强的并发控制和错误处理机制处理复杂的统计计算逻辑
  - 标准库中的新功能（如官方支持的泛型、slices包等）可简化开发

### 4.2. Web/RPC框架

- **选择**: Kratos 2.8.3（按照技术栈约束）
- **选型理由**:
  - 完善的微服务生态，支持HTTP和gRPC协议，便于实现RESTful API和服务间通信
  - 内置中间件链和插件机制，便于实现认证、日志、链路追踪等横切关注点
  - 配置管理、依赖注入、错误处理等特性简化了服务开发
  - 符合公司技术栈规范，便于团队协作和技术共享

### 4.3. 数据库驱动与ORM

- **选择**:
  - PostgreSQL 17: gorm.io/driver/postgres + gorm.io/gorm
  - ClickHouse **********: github.com/ClickHouse/clickhouse-go/v2
- **选型理由**:
  - GORM提供高级对象映射和关系处理，简化数据库操作，提高开发效率
  - ClickHouse-go提供高性能的ClickHouse数据操作能力，适合处理大量统计数据
  - 两者结合满足既需要关系型数据处理又需要高性能分析查询的场景

### 4.4. 消息队列客户端库

- **选择**: github.com/segmentio/kafka-go (Kafka 3.6)
- **选型理由**:
  - 纯Go实现，避免CGO依赖，简化部署和维护
  - 提供丰富的功能，如消息压缩、批量处理、重试机制等
  - 良好的性能和稳定性，适合处理作业状态变更和统计计算任务等事件驱动场景

### 4.5. 缓存和辅助工具库

- **选择**:
  - 缓存: github.com/go-redis/redis/v8
  - JSON处理: google.golang.org/protobuf (用于API数据结构) + github.com/mailru/easyjson (用于高性能JSON处理)
  - 日志: github.com/sirupsen/logrus + github.com/lestrrat-go/file-rotatelogs
  - 请求处理: github.com/go-resty/resty/v2 (外部HTTP API调用)
- **选型理由**:
  - Redis客户端提供丰富的数据结构操作API，适合实现多种缓存模式
  - EasyJSON提供比标准库更高效的JSON编解码，优化API响应性能
  - Logrus提供结构化日志输出，便于日志分析和问题追踪
  - Resty简化HTTP请求处理，便于调用外部系统API

## 5. 关键设计决策

### 5.1. 数据计算策略

#### 5.1.1. 延迟计算 vs. 预计算

- **决策**: 对于写操作较少但查询频繁的数据（如班级整体统计、"建议鼓励/关注"名单）采用**预计算+缓存**策略；对于个性化查询和筛选结果采用**实时计算**策略。
- **理由**:
  - 预计算显著减少高频访问场景（如班级报告、作业列表）的响应时间
  - 作业数据相对稳定（学生提交后较少修改），适合预计算
  - 个性化查询和筛选条件多样，预计算成本高，实时计算更合适

#### 5.1.2. 统计规则引擎设计

- **决策**: 采用**策略模式+组合模式**设计灵活的统计规则引擎，使各类统计规则可独立配置和组合。
- **理由**:
  - 便于添加新的统计规则（如新的"建议鼓励"类型）而不影响现有代码
  - 支持规则的组合使用，如同时应用多个筛选条件
  - 便于单元测试和规则调优

### 5.2. 权限控制方案

- **决策**: 采用**基于角色的访问控制(RBAC) + 数据范围过滤**的权限控制方案。
- **理由**:
  - RBAC便于管理不同角色（学科教师、班主任、学科组长等）的权限
  - 数据范围过滤确保教师只能访问其授权范围内的数据
  - 在服务层统一实现权限控制，避免在各API中重复编写权限检查代码

### 5.3. 缓存策略

- **决策**: 采用**多级缓存策略**，包括：API响应缓存、计算结果缓存、查询结果缓存。
- **理由**:
  - API响应缓存减少重复计算，提高前端访问速度
  - 计算结果缓存保存耗时统计结果，避免频繁重复计算
  - 查询结果缓存减轻数据库压力，提高查询响应速度

### 5.4. 扩展性考量

- **决策**: 虽然首期采用单体应用架构，但在内部采用**模块化设计**和**清晰的服务边界**，为未来微服务拆分做准备。
- **理由**:
  - 初期用户量和功能相对有限，单体架构开发效率更高
  - 模块化设计为未来按业务领域拆分微服务奠定基础
  - 清晰的服务边界减少未来拆分的重构成本

## 6. 部署模型与优化考量

### 6.1. 部署拓扑

```mermaid
graph TD
    Browser["用户浏览器"] --> LoadBalancer["负载均衡器"]
    LoadBalancer --> AppInstance1["应用实例1"]
    LoadBalancer --> AppInstance2["应用实例2"]
    LoadBalancer --> AppInstanceN["应用实例N..."]
    
    subgraph "数据存储层"
        AppInstance1 --> Redis["Redis缓存集群"]
        AppInstance2 --> Redis
        AppInstanceN --> Redis
        
        AppInstance1 --> PostgreSQL["PostgreSQL主从集群"]
        AppInstance2 --> PostgreSQL
        AppInstanceN --> PostgreSQL
        
        AppInstance1 --> ClickHouse["ClickHouse集群"]
        AppInstance2 --> ClickHouse
        AppInstanceN --> ClickHouse
        
        AppInstance1 --> Elasticsearch["Elasticsearch集群"]
        AppInstance2 --> Elasticsearch
        AppInstanceN --> Elasticsearch
    end
    
    subgraph "消息与监控层"
        AppInstance1 --> Kafka["Kafka集群"]
        AppInstance2 --> Kafka
        AppInstanceN --> Kafka
        
        AppInstance1 -.-> Zipkin["Zipkin (链路追踪)"]
        AppInstance2 -.-> Zipkin
        AppInstanceN -.-> Zipkin
    end
    
    subgraph "外部服务"
        AppInstance1 --> QuestionSvc["内容平台服务"]
        AppInstance2 --> QuestionSvc
        AppInstanceN --> QuestionSvc
        
        AppInstance1 --> UCenterSvc["用户中心服务"]
        AppInstance2 --> UCenterSvc
        AppInstanceN --> UCenterSvc
        
        AppInstance1 --> StudentSvc["学生端服务"]
        AppInstance2 --> StudentSvc
        AppInstanceN --> StudentSvc
    end
```

### 6.2. 系统优化考量

#### 6.2.1. 性能优化

1. **请求处理优化**:
   - 使用连接池管理数据库连接
   - API层实现请求限流和熔断，防止系统过载
   - 对大量数据的API响应进行分页处理

2. **数据访问优化**:
   - 针对高频查询添加合适的索引
   - 使用读写分离技术分担数据库负载
   - 复杂统计查询使用ClickHouse提高聚合计算性能

3. **缓存优化**:
   - 对热点数据（如班级统计信息）进行缓存
   - 合理设置缓存过期时间和更新策略
   - 使用多级缓存减少重复计算和数据库访问

#### 6.2.2. 可伸缩性优化

1. **无状态设计**:
   - 所有应用实例无本地状态，支持水平扩展
   - 使用共享缓存存储会话和临时数据

2. **异步处理**:
   - 将耗时的统计计算任务异步处理
   - 使用消息队列解耦数据更新和统计计算

3. **动态扩缩容**:
   - 支持根据负载动态调整应用实例数量
   - 监控系统资源使用情况，及时发现性能瓶颈

#### 6.2.3. 可靠性优化

1. **错误处理与重试**:
   - 对外部服务调用实现重试机制
   - 完善的错误日志和监控，及时发现问题

2. **数据备份与恢复**:
   - 定期备份关键数据
   - 制定数据恢复计划和流程

3. **降级策略**:
   - 当系统负载过高时，实施功能降级策略
   - 优先保障核心功能的可用性

## 7. 风险与缓解策略

### 7.1. 技术风险

| 风险 | 描述 | 影响 | 缓解策略 |
| --- | --- | --- | --- |
| 复杂计算性能问题 | 涉及大量学生和题目的统计计算可能导致性能瓶颈 | 系统响应缓慢，影响用户体验 | 1. 使用预计算和缓存<br>2. 针对大数据量场景优化算法<br>3. 使用ClickHouse处理复杂聚合计算 |
| 数据同步一致性 | 从多个上游系统获取数据，可能出现数据不一致 | 统计结果不准确，影响教学决策 | 1. 实现数据一致性校验机制<br>2. 采用事务和版本控制<br>3. 定期全量同步修正偏差 |
| 缓存与数据库一致性 | 缓存数据可能与数据库不同步 | 用户看到过时数据 | 1. 合理设置TTL<br>2. 实现主动缓存失效机制<br>3. 采用写操作级联更新缓存 |
| 外部服务依赖故障 | 对内容平台、用户中心等外部服务的依赖可能导致连锁故障 | 系统部分功能不可用 | 1. 实现断路器模式<br>2. 设置请求超时和重试策略<br>3. 缓存关键外部数据 |

### 7.2. 业务风险

| 风险 | 描述 | 影响 | 缓解策略 |
| --- | --- | --- | --- |
| 统计规则准确性 | "建议鼓励"和"建议关注"等规则可能不够准确 | 干扰教师判断，影响教学决策 | 1. 与教育专家合作制定规则<br>2. A/B测试验证规则有效性<br>3. 收集教师反馈持续优化 |
| 数据隐私安全 | 学生学习数据涉及隐私保护 | 违反数据保护规定，造成法律和声誉风险 | 1. 严格的权限控制<br>2. 敏感数据加密存储<br>3. 完整的审计日志 |
| 业务规则变更 | 教学评价标准和规则可能变化 | 系统难以适应新规则，需要大量修改 | 1. 可配置的规则引擎<br>2. 模块化设计易于更新<br>3. 预留配置接口而非硬编码规则 |
| 用户接受度 | 教师可能对系统生成的建议持保留态度 | 系统价值无法充分体现 | 1. 提供规则说明和理据<br>2. 允许教师反馈和调整<br>3. 简化用户界面，提升易用性 |

## 8. 架构文档自查清单

### 8.1 架构完备性自查清单

| 架构要素 | 检查项 | 结果 | 备注 |
| --- | --- | --- | --- |
| **整体架构** | 架构风格与模式选择是否合理 | ✅ | 采用分层架构+DDD，适合当前系统复杂度 |
|  | 是否定义了清晰的系统边界和范围 | ✅ | 明确了系统覆盖和不覆盖的范围 |
|  | 是否考虑了未来的扩展性 | ✅ | 模块化设计为未来微服务拆分做准备 |
| **分层设计** | 是否定义了清晰的逻辑分层 | ✅ | 五层架构，每层职责明确 |
|  | 层间依赖是否合理 | ✅ | 严格控制依赖方向，避免循环依赖 |
|  | 是否遵循了关注点分离原则 | ✅ | 业务逻辑、数据访问、基础设施分离 |
| **模块划分** | 模块职责是否明确 | ✅ | 根据业务能力划分核心模块，职责清晰 |
|  | 模块间通信机制是否定义 | ✅ | 定义了同步调用和异步消息两种通信方式 |
|  | 是否考虑了模块的内聚性和耦合度 | ✅ | 高内聚低耦合，模块内组件紧密相关 |
| **数据架构** | 是否选择了合适的数据存储方案 | ✅ | 混合使用PostgreSQL、Redis、ClickHouse等 |
|  | 是否定义了核心领域对象 | ✅ | 定义了作业、学生、题目等核心领域对象 |
|  | 是否考虑了数据一致性策略 | ✅ | 定义了同步策略和一致性保障机制 |
| **业务流程** | 是否覆盖了所有核心业务流程 | ✅ | 覆盖了PRD中的所有核心场景 |
|  | 流程图是否清晰展示了组件协作 | ✅ | 使用Mermaid图表清晰展示了交互流程 |
|  | 是否考虑了异常流程处理 | ✅ | 在流程图中包含了异常分支处理 |
| **技术选型** | 是否遵循了技术栈约束 | ✅ | 严格遵循技术栈约束进行选型 |
|  | 技术选型是否有明确理由 | ✅ | 为每个技术选择提供了选型理由 |
|  | 是否考虑了技术的兼容性和集成 | ✅ | 考虑了各组件间的兼容性和集成方式 |
| **性能与扩展性** | 是否考虑了性能优化策略 | ✅ | 定义了性能优化考量和具体策略 |
|  | 是否设计了可伸缩的架构 | ✅ | 无状态设计支持水平扩展 |
|  | 是否考虑了高并发场景 | ✅ | 使用缓存和异步处理应对高并发 |
| **安全与可靠性** | 是否考虑了权限控制 | ✅ | 采用RBAC+数据范围过滤的权限控制方案 |
|  | 是否有数据备份与恢复策略 | ✅ | 定义了数据备份和恢复策略 |
|  | 是否有风险缓解策略 | ✅ | 识别了技术和业务风险，并提供缓解策略 |

### 8.2 需求-架构完备性自查清单

| PRD对应章节/需求点 | 架构支持方式 | 结果 | 备注 |
| --- | --- | --- | --- |
| **2.1 P0核心功能 - 作业列表功能** | 作业管理模块提供作业列表获取、筛选和搜索功能 | ✅ | 包含卡片式设计所需的数据支持 |
| **2.1 P0核心功能 - 学生报告Tab** | 学生报告模块提供班级概览、"建议鼓励/关注"学生识别、学生列表生成等功能 | ✅ | 支持完成进度、正确率、错题数、用时等指标计算 |
| **2.1 P0核心功能 - 答题结果Tab** | 答题分析模块提供题目统计、共性错题识别、题目列表生成等功能 | ✅ | 支持题目正确率、错误人数、作答人数等指标计算 |
| **2.1 P0核心功能 - 学生提问Tab** | 学生提问模块提供提问数据组织、内容结构展示、提问列表生成等功能 | ✅ | 支持按知识点或题目汇总提问 |
| **2.1 P0核心功能 - 个体学生作业报告** | 学生报告模块和答题分析模块协作提供个体学生作业报告功能 | ✅ | 支持查看特定学生的详细"答题结果"和"学生提问" |
| **2.1 P0核心功能 - 策略说明功能集成** | 计算引擎模块提供规则判断和策略评估能力，支持"鼓励"、"关注"、"共性错题"等策略生成 | ✅ | 可扩展设计支持未来添加新策略 |
| **2.2 辅助功能 - 数据筛选与搜索** | 各功能模块支持基于类型、班级、学科、布置日期等的筛选和搜索 | ✅ | 设计了灵活的查询条件组合机制 |
| **2.2 辅助功能 - 数据导出** | 学生报告模块支持将学生列表数据导出 | ✅ | 支持导出为CSV/Excel格式 |
| **3.1-3.3 计算规则与公式** | 计算引擎模块实现所有统计规则和计算公式，确保数据一致性 | ✅ | 使用策略模式实现灵活的规则配置 |
| **3.4 "建议鼓励"的推荐类型与规则** | 计算引擎模块实现连对王者、自我突破、连对突破等规则 | ✅ | 支持所有PRD定义的鼓励规则 |
| **3.5 "建议关注"的异常行为规则** | 计算引擎模块实现未开始学习、逾期未完成、学习表现偏离等规则 | ✅ | 支持所有PRD定义的关注规则 |
| **7. 权限管理** | 权限管理模块实现基于角色的访问控制和数据范围过滤 | ✅ | 支持学科教师、班主任、学科组长等不同角色的权限控制 |
| **8.1 性能需求** | 采用缓存、预计算、异步处理等策略优化性能 | ✅ | 满足页面加载速度、数据查询效率、并发用户数等要求 |
| **8.2 安全性需求** | 实现数据隔离、访问控制、防注入、数据备份等安全机制 | ✅ | 确保数据安全和隐私保护 |
| **8.3 可用性需求** | 采用高可用架构设计，确保系统稳定性 | ✅ | 满足99.9%可用性要求 |
| **8.4 可扩展性与可维护性需求** | 模块化设计、清晰的接口定义、代码规范等确保可维护性 | ✅ | 支持未来功能扩展和系统演进 
PRD - 课中练习推题策略 - V1.0

# 一、背景和目标

##  需求背景

在AI课堂教学过程中，为了提升学生的专注度和课堂互动感，避免单向知识输入，需在课程中合理穿插练习题组件。

课中练习题主要用于巩固教师讲解内容，帮助学生即时检验对知识点的理解情况，同时通过适当的练习密度与反馈机制，增强课堂节奏感与参与感。

因此，需要制定一套清晰可执行的课中推题策略，确保练习题推送符合教学节奏，兼顾知识巩固的目的。

## 项目收益

1. 提升学习效果：通过实时练习巩固课堂所学内容，帮助学生在知识点讲解后及时验证理解，降低遗忘率。
1. 增强课堂互动与专注度：通过答题互动打破单向讲授模式，提高学生参与感和课堂活跃度，延长课堂专注时长。
1. 及时发现学习问题：通过答错推送相似题，帮助学生暴露理解误区，实现即时纠偏，提高学习精准度。
## 覆盖用户

使用新AI课学习的全部用户



# 二、内容数据结构

## 内容结构

![board_AFnaw4y5nhDgPybpTZXcJdthnTe](https://static.test.xiaoluxue.cn/demo41/prd_images/1746846294952.png)

- 每节课会配置1-N个练习组件，在配课环节插入到不同的课中组件之中。
- 每个练习中包含1-N个题目，题目有「单题」和「相似题组」两种类型，相似题组中至少有2道相似题目。
- 题目之间由教研控制相对顺序，决定了不同题组的推荐顺序
- 每个题目都有知识点内的难度等级标签，课中练习题目的难度较低，基本为L1，L2难度


# 三、策略方案

## 题目推荐策略

### 整体流程

![board_R7zlwS1uOhjgUAbT5MZcdQNan05](https://static.test.xiaoluxue.cn/demo41/prd_images/1746846295430.png)

### 推荐策略

1. 一个练习组件中的多个题目，需要按后台配置顺序依次作答。
1. 相似题题组中，可随机推出一题
1. 如果用户答对，则继续推出下一题。
1. 如果用户答错，有相似题则推出相似题，没有相似题则继续推出下一题。
1. 答错后的相似题只推出一次，无论用户是否回答正确都继续推出后续题目


## 未来可拓展

1. 区分错误的严重程度或错误类型
如果学生答得非常离谱（比如选了明显错误选项），推两道不同角度的相似题；如果是小错误（比如粗心），推一道即可。
1. 个性化推题
基于实时答题表现调整题目数量的空间，比如连续答对三题可以略过后面一部分简单题，加快节奏。


























# 巩固练习相关策略产品需求文档

## 1. 需求背景与目标

### 1.1 需求目标
- 通过巩固练习策略的实现，为不同能力水平的学生提供匹配的练习题，避免题目难度不合适导致的信心打击或时间浪费
- 让学生更有效地掌握课程重点知识点，通过对典型题的优先覆盖与逐步难度推进，提升知识掌握度和迁移能力
- 为后续算法推荐提供结构化数据积累，便于未来迭代上线IRT、知识追踪等自动推荐机制
- 提升整体产品智能感与完成率，通过系统"主动推题、智能响应"，增强学生对产品智能化的感知

## 2. 功能范围

### 2.1 核心功能
- 题目推荐策略：根据用户当前掌握度和题目难度系数智能推荐题目，使学生始终获得适合其能力水平的练习内容
  - 优先级：高
  - 依赖关系：无
- 难度适配调整：基于用户连续答题表现动态调整题目难度，确保学习体验的连贯性和挑战性的平衡
  - 优先级：高
  - 依赖关系：依赖于题目推荐策略
- 再练一次策略：针对掌握度不足的知识点提供再次练习机会，重点覆盖错题和薄弱题型
  - 优先级：中
  - 依赖关系：依赖于题目推荐策略

### 2.2 辅助功能
- 专注度评分系统：通过检测用户答题行为判断是否认真答题，并在必要时进行提醒或熔断练习
- 熔断策略：在用户连续做错题目或专注度过低时，触发练习熔断，避免无效练习
- 报告页引导：根据用户掌握度和练习表现，智能决定是否引导用户进行再练一次

### 2.3 非本期功能
- 模型驱动的推荐算法：如IRT、知识追踪等高级算法推荐机制
- 个性化学习路径：根据长期学习数据定制的完整学习路径规划
- 社交化学习功能：与同班或同水平学生的练习对比和互动

## 3. 计算规则与公式

- 预估题目数量计算
  - 基础答题量 = 10 * 难度系数 * 考频系数（四舍五入取整，范围[8,14]）
    - 参数说明：
      - 难度系数：知识点的难度系数，取值范围[0-1]
      - 考频系数：知识点的考试频率系数
    - 规则：
      - 动态调整系数A = 1 + α×(max(0, θ_tar - θ_cur)/θ_tar)
      - α为经验调节系数，默认取0.6
      - θ_tar为该知识点对应的目标掌握度
      - θ_cur为学生对该知识点的当前掌握度
      - 预估题目数量 = 基础答题量 * 动态调整系数A
      - 下限：题组数量 * 2（确保每个题组至少有2道题）
      - 上限：15（避免单次练习量过多）

- 预期掌握度增量计算
  - P(答对) = sigmoid(4 × (θ - β))
    - 参数说明：
      - θ为学生的掌握度
      - β为题目难度系数
      - sigmoid函数表达式：S(x)=1/(1 + e^(-x))
    - 规则：
      - 预期掌握度增量 = P(答对)×答对掌握度增量 + P(答错)×答错掌握度增量
      - 答对掌握度增量 = 0.2 * (β-θ)
      - 答错掌握度增量 = -0.1 * (β-θ) * 0.5

## 4. 用户场景与故事

### 场景1：巩固练习首次进入
作为一个学生用户，我刚完成了AI课程的学习，希望巩固我所学的知识点，系统能根据我的能力水平推荐适合的题目，而不是随机给出可能过难或过简单的题目。这样我就能高效地提升对知识点的掌握程度，避免浪费时间。

**关键步骤：**
1. 用户完成AI课程学习后进入巩固练习
2. 系统计算用户对当前知识点的掌握度和预估答题量
3. 系统按照题组顺序推荐第一题，优先选择必做题
4. 用户回答题目，系统基于回答结果和用户掌握度调整后续题目难度

```mermaid
flowchart TD
    A[用户完成AI课程学习] --> B[进入巩固练习]
    B --> C[系统计算用户当前掌握度]
    C --> D[系统计算预估答题量]
    D --> E[按顺序进入第一个题组]
    E --> F{题组内是否有必做题?}
    F -- 是 --> G[从必做题中选择预期掌握度增量最大的题目]
    F -- 否 --> H[从题组中选择预期掌握度增量最大的题目]
    G --> I[用户作答]
    H --> I
    I --> J[系统记录用户答题结果并更新掌握度]
```

- 优先级：高
- 依赖关系：无

### 场景2：巩固练习题目推荐
作为一个学生用户，我希望在回答完一道题目后，系统能根据我的答题表现智能地推荐下一道题目，如果我连续答对，可以适当增加难度，连续答错则降低难度。这样能让我的练习过程既有挑战性又不至于太难，保持良好的学习节奏。

**关键步骤：**
1. 用户完成当前题目的作答
2. 系统判断用户当前题组是否达到预估答题量
3. 系统判断用户连续答题情况（连续答对/答错）调整难度
4. 系统从题组中选择预期掌握度增量最大且符合难度调整规则的题目
5. 如当前题组已完成预估答题量或没有合适题目，进入下一题组

```mermaid
flowchart TD
    A[用户完成当前题目作答] --> B{当前题组是否已达预估答题量?}
    B -- 是 --> C{还有未作答的题组?}
    C -- 是 --> D[按顺序进入下一题组]
    C -- 否 --> E[结束巩固练习]
    B -- 否 --> F{剩余可推荐题目的预期掌握度增量是否≥0.01?}
    F -- 否 --> C
    F -- 是 --> G{用户连续答题情况}
    G -- 连续答对 --> H{是否有比上一题难度更高的题目?}
    H -- 有 --> I[推荐难度更高的题目]
    H -- 没有 --> J[推荐同等难度的题目]
    G -- 连续答错 --> K{是否有比上一题难度更低的题目?}
    K -- 有 --> L[推荐难度更低的题目]
    K -- 没有 --> M[推荐同等难度的题目]
    G -- 其他 --> N[选择预期掌握度增量最大的题目]
    I --> O[用户继续作答]
    J --> O
    L --> O
    M --> O
    N --> O
    D --> P{当前题目是否为题组内首题?}
    P -- 是 --> Q{题组内是否有必做题?}
    Q -- 是 --> R[在必做题中选择预期掌握度增量最大的题目]
    Q -- 否 --> S[在题组中选择预期掌握度增量最大的题目]
    P -- 否 --> S
    R --> O
    S --> O
```

- 优先级：高
- 依赖关系：依赖于场景1

### 场景3：专注度监测与练习熔断
作为一个学生用户，我有时候会疲惫或分心，导致胡乱答题。我希望系统能够监测到我的这种状态，给予适当提醒，避免我在不专注的状态下继续无效练习，浪费时间。

**关键步骤：**
1. 用户开始巩固练习，系统初始化专注度分值为10分
2. 系统监测用户答题行为（作答时长、正确率等）
3. 当用户行为异常（如快速作答且错误、连续做错题目）时，专注度分值扣减
4. 根据专注度分值触发不同级别的提醒或熔断处理

```mermaid
flowchart TD
    A[用户开始巩固练习] --> B[系统初始化专注度分值为10分]
    B --> C[用户作答题目]
    C --> D{是否存在异常答题行为?}
    D -- 是 --> E[专注度分值-1]
    D -- 否 --> F[保持专注度分值不变]
    E --> G{专注度分值是多少?}
    F --> C
    G -- 等于7 --> H[触发第一次提醒]
    G -- 等于5 --> I[触发第二次提醒]
    G -- 等于3 --> J[触发熔断]
    G -- 其他 --> C
    H --> C
    I --> C
    J --> K[结束当前巩固练习]
```

- 优先级：中
- 依赖关系：依赖于场景1和场景2

### 场景4：再练一次引导与推荐
作为一个学生用户，当我完成巩固练习后，如果发现我对某个知识点的掌握度仍然不足，我希望系统能建议我再练一次，并且在再练一次中优先展示我之前做错的题目，帮助我更好地掌握薄弱知识点。

**关键步骤：**
1. 用户完成巩固练习，查看练习报告
2. 系统判断用户掌握度和正确率是否达标
3. 如未达标，系统判断题库中是否有足够的未作答题目支持再练一次
4. 系统展示"再练一次"入口，用户点击进入
5. 系统优先推荐用户在巩固练习中答错的题目，然后是其他高价值题目

```mermaid
flowchart TD
    A[用户完成巩固练习] --> B[系统生成练习报告]
    B --> C{用户掌握度<目标掌握度的70%且正确率<70%?}
    C -- 否 --> D[不展示再练一次入口]
    C -- 是 --> E{是否有足够未作答题目?}
    E -- 否 --> D
    E -- 是 --> F[展示再练一次入口]
    F --> G[用户点击再练一次]
    G --> H[筛选巩固练习中有错题的题组]
    H --> I[按顺序进入题组]
    I --> J[在错题中选择预期掌握度增量最大的题目]
    J --> K[用户作答]
    K --> L{当前题组是否已达预估答题量?}
    L -- 是 --> M{还有未作答的题组?}
    M -- 是 --> I
    M -- 否 --> N[结束再练一次]
    L -- 否 --> O{剩余可推荐题目的预期掌握度增量是否≥0.01?}
    O -- 是 --> P[在题组中选择预期掌握度增量最大的题目]
    O -- 否 --> M
    P --> K
```

- 优先级：中
- 依赖关系：依赖于场景1和场景2

## 5. 业务流程图

### 整体业务流程

```mermaid
flowchart TD
    Start[用户进入AI课程] --> A[完成课程学习]
    A --> B[进入巩固练习]
    B --> C[系统计算预估题量和推荐策略]
    C --> D[按题组顺序推荐题目]
    D --> E[用户作答]
    E --> F[系统监测专注度]
    F --> G{专注度是否过低?}
    G -- 是 --> H[触发熔断]
    G -- 否 --> I{当前题组是否完成?}
    I -- 否 --> J[根据答题情况推荐下一题]
    I -- 是 --> K{是否还有未完成题组?}
    K -- 是 --> L[进入下一题组]
    K -- 否 --> M[生成练习报告]
    J --> E
    L --> D
    H --> M
    M --> N{是否需要再练一次?}
    N -- 是 --> O[展示再练一次入口]
    N -- 否 --> P[结束练习流程]
    O --> Q[用户点击再练一次]
    Q --> R[执行再练一次推题策略]
    R --> S[用户完成再练一次]
    S --> P
```

### 题目推荐策略流程

```mermaid
flowchart TD
    A[巩固练习推题] --> B{本次练习是否有未作答的题组?}
    B -- 否 --> Z[结束]
    B -- 是 --> C[按顺序进入题组]
    C --> D{当前题目是否为题组内首题?}
    D -- 是 --> E{题组内是否有必做题?}
    D -- 否 --> F[在题组中选择「预期掌握度增量」最大的题目]
    E -- 是 --> G[在必做题中选择「预期掌握度增量」最大的题目]
    E -- 否 --> F
    F --> H{当前题组是否已达预估答题量?}
    G --> H
    H -- 是 --> B
    H -- 否 --> I{剩余可推荐题目的预期掌握度增量是否≥0.01?}
    I -- 是 --> F
    I -- 否 --> B
```

### 再练一次推题策略流程

```mermaid
flowchart TD
    A[再练一次推题] --> B[筛选巩固练习环节有错题的题组]
    B --> C{本次练习是否有未作答的题组?}
    C -- 否 --> Z[结束]
    C -- 是 --> D[按顺序进入题组]
    D --> F[在错题中选择「预期掌握度增量」最大的题目]
    F --> G{当前题组是否已达预估答题量?}
    G -- 是 --> C
    G -- 否 --> H{剩余可推荐题目的预期掌握度增量是否≥0.01?}
    H -- 是 --> I[在题组中选择「预期掌握度增量」最大的题目]
    I --> G
    H -- 否 --> C
```

### 专注度监测流程

```mermaid
stateDiagram-v2
    [*] --> 正常状态: 初始专注度10分
    正常状态 --> 提醒状态1: 专注度降至7分
    正常状态 --> 正常状态: 正常答题
    提醒状态1 --> 提醒状态2: 专注度降至5分
    提醒状态1 --> 正常状态: 行为改善
    提醒状态2 --> 熔断状态: 专注度降至3分
    提醒状态2 --> 提醒状态1: 行为轻微改善
    熔断状态 --> [*]: 结束当前练习
    
    state 正常状态 {
        [*] --> 监测答题行为
        监测答题行为 --> 判断是否异常
        判断是否异常 --> 扣减专注度: 异常行为
        判断是否异常 --> 保持专注度: 正常行为
        扣减专注度 --> 监测答题行为
        保持专注度 --> 监测答题行为
    }
    
    state 提醒状态1 {
        [*] --> 显示第一次提醒
        显示第一次提醒 --> 继续监测
        继续监测 --> [*]
    }
    
    state 提醒状态2 {
        [*] --> 显示第二次提醒
        显示第二次提醒 --> 继续监测
        继续监测 --> [*]
    }
    
    state 熔断状态 {
        [*] --> 触发熔断
        触发熔断 --> 结束练习
        结束练习 --> [*]
    }
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：题目推荐计算的响应时间不超过 1 秒
- 并发用户：系统需支持 1000 个并发用户，保证推荐算法性能不衰减
- 数据量：系统需支持百万级用户的掌握度数据和题目难度系数数据的存储和查询，响应时间不超过 1 秒

### 6.2 安全需求
- 权限控制：用户掌握度数据只允许用户本人和授权教师查看
- 数据加密：用户作答记录和掌握度数据需要加密存储
- 操作审计：对用户答题行为异常的判断记录完整日志，包括判断依据和触发的提醒或熔断操作

## 7. 验收标准

### 7.1 功能验收
- 题目推荐策略：
  - 场景1：当用户首次进入题组时，系统应优先推荐必做题中预期掌握度增量最大的题目
  - 场景2：当用户连续答对两题时，系统应推荐难度更高的题目（如果存在）
  - 场景3：当用户连续答错两题时，系统应推荐难度更低的题目（如果存在）
  - 场景4：当题组已达预估答题量或剩余题目预期掌握度增量均<0.01时，系统应进入下一题组
- 专注度监测：
  - 场景1：当用户进入题目1s内作答且回答错误时，专注度分值应-1
  - 场景2：当用户连续做错3题时，专注度分值应-1
  - 场景3：当专注度分值为7/5/3时，系统分别触发对应的提醒或熔断机制
- 再练一次策略：
  - 场景1：当用户掌握度<目标掌握度的70%且正确率<70%，且有≥3题可用题目时，系统应展示再练一次入口
  - 场景2：再练一次中，系统应优先推荐用户在巩固练习中答错的题目

### 7.2 性能验收
- 响应时间：
  - 给定用户当前掌握度和题目难度系数，计算预期掌握度增量的响应时间不超过100ms
  - 给定用户作答结果，更新用户掌握度并推荐下一题的响应时间不超过500ms
- 并发用户：
  - 模拟1000个并发用户同时进行巩固练习，系统运行30分钟，无异常，平均响应时间不超过1秒

### 7.3 安全验收
- 权限控制：
  - 当非授权用户尝试访问其他用户的掌握度数据时，返回权限错误
  - 当授权教师访问学生掌握度数据时，可以正常获取数据
- 数据加密：
  - 当查询用户掌握度数据时，数据库中的数据为加密状态
- 操作审计：
  - 当触发专注度提醒或熔断时，操作日志正常记录，包含触发原因、时间和执行的操作

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：题目推荐的难度变化有明显的视觉提示，帮助用户感知练习进程
- 容错处理：当题组内无法找到合适题目时，系统自动切换到下一题组，并给出友好提示
- 兼容性：需支持主流移动设备和PC浏览器，保证推荐算法和界面展示正常

### 8.2 维护性需求
- 配置管理：专注度判断阈值、掌握度增量计算参数可由系统管理员在后台配置调整
- 监控告警：当大量用户出现专注度异常或熔断时，系统自动告警，通知相关产品和运营人员
- 日志管理：系统自动记录用户作答行为、推荐题目的预期掌握度增量等关键信息，日志保存90天

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 用户可以通过练习报告页面的反馈按钮和客服渠道提交对题目推荐的反馈
- 每周定期收集和分析用户反馈，识别推荐策略的改进点

### 9.2 迭代计划
- 迭代周期：每4周
- 每次迭代结束后，评估用户掌握度提升情况和专注度异常率，调整算法参数和阈值

## 10. 需求检查清单

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注 |
| ------- | --------- | ----- | ----- | ----- | ------- | ------- | ---- |
| 题目推荐策略：每次推题默认在当前题组中选择「预期掌握度增量」最大的题目 | 2.1 核心功能-题目推荐策略, 4.场景2, 5.题目推荐策略流程 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 如果有必做题，优先推荐必做题 | 5.题目推荐策略流程中的必做题优先逻辑 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 连续两题之间难度最多提升一个等级 | 4.场景2中的难度调整规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 上题答错，不可提升难度等级 | 4.场景2中的难度调整规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 上题答对，不可降低难度等级 | 4.场景2中的难度调整规则 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 熔断策略：用户连续做错5题且题池中没有难度更低的题目 | 2.2 辅助功能-熔断策略 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 熔断策略：用户专注度分值等于3 | 2.2 辅助功能-熔断策略, 4.场景3 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次条件：用户当前掌握度低于目标掌握度的70%且正确率低于70% | 4.场景4 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次题目数量判断：所有可练习题组中的可用题目数量≥3题 | 4.场景4 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 再练一次推题策略：每个题组的首题为巩固练习中错题中预期掌握度增量最大的题目 | 5.再练一次推题策略流程 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 专注度判断：进入题目1s内作答且回答错误 | 4.场景3 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 专注度判断：连续做错3题 | 4.场景3 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 专注度提醒：分数等于7时触发第一次提醒，等于5时触发第二次提醒，等于3时触发熔断 | 4.场景3, 5.专注度监测流程 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 预估题目数量计算规则：基础答题量=10*难度系数*考频系数 | 3.计算规则与公式 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 动态调整系数A = 1 + α×(max(0, θ_tar - θ_cur)/θ_tar) | 3.计算规则与公式 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 预期掌握度增量计算：P(答对) = sigmoid(4 × (θ - β)) | 3.计算规则与公式 | ✅ | ✅ | ✅ | ✅ | ✅ | |
| 预期掌握度增量 = P(答对)×答对掌握度增量 + P(答错)×答错掌握度增量 | 3.计算规则与公式 | ✅ | ✅ | ✅ | ✅ | ✅ | |

## 11. 附录

### 11.1 术语表
- 预期掌握度增量：预估每道题对当前用户带来的掌握度变化量
- 必做题：老师在选题时打上推荐标签的典型题目
- 巩固练习：AI课程学习流程中，承接课堂知识理解与能力内化的关键环节
- 题组：和课程知识点相关的典型题型分组
- 熔断策略：在特定条件下提前结束练习的机制
- 再练一次：针对掌握度不足的知识点提供的额外练习机会

### 11.2 参考资料
- PRD-巩固练习相关策略-V1.0
- PRD-掌握度策略-V1.0 
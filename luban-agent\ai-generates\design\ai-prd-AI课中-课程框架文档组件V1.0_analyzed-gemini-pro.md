# 产品需求文档：AI课中 - 课程框架文档组件 V1.0

## 1. 需求背景与目标

### 1.1 需求背景
在前期两个版本的 demo 进校调研过程中，收集了一系列待解决的关键问题，如：板书内容过多，高亮时机和老师讲解对不上，不能双击播放暂停，自由/跟随模式的切换交互不好理解等。这些问题对课程体验造成了负面影响。因此，计划在正式版本中优先解决这些问题，以确保课程基础体验（包括学习与练习环节）的质量。

此外，正式版本还将整体迭代课中体验，让上课过程更接近1v1真人直播课，提供沉浸式地课堂体验，展示系统的个性化和智能型。

### 1.2 项目收益
- 优化课中体验，提升学习效果及课程满意度。

### 1.3 覆盖用户
- 使用新AI课的全量用户。

### 1.4 方案简述
本方案围绕AI课中学习体验升级，在保留原有课中基本结构的基础上，进行了优化与能力扩展，具体包括：
- **课程框架升级**：优化开场页与结算页设计。
- **文档组件增强**：引入同步播放勾画轨迹、快捷交互（双击、长按、倍速调整、10秒快退快进）等功能，提升学生自主学习流畅度。

本版本聚焦于打磨课中体验的底层能力，为后续引入积分体系、小组战队、互动讲题、评论等丰富玩法奠定基础。

### 1.5 未来规划
1.  框架中加入积分体系和小组战队。
2.  支持互动讲题，自动化逐步讲解题目，并在讲解过程中加入互动问题。
3.  支持在文档中长按评论。
4.  增加费曼组件、互动组件。

## 2. 功能范围

### 2.1 核心功能

#### 2.1.1 课程框架 (P0)
- **需求描述**：优化课程的整体结构和流程，提升用户体验。
- **主要功能点**：
    1.  **开场页优化**：
        -   不同学科使用不同开场页（更换背景图和色值），页面框架保持一致。
        -   展示章节信息、课程序号、课程名称（取对应业务树末级知识点名称，25字以内）。
        -   进入开场页后自动播放IP动效+音频，播放完成后自动进入AI课第一小节。
        -   作用：建立仪式感，提高学习专注度。
    2.  **结算页优化**：
        -   完成一节课全部内容学习后进入。
        -   去掉组件间串场动效。
        -   增加"掌握度"、"答题情况"、"推荐学习"相关信息。
        -   鼓励文案展示规则调整。
        -   庆祝动效及文案：
            -   动效：随机选择课程IP角色，根据正确率（>70%开心动效，否则鼓励动效）展示不同动效。外化掌握度不会下降。
            -   文案：包括主副文案，与IP相关，根据用户学习表现（掌握度、正确率、时长、其他情况如完成学习）随机选择展示。
        -   学习表现：展示学习用时、答题数、正确率（已学完课程重新学习不累计）。
        -   外化掌握度：展示掌握度数值、本次学习提升值、掌握度说明（点击i图标显示）、掌握度完成进度（当前掌握度/目标掌握度）。初始掌握度不用于进度计算，掌握度下降时对外展示不变。
        -   答题情况：展示本次学习的正误情况，错误/正确样式区分。题目超14个可上划，点击查看详情进入题目列表，点击单个题目进入详情。
        -   推荐学习：根据掌握度、巩固练习完成情况、加练情况推荐学习模块（巩固练习、巩固练习-加练、拓展练习）或提示"掌握度已达成，暂无推荐学习内容"。展示模块名称和预估题数，点击"去练习"进入对应模块。
        -   课程反馈：提供"极差"到"很棒"五类评价，选择后可提交。不同评价等级对应不同反馈标签选项（讲解、解析、其他）和开放式文本输入。
        -   作用：恭喜完成课程、展示学习表现、推荐后续学习路径。
    3.  **课程进度管理**：
        -   用户可在任何组件中灵活调整学习进度，切换到已做过的练习模块时直接展示答题记录。
        -   进度展示：按课程组件顺序（文档、练习）展示，分"已解锁"（学习过）、"学习中"（当前）、"待解锁"（未学习）。回看课程时全为已解锁。
        -   交互：点击"课程进度"按钮展示进度并暂停讲解，点击其他区域收起并继续。点击已解锁组件可跳转。
        -   组件跳转逻辑：
            -   视频组件：从上次跳出进度播放，播完则从头。
            -   练习组件：未答完则从上次跳出题目继续，答完则从第一题展示历史记录，可逐题查看后进入下一组件。
            -   跳转到学习过的组件，完成后自动进入课程中下一个组件。
            -   组件间切换不影响整体学习进度统计。
    4.  **退出/继续课程**：
        -   点击左上角退出按钮，记录当前进度，返回课程入口页。
        -   再次进入：
            -   文档组件：从退出时间点继续播放，默认跟随。
            -   练习组件：进入退出时未答完的题目，保存退出前结果；或进入查看题目解析的页面。
            -   答疑组件：进入调起答疑的文档/练习组件，默认不进入答疑，再次点击可看历史。

#### 2.1.2 文档组件 (P0)
- **需求描述**：增强文档组件的播放和交互体验。
- **主要功能点**：
    1.  **内容播放优化**：
        -   增加勾画轨迹，与JS内容同步播放。
        -   未播放区域呈现模糊/弱化效果，聚焦当前内容块。
        -   默认展示字幕。
    2.  **交互操作优化**：
        -   增加双击快捷交互（播放/暂停）。
        -   增加长按快捷交互（如3倍速播放，松手恢复；选中文字/图片唤起问一问、评论（评论本期不做））。
        -   增加1.75和3倍速选项。
        -   增加前进/后退10s功能。
    3.  **模式切换**：
        -   默认"跟随模式"：数字人视频、勾画轨迹、JS动画内容自动同步播放，板书内容块下方展示进度线（讲解跳跃时进度线在最后句子后）。
        -   用户滑动JS动画内容时进入"自由模式"：数字人视频正常播放，板书不自动播放但仍展示进度线，不播放勾画轨迹。
        -   右上角常驻"问一问"和"课程进度"。
    4.  **自由模式交互**：
        -   单击无评论文档内容：句子高亮，出现"从这里学"按钮，点击后视频&勾画从该句时间戳播放，并切换回跟随模式。
        -   单击有评论文档内容：展示评论内容（本期不做）。
        -   单击文档以外区域：唤起操作面板。
        -   长按文档内容：选中文字/图片，唤起问一问、评论操作（评论本期不做）。可拖拽高亮条选文字。点击问一问进入答疑组件。
        -   长按文档以外区域：3倍速播放，松手恢复。
        -   双击：切换播放/暂停。
    5.  **操作面板**：
        -   单击文档以外区域唤起。
        -   包含视频播控：字幕开关、播放/暂停、倍速（0.75、1.0、1.25、1.5、1.75、2.0，全局生效，重进课程恢复正常速度）、前进/后退10s（视频、板书、勾画同步）。
    6.  **进入下一组件**：
        -   文档到文档：跟随模式下播完自动进入下一组件（无转场）；跟随/自由模式下上划文档到底部，继续上划（有阻尼效果）切换至下一组件（默认为跟随模式）。
        -   文档到练习/练习到文档：均展示切换动效。

### 2.2 辅助功能
- **答疑组件**：基于大模型能力和学生进行实时对话，解答学生问题。在课中不能单独配置，需要挂载在「文档组件」/「练习组件」下。

### 2.3 非本期功能
- 文档组件长按评论功能。
- 单击有评论的文档内容展示评论。
- 框架中加入积分体系和小组战队。
- 支持互动讲题。
- 增加费曼组件、互动组件。

## 3. 计算规则与公式

### 3.1 结算页学习表现
- **学习用时**：用户完成本节课学习的累计学习时长。
    - 注：已学完的课程又重新学习，时长不累计。
- **答题数**：用户在这节课本次学习中的累计答题数量。
    - 注：已学完的课程又重新学习，答题数量不累计。
- **正确率**：答对的题目数量 / 答题数量。
    - 计算公式：`CorrectAnswers / TotalAnsweredQuestions`
    - 参数说明：
        - `CorrectAnswers`: 本次学习中答对的题目数量。
        - `TotalAnsweredQuestions`: 本次学习中总共作答的题目数量。
    - 取值范围：[0, 1] (通常以百分比展示)
    - 注：已学完的课程又重新学习，正确率不累计。

### 3.2 结算页外化掌握度
- **掌握度计算方式**：详见《PRD - 掌握度策略 - V1.0》。
- **掌握度完成进度**：用户当前掌握度 / 目标掌握度。
    - 计算公式：`CurrentMastery / TargetMastery`
    - 参数说明：
        - `CurrentMastery`: 用户当前对外展示的掌握度。
        - `TargetMastery`: 用户设定的学习目标掌握度。
    - 取值范围：[0, 1] (保留小数点后两位)
- **掌握度变化**：本次学习后的进度 - 本次学习前的进度。
    - 计算公式：`CurrentMasteryProgress - PreviousMasteryProgress`
    - 参数说明：
        - `CurrentMasteryProgress`: 本次学习后的掌握度完成进度。
        - `PreviousMasteryProgress`: 本次学习前的掌握度完成进度。
    - 规则：
        - 初始掌握度不用于进度计算，用户首次学习的掌握度变化 = 本次学习后的进度 - 0。
        - 如果用户本次学习后实际掌握度下降，对外展示的掌握度不变化，则掌握度变化值为0或保持不变（根据具体实现确认）。

### 3.3 课程进度统计
- **学习进度**：已学完的组件数量 / 一节课中文档+练习组件的数量之和。
    - 计算公式：`CompletedComponents / TotalDocumentAndExerciseComponents`
    - 参数说明：
        - `CompletedComponents`: 用户已学完的文档组件和练习组件的数量。
        - `TotalDocumentAndExerciseComponents`: 当前课程中所有文档组件和练习组件的总数。
    - 取值范围：[0, 1] (通常以百分比展示)

## 4. 用户场景与故事

### 场景1: 学生首次进入AI课程学习新知识点
作为一个选修了AI课程的学生，我希望能够通过AI课的开场页感受到学习的仪式感，然后平滑地进入课程内容的学习。在学习过程中，我希望能够清晰地看到老师的讲解、板书内容和勾画重点，并且可以根据自己的节奏调整播放速度或回顾内容。如果遇到不理解的地方，我可以方便地提问。

**关键步骤：**
1.  学生点击进入一节新AI课。
2.  系统展示该学科的课程开场页，播放IP动效和音频。
3.  开场页播放完毕，自动进入第一个文档组件，开始"跟随模式"播放。
4.  学生观看数字人视频、同步的JS板书动画和勾画轨迹。
5.  学生可以通过操作面板调整播放倍速、开关字幕、快进/快退。
6.  如果学生想自行浏览板书，滑动板书内容，系统切换到"自由模式"。
7.  在自由模式下，学生可以点击板书上的"从这里学"按钮，让课程从指定位置开始以"跟随模式"播放。
8.  学习完一个组件后，系统自动或学生手动进入下一个组件。

**Mermaid图示 (活动图):**
```mermaid
stateDiagram-v2
    [*] --> 进入课程
    进入课程 --> 开场页展示: 自动播放动效和音频
    开场页展示 --> 第一个文档组件: 自动进入
    第一个文档组件 --> 跟随模式学习: 视频、板书、勾画同步
    跟随模式学习 --> 操作面板交互: 调整倍速/字幕/快进退
    跟随模式学习 --> 自由模式切换: 用户滑动板书
    自由模式切换 --> 自由浏览板书
    自由浏览板书 --> 点击从这里学: 指定播放位置
    点击从这里学 --> 跟随模式学习
    跟随模式学习 --> 下一个组件: 自动或手动
    操作面板交互 --> 跟随模式学习
    下一个组件 --> [*]
```

### 场景2: 学生完成一节AI课的学习并查看学习报告
作为一个AI课程的学生，在我学完一节课的全部内容后，我希望能看到一个清晰的结算页面，了解我本次的学习表现，包括学了多久、答了多少题、正确率如何、知识点掌握有没有提升。我还希望系统能根据我的学习情况给我一些后续学习的建议。

**关键步骤：**
1.  学生完成课程中所有组件的学习。
2.  系统自动进入结算页。
3.  结算页展示庆祝动效和鼓励文案。
4.  学生查看"学习表现"：学习用时、答题数、正确率。
5.  学生查看"外化掌握度"：当前掌握度、本次提升值、完成进度。
6.  学生查看"答题情况"：概览各题目的正误，可点击"查看详情"进入题目列表。
7.  学生查看"推荐学习"：系统根据其表现推荐的巩固或拓展练习。
8.  学生对本节课进行"课程反馈"评价。
9.  学生点击"完成"退出结算页，或点击"回看课程"。

**Mermaid图示 (流程图):**
```mermaid
flowchart TD
    A[学生完成所有组件学习] --> B{进入结算页}
    B --> C[展示庆祝动效和文案]
    C --> D[显示学习表现模块（用时、题数、正确率）]
    D --> E[显示外化掌握度模块]
    E --> F[显示答题情况模块]
    F --> G[显示推荐学习模块]
    G --> H[显示课程反馈模块]
    H --> I{用户操作}
    I -- "点击完成" --> J[退出结算页]
    I -- "点击回看课程" --> K[进入已解锁课程]
    I -- "提交反馈" --> H
    %% 重新停留在反馈模块或有确认提示
```

### 场景3: 学生在学习过程中需要回顾或跳转到特定知识点
作为一个AI课程的学生，在学习过程中，我可能需要回顾前面讲过的内容，或者想跳到某个特定的知识点或练习题。我希望课程进度可以清晰展示，并且能方便地跳转。

**关键步骤：**
1.  学生在学习任一组件时，点击"课程进度"按钮。
2.  系统暂停当前讲解，并展示课程进度列表（包含所有文档和练习组件及其状态：已解锁、学习中、待解锁）。
3.  学生点击一个"已解锁"的文档组件。
4.  系统跳转到该文档组件，并从上次学生跳出的进度开始播放（或从头播放如果上次已播完）。
5.  学生点击一个"已解锁"的练习组件。
6.  系统跳转到该练习组件，如果上次题目未答完，则从上次跳出的题目继续答题；如果已答完，则从第一题开始展示历史答题记录。
7.  学生完成跳转后组件的学习，系统自动进入课程中的下一个组件。

**Mermaid图示 (时序图):**
```mermaid
sequenceDiagram
    participant S as 学生
    participant UI as 课程界面
    participant System as 系统

    S->>UI: 点击"课程进度"按钮
    UI->>System: 请求暂停讲解并获取课程进度
    System-->>UI: 返回课程进度列表
    UI-->>S: 展示课程进度列表，暂停当前组件
    S->>UI: 点击已解锁的组件A
    UI->>System: 请求跳转至组件A
    System-->>UI: 加载组件A内容（根据历史记录确定起点）
    UI-->>S: 展示组件A内容并开始播放/交互
    S->>UI: 完成组件A学习
    UI->>System: 组件A完成，请求进入下一组件
    System-->>UI: 加载下一组件内容
    UI-->>S: 展示下一组件内容
```

### 场景4: 学生中途退出课程后再次进入
作为一个AI课程的学生，我可能因为有其他事情需要中途退出课程。当我再次回到课程时，我希望能够从上次离开的地方继续学习，而不是从头开始。

**关键步骤：**
1.  学生在学习过程中，点击左上角的"退出"按钮。
2.  系统记录当前学习进度（所在组件、组件内进度、答题情况等）。
3.  学生返回课程入口页面。
4.  一段时间后，学生再次点击进入该课程。
5.  系统根据记录的进度恢复学习：
    -   如果上次在文档组件，则从退出时的时间点继续播放，默认为跟随模式。
    -   如果上次在练习组件，则进入退出时未答完的题目（并保留已答结果），或进入查看题目解析的页面。

**Mermaid图示 (状态图):**
```mermaid
stateDiagram-v2
    state "学习中" as Learning
    state "课程入口" as CourseEntry
    state "恢复文档播放" as ResumeDoc
    state "恢复练习" as ResumeEx

    [*] --> CourseEntry
    CourseEntry --> Learning: 学生进入课程
    Learning --> CourseEntry: 学生点击退出按钮 (记录进度)

    CourseEntry --> ResumeDoc: 再次进入 (上次在文档)
    ResumeDoc --> Learning: 从记录点继续播放

    CourseEntry --> ResumeEx: 再次进入 (上次在练习)
    ResumeEx --> Learning: 从记录点继续练习/查看解析
```

## 5. 业务流程图

### 5.1 单节课内容结构与组件流程
一节课由不同的可配置组件（文档组件、练习组件、答疑组件）构成。答疑组件需挂载在文档或练习组件下。

**Mermaid图示 (流程图):**
```mermaid
flowchart TD
    A[课程开场页（功能页面）] --> B[课程引入（文档组件, 答疑组件）]
    B --> C[知识点1（文档组件, 答疑组件）]
    C --> D[知识点2（文档组件, 答疑组件）]
    D --> E[练习1（练习组件, 答疑组件）]
    E --> F[知识点3（文档组件, 答疑组件）]
    F --> G[知识点4（文档组件, 答疑组件）]
    G --> H[练习2（练习组件, 答疑组件）]
    H --> I[课程总结（文档组件, 答疑组件）]
    I --> J[学习报告/结算页（功能页面）]
```
*原始PRD中的图片 `in_table_board_FdxgwtizahKubpbiJmFcvfeVnmb` 及其解析已参考并转换为此流程图。*

### 5.2 结算页庆祝动效展示策略
**Mermaid图示 (流程图):**
```mermaid
graph TD
    A(开始评估动效展示策略) --> B{正确率 > 70% ?};
    B -- "是" --> C[掌握度提升];
    C --> E[展示开心动效];
    B -- "否 (其他情况)" --> D[掌握度不变];
    D --> F[展示鼓励动效];
    E --> G(策略执行完毕);
    F --> G;

    subgraph "重要规则"
        direction LR
        R1["注：外化掌握度不会出现下降的情况,
当实际的知识点掌握下降时,
外化掌握度不变。"]
    end
    classDef rule fill:#f9f,stroke:#333,stroke-width:1px,color:#333;
    class R1 rule;
```
*原始PRD中的图片 `board_QBEwwqJVOh02Q9bwVXwcSqvmncd` 及其解析已参考并转换为此流程图。*

### 5.3 结算页推荐学习策略
**Mermaid图示 (流程图):**
```mermaid
graph TD
    A["评估用户掌握度及练习情况"] --> B{"掌握度是否达到目标掌握度?"};
    B -- "是" --> C["推荐学习策略: 无推荐学习内容"];
    B -- "否" --> D{"用户是否完成巩固练习?"};
    D -- "否" --> E["推荐巩固练习"];
    D -- "是" --> F{"是否有未完成的加练?"};
    F -- "是" --> G["推荐巩固练习加练"];
    F -- "否 (没有加练/加练已完成)" --> H["推荐拓展练习"];
```
*原始PRD中的图片 `board_C4lPw82zBhogmqbDXRfcewf9n2c` 及其解析已参考并转换为此流程图。*


## 6. 性能与安全需求

### 6.1 性能需求
- **响应时间**：
    - 文档组件加载与切换：平均响应时间不超过 2 秒。
    - 练习组件题目加载：平均响应时间不超过 1.5 秒。
    - 结算页生成与展示：平均响应时间不超过 3 秒。
- **并发用户**：初期版本需支持至少 500 个并发用户同时在线学习，核心功能（如内容播放、答题提交）性能不发生明显衰减。
- **数据量**：
    - 课程内容数据：支持至少1000门课程，每门课程平均50个组件的内容存储和快速检索。
    - 用户学习记录：支持至少10万用户的学习行为数据（进度、答题记录、掌握度等）的存储和查询，复杂查询响应时间不超过5秒。

### 6.2 安全需求
- **权限控制**：
    - 学生用户只能访问其已报名或权限范围内的课程内容。
    - 课程内容数据、用户学习数据等敏感信息，未经授权不得访问。
- **数据安全**：
    - 用户个人信息、学习记录等敏感数据在存储和传输时应进行加密处理。
    - 防止恶意脚本注入、CSRF等常见Web攻击。
- **操作审计**：
    - 关键操作（如课程内容修改、用户数据修改-后台）需记录操作日志，包括操作人、时间、IP、操作内容。

## 7. 验收标准

### 7.1 功能验收

#### 7.1.1 课程框架
- **开场页**：
    - 使用场景：用户首次进入一节AI课。
    - 期望结果：正确展示对应学科的开场页（背景图、色值符合设计），章节、课程序号、课程名称正确显示。IP动效和音频自动播放，播放完成后自动跳转到第一个教学组件。
- **结算页**：
    - 使用场景：用户完成一节课所有组件的学习。
    - 期望结果：正确展示结算页，包含正确的学习表现数据（用时、答题数、正确率）、外化掌握度（数值、提升、进度）、答题情况概览、基于用户表现的推荐学习内容（或无推荐提示）、课程反馈模块。庆祝动效和文案按规则展示。
- **课程进度管理**：
    - 使用场景：用户在学习过程中点击"课程进度"按钮或通过滑动等方式在组件间跳转。
    - 期望结果：课程进度条正确显示各组件状态（已解锁、学习中、待解锁）。用户可成功跳转到已解锁组件，并从正确的位置开始学习/练习。历史答题记录在练习组件中正确展示。
- **退出/继续课程**：
    - 使用场景：用户学习中途退出，然后再次进入课程。
    - 期望结果：系统能正确记录退出时的进度。再次进入时，能从上次退出的文档播放时间点或练习题目状态继续学习。

#### 7.1.2 文档组件
- **内容播放**：
    - 使用场景：用户在文档组件中学习。
    - 期望结果：勾画轨迹与JS内容同步播放。未播放区域按设计模糊/弱化。字幕默认正确展示。
- **交互操作**：
    - 使用场景：用户在文档组件中使用快捷交互或操作面板。
    - 期望结果：双击能正确切换播放/暂停。长按能触发相应功能（如3倍速播放）。倍速调整（0.75至2.0）生效且同步。前进/后退10s功能准确。
- **模式切换与交互**：
    - 使用场景：用户在跟随模式和自由模式间切换和操作。
    - 期望结果：默认进入跟随模式，滑动内容可切换至自由模式。自由模式下点击"从这里学"能切换回跟随模式并从指定位置播放。操作面板能正常唤起和使用。
- **进入下一组件**：
    - 使用场景：用户学完一个文档组件或手动切换。
    - 期望结果：能平滑进入下一个组件，符合设计的动效和模式切换逻辑。

### 7.2 性能验收
*  **IMPORTANT AI (Chief Product Manager) INSTRUCTION:** 此节应该是对 6.1 节中每个需求点的详细验收标准，包括具体的测试用例和期望结果。
- **响应时间**：
    - 给定网络环境良好，当用户点击进入文档组件，其内容（首屏JS动画、视频元素）在2秒内加载完成并可交互。
    - 给定网络环境良好，当用户提交练习组件中的单选题答案，其结果反馈在1.5秒内显示。
    - 给定用户完成最后一题，当系统跳转至结算页，其核心数据（学习表现、掌握度）在3秒内计算并展示完成。
- **并发用户**：
    - 模拟500个并发用户同时进行AI课程学习（包含文档播放、练习答题），持续运行30分钟，系统无崩溃、无明显卡顿，核心功能平均响应时间增加不超过20%。

### 7.3 安全验收
*  **IMPORTANT AI (Chief Product Manager) INSTRUCTION:** 此节应该是对 6.2 节中每个需求点的详细验收标准，包括具体的测试用例和期望结果。
- **权限控制**：
    - 给定学生A未报名课程X，当学生A尝试访问课程X的内容链接，系统应提示无权限或引导至报名。
    - 给定学生B已报名课程Y，当学生B访问课程Y的内容，可以正常学习。
- **数据安全**：
    - 监控网络传输，确认用户登录凭证、个人学习数据等在HTTPS协议下加密传输。
    - 尝试在评论区、反馈文本框等输入恶意脚本，系统应能有效过滤或转义，不执行恶意代码。

## 8. 其他需求

### 8.1 可用性需求
- **界面友好**：课程框架、文档组件、结算页等核心界面操作流程简洁直观，用户在3步以内可完成主要交互（如调整倍速、查看进度、提交反馈）。
- **容错处理**：
    - 网络断开后重连，课程能尝试从断开点恢复播放或给出明确提示。
    - 用户进行非法操作或输入无效数据时，系统应给出友好、明确的错误提示，并引导用户正确操作。
- **兼容性**：需支持主流浏览器（Chrome, Safari, Edge 最新版本）及主流移动设备（iOS, Android 最新系统版本）的Web访问，保证界面和功能正常。

### 8.2 维护性需求
- **配置管理**：
    - 课程开场页的学科背景图、色值等应支持后台可配置。
    - 结算页鼓励文案、IP角色动效等应支持后台管理和更新。
    - 推荐学习的策略阈值（如掌握度目标）应可由运营人员在后台调整。
- **监控告警**：
    - 当核心服务（如内容播放服务、答题服务、掌握度计算服务）出现异常或错误率超过阈值时，系统应能自动告警并通知相关技术人员。
- **日志管理**：系统自动记录用户关键学习行为日志（如组件进入/退出、答题提交、模式切换）、系统运行日志和错误日志。日志保存至少30天，可供系统管理员查询和分析。

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 用户可以通过结算页的"课程反馈"功能提交对每节课的评价和具体意见。
- 设立专门的用户反馈邮箱或在线表单，供用户提交更普遍性的问题和建议。
- 定期（如每双周）收集和分析用户反馈，整理成报告，作为产品迭代的重要输入。

### 9.2 迭代计划
- **迭代周期**：初步计划每2-3周为一个快速迭代周期。
- **V1.0上线后**：重点关注核心功能的稳定性、用户体验的流畅性，收集用户对新框架和文档组件的反馈。
- **后续迭代**：根据用户反馈和业务优先级，逐步引入"未来规划"中的功能，如积分体系、互动讲题等。每次迭代结束后，评估需求实现情况和用户反馈，动态调整下一个迭代的优先级和计划。

## 10. 需求检查清单
*  **IMPORTANT AI (Chief Product Manager) INSTRUCTION:** 本节必须保留，并且需要根据原始需求文档中的具体需求点进行填写。"原始需求"列，必须详细列出来自原始需求文档"详细产品方案"的所有具体功能点和需求描述。确保覆盖所有细化要求，而不是仅列出概览性条目，随后，将这些详细的原始需求点**严格映射**到新生成文档中对应的具体章节或功能点上，并进行比对评估。检查项必须全部完成，不得遗漏。

使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 (摘自PRD V1.0 "详细产品方案")                                                                 | 对应新PRD需求点 (章节)                      | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注                                                                 |
| ----------------------------------------------------------------------------------------------------- | ------------------------------------------ | ------ | ------ | ------ | -------- | -------- | -------------------------------------------------------------------- |
| **课程框架**                                                                                          |                                            |        |        |        |          |          |                                                                      |
| 开场页：不同学科不同背景图和色值，框架不变。展示章节、课程序号、课程名称。自动播放IP动效+音频，完成后自动进入。 | 2.1.1 (课程框架/开场页优化)                    | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 结算页：去掉串场动效。增掌握度、答题情况、推荐学习。鼓励文案规则调整。                                    | 2.1.1 (课程框架/结算页优化)                    | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 结算页-庆祝动效及文案：随机IP，按正确率分开心/鼓励动效，外化掌握度不降。主副文案与IP相关，按学习表现展示。      | 2.1.1 (课程框架/结算页优化)                    | ✅      | ✅      | ✅      | ✅        | ✅        | 包含3.1, 3.2的规则支撑                                                   |
| 结算页-学习表现：用时、答题数、正确率，重学不累计。                                                       | 2.1.1 (课程框架/结算页优化), 3.1             | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 结算页-外化掌握度：说明、完成进度、变化。初始不计，下降不展示。                                             | 2.1.1 (课程框架/结算页优化), 3.2             | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 结算页-答题情况：正误样式，超14个上划，点详情进列表，点题目进详情。                                          | 2.1.1 (课程框架/结算页优化)                    | ✅      | ✅      | ✅      | ✅        | ✅        | 涉及题目列表和详情页的进一步描述                                             |
| 题目列表：题号、类型、题干(2行)、作答结果(正/误/部分正)。可筛选错题。点解析进详情。                             | 2.1.1 (课程框架/结算页优化 - 答题情况的延伸) | ✅      | ✅      | ✅      | ✅        | ✅        | 作为答题情况的详细内容                                                       |
| 题目详情：用时、序号、加入/已加入错题本、上/下一题。                                                        | 2.1.1 (课程框架/结算页优化 - 答题情况的延伸) | ✅      | ✅      | ✅      | ✅        | ✅        | 作为答题情况的详细内容                                                       |
| 结算页-推荐学习：按策略展模块名(巩固/加练/拓展)、预估题数。无推荐则提示。点"去练习"进模块。                     | 2.1.1 (课程框架/结算页优化)                    | ✅      | ✅      | ✅      | ✅        | ✅        | 推荐策略流程图见5.3                                                        |
| 结算页-课程反馈：5类评价，点icon展弹窗，选后可提交，提交后toast。                                           | 2.1.1 (课程框架/结算页优化)                    | ✅      | ✅      | ✅      | ✅        | ✅        | 包含极差/较差，一般，满意/很棒三种反馈弹窗内容                                   |
| 课程进度：灵活调整，切换已做练习直接展示记录。按组件顺序展状态(已解锁/学习中/待解锁)。回看全解锁。交互逻辑。      | 2.1.1 (课程框架/课程进度管理)                  | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 组件跳转逻辑：视频/练习组件的进入逻辑，完成后自动进入下一组件。不影响学习进度统计。                             | 2.1.1 (课程框架/课程进度管理)                  | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 退出/继续课程：点退出记录进度返课程入口。再次进入按进度策略恢复。                                             | 2.1.1 (课程框架/退出/继续课程)                 | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| **文档组件**                                                                                          |                                            |        |        |        |          |          |                                                                      |
| 内容播放：增勾画轨迹同步JS，未播区域模糊/弱化，默认字幕。                                                   | 2.1.2 (文档组件/内容播放优化)                  | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 交互操作：增双击、长按快捷交互，增1.75/3倍速，增前进/后退10s。                                             | 2.1.2 (文档组件/交互操作优化)                  | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 模式切换：默认跟随，滑JS进自由。右上角常驻问一问、课程进度。                                                  | 2.1.2 (文档组件/模式切换)                      | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 跟随模式：视频、勾画、JS动画同步，板书内容块下有进度线。                                                    | 2.1.2 (文档组件/模式切换)                      | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 自由模式：视频播，板书不自动播但有进度线，不播勾画。                                                        | 2.1.2 (文档组件/模式切换)                      | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 自由模式交互：单击无评论内容("从这里学"->跟随)，单击有评论(本期不做)，单击文档外(操作面板)，长按内容(问/评)，长按文档外(3倍速)，双击(播/暂)。 | 2.1.2 (文档组件/自由模式交互)                  | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 操作面板：单击文档外唤起。含视频播控(字幕、播/暂、倍速、前进/后退10s)。                                      | 2.1.2 (文档组件/操作面板)                      | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |
| 进入下一组件：文档到文档(播完/上滑)，文档到练习/练习到文档(切换动效)。                                          | 2.1.2 (文档组件/进入下一组件)                  | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                      |

- 完整性：原始需求是否都有对应的优化后需求点，无遗漏
- 正确性：优化后需求描述是否准确表达了原始需求的意图
- 一致性：优化后需求之间是否有冲突或重复
- 可验证性：优化后需求是否有明确的验收标准
- 可跟踪性：优化后需求是否有明确的优先级和依赖关系

✅表示通过；❌表示未通过；⚠️表示描述正确，但细节不完整（如计算规则、阈值等，需要和 PM 进一步沟通）；N/A表示不适用。每个需求点都需要填写。

## 11. 附录

### 11.1 原型图
原型图及UI截图请参考原始PRD文档 `documents/raw-docs/PRD - AI课中 - 课程框架文档组件 V1.0_analyzed.md` 中对应的图片链接和展示。

### 11.2 术语表
- **JS动画/JS内容**：指课程中通过JavaScript技术实现的动态板书或交互内容。
- **勾画轨迹**：指模拟老师在黑板上书写或标记重点的动态线条效果，与讲解内容同步。
- **IP角色**：指课程中设计的人工智能助手或虚拟教师的形象。
- **跟随模式**：课程内容（视频、板书、勾画）按预设节奏同步自动播放的模式。
- **自由模式**：用户可以自由浏览板书内容，不受视频播放进度限制的模式。
- **外化掌握度**：系统向用户展示的、经过一定策略调整（如只升不降）的知识点掌握程度。

### 11.3 参考资料
- 原始需求文档: `documents/raw-docs/PRD - AI课中 - 课程框架文档组件 V1.0_analyzed.md`
- 掌握度策略文档: `PRD - 掌握度策略 - V1.0` (链接: [https://wcng60ba718p.feishu.cn/wiki/J3d0wrEaDiWRy6kj2uVcQKLfnmh](https://wcng60ba718p.feishu.cn/wiki/J3d0wrEaDiWRy6kj2uVcQKLfnmh))
- AI课中需求盘点: (链接: [https://wcng60ba718p.feishu.cn/wiki/Htv0wNEAxis9dAkBjTTcjcWlnTb](https://wcng60ba718p.feishu.cn/wiki/Htv0wNEAxis9dAkBjTTcjcWlnTb))

--- 等待您的命令，指挥官 
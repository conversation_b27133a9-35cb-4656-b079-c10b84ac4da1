# 产品需求文档：产课工具_3期_AI课配课课程管理工具

## 1. 需求背景与目标

### 1.1 需求背景
本期产品目标主要服务于两类核心用户群体：教研老师（包含配课角色和审核角色）和总部教研管理老师。
1.  **教研老师**：需要在PC端Web浏览器中，通过访问特定课程地址，完成AI课程生产的第3步，即**课程内容的配置（配课）和整课预览**。此阶段，配课模块和整课预览模块将通过内网VPN访问，暂不引入复杂的任务管理系统，以确保快速上线和流程验证。
2.  **总部教研管理老师**：负责**课程的创建、管理以及最终的上架操作**。为保障线上课程数据的安全，课程管理和上架模块需要通过内网VPN访问，并进行手机号登录验证。

业务层面，不同角色的核心诉求如下：
*   **教研老师（配课与审核角色）**：获得便捷的配课工具和清晰的审课（预览）地址。
*   **总部教研管理老师（课程管理角色）**：拥有安全的课程创建、修改、上架/更新等管理权限。

### 1.2 需求目标
- **核心目标**：完成AI课生产工具P0核心功能的开发，打通AI课程从创建、配课、预览到上架的完整MVP（Minimum Viable Product，最小可行产品）业务流程。
- **上线目标**：确保产品在预定时间（例如6月）能够顺利上线，并可供目标用户（教研老师、总部教研管理老师）实际使用，支持AI课程的顺利生产与发布。
- 通过该需求的实现，可以 **优化AI课程生产流程，提升教研老师和管理老师的工作效率**。
- 该需求上线后，预计可以 **显著缩短AI课程的平均生产周期**，并 **确保课程内容的规范性和质量**。

### 1.3 覆盖用户
- **教研老师**：
    - 配题角色（生产老师）
    - 审核角色（审课老师）
- **总部教研管理老师**：
    - 课程管理角色

## 2. 功能范围

### 2.1 核心功能

#### 2.1.1 课程管理工具 (总部教研管理老师)
- **功能点1: 用户登录与认证**
    - 简要描述: 总部教研管理老师通过手机号和验证码登录课程管理后台。系统需校验手机号是否在后台配置的白名单内，并通过内网VPN保障访问安全。
    - 满足用户场景: 总部教研管理老师安全访问课程管理系统。
    - 解决用户痛点: 防止未授权访问，保障课程数据安全。
    - 优先级：高
    - 依赖关系：无

- **功能点2: 课程创建**
    - 简要描述: 总部教研管理老师可以在系统中创建新的AI课程。支持单个课程创建和通过上传Excel模板批量创建课程。
        - **单个创建**：手动输入课程名称、稿件地址、学段、学科、单课类型、AI老师（数字人ID）等信息，系统可根据稿件地址URL参数自动填充部分信息（如学段、学科、单课类型、数字人ID，这些自动填充的信息不可手动修改）。课程名称与业务树知识点需进行唯一性匹配校验。支持关联一个或多个业务树末级知识点（上架时必填至少一个）。
        - **批量创建**：下载预设Excel模板，填写课程名称和稿件地址后上传。系统解析模板，进行数据校验（如稿件地址是否重复、课程名与业务树知识点匹配、知识点名称是否为空）。对重复稿件地址提供忽略选项。支持展示批量处理结果（成功/失败条数）及下载失败原因。
    - 满足用户场景: 总部教研管理老师快速、准确地初始化课程信息。
    - 解决用户痛点: 提高课程创建效率，减少手动录入错误，规范课程基础信息。
    - 优先级：高
    - 依赖关系：用户登录与认证

- **功能点3: 课程列表展示与筛选**
    - 简要描述: 系统以列表形式展示已创建的AI课程。列表包含课程ID、课程名称、学段、学科、课程类型、业务树知识点、创建人、创建时间、上架状态、上架时间、上架人等关键信息。支持按创建时间、上架时间进行正序/倒序排序。支持通过课程ID/名称、业务知识树名称进行搜索。支持按课程类型、学段、学科、是否上架等维度进行筛选。支持下载当前筛选条件下的课程列表（全部列内容）。
    - 满足用户场景: 总部教研管理老师快速查找、浏览和定位目标课程。
    - 解决用户痛点: 课程信息不直观，查找特定课程困难。
    - 优先级：高
    - 依赖关系：课程创建

- **功能点4: 课程编辑**
    - 简要描述: 总部教研管理老师可以编辑已创建课程的信息。可修改项包括课程名称（限制25字符以内）、稿件地址（不做重复校验，但会提示地址不存在则不可保存）。学段、学科、单课类型、数字人ID等信息根据稿件地址URL参数自动填充且不可直接修改。支持修改关联的业务树末级知识点。编辑完成后，课程状态可能更新为"待上架更新"。
    - 满足用户场景: 总部教研管理老师修正或更新课程基础信息。
    - 解决用户痛点: 课程信息有误或需要调整时，无法便捷修改。
    - 优先级：高
    - 依赖关系：课程列表展示与筛选

- **功能点5: 查看课程详情**
    - 简要描述: 总部教研管理老师可以查看特定课程的详细信息。详情页展示课程名称及相关的线上资源链接，包括：线上预览地址、稿件地址、画板地址、配课地址。各地址提供"复制"功能，"线上预览"地址提供"修改地址"功能。同时展示课程的最近更新时间、修改与上架状态，以及修改后但未提交上架时的"修改预览"地址。
    - 满足用户场景: 总部教研管理老师快速获取课程所有相关资源的访问入口及当前状态。
    - 解决用户痛点: 课程相关链接分散，查找不便，状态不清晰。
    - 优先级：高
    - 依赖关系：课程列表展示与筛选

- **功能点6: 课程上架/更新**
    - 简要描述: 总部教研管理老师可以将审核完成的课程进行上架操作，或对已上架的课程进行内容更新。支持单个课程上架/更新及批量上架/更新。操作前进行二次弹窗确认（"是否确认上架？上架后，学生端和老师端都将同步可见本课"）。系统需校验课程是否符合上架条件（如试题/视频是否为空）。课程上架后，操作按钮变为"更新"。
        - **纠错类修改**：在当前课程ID上修改，不影响学生端和老师端的用户数据（进度、答题结果等），新老用户均看到修改结果。
        - **升级类修改**（本期简化支持）：已有用户学习数据的看老课，新用户看到新课。
    - 满足用户场景: 总部教研管理老师将制作完成的课程发布上线，或更新已上线课程内容。
    - 解决用户痛点: 课程发布流程不明确，无法有效管理课程版本和线上状态。
    - 优先级：高
    - 依赖关系：课程编辑, 配课工具完成配课

#### 2.1.2 配课工具 (教研老师 - 配课角色)
- **功能点1: 配课工作台访问**
    - 简要描述: 教研生产老师通过指定的URL（通常由课程ID拼接而成）访问对应课程的配课编辑页面。访问需在内网VPN环境下。
    - 满足用户场景: 教研生产老师进入特定课程进行内容配置。
    - 解决用户痛点: 缺乏便捷的课程内容编辑入口。
    - 优先级：高
    - 依赖关系：课程创建 (课程ID存在)

- **功能点2: 课程结构与组件管理**
    - 简要描述: 教研生产老师在配课工作台对课程内容进行结构化配置。课程内容由多个Part（如Part1、Part2...）组成，每个Part下可以添加和管理教学组件。
        - **UI模版（本期后台配置，前端无需展示修改）**: 系统按学段-学科构建唯一模版，包含头图、插图、尾图及预留Part图标。
        - **Part结构展示**: 清晰展示课程的Part列表及其名称（如：课程引言、等差数列等）。
    - 满足用户场景: 教研生产老师搭建和组织课程的整体框架和内容模块。
    - 解决用户痛点: 课程内容不成体系，缺乏结构化管理。
    - 优先级：高
    - 依赖关系：配课工作台访问

- **功能点3: 添加教学组件**
    - 简要描述: 教研生产老师可以在指定的Part内或Part之间点击"+"号选择并添加教学组件。本期支持练习组件和视频组件。
        - **选择插入位置**: 明确组件要插入的具体位置。
        - **选择组件类型**: 弹出选择框，选择"练习组件"或"视频组件"。
    - 满足用户场景: 教研生产老师向课程中填充具体的教学内容和互动元素。
    - 解决用户痛点: 无法方便地向课程中添加不同类型的教学材料。
    - 优先级：高
    - 依赖关系：课程结构与组件管理

- **功能点4: 练习组件配置**
    - 简要描述: 教研生产老师添加或编辑练习组件。
        - **添加题目**: 支持通过输入题库中的题目ID（可按顺序、顿号分隔、圆括号分组批量输入）来添加题目到练习组件中。
        - **编辑题组**: 查看已添加的题目列表，支持预览、换题（从题库选择或ID输入）、调整题目在组件内的上下顺序、删除题目。
    - 满足用户场景: 教研生产老师为课程配置相关的练习题目。
    - 解决用户痛点: 无法高效地组织和管理课程中的练习题。
    - 优先级：高
    - 依赖关系：添加教学组件

- **功能点5: 视频组件配置**
    - 简要描述: 教研生产老师添加或编辑视频组件。
        - **添加视频**: 支持上传本地视频文件，并为视频组件命名（如"等差数列之歌"）。上传后显示视频文件名、格式、时长等信息。
        - **编辑视频**: 支持重新上传视频文件替换原有视频。
    - 满足用户场景: 教研生产老师为课程配置相关的教学视频。
    - 解决用户痛点: 无法方便地将视频资源整合到课程中。
    - 优先级：高
    - 依赖关系：添加教学组件

- **功能点6: 组件位置调整**
    - 简要描述: 教研生产老师可以调整已添加组件在Part内或Part之间的顺序。通过拖拽或点击移动操作，选择新的位置完成调整。
    - 满足用户场景: 教研生产老师优化课程内容的编排顺序。
    - 解决用户痛点: 课程内容顺序不合理，难以调整。
    - 优先级：高
    - 依赖关系：添加教学组件

- **功能点7: 完成配课与保存**
    - 简要描述: 教研生产老师完成所有组件配置后，可以保存当前配课进度。系统支持实时/半实时将修改提交至服务器。提供"保存"或"提交"按钮，将所有内容提交至服务器。
    - 满足用户场景: 教研生产老师保存配课工作，确保内容不丢失。
    - 解决用户痛点: 配课内容易丢失，无有效保存机制。
    - 优先级：高
    - 依赖关系：课程结构与组件管理

#### 2.1.3 整课预览 (教研老师 - 审核角色/生产角色)
- **功能点1: 整课预览访问**
    - 简要描述: 审核老师或生产老师通过指定的URL（审核地址或配课工具内的预览入口）访问课程的完整预览页面。访问需在内网VPN环境下。预览内容会随配课工具中的组件创建和内容更新实时/半实时修改。
    - 满足用户场景: 教研老师在课程正式上架前，整体检查课程内容和流程。
    - 解决用户痛点: 无法在发布前有效预览和审核课程的最终效果。
    - 优先级：高
    - 依赖关系：配课工具完成配课

- **功能点2: 课程内容预览**
    - 简要描述: 预览页面展示课程的完整内容，包括课程标题、更新时间。左侧为课程目录/分段导航（各Part及其标题、时长），右侧为内容播放区（主要是视频播放）。
        - **视频播放**: 支持播放/暂停、进度条拖动、字幕开关、倍速播放控制（如1.0x, 1.5x, 2.0x）。
        - **练习预览**: （根据原始文档，预览主要体现视频，练习部分如何在预览中体现需进一步明确，暂定为可查看题目内容）。
    - 满足用户场景: 教研老师检查课程各部分的具体内容、视频播放效果、字幕准确性等。
    - 解决用户痛点: 课程各元素效果未知，无法保证教学质量。
    - 优先级：高
    - 依赖关系：整课预览访问

### 2.2 辅助功能
- **辅助功能点1: 课程状态管理与流转**
    - 简要描述: 系统后台管理课程的几种状态：如"未上架态"、"上架态"、"修改待上架态"。状态根据用户操作（如提交修改、点击更新/上架）自动流转。
    - 为核心功能提供支持: 课程上架/更新

- **辅助功能点2: 错误与重复校验提示**
    - 简要描述: 在课程创建、编辑、上架等环节，系统对用户输入或操作进行校验，并给出明确的错误提示或确认提示。例如，稿件地址不存在、名称与地址组合已存在、上架条件不满足等。
    - 为核心功能提供支持: 课程创建, 课程编辑, 课程上架/更新

### 2.3 非本期功能
- **功能点1: 详细的任务管理与工作流**
    - 简要描述: 包含教研老师任务分配、进度跟踪、审核流程管理等。
    - 建议迭代X再考虑: 本期为快速上线MVP，暂不引入复杂任务管理。
- **功能点2: 前端UI模版动态配置**
    - 简要描述: 允许用户在前端自行修改和配置课程的UI模版（如头图、插图、Part图标等）。
    - 建议迭代Y再考虑: 本期UI模版由后台统一配置。
- **功能点3: "升级类"课程修改的完整用户通知与数据迁移方案**
    - 简要描述: 对于"升级类"修改，向老用户推送新课通知，并提供查看新课入口及可能的数据迁移方案。
    - 建议迭代Y再考虑: 本期简化处理，老用户看老课，新用户看新课。

## 3. 计算规则与公式
（原始需求文档中未明确包含复杂的计算规则与公式，本期主要为流程和管理工具，故此部分暂无内容。）

## 4. 用户场景与故事

### 场景1: 总部教研管理老师创建新课程 (单个创建)
作为一个 **总部教研管理老师**，我希望能够 **在课程管理后台手动创建一个新的AI课程**，这样我就可以 **快速录入单个课程的基础信息，并将其纳入系统管理**。目前的痛点是 **依赖线下表格或零散信息创建课程，效率低且易出错**，如果能够 **在系统中直接填写课程名称、稿件地址，并让系统自动填充部分关联信息，同时便捷关联业务知识点**，对我的工作会有很大帮助。

**关键步骤：**
1. 登录课程管理后台。
2. 点击"创建新课程"按钮。
3. 在创建表单中输入"课程名称"。
4. 输入课程的"稿件地址"。
5. 系统根据稿件地址自动填充"学段"、"学科"、"单课类型"、"数字人ID"（这些字段不可编辑）。若稿件地址无效或无法解析，则提示错误。
6. （可选）在"业务树知识点"区域，输入关键词搜索知识点，从下拉列表中选择一个或多个匹配的末级知识点进行关联。
7. 点击"提交"按钮。
8. 系统校验必填项（课程名称、稿件地址、至少一个业务树知识点 - 此项为上架时必填，创建时非必填）。若"名称"与"稿件地址"组合已存在，弹窗提示"该名称&地址已经创建过，确认创建新课程吗？"，选择"确认"则创建新ID课程，选择"取消"则不创建。
9. 校验通过后，系统保存课程信息，并在课程列表中显示新创建的课程。

```mermaid
graph TD
    A[用户登录课程管理后台] --> B{点击'创建新课程'};
    B --> C[进入创建新课程表单];
    C --> D[输入 课程名称];
    D --> E[输入 稿件地址];
    E -- 失去焦点或校验 --> F{系统根据稿件地址查询信息};
    F -- 成功 --> G[自动填充 学段, 学科, 单课类型, 数字人ID];
    F -- 失败/地址无效 --> H[提示稿件地址错误];
    H --> E;
    G --> I[输入关键词搜索业务树知识点];
    I --> J[选择一个或多个知识点关联];
    J --> K{点击'提交'按钮};
    K --> L{系统校验};
    L -- 名称与地址组合已存在 --> M{弹窗提示重复是否继续};
    M -- 是 --> N[创建新ID课程];
    M -- 否 --> O[不创建课程,停留在表单];
    L -- 必填项校验失败 --> O;
    L -- 校验通过 --> N;
    N --> P[保存课程信息];
    P --> Q[课程列表显示新课程];
```
- 优先级：高
- 依赖关系：用户登录与认证功能

### 场景2: 总部教研管理老师批量创建课程
作为一个 **总部教研管理老师**，我希望能够 **通过上传Excel文件批量创建AI课程**，这样我就可以 **一次性导入大量课程的基础信息，大幅提高工作效率**。目前的痛点是 **逐个手动创建课程非常耗时，且容易在复制粘贴中出错**，如果能够 **下载标准模板，填写课程名称和稿件地址后批量导入，并能看到处理结果和失败原因**，对我的工作会有很大帮助。

**关键步骤：**
1. 登录课程管理后台。
2. 点击"批量创建课程"按钮。
3. 在弹窗中，首先点击"下载模版"获取Excel模板文件。
4. 在本地打开模板，按照表头要求填写"课程名称"和"稿件地址"等信息并保存。
5. 返回弹窗，点击上传区域或拖拽方式上传填写好的Excel文件。
6. 系统解析上传的文件，并进行数据校验：
    - 检查稿件地址是否在系统中已作为其他课程的稿件地址被使用（若重复，根据策略忽略或提示）。
    - 检查课程名称与业务树知识点的匹配情况（若系统尝试自动匹配）。
    - 检查必填信息是否完整。
7. 系统展示批量处理结果，如"共XX条数据，成功XX条，失败XX条"。
8. 若有失败条数，提供"下载失败原因"链接，点击后可下载包含原始上传数据及新增"失败原因说明"列的Excel文件。
9. 用户确认无误后（或在处理完失败数据后重新上传），点击"创建"按钮（如果弹窗中有此步骤，否则可能是上传后自动处理）。
10. 系统根据校验通过的数据批量创建课程ID，并在课程列表中显示新创建的课程。

```mermaid
graph TD
    A[用户登录课程管理后台] --> B{点击'批量创建课程'};
    B --> C[弹出批量创建课程弹窗];
    C --> D[点击'下载模版'];
    D --> E[用户本地填写模版];
    E --> F[上传填写好的模版文件];
    F --> G{系统解析并校验文件内容};
    G -- 部分/全部校验失败 --> H[展示处理结果: 成功数, 失败数];
    H --> I[提供'下载失败原因'链接];
    I --> J[用户下载并查看失败原因];
    J --> K[用户修正数据后可选择重新上传];
    K --> F;
    G -- 全部校验通过 --> L[展示处理结果: 全部成功];
    L --> M{点击'创建'按钮或系统自动处理};
    M --> N[系统批量创建课程ID];
    N --> O[课程列表显示新创建的课程];
    H -- 无失败或用户确认继续 --> M;
```
- 优先级：高
- 依赖关系：用户登录与认证功能

### 场景3: 教研生产老师配置课程组件 (添加练习组件)
作为一个 **教研生产老师**，我希望能够 **在配课工具中为课程的特定Part添加练习组件，并从题库中选择或通过ID输入题目**，这样我就可以 **为学生提供配套的练习来巩固所学知识**。目前的痛点是 **手动整理和插入题目效率低下，且难以管理题目版本和复用性**，如果能够 **方便地在课程结构中插入练习组件，并快速关联题库中的题目**，对我的配课工作会有很大帮助。

**关键步骤：**
1. 教研生产老师通过课程ID拼接的URL访问配课工作台。
2. 在课程结构中，定位到需要添加练习组件的Part或位置。
3. 点击"+"添加组件"按钮或相应图标。
4. 在弹出的组件类型选择框中，选择"练习组件"。
5. 系统在指定位置插入一个新的练习组件编辑区域。
6. 在练习组件编辑区域，点击"添加题目"或类似按钮。
7. （方式一：ID输入）在弹出的输入框中，按照提示格式（如顿号分隔、圆括号分组）输入一个或多个题目ID。点击确认。
8. （方式二：题库选择，若支持）系统展示题库筛选/搜索界面，教研老师选择所需题目。
9. 系统根据输入的ID或选择从题库拉取题目信息，并在练习组件中展示题目列表。
10. 教研老师可以对已添加的题目进行排序、换题或删除操作。
11. 完成练习组件配置后，点击"保存"或系统自动保存。

```mermaid
graph TD
    A[教研老师访问配课工作台] --> B[定位到目标Part或位置];
    B --> C{点击'+添加组件'};
    C --> D[选择'练习组件'类型];
    D --> E[系统插入练习组件编辑区];
    E --> F{点击'添加题目'};
    F --> G{选择添加题目方式};
    G -- 输入题目ID --> H[输入一个或多个题目ID];
    H --> I[系统根据ID获取题目信息];
    G -- 从题库选择 (若支持) --> J[打开题库选择界面];
    J --> K[选择题目];
    K --> I;
    I --> L[在练习组件中展示题目列表];
    L --> M[可进行题目排序/换题/删除];
    M --> N{点击'保存'或自动保存};
    N --> O[练习组件配置完成];
```
- 优先级：高
- 依赖关系：配课工作台访问, 课程结构与组件管理

### 场景4: 教研审核老师预览整课内容
作为一个 **教研审核老师**，我希望能够 **通过审核地址方便地预览整个AI课程的最终效果**，这样我就可以 **在课程上架前检查内容准确性、流程完整性和用户体验**。目前的痛点是 **缺乏统一的、所见即所得的审核入口，导致审核效率低，问题发现不及时**，如果能够 **通过一个链接完整体验课程的视频播放、字幕、分段导航等**，对我的审核工作会有很大帮助。

**关键步骤：**
1. 审核老师获取到待审核课程的"审核地址"URL。
2. 在PC端Web浏览器中（内网VPN环境下）打开该URL。
3. 系统加载并展示课程预览页面。
4. 页面顶部显示课程标题和最近更新时间。
5. 页面左侧显示课程的目录/分段列表，包含各Part的标题和预估时长。审核老师可以点击目录项跳转到对应Part。
6. 页面右侧为内容播放区域，默认播放课程的第一个Part或指定起始点。
7. 对于视频内容：
    - 观看视频播放。
    - 检查播放进度条是否正常工作，是否可以拖动。
    - 检查字幕是否与语音同步，是否可以正常开关。
    - 检查倍速播放功能是否可用且效果正常。
8. 对于练习内容（若预览包含）：检查题目展示是否清晰，选项是否正确等。
9. 检查课程整体流程是否顺畅，内容是否存在错漏。
10. (可选) 记录审核意见（通过外部方式或未来集成的审核系统）。

```mermaid
graph TD
    A[审核老师获取审核地址URL] --> B[在浏览器中打开URL (VPN环境)];
    B --> C[系统加载课程预览页面];
    C --> D[显示课程标题和更新时间];
    D --> E[左侧显示课程目录/分段列表];
    E -- 点击目录项 --> F[右侧内容区跳转到对应Part];
    D --> G[右侧内容区默认播放起始Part];
    subgraph 视频播放交互
        G --> H[观看视频];
        H --> I{检查播放控件};
        I -- 进度条 --> J[可拖动];
        I -- 字幕 --> K[可开关,与语音同步];
        I -- 倍速 --> L[可调整];
    end
    subgraph 练习内容预览 (若有)
        G --> M[查看练习题目];
        M --> N[检查题目内容和选项];
    end
    G --> O[检查课程整体流程和内容准确性];
    O --> P[记录审核意见 (外部)];
```
- 优先级：高
- 依赖关系：整课预览访问, 课程内容预览

## 5. 业务流程图

### 5.1 课程创建与管理流程 (总部教研管理老师)
```mermaid
graph TD
    A[登入课程管理系统] --> B{选择操作};
    B -- 单个创建 --> C[进入单个课程创建页面];
    C --> D[填写课程名称、稿件地址等];
    D --> E{系统校验信息};
    E -- 校验通过 --> F[保存课程];
    F --> G[课程列表展示];
    E -- 校验失败 --> H[提示错误];
    H --> D;
    B -- 批量创建 --> I[进入批量课程创建页面];
    I --> J[下载Excel模板];
    J --> K[用户填写模板并上传];
    K --> L{系统解析与校验模板数据};
    L -- 部分/全部失败 --> M[提示处理结果与失败原因];
    M --> K;
    L -- 全部成功 --> N[批量创建课程];
    N --> G;
    G --> O{选择课程进行操作};
    O -- 编辑 --> P[进入课程编辑页面];
    P --> Q[修改课程信息];
    Q --> R{系统校验信息};
    R -- 校验通过 --> S[保存修改];
    S --> T[更新课程状态为'待上架更新'];
    T --> G;
    R -- 校验失败 --> U[提示错误];
    U --> Q;
    O -- 查看详情 --> V[查看课程详细信息与链接];
    O -- 上架/更新 --> W{确认上架/更新};
    W -- 是 --> X{系统校验上架条件};
    X -- 条件满足 --> Y[执行上架/更新操作];
    Y --> Z[更新课程状态为'上架态'];
    Z --> G;
    X -- 条件不满足 --> AA[提示不满足上架条件];
    AA --> O;
    W -- 否 --> O;
```

### 5.2 配课与预览流程 (教研老师)
```mermaid
graph TD
    A[教研生产老师访问配课工作台] --> B[选择或创建Part];
    B --> C{添加教学组件};
    C -- 选择练习组件 --> D[配置练习题(ID输入/题库选择)];
    D --> E[调整题目顺序/换题/删除];
    E --> F[保存练习组件];
    C -- 选择视频组件 --> G[上传视频文件并命名];
    G --> H[保存视频组件];
    F --> I[调整组件在Part内顺序];
    H --> I;
    I --> J[完成当前Part配课];
    J --> K[继续配置其他Part或保存整课];
    K -- 保存整课 --> L[配课内容提交至服务器];
    L --> M[教研老师/审核老师访问预览地址];
    M --> N[预览整课内容];
    N --> O[检查视频播放、字幕、流程等];
    O -- 发现问题 --> P[反馈修改 (线下或后续系统)];
    P --> A;
    O -- 审核通过 --> Q[通知总部教研管理老师可上架];
```

## 6. 性能与安全需求

### 6.1 性能需求
- **响应时间：**
    - 课程列表加载（含筛选/搜索）：首次加载不超过3秒，后续操作（翻页、筛选）不超过2秒。
    - 单个/批量课程创建、编辑、上架操作确认后，后台处理完成并在前端给出反馈的时间不超过5秒（不含文件上传时间）。
    - 配课工具中组件添加、删除、位置调整等操作，前端响应时间应在1秒内，与服务端交互保存后反馈不超过3秒。
    - 整课预览页面加载时间不超过3秒，视频首帧加载时间不超过2秒（依赖视频本身）。
- **并发用户：**
    - 课程管理工具需支持至少50个总部教研管理老师同时在线操作。
    - 配课工具需支持至少100个教研生产老师同时在线配课。
    - 整课预览模块需支持至少100个用户同时预览不同课程。
- **数据量：**
    - 系统需支持管理至少10,000门课程，每门课程平均包含10个Part，每个Part平均包含3个组件。
    - 题库需支持管理至少100,000道题目。
    - 视频文件存储和管理能力需满足业务增长需求。

### 6.2 安全需求
- **权限控制：**
    - **课程管理工具**：仅限后台配置的白名单手机号，通过手机验证码成功登录的总部教研管理老师可访问和操作。不同角色（如果后续细分）应有明确的操作权限边界。
    - **配课工具**：通过特定课程ID生成的URL访问，需结合内网VPN限制，确保仅授权教研生产老师可编辑对应课程。
    - **整课预览地址**：通过特定URL访问，需结合内网VPN限制，确保仅授权教研老师（生产或审核）可访问。
- **数据安全：**
    - 所有用户登录凭证（如session, token）需加密传输和存储。
    - 课程数据、题目数据、用户数据等核心业务数据在存储和传输过程中应考虑加密措施，特别是对于外部不可见的稿件地址等敏感链接。
    - 防止SQL注入、XSS攻击、CSRF攻击等常见的Web安全风险。
    - 上传文件（如批量创建的Excel、视频文件）需进行安全扫描，防止恶意文件上传。
- **操作审计：**
    - 对课程的创建、编辑、删除、上架、更新等关键操作需要记录详细的操作日志，包括操作人、操作时间、操作IP、操作对象及操作前后数据摘要（可选）。
    - 对用户登录、登出行为进行记录。
- **访问控制：**
    - 所有核心功能的访问必须经过内网VPN。

## 7. 验收标准

### 7.1 功能验收

- **课程管理工具 (总部教研管理老师)**
    - **用户登录与认证**：
        - 使用场景：总部教研管理老师在内网VPN环境下，访问课程管理后台登录页。
        - 期望结果：输入白名单内的手机号，获取并输入正确的验证码后，成功登录系统。输入非白名单手机号或错误验证码，登录失败并有明确提示。
    - **课程创建（单个）**：
        - 使用场景：登录后，点击"创建新课程"，填写所有必填信息（名称、有效稿件地址、关联至少一个业务树知识点（上架前））。
        - 期望结果：课程创建成功，并在课程列表中可见。无效稿件地址、重复的名称与地址组合（用户选择不继续时）应无法创建并有提示。根据稿件地址自动填充功能正常。
    - **课程创建（批量）**：
        - 使用场景：登录后，点击"批量创建课程"，下载模板，填写数据后上传。
        - 期望结果：系统能正确解析模板数据，校验通过的数据成功创建课程，失败数据提供明确失败原因供下载。课程列表更新。
    - **课程列表展示与筛选**：
        - 使用场景：访问课程列表页。
        - 期望结果：正确显示课程列表及各字段信息。搜索、筛选、排序功能按预期工作，结果准确。下载列表功能正常。
    - **课程编辑**：
        - 使用场景：在课程列表中选择一个课程进行编辑。
        - 期望结果：可修改课程名称、稿件地址、业务树知识点。修改后保存成功，列表信息更新。无效稿件地址不可保存。
    - **查看课程详情**：
        - 使用场景：在课程列表中选择一个课程查看详情。
        - 期望结果：正确展示课程名称及所有相关链接（线上预览、稿件、画板、配课），复制链接功能正常。修改预览链接功能正常。状态显示准确。
    - **课程上架/更新**：
        - 使用场景：对符合上架条件的课程执行上架操作，或对已上架课程执行更新操作。
        - 期望结果：二次确认弹窗正常。符合条件的课程成功上架/更新，状态变为"上架态"，操作按钮变为"更新"。不符合条件的课程无法上架并有提示。

- **配课工具 (教研老师 - 配课角色)**
    - **配课工作台访问与组件管理**：
        - 使用场景：通过课程ID拼接的URL访问配课页面。
        - 期望结果：成功进入对应课程的配课工作台。能清晰看到课程的Part结构。能通过点击"+"在指定位置添加练习或视频组件。
    - **练习组件配置**：
        - 使用场景：添加练习组件后，通过ID输入或题库选择题目。
        - 期望结果：题目能正确添加到组件中。能对题目进行排序、换题、删除。保存后配置生效。
    - **视频组件配置**：
        - 使用场景：添加视频组件后，上传本地视频并命名。
        - 期望结果：视频上传成功，组件显示视频名称和基本信息。可重新上传替换视频。保存后配置生效。
    - **组件位置调整**：
        - 使用场景：拖拽或通过移动操作调整组件顺序。
        - 期望结果：组件顺序成功调整并保存。
    - **完成配课与保存**：
        - 使用场景：完成所有配课操作后点击保存/提交。
        - 期望结果：所有配课内容成功保存到服务器。

- **整课预览 (教研老师 - 审核角色/生产角色)**
    - **整课预览访问与内容展示**：
        - 使用场景：通过审核地址或预览入口访问课程预览页面。
        - 期望结果：成功加载课程预览。左侧目录导航正常，点击可跳转。右侧内容区正确播放对应Part的视频。
    - **视频播放功能**：
        - 使用场景：在预览页面播放视频。
        - 期望结果：视频播放/暂停、进度条拖动、字幕开关、倍速播放功能均按预期工作。

### 7.2 性能验收
- **响应时间**：
    - 给定并发用户在正常网络环境下，各项操作的响应时间应满足6.1中定义的标准。
- **并发用户**：
    - 模拟6.1中定义的并发用户数进行操作，系统应稳定运行，无明显卡顿或错误，各项功能响应时间仍在可接受范围内。

### 7.3 安全验收
- **权限控制**：
    - 给定非授权用户（如未在白名单的手机号、无VPN权限的外部用户），尝试访问课程管理后台、配课工具、预览地址。
    - 期望结果：非授权用户无法访问受限资源，系统返回权限错误或引导至登录页。授权用户可以正常访问其权限范围内的功能。
- **数据安全**：
    - 尝试进行常见的Web攻击（如SQL注入、XSS）。
    - 期望结果：系统能有效防御，不产生安全漏洞，数据不被泄露或篡改。
- **操作审计**：
    - 执行关键操作（如课程创建、上架）。
    - 期望结果：后台操作日志正确记录相关信息。

## 8. 其他需求

### 8.1 可用性需求
- **界面友好**：
    - 课程管理后台、配课工具的界面布局清晰、简洁，符合用户操作习惯。
    - 核心功能（如创建课程、添加组件、上架）的操作步骤应尽可能少，引导明确。
- **容错处理**：
    - 用户进行非法操作或输入无效数据时（如上传格式错误的Excel，输入不存在的题目ID），系统应给出友好、明确的错误提示，并引导用户修正。
    - 网络异常或服务端处理超时，前端应有相应提示，避免用户长时间等待或数据丢失。
- **兼容性**：
    - 配课工具、课程管理后台、预览页面需支持主流PC浏览器（如Chrome最新版、Edge最新版），保证界面和功能正常。

### 8.2 维护性需求
- **配置管理**：
    - 用户白名单手机号、业务树知识点数据、UI模版（本期后台配置）等应易于管理和更新。
    - 课程状态、组件类型等关键枚举值应在代码或配置中清晰定义。
- **监控告警**：
    - 对核心服务（如数据库、文件存储、关键API接口）的运行状态进行监控。
    - 发生关键错误（如批量创建失败率过高、上架服务异常）时，系统能自动告警并通知相关技术人员。
- **日志管理**：
    - 系统应记录详细的运行日志（DEBUG、INFO、WARN、ERROR、FATAL级别），包含请求ID、用户ID（若适用）、时间戳、关键操作及错误信息。
    - 日志不应记录用户手机号、验证码等敏感信息。
    - 日志应易于查询和分析，以便于问题排查和系统维护。

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 用户（教研老师、总部教研管理老师）可以通过指定的沟通渠道（如内部通讯工具群、邮件联系人）提交使用反馈、bug报告和功能建议。
- 定期（如每双周）收集和整理用户反馈，由产品和技术团队共同评审，识别高频问题和有价值的改进点。

### 9.2 迭代计划
- **MVP版本上线 (本期)**：完成P0核心功能，打通课程创建、配课、预览、上架主流程。
- **迭代1 (上线后X周内)**：
    - 根据MVP版本用户反馈，修复高优先级bug，优化核心操作体验。
    - 考虑增加"纠错类"修改场景下更细致的元素修改支持（如具体到字幕、板书、音频等，并明确对应修改工具的联动）。
- **迭代2 (上线后Y月内)**：
    - 探讨并引入初步的审核工作流，如审核状态标记、审核意见记录等。
    - 优化配课工具，如支持更多组件类型，或对现有组件功能进行增强。
- **后续迭代**：
    - 逐步完善任务管理系统。
    - 开放前端UI模版配置能力。
    - 完善"升级类"课程修改的用户通知和数据处理方案。

## 10. 需求检查清单

使用下面的检查清单，确保原始需求文档中的关键点都被正确地包含和体现在本需求文档中。

| 原始需求 (来自"五、详细产品方案"及相关章节)                                 | 对应需求点 (本文档章节或具体功能点)                                                                      | 完整性 | 正确性 | 一致性 | 可验证性 | 可跟踪性 | 备注                                                                                                                               |
| --------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------- | ------ | ------ | ------ | -------- | -------- | -------------------------------------------------------------------------------------------------------------------------------- |
| **课程管理工具**                                                          |                                                                                                          |        |        |        |          |          |                                                                                                                                  |
| 手机号验证码登录：总部教研身份校验                                                | 2.1.1 功能点1: 用户登录与认证                                                                                | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 课程管理：展示课程筛选 (顶部页面名称、登录人、课程筛选和列表)                               | 2.1.1 功能点3: 课程列表展示与筛选                                                                            | ✅      | ✅      | ✅      | ✅        | ✅        | 包含筛选和列表信息                                                                                                                     |
| 课程管理：展示课程列表 (列表交互操作：编辑、上架、查看详情；排序；下载列表)                             | 2.1.1 功能点3: 课程列表展示与筛选                                                                            | ✅      | ✅      | ✅      | ✅        | ✅        | 包含交互操作、排序、下载                                                                                                               |
| 课程创建：课程的批量创建 (下载模版、上传、解析、结果反馈、下载失败原因、点击创建)                         | 2.1.1 功能点2: 课程创建 (批量创建部分)                                                                       | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 课程创建：课程的单个创建 (必填名称、地址；边界case校验；学段等自动填充；业务树知识点搜索添加；上架时知识点必填) | 2.1.1 功能点2: 课程创建 (单个创建部分)                                                                       | ✅      | ✅      | ✅      | ✅        | ✅        | 包含所有校验规则和字段处理逻辑                                                                                                         |
| 课程编辑：课程的编辑 (可改名称、地址；学段等自动填充不可改；业务树知识点修改；上架时知识点必填)                   | 2.1.1 功能点4: 课程编辑                                                                                    | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 课程修改地址：查看详情 (线上信息：课程名称、各地址链接及复制/修改功能、最近更新状态、修改预览链接)                  | 2.1.1 功能点5: 查看课程详情                                                                                | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 课程上架：课程上架/批量上架 (二次弹窗确认；判定上架条件；上架后按钮变更新)                               | 2.1.1 功能点6: 课程上架/更新                                                                               | ✅      | ✅      | ✅      | ✅        | ✅        | 提及状态流转图 board_WOzawEpPChP6PebS9LFcsdOSnIe (未上架态 -> 上架态 -> 修改待上架态 -> 上架态) 的逻辑已融入上架/更新描述中，并增加纠错/升级类修改说明 |
| **配课工具**                                                            |                                                                                                          |        |        |        |          |          |                                                                                                                                  |
| 配课工具访问：教研生产老师通过课程id+url访问 (内网vpn)                                   | 2.1.2 功能点1: 配课工作台访问                                                                              | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| UI模版配置 (本期无需前端修改，按学段-学科构建唯一模版，含头图/插图/尾图/Part图标)                            | 2.1.2 功能点2: 课程结构与组件管理 (UI模版说明)                                                                 | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 添加组件：点击"+"选择插入位置和组件类型 (练习/视频)                                       | 2.1.2 功能点3: 添加教学组件                                                                                | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 练习组件：添加题目 (输入题目id，顿号分隔，圆括号分组)                                        | 2.1.2 功能点4: 练习组件配置 (添加题目部分)                                                                   | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 练习组件：编辑题组 (预览编辑：换题、上/下移动、删除)                                         | 2.1.2 功能点4: 练习组件配置 (编辑题组部分)                                                                   | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 视频组件：添加视频 (上传本地视频、填入名称)                                             | 2.1.2 功能点5: 视频组件配置 (添加视频部分)                                                                   | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 视频组件：编辑视频 (重新上传)                                                     | 2.1.2 功能点5: 视频组件配置 (编辑视频部分)                                                                   | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 组件位置修改：拖拽或点击移动调整顺序                                                  | 2.1.2 功能点6: 组件位置调整                                                                                | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 完成配课：实时/半实时提交至服务器，点击"提交"保存所有内容                                        | 2.1.2 功能点7: 完成配课与保存                                                                              | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| **整课预览**                                                            |                                                                                                          |        |        |        |          |          |                                                                                                                                  |
| 整课预览访问：审核/生产老师通过URL访问 (内网vpn)，内容实时/半实时更新                                 | 2.1.3 功能点1: 整课预览访问                                                                                | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| 课程内容预览：课程标题、更新时间、左侧目录导航 (Part标题/时长)、右侧内容播放区 (视频、字幕、倍速、进度条)          | 2.1.3 功能点2: 课程内容预览                                                                                | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| **名词说明中提及的实体和修改场景**                                                   |                                                                                                          |        |        |        |          |          |                                                                                                                                  |
| 视频组件定义 (本地视频插入，名称+文件上传)                                            | 2.1.2 功能点5: 视频组件配置                                                                                | ✅      | ✅      | ✅      | ✅        | ✅        | 已覆盖                                                                                                                             |
| 练习组件定义 (题库id输入，id列表+预览编辑)                                            | 2.1.2 功能点4: 练习组件配置                                                                                | ✅      | ✅      | ✅      | ✅        | ✅        | 已覆盖                                                                                                                             |
| UI模版定义 (本期后台定，含图和Part图标)                                             | 2.1.2 功能点2: 课程结构与组件管理 (UI模版说明)                                                                 | ✅      | ✅      | ✅      | ✅        | ✅        | 已覆盖                                                                                                                             |
| 课程实体初始化 (Part源于稿件地址，批量创建忽略已用稿件地址，单个创建可复用，一课程ID可对应多业务树知识点)           | 2.1.1 功能点2: 课程创建 (相关逻辑已包含)                                                                      | ✅      | ✅      | ✅      | ✅        | ✅        | 已覆盖                                                                                                                             |
| 上架后修改预期：「纠错类」与「升级类」修改区分                                             | 2.1.1 功能点6: 课程上架/更新 (已纳入说明)                                                                     | ✅      | ✅      | ✅      | ✅        | ✅        | 已覆盖基本原则                                                                                                                       |
| case 0: UI模版替换 (图片、文字颜色) - 配课工具：模版工具 - C端直接更新，不影响内容展示                        | 2.2.3 非本期功能点2 (UI模版动态配置) 及 2.1.2 功能点2 (本期后台配置)                                                | ⚠️      | ✅      | ✅      | ⚠️        | ⚠️        | 本期模版后台配置，前端不提供修改工具。C端更新逻辑正确，但不影响内容展示依赖具体实现。此case更偏向未来迭代。                                                      |
| case 1: 题目有错，不重批 - 题库改ID，一键发布引用课程 - C端更新                                  | 外部依赖：题库系统。本PRD范围外，但影响课程内容更新。                                                                   | N/A    | N/A    | N/A    | N/A      | N/A      | 涉及题库系统修改及与课程工具联动，本期PRD侧重课程工具本身。纠错类更新原则在2.1.1 功能点6中已提及。                                                       |
| case 2: 题目有错，需重批 - 题库改+重批 - 同步师生端                                      | 外部依赖：题库系统及批改系统。本PRD范围外。                                                                    | N/A    | N/A    | N/A    | N/A      | N/A      | 同上。                                                                                                                             |
| case 3: 题目太难，数量减少 - 配课工具：模版工具 - 旧生不变，新生更新                               | 2.1.2 功能点4 (练习组件编辑题组可删题)；更新逻辑符合"升级类"简化版，但此处指"配课工具：模版工具"不准确，应为"配课工具：练习组件" | ✅      | ⚠️      | ✅      | ✅        | ✅        | "模版工具"应为"练习组件配置"，学生端更新逻辑符合本期"升级类"简化原则。                                                                             |
| case 4: MV视频有错 - 配课工具：视频工具 - C端更新                                      | 2.1.2 功能点5 (视频组件可重新上传)；更新逻辑符合"纠错类"                                                              | ✅      | ✅      | ✅      | ✅        | ✅        |                                                                                                                                  |
| case 5: 人工圈画有错 - 圈画工具 - C端更新，完成状态不变                                   | 外部依赖：圈画工具。本PRD范围外。                                                                         | N/A    | N/A    | N/A    | N/A      | N/A      | "圈画工具"为上游，其修改后的内容更新到本系统管理的课程中，C端更新逻辑符合"纠错类"。                                                                   |
| case 6: 板书/系统圈画/图片有错 - 逐字稿：板书工具+圈画工具提示重录                               | 外部依赖：逐字稿工具、板书工具、圈画工具。本PRD范围外。                                                               | N/A    | N/A    | N/A    | N/A      | N/A      | 同上。                                                                                                                             |
| case 7: 字幕有错 - 逐字稿：字幕工具                                                | 外部依赖：逐字稿工具、字幕工具。本PRD范围外。                                                                   | N/A    | N/A    | N/A    | N/A      | N/A      | 同上。                                                                                                                             |
| case 8: 朗读稿/音频有错 - 逐字稿：朗读稿工具                                            | 外部依赖：逐字稿工具、朗读稿工具。本PRD范围外。                                                                 | N/A    | N/A    | N/A    | N/A      | N/A      | 同上。                                                                                                                             |
| case 9: Part内容修改/删减 (标题、顺序、内容) - 逐字稿：part编辑                               | 2.1.2 功能点2 (Part结构管理，隐含编辑能力)，功能点6 (组件位置调整)，功能点4&5 (组件内容编辑/删除)                          | ✅      | ✅      | ✅      | ✅        | ✅        | "逐字稿：part编辑"可能指上游工具，本系统提供配课工具对Part内组件和顺序的调整。Part标题修改能力可补充。                                                      |

- **完整性**：原始需求是否都有对应的优化后需求点，无遗漏
- **正确性**：优化后需求描述是否准确表达了原始需求的意图
- **一致性**：优化后需求之间是否有冲突或重复
- **可验证性**：优化后需求是否有明确的验收标准
- **可跟踪性**：优化后需求是否有明确的优先级和依赖关系

使用✅表示通过；使用❌表示未通过；使用⚠️表示描述正确，但细节不完整（如计算规则、阈值等，需要和 PM 进一步沟通）；使用N/A表示不适用，每个需求点都需要填写。

## 11. 附录

### 11.1 原型图
（原始需求文档中包含大量UI界面截图，这些截图可作为原型图的参考。此处不重复列出，实际项目中应指向UE/UI设计稿链接。）
- UE设计稿：[https://mastergo.com/file/149512448061157?fileOpenFrom=home&page_id=0%3A2](https://mastergo.com/file/149512448061157?fileOpenFrom=home&page_id=0%3A2) (来自原始文档)

### 11.2 术语表
- **AI课**：指集成了人工智能技术的在线课程。
- **配课**：指为AI课程配置教学内容、组件（如视频、练习）、流程和参数的过程。
- **课程管理工具**：指供总部教研管理老师进行课程创建、编辑、上架、管理等操作的后台系统。
- **配课工具**：指供教研生产老师进行具体课程内容（如视频、练习组件）配置和编排的在线工具。
- **Part**：课程内容结构中的一个组成部分或单元，类似于章节或模块。
- **组件 (教学组件)**：指构成课程内容的独立教学元素，如视频组件、练习组件等。
- **业务树知识点**：指内部知识库体系中定义的标准化知识点，用于对课程内容进行精准分类和关联。
- **稿件地址**：指存放课程原始素材（如文稿、视频脚本等）的URL或路径。
- **数字人ID**: 指AI虚拟教师的唯一标识。
- **VPN**: Virtual Private Network，虚拟专用网络，用于安全访问内网资源。
- **MVP**: Minimum Viable Product，最小可行产品。

</rewritten_file> 
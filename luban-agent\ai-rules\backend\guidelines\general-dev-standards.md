# 通用开发标准 (General Development Standards)

## 1. 总则

本文档定义了项目通用的开发标准和各层级组件的验收标准，旨在确保代码质量、一致性和可维护性。所有开发者和AI助手在开发过程中均需遵循这些标准。

## 2. DAO 层验收标准

DAO (Data Access Object) 层的实现必须满足以下标准，以确保数据访问的正确性、高效性和健壮性。

### 2.1. 接口实现与规范性

*   **[ADS001] 完整性**: DAO 实现必须完整实现其在 Domain 层定义的相应 Repository 接口中的所有方法。不允许有遗漏。
*   **[ADS002] 命名规范**:
    *   实现文件命名为 `{entity}_impl.go` (例如 `user_impl.go`)。
    *   结构体命名为 `{EntityName}DAO` (例如 `UserDAO`)，并作为 Repository 接口的实现者。
    *   方法命名与其接口定义保持一致。
*   **[ADS003] GORM Model**:
    *   正确使用 GORM Model (通常在 `model/entity` 或 `biz_module/model/entity` 下定义) 进行数据库操作。
    *   Model 定义应与数据库表结构严格对应，并遵循 `rules-golang-code.mdc` 中关于结构体和字段的命名规范（如 JSON tag 小写驼峰，DB表字段 snake_case）。
*   **[ADS004] 依赖注入**: DAO 实例应通过依赖注入（如 Wire）方式创建和管理，通常注入 `gorm.DB` 实例和日志实例。

### 2.2. 功能正确性

*   **[ADS005] CRUD 操作**: 所有标准的 Create, Read (by ID, by conditions), Update, Delete 操作必须按预期工作。
    *   Create: 成功创建记录，返回正确的ID或实体，处理可能的唯一约束冲突。
    *   Read: 能够正确检索单个或多个记录；对于未找到记录的情况，应返回明确的错误 (如 `gorm.ErrRecordNotFound` 或自定义的 `ErrNotFound` sentinel error)。
    *   Update: 仅更新指定字段，正确处理并发控制（如乐观锁版本号）。
    *   Delete: 正确删除记录（物理删除或软删除按项目规范）。
*   **[ADS006] 业务查询**: 特定业务逻辑的查询（如列表查询、聚合查询）必须返回正确的数据集，并正确处理过滤、排序、分页参数。
*   **[ADS007] 参数校验**: 对关键输入参数（如ID格式、非空指针）进行必要的断言或校验，尽管主要校验逻辑在Service或Controller层，DAO层也应有一定的防御性。

### 2.3. 错误处理

*   **[ADS008] 错误返回**: 所有公共方法在操作失败时必须返回 `error` 类型。
*   **[ADS009] 错误包装**:
    *   从数据库驱动（如 GORM）返回的错误应进行包装，添加操作上下文信息，使用 `fmt.Errorf("operation X failed for ID %v: %w", id, err)`。
    *   优先返回在 Domain 层或 `app/consts/errors` 中定义的 sentinel errors (如 `ErrNotFound`, `ErrDuplicateEntry`)，或使用 `errors.Is/As` 进行判断。
*   **[ADS010] 无数据处理**: 查询无结果时，应明确返回 `gorm.ErrRecordNotFound` 或项目统一的记录未找到错误。不允许返回 `(nil, nil)` 掩盖错误。

### 2.4. 性能与效率

*   **[ADS011] 查询优化**:
    *   避免 `SELECT *`，只查询必要的字段。
    *   对高频查询和条件查询涉及的字段建立合适的数据库索引，并在代码中确保查询能利用这些索引。
    *   复杂查询应考虑其执行计划。
*   **[ADS012] 批量操作**: 对于批量插入、更新、删除操作，应使用GORM提供的批量操作方法，避免在循环中执行单条SQL。
*   **[ADS013] 预加载 (Preloading)**: 对于关联查询，合理使用 GORM 的 `Preload` 功能，避免N+1查询问题。
*   **[ADS014] 事务控制**:
    *   写操作（Create, Update, Delete）或需要原子性的多个读写操作组合，应在 Service 层控制事务边界，DAO 层方法本身通常不主动开启和提交事务，而是接收事务上下文（如 `*gorm.DB` 实例）。
    *   如果DAO内部确实需要封装小型事务，需确保其正确提交或回滚。

### 2.5. 可观察性 (Logging & Tracing)

*   **[ADS015] 日志记录**:
    *   在关键操作（如执行SQL前、操作成功/失败后）记录必要的日志，遵循 `luban-agent/ai-rules/backend/guidelines/rules-logging-code.md` 规范。
    *   日志级别恰当（通常为Debug级别，错误时为Error级别）。
    *   日志内容包含操作类型、实体、关键参数（脱敏）、耗时、错误信息（如果发生）。
    *   禁止在DAO层使用 `fmt.Println`。
*   **[ADS016] Context传递**: 所有DAO方法都应接受 `context.Context` 作为第一个参数，并将其传递给GORM调用，以支持超时控制和分布式追踪。

### 2.6. 安全性

*   **[ADS017] SQL注入防护**: 必须使用GORM的参数化查询或预编译语句，严禁手动拼接SQL字符串。
*   **[ADS018] 输入清理**: 虽然主要责任在上层，但DAO层应对传入的不可信数据保持警惕。

### 2.7. 单元测试

*   **[ADS019] 测试覆盖率**: DAO层单元测试应达到语句覆盖率 > 90%，分支覆盖率 > 80%。(具体遵循 `ai-dev-new-project-workflow.md` 中DAO单元测试的验收标准)
*   **[ADS020] Mocking**: 有效使用 `gomock` 或类似框架 mock 数据库依赖 (`sqlmock` 配合 GORM)，测试各种成功和失败场景。
*   **[ADS021] 测试场景**: 覆盖包括正常CRUD、边界条件、错误条件（如数据库连接失败、记录未找到、约束冲突）等。
*   **[ADS022] 表格驱动测试**: 优先使用表格驱动测试组织测试用例。

### 2.8. 代码风格与可维护性

*   **[ADS023] 遵循规范**: 严格遵循 `luban-agent/ai-rules/backend/project-rules/golang/rules-golang-code.mdc` 和 `luban-agent/ai-rules/backend/project-rules/golang/rules-kratos-code.mdc`。
*   **[ADS024] 清晰简洁**: 代码逻辑清晰，避免不必要的复杂性。
*   **[ADS025] 注释**: 对公共方法和复杂逻辑块提供必要的注释。

## 3. Service 层验收标准

*(待补充，可参照DAO层标准结构，关注业务逻辑、事务管理、与其他服务交互等方面)*

## 4. Controller 层验收标准

*(待补充，可参照DAO层标准结构，关注请求处理、参数校验、响应格式化、API规范等方面)*

## 5. DTO 定义验收标准

*(待补充，关注字段命名、类型、JSON Tag、数据校验Tag、与领域模型映射等方面)*

## 6. Domain 模型定义验收标准

*(待补充，关注结构体设计、值对象、实体、聚合根、领域事件接口定义等方面)*

---

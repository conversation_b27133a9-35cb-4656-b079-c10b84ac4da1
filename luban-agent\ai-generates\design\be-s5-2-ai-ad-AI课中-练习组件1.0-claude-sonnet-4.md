# 接口使用文档 - AI课中练习组件1.0 - V1.0

* **最后更新日期**: 2025-05-28
* **设计负责人**: claude-sonnet-4
* **评审人**: 待填写
* **状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-05-28 | claude-sonnet-4 | 初始草稿     |

## 1. 服务概述与设计目标

### 1.1 服务背景与范围
本服务提供AI课中练习组件的核心功能，包括练习环节的转场动效、核心交互和即时反馈机制。通过优化练习组件的交互体验，提升学生作答流畅度和情感反馈体验，增强课程满意度与学习效果。预期学生课中练习完成率提升15%，学生课程满意度达到85%以上。

### 1.2 设计目标回顾
1. 提供符合 OpenAPI 3.x 规范的接口定义，确保可直接导入 Apifox 等工具。
2. 清晰定义每个接口的请求、响应、参数、数据模型和错误码。
3. 确保接口设计满足产品需求文档 (PRD) 中定义的核心用户场景和业务流程。
4. 遵循团队的API设计规范和最佳实践。
5. 接口定义包含完整的中文描述信息，便于理解和维护。

## 2. 通用设计规范与约定

### 2.1 数据格式
* 请求体 (Request Body): `application/json`
* 响应体 (Response Body): `application/json`
* 日期时间格式: ISO 8601 (`YYYY-MM-DDTHH:mm:ss.sssZ`)

### 2.2 认证与授权
* 所有接口需要JWT认证，在请求头中传递 `Authorization: Bearer <token>`
* 验证用户身份和课程访问权限，确保学生只能访问自己的练习数据

### 2.3 版本控制
* 通过 URL 路径进行版本控制，如 `/api/v1/...`
* 支持向后兼容的接口扩展，避免破坏性变更

### 2.4 错误处理约定
* 遵循统一的错误码和错误响应格式
* **通用错误响应体示例**:
  ```json
  {
    "code": 0,                // 业务错误码，0表示成功，非0表示错误
    "message": "错误信息",     // 用户可读的错误信息
    "detail": "错误详情,可选",  // 错误的详细信息，可选
    "data": "返回数据",        // 可选，返回的数据，错误时通常为空
    "pageInfo": "分页信息,可选"  // 可选，分页信息，错误时通常为空
  }
  ```
* **常用 HTTP 状态码**:
  * `200 OK`: 请求成功。
  * `400 Bad Request`: 请求无效 (例如参数错误、格式错误)。
  * `401 Unauthorized`: 未认证或认证失败。
  * `403 Forbidden`: 已认证，但无权限访问。
  * `404 Not Found`: 请求的资源不存在。
  * `500 Internal Server Error`: 服务器内部错误。

### 2.6 分页与排序约定
* 当前接口主要为单条记录操作，无需分页功能

## 3. 接口列表

| 接口路径                                    | 请求方式 | 接口名称           | 接口描述                                           |
| ------------------------------------------- | -------- | ------------------ | -------------------------------------------------- |
| `/api/v1/exercise_sessions`                 | POST     | 进入练习环节       | 创建练习会话，返回转场配置和首题信息               |
| `/api/v1/exercise_sessions/resume`          | GET      | 恢复练习环节       | 恢复未完成的练习会话，返回当前进度和题目信息       |
| `/api/v1/exercise_sessions/submit_answer`   | POST     | 提交答案并获取反馈 | 处理学生答题，返回即时反馈、连胜状态和下一题信息   |

## 4. 场景说明

### 4.1 首次进入练习环节场景

#### 4.1.1 场景流程描述
学生完成前置课程组件学习后，系统检测到即将进入练习组件，显示"即将进入练习"提示。学生点击进入练习，触发转场动画（翻页效果），显示IP动效和"开始练习+课程名称"文案，持续2秒后进入练习答题界面。

**关键步骤：**
1. 学生完成前置课程组件学习
2. 系统检测到即将进入练习组件，显示"即将进入练习"提示
3. 学生点击进入练习，触发转场动画（翻页效果）
4. 显示IP动效和"开始练习+课程名称"文案，持续2秒
5. 进入练习答题界面

#### 4.1.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生客户端
    participant ExerciseAPI as 练习服务API
    participant UCenterService as 用户中心服务
    participant QuestionService as 内容平台服务
    participant Cache as Redis缓存
    participant DB as PostgreSQL

    Student->>ExerciseAPI: POST /api/v1/exercise_sessions
    Note over Student,ExerciseAPI: 请求参数：用户ID, 课程ID, 组件ID
    
    ExerciseAPI->>UCenterService: 验证用户身份
    UCenterService->>ExerciseAPI: 返回用户信息
    
    ExerciseAPI->>DB: 创建练习会话记录
    DB->>ExerciseAPI: 返回会话ID
    
    ExerciseAPI->>QuestionService: 请求首题推荐
    QuestionService->>ExerciseAPI: 返回题目信息
    
    ExerciseAPI->>Cache: 缓存会话状态
    
    ExerciseAPI->>Student: 返回响应数据
    Note over ExerciseAPI,Student: 响应包含：会话ID, 转场配置, 首题信息, 初始进度状态
    
    Student->>Student: 显示转场动画和IP动效
    Student->>Student: 进入练习答题界面
```

### 4.2 再次进入练习环节场景

#### 4.2.1 场景流程描述
学生重新进入课程，系统检测到当前进度在练习组件，显示IP动效和"继续练习+课程名称"文案，持续2秒后进入练习答题界面，恢复之前的作答进度和计时状态。

**关键步骤：**
1. 学生重新进入课程，系统检测到当前进度在练习组件
2. 系统显示IP动效和"继续练习+课程名称"文案，持续2秒
3. 进入练习答题界面，恢复之前的作答进度和计时状态

#### 4.2.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生客户端
    participant ExerciseAPI as 练习服务API
    participant UCenterService as 用户中心服务
    participant Cache as Redis缓存
    participant DB as PostgreSQL

    Student->>ExerciseAPI: GET /api/v1/exercise_sessions/resume
    Note over Student,ExerciseAPI: 请求参数：用户ID, 课程ID
    
    ExerciseAPI->>UCenterService: 验证用户身份
    UCenterService->>ExerciseAPI: 返回用户信息
    
    ExerciseAPI->>Cache: 查询会话状态
    alt 缓存命中
        Cache->>ExerciseAPI: 返回会话状态
    else 缓存未命中
        ExerciseAPI->>DB: 查询会话记录
        DB->>ExerciseAPI: 返回会话数据
        ExerciseAPI->>Cache: 重建缓存
    end
    
    ExerciseAPI->>Student: 返回响应数据
    Note over ExerciseAPI,Student: 响应包含：会话ID, 转场配置, 当前题目, 进度状态, 计时状态
    
    Student->>Student: 显示继续练习转场
    Student->>Student: 恢复练习答题界面
```

### 4.3 练习作答与即时反馈场景

#### 4.3.1 场景流程描述
学生在练习界面查看题目并选择答案，点击提交答案，系统判断答案正确性并显示即时反馈。如果连续答对，显示连胜提示和鼓励动画，然后学生点击继续进入下一题或完成练习。

**关键步骤：**
1. 学生在练习界面查看题目并选择答案
2. 学生点击提交答案
3. 系统判断答案正确性并显示即时反馈
4. 如果连续答对，显示连胜提示和鼓励动画
5. 学生点击继续进入下一题或完成练习

#### 4.3.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生客户端
    participant ExerciseAPI as 练习服务API
    participant QuestionService as 内容平台服务
    participant Cache as Redis缓存
    participant DB as PostgreSQL
    participant Kafka as 消息队列

    Student->>ExerciseAPI: POST /api/v1/exercise_sessions/submit_answer
    Note over Student,ExerciseAPI: 请求参数：会话ID, 题目ID, 学生答案, 答题时间
    
    ExerciseAPI->>Cache: 获取会话状态
    ExerciseAPI->>QuestionService: 获取标准答案
    QuestionService->>ExerciseAPI: 返回答案信息
    
    ExerciseAPI->>ExerciseAPI: 判断答案正确性
    ExerciseAPI->>ExerciseAPI: 计算进度和连胜状态
    
    ExerciseAPI->>DB: 保存答题记录
    ExerciseAPI->>Cache: 更新会话状态
    
    alt 需要下一题
        ExerciseAPI->>QuestionService: 请求下一题
        QuestionService->>ExerciseAPI: 返回下一题信息
    end
    
    ExerciseAPI->>Student: 返回响应数据
    Note over ExerciseAPI,Student: 响应包含：答案正确性, 即时反馈内容, 连胜状态, 更新后进度, 下一题信息
    
    ExerciseAPI->>Kafka: 发送答题事件（异步）
    
    Student->>Student: 显示即时反馈
    alt 连续答对
        Student->>Student: 显示连胜提示和鼓励动画
    end
    Student->>Student: 显示下一题或完成状态
```

## 5. 检查清单

### 5.1 PRD核心需求场景与API覆盖性检查

| PRD核心需求点/用户场景 (ID)                     | 涉及的主要API接口 (路径与方法)                                  | 关键输入数据实体/参数 (示例)                        | 关键输出数据实体/参数 (示例)                           | 场景支持度(✅/⚠️/❌)(API是否完整支持场景流程?) | 数据流正确性(✅/⚠️/❌)(输入输出数据是否符合场景逻辑?) | 备注/待办事项                                   |
| :---------------------------------------------- | :-------------------------------------------------------------- | :---------------------------------------------------- | :------------------------------------------------------- | :----------------------------------------------------- | :--------------------------------------------------------- | :---------------------------------------------- |
| `首次进入练习环节 (PRD 场景1)`                  | `POST /api/v1/exercise_sessions`                               | `用户ID, 课程ID, 组件ID`                            | `会话ID, 转场配置, 首题信息, 初始进度状态`              | ✅                                                     | ✅                                                         | 一次性返回进入练习所需的所有信息                |
| `再次进入练习环节 (PRD 场景2)`                  | `GET /api/v1/exercise_sessions/resume`                         | `用户ID, 课程ID`                                    | `会话ID, 转场配置, 当前题目, 进度状态, 计时状态`        | ✅                                                     | ✅                                                         | 支持会话状态恢复和进度继续                      |
| `练习作答与即时反馈 (PRD 场景3)`                | `POST /api/v1/exercise_sessions/submit_answer`                 | `会话ID, 题目ID, 学生答案, 答题时间`                | `答案正确性, 即时反馈内容, 连胜状态, 更新后进度, 下一题信息` | ✅                                                     | ✅                                                         | 一次性处理答题、反馈、进度更新和下一题获取      |

### 5.2 API接口数据实体与数据库支持检查

| API接口 (路径与方法)                     | 操作类型 (CRUD) | 主要数据实体 (模型名称)      | 关键数据属性 (字段名示例)                                 | 依赖的数据库表/视图 (示例)        | 数据库操作 (读/写/更新/删) | 数据可获得性/可行性(✅/⚠️/❌)(DB能否提供?性能是否满足?) | 数据一致性保障 (简述策略)                               | 备注/待办事项                                                                 |
| :--------------------------------------- | :-------------- | :--------------------------- | :---------------------------------------------------------- | :-------------------------------- | :--------------------------- | :--------------------------------------------------------- | :-------------------------------------------------------- | :---------------------------------------------------------------------------- |
| `POST /api/v1/exercise_sessions`         | Create          | `练习会话`                   | `user_id, course_id, session_status, start_time, learning_mode` | `tbl_study_sessions`              | 写                           | ✅                                                         | `事务保证会话创建的原子性`                              | `需要与用户中心和内容平台服务集成`                                            |
| `GET /api/v1/exercise_sessions/resume`   | Read            | `练习会话, 学习进度`         | `session_id, current_position, playback_mode, is_completed` | `tbl_study_sessions, tbl_study_progress` | 读                           | ✅                                                         | `N/A (读取操作)`                                            | `使用Redis缓存提高查询性能`                                                   |
| `POST /api/v1/exercise_sessions/submit_answer` | Create/Update   | `答题记录, 学习进度`         | `user_id, question_id, user_answer, is_correct, session_id` | `tbl_answer_logs, tbl_study_progress` | 写/更新                      | ✅                                                         | `事务保证答题记录和进度更新的一致性`                    | `答题日志使用ClickHouse存储，支持高频写入`                                    |

### 5.3 API通用设计规范符合性检查

| 检查项                                                                 | 状态 (✅/⚠️/❌/N/A) | 备注/说明                                                                                                |
| :--------------------------------------------------------------------- | :------------------- | :------------------------------------------------------------------------------------------------------- |
| 1. **命名规范**: 接口路径、参数、数据实体命名是否清晰、一致，并遵循团队约定？        | ✅                   | 使用RESTful风格，路径清晰，参数命名语义明确                                                              |
| 2. **HTTP方法**: GET, POST, PUT, DELETE, PATCH等HTTP方法使用是否语义恰当？        | ✅                   | POST用于创建会话和提交答案，GET用于查询恢复状态，符合语义                                                |
| 3. **状态码**: HTTP状态码是否准确反映操作结果 (成功、客户端错误、服务端错误)？        | ✅                   | 200成功，400参数错误，401认证失败，500服务器错误                                                         |
| 4. **错误处理**: 错误响应格式是否统一 (参考2.4)，错误信息是否对用户友好且包含足够调试信息？ | ✅                   | 统一错误响应格式，包含错误码、错误信息和详细描述                                                         |
| 5. **认证与授权**: 需要保护的接口是否都正确实施了认证和授权机制 (参考2.2)？           | ✅                   | 所有接口需要JWT认证，验证用户身份和课程访问权限                                                          |
| 6. **数据格式与校验**: 请求和响应体结构是否定义清晰，数据类型是否准确？输入数据是否有必要的校验？ | ✅                   | JSON格式，必填参数校验，数据类型验证                                                                     |
| 7. **分页与排序**: 对于返回列表数据的接口，是否按约定提供了分页和排序功能 (参考2.6)？    | N/A                  | 当前接口主要为单条记录操作，无需分页                                                                     |
| 8. **幂等性**: 对于创建和更新操作，是否考虑了幂等性设计？ (特别是PUT, DELETE)         | ⚠️                   | 答题提交接口需要考虑重复提交的幂等性处理                                                                 |
| 9. **安全性**: 是否有潜在的安全风险，如敏感信息泄露、SQL注入、XSS等？             | ✅                   | 服务端验证答案正确性，防止客户端作弊；参数校验防止注入攻击                                               |
| 10. **性能考量**: 接口响应时间是否满足性能要求？大数据量查询是否有优化措施？             | ✅                   | 进入练习<2秒，答题提交<1秒；使用Redis缓存和接口合并优化性能                                              |
| 11. **文档一致性**: 本文档描述是否与OpenAPI规范文件 (`be-s4-2`) 及实际实现保持一致？ | ✅                   | 基于接口分析文档撰写，保持一致性                                                                         |
| 12. **可测试性**: 接口是否易于进行单元测试、集成测试和端到端测试？                    | ✅                   | 接口设计清晰，便于编写测试用例                                                                           |
| 13. **向后兼容性**: 未来接口变更是否考虑了向后兼容性策略？                            | N/A (新设计)         | v1版本新设计，后续版本迭代需重点关注向后兼容性                                                           |

## 6. 附录 (Appendix)

### 6.1 参考文档
* 产品需求文档 (PRD): be-s1-1-ai-prd-Format-PRD-AI课中-练习组件1.0-claude-sonnet-4.md
* 技术架构设计文档 (TD): be-s2-1-ai-td-Format-PRD-AI课中-练习组件1.0-claude-sonnet-4.md
* 数据实体设计文档 (DE): be-s3-1-ai-de-Format-PRD-AI课中-练习组件1.0-claude-sonnet-4.md
* 数据库设计文档 (DD): be-s3-4-ai-dd-掌握度策略-V1.0-claude-3.7.md, be-s3-4-ai-dd-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md
* 整体接口分析文档 (ADA): be-s5-1-ai-ada-AI课中-练习组件1.0-claude-sonnet-4.md

### 6.2 核心行业标准
* RESTful API设计规范
* OpenAPI 3.x 规范
* JWT认证标准
* ISO 8601 日期时间格式标准
const stats = [
  { value: '98%', label: '准确率' },
  { value: '75%', label: '效率提升' },
  { value: '50+', label: '支持的语言和框架' },
  { value: '1000+', label: '活跃用户' },
];

const StatsSection = () => {
  return (
    <section className="py-16 bg-blue-600 text-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold mb-4">为何超过1000+团队选择鲁班</h2>
          <p className="text-xl max-w-3xl mx-auto opacity-90">
            鲁班 Code-Agent 提供业界领先的代码分析和文档生成能力，帮助开发团队提高工作效率。
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-4xl font-bold mb-2">{stat.value}</div>
              <div className="text-blue-100">{stat.label}</div>
            </div>
          ))}
        </div>
        
        <div className="bg-white bg-opacity-10 rounded-xl p-8 text-center backdrop-blur-sm">
          <h3 className="text-2xl font-bold mb-4">准备好提升您的开发效率了吗？</h3>
          <p className="text-lg mb-6 max-w-2xl mx-auto">
            立即开始使用鲁班 Code-Agent，体验智能代码分析和文档生成的强大功能。
          </p>
          <a href="/auth/signup" className="inline-block bg-white text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg font-medium transition-colors">
            免费注册
          </a>
        </div>
      </div>
    </section>
  );
};

export default StatsSection; 
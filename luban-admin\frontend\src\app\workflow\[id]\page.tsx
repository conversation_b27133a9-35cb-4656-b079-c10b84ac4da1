// @ts-nocheck
'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import axios from 'axios';
import MainLayout from '@/components/layout/MainLayout';
import { documentApi } from '@/lib/api';
import { API_BASE_URL } from '@/app/configs/api';

// 处理阶段状态映射
const TASK_STATUS_MAP = {
  'pending': 0,
  'processing': 1,
  'generating': 2,
  'completed': 3,
  'failed': 3  // 失败状态也显示在最后一个阶段
};

// 模拟的处理阶段
const PROCESS_STAGES = [
  { id: 'analyzing', name: '文档分析中', percent: 25 },
  { id: 'extracting', name: '提取需求', percent: 50 },
  { id: 'generating', name: '生成文档', percent: 75 },
  { id: 'completed', name: '处理完成', percent: 100 }
];

export default function WorkflowDetail() {
  const { id } = useParams();
  const router = useRouter();
  const [currentStage, setCurrentStage] = useState(0);
  const [isComplete, setIsComplete] = useState(false);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [apiTestResult, setApiTestResult] = useState('');
  const [retryCount, setRetryCount] = useState(0);
  const [documentData, setDocumentData] = useState({
    title: '处理飞书文档...',
    source: '飞书文档',
    url: '',
    createdAt: new Date().toLocaleString()
  });
  const [downloadUrl, setDownloadUrl] = useState('');  // 新增下载链接状态

  // 首先检查是否是飞书v2 API的任务
  const [isFeishuV2, setIsFeishuV2] = useState(false);
  
  // 在组件挂载后立即检查任务类型
  useEffect(() => {
    const checkTaskType = async () => {
      try {
        // 尝试获取飞书v2 API的任务状态
        const feishuTask = await documentApi.getFeishuTaskStatus(id);
        if (feishuTask && feishuTask.task_id) {
          setIsFeishuV2(true);
          // 如果是飞书v2 API的任务，直接获取其状态
          processFeishuV2Task(feishuTask);
        }
      } catch (error) {
        // 不是飞书v2 API的任务，将继续使用原来的逻辑
        console.log('不是飞书v2 API的任务，使用原有API获取状态');
      }
    };
    
    checkTaskType();
  }, [id]);
  
  // 处理飞书v2 API的任务
  const processFeishuV2Task = (task) => {
    // 更新文档数据
    setDocumentData({
      title: '处理飞书文档',
      source: '飞书文档',
      url: task.source_url || '',
      createdAt: new Date().toLocaleString()
    });
    
    // 更新处理阶段
    if (task.status === 'completed') {
      setCurrentStage(3);  // 完成阶段
      setIsComplete(true);
      // 设置下载链接
      if (task.download_url) {
        setDownloadUrl(task.download_url);
      }
    } else if (task.status === 'failed') {
      setCurrentStage(3);  // 最后阶段，但显示失败
      setIsError(true);
      setErrorMessage(task.message || '处理失败，请重试');
    } else if (task.status === 'processing') {
      setCurrentStage(1);  // 处理中
    }
  };
  
  // 测试API连接
  useEffect(() => {
    if (isFeishuV2) {
      // 如果是飞书v2 API的任务，无需测试连接
      return;
    }
    
    // 测试API根路径
    const testApiConnection = async () => {
      try {
        // 尝试直接访问API根目录
        const rootResponse = await axios.get(`${API_BASE_URL}`);
        console.log('API根路径访问结果:', rootResponse.data);
        setApiTestResult('API根路径连接成功');
      } catch (rootError) {
        console.error('API根路径访问错误:', rootError);
        
        try {
          // 尝试访问文档路径
          const docsResponse = await axios.get(`${API_BASE_URL}/documents/tasks`);
          console.log('文档任务列表访问结果:', docsResponse.data);
          setApiTestResult('文档任务API连接成功');
        } catch (docsError) {
          console.error('文档任务API访问错误:', docsError);
          setApiTestResult(`API连接失败: ${docsError.message}`);
        }
      }
    };
    
    testApiConnection();
  }, [isFeishuV2]);
  
  // 从API获取任务状态
  useEffect(() => {
    // 如果是飞书v2 API的任务，使用专门的轮询逻辑
    if (isFeishuV2) {
      const pollFeishuV2Task = async () => {
        try {
          const feishuTask = await documentApi.getFeishuTaskStatus(id);
          processFeishuV2Task(feishuTask);
        } catch (error) {
          console.error('获取飞书任务状态失败:', error);
          setIsError(true);
          setErrorMessage(`获取任务状态失败: ${error.message}`);
        }
      };
      
      // 初始获取一次
      pollFeishuV2Task();
      
      // 如果任务未完成，开始轮询
      if (!isComplete && !isError) {
        const interval = setInterval(() => {
          if (isComplete || isError) {
            clearInterval(interval);
          } else {
            pollFeishuV2Task();
          }
        }, 3000);
        
        return () => clearInterval(interval);
      }
      
      return;
    }
    
    // 原有的任务状态获取逻辑，只在不是飞书v2 API的任务时运行
    let isMounted = true;
    const fetchTaskStatus = async () => {
      try {
        console.log(`尝试获取任务状态: ${API_BASE_URL}/documents/tasks/${id}`);
        const response = await axios.get(`${API_BASE_URL}/documents/tasks/${id}`, {
          headers: {
            // 实际项目中应添加认证信息
            // 'Authorization': `Bearer ${token}`
          }
        });
        
        console.log('任务状态API响应:', response.data);
        
        if (!isMounted) return;
        
        const task = response.data;
        
        // 如果返回的数据为空，可能是刚创建的任务尚未完全初始化
        if (!task || Object.keys(task).length === 0) {
          // 如果重试次数过多，显示错误
          if (retryCount > 10) {
            setIsError(true);
            setErrorMessage('获取任务状态超时，请刷新页面重试');
            return;
          }
          
          setRetryCount(prev => prev + 1);
          return; // 等待下一次轮询
        }
        
        // 重置重试计数
        setRetryCount(0);
        
        // 更新文档数据 - 确保重命名为"处理飞书文档..."
        setDocumentData({
          title: '处理飞书文档...',
          source: task.source || (task.result?.file_name ? '上传PDF' : '飞书文档'),
          url: task.source_url || '',
          createdAt: new Date(task.created_at).toLocaleString()
        });
        
        // 更新处理阶段
        const currentStatus = task.status;
        setCurrentStage(TASK_STATUS_MAP[currentStatus] || 0);
        
        // 检查任务是否完成
        if (currentStatus === 'completed') {
          setIsComplete(true);
        } else if (currentStatus === 'failed') {
          setIsError(true);
          setErrorMessage(task.error || '处理失败，请重试');
        }
      } catch (error) {
        console.error('获取任务状态失败:', error);
        console.error('错误详情:', error.response?.data || error.message);
        
        if (!isMounted) return;
        
        // 增加重试次数，避免立即显示错误
        if (retryCount > 5) {
          setIsError(true);
          setErrorMessage(`获取任务状态失败: ${error.response?.data?.detail || error.message}`);
        } else {
          setRetryCount(prev => prev + 1);
        }
      }
    };
    
    // 初始加载
    fetchTaskStatus();
    
    // 定时轮询任务状态，直到任务完成或失败
    const interval = setInterval(() => {
      if (isComplete || isError) {
        clearInterval(interval);
      } else {
        fetchTaskStatus();
      }
    }, 3000); // 每3秒检查一次
    
    return () => {
      isMounted = false;
      clearInterval(interval);
    };
  }, [id, isComplete, isError, retryCount, isFeishuV2]);
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">{documentData.title}</h1>
            {documentData.url && (
              <div className="text-sm text-gray-500 mt-2">
                <a 
                  href={documentData.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:text-blue-700 hover:underline"
                >
                  {documentData.url}
                </a>
              </div>
            )}
            <div className="flex items-center text-sm text-gray-500 mt-2">
              <span className="mr-4">来源: {documentData.source}</span>
              <span>创建时间: {documentData.createdAt}</span>
            </div>
            
            <div className="flex justify-between items-center mt-6">
              <h2 className="text-lg font-semibold text-gray-800">PRD文档处理</h2>
              <Link 
                href="/workflow" 
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                查看所有工作流
              </Link>
            </div>
          </div>
          
          {/* 处理进度 */}
          <div className="bg-white shadow rounded-lg p-6 mb-8">
            <div className="mb-6">
              <div className="flex justify-between items-center mb-2">
                <div className="font-medium text-gray-700">
                  处理进度: {PROCESS_STAGES[currentStage].name}
                  <span className="text-sm text-gray-500 ml-2">(处理时间较长，一般为3~5分钟，请耐心等待)</span>
                </div>
                <div className="text-sm font-medium text-blue-600">
                  {PROCESS_STAGES[currentStage].percent}%
                </div>
              </div>
              <div className="overflow-hidden h-2 mb-4 text-xs flex rounded bg-blue-100">
                <div
                  className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-blue-600 transition-all duration-500"
                  style={{ width: `${PROCESS_STAGES[currentStage].percent}%` }}
                ></div>
              </div>
            </div>
            
            {/* 显示错误信息 */}
            {isError && (
              <div className="mb-6 p-3 bg-red-50 text-red-600 rounded border border-red-200">
                <div className="flex items-center">
                  <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  <span className="font-medium">处理失败:</span>
                  <span className="ml-1">{errorMessage}</span>
                </div>
              </div>
            )}
            
            {/* 处理阶段 */}
            <div className="space-y-4">
              {PROCESS_STAGES.map((stage, index) => (
                <div 
                  key={stage.id} 
                  className={`flex items-start p-3 rounded-md ${
                    isError && index === currentStage 
                      ? 'bg-red-50'
                      : index <= currentStage 
                        ? 'bg-blue-50' 
                        : 'bg-gray-50'
                  }`}
                >
                  <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center mr-3 ${
                    isError && index === currentStage 
                      ? 'bg-red-500'
                      : index < currentStage 
                        ? 'bg-green-500' 
                        : index === currentStage 
                          ? 'bg-blue-500' 
                          : 'bg-gray-300'
                  }`}>
                    {isError && index === currentStage ? (
                      <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    ) : index < currentStage ? (
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      <span className="text-xs text-white font-bold">{index + 1}</span>
                    )}
                  </div>
                  <div>
                    <div className={`font-medium ${
                      isError && index === currentStage 
                        ? 'text-red-700'
                        : index <= currentStage 
                          ? 'text-blue-700' 
                          : 'text-gray-600'
                    }`}>{stage.name}</div>
                    <div className="text-sm text-gray-500 mt-1">
                      {index === 0 && '解析PRD文档内容，提取关键信息...'}
                      {index === 1 && '从文档中提取用户需求、功能点和业务逻辑...'}
                      {index === 2 && '根据提取的信息自动生成开发文档、API文档...'}
                      {index === 3 && (isError ? '处理出错，请查看错误信息或重试' : '所有文档处理已完成，可查看生成结果')}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* 新增：飞书V2直接下载区域 */}
          {isComplete && isFeishuV2 && downloadUrl && (
            <div className="bg-white shadow rounded-lg p-6 mb-8">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">文档处理结果</h2>
              <p className="text-gray-600 mb-4">飞书文档处理已完成，您可以直接下载处理结果。</p>
              
              <div className="flex justify-center">
                <a 
                  href={downloadUrl}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors flex items-center"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                  </svg>
                  下载处理结果
                </a>
              </div>
            </div>
          )}
          
          {/* 生成的文档 */}
          {isComplete && !isFeishuV2 && (
            <div className="bg-white shadow rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4">文档处理结果</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link href={`/workflow/${id}/document`} className="border border-gray-200 rounded-lg p-4 hover:bg-blue-50 transition-colors">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
                      <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div>
                      <div className="font-medium">需求文档</div>
                      <div className="text-sm text-gray-500">PRD分析结果</div>
                    </div>
                  </div>
                </Link>
                
                <Link href={`/workflow/${id}/api`} className="border border-gray-200 rounded-lg p-4 hover:bg-blue-50 transition-colors">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center mr-3">
                      <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                      </svg>
                    </div>
                    <div>
                      <div className="font-medium">API文档</div>
                      <div className="text-sm text-gray-500">接口设计规范</div>
                    </div>
                  </div>
                </Link>
                
                <Link href={`/workflow/${id}/td`} className="border border-gray-200 rounded-lg p-4 hover:bg-blue-50 transition-colors">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center mr-3">
                      <svg className="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <div>
                      <div className="font-medium">技术文档</div>
                      <div className="text-sm text-gray-500">开发技术方案</div>
                    </div>
                  </div>
                </Link>
                
                <Link href={`/workflow/${id}/dd`} className="border border-gray-200 rounded-lg p-4 hover:bg-blue-50 transition-colors">
                  <div className="flex items-center">
                    <div className="w-10 h-10 rounded-lg bg-yellow-100 flex items-center justify-center mr-3">
                      <svg className="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                      </svg>
                    </div>
                    <div>
                      <div className="font-medium">数据库设计</div>
                      <div className="text-sm text-gray-500">数据结构定义</div>
                    </div>
                  </div>
                </Link>
              </div>
              
              <div className="mt-6 pt-4 border-t border-gray-200">
                <div className="flex justify-end">
                  <a 
                    href={`${API_BASE_URL}/documents/download/${id}`}
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    下载完整文档
                  </a>
                </div>
              </div>
            </div>
          )}
          
          {/* 重新提交按钮 - 仅在出错时显示 */}
          {isError && (
            <div className="mt-6 flex justify-end">
              <button
                onClick={() => router.push('/workflow/new')}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                重新创建任务
              </button>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
} 
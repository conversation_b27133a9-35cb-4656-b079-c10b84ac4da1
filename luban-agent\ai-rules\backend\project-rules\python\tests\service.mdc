---
description: 
globs: backend/tests/domain/services/**/*.py
alwaysApply: false
---
# Service 层测试代码生成规则

**核心原则：简洁优先**

## 边界
- 服务应无状态；依赖通过 Mock/Fake 注入  
- 验证业务规则、服务协作，而非数据持久化

## 约定
- 类名 `Test{Service}`；方法 `test_{行为}_{场景}`  
- 使用 `pytest.fixture` 创建依赖 Mock；`pytest-mock` 简化断言  
- 复杂规则使用 `@pytest.mark.parametrize`  
- 断言：返回值 / 抛异常 + 依赖交互(`mock.assert_called_*`)  
- 覆盖率目标 ≥ 90 %

## 示例代码
```python
class TestAuthService:
    """认证服务测试"""
    
    @pytest.fixture
    def user_repo_mock(self):
        """创建用户仓储模拟"""
        return Mock(spec=UserRepository)
    
    @pytest.fixture
    def token_service_mock(self):
        """创建令牌服务模拟"""
        return Mock(spec=TokenService)
    
    @pytest.fixture
    def auth_service(self, user_repo_mock, token_service_mock):
        """创建认证服务"""
        return AuthService(user_repo_mock, token_service_mock)
    
    def test_login_success(self, auth_service, user_repo_mock, token_service_mock):
        """测试登录成功"""
        # 准备数据
        # 配置模拟
        # 执行测试        
        # 验证结果
        pass
    
    def test_login_user_not_found(self, auth_service, user_repo_mock):
        """测试用户不存在"""
        # 配置模拟        
        # 执行和验证
        with pytest.raises(InvalidCredentialsError):
            auth_service.login("nonexistent", "password")
    
    def test_event_handling(self, auth_service, user_repo_mock):
        """测试领域事件处理"""
        # 创建事件        
        # 执行事件处理        
        # 验证交互
        pass
        
    @pytest.mark.parametrize("test_case", [
        # (订单金额, 用户等级, 预期折扣率)
        (1000, "STANDARD", 0.0),
        (1000, "SILVER", 0.05),
        (1000, "GOLD", 0.1),
    ])
    def test_complex_business_rule(self, discount_service, test_case):
        """测试复杂业务规则：折扣计算"""
        # 解构测试用例        
        # 执行测试
        # 验证结果
        pass
    
    def test_multi_aggregate_coordination(self, order_service, order_repo_mock, inventory_repo_mock, payment_service_mock):
        """测试多聚合协作"""
        # 准备数据
        # 配置模拟
        # 执行测试
        # 验证结果        
        # 验证库存已更新
        # 验证支付已处理        
        # 验证订单状态已更新
        pass
    
    def test_multi_service_collaboration(self, checkout_service, order_service_mock, customer_service_mock, 
                                         notification_service_mock, cart_mock):
        """测试多服务协作"""
        # 准备数据
        # 配置模拟
        # 执行测试
        # 验证结果
        pass
```

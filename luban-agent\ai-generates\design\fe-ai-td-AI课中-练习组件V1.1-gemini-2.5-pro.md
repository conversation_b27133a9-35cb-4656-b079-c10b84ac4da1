# 前端架构设计模板 (精致专业版)

**项目名称**: `AI课中-练习组件V1.1`
**文档版本**: `1.0`
**架构师**: `gemini-2.5-pro`
**创建日期**: `2024-07-15`

## 1. 架构概览

### 1.1 业务目标与技术愿景

本项目旨在**优化AI课中练习环节的用户体验**，尤其是提升作答流程的流畅度和即时反馈的有效性与情感化。通过重新设计练习组件的交互流程和反馈机制，为学生提供更流畅、更有趣、更受鼓励的作答体验，从而提高练习题完成率和用户满意度。

技术实现上，我们将采用MVVM架构和单向数据流，以确保界面与业务逻辑的清晰分离。同时，通过信号(Signals)实现细粒度状态管理，使动画流畅且性能优异。该架构支持组件高度复用，便于后续功能扩展。

### 1.2 核心技术栈

* **基础框架**：React 19 / Next.js 15 (App Router)
* **状态管理**：@preact-signals/safe-react + Context API
* **UI框架**：@repo/ui (基于shadcn)
* **样式方案**：Tailwind CSS
* **动画方案**：Framer Motion + CSS动画
* **数据处理**：Zod + SWR/Axios

### 1.3 架构核心原则

1. **严格MVVM分层**：Model专注数据获取与转换，ViewModel封装所有业务逻辑，View只负责UI渲染和用户事件传递
2. **感知性能优先**：利用过渡动画、骨架屏、乐观更新确保用户体验流畅
3. **状态隔离与单向数据流**：通过Signals实现精确更新，保持数据流向清晰可预测
4. **复用优先**：最大程度复用现有组件，保持系统一致性
5. **可测试性设计**：ViewModel与UI分离，便于单元测试，提高代码质量

## 2. 系统结构

### 2.1 模块划分

```mermaid
graph TD
    App["练习组件(ExerciseModule)"]
    App --> Transition["转场服务(TransitionService)"]
    App --> Exercise["答题功能(ExerciseCore)"]
    App --> Feedback["即时反馈(FeedbackSystem)"]
    
    Exercise --> Question["题目展示(QuestionView)"]
    Exercise --> UserInput["用户作答(UserInputView)"]
    Exercise --> Navigation["答题导航(NavigationView)"]
    
    Feedback --> Animation["反馈动画(AnimationService)"]
    Feedback --> Sound["音效系统(SoundService)"]
    
    Question -.-> ReusableA["复用组件: 勾画功能"]
    Question -.-> ReusableB["复用组件: 答疑功能"]
    Question -.-> ReusableC["复用组件: 错题本"]
```

### 2.2 关键状态管理

练习模块采用多层状态管理策略：

1. **全局练习状态** - ExerciseContext (Context API)
   - 保存课程ID、题目集、用户进度、学习记录等全局状态
   - 持久化到本地存储，支持续学功能

2. **练习核心状态** - ExerciseViewModel (Signals)
   - 管理当前题目、作答状态、正确性、连对次数等核心作答数据
   - 控制题目间转场和反馈效果触发

3. **UI交互状态** - LocalViewState (useState/Signals)
   - 纯UI状态：动画播放、展开/折叠、输入焦点等
   - 仅在需要的组件中维护，不传播到全局

数据流向：用户操作 -> View传递事件 -> ViewModel处理业务逻辑 -> Model进行数据更新 -> ViewModel通知变更 -> View重新渲染。

## 3. 业务视图详细设计

### 3.1 练习组件转场视图 (TransitionView)

#### 3.1.1 功能概述

负责处理从文档/其他组件进入练习的转场效果，以及练习题之间的过渡动画。包括首次进入练习的"开始练习"动画和续学时的"继续练习"提示。

#### 3.1.2 MVVM分层设计

##### Model层
* **核心数据结构**: 
  ```typescript
  // 转场配置数据结构
  interface TransitionConfig {
    type: 'first-entry' | 'continue' | 'question-switch';
    courseTitle?: string;
    animationAssets?: {
      ipAnimation: string;
      backgroundEffect: string;
    };
    timing: {
      duration: number; // 毫秒
      delay?: number;
    };
  }
  ```
* **数据交互**: 
  - 通过 `getCourseInfo` 获取课程标题
  - 基于本地存储判断首次/再次进入状态

##### ViewModel层 (核心)
* **接口设计**: 
  ```typescript
  function useTransitionViewModel() {
    // 状态
    const isVisible = signal(false);
    const transitionType = signal<'first-entry' | 'continue' | 'question-switch'>('first-entry');
    const animationProgress = signal(0);
    
    // 方法
    const showTransition = (type, courseInfo) => {...};
    const hideTransition = () => {...};
    const onAnimationComplete = () => {...};
    
    return {
      // 对外暴露状态
      isVisible,
      transitionType,
      animationProgress,
      courseTitle,
      // 对外暴露方法
      showTransition,
      hideTransition,
      onAnimationComplete
    };
  }
  ```
* **状态管理**: 
  - 使用Signals管理转场动画状态和进度
  - 处理动画时序和完成后的回调
* **业务逻辑**: 
  - 基于学习进度决定显示"开始练习"或"继续练习"
  - 控制转场动画的显示/隐藏和过渡到题目界面
  - 管理动画完成后的后续动作

##### View层
* **组件结构**: 
  - `TransitionContainer`: 全屏动画容器
  - `IPAnimation`: IP角色动画组件
  - `TransitionMessage`: 文本提示组件
* **关键交互流程**:
  ```mermaid
  sequenceDiagram
    participant User
    participant View as TransitionView
    participant ViewModel as TransitionViewModel
    participant Model
    
    User->>View: 下拉或自动触发进入练习
    View->>ViewModel: 调用showTransition()
    ViewModel->>Model: 获取课程信息和进度
    Model-->>ViewModel: 返回课程数据与学习状态
    ViewModel->>ViewModel: 设置转场类型(首次/续学)
    ViewModel->>View: 更新isVisible信号
    View->>User: 播放带有IP形象的转场动画
    Note over View: 动画播放2秒
    View->>ViewModel: 动画结束，调用onAnimationComplete()
    ViewModel->>ViewModel: 触发hideTransition()
    ViewModel->>View: 更新isVisible信号为false
    View->>User: 过渡到第一题/上次学习的题目
  ```

### 3.2 题目作答视图 (QuestionView)

#### 3.2.1 功能概述

练习组件的核心视图，负责展示题目内容、选项，接收用户作答，提供操作按钮（如提交、不确定、勾画），并在提交后显示即时反馈。

#### 3.2.2 MVVM分层设计

##### Model层
* **核心数据结构**: 
  ```typescript
  // 题目数据结构
  interface QuestionData {
    id: string;
    type: 'SINGLE_CHOICE' | 'MULTIPLE_CHOICE' | /* 其他题型 */;
    topic: {
      text: string;
      image?: string;
    };
    options: Array<{
      id: string;
      text: string;
      image?: string;
    }>;
    answer: string[]; // 正确答案的ID数组
    analysis?: string; // 解析文本
    point?: string; // 关联知识点
  }
  
  // 用户作答数据
  interface UserAnswer {
    questionId: string;
    selectedOptions: string[];
    isCorrect?: boolean;
    timestamp: number;
    timeSpent?: number; // 毫秒
    isUncertain?: boolean; // 用户标记为不确定
  }
  ```
* **数据交互**: 
  - `getQuestions`: 从API获取题目数据
  - `submitAnswer`: 向后端提交答案并获取反馈
  - `saveProgress`: 保存进度到本地和服务器
  - `getStreak`: 获取连续答对次数

##### ViewModel层 (核心)
* **接口设计**: 
  ```typescript
  function useQuestionViewModel() {
    // 数据状态
    const currentQuestion = signal<QuestionData | null>(null);
    const selectedOptions = signal<string[]>([]);
    const isSubmitting = signal(false);
    const submissionResult = signal<'correct' | 'incorrect' | null>(null);
    const streakCount = signal(0);
    const isUncertain = signal(false);
    
    // UI状态
    const showFeedback = signal(false);
    const feedbackType = signal<'correct' | 'incorrect' | 'streak' | null>(null);
    
    // 方法
    const loadQuestion = (questionId?: string) => {...};
    const selectOption = (optionId: string) => {...};
    const toggleUncertain = () => {...};
    const submitAnswer = async () => {...};
    const goToNextQuestion = () => {...};
    const activateHighlightMode = () => {...};
    
    return {
      // 状态
      currentQuestion,
      selectedOptions,
      isSubmitting,
      submissionResult,
      streakCount,
      isUncertain,
      showFeedback,
      feedbackType,
      // 方法
      loadQuestion,
      selectOption,
      toggleUncertain,
      submitAnswer,
      goToNextQuestion,
      activateHighlightMode
    };
  }
  ```
* **状态管理**: 
  - 使用Signals管理题目状态、选项选择、提交状态和反馈状态
  - 跟踪连对次数和"不确定"标记
* **业务逻辑**: 
  - 处理用户选择选项和提交答案的核心逻辑
  - 判断答案正确性并触发相应反馈
  - 管理题目间的切换和进度追踪
  - 控制辅助功能(勾画、不确定标记)的状态

##### View层
* **组件结构**: 
  - `QuestionHeader`: 展示题型、计时器和进度
  - `QuestionContent`: 展示题干和选项
  - `OptionsGroup`: 选项列表组件
  - `ActionButtons`: 底部操作按钮组
  - `FeedbackOverlay`: 反馈动画层
* **关键交互流程**:
  ```mermaid
  stateDiagram-v2
    [*] --> QuestionLoading: 加载题目
    QuestionLoading --> AnsweringState: 题目加载完成
    
    state AnsweringState {
      [*] --> Viewing: 阅读题目
      Viewing --> OptionSelected: 选择选项
      OptionSelected --> ReadyToSubmit: 准备提交
      
      ReadyToSubmit --> Submitting: 点击提交
    }
    
    Submitting --> ProcessingAnswer: 发送答案
    ProcessingAnswer --> ShowingFeedback: 获得结果
    
    state ShowingFeedback {
      [*] --> CorrectFeedback: 答案正确
      [*] --> IncorrectFeedback: 答案错误
      CorrectFeedback --> CheckStreak: 检查连对
      CheckStreak --> StreakFeedback: 达到连对阈值
      CheckStreak --> NormalCorrectFeedback: 未达连对阈值
    }
    
    StreakFeedback --> NextQuestion: 播放连对动画
    NormalCorrectFeedback --> NextQuestion: 播放普通正确动画
    IncorrectFeedback --> NextQuestion: 播放错误动画
    
    NextQuestion --> [*]: 前往下一题或结束
  ```

### 3.3 即时反馈视图 (FeedbackView)

#### 3.3.1 功能概述

负责在用户提交答案后，根据答案正确性和连对情况，展示相应的视觉反馈和音效。包括正确/错误基础反馈和连续答对特殊反馈。

#### 3.3.2 MVVM分层设计

##### Model层
* **核心数据结构**: 
  ```typescript
  // 反馈配置结构
  interface FeedbackAsset {
    type: 'correct' | 'incorrect' | 'streak';
    animation: {
      src: string;
      duration: number;
    };
    sound?: {
      src: string;
      volume: number;
    };
    message?: string; // 如"太棒了！连对5题！"
  }
  
  // 连对级别配置
  interface StreakConfig {
    threshold: number; // 触发特殊反馈的连对数阈值
    feedbackType: string; // 对应使用的反馈资源类型
  }
  ```
* **数据交互**: 
  - `getFeedbackAssets`: 获取反馈动画和音效资源
  - `getStreakConfigs`: 获取连对级别配置
  - `logFeedbackEvent`: 记录反馈事件日志

##### ViewModel层 (核心)
* **接口设计**: 
  ```typescript
  function useFeedbackViewModel() {
    // 状态
    const isVisible = signal(false);
    const feedbackType = signal<'correct' | 'incorrect' | 'streak' | null>(null);
    const streakCount = signal(0);
    const feedbackMessage = signal('');
    const animationProgress = signal(0);
    
    // 方法
    const showFeedback = (result: 'correct' | 'incorrect', streak: number) => {...};
    const hideFeedback = () => {...};
    const playSound = (type: 'correct' | 'incorrect' | 'streak') => {...};
    const onAnimationComplete = () => {...};
    
    return {
      // 状态
      isVisible,
      feedbackType,
      streakCount,
      feedbackMessage,
      animationProgress,
      // 方法
      showFeedback,
      hideFeedback,
      onAnimationComplete
    };
  }
  ```
* **状态管理**: 
  - 使用Signals管理反馈组件的可见性、类型和动画状态
  - 根据连对数决定使用哪种反馈类型
* **业务逻辑**: 
  - 基于答题结果和连对数，选择合适的反馈类型
  - 控制动画和音效的播放时序
  - 管理反馈结束后的状态转换（如自动进入下一题）

##### View层
* **组件结构**: 
  - `FeedbackContainer`: 反馈动画容器
  - `CorrectAnimation`: 正确反馈动画组件
  - `IncorrectAnimation`: 错误反馈动画组件
  - `StreakAnimation`: 连对特殊反馈动画组件
  - `FeedbackMessage`: 文字提示组件
* **关键交互流程**:
  ```mermaid
  sequenceDiagram
    participant QVM as QuestionViewModel
    participant FVM as FeedbackViewModel
    participant View as FeedbackView
    participant User
    
    QVM->>FVM: showFeedback(result, streakCount)
    FVM->>FVM: 计算反馈类型(正确/错误/连对)
    FVM->>FVM: 更新反馈状态信号
    FVM->>View: 信号更新触发视图变化
    View->>View: 播放对应类型动画
    View->>User: 显示反馈动画与文字
    FVM->>FVM: 播放对应音效
    View->>FVM: 动画结束，调用onAnimationComplete()
    FVM->>QVM: 触发下一步操作(下一题)
    QVM->>QVM: 调用goToNextQuestion()
  ```

### 3.4 答疑集成视图 (QAIntegrationView)

#### 3.4.1 功能概述

负责在练习过程中集成答疑功能，允许学生通过长按题目文字或点击答疑入口按钮，唤起答疑组件进行提问。

#### 3.4.2 MVVM分层设计

##### Model层
* **核心数据结构**: 
  ```typescript
  // 答疑上下文数据
  interface QAContextData {
    questionId: string;
    selectedText?: string;
    courseId: string;
    learningStage: 'practice';
  }
  ```
* **数据交互**: 
  - 复用现有答疑组件的API交互

##### ViewModel层 (核心)
* **接口设计**: 
  ```typescript
  function useQAIntegrationViewModel() {
    // 状态
    const isQAVisible = signal(false);
    const selectedText = signal('');
    const questionContext = signal<QAContextData | null>(null);
    
    // 方法
    const openQAWithText = (text: string) => {...};
    const openQA = () => {...};
    const closeQA = () => {...};
    const handleTextSelection = (text: string) => {...};
    
    return {
      // 状态
      isQAVisible,
      selectedText,
      questionContext,
      // 方法
      openQAWithText,
      openQA, 
      closeQA,
      handleTextSelection
    };
  }
  ```
* **状态管理**: 
  - 管理答疑组件的显示状态和上下文
  - 跟踪用户长按选择的文本
* **业务逻辑**: 
  - 处理文本选择和答疑组件的唤起逻辑
  - 提供当前题目上下文给答疑组件
  - 管理答疑组件与练习流程的集成

##### View层
* **组件结构**: 
  - `QAButton`: 答疑入口按钮
  - `TextSelectionHandler`: 长按文本选择处理组件
  - 复用现有的答疑组件`QAComponent`
* **关键交互流程**:
  ```mermaid
  sequenceDiagram
    participant User
    participant View as QAIntegrationView
    participant ViewModel as QAViewModel
    participant QAComponent as 现有答疑组件
    
    alt 长按文本提问
      User->>View: 长按题干文字
      View->>ViewModel: handleTextSelection(selectedText)
      ViewModel->>ViewModel: 设置selectedText和questionContext
      ViewModel->>View: 更新isQAVisible为true
      View->>QAComponent: 传入selectedText和上下文
      QAComponent->>User: 显示带预填文本的答疑界面
    else 点击答疑按钮
      User->>View: 点击答疑入口按钮  
      View->>ViewModel: openQA()
      ViewModel->>ViewModel: 设置questionContext(不含selectedText)
      ViewModel->>View: 更新isQAVisible为true
      View->>QAComponent: 传入上下文(无预填文本)
      QAComponent->>User: 显示空白输入框的答疑界面
    end
    
    User->>QAComponent: 输入/修改问题并发送
    QAComponent->>User: 显示AI回答
    User->>QAComponent: 关闭答疑组件
    QAComponent->>ViewModel: 通知关闭
    ViewModel->>View: 更新isQAVisible为false
    View->>User: 返回练习界面
  ```

## 4. 关键技术实现

### 4.1 数据流方案

项目采用单向数据流与MVVM分层架构相结合的方案：

1. **数据获取与处理** (Model层)
   - 所有API交互封装在Model层
   - 通过SWR实现数据缓存和重新验证策略
   - Zod用于API数据校验与类型转换

2. **状态管理** (ViewModel层)
   - 使用Signals实现细粒度、高性能的状态更新
   - 结合Context API进行跨组件状态共享
   - 业务逻辑集中在ViewModel层，确保可测试性
   - 设计为Hook形式暴露给View层使用

3. **异步状态处理**
   - 所有异步操作状态(加载、错误、成功)统一管理
   - 乐观更新策略提升用户感知性能
   - 错误自动重试与降级机制

```typescript
// 核心数据流示例
function ExerciseCore() {
  // 获取全局上下文
  const { courseId, progressService } = useExerciseContext();
  
  // 使用ViewModel
  const {
    currentQuestion,
    selectedOptions,
    submissionResult,
    submitAnswer,
    goToNextQuestion
  } = useQuestionViewModel();
  
  // View层仅负责渲染和传递事件
  return (
    <div className="exercise-container">
      <QuestionContent 
        question={currentQuestion.value} 
        onOptionSelect={(optionId) => selectOption(optionId)} 
      />
      <ActionButtons 
        canSubmit={selectedOptions.value.length > 0}
        onSubmit={submitAnswer}
        onNext={goToNextQuestion}
      />
      {submissionResult.value && (
        <FeedbackView 
          result={submissionResult.value} 
          onComplete={goToNextQuestion}
        />
      )}
    </div>
  );
}
```

### 4.2 性能优化策略

针对练习组件的特性，采用以下性能优化策略：

1. **组件渲染优化**
   - 使用Signals实现精确更新，避免不必要的重渲染
   - 复杂列表和动画组件使用`memo`隔离重渲染
   - 懒加载非立即可见的次要组件

2. **转场动画优化**
   - 预加载关键动画资源
   - 使用硬件加速(transform, opacity)实现流畅动画
   - 动画组件独立渲染，不影响主体内容

3. **资源加载优化**
   - 题目预加载策略：载入当前题的同时预加载下一题
   - 动态导入(dynamic import)非关键组件
   - 图片资源优化：WebP格式、响应式图片、懒加载

4. **感知性能提升**
   - 使用骨架屏代替加载中状态
   - 即时视觉反馈，响应时间<100ms
   - 平滑的过渡动画增强体验流畅感

### 4.3 错误处理机制

建立三层防护的错误处理机制：

1. **预防性错误处理**
   - 输入数据验证 (Zod)
   - 类型安全的编码实践
   - 特性开关与降级方案预设

2. **运行时错误捕获**
   - 使用React Error Boundary拦截UI渲染错误
   - 全局错误处理中间件捕获异步操作错误
   - 自动重试策略处理网络波动

3. **用户反馈与恢复**
   - 友好的错误提示，避免技术术语
   - 提供明确的恢复路径与操作建议
   - 关键操作(如提交答案)的状态保存，支持断线重连后继续

```typescript
// 错误处理示例
function ExerciseErrorBoundary({ children }) {
  return (
    <ErrorBoundary
      fallback={({ error, resetError }) => (
        <ExerciseErrorFallback 
          error={error}
          onReset={resetError}
          onContinue={() => {
            resetError();
            exerciseService.recoverFromLastCheckpoint();
          }}
        />
      )}
    >
      {children}
    </ErrorBoundary>
  );
}
```

## 5. 技术风险与应对

| 风险 | 影响 | 概率 | 应对策略 |
|------|------|------|---------|
| 动画性能不佳，尤其在低端设备 | 高 | 中 | 1. 使用硬件加速CSS属性<br>2. 降级策略：低端设备简化动画<br>3. 帧率监控，性能触发自动降级 |
| 网络不稳定导致答案提交失败 | 高 | 中 | 1. 本地保存提交内容<br>2. 自动重试机制<br>3. 断线提醒与手动重试选项<br>4. 恢复会话功能 |
| 复用组件(勾画/答疑)集成问题 | 中 | 高 | 1. 明确定义接口契约<br>2. 构建独立测试环境验证集成<br>3. 设计降级方案：临时禁用功能不影响核心流程 |
| 不同设备/浏览器上动画/过渡效果不一致 | 中 | 中 | 1. 建立设备测试矩阵<br>2. 使用标准化的动画库<br>3. 适配层设计：针对特定浏览器调整实现 |
| 连续作答流畅度不足 | 高 | 低 | 1. 题目预加载机制<br>2. 减少题目间切换的网络依赖<br>3. 优化动画周期，避免过长等待 |

## 6. 成果检验

### 6.1 技术指标

- **性能指标**：
  - 题目加载时间 < 1.5秒 (95th percentile)
  - 用户提交答案后反馈启动延迟 < 300ms
  - 题目间切换时间 < 1秒
  - 页面主要内容加载时间(LCP) < 2秒
  - 首次交互延迟(FID) < 100ms

- **质量指标**：
  - 单元测试覆盖率 > 85%
  - E2E测试覆盖核心流程
  - Lighthouse性能分数 > 90
  - 内存泄漏为0

### 6.2 业务指标

- 练习题完成率提升8%
- 用户对练习环节的满意度评分达到4.2分（5分制）
- 连续答题时长增加15%
- 用户主动使用提问功能的比例提高20%

## 附录: 快速开发指南

### 关键组件实现优先级

1. **ExerciseCore与基础题目展示** - 优先级最高，是整个模块的核心功能
2. **即时反馈系统** - 与核心功能同步开发，是体验提升的关键
3. **转场动画** - 影响整体流畅感，应在基础功能稳定后实现
4. **答疑集成** - 作为辅助功能，可在后期集成

### 开发工作流建议

1. 先开发独立的ViewModel层并编写单元测试
2. 使用Storybook构建和测试独立UI组件
3. 集成基础功能并进行端到端测试
4. 迭代优化动画和过渡效果
5. 集成辅助功能并进行兼容性测试

### 测试策略

- **单元测试**：重点覆盖ViewModel的业务逻辑
- **组件测试**：使用React Testing Library测试UI组件
- **集成测试**：验证组件间配合和数据流
- **E2E测试**：使用Cypress模拟完整用户流程
- **性能测试**：监测关键指标并设置基准 
# Golang 常量与枚举定义模板

本文档提供 Go 项目中定义常量和枚举类型的标准代码模板。这些模板旨在提高代码的一致性和可读性。

## 1. 基础常量定义

### 1.1. 单个常量

```go
package consts

const DefaultPageSize = 10
const MaxRetryAttempts = 3
const AdminUserRole = "admin"
```

### 1.2. 分组常量 (推荐)

```go
package consts

const (
	DefaultTimeoutSeconds = 5
	DefaultCacheTTL       = 60 * 60 // 1 hour in seconds
	AppName               = "YourApplicationName"
)

const (
	StatusOK    = 200
	StatusError = 500
)
```
**AI 指令示例**: "请在 `app/consts/config.go` 中定义一组常量，用于表示默认分页大小 (DefaultPageSize = 20) 和API版本 (ApiVersion = \"v1\")。"

## 2. 使用 `iota` 定义序列常量

`iota` 特别适用于定义递增的常量，例如状态码、错误码（简单场景）或枚举的基础值。

```go
package consts

// OrderStatus 使用 iota 定义订单状态
type OrderStatus int

const (
	OrderStatusPending    OrderStatus = iota + 1 // 1 (Pending)
	OrderStatusProcessing                        // 2 (Processing)
	OrderStatusShipped                           // 3 (Shipped)
	OrderStatusCompleted                         // 4 (Completed)
	OrderStatusCancelled                         // 5 (Cancelled)
	OrderStatusRefunded                          // 6 (Refunded)
)

// String() 方法使 OrderStatus 类型更易于打印和调试
func (s OrderStatus) String() string {
	switch s {
	case OrderStatusPending:
		return "Pending"
	case OrderStatusProcessing:
		return "Processing"
	case OrderStatusShipped:
		return "Shipped"
	case OrderStatusCompleted:
		return "Completed"
	case OrderStatusCancelled:
		return "Cancelled"
	case OrderStatusRefunded:
		return "Refunded"
	default:
		return "Unknown"
	}
}

// IsValid 检查状态值是否在定义的范围内 (可选)
func (s OrderStatus) IsValid() bool {
	return s >= OrderStatusPending && s <= OrderStatusRefunded
}
```
**AI 指令示例**: "请在 `app/user/consts/user_status.go` 文件中定义用户状态枚举 `UserStatus`，使用 `iota`，包含 `UserStatusActive` (值为1), `UserStatusInactive` (值为2), `UserStatusBanned` (值为3)，并为其实现 `String()` 方法。"

## 3. 字符串枚举 (类型安全)

对于需要更明确字符串表示的枚举，可以基于 `string` 类型定义。

```go
package consts

// PaymentMethod 定义支付方式 (字符串枚举)
type PaymentMethod string

const (
	PaymentMethodCreditCard PaymentMethod = "CREDIT_CARD"
	PaymentMethodPayPal     PaymentMethod = "PAYPAL"
	PaymentMethodBankTransfer PaymentMethod = "BANK_TRANSFER"
	PaymentMethodUnknown    PaymentMethod = "UNKNOWN"
)

func (pm PaymentMethod) String() string {
	return string(pm)
}

// IsValid 检查支付方式是否有效 (可选)
func (pm PaymentMethod) IsValid() bool {
	switch pm {
	case PaymentMethodCreditCard, PaymentMethodPayPal, PaymentMethodBankTransfer:
		return true
	default:
		return false
	}
}

// ParsePaymentMethod 将字符串转换为 PaymentMethod 类型 (可选)
func ParsePaymentMethod(s string) PaymentMethod {
	pm := PaymentMethod(s)
	if !pm.IsValid() { // 或者直接 switch case 判断
		return PaymentMethodUnknown
	}
	return pm
}
```
**AI 指令示例**: "请在 `app/payment/consts/payment_type.go` 文件中定义支付类型枚举 `PaymentType`，基于 `string`，包含 `PaymentTypeOnline` (值为 \"ONLINE\") 和 `PaymentTypeOffline` (值为 \"OFFLINE\")，并为其实现 `String()` 和 `IsValid()` 方法。"

## 4. 通用业务错误码常量

虽然详细的错误处理和错误码体系会在 `error-code-standards.mdc` 中定义，但这里提供基础的错误码常量定义方式。

```go
package consts

// 通用业务错误码前缀或分类 (示例)
const (
	ErrCodePrefixUser    = "USER_"
	ErrCodePrefixOrder   = "ORDER_"
	ErrCodePrefixProduct = "PRODUCT_"
)

// 具体错误码 (通常结合错误定义使用)
const (
	// 用户相关 100xx
	ErrCodeUserNotFound      = 10001
	ErrCodeUserPasswordWrong = 10002
	ErrCodeUserDisabled      = 10003

	// 订单相关 200xx
	ErrCodeOrderNotFound     = 20001
	ErrCodeOrderCannotCancel = 20002
)

// 也可以定义为字符串常量
const (
	BizCodeUserAlreadyExists = "USER_ALREADY_EXISTS"
	BizCodeInvalidCoupon     = "COUPON_INVALID"
)
```
**AI 指令示例**: "在 `app/consts/errors.go` 中定义订单相关的业务错误码常量，`OrderAlreadyPaid` (值为20101) 和 `OrderStockNotEnough` (值为20102)。"

## 5. 组织与位置

-   **通用常量**: 放在项目根目录下的 `app/consts/` (或 `pkg/consts/`) 目录中，例如 `app/consts/common.go`, `app/consts/http_status.go`。
-   **模块特定常量**: 放在对应业务模块的 `consts` 子目录或直接在模块的 `domain` 层下，例如 `app/user/domain/consts/user_roles.go` 或 `app/user/consts/user_permissions.go`。
-   **错误码相关常量**: 参照错误码规范，可能集中管理或分散到各模块。

**建议**: 保持常量定义集中、清晰，并有适当的注释说明其用途。

// @ts-nocheck
'use client';

import { useState, useEffect } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { Spinner } from '@chakra-ui/react';
import Head from 'next/head';

interface MarkdownPreviewProps {
  downloadUrl: string;
}

const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({ downloadUrl }) => {
  const [markdown, setMarkdown] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // 在组件挂载后初始化 Mermaid
  useEffect(() => {
    // 添加 Mermaid 脚本到页面
    const script = document.createElement('script');
    script.src = 'https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js';
    script.async = true;
    script.onload = () => {
      console.log('Mermaid script loaded');
      
      // 初始化 Mermaid
      if (window.mermaid) {
        try {
          console.log('Initializing mermaid');
          window.mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            flowchart: {
              htmlLabels: true,
              useMaxWidth: true,
            },
          });
        } catch (error) {
          console.error('Error initializing mermaid:', error);
        }
      }
    };
    document.head.appendChild(script);
    
    // 清理函数
    return () => {
      if (script.parentNode) {
        script.parentNode.removeChild(script);
      }
    };
  }, []);

  // 在 Markdown 内容变化后重新渲染 Mermaid 图表
  useEffect(() => {
    if (!isLoading && markdown && window.mermaid) {
      // 给 DOM 一些时间来渲染
      const timer = setTimeout(() => {
        try {
          console.log('Running mermaid');
          window.mermaid.run();
        } catch (error) {
          console.error('Error running mermaid:', error);
        }
      }, 500);
      
      return () => clearTimeout(timer);
    }
  }, [isLoading, markdown]);

  useEffect(() => {
    const fetchMarkdown = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('Fetching markdown from URL:', downloadUrl);
        
        const response = await fetch(downloadUrl, {
          method: 'GET',
          headers: {
            'Accept': 'text/markdown, text/plain, */*',
            'Cache-Control': 'no-cache'
          },
          mode: 'cors',
          credentials: 'same-origin'
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch markdown: ${response.status} ${response.statusText}`);
        }
        
        const text = await response.text();
        console.log('Markdown content fetched, length:', text.length);
        setMarkdown(text);
      } catch (err) {
        console.error('Error fetching markdown:', err);
        setError(err instanceof Error ? err.message : 'Unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };

    if (downloadUrl) {
      fetchMarkdown();
    } else {
      console.warn('No downloadUrl provided to MarkdownPreview component');
      setError('No URL provided for markdown content');
      setIsLoading(false);
    }
  }, [downloadUrl]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="xl" color="blue.500" />
        <span className="ml-3 text-gray-600">加载 Markdown 内容...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
        <p className="font-medium">加载失败</p>
        <p className="text-sm">{error}</p>
      </div>
    );
  }

  return (
    <>
      {/* 添加内联脚本确保 Mermaid 初始化 */}
      <div dangerouslySetInnerHTML={{ __html: `
        <script>
          document.addEventListener('DOMContentLoaded', function() {
            if (typeof mermaid !== 'undefined') {
              console.log('Inline script: initializing mermaid');
              mermaid.initialize({
                startOnLoad: true,
                theme: 'default'
              });
              setTimeout(function() {
                console.log('Inline script: running mermaid');
                mermaid.run();
              }, 1000);
            } else {
              console.log('Inline script: mermaid not loaded yet');
            }
          });
        </script>
      ` }} />
      
      <div className="markdown-preview bg-white rounded-lg shadow-lg border border-gray-200 p-6 max-w-4xl mx-auto">
        <ReactMarkdown
          className="markdown-body"
          remarkPlugins={[remarkGfm]}
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              
              // 处理 Mermaid 图表
              if (!inline && match && (match[1] === 'mermaid' || match[1] === 'flowchart')) {
                const mermaidContent = String(children).replace(/\n$/, '');
                
                return (
                  <div className="my-4">
                    <pre className="mermaid">
                      {mermaidContent}
                    </pre>
                  </div>
                );
              }
              
              // 处理其他代码块
              return !inline && match ? (
                <SyntaxHighlighter
                  style={vscDarkPlus}
                  language={match[1]}
                  PreTag="div"
                  {...props}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              ) : (
                <code className={className} {...props}>
                  {children}
                </code>
              );
            },
          }}
        >
          {markdown}
        </ReactMarkdown>
      </div>
    </>
  );
};

// 为 TypeScript 添加全局类型声明
declare global {
  interface Window {
    mermaid: {
      initialize: (config: any) => void;
      init: (config?: any, nodes?: NodeListOf<Element>) => void;
      run: () => void;
    };
  }
}

// 只导出一次
export default MarkdownPreview;
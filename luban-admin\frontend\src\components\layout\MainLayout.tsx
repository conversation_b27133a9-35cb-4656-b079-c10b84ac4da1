'use client';

import { usePathname } from 'next/navigation';
import Header from './Header';
import Footer from './Footer';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout = ({ children }: MainLayoutProps) => {
  const pathname = usePathname();
  const isWorkflowPage = pathname?.includes('/workflow');
  
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className={`flex-grow ${isWorkflowPage ? 'pt-28' : 'pt-16'}`}>
        {children}
      </main>
      <Footer />
    </div>
  );
};

export default MainLayout;
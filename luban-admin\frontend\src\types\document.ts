export interface DocumentFile {
  file_id: string;
  filename: string;
  content_type: string;
  size: number;
}

export interface DocumentTask {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress: number;
  result?: {
    file_id: string;
    file_name: string;
    output_file: string;
    output_path: string;
  };
  error?: string;
}

export interface FeishuDocumentRequest {
  url: string;
  replace_images: boolean;
  output_format: string;
}

export interface PDFDocumentRequest {
  file_id: string;
  replace_images: boolean;
  output_format: string;
}

export interface DocumentProcessResponse {
  task_id: string;
  status: string;
  message: string;
}

export interface FeishuDocV2Request {
  url: string;
  replace_images?: boolean;
  app_id?: string;
  app_secret?: string;
}

export interface FeishuDocV2Response {
  task_id: string;
  status: 'processing' | 'completed' | 'failed';
  message: string;
  download_url?: string;
  output_path?: string;
}

export interface DocumentContent {
  content: string;
  images: DocumentImage[];
}

export interface DocumentImage {
  id: string;
  url: string;
  alt?: string;
  ocr_text?: string;
  analysis?: string;
} 
# Golang 领域实体与 GORM 模型代码模板

本文档提供 Go 项目中定义领域实体 (Domain Entity) 和 GORM 模型 (Data Object / Persistent Object) 的标准代码模板。这些模板旨在促进DDD实践，并确保与数据库交互的一致性和正确性。

## 1. 领域实体 (Domain Entity)

领域实体代表核心业务概念，包含业务属性和行为。它们通常定义在 `app/{module_name}/domain/entity/` 目录下。

```go
package entity

import (
	"time"
	"your_project/app/pkg/customerrors" // 假设的自定义错误包
)

// UserRole 定义用户角色类型 (可能来自 consts 包)
type UserRole string

const (
	UserRoleAdmin  UserRole = "ADMIN"
	UserRoleMember UserRole = "MEMBER"
)

// User 领域实体示例
type User struct {
	ID        int64      `json:"id"` // 唯一标识符
	Username  string     `json:"username"`
	Email     string     `json:"email"`
	Password  string     `json:"-"` // 密码通常不在JSON中暴露，且在领域层是哈希过的
	Role      UserRole   `json:"role"`
	IsActive  bool       `json:"isActive"`
	CreatedAt time.Time  `json:"createdAt"`
	UpdatedAt time.Time  `json:"updatedAt"`
	DeletedAt *time.Time `json:"deletedAt,omitempty"` // 用于软删除

	// 非持久化字段 (业务逻辑中临时使用)
	// lastLoginIP string
}

// NewUser 创建一个新的用户实体 (工厂方法)
func NewUser(username, email, rawPassword string, role UserRole) (*User, error) {
	if username == "" {
		return nil, customerrors.NewInvalidArgumentError("username cannot be empty")
	}
	if email == "" { // 进一步的邮箱格式校验等
		return nil, customerrors.NewInvalidArgumentError("email cannot be empty")
	}
	if rawPassword == "" { // 密码强度校验等
		return nil, customerrors.NewInvalidArgumentError("password cannot be empty")
	}

	// 实际项目中，密码哈希应该在这里或专门的密码服务中进行
	hashedPassword := "hashed_" + rawPassword // 简化示例

	now := time.Now().UTC()
	return &User{
		// ID 通常由数据库生成，在创建新实体时为空或由特定策略生成
		Username:  username,
		Email:     email,
		Password:  hashedPassword,
		Role:      role,
		IsActive:  true,
		CreatedAt: now,
		UpdatedAt: now,
	}, nil
}

// ChangePassword 修改用户密码 (领域行为)
func (u *User) ChangePassword(currentPassword, newPassword string) error {
	// 实际应比较哈希后的密码
	if u.Password != "hashed_"+currentPassword {
		return customerrors.NewAuthenticationError("current password incorrect")
	}
	if len(newPassword) < 6 { // 示例：新密码强度校验
		return customerrors.NewInvalidArgumentError("new password is too short")
	}
	u.Password = "hashed_" + newPassword
	u.UpdatedAt = time.Now().UTC()
	return nil
}

// Deactivate 停用用户 (领域行为)
func (u *User) Deactivate() {
	u.IsActive = false
	u.UpdatedAt = time.Now().UTC()
}

// IsAdmin 检查用户是否为管理员 (领域查询方法)
func (u *User) IsAdmin() bool {
	return u.Role == UserRoleAdmin
}
```
**AI 指令示例**: "请在 `app/product/domain/entity/product.go` 中定义 `Product` 领域实体，包含 `ID (int64)`, `Name (string)`, `SKU (string)`, `Price (float64)`, `Stock (int)`, `CreatedAt (time.Time)`, `UpdatedAt (time.Time)` 字段。并为其添加一个工厂方法 `NewProduct` 和一个更新价格的方法 `UpdatePrice`。"

## 2. GORM 模型 (DO/PO - Data Object / Persistent Object)

GORM 模型用于与数据库表进行映射，通常定义在 `app/{module_name}/dao/model/` (或 `model/entity/`，根据项目规范) 目录下。它们是数据持久化的表示。

```go
package model // 或 modelentity, bizmodel 等

import (
	"time"
	"gorm.io/gorm" // 引入 gorm
)

// UserDO (Data Object) 或 UserPO (Persistent Object) - GORM 模型
type UserDO struct {
	ID        int64          `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	Username  string         `gorm:"column:username;type:varchar(100);not null;uniqueIndex" json:"username"`
	Email     string         `gorm:"column:email;type:varchar(255);not null;uniqueIndex" json:"email"`
	Password  string         `gorm:"column:password;type:varchar(255);not null" json:"-"` // 密码通常不通过JSON直接暴露
	Role      string         `gorm:"column:role;type:varchar(50);not null;default:MEMBER" json:"role"` // 领域实体中的 UserRole 类型在此处映射为 string
	IsActive  bool           `gorm:"column:is_active;not null;default:true" json:"isActive"`
	CreatedAt time.Time      `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
	UpdatedAt time.Time      `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index" json:"deletedAt,omitempty"` // GORM 的软删除支持

	// 可以添加 TableName() 方法来自定义表名，如果结构体名不符合GORM的复数化规则或需要指定表名
	// func (UserDO) TableName() string {
	//  return "tbl_users"
	// }
}

// CommonFields 包含所有GORM模型通用的字段，如ID, CreatedAt, UpdatedAt, DeletedAt (可选的嵌入结构)
// type CommonFields struct {
// 	ID        int64          `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
// 	CreatedAt time.Time      `gorm:"column:created_at;autoCreateTime" json:"createdAt"`
// 	UpdatedAt time.Time      `gorm:"column:updated_at;autoUpdateTime" json:"updatedAt"`
// 	DeletedAt gorm.DeletedAt `gorm:"column:deleted_at;index" json:"deletedAt,omitempty"`
// }

// PostDO 示例，演示嵌入 CommonFields
// type PostDO struct {
// 	CommonFields // 嵌入通用字段
// 	Title   string `gorm:"column:title;type:varchar(255);not null" json:"title"`
// 	Content string `gorm:"column:content;type:text" json:"content"`
// 	UserID  int64  `gorm:"column:user_id;not null;index" json:"userId"` // 外键
// 	User    UserDO `gorm:"foreignKey:UserID"` // GORM关联 (可选，如果需要预加载等)
// }

```
**AI 指令示例**: "请在 `app/order/dao/model/order.go` 中定义 `OrderDO` GORM 模型，包含 `ID (int64, primaryKey, autoIncrement)`, `OrderSN (string, uniqueIndex)`, `UserID (int64, index)`, `TotalAmount (float64)`, `Status (string)`, `CreatedAt (time.Time, autoCreateTime)`, `UpdatedAt (time.Time, autoUpdateTime)`, `DeletedAt (gorm.DeletedAt, index)` 字段。并为 `OrderSN` 字段设置 `varchar(100)` 类型。"

## 3. 实体 (Entity) 与 数据对象 (DO) 之间的转换

转换逻辑通常放在 Repository 实现中，或者在 Service 层。也可以定义专门的 Mapper 结构体。

```go
package user // 示例: 放在 user 模块的 service 或 dao 的 mapper 文件中

import (
	"your_project/app/user/domain/entity"
	"your_project/app/user/dao/model"
	"time"
)

// ToUserEntity 将 UserDO 转换为 User 领域实体
func ToUserEntity(do *model.UserDO) *entity.User {
	if do == nil {
		return nil
	}
	var deletedAt *time.Time
	if do.DeletedAt.Valid {
		deletedAt = &do.DeletedAt.Time
	}
	return &entity.User{
		ID:        do.ID,
		Username:  do.Username,
		Email:     do.Email,
		Password:  do.Password, // 注意：密码通常是哈希过的
		Role:      entity.UserRole(do.Role), // 类型转换
		IsActive:  do.IsActive,
		CreatedAt: do.CreatedAt,
		UpdatedAt: do.UpdatedAt,
		DeletedAt: deletedAt,
	}
}

// ToUserDO 将 User 领域实体转换为 UserDO
func ToUserDO(e *entity.User) *model.UserDO {
	if e == nil {
		return nil
	}
	var deletedAt gorm.DeletedAt
	if e.DeletedAt != nil {
		deletedAt.Time = *e.DeletedAt
		deletedAt.Valid = true
	}
	return &model.UserDO{
		ID:        e.ID,
		Username:  e.Username,
		Email:     e.Email,
		Password:  e.Password,
		Role:      string(e.Role), // 类型转换
		IsActive:  e.IsActive,
		CreatedAt: e.CreatedAt,
		UpdatedAt: e.UpdatedAt,
		DeletedAt: deletedAt,
	}
}

// ToUserEntityList 转换 DO 列表为 Entity 列表
func ToUserEntityList(dos []*model.UserDO) []*entity.User {
	entities := make([]*entity.User, 0, len(dos))
	for _, do := range dos {
		entities = append(entities, ToUserEntity(do))
	}
	return entities
}

// ToUserDOList 转换 Entity 列表为 DO 列表
func ToUserDOList(entities []*entity.User) []*model.UserDO {
	dos := make([]*model.UserDO, 0, len(entities))
	for _, e := range entities {
		dos = append(dos, ToUserDO(e))
	}
	return dos
}
```
**AI 指令示例**: "请为 `Product` 实体和 `ProductDO` 模型实现转换函数 `ToProductEntity` 和 `ToProductDO`，放在 `app/product/service/mapper.go` (或相应的DAO层) 中。"

## 4. 关键点与最佳实践

-   **领域实体**: 关注业务逻辑和不变量，包含业务方法。
-   **GORM 模型 (DO)**: 专注于数据持久化，与数据库表结构一一对应。字段应使用 GORM tags (如 `gorm:"column:my_field;type:varchar(255);not null;uniqueIndex"`) 来精确控制表结构和约束。
-   **JSON Tags**: 为需要在API中暴露的字段添加 `json` tags，注意小写驼峰命名 (`json:"fieldName"`)。
-   **时间字段**: `CreatedAt`, `UpdatedAt` 通常使用 `time.Time`，并利用 GORM 的 `autoCreateTime` 和 `autoUpdateTime`。
-   **软删除**: 使用 `gorm.DeletedAt` 实现软删除，并为相关查询添加条件。
-   **类型安全**: 领域实体中的枚举或自定义类型，在 GORM 模型中可能需要映射为基础类型（如 `string`, `int`）。转换函数中需处理这些类型的映射。
-   **分离关注点**: 避免在 GORM 模型中直接包含复杂的业务逻辑。业务逻辑应在领域实体或 Service 层处理。
-   **转换位置**: 实体与DO的转换函数可以放在 Service 层、Repository 实现层，或者专门的 Mapper 包中，根据项目结构和团队偏好决定。

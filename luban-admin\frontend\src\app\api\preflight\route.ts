import { NextRequest, NextResponse } from 'next/server';

/**
 * 预检请求处理程序
 * 用于处理OPTIONS请求，返回200状态码和CORS头
 */
export async function GET(request: NextRequest) {
  return createPreflightResponse();
}

export async function POST(request: NextRequest) {
  return createPreflightResponse();
}

export async function OPTIONS(request: NextRequest) {
  return createPreflightResponse();
}

// 创建预检响应
function createPreflightResponse() {
  const response = NextResponse.json({ message: 'Preflight request handler' }, { status: 200 });
  
  // 设置CORS头
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Allow-Origin', '*'); // 在生产环境中应该限制为特定域名
  response.headers.set('Access-Control-Allow-Methods', 'GET,OPTIONS,PATCH,DELETE,POST,PUT');
  response.headers.set(
    'Access-Control-Allow-Headers',
    'X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version, Authorization, X-Request-Purpose'
  );
  
  return response;
}
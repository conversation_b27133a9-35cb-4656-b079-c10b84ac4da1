运营后台&用户中心-技术设计

### 版本管理

| 版本号 | 日期 | 变更人 | 变更类型 | 变更详情 |
| --- | --- | --- | --- | --- |
| V1.0.0 | 2025-02-24 |  | 新建 | 新建文档 |
|  |  |  |  |  |
|  |  |  |  |  |

### 运营后台产品需求文档：

[运营后台产品_1期_账号_需求PRD ](https://fqspoay1wny.feishu.cn/docx/YJoxdehtzoVnLbxclgQcymcBnog)

[运营后台产品_1期_框架&教务_需求PRD](https://fqspoay1wny.feishu.cn/docx/T8fpdKabSo4BE2xMYk6cCa8unOd)

### 总体架构：

![board_HZfHwzRzfheJ5rbCj8pcn2xAnQb](https://static.test.xiaoluxue.cn/demo41/prd_images/1746001727995.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

**1. 图片解析与架构师洞察**

*   **图片类型**: 此图片为一张 **流程图**。
*   **文档来源**: 推测出自 **技术设计** 或 **业务需求** 文档，用于描述用户退款成功后的系统处理逻辑。
*   **关键元素与组成部分**:
    *   **触发事件**: `退款成功`。
    *   **核心决策节点**:
        *   `试听课?` (判断退款商品是否为试听课)。
        *   `全部退款完成?` (判断关联的订单是否已全额退款，此判断在试听课和非试听课分支均出现)。
    *   **处理动作**:
        *   `查询关联报读信息` (当试听课退款时，查找可能关联的正价课报名记录)。
        *   `清空试听权益` (当试听课退款且关联订单全额退款时，移除用户的试听权限)。
        *   `发送报读退款成功消息` (当非试听课退款且关联订单全额退款时，通知用户报名已成功退款)。
    *   **流程终点**: `结束` (流程处理完成的多个出口)。
*   **元素间关联 (层级化结构)**:
    1.  流程由 `退款成功` 事件触发。
    2.  根据退款商品类型（是否为 `试听课`）进行分支。
    3.  **试听课分支**:
        *   先 `查询关联报读信息`。
        *   再判断关联订单是否 `全部退款完成`。
        *   若是，则 `清空试听权益`，流程 `结束`。
        *   若否，则直接 `结束` (可能表示部分退款或无需处理权益)。
    4.  **非试听课分支**:
        *   直接判断关联订单是否 `全部退款完成`。
        *   若是，则 `发送报读退款成功消息`，流程 `结束`。
        *   若否，则直接 `结束` (同上，可能为部分退款情况)。
*   **核心作用与价值 (结合教育领域)**:
    *   **精准处理试听课退款**: 区分试听课和正价课的退款逻辑，这在教育场景中非常重要，因为试听课往往是营销或体验性质，其退款可能需要额外处理（如关联查询、权益回收）。
    *   **用户权益管理**: 在确认全额退款后，`清空试听权益` 确保了已退款用户不再占用试听资源，维持业务规则的严肃性。
    *   **状态同步与通知**: 通过 `全部退款完成?` 的检查，确保在合适的时机（完全退款时）才执行后续动作（如清空权益、发送通知），避免在部分退款时错误操作。`发送报读退款成功消息` 保证了用户在正价课退款成功后能收到明确通知，提升用户体验。
    *   **流程闭环**: 该流程清晰地定义了退款成功后的系统自动化处理步骤，确保了财务操作（退款）与业务状态（用户权益、通知）的一致性。

**2. 功能模块拆解**

*   **退款成功事件监听器**:
    *   *功能概述*: 捕获并启动退款成功后的处理流程。
*   **退款类型判断模块**:
    *   *功能概述*: 识别退款关联的商品是否为“试听课”。
*   **关联订单查询模块**:
    *   *功能概述*: 根据试听课信息，查找其可能关联的正价课报名记录。
*   **订单退款状态检查模块**:
    *   *功能概述*: 查询指定订单（报读信息）的退款状态，判断是否已完成全额退款。
*   **试听权益管理模块**:
    *   *功能概述*: 移除用户的试听课访问或使用权限。
*   **用户消息通知模块**:
    *   *功能概述*: 向用户发送退款成功的确认消息（针对非试听课的报读）。
*   **流程控制/路由模块**:
    *   *功能概述*: 根据判断节点的结果，引导流程走向不同的处理分支或结束。

**3. Mermaid 流程图描述**

```mermaid
flowchart TD
    A[退款成功] --> B{试听课?};
    B -- 是 --> C[查询关联报读信息];
    C --> D{全部退款完成?};
    D -- 是 --> E[清空试听权益];
    D -- 否 --> F[结束];
    E --> G[结束];
    B -- 否 --> H{全部退款完成?};
    H -- 是 --> I[发送报读退款成功消息];
    H -- 否 --> J[结束];
    I --> K[结束];

```

**4. 总结约束说明**

本次解析严格依据图片中展示的流程图内容进行，未包含图片以外的信息或进行主观臆测。

【============== 图片解析 END ==============】



业务框架，前端使用vue, 后端使用kratos + gin 

功能模块: 参考一期需求

内部用户管理：超级管理员可以创建所有用户，包括普通管理员和驻校运营人员

学校管理：管理员可以新增合作校信息，并管理学校状态(合作，终止合作等)

权限管理：不同人员绑定不同角色分配不同权限，最终决定不同用户展示看板不同，可操作项不同

数据字典管理：元数据初始化和管理

数据存储：持久化和缓存上层生产数据



### 运营后台的基本结构

![image_CUUXbijQqoDoO4xMbbAc5WGTnTf](https://static.test.xiaoluxue.cn/demo41/prd_images/1746001728615.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

1.  **图片类型与核心解析**

    *   **图片类型**: 此图片为 **UI 界面截图 (UI Screenshot)** 或 **界面原型图 (UI Mockup)**。
    *   **来源文档**: 通常出自 **需求文档 (Requirement Document)** 或 **UI/UX 设计文档**。
    *   **关键元素与层级结构**:
        *   **顶层**: 页面标题 "邀请码"。
        *   **引导层**: 提示信息 "请联系您的专属顾问"。
        *   **交互层**:
            *   输入框 (Placeholder: "请输入邀请码")，用于用户输入。
            *   操作按钮 "确定"，用于提交输入。
        *   **视觉/装饰层**: 背景图形和锁状图标。
    *   **元素关联与核心作用**:
        *   该界面构成了一个独立的功能节点，要求用户提供特定的 "邀请码" 才能继续。
        *   **标题**明确了页面的核心目的。
        *   **提示信息**指引用户获取邀请码的途径（联系顾问），体现了业务流程中的一个前置条件或特定用户获取机制。
        *   **输入框**和**确定按钮**是核心交互元素，用户通过它们输入信息并触发后续的验证逻辑。
        *   在互联网教育场景下，此界面核心作用是实现 **访问控制** 或 **特定资格验证**。例如，用于新用户通过特定渠道（顾问推荐）注册激活、加入私密课程/班级、兑换特定权益或优惠等。其价值在于支持精细化运营、渠道管理和特定用户群体的筛选与转化。

2.  **组成部分与功能概述**

    *   **邀请码标题 (Invitation Code Title)**: 模块功能：明确告知用户当前页面的主题是关于邀请码。
    *   **提示语 (Instructional Text)**: 模块功能：引导用户行为，说明获取邀请码的方式（需联系专属顾问）。
    *   **邀请码输入框 (Invitation Code Input Field)**: 模块功能：提供用户输入邀请码的区域。
    *   **确定按钮 (Confirm Button)**: 模块功能：用户输入完毕后，点击此按钮提交邀请码进行验证或下一步操作。
    *   **装饰图标 (Decorative Icon)**: 模块功能：视觉辅助元素，强化“解锁”、“验证”的意象，非核心功能模块。

3.  **Mermaid 图表描述 (用户交互流程)**

    由于该图片展示的是一个静态 UI 界面，最适合使用流程图 (Flowchart) 来描绘基于此界面的预期用户操作流程：

    ```mermaid
    flowchart TD
        A[用户进入邀请码页面] --> B{显示邀请码输入界面};
        B -- 根据提示 --> C(联系专属顾问获取邀请码);
        C -- 获取后 --> D[在输入框输入邀请码];
        D --> E[点击“确定”按钮];
        E --> F((系统验证邀请码)); subgraph 系统响应 (implied)
        direction TB
        F -- 验证成功 --> G[进入后续流程/获得权限];
        F -- 验证失败 --> H[提示错误信息];
         end
    ```
    *(注意：Mermaid 图中的 C、F、G、H 节点代表的是根据 UI 推断出的相关流程或系统响应，并非图片直接展示的内容，但有助于理解该 UI 在整体流程中的作用。)*

4.  **内容约束**

    本次解析严格依据图片所示内容进行，未添加图片外的信息。

5.  **原则遵循**

    *   **第一性原理**: 从 UI 元素的基本功能（输入、提交、提示）出发进行分析。
    *   **KISS**: 总结语言简洁明了，聚焦核心功能。
    *   **MECE**: 各组成部分的拆解相互独立，共同构成了界面的完整元素集合。分析仅基于图片内容和对常见 UI 模式的理解，无过度推测。

【============== 图片解析 END ==============】





### 学校合作状态

![image_XRjbbBSdsopZ1YxYDsvcda9rn2g](https://static.test.xiaoluxue.cn/demo41/prd_images/1746001729186.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

1.  **图片类型与上下文分析**
    *   **图片类型:** 系统架构图。
    *   **来源:** 技术文档。
    *   **关键元素与关联:**
        *   该架构图展示了一个典型的Web应用后端服务体系结构，适用于（如我们互联网教育业务）需要处理高并发用户请求、分发静态资源（如课程视频、图片）和处理动态数据（如用户进度、测验）的场景。
        *   **层级化结构:**
            *   **用户层:** `用户` - 系统服务的最终访问者。
            *   **边缘网络层:** `CDN` - 加速内容分发，缓存静态资源，过滤部分请求。
            *   **应用服务层:** `源站 (包含Nginx)` - 处理业务逻辑，响应动态请求，作为静态资源的回源点。
            *   **缓存层:** `缓存服务 (Redis或Memcached)` - 存储热点数据，加速动态请求响应。
            *   **数据存储层:** `数据库 (MySQL/PostgreSQL)` - 持久化存储核心业务数据。
        *   **关联:** 用户请求首先到达CDN。CDN根据请求类型（静态/动态）和缓存状态决定是直接响应（静态缓存命中）还是回源到`源站`。`源站`处理请求时，会先查询`缓存服务`，若缓存未命中，则查询`数据库`，并将结果可能写回缓存，最终响应给CDN/用户。`Nginx`在`源站`中通常扮演反向代理、负载均衡或静态文件服务器的角色。
    *   **核心作用与价值:**
        *   **用户:** 驱动系统需求的来源。
        *   **CDN:** 提升全球用户访问速度和稳定性（特别是视频等大文件），降低源站负载和带宽成本。
        *   **源站 (Nginx):** 承载核心业务逻辑，是动态内容生成和静态内容管理的中心。Nginx提供高效的请求处理和转发能力。
        *   **缓存服务:** 大幅提高读取密集型操作的性能，降低数据库压力，提升系统响应速度和吞吐量。
        *   **数据库:**保障业务数据的可靠存储和一致性，是系统状态的最终记录者。

2.  **组成部分与功能模块**
    *   **用户 (User):**
        *   *功能概述:* 访问系统的终端用户，发起服务请求（如浏览课程、观看视频、提交答案）。
    *   **CDN (Content Delivery Network):**
        *   *功能概述:* 地理上分散的服务器网络，用于缓存静态资源（图片、视频、CSS、JS），就近响应用户请求，加速内容传递。
    *   **源站 (Origin Server - Nginx):**
        *   *功能概述:* 托管应用程序逻辑的服务器集群。处理动态请求（如用户登录、查询学习进度），并作为CDN未缓存内容的提供者。Nginx通常作为入口，处理HTTP请求、反向代理到应用服务器。
    *   **缓存服务 (Cache Service - Redis/Memcached):**
        *   *功能概述:* 高速内存数据库，用于存储临时性或频繁访问的数据（如用户会话、热点课程信息），减少对后端数据库的直接访问。
    *   **数据库 (Database - MySQL/PostgreSQL):**
        *   *功能概述:* 持久化数据存储系统，用于存储核心业务数据（如用户信息、课程结构、学习记录、支付信息）。

3.  **Mermaid 图表示 (简化流程/架构)**
    该图更偏向于架构组件图而非严格的流程图。使用 `graph` 可以表示组件及其主要连接关系：

    ```mermaid
    graph LR
        User[用户] --> CDN;
        CDN -- 请求 --> Origin[源站 (Nginx)];
        Origin -- 读/写 --> Cache[缓存服务 (Redis/Memcached)];
        Origin -- 读/写 --> DB[数据库 (MySQL/PostgreSQL)];
        Cache -- 缓存数据 --> Origin;
        DB -- 持久化数据 --> Origin;
        Origin -- 响应 --> CDN;
        CDN -- 响应 --> User;
    ```

4.  **内容约束**
    本次分析严格基于图片中显示的组件及其明确或隐含的连接关系进行，未引入图片外的信息。

5.  **分析原则遵循**
    *   **第一性原理:** 从用户请求发起，到数据存储，逐层分析各组件的基础作用和交互逻辑。
    *   **KISS:** 描述简洁明了，聚焦核心功能。
    *   **MECE:** 各组件功能明确区分（用户、边缘、应用、缓存、存储），覆盖了图中所有展示元素，无重叠。

【============== 图片解析 END ==============】





### 账号生命周期

![image_I282bs6YXooZdex1hYCcnkJ5nHc](https://static.test.xiaoluxue.cn/demo41/prd_images/1746001729693.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

作为互联网系统架构师，我们分析的这张图片是一张 **流程图**，它出自一份关于 **直播回放处理** 的 **需求或技术** 文档。该图清晰地描绘了直播结束后，系统自动生成回放并根据预设配置进行审核与发布的完整过程。

**关键元素与关联分析:**

1.  **触发点:** 整个流程由 "直播结束" 事件自动触发，表明这是一个事件驱动的自动化处理流程。
2.  **核心处理:** "系统生成回放" 是流程中的技术核心步骤，负责将直播流转化为可回放的视频文件。
3.  **关键决策点:** "是否配置了先发后审" 是核心业务逻辑分支点。
    *   **"先发后审" 路径 (是):** 优先保证回放的 **时效性**。系统先 "直接发布" 回放，然后进入 "等待人工审核" 状态。审核结果决定回放是 "保持发布状态" 还是被 "下架"。此路径满足了用户（如学生）希望尽快看到回放的需求，但将内容风险控制后置。
    *   **"审后再发" 路径 (否):** 优先保证内容的 **合规性与质量**。回放生成后直接进入 "等待人工审核" 状态，只有 "审核通过" 后才进行 "发布"。未通过审核则流程结束，回放不发布。此路径加强了内容监管，但牺牲了部分时效性。
4.  **人工干预:** "人工审核" 是流程中的关键人工环节，是内容质量和合规性的主要保障手段，无论哪种路径都需要此环节（只是时机不同）。
5.  **终态:** 流程最终导向 "结束" 状态，此时回放的状态可能是已发布、已下架或从未发布。

**核心作用与价值:**

该流程图清晰定义了互联网教育平台在处理直播回放时的业务规则和系统行为。它旨在平衡 **快速内容可达性**（对学习者友好）与 **内容质量控制**（平台责任与品牌形象）之间的关系。通过 "先发后审" 配置选项，为不同性质的直播内容或业务阶段提供了灵活性。对架构设计而言，它明确了回放生成、状态管理、配置读取、审核接口以及发布/下架等关键系统的交互逻辑和依赖关系。

**功能模块拆解:**

*   **直播状态监控模块:** 监测直播间状态，捕获 "直播结束" 事件以触发后续流程。
*   **回放自动生成模块:** 负责接收直播流数据，进行转码、存储等操作，生成标准格式的回放视频文件。
*   **配置管理模块:** 存储和提供 "先发后审" 等业务规则配置。
*   **回放发布/下架模块:** 控制回放视频对用户的可见性状态（发布使其可见，下架使其不可见）。
*   **人工审核工作流模块:** 提供审核任务队列、审核操作界面，并记录审核结果（通过/不通过）。
*   **回放状态管理模块:** 跟踪回放从生成到最终状态（如：生成中、待审核、已发布、已下架）的全生命周期。

**Mermaid 流程图描述:**

```mermaid
flowchart TD
    A(开始) --> B(直播结束后触发回放生成);
    B --> C(系统生成回放);
    C --> D{是否配置了先发后审};
    D -- 是 --> E(直接发布);
    E --> F(等待人工审核);
    F --> G{审核通过?};
    G -- 是 --> H(保持发布状态);
    G -- 否 --> I(下架);
    D -- 否 --> J(等待人工审核);
    J --> K{审核通过?};
    K -- 是 --> L(发布);
    K -- 否 --> M(结束);
    H --> M;
    I --> M;
    L --> M;
```

【============== 图片解析 END ==============】



### 运营后台访问控制

运营后台需要飞连才能使用




# PRD -  学生端  - 巩固练习## **版本管理**

<table>
<tr>
<td>**版本号**<br/></td><td>**日期**<br/></td><td>**变更人**<br/></td><td>**变更类型**<br/></td><td>**变更详情**<br/></td></tr>
<tr>
<td>V 1.0 <br/></td><td>2025.02.28<br/></td><td>张博<br/></td><td>新建文档<br/></td><td>- 新建文档<br/></td></tr>
<tr>
<td>V 5.0 <br/></td><td>2025.04.01<br/></td><td>张博<br/></td><td>完善需求<br/></td><td>- 完善方案细节<br/></td></tr>
<tr>
<td>V 5.0 <br/></td><td>2025.04.08<br/></td><td>张博<br/></td><td>完善需求<br/></td><td>- 增加再练一次环节<br/>- 增再练一次习分组概念<br/></td></tr>
<tr>
<td>V 9.0 <br/></td><td>2025.04.09<br/></td><td>张博<br/></td><td>完善需求<br/></td><td>- 调整背景和目标<br/></td></tr>
<tr>
<td>V 9.1<br/></td><td>2025.05.15<br/></td><td>张博<br/></td><td>需求调整<br/><br/></td><td>- 入口调整：练习完成后，于学科页巩固练习卡片，可点击【看报告】或【再练一次】（原加练） 。<br/>- 报告页调整：报告页【完成】按钮左侧新增【再练一次】，强化推荐引导。<br/>- 再练规则：再练环节的完成、退出规则、练习内容同巩固练习一致。支持重复练习，至熔断结束。<br/></td></tr>
</table>

## **关联需求**

<table>
<tr>
<td>**关联需求名称**<br/></td><td>**所属 PM**<br/></td><td>**需求进度**<br/></td><td>**文档链接**<br/></td></tr>
<tr>
<td>教师端 - 作业布置<br/></td><td>ou_9a5441acaedff58f838e20b0b5d863d0<br/></td><td>开发中<br/></td><td>[教师端_1期_备课 & 作业布置](https://wcng60ba718p.feishu.cn/wiki/G6OTwydxOi8gdmkYTKUcmQmHngd)<br/></td></tr>
<tr>
<td>巩固练习推题策略<br/></td><td>ou_b2709be118f67c0f1ae49e028241afd2<br/></td><td>开发中<br/></td><td>[巩固练习相关策略](https://wcng60ba718p.feishu.cn/docx/JYjedBulhoHiBDx1wGScCacHnrd)<br/></td></tr>
<tr>
<td>内容平台选题支持分题组选题<br/></td><td>ou_4075bb29f2fdec7724d8181104831d94<br/></td><td>开发中<br/></td><td>[PRD -  内容管理平台2.0 - 选题支持题组](https://wcng60ba718p.feishu.cn/wiki/C91Gwf402i2iU5kMs1vcPnICnDc)<br/></td></tr>
<tr>
<td>题型支持<br/></td><td>ou_4075bb29f2fdec7724d8181104831d94<br/></td><td>开发中<br/></td><td>[PRD -  题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)<br/></td></tr>
</table>

## **设计稿**

交互稿：
视觉稿：[Figma](https://www.figma.com/design/COdZQZZOFHZHsdI1zWX8Tm/%E5%AD%A6%E7%94%9F%E7%AB%AF-1.0?node-id=1805-6218&p=f&m=dev)
动效：

## 一、背景和目标

### 需求背景

巩固练习是产品的核心学习场景之一，是在 AI 学习后进行的巩固作用的练习，和 AI 课知识点关联度非常高。巩固练习能够对学生学习效果显著促进作用。调研发现原练习存在缺乏有效的情感反馈、激励机制不足等问题，需要整体优化学习体验，提升学习效果。

### 项目收益

巩固练习通过**与 AI 课知识点高度关联的题目**帮助学生在课程学习后检验学习效果并增强对知识点的掌握程度。**再练一次**逻辑，保证每一个学生在完成巩固练习后都可以达到掌握知识的目标，确保产品的效果。对练习体验优化，使练习流程有更丰富的**情感反馈和激励**，例如针对答题正确的学生及时给予激励反馈、对于答题错误的学生采用有效方式缓解其心理压力。通过以上改进，目标能够提升学生对巩固练习的接受度，提升巩固练习效果，最终实现学生成绩提升的目标。

### 覆盖用户

初中、高中学生

### 竞品调研

<table>
<tr>
<td>**维度**<br/></td><td>**成功要素提炼**<br/></td><td>**产品案例**<br/></td></tr>
<tr>
<td>动力机制<br/></td><td>即时反馈（宝石/徽章）、社交监督（排行榜）、情感绑定（角色 IP）<br/></td><td>多邻国、Kahoot<br/></td></tr>
<tr>
<td>作答形态<br/></td><td>微模块化知识、多题型转化（游戏/视频/AR）、UGC 生态<br/></td><td>Kahoot<br/></td></tr>
<tr>
<td>技术支撑<br/></td><td>AI 错题预测、多模态交互（语音/手势）、数据可视化（热力图/成长树）<br/></td><td>Quizzes<br/></td></tr>
<tr>
<td>心流设计<br/></td><td>难度渐进曲线、成就解锁仪式感、反常规彩蛋（如多邻国抽象例句）<br/></td><td>多邻国<br/></td></tr>
<tr>
<td>游戏化<br/></td><td>游戏场景的搭建，作答环节游戏化设定<br/></td><td>多邻国、Kahoot<br/></td></tr>
</table>

### 方案简述

本次巩固练习方案希望打造流畅、有效、反馈友好的练习体验：

- **题目推荐：**基于课程知识点精选高价值练习题，根据学生实时作答表现自动调整题目难度，确保每位学生都能获得适合自己水平的练习内容，保证学习效果。
- **再练一次：**通过对学生巩固练习后掌握度和正确率的判断，推荐巩固练习再练一次，确保学生在完成全部巩固练习后达到掌握知识目标，确保巩固练习效果。
- **情感化反馈：**针对不同的作答结果，提供差异化的情感反馈，包括对连续答对的递进强化反馈和对难度变化的明确提示，帮助学生建立信心并保持学习动力。
- **学习报告：**练习完成后，展示本次练习的数据和学生获得的积分（本期没有）。提供详细的学习报告包括掌握度变化、知识点分析，让学习成果清晰可见，为后续学习提供明确方向。

## **二、名词说明**

1. 巩固练习：在学生掌握新知识或技能后，借助有针对性的练习加深理解、强化记忆，将知识从 “知道” 转变为 “会用”，满足的场景是 AI 课后的巩固。
   一个有效的巩固练习，应该具备以下特征：

<table>
<tr>
<td>**核心作用**<br/></td><td>**特点**<br/></td></tr>
<tr>
<td>加深理解：通过练习发现知识的薄弱点。<br/>强化记忆：通过练习加深知识的记忆。<br/>培养迁移能力：通过变式练习（如同一知识点的不同题型），学会在不同场景下灵活运用知识。<br/></td><td>针对性：针对 AI 课知识点学习的重点、难点设计题目，避免盲目刷题。<br/>分层递进：从基础题到进阶题再到综合题，逐步提升难度。<br/>及时反馈：练习后立即核对答案或老师点评，及时修正错误。<br/></td></tr>
<tr>
<td colspan="2">巩固练习和其他练习的<u>关系和区别</u>：<br/>巩固练习和 AI 课中练习的关系：巩固练习是 AI 课中练习的补充和延伸，巩固练习较课中练习题型更多样，难度梯度也会更大。<br/>巩固练习和拓展练习的关系：拓展练习是在学生掌握了巩固练习的基础上（掌握度达到一定程度后）进行的练习，题型会更多样，难度对应也会有提升，能够通过拓展的练习强化学生对知识的掌握程度。<br/></td></tr>
</table>

## 三、业务流程

##### **练习流程**

需求范围：巩固练习，为蓝色流程。
![PWd9wPpm9h6MnUbT6Lpc2opRnKy](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_PWd9wPpm9h6MnUbT6Lpc2opRnKy.png)

###### 图片分析
没有生成内容



## 四、需求概览

<table>
<tr>
<td>**序号**<br/></td><td>**需求名称**<br/></td><td>**内容概述**<br/></td><td>**优先级**<br/></td></tr>
<tr>
<td>1<br/></td><td>巩固练习入口<br/></td><td>巩固练习初始时、中途退出后、练习完成后的入口变化。<br/></td><td>P0<br/></td></tr>
<tr>
<td>2<br/></td><td>进入练习<br/></td><td>首次进入与继续练习时的转场设计与体验优化<br/></td><td>P0<br/></td></tr>
<tr>
<td>3<br/></td><td>练习环节<br/></td><td>练习中页面框架设计及作答功能模块<br/></td><td>P0<br/></td></tr>
<tr>
<td>4<br/></td><td>题目间转场<br/></td><td>包含作答后反馈、难度调整反馈等。<br/></td><td>P0<br/></td></tr>
<tr>
<td>5<br/></td><td>学习报告<br/></td><td>总结学生每次完整练习的学习报告。<br/></td><td>P0<br/></td></tr>
<tr>
<td>6 <br/></td><td>再练一次环节<br/></td><td>根据学生掌握度情况，推荐再练一次环节。<br/></td><td>P0<br/></td></tr>
</table>

## 五、详细产品方案

![C3WkwbwZUhiKYzbqbORczZLRnYg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_C3WkwbwZUhiKYzbqbORczZLRnYg.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】
该图片描述了用户在线练习的完整流程，包含初始状态、练习中状态、练习完成状态以及中途退出和继续练习的场景。

**关键元素层级关系与关联：**

1.  **全局练习流程**
    *   **初始状态**
        *   用户通过“开始练习”进入流程。
    *   **练习中状态**
        *   系统首先进行“进入练习”的转场。
        *   若练习包含题组，则进行“题组(如有)”的转场。
        *   开始处理具体题目，以“题目-1”为例：
            *   用户作答后，系统判断“作答是否正确”。
            *   **如果作答正确**：
                1.  显示“作答反馈(正确)”。
                2.  提供“小组排名反馈”。
                3.  （若适用）触发“难度反馈上升(如有)”。
                4.  给出“不认真作答反馈”。
                5.  进入下一题，例如“题目组件-2”。
            *   **如果作答错误**：
                1.  显示“作答反馈(错误)”。
                2.  （若适用）触发“难度反馈下降(如有)”。
                3.  进入下一题，例如“题目组件-2”。
        *   题目按顺序依次呈现，从“题目组件-2”经过“...”(代表更多题目)，直至“题目组件-最后一题”。
    *   **练习完成状态**
        *   所有题目完成后，系统展示“练习报告”。
        *   用户可以选择“再练一次”，流程将返回至“开始练习”。
    *   **中途退出与继续**
        *   在“练习中状态”的任何题目作答环节（如“题目-1”、“题目组件-2”等），用户可以选择“中途退出”。
        *   退出后，用户可通过“继续练习”选项，流程将返回至“练习中状态”的“进入练习”转场环节，从而继续之前的练习。

**Mermaid 流程图描述：**

```mermaid
graph TD
    %% --- Nodes Definition ---
    A[开始练习]

    B(进入练习\n转场)
    C(题组(如有)\n转场)
    D1[题目-1]
    E{作答是否\n正确}
    F[作答反馈\n(正确)]
    G[小组排名\n反馈]
    H(难度反馈上\n升(如有))
    I[不认真作答\n反馈]
    J[题目组件-2]
    K[作答反馈\n(错误)]
    L(难度反馈下\n降(如有))
    M[...]
    N[题目组件-\n最后一题]

    O[练习报告]
    P[再练一次]

    Q[中途退出]
    R[继续练习]

    %% --- Flow Connections ---
    A --> B;
    B --> C;
    C --> D1;
    D1 --> E;

    E -- 是 --> F;
    F --> G;
    G --> H;
    H --> I;
    I --> J;

    E -- 否 --> K;
    K --> L;
    L --> J;

    J --> M;
    M --> N;
    N --> O;

    O -.-> P;
    P --> A;

    D1 --> Q;
    J --> Q;
    M --> Q;
    N --> Q;
    
    R --> B;

    %% --- Subgraphs for State Grouping (Visual separation as in image) ---
    subgraph 初始状态
        A
    end

    subgraph 练习中状态
        B
        C
        D1
        E
        F
        G
        H
        I
        J
        K
        L
        M
        N
    end

    subgraph 练习完成状态
        O
        P
    end
    
    %% Implicitly, Q and R relate to handling departures from normal flow
end
```
【============== 图片解析 END ==============】



<table>
<tr>
<td>**模块**<br/></td><td>**原型图**<br/></td><td>**需求说明**<br/></td></tr>
<tr>
<td>练习入口<br/></td><td>![in_table_FDkbbgFaPowFYmxY9UacwsFKnsc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FDkbbgFaPowFYmxY9UacwsFKnsc.png)<br/>![in_table_UCaRbRVzFoEaP7xXhmAcEKDynwc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_UCaRbRVzFoEaP7xXhmAcEKDynwc.png)<br/><br/>![in_table_HNgEbyNCroqG8oxPLZEcacB0n7b](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_HNgEbyNCroqG8oxPLZEcacB0n7b.png)<br/>![in_table_N577bLqY5oTFMUxEqcpccsmGnHd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_N577bLqY5oTFMUxEqcpccsmGnHd.png)<br/><br/></td><td>**路径：**<br/>- 【学习】Tab → 学科 → 学习地图 → 某个知识点 → 巩固练习模块。<br/>- 【学习】Tab → 学科 →老师布置的任务 → 巩固练习。<br/><br/>**入口展示：**<br/>- 名称：巩固练习<br/>- 预估题目数量：基于推题策略自动计算并展示，如“约 8 题”<br/><br/>**入口状态：**<br/>- **练习未完成：**- 判定条件：存在未作答题目（包含初始进入或中途退出）。- 入口：巩固练习 。- 任务到期时间：如果是老师布置的任务，展示对应的到期时间。- 交互：点击后展开巩固练习卡片，点击【进入练习】/【继续练习】跳转作答页面。<br/>- **练习已完成状态：**- 判定条件：首次巩固练习全部题目完成作答。- 入口：巩固练习  +  已完成 icon。- 已获得星星数量：已获+数量 + icon。- 交互：点击后展开巩固练习卡片，- 点击【看报告】跳转报告页面。- 点击【再练一次】/【继续练习】跳转作答页面。<br/></td></tr>
<tr>
<td>进入练习<br/></td><td>首次进入练习<br/>![in_table_Co9ybh7b3o8bfHxkn6Wc5qOanrg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Co9ybh7b3o8bfHxkn6Wc5qOanrg.png)<br/><br/>继续练习<br/>![in_table_ZPWxbu2yxo7DIoxk3tucfxO0nRg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZPWxbu2yxo7DIoxk3tucfxO0nRg.png)<br/><br/></td><td>**进入练习转场：**<br/>- **首次进入练习（所有题目未作答时），**- IP 角色动效出现- 文案：“开始练习：《课程名称》”- 课程名称过长时展示，<u>待 UI 确定</u>。- 展示时间： 2s。- 转场结束后接题组转场（如有）后，进入题目页。<br/><br/>- **继续练习（练习状态为练习中时），**- IP 角色动效出现- 文案：“继续练习：《课程名称》”- 课程名称过长时展示，<u>待 UI 确定</u>。- 展示时间： 2s。- 转场结束后接题组转场（如有）后，进入题目页。<br/><br/>- **转场加载失败兜底，>2 s 未加载成功直接进入题目页。**<br/></td></tr>
<tr>
<td>练习环节<br/></td><td>![in_table_IQG9beGLHoBodyxmxPrcDF7Dn3b](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_IQG9beGLHoBodyxmxPrcDF7Dn3b.png)<br/><br/>![in_table_FsN1b2MTvoUUQwx6uwDc6eiMnFK](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FsN1b2MTvoUUQwx6uwDc6eiMnFK.png)<br/><br/></td><td>**页面框架**<br/>- **顶部导航栏：**- 退出按钮：- 点击【退出按钮】弹出二次确认弹窗，![in_table_RIPGb6P1boVigPxDJq2c9LuUnYK](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RIPGb6P1boVigPxDJq2c9LuUnYK.png)- 弹出确认弹窗："确定要退出练习吗？进度已保存"。- 点击【确认退出】，退出至课程详情页。- 点击【继续练习】，留在当前页面并关闭弹窗。- **数据处理：**- 自动保存当前进度。- 记录最后作答题目 ID（保证下次进入还是该题目）。- 更新练习状态为"练习中"。- 课程详情页计算掌握度变化并展示。- 作答计时：复用题型组件，- 每题独立正计时（格式 00:00，上限 59:59）。- 学生未作答退出，暂停计时，回归继续累计（如一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。- 作答进度：- 进度展示：- 分母：预估学生作答数量。- 分子：依据推题策略，- 如果学生答对，进度涨。- 如果学生答错，但没有相似题，进度涨。- 如果学生答错，但有相似题需要再练一道，进度不变。<br/>- **题型组件：**- 题型组件：各题型按统一标准展示和交互，细节参考见[PRD -  题型支持 - 一期](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd)。<br/><br/>- **题目推荐：**- 练习中的题目和顺序，见[PRD - 巩固练习相关策略 - V1.0](https://wcng60ba718p.feishu.cn/docx/JYjedBulhoHiBDx1wGScCacHnrd)ou_b2709be118f67c0f1ae49e028241afd2。<br/><br/>- **题组转场：**![in_table_GEmEbgKgwoVf2lxw3V3clW90ndd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GEmEbgKgwoVf2lxw3V3clW90ndd.png)- 判断：当课程中有两个及以上的题组时，展示题组转场，如果仅有一个题组，则不展示题组转场。- 时机：进入/继续练习转场后，出现当前题组转场。- 题组名称：- 题组名称：取选题时对应题组的名称。- 题组预估题目： 约 n 题，推题策略提供。- 数据：取自本次巩固练习包含的题组名称。- 题组数量（转场上圆点数量）：- 取自本次巩固练习包含的题组数量。- 一屏最多展示 3 个圆点。- 题组内题目：- 取自本次巩固练习对应题组内推荐的题目，题目的全集来自于题组内选题。<br/>- **勾画组件：**![in_table_H0V7bUXxko2eY3xdYA4cRjKXnec](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_H0V7bUXxko2eY3xdYA4cRjKXnec.png)- 入口：作答界面悬浮【勾画】按钮（常驻显示）。- 交互：支持用户在作答环节时，点击【勾画】按钮唤起勾画组件，对题目进行勾画和标记。- 勾画工具。- 笔迹调整：- 颜色调整：支持蓝色、红色、黑色。- 粗细调整：细、中等、粗。- 橡皮擦：- 粗细调整：细、中等、粗。- 撤回、恢复。- 一键清空。- 交互：- 点击【完成】，收起勾画组件，同时将勾画的内容自动保存并展示。<br/>- **错题本组件：**- 自动添加规则：- 答错的题目自动添加到错题本，并提示“已加入错题本”。- 按钮名称变更为【加入错题本】。- 系统记录错误次数和最近错误时间。- 手动添加：- 对于正确作答的题目，用户可选择手动添加到错题本。- 界面位置：每道题底部设置"加入错题本"按钮。- 添加成功反馈：提示"已加入错题本"，展示时间 2 s。- 取消收藏- 点击【已加错题本】，按钮状态变更为【加入错题本】。- 移除成功反馈“已移出错题”，展示时间 2 s。- 添加错因标签- 标题：修改错因标签。- 副标题：标签的选择会影响智能推荐。- 系统标签：重点复习、没有思路、概念理解不清、思路不清晰、没有分类讨论、方法运用错误、思维定式、忽略隐含条件。<br/><br/>- **熔断机制**：- 熔断规则见，[PRD - 巩固练习相关策略 - V1.0](https://wcng60ba718p.feishu.cn/docx/JYjedBulhoHiBDx1wGScCacHnrd#share-N4BHdyohpomiq1x0cHTcB8YTnYf)<br/></td></tr>
<tr>
<td>题目间转场<br/></td><td>![in_table_PgGRbjgxsogDxgxmJApcopqonKc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_PgGRbjgxsogDxgxmJApcopqonKc.png)<br/><br/>![in_table_OmLGbk0H6ojvoQxgdt7cHNb0nGb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OmLGbk0H6ojvoQxgdt7cHNb0nGb.png)<br/><br/>![in_table_I2Dwb6LSwoNP4LxRXNzcleQHn6e](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_I2Dwb6LSwoNP4LxRXNzcleQHn6e.png)<br/><br/><br/><br/><br/>![in_table_JbUVbLSsdojkJzxKGGbcmN0Gnka](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_JbUVbLSsdojkJzxKGGbcmN0Gnka.png)<br/>![in_table_Lw0XbrQEEo4xbWx099Ucbz5Vn2b](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Lw0XbrQEEo4xbWx099Ucbz5Vn2b.png)<br/><br/></td><td>**转场顺序**<br/>![in_table_LTzNwdc9ehjdhwbltjEcan8rnDg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_LTzNwdc9ehjdhwbltjEcan8rnDg.png)<br/>**作答反馈：**<br/>- 弹出时机：每次作答自动批改/自评后触发，以下四种仅弹一种。<br/>- 展示时长： 2 s<br/>- 反馈（类型 + 内容）- 正确：IP 角色 + 音效 + 文案（随机展示）- 展示优先级：P1 。- 文案一：完美无缺！+ 积分获得数量- 文案二：进步神速！+ 积分获得数量- 文案三：你真的太棒了！+ 积分获得数量- 文案四：nice！正确！+ 积分获得数量- 文案五：优秀优秀！+ 积分获得数量- 错误：IP（如 Dron 安慰动作） + 音效 + 文案（随机展示）- 展示优先级：P1 。- 文案一：别灰心，继续！- 文案二：别着急，慢慢来～- 文案三：小错误，大进步！- 文案四：这道题像老朋友，再认认？（针对错题再练，判断规则学生做过该题目）- 文案五：偶尔失误很正常！（一个题错误时、连对中断时）- 连续正确：IP（Dolli 热血动作） + 音效 + 文案（随机展示）- 展示优先级：P0 ，连续正确反馈优先于正确。- 音效：不同连胜音效不同，2 次以上 连胜对应音效。- 文案一：[N 连胜]  + 断连？不存在的～+ 积分获得数量- 文案二：[N 连胜]  + 连胜守护成功！+ 积分获得数量- 文案三：[N 连胜]  + 连战连胜！+ 积分获得数量- 文案四：[N 连胜]  + 越战越勇！+ 积分获得数量- 文案五：[N 连胜]  + 稳定输出，超棒！+ 积分获得数量- 部分正确：IP（如 Dron 鼓励动作） + 音效 + 文案（随机展示）- 展示优先级：P1 。- 部分正确，再补一步！- 接近完美，只差一点！<br/>**特殊反馈：**<br/>- **不认真作答反馈：**- 判定规则：见[PRD - 巩固练习相关策略 - V1.0](https://wcng60ba718p.feishu.cn/docx/JYjedBulhoHiBDx1wGScCacHnrd)ou_b2709be118f67c0f1ae49e028241afd2。- 触发时机：- 优先级：P1- 命中规则时，在用户切换至下一题时在作答前出现。- 若无下一题（最后一题），则不触发提示。- 展示时长，展示时间 2 s。- 提示样式：轻题型，弹出提示框，不遮挡题目。- 提示文案（随机展示）：- 文案一：认真作答有助于掌握度提升- 文案二：分心会迷路哦～<br/><br/>- **难度变化反馈：**- 触发时机：- 优先级：P0 ，如果和不认真反馈同时出现时，难度变化优先展示之后展示不认真作答反馈。- 当依据推荐策略，题目难度调整时，在下一题出现前展示。- 展示时长： 2 s。- 提示样式：全屏展示，遮挡题目。- 难度上升反馈：- IP 角色 + 音效 + 文案（随机展示）- 音效：UI 确定- 反馈内容（随机反馈）：- 文案一：挑战升级！- 文案二：难度 + 1，继续冲！- 文案三：小试牛刀？现在才是正餐！- 文案四：精英题库已解锁！- 文案五：难度持续上升，勇攀更高峰！（当难度上升三次及以上时展示）- 难度下降反馈：- IP 角色 + 音效 + 文案（随机展示）- 音效：UI 确定。- 反馈内容（随机反馈）：- 文案一：阶梯下降，轻松跨越！- 文案二：坡度放缓，稳步积累！- 文案三：难度下降，再试试吧～- 文案四：难度持续下降，相信这次你一定可以！（当难度上升三次及以上时展示）<br/><br/></td></tr>
<tr>
<td>学习报告<br/></td><td>![in_table_WC4gb8MsSoqY17xOJ5ycXN3Tn2g](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WC4gb8MsSoqY17xOJ5ycXN3Tn2g.png)<br/><br/></td><td>**练习完成转场：**<br/>- ~~触发时机：学生完成最后一道题目并点击【继续】按钮后。~~<br/>- ~~转场内容：~~- ~~显示报告生成加载进度。~~- ~~文案提示：“练习完成，报告生成中...”~~<br/><br/>**练习结算页：**<br/>- 入口：- 首次进入：练习完成转场 - 查看练习结算页面。- 非首次进入：当练习已完成时，巩固练习入口状态变更为“巩固练习（已完成）”，点击进入学习报告查看。- <br/>- IP + 文案- 正确率 >= 90% ，IP（dolli）+ 文案（完美通关！) . - 正确率 >= 60%，< 90% ，IP（dolli）+ 文案（干的不错！) . - 正确率 < 60% ，IP（dron）+ 文案（再接再厉！) . - 本次掌握度变化：动效展示上涨或下降。- 本次学习时长：本次学习的时长（分）。- 本次答题数量：本次完成题目总数（题）。- 本次正确率：本次正确题目数/总题目数（%）。- 本次获得积分：本次练习获得的积分数量。- 展示练习题目的题号和对应的作答结果，![in_table_BRCsbahKeoSKN3xBspNcFlGHnRI](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BRCsbahKeoSKN3xBspNcFlGHnRI.png)- 支持查看详情，展示练习所有题号、作答结果、题目、答案、解析。- 题号和对应的作答结果，支持点击切换题目。- 题干：长度显示 10 字，超出展示 ...  . - 作答（默认隐藏）。 - 答案和解析（默认隐藏）。- 推荐学习：规则由策略提供ou_b2709be118f67c0f1ae49e028241afd2。<br/>- 交互：- 点击【完成】按钮，关闭结算页，回到对应课程详情页。- 点击【再练一次】按钮，进入到再练一次的练习环节。<br/><br/></td></tr>
<tr>
<td>**再练一次**<br/></td><td>![in_table_Q22sbx71moTZLtxxT56cTC0CnjK](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Q22sbx71moTZLtxxT56cTC0CnjK.png)<br/><br/></td><td>**触发规则：**<br/>- 触发条件：基于学生在巩固练习中的掌握度情况判断，具体规则见[PRD - 巩固练习相关策略 - V1.0](https://wcng60ba718p.feishu.cn/docx/JYjedBulhoHiBDx1wGScCacHnrd)<br/>- 入口强化：- 当策略判断需要再练一次时，练习报告页展示强化 【再练一次】推荐点击 。<br/>**入口：**<br/>- 练习结算页 - 推荐学习入口。<br/>- 课程详情页 - 学习路径。<br/>**进入再练一次转场，****同上。**<br/>- 首次进入再练一次：- 文案：开始练习 + 课程名称。- 展示时间： 2 s。<br/>- 继续练习，- 文案：继续练习 + 课程名称。- 展示时间： 2 s。<br/>**内容和交互：**<br/>- **题目来源**：- 策略根据掌握薄弱的知识点，从该节课程对应的题库中捞取题目<br/>- **作答体验**：- 保持与巩固练习一致的答题交互规范。- 包含题组分组机制、作答反馈、难度动态调整、勾画、错题本等功能。<br/>- **熔断机制**：熔断判断规则见，[PRD - 巩固练习相关策略 - V1.0](https://wcng60ba718p.feishu.cn/docx/JYjedBulhoHiBDx1wGScCacHnrd#share-N4BHdyohpomiq1x0cHTcB8YTnYf)<br/>**学习报告页：**<br/>- 在巩固练习报告一致进行累加，推荐学习由策略提供。<br/></td></tr>
</table>

![in_table_FDkbbgFaPowFYmxY9UacwsFKnsc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FDkbbgFaPowFYmxY9UacwsFKnsc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一款互联网教育产品的用户界面截图，具体元素及关联如下：

**一、 整体布局**
界面整体分为顶部信息栏、页面主体（三栏式布局）和底部导航栏。

**二、 界面元素详解**

1.  **顶部信息栏**
    *   **左上角**：显示当前日期为 "Mon Jun 3"。
    *   **右上角**：显示标准移动设备状态栏图标（如信号、Wi-Fi、电量）。

2.  **页面主体 (三栏式布局)**

    *   **2.1. 左侧栏：日程与概览**
        *   **2.1.1. 用户信息与激励**
            *   **头像**：一个卡通鹿形象。
            *   **激励语**："所有努力都在悄悄发光，别怕！"。
        *   **2.1.2. 当日日程安排**
            *   **日期与天气**：显示 "星期一 >" (暗示可切换日期) 及 "多云 17°C"。
            *   **日程列表**：
                *   数学: 8:00 - 8:45
                *   语文: 8:55 - 9:40
                *   改月考卷子: 10:55到期
                *   化学: 11:05 - 11:45
                *   生物: 14:00 - 14:45
                *   历史: 14:55 - 15:40
                *   体育: 16:05 - 16:55
                *   教室: 16:05 - 16:55
            *   **功能按钮**："添加日程"。

    *   **2.2. 中间栏：学习任务与工具**
        *   **2.2.1. 学科任务卡片**：以网格形式展示各学科的学习任务。
            *   **语文卡片**：
                *   标签："语文"，"进行中"
                *   标题："子路曾皙冉有公西华侍坐"
                *   任务信息："1任务 18:00到期"
            *   **数学卡片**：
                *   标签："数学"
                *   标题："1.1.3 集合的概念"
                *   任务信息："99+任务 下周日到期"
            *   **英语卡片**：
                *   标签："英语"
                *   标题："Unit 5: First Aid"
                *   任务信息："1任务 18:00到期"
            *   **物理卡片**：
                *   标签："物理"
                *   标题："安培力与洛伦兹力"
                *   任务信息："99+任务 10周到期"
            *   **化学卡片**：
                *   标签："化学"
                *   标题："化学反应速率与化学平衡"
                *   任务信息："1任务 18:00到期"
            *   **生物卡片**：
                *   标签："生物"
                *   标题："种群及其动态"
                *   任务信息："1任务 18:00到期"
        *   **2.2.2. 学习辅助工具快捷入口**：一行图标按钮。
            *   错题本
            *   查单词
            *   拓展练习
            *   笔记本
            *   背单词

    *   **2.3. 右侧栏：个性化推荐**
        *   **2.3.1. 模块标题**："给你的提升秘籍"，配有卡通鹿插图。
        *   **2.3.2. 推荐内容卡片**：
            *   **标题**："函数图像知识点拓展练习"
            *   **描述**："你的函数图像知识点好像还比较薄弱，推荐你这几道真题，深化数形结合思维，增强运用图像解决实际问题的意识。"
            *   **行动点**："去看看 >"
            *   **附加信息**："约15分钟"，"30" (带有金币图标，可能指积分或奖励)
        *   **2.3.3. 用户反馈按钮**："没兴趣"。

3.  **底部导航栏**
    *   包含三个主要功能模块入口：
        *   学习 (当前选中状态)
        *   成长
        *   我的

**三、 元素间关联**

*   左侧栏的“星期一”日程与中间栏的学科任务卡片可能存在关联，共同构成了用户的当日学习计划。
*   中间栏的“学习辅助工具”为完成学科任务提供支持。
*   右侧栏的“个性化推荐”基于用户的学习情况（如描述中提到的“函数图像知识点好像还比较薄弱”）提供针对性学习资源。
*   底部导航栏用于在APP的不同核心功能区之间切换，“学习”是当前界面的核心功能。

【============== 图片解析 END ==============】

![in_table_UCaRbRVzFoEaP7xXhmAcEKDynwc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_UCaRbRVzFoEaP7xXhmAcEKDynwc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片为一款教育应用的数学学习模块界面截图，展示了用户在“集合”相关章节的学习路径、进度及互动元素。

1.  **屏幕整体布局**
    *   **1.1. 顶部状态与导航栏**
        *   1.1.1. 左上角：当前系统时间 "7:35 Mon Jun 3"。
        *   1.1.2. 顶部中央：
            *   返回箭头 "←"。
            *   当前模块标题："数学"。
        *   1.1.3. 右上角：
            *   用户成就/任务信息："任务 7" (带有任务簿图标)。
            *   用户积分/星星："★ 722"。
            *   系统状态：网络信号图标，电量 "100%"。
    *   **1.2. 中心内容区域：学习路径图**
        *   该区域以一条蜿蜒的浅棕色路径串联起各个学习节点和章节标题。
        *   **1.2.1. 章节引导："1.1 集合的概念"** (位于路径旁)
            *   **学习节点："1.1.1 集合的概念"**
                *   状态显示：绿色圆形背景，内有 "100/100" 字样及绿色旗帜图标，表示已完成。
        *   **1.2.2. 章节引导："1.2 集合间的基本关系"** (位于路径旁)
            *   **学习节点："1.2.1 集合间的基本关系"**
                *   状态显示：绿色圆形背景，内有 "100/100" 字样及绿色旗帜图标，表示已完成。
            *   **学习节点："1.2.2 集合的关系问题"** (文本标注于路径旁)
        *   **1.2.3. 学习活动组 (较大图标节点):**
            *   **活动节点："课程学习"**
                *   图标：播放按钮样式。
                *   标签："任务" (位于节点右上角)。
                *   附加信息："约45分钟"。
            *   **活动节点："巩固练习"**
                *   图标：E字形列表样式。
                *   标签："任务" (位于节点右上角)。
            *   **活动节点："拓展练习"**
                *   图标：铅笔样式。
        *   **1.2.4. 章节引导："1.3 集合的运算"** (位于路径旁，在上述活动组之后)
            *   **学习节点："1.1.4 集合的概念"**
                *   状态显示：灰色圆形背景，内有 "0/100" 字样及灰色星星图标。
                *   关联信息："笔记 1"，"错题 3"。
            *   **学习节点："1.3.2 集合的补集运算"**
                *   状态显示：橙色进度环形，内有 "75/100" 字样及绿色星星图标，表示部分完成。
            *   **学习节点："1.3.3 运用图示法处理集合问题"**
                *   状态显示：灰色圆形背景，内有 "0/100" 字样及灰色星星图标。
            *   **学习节点："1.3.4 集合中的求参问题"**
                *   状态显示：灰色圆形背景，内有 "0/100" 字样及灰色星星图标。
            *   **学习节点："1.3.5 利用数学思想求集合问题"**
                *   状态显示：灰色圆形背景，内有 "0/100" 字样及灰色星星图标。
    *   **1.3. 左下角浮动信息：学习动态**
        *   实时统计："当前43人正在学"。
        *   动态列表：
            *   头像与文字："周到 完成课程学习-合集的三性质 8:02"。
            *   头像与文字："李思勉 完成巩固练习-合集的三性质 8:15"。
            *   头像与文字："孙叁 完成巩固练习-合集的概念 现在"。

【============== 图片解析 END ==============】

![in_table_HNgEbyNCroqG8oxPLZEcacB0n7b](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_HNgEbyNCroqG8oxPLZEcacB0n7b.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片为一款教育产品的用户界面截图，展示了“数学”学科的学习进度与任务列表。

**I. 页面顶部状态栏**
*   A. 左侧：显示当前时间与日期：“7:35 Mon Jun 3”。
*   B. 右侧：
    *   1.  用户统计信息：
        *   a.  “任务 7” (表示有7项任务)
        *   b.  “薄弱 1” (表示有1个薄弱点)
        *   c.  “星 722” (表示获得722颗星/积分)
    *   2.  系统状态：电量“100%”。

**II. 页面标题栏**
*   A. 左侧：“←” 返回图标。
*   B. 中间：当前学科标题“数学”。

**III. 主内容区域：学习路径图**
*   A. 整体：一条弯曲的路径，串联多个学习节点。
*   B. 路径上方标题：显示章节信息，如“1.1 集合的概念”。
*   C. 学习节点（沿路径分布）：
    *   1.  节点（已完成）：
        *   文本：“1.1.4 集合的概念”
        *   得分：“100/100”
        *   视觉：绿色圆形背景，带旗帜和星形图标。
    *   2.  节点（已完成）：
        *   文本：“1.1.4 集合的概念”
        *   得分：“100/100”
        *   视觉：绿色圆形背景，带旗帜和星形图标。
    *   3.  节点（部分完成）：
        *   文本：“1.1.4 集合的概念”
        *   得分：“75/100”
        *   视觉：橙色环形进度条，带星形图标。
    *   4.  当前活动节点（进行中/未开始）：
        *   文本：“1.1.4 集合的概念”
        *   得分：“0/100”
        *   子元素：“笔记 1”，“错题 3”
        *   关联学习活动：
            *   i.  “课程学习 45分钟”（紫色圆形，带“任务”标签）
            *   ii. “巩固练习”（蓝色圆形，带“任务”标签）
            *   iii.“拓展”（绿色圆形）
    *   5.  节点（未开始）：
        *   文本：“1.1.4 集合的概念”
        *   得分：“0/100” (根据上方0/100的显示判断)
        *   视觉：灰色圆形背景，带星形图标。
    *   6.  节点（薄弱点）：
        *   文本：“1.1.4 集合的概念”
        *   得分：“21/100”
        *   标签：“薄弱”
        *   视觉：红色环形进度条，带星形图标。
*   D. 独立任务元素（位于学习路径图右下方）：
    *   类型：“作业”
    *   名称：“集合的概念作业”
    *   标签：“任务”

**IV. 右侧弹窗：任务列表**
*   A. 顶部信息：用户头像图标和提示文字“剩余7个任务，加油！”。
*   B. 筛选按钮：“全部”。
*   C. 任务清单：
    *   1.  “课程：集合的概念课程 20:00到期”
    *   2.  “练习：集合的概念巩固练习 20:00到期”
    *   3.  “资源：集合课程补充视频 明日16:00到期”
    *   4.  “资源：实验演示1 5月16日16:00到期”
    *   5.  “作业：集合的概念作业 5月16日16:00到期”
    *   6.  “课程：集合的三性质 5月16日16:00到期”

【============== 图片解析 END ==============】

![in_table_N577bLqY5oTFMUxEqcpccsmGnHd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_N577bLqY5oTFMUxEqcpccsmGnHd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

### 1. 页面整体布局与状态信息
*   **顶部状态栏:**
    *   左上角：显示时间 `7:35` 和日期 `Mon Jun 3`。
    *   右上角：显示信号、Wi-Fi 和电池电量 `100%`。
*   **导航栏:**
    *   左侧：返回按钮 (左箭头图标) 及页面标题 `数学`。
    *   右上角：
        *   `任务` 图标及数量 `7`。
        *   `星级` 图标及数量 `743`。

### 2. 主要内容区域：学习路径地图
该区域通过弯曲虚线路径连接多个圆形节点，展示学习单元和进度。
*   **学习路径节点 (圆形图标，显示分数/总分及知识点名称):**
    *   节点 (左上，绿色旗帜图标): `100/100`，`1.1.4 集合的概念`。
    *   节点 (左中，绿色旗帜图标): `100/100`，`1.1.4 集合的概念`。
    *   节点 (中部略下，橙色进度环): `75/100`，`1.1.4 集合的概念`。
    *   节点 (右下，橙色进度环): `21/100`，`1.1.4 集合的概念`。
    *   节点 (右上，灰色空心圆): `0/100`，`1.1.4 集合的概念`。
    *   节点 (右上，灰色空心圆): `0/100`，`1.1.4 集合的概念`。
    *   节点 (右下，灰色空心圆): `0/100`，`1.1.4 集合的概念`。
*   **知识点章节标签 (文本标注于路径旁):**
    *   `1.1 集合的概念` (路径左上段)。
    *   `1.2 集合的概念` (路径右下段)。
*   **当前学习阶段模块 (位于路径中央的较大圆形交互元素):**
    *   `课程学习` (紫色背景，带白色勾选标记)。
    *   `巩固练习` (蓝色背景，带白色勾选标记)。
    *   `拓展练习` (绿色背景，带火箭图标，文字 `约20星`)。

### 3. 交互弹窗：“巩固练习”完成提示
居中显示，叠加在学习路径之上。
*   **弹窗标题:** `巩固练习` (左侧有绿色勾选图标)。
*   **获得奖励信息:** `已获20★` (星形图标)。
*   **操作按钮:**
    *   `看报告` (白色背景，灰色文字)。
    *   `再练一次` (绿色背景，白色文字)。
*   **关联信息标签 (弹窗下方):**
    *   `笔记 1`。
    *   `错题 3`。

### 4. 辅助信息区域
*   **左下角用户动态列表:**
    *   顶部提示：`当前43人正在学` (配有多个用户头像堆叠图标)。
    *   动态条目 (从上至下，包含用户头像、用户名、完成事项和时间):
        *   `子效远 完成课程学习-合集的三性质` (`7:58`)。
        *   `周到 完成课程学习-合集的三性质` (`8:02`)。
        *   `李思勉 完成巩固练习-合集的三性质` (`8:15`)。
        *   `孙垒 完成巩固练习-合集的概念` (`现在`)。
*   **右下角作业提示卡片:**
    *   卡片标题：`作业`。
    *   作业名称：`集合的概念作业`。
    *   状态标签：`任务` (蓝色背景)。

【============== 图片解析 END ==============】

![in_table_Co9ybh7b3o8bfHxkn6Wc5qOanrg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Co9ybh7b3o8bfHxkn6Wc5qOanrg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个UI界面元素，通常用于在线教育产品中，提示用户开始特定知识点的练习。

关键元素与组成结构如下：

1.  **背景层 (Background Layer)**
    *   主色调：浅橙色。
    *   视觉元素：平铺有多个浅色的、与学习相关的简笔图标（如电脑鼠标、音符、菱形块、尺子、闪电、披萨块、烧杯、信息分享等）。

2.  **内容卡片 (Content Card)**：位于前景，是视觉焦点。
    *   形状：圆角矩形。
    *   边框：较深的橙色。
    *   填充色：米白色/浅黄色。
    *   **右上角元素**：
        *   一枚蓝色的图钉图标。
    *   **文本信息 (Text Information)**：居中偏上排列。
        *   **行动指引文本 (Action Prompt Text)**：
            *   内容：“开始练习”
            *   样式：橙色字体，下方有手绘风格的橙色波浪线。
        *   **主题描述文本 (Topic Description Text)**：
            *   内容：“复数的概念”
            *   样式：黑色字体，字号大于“开始练习”。
    *   **右下角插画 (Illustration)**：
        *   一个卡通小鹿角色，身着蓝白相间的校服（带有徽章），手持一支黄色铅笔，面带微笑。

元素间关联：

*   **内容卡片**是信息传递的核心载体。
*   “**开始练习**”明确了该UI模块的功能，引导用户进行交互。
*   “**复数的概念**”指出了练习的具体学习内容主题。
*   **图钉图标**和**卡通小鹿**作为装饰性元素，增强了界面的视觉吸引力和亲和力，符合教育产品的用户体验设计。
*   **背景层**的教育相关图标暗示了产品所处的领域。

整体而言，该图片呈现了一个清晰的、引导用户针对“复数的概念”这一主题进行“开始练习”操作的入口界面。

【============== 图片解析 END ==============】

![in_table_ZPWxbu2yxo7DIoxk3tucfxO0nRg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZPWxbu2yxo7DIoxk3tucfxO0nRg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线教育产品的数学单选题答题界面。

1.  **顶部状态栏**
    *   左侧：
        *   返回按钮：“<” 图标。
        *   计时器：“用时 01:24”。
    *   中间：一个模糊的水平条状区域，可能是进度指示器或装饰。
    *   右侧：系统电量显示：“100%”。

2.  **主要内容区域**
    *   **左侧区域：题目展示与引导**
        *   **题目信息卡片**：
            *   题型与来源标签：“单选 (2024春·浙江期中)”。
            *   题干内容：
                “在四面体ABCD中，BCD为等边三角形，
                $$
                \angle ADB = \frac{\pi}{2}
                $$
                ，二面角B-AD-C的大小为a，则a的取值范围是（ ）”
        *   **引导互动元素**：
            *   卡通小鹿形象：手持哑铃，站在书本上。
            *   对话气泡：包含文字“让我们进入 练习空间几何～”。
    *   **右侧区域：选项列表**
        *   选项A:
        $$
        (0, \frac{\pi}{6}]
        $$
        *   选项B:
        $$
        (0, \frac{\pi}{6}]
        $$
        *   选项C:
        $$
        (0, \frac{\pi}{6}]
        $$
        *   选项D:
        $$
        (0, \frac{\pi}{6}]
        $$

3.  **底部操作栏**
    *   左侧按钮：“加入错题本”。
    *   右侧按钮：“继续”（黄色背景）。

【============== 图片解析 END ==============】

![in_table_IQG9beGLHoBodyxmxPrcDF7Dn3b](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_IQG9beGLHoBodyxmxPrcDF7Dn3b.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

- **页面整体结构：** 在线答题界面。
    - **顶部状态/导航栏：**
        - 左上角：返回按钮（`<` 图标）。
        - 中部：计时器，显示“用时 01:24”。
        - 中部下方：答题进度条（橙色部分表示已完成进度）。
        - 右上角：系统状态栏信息（时间 “7:35 Mon Jun 3”，网络状态，电池电量 “100%”）。
    - **题目区域：**
        - 题型及来源标签：“单选 (2024春·浙江期中)”。
        - 题干文本：
            “在四面体ABCD中，BCD为等边三角形，$\angle ADB = \frac{\pi}{2}$，二面角B-AD-C的大小为a，则a的取值范围是 ( )”
        - 辅助图片：一个四面体ABCD的示意图。其中，边BD用虚线表示。
            - 图片右下角：放大镜图标（表示可放大图片）。
    - **选项区域：**
        - 选项A：“选项过长展示效果选项过长展示效果选项过长展示效果选项过长展示效果”
        - 选项B：“$(0, \frac{\pi}{6}]$”
        - 选项C：“$(0, \frac{\pi}{6}]$”
        - 选项D：“$(0, \frac{\pi}{6}]$”
    - **底部操作栏：**
        - 左侧：“勾画”按钮（带铅笔图标）。
        - 右侧：“不确定”按钮。
        - 右侧：“提交”按钮（橙色背景）。

【============== 图片解析 END ==============】

![in_table_FsN1b2MTvoUUQwx6uwDc6eiMnFK](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FsN1b2MTvoUUQwx6uwDc6eiMnFK.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
这张图片展示了一个在线教育平台的数学题目练习界面截图。

1.  **界面顶部信息 (Interface Header Information)**
    *   **时间与状态 (Time and Status)**:
        *   显示时间 (Displayed Time): 7:35 Mon Jun 3
        *   手机电量 (Phone Battery): 100%
    *   **答题计时 (Timer)**:
        *   用时 (Time Spent): 01:24

2.  **题目内容区域 (Problem Content Area)**
    *   **2.1 题目属性 (Problem Attributes)**
        *   **题型 (Question Type)**: 单选 (Single Choice)
        *   **来源 (Source)**: (2024春·浙江期中) (Spring 2024 Zhejiang Midterm)
    *   **2.2 题干 (Problem Statement)**
        *   **文字描述 (Textual Description)**: 在四面体ABCD中，BCD为等边三角形ADB=pai/2，二面角B-AD-C的大小为a，则a的取值范围是 ( )
        *   **关键几何条件与求解目标 (Key Geometrical Conditions and Objective)**:
            *   几何体 (Solid): 四面体 ABCD (Tetrahedron ABCD)
            *   条件1: △BCD 为等边三角形 (Triangle BCD is equilateral)
            *   条件2: $$ \angle ADB = \frac{\pi}{2} $$ (Note: "pai" in image and OCR is interpreted as π)
            *   定义 (Definition): 二面角 B-AD-C 的大小为 $$a$$ (The measure of the dihedral angle B-AD-C is $$a$$)
            *   求解 (Objective): $$a$$ 的取值范围 (The range of values for $$a$$)
    *   **2.3 辅助图像 (Diagram)**
        *   **内容 (Content)**: 展示了一个四面体 ABCD 的三维线框示意图。
        *   **顶点 (Vertices)**: A, B, C, D
        *   **边 (Edges)**:
            *   AB, AC, AD, BC, CD 表现为实线。
            *   DB 表现为虚线 (通常表示被遮挡或不属于正面)。
    *   **2.4 选项 (Multiple Choice Options)**
        *   各选项均附带选择该选项的用户百分比。
        *   选项A: $$(0, \frac{\pi}{6}]$$ (选择比例: 10.8%)
        *   选项B: $$(0, \frac{\pi}{6}]$$ (选择比例: 50.2%) - 界面标记为正确答案 (Correct Answer)
        *   选项C: $$(0, \frac{\pi}{6}]$$ (选择比例: 20.9%) - 界面标记为错误选项 (Incorrect Answer)
        *   选项D: $$(0, \frac{\pi}{6}]$$ (选择比例: 18.1%)
        *   *注: 根据图片内各选项的视觉显示，其内容均为数学区间 $$(0, \frac{\pi}{6}]$$。提供的OCR文本将选项表述为 "{0, pai/6}"，这是一种集合表示法，与图片中清晰的区间括号 $$( $$ 和 $$ ] $$ 不符。尽管各选项在图片中显示内容相同，但其不同的用户选择比例以及正确/错误标记暗示原始题目中的选项应有所区别。此解析基于图片直接显示的内容。*
    *   **2.5 正确答案指示 (Correct Answer Indication)**
        *   **标识 (Indicator)**: 选项B被绿色背景和勾号标记，明确指示为正确答案。

3.  **题目解析区域 (Solution Explanation Area)**
    *   **内容 (Content)**: 该区域仅包含重复的占位文字“题目解析”，未提供实际的解题过程或说明。

4.  **界面底部操作按钮 (Interface Footer Buttons)**
    *   左侧图标: “显示” (一个编辑/笔形图标，可能用于切换解析显示或笔记功能)
    *   中间按钮: “加入错题本” (Add to error log)
    *   右侧按钮: “继续” (Continue)

【============== 图片解析 END ==============】

![in_table_RIPGb6P1boVigPxDJq2c9LuUnYK](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RIPGb6P1boVigPxDJq2c9LuUnYK.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片展示了一个在线学习或练习界面的UI设计，包含一个主界面和一个居中显示的模态弹窗。

1.  **主界面 (Main Interface)** - 背景模糊显示
    *   **顶部区域 (Top Area)**
        *   **左上角:**
            *   返回图标 (向左箭头)
            *   文本：“退出学习”
        *   **计时显示:** 文本：“用时 00:10”
    *   **题目内容区域 (Question Content Area)**
        *   **题型标签:** “单选”
        *   **题干文本:** “关于空间几何, 以下哪个描述是正确的?”
        *   **选项区:**
            *   **选项 A:** 包含文本 “A 打点计时器”
            *   **(选项 B):** (UI上为选项 B 预留了位置，但内容未显示)
            *   **选项 C:** 包含文本 “C 打点计时器”
            *   **(选项 D):** (UI上为选项 D 预留了位置，但内容未显示)
    *   **底部操作栏 (Bottom Action Bar)** (部分可见)
        *   **勾画按钮:** 文本：“勾画”
        *   **不确定按钮:** 文本：“不确定”
        *   **提交按钮:** 文本：“提交”

2.  **模态弹窗 (Modal Dialog)** - 居中显示，覆盖在主界面之上
    *   **标题文本:** “确定要退出练习吗?”
    *   **提示信息文本:** “进度已保存”
    *   **操作按钮区 (Action Button Area)**
        *   **确认退出按钮:** 文本：“确认退出”
        *   **继续练习按钮:** 文本：“继续练习” (此按钮有明显背景色，区别于“确认退出”按钮)

【============== 图片解析 END ==============】

![in_table_GEmEbgKgwoVf2lxw3V3clW90ndd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GEmEbgKgwoVf2lxw3V3clW90ndd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片为一个在线教育产品中的答题界面UI，其关键元素和组成部分层级化结构如下：

1.  **整体界面 (Online Quiz/Test Interface)**
    *   **顶部状态/导航栏**
        *   左上角：返回按钮 (`<`)
        *   导航栏中部：计时器，显示 "用时 01:24"
        *   导航栏右侧：答题进度条 (橙色条状)
        *   系统状态信息 (右上角，如时间 "7:35 Mon Jun 3", 信号, 电量 "100%")

    *   **题目区域**
        *   **题目信息**
            *   题型："单选"
            *   题目来源："(2024春·浙江期中)"
        *   **题干内容**
            *   文字描述： "在四面体ABCD中，BCD为等边三角形ADB=面角B-AD-C的大小为a，则a的取值范围是（）"
            *   几何图形：一个四面体ABCD的示意图。
                *   图形右下角：放大镜图标 (推测为图片缩放功能)
        *   **答案选项**
            *   **选项 A**: "选项过长展示效果选项过长展示效果选项过长展示效果选项过长展示效果"
            *   **选项 B**: 文字描述为 "(0, pai/6]"
                *   数学公式:
                    $$
                    [0, \frac{\pi}{6}]
                    $$
            *   **选项 C**: 文字描述为 "(0, pai/6]"
                *   数学公式:
                    $$
                    [0, \frac{\pi}{6}]
                    $$
            *   **选项 D**: 文字描述为 "(0, pai/6]"
                *   数学公式:
                    $$
                    [0, \frac{\pi}{6}]
                    $$
        *   **浮层提示/信息** (叠加在题目和选项C之间区域)
            *   文字信息1: "复数的概念"
            *   文字信息2: "约3题"
            *   视觉指示：一个橙色圆点，通过虚线连接到选项C附近，指示某个焦点或关联。

    *   **底部操作栏**
        *   左侧按钮："勾画" (带编辑图标)
        *   中间按钮："不确定"
        *   右侧按钮："提交" (黄色背景)

【============== 图片解析 END ==============】

![in_table_H0V7bUXxko2eY3xdYA4cRjKXnec](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_H0V7bUXxko2eY3xdYA4cRjKXnec.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线学习答题界面的截图，其关键元素和组成部分结构如下：

一、 **顶部导航栏**
    *   **左侧区域**:
        *   返回按钮图标及文字：“退出学习”
        *   计时信息：“用时 00:10”
    *   **右侧区域**:
        *   按钮：“学习地图”
        *   按钮：“反馈”

二、 **题目区域**
    *   题型标识：“单选”
    *   题目描述：“关于空间几何, 以下哪个描述是正确的?”
    *   **选项区域**:
        *   选项A：“打点计时器”
        *   选项B：“打点计时器”
        *   选项C：“打点计时器”
        *   选项D：“打点计时器”

三、 **底部工具栏 (悬浮)**
    *   **颜色选择器**:
        *   蓝色 (选中状态)
        *   橙色
        *   黑色
    *   **笔触样式/粗细选择器**:
        *   三个不同笔触/粗细的示意图标
    *   **功能按钮**:
        *   笔工具图标 (蓝色，选中状态)
        *   橡皮擦工具图标 (用斜杠表示)
        *   清除/删除工具图标 (垃圾桶形状)
        *   撤销操作图标
        *   重做操作图标
        *   操作按钮：“完成”

【============== 图片解析 END ==============】

![in_table_PgGRbjgxsogDxgxmJApcopqonKc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_PgGRbjgxsogDxgxmJApcopqonKc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片为一个在线教育平台的数学题目作答及反馈界面。

1.  **顶部信息区 (Top Information Area)**
    *   用时 (Time Spent): "01:24"

2.  **题目区域 (Question Area)**
    *   **题目标签 (Question Tag):** "单选 (2024春·浙江期中)"
    *   **题干 (Problem Statement):**
        *   文字描述: "在四面体ABCD中，BCD为等边三角形，∠ADB=π/2，二面角B-AD-C的大小为α，则α的取值范围是？"
        *   数学符号表达:
            $$
            \angle ADB = \frac{\pi}{2}
            $$
    *   **配图 (Accompanying Diagram):**
        *   一个四面体ABCD的线框图，顶点B和C在底部，A和D在顶部趋势，B、C、D构成底面一部分的视觉。

3.  **选项区域 (Options Area)**
    *   **选项A:** `(0, π/6]`
        *   选择比例: 10.8%
    *   **选项B:** `(0, π/3]`
        *   选择比例: 50.2%
        *   状态: 用户已选择，且为正确答案 (绿色背景和勾号标记)
    *   **选项C:** `(π/6, π/3]` (根据图片显示，OCR识别的"{0, π/6}"与图片不符，应为区间)
        *   选择比例: 20.9%
    *   **选项D:** `(π/3, π/2]` (根据图片显示，OCR识别的"{0, π/6}"与图片不符，应为区间)
        *   选择比例: 18.1%

4.  **反馈与解析区域 (Feedback and Analysis Area)**
    *   **反馈图标与文字 (Feedback Icon and Text):**
        *   一个卡通小鹿竖起大拇指的形象。
        *   文字: "太棒了！同学巩固得很牢固～"
    *   **正确答案提示 (Correct Answer Indication):** "正确答案：B"
    *   **题目解析文本框 (Problem Analysis Text Box):** 显示多行重复的 "题目解析" 字样作为占位符。

5.  **底部操作按钮区域 (Bottom Action Buttons Area)**
    *   **提示图标 (Hint Icon):** 左下角铅笔编辑图标，文字为“显示”(此处的“显示”可能为OCR识别错误或UI元素功能名称，实际图片中该文字部分被遮挡，仅能看到编辑图标)
    *   **加入错题本按钮 (Add to Error Log Button):** "加入错题本"
    *   **继续按钮 (Continue Button):** "继续" (橙色背景)

【============== 图片解析 END ==============】

![in_table_OmLGbk0H6ojvoQxgdt7cHNb0nGb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OmLGbk0H6ojvoQxgdt7cHNb0nGb.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

**1. 页面整体布局与元素**

该图片为一个在线答题结果反馈界面，主要包含以下几个区域：顶部状态栏、顶部导航栏、题目内容区、答案选项区、正确答案及解析区、底部操作按钮区。一个卡通小鹿形象的提示弹窗叠加在选项区域。

**2. 元素层级及关联**

*   **2.1. 顶部状态栏**
    *   左上角：时间 "7:35 Mon Jun 3"
    *   右上角：网络信号图标、电量 "100%"

*   **2.2. 顶部导航栏**
    *   左侧：返回图标 "<"
    *   中间：计时器 "用时01:24"
    *   下方：一个橙色的进度条，表示用时或答题进度。

*   **2.3. 题目内容区**
    *   题型及来源："判断题 (2024春·浙江期中)"
    *   题干描述："“跬步”, 古代称跨出一脚为“跬”, 跨出两脚为“步”, 也被用于形容极近的距离、数量极少等。"

*   **2.4. 答案选项区**
    *   **选项A**: 内容为 `A (0, pai/6]`
        *   数学公式表达：
        $$
        (0, \pi/6]
        $$
    *   **选项B**: 内容为 `B (0, pai/6]`，右侧显示选择比例 "50.2%" 和一个绿色勾选标记，表示此为正确选项。
        *   数学公式表达：
        $$
        (0, \pi/6]
        $$
    *   **选项C**: 内容为 `C (0, pai/6]`，背景高亮为红色，右侧显示选择比例 "18.1%"，表示此为用户选择的错误选项。
        *   数学公式表达：
        $$
        (0, \pi/6]
        $$
    *   **提示弹窗（叠加在选项B和C之间，偏向C）**:
        *   卡通小鹿形象
        *   文字提示："别气馁"
        *   文字提示："试着自己看看解析"

*   **2.5. 正确答案及解析区**
    *   正确答案标签："正确答案"
    *   正确答案内容："B"
    *   题目解析标题："题目解析"
    *   题目解析内容：由多行重复的 "题目解析" 文本构成，作为实际解析内容的占位符。

*   **2.6. 底部操作按钮区**
    *   左侧按钮（白色背景）："加入错题本"
    *   右侧按钮（橙色背景）："继续"

【============== 图片解析 END ==============】

![in_table_I2Dwb6LSwoNP4LxRXNzcleQHn6e](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_I2Dwb6LSwoNP4LxRXNzcleQHn6e.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

*   **整体描述**:
    *   此图为一个祝贺性的用户界面(UI)反馈元素，常见于在线教育产品中，用于表彰用户连续答对题目的成就。

*   **关键元素层级结构**:

    1.  **背景 (Background Layer)**
        *   颜色: 整体为粉橙色渐变。
        *   装饰图形: 包含模糊的几何形状（如左侧黄色方块、右上方蓝色和黄色星形、右下方紫色圆形）及斜向光影。

    2.  **内容区域 (Content Area)**
        *   形状: 一个具有圆角的、略微向右上方倾斜的横幅。
        *   颜色: 红色到粉色的渐变。
        *   内部设计: 左上角有白色小圆点。
        *   **文本信息 (Text Information)**
            *   **主标题**: "太棒了！"
                *   字体: 白色，较大。
                *   修饰: "太棒"二字下方有黄色横线。
            *   **副标题/描述**: "连对5题！实力派666"
                *   字体: 白色，较小，位于主标题下方。
        *   **卡通形象 (Mascot Character)**
            *   形象描述: 一只卡通小鹿，穿着白色上衣、蓝色裤子和红色领结。
            *   姿态: 右手竖起大拇指，左眼眨眼微笑。
            *   位置: 位于文本信息右侧。

【============== 图片解析 END ==============】

![in_table_JbUVbLSsdojkJzxKGGbcmN0Gnka](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_JbUVbLSsdojkJzxKGGbcmN0Gnka.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个UI界面截图，整体风格活泼，可能用于提示用户进入新阶段或接受挑战。其关键元素和组成部分如下：

1.  **状态栏区域 (顶部)**
    *   左上角：时间显示 "7:35 Mon Jun 3"
    *   右上角：
        *   Wi-Fi信号图标
        *   电池电量显示 "100%" 及电池图标

2.  **导航/操作区域 (状态栏下方)**
    *   左侧：返回箭头图标 "<"
    *   返回箭头右侧：文字 "用时01:24"

3.  **主内容区域 (中部)**
    *   **背景元素**:
        *   整体为浅色渐变背景。
        *   多条粉橙色斜向条纹贯穿背景。
        *   左下方有一个四角星闪烁图形。
        *   右上方有一个黄色小方块图形。
    *   **核心插画**:
        *   一个卡通小鹿角色，穿着蓝色短裤和带领结的白色衬衫，背着蓝色书包。
        *   小鹿骑在一个白、红、灰相间的卡通火箭上，火箭向右上方飞行。
    *   **主要文字信息 (插画下方)**:
        *   大号艺术字体："难度升级"，其右侧有一个橙色的向上箭头图标 "↑"。
    *   **次要文字信息 (主要文字下方)**:
        *   较小字体："迎接挑战吧！"

【============== 图片解析 END ==============】

![in_table_Lw0XbrQEEo4xbWx099Ucbz5Vn2b](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Lw0XbrQEEo4xbWx099Ucbz5Vn2b.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

这张UI图片展示了一个活动或任务完成后的结果反馈界面，可能应用于在线教育产品中的答题、竞赛或闯关等场景。

**1. 核心功能与信息层级：**

*   **1.1. 结果反馈 (首要信息):**
    *   **1.1.1. 排名变化:** "排名上升8位 ↑" - 以醒目字体居中展示，是用户最关心的核心成绩。
    *   **1.1.2. 积极评价:** "团队大腿！！" - 对用户表现的正面激励性评价，增强成就感。
*   **1.2. 辅助信息 (次要信息):**
    *   **1.2.1. 用时记录:** 左上角显示 "用时01:24"，记录了用户完成任务所花费的时间。
*   **1.3. 视觉与氛围营造:**
    *   **1.3.1. 中心插画:** 一只卡通小鹿骑在火箭上，寓意快速进步、成绩优异。
    *   **1.3.2. 背景设计:** 采用暖色调渐变背景，点缀有星星和几何图形，营造轻松愉快的氛围。

**2. 交互元素：**

*   **2.1. 返回按钮:** 左上角 "<" 图标，指示用户可以返回上一级页面。

**3. 元素间关联：**

*   "用时01:24" 是达成 "排名上升8位" 这一结果的过程性数据之一。
*   插画、"排名上升8位" 和 "团队大腿！！" 共同构成了对用户优异表现的祝贺和肯定。
*   返回按钮提供了离开当前结果页面的操作路径。

【============== 图片解析 END ==============】

![in_table_LTzNwdc9ehjdhwbltjEcan8rnDg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_LTzNwdc9ehjdhwbltjEcan8rnDg.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

此图片为一个用户练习流程图，详细描述了用户从开始练习到完成练习的各个环节及可能的分支路径。

**一、关键元素与组成部分层级化阐述：**

1.  **练习流程总览**: 分为初始状态、练习中状态、练习完成状态，并包含中途退出与继续练习的特殊路径。
    *   **初始状态**:
        *   `开始练习`: 用户发起操作，进入练习流程。
    *   **练习中状态**:
        *   **准备阶段**: `进入练习 (转场)` -> (可选)`题组(如有) (转场)`。
        *   **答题环节** (循环处理 `题目-1` 至 `题目组件-最后一题`):
            *   显示当前题目。
            *   用户作答后，判断 `作答是否正确`。
                *   **若正确**: 依次经过 `作答反馈 (正确)` -> `小组排名反馈` -> `难度反馈上升 (如有)` -> `不认真作答反馈`。
                *   **若错误**: 依次经过 `作答反馈 (错误)` -> `难度反馈下降 (如有)`。
            *   完成上述反馈后，若非最后一题，则进入下一题目；若是最后一题，则进入 `练习报告`。
    *   **练习完成状态**:
        *   `练习报告`: 展示练习结果。
        *   `再练一次` (可选): 用户选择后，返回 `开始练习`。
    *   **中途退出与继续**:
        *   `中途退出`: 用户在练习过程中主动中断。
        *   `继续练习`: 用户选择后，返回 `进入练习 (转场)` 阶段恢复练习。

**二、Mermaid 流程图描述：**

```mermaid
flowchart LR
    subgraph Init [初始状态]
        A[开始练习]
    end

    subgraph Practicing [练习中状态]
        B(进入练习 转场)
        C(题组(如有) 转场)
        
        Node_CurrentQuestion[当前题目<br>(题目-1 / 题目组件-2 / ... / 题目组件-最后一题)]
        Node_Decision{作答是否正确}
        
        subgraph Feedback_Correct [正确反馈路径]
            direction LR
            FC1(作答反馈 正确) --> FC2(小组排名反馈) --> FC3(难度反馈上升 如有) --> FC4(不认真作答反馈)
        end
        
        subgraph Feedback_Incorrect [错误反馈路径]
            direction LR
            FI1(作答反馈 错误) --> FI2(难度反馈下降 如有)
        end

        Node_NextStep{是最后一题?}

        MidExit[中途退出]
        ContinuePractice[继续练习]
    end

    subgraph Completed [练习完成状态]
        Report[练习报告]
        Retry[再练一次]
    end

    %% 主流程
    A --> B
    B --> C
    C --> Node_CurrentQuestion
    B -.-> Node_CurrentQuestion % 无题组时，直接进入题目

    %% 答题与反馈循环
    Node_CurrentQuestion --> Node_Decision
    Node_Decision -- 是 --> FC1
    FC4 --> Node_NextStep
    
    Node_Decision -- 否 --> FI1
    FI2 --> Node_NextStep

    %% 题目流转或结束
    Node_NextStep -- 否 (下一题) --> Node_CurrentQuestion
    Node_NextStep -- 是 (练习结束) --> Report
    
    %% 中途退出与继续流程
    %% (示意：可在答题各环节后退出)
    FC4 -.-> MidExit
    FI2 -.-> MidExit 
    Node_CurrentQuestion -.-> MidExit % 也可在题目加载后、作答前退出

    MidExit --> ContinuePractice
    ContinuePractice --> B

    %% 完成后流程
    Report --> Retry
    Retry --> A
```

【============== 图片解析 END ==============】

![in_table_WC4gb8MsSoqY17xOJ5ycXN3Tn2g](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WC4gb8MsSoqY17xOJ5ycXN3Tn2g.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片为互联网教育产品中的学习激励与结果反馈界面，其关键元素及组成结构如下：

**1. 屏幕顶部状态信息:**
    *   时间日期: "7:35 Mon Jun 3"

**2. 界面左侧激励模块:**
    *   **插画:**
        *   主体：卡通小鹿从礼盒中探出，手举奖杯。
    *   **横幅标语:**
        *   内容: "最多7个字展示" (提示性文字，表明此处可动态展示最多7个字的文本)
    *   **鼓励性文字:**
        *   内容: "你是怎么做到这么完美的!"

**3. 界面右侧学习数据与交互模块:**
    *   **A. 核心数据统计 (横向三栏):**
        *   **学习用时:**
            *   标签: "学习用时"
            *   数值: "45分钟"
        *   **答题数:**
            *   标签: "答题数"
            *   数值: "27题"
        *   **正确率:**
            *   标签: "正确率"
            *   数值: "75%"
    *   **B. 掌握度模块:**
        *   标题: "掌握度 ⓘ" (ⓘ 为信息图标)
        *   提升说明: "本次学习提升35%↑"
        *   进度条: 展示当前掌握程度，末端标有 "当前100%"
        *   链接: "答题详情 >"
    *   **C. 成长值模块:**
        *   标题: "成长值"
        *   获得说明: "本章节获得28颗✨" (28颗星，✨代表星星图标)
        *   数值与图标: "x28" (位于星星图标旁)
    *   **D. 课程感受反馈模块:**
        *   提问文本: "本节课感受如何?"
        *   反馈选项 (通过选择表情符号进行评价):
            *   极差
            *   较差
            *   一般
            *   满意
            *   很棒
    *   **E. 操作按钮区域:**
        *   按钮1: "再练一次" (白色背景按钮)
        *   按钮2: "完成" (橙红色背景按钮)

【============== 图片解析 END ==============】

![in_table_BRCsbahKeoSKN3xBspNcFlGHnRI](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BRCsbahKeoSKN3xBspNcFlGHnRI.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个移动应用界面，标题为“题目列表”，用于展示用户作答后的题目情况。

*   **页面全局元素：**
    *   **设备状态栏**（屏幕顶部）：
        *   时间显示：“7:35 Mon Jun 3”
        *   状态指示：“100%”（可能表示电量或信号）
    *   **应用导航栏**：
        *   返回按钮：位于左上角，图标为“<”。
        *   页面标题：“题目列表”。
        *   筛选控件：“仅看错题”，其左侧有一个圆形选择框。

*   **题目列表区域：**
    *   **题目项 1：**
        *   题号：“1”
        *   类型：“[单选]”
        *   题干：“关于空间几何，以下哪个描述是正确的？”
        *   作答状态：
            *   图标：绿色勾号 “✓”
            *   文本：“回答正确”
        *   操作：“查看解析 >”
    *   **题目项 2：**
        *   题号：“2”
        *   类型：“[单选]”
        *   题干：“关于空间几何，以下哪个描述是正确的？关于空间几何，以下哪个描述是正确的？关于空间几何，以下哪个描述是正确的？”
        *   作答状态：
            *   图标：红色叉号 “✕”
            *   文本：“回答错误”
        *   操作：“查看解析 >”
    *   **题目项 3：**
        *   题号：“2” (与上一题题号相同)
        *   类型：“[单选]”
        *   题干：“关于空间几何，以下哪个描述是正确的？关于空间几何，以下哪个描述是正确的？关于空间几何，以下哪个描述是正确的？关于空间几何，以下哪个描述是正确的？关于空间几何，以下哪个描述是正确的？”
        *   作答状态：
            *   图标：红色叉号 “✕”
            *   文本：“回答错误”
        *   操作：“查看解析 >” (此操作在OCR文本中未明确列出，但根据UI一致性及图片显示可推断存在，但为恪守“不要妄加推断”，此处依据OCR文本不列出。然而，观察图片，该项下方确实有“查看解析 >”字样，OCR可能遗漏了第三项的查看解析。鉴于指令8“根据ocr解析文本，结合图片还原图片原本的含义”，且图片清晰显示，此处应补全。经再次检查，OCR `--- OCR End ---`部分确实也遗漏了第三个“查看解析”，但综合图片内容和上下文一致性，应予补充。)
        *   操作：“查看解析 >” (此条根据图片内容显示, 且符合列表项一致性)

【============== 图片解析 END ==============】

![in_table_Q22sbx71moTZLtxxT56cTC0CnjK](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Q22sbx71moTZLtxxT56cTC0CnjK.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

**一、 页面整体结构： 数学学科学习界面**

*   **A. 顶部状态与导航栏**
    *   **A1. 左上角区域**
        *   A1.1. 时间显示： "7:35 Mon Jun 3"
    *   **A2. 页面标题与返回**
        *   A2.1. 返回图标 (向左箭头)
        *   A2.2. 页面标题："数学"
    *   **A3. 右上角区域**
        *   A3.1. Wifi图标
        *   A3.2. 电量显示："100%"
        *   A3.3. 任务提示："任务 7" (文字在紫色背景标签内)
        *   A3.4. 积分/成就显示：包含一个星星图标和数字 "743"

*   **B. 主要内容区域： 学习路径图**
    *   **B1. 学习路径背景**
        *   B1.1. 浅黄色弯曲路径，其上点缀有若干小星星图案。
    *   **B2. 路径上的学习节点/关卡** (多个，分布于B1路径上)
        *   _此类节点通常包含：进度环/图标、得分/总分(如 X/100)、节点编号与名称_
        *   B2.1. 已完成节点1 (左侧路径):
            *   图标：绿色圆环，内有旗帜和星标。
            *   进度/得分："100/100"
            *   节点信息："1.1.4 集合的概念"
        *   B2.2. 已完成节点2 (左侧路径，B2.1上方):
            *   图标：绿色圆环，内有旗帜和星标。
            *   进度/得分："100/100"
            *   节点信息："1.1.4 集合的概念"
        *   B2.3. 部分完成节点 (中央交互模块左侧):
            *   图标：橙色圆环，内有星标。
            *   进度/得分："75/100"
            *   节点信息："1.1.4 集合的概念"
        *   B2.4. 部分完成节点 (右下侧路径):
            *   图标：红色/橙红色圆环，内有星标。
            *   进度/得分："21/100"
            *   节点信息："1.1.4 集合的概念"
        *   B2.5. 未开始/初始状态节点1 (右上侧路径):
            *   图标：灰色圆环，内有星标。
            *   进度/得分："0/100"
            *   节点信息："1.1.4 集合的概念"
        *   B2.6. 未开始/初始状态节点2 (右上侧路径，B2.5上方):
            *   图标：灰色圆环，内有星标。
            *   进度/得分："0/100"
            *   节点信息："1.1.4 集合的概念"
        *   B2.7. 章节/阶段标题 (位于路径段旁边，非具体节点，用于标识路径上的不同学习阶段)
            *   "1.1 集合的概念" (位于左侧路径段旁)
            *   "1.2 集合的概念" (位于右下侧路径段旁)
    *   **B3. 当前学习活动交互模块 (居中显示，突出)**
        *   B3.1. **学习环节选项 (横向排列的三个圆形按钮)**
            *   B3.1.1. "课程学习" (紫色背景，带白色完成勾选标记)
            *   B3.1.2. "巩固练习" (蓝色背景，带白色完成勾选标记，当前与下方B3.2弹窗关联)
            *   B3.1.3. "拓展练习" (绿色背景，带白色火箭图标，下方有小字，部分可见“共30★”)
        *   B3.2. **"巩固练习" 详情弹窗 (位于B3.1.2下方，白色背景)**
            *   B3.2.1. 标题区域:
                *   左侧：绿色完成勾选标记与文字 "巩固练习"
                *   右侧：文字 "已获20★" (★为星星图标)
            *   B3.2.2. 操作按钮区域 (横向排列):
                *   左按钮："看报告" (白色背景，灰色文字)
                *   右按钮："再练一次" (绿色背景，白色文字)
        *   B3.3. **关联信息 (位于B3.2弹窗下方，背景区域)**
            *   "笔记 1"
            *   "错题 3"
    *   **B4. 辅助信息浮窗**
        *   B4.1. **左下角学习动态浮窗 (半透明背景)**
            *   B4.1.1. 顶部信息：包含多个小头像图标和文字 "当前43人正在学"
            *   B4.1.2. 动态列表 (滚动显示，每条包含头像、用户行为和时间):
                *   示例1: "周到 完成课程学习-合集的三性质 8:02"
                *   示例2: "李思勉 完成巩固练习-合集的三性质 8:15"
                *   示例3: "孙些 完成巩固练习-合集的概念 现在"
        *   B4.2. **右下角作业/任务提醒浮窗 (白色背景)**
            *   B4.2.1. 标签1: "作业" (灰色文字)
            *   B4.2.2. 标签2: "任务" (白色文字在紫色背景上)
            *   B4.2.3. 内容描述："集合的概念作业"

*   **元素间关联性：**
    *   整个界面是 "数学" 学科的学习主界面 (A2.2)。顶部栏 (A) 提供时间 (A1.1)、导航 (A2.1) 及用户成就 (A3.3, A3.4)。
    *   学习内容以游戏化路径 (B1) 串联起各个学习节点 (B2)。每个节点 (如B2.1) 代表一个知识点 (如"1.1.4 集合的概念") 及其学习状态 (如"100/100")。路径通过章节标题 (B2.7) 进行分段。
    *   中央的交互模块 (B3) 是当前学习的焦点。它展示了针对某一学习主题 (可能对应于附近的节点B2.3) 的不同学习环节："课程学习"(B3.1.1)、"巩固练习"(B3.1.2)、"拓展练习"(B3.1.3)。
    *   图中 "巩固练习" (B3.1.2) 被激活，下方弹窗 (B3.2) 显示其完成结果 ("已获20★") 并提供 "看报告" 或 "再练一次" 的操作。关联的 "笔记" 和 "错题" (B3.3) 为该学习环节提供辅助。
    *   左下角浮窗 (B4.1) 展示其他用户的学习动态，营造学习氛围。右下角浮窗 (B4.2) 提示待完成的 "集合的概念作业"。

【============== 图片解析 END ==============】



## 六、数据需求（待定）

#### 6.1 效果追踪

<table>
<tr>
<td>追踪维度<br/></td><td>具体指标<br/></td><td>说明<br/></td></tr>
<tr>
<td>练习参与率<br/></td><td>进入巩固练习的人数 / 完成 AI 课的人数<br/></td><td>衡量巩固练习功能的渗透率<br/></td></tr>
<tr>
<td>完成率<br/></td><td>完成所有题目的用户数 / 进入巩固练习人数<br/></td><td>衡量练习任务完成情况<br/></td></tr>
<tr>
<td>再练一次触发率<br/></td><td>触发再练一次的人数 / 完成巩固练习人数<br/></td><td>监控再练一次模块推送效果<br/></td></tr>
<tr>
<td>再练一次完成率<br/></td><td>完成再练一次人数 / 触发再练一次人数<br/></td><td>监控再练一次实际完成度<br/></td></tr>
<tr>
<td>正确率变化<br/></td><td>再练一次前后正确率提升幅度<br/></td><td>验证再练一次是否有效提升<br/></td></tr>
<tr>
<td>学习时长<br/></td><td>完成一次巩固练习所需平均时间<br/></td><td>辅助评估练习负担感<br/></td></tr>
</table>

#### 6.2 数据埋点

<table>
<tr>
<td>**页面/模块**<br/></td><td>**动作**<br/></td><td>**埋点名称**<br/></td><td>备注<br/></td></tr>
<tr>
<td>巩固练习入口<br/></td><td>点击<br/></td><td>consolidation_entry_click<br/></td><td>记录每次点击巩固练习入口<br/></td></tr>
<tr>
<td>作答行为<br/></td><td>提交答案<br/></td><td>exercise_answer_submit<br/></td><td>包括作答用时、作答内容<br/></td></tr>
<tr>
<td>退出确认<br/></td><td>点击退出<br/></td><td>exercise_exit_confirm_click<br/></td><td>退出确认弹窗曝光及选择<br/></td></tr>
<tr>
<td>勾画组件<br/></td><td>勾画/清除/撤回<br/></td><td>drawing_action<br/></td><td>记录勾画行为细节<br/></td></tr>
<tr>
<td>错题收藏<br/></td><td>添加/取消收藏<br/></td><td>mistakebook_toggle_click<br/></td><td>收藏或取消收藏错题<br/></td></tr>
<tr>
<td>作答反馈<br/></td><td>正确/错误反馈弹窗展示<br/></td><td>answer_feedback_show<br/></td><td>展示类型、文案<br/></td></tr>
<tr>
<td>连胜反馈<br/></td><td>连胜提示弹窗展示<br/></td><td>winning_streak_feedback_show<br/></td><td>连胜次数、提示内容<br/></td></tr>
<tr>
<td>难度变化<br/></td><td>难度上升/下降提示弹窗展示<br/></td><td>difficulty_change_feedback_show<br/></td><td>上升/下降类型<br/></td></tr>
<tr>
<td>学习报告页<br/></td><td>查看详情<br/></td><td>learning_report_view_detail<br/></td><td>点击查看题目作答详情<br/></td></tr>
<tr>
<td>推荐学习<br/></td><td>点击推荐入口<br/></td><td>recommended_learning_click<br/></td><td>推荐模块点击情况<br/></td></tr>
<tr>
<td>再练一次入口<br/></td><td>进入再练一次<br/></td><td>consolidation_extra_practice_entry_click<br/></td><td>触发再练一次点击<br/></td></tr>
</table>

## 七、a/b 实验需求

暂无

## 附：评审记录
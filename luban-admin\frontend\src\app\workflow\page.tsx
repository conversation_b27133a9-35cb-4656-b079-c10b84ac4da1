// @ts-nocheck
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import axios from 'axios';
import MainLayout from '@/components/layout/MainLayout';
import { API_BASE_URL } from '@/app/configs/api';

export default function WorkflowList() {
  const [workflows, setWorkflows] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [statusFilter, setStatusFilter] = useState('all');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState('');
  
  // 从API获取工作流数据
  useEffect(() => {
    const fetchWorkflows = async () => {
      setIsLoading(true);
      setError('');
      
      try {
        const response = await axios.get(`${API_BASE_URL}/documents/tasks`, {
          headers: {
            // 实际项目中应添加认证信息
            // 'Authorization': `Bearer ${token}`
          }
        });
        
        // 将后端数据转换为前端需要的格式
        const formattedTasks = response.data.map(task => ({
          id: task.id,
          title: task.name || '未命名任务',
          source: task.source || (task.result?.file_name ? '上传PDF' : '飞书文档'),
          status: task.status,
          progress: Math.round(task.progress * 100),
          createdAt: task.created_at
        }));
        
        setWorkflows(formattedTasks);
        setIsLoading(false);
      } catch (error) {
        console.error('获取工作流列表失败:', error);
        setError('获取数据失败，请刷新重试');
        setIsLoading(false);
      }
    };
    
    fetchWorkflows();
  }, []);
  
  // 过滤和排序工作流
  const filteredAndSortedWorkflows = workflows
    .filter(workflow => {
      // 根据搜索词过滤
      const matchesSearch = workflow.title.toLowerCase().includes(searchTerm.toLowerCase());
      
      // 根据状态过滤
      const matchesStatus = statusFilter === 'all' || workflow.status === statusFilter;
      
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      // 按照选择的字段排序
      if (sortBy === 'createdAt') {
        // 日期排序
        const dateA = new Date(a.createdAt);
        const dateB = new Date(b.createdAt);
        return sortOrder === 'asc' ? dateA - dateB : dateB - dateA;
      } else {
        // 标题排序
        const valueA = a.title.toLowerCase();
        const valueB = b.title.toLowerCase();
        return sortOrder === 'asc' 
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      }
    });
  
  // 状态标签颜色和文本
  const getStatusDetails = (status) => {
    switch(status) {
      case 'completed':
        return { text: '已完成', color: 'bg-green-100 text-green-800' };
      case 'processing':
      case 'pending':
      case 'generating':
        return { text: '处理中', color: 'bg-blue-100 text-blue-800' };
      case 'failed':
        return { text: '失败', color: 'bg-red-100 text-red-800' };
      default:
        return { text: '未知', color: 'bg-gray-100 text-gray-800' };
    }
  };
  
  // 切换排序方向
  const handleSortToggle = (field) => {
    if (sortBy === field) {
      // 如果已经按这个字段排序，则切换排序方向
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      // 如果是新字段，设置为默认降序
      setSortBy(field);
      setSortOrder('desc');
    }
  };
  
  return (
    <MainLayout>
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-5xl mx-auto">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold text-gray-900">工作流列表</h1>
            <Link 
              href="/workflow/new" 
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
            >
              创建新工作流
            </Link>
          </div>
          
          {/* 搜索和过滤 */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="relative rounded-md shadow-sm md:w-1/2">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="搜索工作流"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <div className="flex gap-2">
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="block pl-3 pr-10 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="all">所有状态</option>
                <option value="completed">已完成</option>
                <option value="processing">处理中</option>
                <option value="pending">等待中</option>
                <option value="generating">生成中</option>
                <option value="failed">失败</option>
              </select>
              
              <select
                value={`${sortBy}-${sortOrder}`}
                onChange={(e) => {
                  const [field, order] = e.target.value.split('-');
                  setSortBy(field);
                  setSortOrder(order);
                }}
                className="block pl-3 pr-10 py-2 text-base border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="createdAt-desc">最新创建</option>
                <option value="createdAt-asc">最早创建</option>
                <option value="title-asc">标题 A-Z</option>
                <option value="title-desc">标题 Z-A</option>
              </select>
            </div>
          </div>
          
          {/* 错误信息 */}
          {error && (
            <div className="mb-6 p-3 bg-red-50 text-red-600 rounded-md border border-red-200">
              {error}
            </div>
          )}
          
          {/* 加载中状态 */}
          {isLoading && (
            <div className="py-8 text-center">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
              <p className="mt-2 text-gray-500">加载工作流列表中...</p>
            </div>
          )}
          
          {/* 工作流列表 */}
          {!isLoading && (
            <div className="bg-white shadow overflow-hidden sm:rounded-md">
              <ul className="divide-y divide-gray-200">
                {filteredAndSortedWorkflows.length > 0 ? (
                  filteredAndSortedWorkflows.map((workflow) => {
                    const statusDetails = getStatusDetails(workflow.status);
                    const isProcessing = ['processing', 'pending', 'generating'].includes(workflow.status);
                    
                    return (
                      <li key={workflow.id}>
                        <Link href={`/workflow/${workflow.id}`} className="block hover:bg-gray-50">
                          <div className="px-4 py-4 sm:px-6">
                            <div className="flex items-center justify-between">
                              <div className="flex items-center">
                                <p className="text-sm font-medium text-blue-600 truncate">{workflow.title}</p>
                                <div className={`ml-2 px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${statusDetails.color}`}>
                                  {statusDetails.text}
                                </div>
                              </div>
                              <div className="ml-2 flex-shrink-0 flex">
                                <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                                  {workflow.source}
                                </p>
                              </div>
                            </div>
                            <div className="mt-2 sm:flex sm:justify-between">
                              <div className="sm:flex">
                                <p className="flex items-center text-sm text-gray-500">
                                  <svg className="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                  </svg>
                                  创建于 {new Date(workflow.createdAt).toLocaleString()}
                                </p>
                              </div>
                              
                              {isProcessing && (
                                <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                                  <div className="w-32 bg-gray-200 rounded-full h-2.5 mr-2">
                                    <div 
                                      className="bg-blue-600 h-2.5 rounded-full" 
                                      style={{ width: `${workflow.progress}%` }}
                                    ></div>
                                  </div>
                                  <p>{workflow.progress}%</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </Link>
                      </li>
                    );
                  })
                ) : (
                  <li className="px-4 py-8 text-center text-gray-500">
                    没有找到匹配的工作流。
                    <Link 
                      href="/workflow/new" 
                      className="ml-1 text-blue-600 hover:text-blue-800 font-medium"
                    >
                      创建一个新的？
                    </Link>
                  </li>
                )}
              </ul>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
} 
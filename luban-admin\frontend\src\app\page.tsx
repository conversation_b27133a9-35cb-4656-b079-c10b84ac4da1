'use client';

import Link from 'next/link';
import MainLayout from '@/components/layout/MainLayout';

const features = [
  {
    name: '智能文档解析',
    description: '精准识别原始PRD、进行PRD的AI化，保证信息的准确性和完整性。',
  },
  {
    name: '自动化技术设计生成',
    description: '基于PRD，一键生成高质量的技术设计文档、数据库设计、API接口定义文档。',
  },
  {
    name: '模版与规则',
    description: '每个阶段生成的文档都可以设定模版进行定制化，确保文档的一致性和风格。',
  },
];

export default function HomePage() {
  return (
    <MainLayout>
      <div className="flex flex-col items-center justify-center text-center flex-grow py-12 sm:py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl w-full">
          <div className="mb-10">
            <p className="text-2xl sm:text-3xl md:text-4xl font-semibold text-gray-500 tracking-wide leading-relaxed">
              银河智学研发
            </p>
          </div>
          <h2 className="text-5xl sm:text-6xl md:text-7xl font-extrabold tracking-tight mb-12 leading-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-emerald-600 to-teal-400">
              Technical Design Agent
            </span>
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-2xl mx-auto mb-12 mt-16 leading-relaxed">
            一键完成架构、数据结构、接口的设计，一气呵成，需求还原度90%
          </p>
          <Link
            href="/workflow/format-prd"
            className="inline-flex items-center justify-center px-10 py-4 bg-green-600 text-white text-xl font-semibold rounded-lg shadow-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-transform transform hover:scale-105 duration-300 ease-in-out group"
          >
            进入 Luban Design Agent
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 ml-3 transition-transform duration-300 ease-in-out group-hover:translate-x-1" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>

      <div className="py-12 sm:py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="lg:text-center">
            <h2 className="text-base text-green-600 font-semibold tracking-wide uppercase">核心能力</h2>
            <p className="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-700 sm:text-4xl">
              释放您的开发生产力
            </p>
          </div>

          <div className="mt-10">
            <dl className="space-y-10 md:space-y-0 md:grid md:grid-cols-3 md:gap-x-8 md:gap-y-10">
              {features.map((feature) => (
                <div key={feature.name} className="relative p-6 bg-white rounded-lg shadow-lg border border-gray-200 hover:shadow-xl transition-shadow duration-300">
                  <dt>
                    <p className="text-xl leading-6 font-bold text-gray-700">{feature.name}</p>
                  </dt>
                  <dd className="mt-2 text-base text-gray-600">
                    {feature.description}
                  </dd>
                </div>
              ))}
            </dl>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
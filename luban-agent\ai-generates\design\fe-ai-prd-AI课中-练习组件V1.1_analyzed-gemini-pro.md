# AI课中-练习组件 1.0 产品需求文档 (结构化输出, v1.1)

## 1. 需求背景与目标

### 1.1 需求目标
- 通过对AI课中练习组件的交互体验升级，包括优化进入练习的转场动效、题目间即时反馈机制，提升学生作答流畅度和情感反馈体验。
- 该需求上线后，预计用户课程平均完成时长缩短5%，练习题平均正确率提升3%，用户满意度提升至88%。

### 1.2 用户价值
- 对于学生用户，解决了练习切换生硬、反馈不及时、缺乏激励等问题，带来更流畅、更具沉浸感和成就感的作答体验。
- 对于教学/教研用户，优化后的练习组件能更好地收集学生作答数据，为后续迭代互动讲题、个性化推题等功能提供基础。

## 2. 功能范围

### 2.1 核心功能
#### **[核心功能点1]:** 练习组件进入与题目间转场交互优化

**用户价值与场景:** 提升用户在练习环节的过渡流畅性和仪式感，明确告知用户即将开始或继续练习，减少学习过程中的认知中断。

**功能详述与前端呈现:**
- **主要交互流程:**
    1.  **首次进入练习:** 用户完成前序课程内容（如文档组件学习）后，系统自动或用户手动进入练习组件。
        *   前端展示特定转场动效（如翻页效果）。
        *   动效伴随IP形象和提示文案（“开始练习 + {课程名称}”）。
        *   转场动效及文案展示时长2秒后，自动进入第一道题目。
    2.  **再次进入练习:** 用户中途退出课程后，再次进入且课程进度定位在练习组件时。
        *   前端展示特定转场动效（可与首次进入一致或有细微区分）。
        *   动效伴随IP形象和提示文案（“继续练习 + {课程名称}”）。
        *   转场动效及文案展示时长2秒后，自动进入上次作答的题目或第一道未作答题目。
    3.  **题目间转场与反馈:** 用户提交一道题目答案后。
        *   系统立即展示该题的作答反馈（正确/错误动画及提示音）。
        *   若有连对，触发连对激励动效。
        *   随后自动或用户点击“下一题”后，平滑过渡到下一道题目。
- **界面布局与元素:**
    *   **进入练习转场页:**
        *   全屏或主要内容区域覆盖。
        *   居中或显著位置展示IP形象动效。
        *   IP形象下方或旁边展示动态提示文案（如“开始练习”、“继续练习”和课程名称）。
        *   背景可采用与课程主题相关的视觉元素。
    *   **题目间反馈:**
        *   **正确反馈:** 界面出现醒目的“正确”标识（如绿色勾号动画），可伴随积极提示音和/或简短鼓励文案（如“太棒了！”）。
        *   **错误反馈:** 界面出现醒目的“错误”标识（如红色叉号动画），可伴随提示音和/或引导性文案（如“再想想哦！”）。
        *   **连对反馈:** 达到特定连对次数（如3连对、5连对）时，在题目反馈基础上叠加更强的视觉和听觉激励，如徽章、特殊音效、祝贺文案。
- **用户操作与反馈:**
    *   进入练习转场为自动播放，用户无需操作，系统在2秒后自动推进到题目。
    *   题目提交后，作答反馈（正确/错误/连对）为即时自动展示，无需用户操作。
- **数据展示与更新:**
    *   转场文案中的“{课程名称}”需动态获取并展示。
    *   连对次数由系统实时记录并用于触发相应反馈。

**优先级：** 高
**依赖关系：** 依赖课程信息获取、用户作答历史记录。

##### 用户场景与故事 (针对此核心功能点)

###### [场景1：学生首次进入练习环节]
作为一个 **学生用户**，我希望能够 **在完成一个知识点的学习后，平滑且有仪式感地进入练习环节**，这样我就可以 **清晰地感知到学习阶段的转换，并为接下来的练习做好心理准备**。目前的痛点是 **直接跳转到题目可能感觉突兀，缺乏过渡**，如果能够 **通过一个简短有趣的转场动画和提示告诉我“要开始练习了”，并显示课程名称**，我会感觉学习体验更连贯和友好。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 用户在AI课中完成当前文档组件的学习，系统自动或用户点击“进入练习”。
    * **界面变化:** 屏幕发生转场（例如，类似书本翻页的动画效果），覆盖当前文档内容。新的转场界面居中出现一个可爱的IP形象（例如，小AI助手），并播放简短的入场动画。
    * **即时反馈:** IP形象下方出现动态文字提示：“开始练习《{课程名称}》”。
2.  **用户操作2:** （无用户操作，系统自动进行）转场动画和提示文案持续显示2秒。
    * **界面变化:** 2秒后，转场界面消失（例如，平滑淡出或向一侧滑走），练习组件的第一道题目界面完整呈现。
    * **即时反馈:** 无额外反馈，直接进入答题状态。

**场景细节补充：**
* **前置条件:** 用户已完成当前课程的文档学习部分，且该课程配置了练习组件。
* **期望结果:** 用户清晰感知到练习环节的开始，转场流畅自然，无卡顿。
* **异常与边界情况:** 若课程名称过长，转场文案需能优雅换行或缩略显示；若转场资源加载失败，应有平稳的降级方案（如仅文字提示，无动画）。

**优先级：** 高
**依赖关系：** 课程信息服务。

**Mermaid图示 (核心功能点本身的流程图/状态图等):**
  ```mermaid
  flowchart TD
    A[用户完成文档学习] --> B{触发进入练习};
    B -- 是 --> C[显示转场动画（IP形象 + '开始练习 {课程名称}'）];
    C -- 持续2秒 --> D[转场动画消失];
    D --> E[加载并显示第一道练习题];
    E --> F[用户开始答题];
  ```

###### [场景2：学生中途退出后再次进入练习]
作为一个 **学生用户**，我希望能够 **在我中途退出AI课程后再次进入时，如果上次的进度是在练习题，系统能有一个明确的提示让我知道将继续之前的练习**，这样我就可以 **快速衔接之前的学习状态，而不会感到困惑**。目前的痛点是 **直接进入题目可能会让我忘记上次的练习背景**，如果能够 **通过一个简短的转场提示我“继续练习”，并显示课程名称**，我会感觉更贴心。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 用户打开AI课应用，选择之前未完成的课程，且系统记录其上次退出时正处于该课程的练习组件中。
    * **界面变化:** 屏幕发生转场（可与首次进入类似），IP形象出现。
    * **即时反馈:** IP形象下方出现动态文字提示：“继续练习《{课程名称}》”。
2.  **用户操作2:** （无用户操作，系统自动进行）转场动画和提示文案持续显示2秒。
    * **界面变化:** 2秒后，转场界面消失，练习组件加载并显示用户上次作答的题目或第一道未作答的题目。
    * **即时反馈:** 无额外反馈，直接进入答题状态，题目状态（如已作答选项、用时等）应恢复。

**场景细节补充：**
* **前置条件:** 用户之前学习过此课程，中途在练习组件退出，且系统保存了其学习进度。
* **期望结果:** 用户清晰感知到将继续之前的练习，转场流畅，学习进度正确恢复。
* **异常与边界情况:** 若进度恢复失败，应有友好提示并引导用户从练习开始；若课程名称获取失败，转场文案可简化为“继续练习”。

**优先级：** 高
**依赖关系：** 用户学习进度服务、课程信息服务。

**Mermaid图示 (核心功能点本身的流程图/状态图等):**
  ```mermaid
  flowchart TD
    A[用户打开应用并选择未完成课程] --> B{检查上次进度是否在练习组件};
    B -- 是 --> C[显示转场动画（IP形象 + '继续练习 {课程名称}'）];
    C -- 持续2秒 --> D[转场动画消失];
    D --> E[加载并显示上次作答题目或首个未答题目];
    E --> F[用户继续答题];
    B -- 否 --> G[按正常课程进度加载];
  ```

###### [场景3：学生提交答案后看到即时反馈]
作为一个 **学生用户**，我希望能够 **在提交每道练习题的答案后，立刻看到我的答案是对是错，并且如果我连续答对，能有一些鼓励**，这样我就可以 **及时了解自己的掌握情况，并保持学习的积极性**。目前的痛点是 **有时反馈不明显或延迟，缺乏激励感**，如果能够 **用清晰的动画和声音告诉我“答对了”或“答错了”，并且连续答对时有额外的祝贺效果**，我会更有动力。

**前端界面与交互关键步骤：**
1.  **用户操作1:** 用户在练习题界面选择答案后，点击“提交”按钮。
    * **界面变化:** “提交”按钮可能变为禁用状态或显示加载中。题目选项区域暂时不可操作。
    * **即时反馈:** 系统进行答案判断。
2.  **系统响应1 (答案正确):**
    * **界面变化:** 屏幕上出现明显的“正确”视觉反馈（例如，绿色对勾动画覆盖在题目区域，或选项变绿并出现对勾图标）。若触发连对（如3连对），则在基础正确反馈之上叠加更华丽的动画效果（如烟花、徽章闪耀）和祝贺文字（如“太棒了！已连对3题！”）。
    * **即时反馈:** 伴随积极的提示音效。
3.  **系统响应2 (答案错误):**
    * **界面变化:** 屏幕上出现明显的“错误”视觉反馈（例如，红色叉号动画，或错误选项标红，正确选项标绿）。
    * **即时反馈:** 伴随提示性的音效（区别于正确音效）。
4.  **用户操作2 (或系统自动):** 用户点击“下一题”按钮，或在短暂反馈展示后系统自动跳转。
    * **界面变化:** 当前题目及反馈消失，平滑过渡到下一道题目界面。
    * **即时反馈:** 无额外反馈，用户准备作答新题目。

**场景细节补充：**
* **前置条件:** 用户正在进行练习组件的答题。
* **期望结果:** 用户在提交答案后能立即获得清晰、生动的作答反馈和激励，反馈效果与作答结果（正确/错误/连对）匹配。
* **异常与边界情况:** 若提交后网络延迟，应有loading提示；若反馈资源加载失败，应有纯文字的正确/错误提示作为降级。连对反馈的触发条件（如具体连对次数）需明确。

**优先级：** 高
**依赖关系：** 题目答案校验服务、用户作答统计服务。

**Mermaid图示 (核心功能点本身的流程图/状态图等):**
  ```mermaid
  flowchart TD
    A[用户选择答案并点击提交] --> B{答案判断};
    B -- 正确 --> C[显示正确反馈动画与音效];
    C --> D{是否触发连对?};
    D -- 是 --> E[叠加显示连对激励动画与文案];
    D -- 否 --> F[等待进入下一题];
    B -- 错误 --> G[显示错误反馈动画与音效];
    G --> F;
    E --> F;
    F --> H[加载下一题];
  ```

#### **[核心功能点2]:** 练习组件内导航与辅助功能

**用户价值与场景:** 在练习过程中为用户提供必要的控制（退出、查看进度）和辅助工具（勾画、错题本、问一问），提升用户自主学习的便利性和学习效率。

**功能详述与前端呈现:**
- **主要交互流程:**
    1.  **顶部导航:** 练习界面顶部始终显示“退出”按钮、作答计时、作答进度。
        *   点击“退出”：弹出确认框，用户确认后退出练习组件，保存当前作答进度。
        *   作答计时：从进入题目开始独立计时，中途退出暂停，再次进入从暂停处继续。上限59:59。
        *   作答进度：以“X/Y”形式展示，分子根据推题策略动态变化（答对或无相似题时增加），分母为预估总题数。
    2.  **勾画功能:** 用户在题目区域可调用勾画工具进行标记。
        *   提供勾画、橡皮擦、清除、撤销、恢复等操作。
        *   勾画内容随题目存在，再次进入时可恢复（若技术支持）。
    3.  **错题本功能:** 用户可将任意题目加入或移出错题本。
        *   题目解析状态前，题目下方或操作栏提供“加入错题本”按钮。
        *   点击后状态切换为“已加入错题本”（或类似提示），再次点击可取消。
    4.  **答疑组件（问一问）:**
        *   题目解析状态前，右上角常驻“问一问”入口。
        *   用户可长按题目内容（文字/图片）选中特定部分，然后点击“问一问”图标，唤起答疑组件，并将选中内容作为提问上下文。
        *   直接点击“问一问”图标，也可唤起答疑组件进行自由提问。
- **界面布局与元素:**
    *   **顶部导航栏:**
        *   左侧：“退出”按钮（图标或文字）。
        *   中间或右侧：作答计时（格式 00:00）、作答进度（格式 X/Y）。
    *   **题目区域:**
        *   题干、选项。
    *   **底部或侧边操作栏 (根据整体设计风格调整):**
        *   “勾画”工具入口图标。点击后可展开勾画工具条（包含画笔、橡皮、颜色选择、撤销/恢复、清除等子图标）。
        *   “加入错题本”/“已加入错题本”按钮（图标+文字）。
    *   **右上角固定入口:**
        *   “问一问”图标。
    *   **长按选中菜单 (唤起问一问时):**
        *   在选中文字/图片附近出现“问一问”的快捷操作图标或菜单项。
- **用户操作与反馈:**
    *   点击“退出”：弹出二次确认弹窗（“确认退出吗？将保存您的进度”），按钮“确认退出”、“继续作答”。确认后返回课程大纲或上一级。
    *   勾画操作：选择工具后即可在题目区域操作，勾画轨迹实时显示。
    *   点击“加入错题本”：按钮状态变化，toast提示“已加入错题本”或“已取消收藏”。
    *   长按选中内容后点击“问一问”：答疑组件侧滑或弹窗出现，选中内容自动带入提问框。
- **数据展示与更新:**
    *   作答计时实时更新。
    *   作答进度在题目切换或特定逻辑触发后更新。
    *   “加入错题本”按钮状态根据题目收藏状态动态显示。

**优先级：** 高
**依赖关系：** 用户作答数据服务、错题本服务、答疑组件。

##### 用户场景与故事 (针对此核心功能点)

###### [场景4：学生在答题时需要标记题目或临时退出]
作为一个 **学生用户**，我希望能够 **在答题过程中，如果遇到一道难题想先跳过，或者临时有事需要退出，我的答题进度和标记（如勾画）都能被保存**，这样我就可以 **灵活安排我的学习时间，不用担心数据丢失**。目前的痛点是 **有时退出后进度丢失，或者无法方便地标记重点**，如果能够 **提供清晰的退出按钮并自动保存进度，同时有勾画工具让我随手标记**，那就太好了。

**前端界面与交互关键步骤：**
1.  **用户操作1 (勾画标记):** 用户在查看某道题目时，觉得某个关键词或图形很重要，点击题目下方的“勾画”图标。
    * **界面变化:** 出现勾画工具条（画笔、橡皮擦、颜色等）。用户选择画笔和颜色。
    * **即时反馈:** 用户在题目区域拖动画笔，实时显示勾画痕迹。
2.  **用户操作2 (临时退出):** 用户完成勾画后，需要临时退出练习，点击界面顶部的“退出”按钮。
    * **界面变化:** 屏幕中央弹出确认对话框，提示：“确认退出吗？将保存您的进度”。对话框包含“确认退出”和“继续作答”两个按钮。
    * **即时反馈:** 练习计时暂停。
3.  **用户操作3:** 用户点击“确认退出”按钮。
    * **界面变化:** 练习组件界面关闭，返回到课程大纲或其他上一级页面。
    * **即时反馈:** 系统在后台保存当前练习的作答进度、用时、勾画内容（如果支持）。

**场景细节补充：**
* **前置条件:** 用户正在进行练习组件的答题。
* **期望结果:** 用户可以方便地使用勾画工具，并且退出时进度和标记能够被成功保存，再次进入时可恢复。
* **异常与边界情况:** 若勾画内容过大，保存时应有性能考量；若保存失败，应有提示。退出确认时，若用户误点，可通过“继续作答”返回。

**优先级：** 高
**依赖关系：** 用户学习进度服务。

**Mermaid图示 (核心功能点本身的流程图/状态图等):**
  ```mermaid
  flowchart TD
    A[用户在答题界面] --> B{需要标记?};
    B -- 是 --> C[点击“勾画”图标];
    C --> D[显示勾画工具条];
    D --> E[用户进行勾画操作];
    E --> A;
    B -- 否 --> F{需要退出?};
    F -- 是 --> G[点击“退出”按钮];
    G --> H[弹出退出确认框（含保存提示）];
    H --> I{用户选择?};
    I -- 确认退出 --> J[保存进度与勾画];
    J --> K[关闭练习组件，返回上级页面];
    I -- 继续作答 --> A;
    F -- 否 --> A; %% 继续当前操作或答题
  ```

###### [场景5：学生希望将难题加入错题本或针对题目提问]
作为一个 **学生用户**，我希望能够 **对于做错的题目或者觉得有价值的题目，方便地将它们加入到我的错题本中，并且如果我对题目本身有疑问，可以直接提问**，这样我就可以 **高效地管理我的薄弱点，并及时解决疑问**。目前的痛点是 **收藏错题不方便，或者提问流程繁琐**，如果能够 **在每道题旁边提供“加入错题本”按钮，并且通过“问一问”功能快速发起提问**，学习效率会高很多。

**前端界面与交互关键步骤：**
1.  **用户操作1 (加入错题本):** 用户在查看一道题目（无论是否已作答）时，觉得这道题有必要记录，点击题目下方的“加入错题本”按钮。
    * **界面变化:** “加入错题本”按钮的文本/图标变为“已加入”（或类似已收藏状态），按钮可能变灰或有其他视觉区分。
    * **即时反馈:** 屏幕可能会短暂toast提示“已加入错题本”。
2.  **用户操作2 (针对题目提问):** 用户对当前题目题干的某部分有疑问，长按选中该部分文字。
    * **界面变化:** 选中文字高亮，并在选中区域附近出现一个快捷操作菜单，其中包含“问一问”图标/文字。
    * **即时反馈:** 无。
3.  **用户操作3:** 用户点击快捷操作菜单中的“问一问”。
    * **界面变化:** 答疑组件（通常为侧边栏或弹窗）从屏幕边缘滑出或弹出，之前选中的题目内容自动填充到答疑组件的提问输入框中作为上下文。
    * **即时反馈:** 用户可以在答疑组件中编辑或直接发送已填充的问题。

**场景细节补充：**
* **前置条件:** 用户正在练习组件中查看题目。
* **期望结果:** 用户可以一键收藏/取消收藏错题，并能方便地针对特定题目内容发起提问。
* **异常与边界情况:** 若错题本已满（如有此限制），应有提示；若网络问题导致收藏失败，应有重试机制或提示。长按选词提问时，若选中内容过长，应能合理截取或提示。

**优先级：** 高
**依赖关系：** 错题本服务、答疑组件服务。

**Mermaid图示 (核心功能点本身的流程图/状态图等):**
  ```mermaid
  flowchart TD
    A[用户在答题界面] --> B{需要收藏错题?};
    B -- 是 --> C[点击“加入错题本”按钮];
    C --> D[按钮状态变为“已加入”];
    D --> E[Toast提示“已加入错题本”];
    E --> A;
    B -- 否 --> F{对题目有疑问?};
    F -- 是 --> G[长按选中题目内容];
    G --> H[显示“问一问”快捷操作];
    H --> I[点击“问一问”];
    I --> J[打开答疑组件（带入选中内容）];
    J --> K[用户在答疑组件提问];
    K --> A;
    F -- 否 --> A; %% 继续当前操作或答题
  ```

### 2.2 辅助功能
- **[作答计时器]:** 记录每道题的作答时长及整体练习用时。
    - **功能详述与前端呈现:** 界面顶部显示格式为 `MM:SS` 的计时器。单题独立计时，退出暂停，进入继续。整体用时可在练习总结页展示。
- **[作答进度展示]:** 显示当前已答题目与预估总题目数的比例。
    - **功能详述与前端呈现:** 界面顶部显示格式为 `X/Y` 的进度。X根据推题策略动态更新，Y为预估总题数。

### 2.3 非本期功能
- **[互动讲题模块]:** 基于题目分布进行自动化讲解和互动问答，此功能计划在未来版本中引入，以进一步提升课中练习体验与教学效果。
- **[难度变化实时反馈]:** 根据用户作答情况动态调整题目难度，并在难度变化时给予用户明确提示，此功能暂不包含在本期。
- **[不认真作答提示]:** 监测用户作答行为（如过快提交、随机选择等），在识别到不认真作答时给予提醒，此功能暂不包含在本期。

## 3. 业务流程图 (可选，用于整体业务流)

```mermaid
graph TD
    A[用户进入课程] --> B{是否首次进入练习?};
    B -- 是 --> C[播放“开始练习”转场动画];
    B -- 否 --> D[播放“继续练习”转场动画];
    C --> E[进入第一道题];
    D --> F[进入上次作答题目/首未答题];
    E --> G[用户答题界面];
    F --> G;
    subgraph "用户答题界面交互"
        direction LR
        G_Nav[顶部导航（退出/计时/进度）]
        G_Content[题目内容展示（题干/选项）]
        G_Actions[操作栏（提交/勾画/错题本/问一问）]
        G --> G_Content;
        G_Nav --> G;
        G_Content --> G_Actions;
    end
    G_Actions -- 点击提交 --> H{答案判断};
    H -- 正确 --> I[显示正确反馈（含连对）];
    H -- 错误 --> J[显示错误反馈];
    I --> K{是否还有下一题?};
    J --> K;
    K -- 是 --> L[加载下一题];
    L --> G;
    K -- 否 --> M[练习结束，显示总结页（本期不做）或返回课程];
    G_Actions -- 点击勾画 --> G_Draw[用户进行勾画];
    G_Draw --> G_Actions;
    G_Actions -- 点击错题本 --> G_Mistake[加入/取消错题本];
    G_Mistake --> G_Actions;
    G_Actions -- 点击问一问 --> G_QA[打开答疑组件];
    G_QA --> G_Actions;
    G_Nav -- 点击退出 --> N[确认退出弹窗];
    N -- 确认 --> O[保存进度并退出练习];
```

## 4. 性能与安全需求

### 4.1 性能需求
-   **响应时间：**
    -   进入练习转场动画加载完成时间应在 1 秒内。
    -   题目内容（题干、选项）加载完成时间应在 500 毫秒内 (95th percentile)。
    -   提交答案后，作答反馈（正确/错误）的显示延迟应小于 300 毫秒。
    -   勾画操作响应流畅，无明显卡顿。
-   **前端性能感知：**
    -   转场动画播放流畅，无掉帧。
    -   题目切换平滑，避免白屏或长时间loading。
    -   对于包含大量图片或复杂公式的题目，应考虑懒加载或分步加载策略，确保首屏题目内容快速呈现。

### 4.2 安全需求
-   **数据传输：** 用户答案、作答时长、错题本记录等个人学习数据在前后端传输过程中必须使用 HTTPS 加密。
-   **本地存储：** 避免在前端本地（如localStorage）明文存储用户答案等敏感信息。若需缓存进度，应进行适当加密或只存储非敏感标识。
-   **输入校验：** “问一问”功能若允许用户输入，需对输入内容进行基础校验，防止XSS等常见注入风险。

## 5. 验收标准

### 5.1 功能验收
-   **[核心功能点1：练习组件进入与题目间转场交互优化]：**
    -   **用户场景1 (首次进入练习)：**
        -   **界面与交互：** 成功完成文档学习后进入练习，前端正确显示“开始练习”转场动画（含IP形象、课程名称），2秒后自动进入第一题。
        -   **期望结果：** 转场动画播放流畅，课程名称显示正确。
    -   **用户场景2 (再次进入练习)：**
        -   **界面与交互：** 用户中途退出后再次进入练习组件，前端正确显示“继续练习”转场动画（含IP形象、课程名称），2秒后自动进入上次作答题目或首个未答题，计时和进度恢复。
        -   **期望结果：** 转场动画播放流畅，课程名称及学习进度恢复正确。
    -   **用户场景3 (提交答案后即时反馈)：**
        -   **界面与交互：** 提交答案后，立即显示对应的正确/错误动画和音效。若触发连对，额外显示连对激励效果。点击“下一题”或自动跳转后平滑过渡。
        -   **期望结果：** 反馈及时准确，动画效果符合设计，连对逻辑正确。
-   **[核心功能点2：练习组件内导航与辅助功能]：**
    -   **用户场景4 (标记与退出)：**
        -   **界面与交互：** 勾画工具可用，勾画内容实时显示。点击退出按钮，弹出确认框，确认后保存进度并成功退出。再次进入时勾画内容可见（若支持）。
        -   **期望结果：** 勾画功能正常，退出及进度保存符合预期。
    -   **用户场景5 (错题本与提问)：**
        -   **界面与交互：** “加入错题本”按钮状态能正确切换，并toast提示。长按选中题目内容后，能成功唤起“问一问”并将选中内容带入。
        -   **期望结果：** 错题本功能正常，问一问能带入上下文并发起提问。
    -   **顶部导航功能：**
        -   **界面与交互：** 退出按钮功能正常。作答计时在进入题目后启动，退出暂停，进入继续，上限59:59。作答进度按X/Y格式显示，分子根据策略更新。
        -   **期望结果：** 导航栏各项功能符合描述。

### 5.2 性能验收
-   **响应时间：**
    -   **测试用例1:** 模拟普通WiFi及4G网络，首次进入练习组件，转场动画开始渲染时间 ≤ 1秒。
    -   **测试用例2:** 连续作答10道题目，每道题目内容（不含复杂图片）加载完成时间95% ≤ 500ms。
    -   **测试用例3:** 对答案提交操作进行20次测试，作答反馈动画出现延迟95% ≤ 300ms。
-   **前端性能感知验收：**
    -   **测试用例1 (动画流畅度):** 在中低端测试机型上，进入练习转场动画、题目反馈动画播放过程中无肉眼可见卡顿或掉帧。
    -   **测试用例2 (资源加载):** 包含大图的题目，在图片加载完成前，题目文字部分应优先显示，图片区域可有占位符或loading。

### 5.3 安全验收
-   **数据传输：**
    -   **测试用例1:** 使用网络抓包工具检查，用户提交答案、同步错题本等操作的API请求均通过HTTPS协议。
-   **本地存储：**
    -   **测试用例1:** 检查浏览器localStorage/sessionStorage等，确认无明文存储的学生答案或可直接利用的身份信息。
-   **输入校验：**
    -   **测试用例1 (XSS in Q&A):** 若“问一问”功能支持用户输入，尝试输入常见XSS payload，如 `<script>alert('xss')</script>`，提交后查看是否被执行。期望结果：脚本未执行，输入被转义或过滤。

## 6. 其他需求

### 6.1 可用性与体验需求
-   **界面友好与直观性：**
    -   所有按钮和可交互元素的用途清晰易懂，图标表意明确。
    -   转场动画、反馈动画不应过于花哨以致分散用户注意力，时长控制合理。
    -   字体大小、颜色对比度在不同光照条件下易于阅读。
-   **容错处理：**
    -   当网络异常导致题目加载失败或答案提交失败时，应有明确的错误提示（如“网络不给力，请稍后重试”）和用户可操作的重试按钮。
    -   若转场动画资源加载失败，应优雅降级为静态图片或纯文字提示，不影响核心流程。
-   **兼容性：**
    -   **浏览器：** （若为Web应用）需支持最新版本的 Chrome, Safari, Firefox, Edge。
    -   **响应式布局：** (若需支持多端)
        *   **移动端 (<768px):** 保证单手操作便捷性，按钮点击区域足够大，题干和选项内容清晰展示不拥挤。
        *   **平板端 (≥768px):** 可适当调整布局，如将操作按钮集中或利用更宽的屏幕空间。
-   **可访问性 (A11y)：**
    *   核心交互元素（如选项、提交按钮、退出按钮、问一问入口）应支持键盘操作（Tab聚焦，Enter/Space激活）。
    *   对错反馈的动画效果，应有对应的ARIA live region或非视觉方式（如清晰音效）告知屏幕阅读器用户。
    *   IP形象、重要图标等应提供替代文本。
-   **交互一致性：**
    *   练习组件内的“退出”逻辑与课程框架其他部分的“退出”逻辑应保持一致。
    *   Toast提示的样式和位置应在产品内保持统一。

### 6.2 维护性需求
-   **配置管理：**
    *   转场动画的IP形象、文案模板（如“开始练习{课程名称}”）中的变量部分应易于配置或通过接口获取。
    *   连对N次的具体数值N应可配置。
    *   作答反馈的动画、音效资源应可方便替换和管理。
-   **监控告警：**
    *   前端需上报JS错误、API请求异常（如提交答案失败、获取题目失败）。
    *   对核心功能（如题目加载成功率、答案提交成功率）设置监控，并在指标异常时告警。
-   **日志管理：**
    *   记录用户在练习组件内的关键行为路径（如进入练习、加载题目、提交答案、使用勾画、加入错题本、发起提问、退出练习）。
    *   记录每道题的作答结果、用时、是否勾画、是否加入错题本等。

## 7. 用户反馈和迭代计划

### 7.1 用户反馈机制
-   （本期练习组件内暂不单独设置反馈入口，依赖课程整体的反馈渠道）用户可以通过课程结算页的反馈入口或App/平台内的通用反馈渠道，提交关于练习组件体验的问题和建议。
-   运营/产品团队定期收集用户反馈中关于练习部分的意见，作为后续迭代的重要输入。

### 7.2 迭代计划
-   **迭代周期：** 遵循整体AI课的版本迭代周期（例如，每2-4周）。
-   **迭代流程：** 需求分析 -> UI/UX设计 -> 开发与联调 -> 测试 -> 发布上线 -> 数据跟踪与用户反馈收集 -> 下一轮迭代规划。
-   **优先级调整：** 根据上线后练习组件的使用数据（如完成率、平均用时、正确率、辅助功能使用率）和用户反馈，结合业务目标，调整后续优化点（如互动讲题、智能难度调节等）的优先级。

## 8. 需求检查清单

| 原始需求 (来自原始PRD的详细条目) | 对应优化后需求点/章节 (在本PRD中) | 完整性 | 正确性 | 一致性 | 可验证性 (是否有验收标准) | 可跟踪性 (是否有优先级/依赖) | UI/UX明确性 | 备注 |
| -------------------------------- | ----------------------------------- | ------ | ------ | ------ | ------------------------- | --------------------------- | ----------- | ---- |
| 进入练习时老师在但内容侧滑进来有题的转场 | [2.1 核心功能点1], [场景1], [场景2] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         | 原始描述被理解为IP形象的转场 |
| 老师说完题和老师滑走，然后进入题目 | [2.1 核心功能点1], [场景1], [场景2] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         | 同上，理解为转场结束进入题目 |
| 顶部导航栏：退出按钮逻辑同课程框架 | [2.1 核心功能点2], [场景4] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         |      |
| 顶部导航栏：作答计时（每题独立，退出暂停，进入继续，上限59:59） | [2.1 核心功能点2], [2.2 辅助功能] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         |      |
| 顶部导航栏：作答进度（分子按策略涨，分母预估） | [2.1 核心功能点2], [2.2 辅助功能] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         |      |
| 连对触发效果：同巩固练习 | [2.1 核心功能点1], [场景3] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         | 假设“巩固练习”有类似定义 |
| 题型组件：各题型在练习中的展示和交互 | （本PRD不详细展开各题型内部，关注练习组件框架） | N/A    | N/A    | N/A    | N/A                       | N/A                         | N/A         | 依赖具体题型PRD |
| 题目推荐：见课中题目推荐策略 | （本PRD不涉及推题策略本身） | N/A    | N/A    | N/A    | N/A                       | N/A                         | N/A         | 依赖推题策略PRD |
| 勾画组件：同巩固练习 | [2.1 核心功能点2], [场景4] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         | 假设“巩固练习”有类似定义 |
| 错题本组件：同巩固练习 | [2.1 核心功能点2], [场景5] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         | 假设“巩固练习”有类似定义 |
| 答疑组件入口：右上角常驻“问一问”，题目解析前屏蔽 | [2.1 核心功能点2], [场景5] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         |      |
| 答疑组件交互：长按题目内容选中唤起问一问 | [2.1 核心功能点2], [场景5] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         |      |
| 首次进入练习转场（翻页、IP动效+文案“开始练习+课程名称”，2s） | [2.1 核心功能点1], [场景1] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         |      |
| 再次进入练习转场（IP动效+文案“继续练习+课程名称”，2s） | [2.1 核心功能点1], [场景2] | ✅    | ✅    | ✅    | ✅                       | ✅                         | ✅         |      |
| 数据存储：学生选择的答题选项（内容，是否确定） | [4.2 安全需求], [6.2 日志管理] | ✅    | ✅    | ✅    | ⚠️ (验收需确认具体存储方案) | ✅                         | N/A        | 主要为后端需求，前端配合 |
| 数据存储：每题作答时长 | [6.2 日志管理] | ✅    | ✅    | ✅    | ⚠️ (同上) | ✅                         | N/A        | 同上 |
| 数据存储：每题作答正确/错误标识 | [6.2 日志管理] | ✅    | ✅    | ✅    | ⚠️ (同上) | ✅                         | N/A        | 同上 |
| 数据存储：连续正确次数 | [2.1 核心功能点1-数据], [6.2 日志管理] | ✅    | ✅    | ✅    | ⚠️ (同上) | ✅                         | N/A        | 同上 |
| 数据存储：是否触发难度上升/下降 | (本期不做此功能) | N/A    | N/A    | N/A    | N/A                       | N/A                         | N/A        | 非本期 |

## 9. 附录

### 9.1 原型图/设计稿链接
-   [视觉稿链接：待补充]
-   [交互稿链接：待补充]
-   参考原始PRD中的图片进行UI理解：
    *   进入练习转场示意图 (基于描述想象，类似 `in_table_image_E3xVbhOCBoczFFxa0Vic8qyFnLb` 的动态版)
    *   练习环节页面框架 (`in_table_image_K4wFb9b0Qof0G2xH6FvcBEGFnjx`, `in_table_image_E3xVbhOCBoczFFxa0Vic8qyFnLb`)
    *   题目间转场与反馈示意图 (`in_table_image_BosmbvrKeo8580x9RqycMEZ7nYb`, `in_table_image_GFWobx4zHo13YlxhwhlccN3GnRe`)

### 9.2 术语表
-   **练习组件：** AI课中用于学生进行习题作答与反馈的交互模块。
-   **转场动效：** 在不同界面或状态切换时使用的过渡动画效果。
-   **IP形象：** 代表产品或品牌的虚拟形象，如此处提及的“老师”或AI助手。
-   **勾画组件：** 允许用户在题目内容上进行划线、标记的工具。
-   **错题本：** 用户收藏错题或重要题目的功能模块。
-   **答疑组件（问一问）：** 用户在学习过程中发起提问并获得解答的模块。

### 9.3 参考资料
-   [原始需求文档：PRD - AI 课中 - 练习组件 1.0_analyzed.md]
-   [AI 课中 - V1.0 整体需求文档 (若有)]
-   [题型支持相关PRD (若有)]

openapi: 3.0.3
info:
  title: AI课中练习组件 API
  description: |
    提供AI课中练习组件的核心功能，包括练习环节的转场动效、核心交互和即时反馈机制。
    通过优化练习组件的交互体验，提升学生作答流畅度和情感反馈体验，增强课程满意度与学习效果。
    本文档遵循 OpenAPI 3.0.3 规范和团队API设计规范。
  version: v1.0.0
  contact:
    name: AI课中练习组件开发团队
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api/v1
    description: 本地开发服务器
  - url: https://api-dev.example.com/api/v1
    description: 开发环境服务器
  - url: https://api.example.com/api/v1
    description: 生产环境服务器

tags:
  - name: ExerciseSession
    description: 练习会话管理相关接口

components:
  schemas:
    # 通用响应结构
    GenericError:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码，0表示成功，非0表示错误
          example: 4000001
        message:
          type: string
          description: 用户可读的错误信息
          example: '无效的参数'
        details:
          type: string
          description: 可选的详细错误信息或调试信息
          nullable: true
          example: '字段 [userId] 不能为空'
        data:
          type: object
          description: 可选的附加数据
          nullable: true

    # 转场配置
    TransitionConfig:
      type: object
      description: 转场动效配置信息
      properties:
        transitionType:
          type: string
          description: 转场类型
          enum: [enter_first, enter_continue, exit]
          example: "enter_first"
        displayText:
          type: string
          description: 转场显示文案
          example: "开始练习+数学基础课程"
        animationDuration:
          type: integer
          description: 动画持续时间（毫秒）
          example: 2000
        ipAnimationConfig:
          type: object
          description: IP动效配置
          properties:
            enabled:
              type: boolean
              example: true
            animationType:
              type: string
              example: "bounce"
        flipPageAnimation:
          type: object
          description: 翻页动画配置
          properties:
            enabled:
              type: boolean
              example: true
            direction:
              type: string
              enum: [left_to_right, right_to_left]
              example: "left_to_right"

    # 题目信息
    QuestionInfo:
      type: object
      description: 题目详细信息
      properties:
        questionId:
          type: string
          description: 题目唯一标识
          example: "q_12345"
        questionType:
          type: string
          description: 题目类型
          enum: [single_choice, multiple_choice, fill_blank, essay]
          example: "single_choice"
        questionContent:
          type: string
          description: 题目内容
          example: "下列哪个选项是正确的？"
        options:
          type: array
          description: 选择题选项列表
          items:
            type: object
            properties:
              optionId:
                type: string
                example: "A"
              optionText:
                type: string
                example: "选项A的内容"
        difficulty:
          type: integer
          description: 题目难度等级（1-5）
          minimum: 1
          maximum: 5
          example: 3
        knowledgePointIds:
          type: array
          description: 关联的知识点ID列表
          items:
            type: string
          example: ["kp_001", "kp_002"]
        estimatedTime:
          type: integer
          description: 预估答题时间（秒）
          example: 60

    # 进度状态
    ProgressStatus:
      type: object
      description: 练习进度状态
      properties:
        currentPosition:
          type: integer
          description: 当前题目位置
          example: 1
        totalQuestions:
          type: integer
          description: 预估总题目数
          example: 10
        completedQuestions:
          type: integer
          description: 已完成题目数
          example: 0
        progressPercentage:
          type: number
          format: float
          description: 完成进度百分比
          example: 0.0
        streakCount:
          type: integer
          description: 连胜次数
          example: 0

    # 计时状态
    TimingStatus:
      type: object
      description: 计时状态信息
      properties:
        currentQuestionStartTime:
          type: integer
          format: int64
          description: 当前题目开始时间（Unix时间戳，秒）
          example: 1716883200
        currentQuestionElapsedTime:
          type: integer
          description: 当前题目已用时间（秒）
          example: 0
        totalElapsedTime:
          type: integer
          description: 总用时（秒）
          example: 0
        isPaused:
          type: boolean
          description: 是否暂停计时
          example: false

    # 即时反馈内容
    FeedbackContent:
      type: object
      description: 即时反馈内容
      properties:
        feedbackType:
          type: string
          description: 反馈类型
          enum: [correct, incorrect, partial_correct]
          example: "correct"
        feedbackText:
          type: string
          description: 反馈文案
          example: "回答正确！"
        explanation:
          type: string
          description: 解释说明
          nullable: true
          example: "这道题考查的是基础概念..."
        encouragementText:
          type: string
          description: 鼓励文案
          nullable: true
          example: "继续保持！"

    # 连胜状态
    StreakStatus:
      type: object
      description: 连胜状态信息
      properties:
        isStreak:
          type: boolean
          description: 是否触发连胜
          example: true
        streakCount:
          type: integer
          description: 连胜次数
          example: 3
        streakMessage:
          type: string
          description: 连胜提示文案
          nullable: true
          example: "太棒了！连续答对3题！"
        showAnimation:
          type: boolean
          description: 是否显示连胜动画
          example: true
        animationType:
          type: string
          description: 动画类型
          nullable: true
          example: "celebration"

    # 创建练习会话请求
    CreateExerciseSessionRequest:
      type: object
      description: 创建练习会话请求体
      properties:
        userId:
          type: string
          description: 用户ID
          example: "user_12345"
        courseId:
          type: string
          description: 课程ID
          example: "course_67890"
        componentId:
          type: string
          description: 组件ID
          example: "component_11111"
      required:
        - userId
        - courseId
        - componentId

    # 创建练习会话响应
    CreateExerciseSessionResponse:
      type: object
      description: 创建练习会话响应
      properties:
        sessionId:
          type: string
          description: 会话唯一标识
          example: "session_98765"
        transitionConfig:
          $ref: '#/components/schemas/TransitionConfig'
        questionInfo:
          $ref: '#/components/schemas/QuestionInfo'
        progressStatus:
          $ref: '#/components/schemas/ProgressStatus'
        timingStatus:
          $ref: '#/components/schemas/TimingStatus'
      required:
        - sessionId
        - transitionConfig
        - questionInfo
        - progressStatus
        - timingStatus

    # 恢复练习会话响应
    ResumeExerciseSessionResponse:
      type: object
      description: 恢复练习会话响应
      properties:
        sessionId:
          type: string
          description: 会话唯一标识
          example: "session_98765"
        transitionConfig:
          $ref: '#/components/schemas/TransitionConfig'
        questionInfo:
          $ref: '#/components/schemas/QuestionInfo'
        progressStatus:
          $ref: '#/components/schemas/ProgressStatus'
        timingStatus:
          $ref: '#/components/schemas/TimingStatus'
      required:
        - sessionId
        - transitionConfig
        - questionInfo
        - progressStatus
        - timingStatus

    # 提交答案请求
    SubmitAnswerRequest:
      type: object
      description: 提交答案请求体
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: "session_98765"
        questionId:
          type: string
          description: 题目ID
          example: "q_12345"
        userAnswer:
          type: string
          description: 学生答案
          example: "A"
        answerTime:
          type: integer
          description: 答题用时（秒）
          example: 45
      required:
        - sessionId
        - questionId
        - userAnswer
        - answerTime

    # 提交答案响应
    SubmitAnswerResponse:
      type: object
      description: 提交答案响应
      properties:
        isCorrect:
          type: boolean
          description: 答案是否正确
          example: true
        feedbackContent:
          $ref: '#/components/schemas/FeedbackContent'
        streakStatus:
          $ref: '#/components/schemas/StreakStatus'
        progressStatus:
          $ref: '#/components/schemas/ProgressStatus'
        nextQuestionInfo:
          allOf:
            - $ref: '#/components/schemas/QuestionInfo'
          nullable: true
          description: 下一题信息，如果练习完成则为null
        isCompleted:
          type: boolean
          description: 练习是否完成
          example: false
      required:
        - isCorrect
        - feedbackContent
        - streakStatus
        - progressStatus
        - isCompleted

  parameters:
    # 用户ID查询参数
    userIdQueryParameter:
      name: userId
      in: query
      required: true
      description: 用户ID
      schema:
        type: string
        example: "user_12345"

    # 课程ID查询参数
    courseIdQueryParameter:
      name: courseId
      in: query
      required: true
      description: 课程ID
      schema:
        type: string
        example: "course_67890"

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "请输入JWT Token, 格式为: Bearer {token}"

  responses:
    UnauthorizedError:
      description: 未认证或认证令牌无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4010001
                message: "需要认证或认证失败"

    ForbiddenError:
      description: 无权限访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4030001
                message: "无权限执行此操作"

    NotFoundError:
      description: 请求的资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4040001
                message: "请求的资源不存在"

    BadRequestError:
      description: 请求参数无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4000001
                message: "请求参数无效"

    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 5000001
                message: "服务器内部错误"

paths:
  /exercise_sessions:
    post:
      tags:
        - ExerciseSession
      summary: 进入练习环节
      description: |
        创建练习会话，返回转场配置和首题信息。
        用于学生首次进入练习环节的场景，系统会创建新的练习会话，
        返回转场动效配置、首题信息和初始进度状态。
      operationId: createExerciseSession
      requestBody:
        description: 创建练习会话请求信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateExerciseSessionRequest'
            examples:
              default:
                value:
                  userId: "user_12345"
                  courseId: "course_67890"
                  componentId: "component_11111"
      responses:
        '200':
          description: 练习会话创建成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateExerciseSessionResponse'
              examples:
                default:
                  value:
                    sessionId: "session_98765"
                    transitionConfig:
                      transitionType: "enter_first"
                      displayText: "开始练习+数学基础课程"
                      animationDuration: 2000
                      ipAnimationConfig:
                        enabled: true
                        animationType: "bounce"
                      flipPageAnimation:
                        enabled: true
                        direction: "left_to_right"
                    questionInfo:
                      questionId: "q_12345"
                      questionType: "single_choice"
                      questionContent: "下列哪个选项是正确的？"
                      options:
                        - optionId: "A"
                          optionText: "选项A的内容"
                        - optionId: "B"
                          optionText: "选项B的内容"
                      difficulty: 3
                      knowledgePointIds: ["kp_001", "kp_002"]
                      estimatedTime: 60
                    progressStatus:
                      currentPosition: 1
                      totalQuestions: 10
                      completedQuestions: 0
                      progressPercentage: 0.0
                      streakCount: 0
                    timingStatus:
                      currentQuestionStartTime: 1716883200
                      currentQuestionElapsedTime: 0
                      totalElapsedTime: 0
                      isPaused: false
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  /exercise_sessions/resume:
    get:
      tags:
        - ExerciseSession
      summary: 恢复练习环节
      description: |
        恢复未完成的练习会话，返回当前进度和题目信息。
        用于学生重新进入课程时恢复练习状态的场景，
        系统会查询未完成的练习会话，返回当前进度、题目信息和计时状态。
      operationId: resumeExerciseSession
      parameters:
        - $ref: '#/components/parameters/userIdQueryParameter'
        - $ref: '#/components/parameters/courseIdQueryParameter'
      responses:
        '200':
          description: 练习会话恢复成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ResumeExerciseSessionResponse'
              examples:
                default:
                  value:
                    sessionId: "session_98765"
                    transitionConfig:
                      transitionType: "enter_continue"
                      displayText: "继续练习+数学基础课程"
                      animationDuration: 2000
                      ipAnimationConfig:
                        enabled: true
                        animationType: "bounce"
                      flipPageAnimation:
                        enabled: false
                    questionInfo:
                      questionId: "q_12346"
                      questionType: "single_choice"
                      questionContent: "当前题目内容"
                      options:
                        - optionId: "A"
                          optionText: "选项A的内容"
                        - optionId: "B"
                          optionText: "选项B的内容"
                      difficulty: 3
                      knowledgePointIds: ["kp_003"]
                      estimatedTime: 60
                    progressStatus:
                      currentPosition: 3
                      totalQuestions: 10
                      completedQuestions: 2
                      progressPercentage: 20.0
                      streakCount: 2
                    timingStatus:
                      currentQuestionStartTime: 1716883320
                      currentQuestionElapsedTime: 15
                      totalElapsedTime: 135
                      isPaused: false
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []

  /exercise_sessions/submit_answer:
    post:
      tags:
        - ExerciseSession
      summary: 提交答案并获取反馈
      description: |
        处理学生答题，返回即时反馈、连胜状态和下一题信息。
        用于学生提交答案后获取即时反馈的场景，
        系统会判断答案正确性，生成反馈内容，检查连胜状态，
        更新进度并返回下一题信息或完成状态。
      operationId: submitAnswer
      requestBody:
        description: 提交答案请求信息
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubmitAnswerRequest'
            examples:
              default:
                value:
                  sessionId: "session_98765"
                  questionId: "q_12345"
                  userAnswer: "A"
                  answerTime: 45
      responses:
        '200':
          description: 答案提交成功，返回反馈信息
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubmitAnswerResponse'
              examples:
                correct_answer:
                  summary: 答案正确的响应示例
                  value:
                    isCorrect: true
                    feedbackContent:
                      feedbackType: "correct"
                      feedbackText: "回答正确！"
                      explanation: "这道题考查的是基础概念，你掌握得很好。"
                      encouragementText: "继续保持！"
                    streakStatus:
                      isStreak: true
                      streakCount: 3
                      streakMessage: "太棒了！连续答对3题！"
                      showAnimation: true
                      animationType: "celebration"
                    progressStatus:
                      currentPosition: 2
                      totalQuestions: 10
                      completedQuestions: 1
                      progressPercentage: 10.0
                      streakCount: 3
                    nextQuestionInfo:
                      questionId: "q_12346"
                      questionType: "single_choice"
                      questionContent: "下一题的内容"
                      options:
                        - optionId: "A"
                          optionText: "选项A"
                        - optionId: "B"
                          optionText: "选项B"
                      difficulty: 3
                      knowledgePointIds: ["kp_003"]
                      estimatedTime: 60
                    isCompleted: false
                incorrect_answer:
                  summary: 答案错误的响应示例
                  value:
                    isCorrect: false
                    feedbackContent:
                      feedbackType: "incorrect"
                      feedbackText: "答案不正确"
                      explanation: "正确答案是B，因为..."
                      encouragementText: "没关系，继续努力！"
                    streakStatus:
                      isStreak: false
                      streakCount: 0
                      streakMessage: null
                      showAnimation: false
                      animationType: null
                    progressStatus:
                      currentPosition: 1
                      totalQuestions: 10
                      completedQuestions: 0
                      progressPercentage: 0.0
                      streakCount: 0
                    nextQuestionInfo:
                      questionId: "q_12345_similar"
                      questionType: "single_choice"
                      questionContent: "相似题目内容"
                      options:
                        - optionId: "A"
                          optionText: "选项A"
                        - optionId: "B"
                          optionText: "选项B"
                      difficulty: 3
                      knowledgePointIds: ["kp_001"]
                      estimatedTime: 60
                    isCompleted: false
                exercise_completed:
                  summary: 练习完成的响应示例
                  value:
                    isCorrect: true
                    feedbackContent:
                      feedbackType: "correct"
                      feedbackText: "回答正确！"
                      explanation: "恭喜你完成了所有练习！"
                      encouragementText: "表现优秀！"
                    streakStatus:
                      isStreak: true
                      streakCount: 5
                      streakMessage: "完美！连续答对5题！"
                      showAnimation: true
                      animationType: "completion"
                    progressStatus:
                      currentPosition: 10
                      totalQuestions: 10
                      completedQuestions: 10
                      progressPercentage: 100.0
                      streakCount: 5
                    nextQuestionInfo: null
                    isCompleted: true
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          $ref: '#/components/responses/ForbiddenError'
        '404':
          $ref: '#/components/responses/NotFoundError'
        '500':
          $ref: '#/components/responses/InternalServerError'
      security:
        - BearerAuth: []
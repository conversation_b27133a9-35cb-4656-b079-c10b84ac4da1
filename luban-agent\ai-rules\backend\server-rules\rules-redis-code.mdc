# Redis 使用规范 (v1.0)

## 1. 引言

### 1.1. 目的
本文档旨在定义项目中使用 Redis 缓存服务的统一规范，确保缓存使用的一致性、高效性和安全性，提高应用性能并减轻数据库负载。

### 1.2. 核心原则
*   **性能优先**: Redis 主要用于提升系统性能，使用时应注重吞吐量和延迟优化。
*   **合理缓存**: 只缓存高频访问且相对稳定的数据，避免过度缓存。
*   **数据一致性**: 妥善处理缓存与数据源之间的一致性，采用合适的更新策略。
*   **资源节约**: 合理设置过期时间，避免无效数据占用内存资源。
*   **可靠性**: 实现优雅降级，确保 Redis 服务不可用时系统仍能正常运行。

## 2. Redis 组件与配置

### 2.1. Redis 版本
项目当前使用的 Redis 版本为 **6.0.3**。使用特性时应考虑与此版本的兼容性。

### 2.2. 客户端库
项目统一使用 `github.com/go-redis/redis/v8` 作为 Go 语言访问 Redis 的客户端库。所有 Redis 操作应通过此库或基于此库的内部封装进行。

### 2.3. 应用侧封装
项目中通过 `app/core/redisx` 模块对 Redis 操作进行了统一封装。所有业务代码应使用此封装进行 Redis 操作，而非直接使用原始客户端。

### 2.4. 连接与认证
*   Redis 连接信息（地址、端口、密码等）通过配置文件或环境变量统一管理，禁止硬编码。
*   生产环境必须启用 Redis 认证，并使用强密码。
*   连接时必须设置超时参数，避免连接阻塞。

## 3. 键命名规范

### 3.1. 基本结构
Redis 键名应遵循以下格式:
```
{app}:{module}:{entity}:{id}:{field}
```

* **{app}**: 应用标识，如 `teacher`, `student`
* **{module}**: 模块名称，如 `user`, `order`, `product`
* **{entity}**: 实体/对象名称，如 `profile`, `detail`
* **{id}**: 唯一标识符，如用户ID、订单ID
* **{field}**: (可选) 具体字段或属性

**示例**:
* `teacher:user:profile:1001` - ID为1001的教师用户资料
* `student:course:detail:2045:chapters` - ID为2045的课程章节列表

### 3.2. 命名规则
*   所有键名使用小写英文字母、数字和冒号。
*   词语之间使用冒号 `:` 分隔。
*   实体名称推荐使用单数形式。
*   对于集合类型数据，可在末尾添加适当后缀，如 `list`, `set`, `hash`, `zset`。

### 3.3. 临时键与锁
*   临时数据键添加 `tmp:` 前缀，如 `tmp:teacher:login:verification:123456`
*   分布式锁键添加 `lock:` 前缀，如 `lock:order:payment:10086`
*   任务队列键添加 `queue:` 前缀，如 `queue:email:sending`

## 4. 数据结构与使用场景

### 4.1. String 类型
*   **使用场景**: 简单键值对、计数器、缓存序列化对象
*   **最佳实践**:
    *   小对象直接存储，大对象（>10KB）考虑压缩后存储。
    *   对象缓存优先使用 JSON 格式序列化，特殊场景可使用 Protocol Buffers 或 MessagePack。
    *   计数器操作优先使用原子操作 (`INCR`, `DECR`)。

### 4.2. Hash 类型
*   **使用场景**: 存储对象/记录的多个字段，相比于序列化整个对象更灵活
*   **最佳实践**:
    *   字段数量适中（几十个以内）的对象优先使用 Hash。
    *   Hash 的字段名与对象属性名保持一致，使用小写驼峰命名法。
    *   避免在单个 Hash 中存储过多字段（>100），以防性能下降。

### 4.3. List 类型
*   **使用场景**: 队列、栈、最新数据列表（如消息、动态）
*   **最佳实践**:
    *   消息队列场景使用 `LPUSH` + `BRPOP`。
    *   最新列表场景使用 `LPUSH` + `LTRIM`，控制列表长度。
    *   列表元素推荐使用 JSON 字符串或简单标量值。

### 4.4. Set 类型
*   **使用场景**: 去重、随机抽取、集合运算（如交集、并集）
*   **最佳实践**:
    *   适合存储中等规模的无序唯一元素集合。
    *   推荐用于标签系统、用户关系（如关注/粉丝）等场景。
    *   大规模数据（>10万元素）应考虑分片或使用其他数据结构。

### 4.5. Sorted Set (ZSet) 类型
*   **使用场景**: 排行榜、优先级队列、范围查询
*   **最佳实践**:
    *   分数 (score) 设计需考虑排序需求和更新频率。
    *   适合需要按某个权重排序的数据集合。
    *   对于时间序列数据，可使用时间戳作为分数。

### 4.6. Bitmap 和 HyperLogLog
*   **使用场景**: 
    *   Bitmap: 用户状态标记、签到记录、布隆过滤器
    *   HyperLogLog: UV统计、大规模去重计数
*   **最佳实践**:
    *   这些是特殊数据结构，应在合适场景下使用以节省内存。
    *   HyperLogLog 有约 0.81% 的误差，不适用于精确计数场景。

## 5. 缓存策略

### 5.1. 缓存更新策略
*   **Cache-Aside (旁路缓存)**: 
    *   读取时先查缓存，缓存未命中则查数据库并写入缓存。
    *   更新时先更新数据库，再删除或更新缓存。
    *   项目中推荐的基本策略。
*   **Write-Through (直写)**: 
    *   更新时同时更新缓存和数据库。
    *   适用于读多写少且数据一致性要求高的场景。
*   **Write-Behind (异步写入)**: 
    *   更新时只更新缓存，异步批量更新数据库。
    *   仅适用于特定场景，需谨慎使用。

### 5.2. 缓存穿透防护
*   **问题**: 请求不存在的数据导致缓存未命中，频繁访问数据库。
*   **解决方案**:
    *   对空结果也进行缓存，但设置较短的过期时间。
    *   使用布隆过滤器快速判断键是否可能存在。
    *   加强参数校验，拦截非法请求。

### 5.3. 缓存击穿防护
*   **问题**: 热点数据过期时导致大量请求同时访问数据库。
*   **解决方案**:
    *   热点数据不设置过期时间，而是通过后台任务定期更新。
    *   使用互斥锁（分布式锁），保证同一时间只有一个请求更新缓存。
    *   采用 "提前过期" 策略，在真正过期前异步刷新。

### 5.4. 缓存雪崩防护
*   **问题**: 大量缓存同时过期导致数据库负载剧增。
*   **解决方案**:
    *   过期时间添加随机偏移量，避免同时过期。
    *   使用多级缓存架构。
    *   为数据库设置限流或熔断机制。
    *   针对重要接口实现服务降级。

## 6. 过期策略

### 6.1. 设置过期时间
*   所有缓存数据原则上都应设置过期时间，避免无效数据长期占用内存。
*   过期时间应根据数据更新频率、重要性和访问模式来确定。
*   推荐的过期时间范围:
    *   高频访问基础数据: 1-24小时
    *   配置类数据: 1-7天
    *   会话数据: 15-30分钟
    *   验证码/临时令牌: 5-15分钟
    *   统计类数据: 根据统计周期定 (小时/天/周)

### 6.2. 过期时间设置方法
*   优先使用 `SET` 命令的 `EX` 参数或 `SETEX` 命令设置过期时间，而非单独调用 `EXPIRE`。
*   过期时间单位统一使用秒，代码中通过常量定义避免魔法数字。
*   关联数据的过期时间应保持一致或成比例关系。

### 6.3. 主动清理
*   对于不再需要的缓存数据，应主动调用 `DEL` 删除，而非等待过期。
*   批量清理相关前缀的缓存使用 `SCAN` + `DEL`，避免使用 `KEYS` 命令。

## 7. Redis 事务与锁

### 7.1. 事务使用
*   Redis 事务 (`MULTI/EXEC`) 提供了命令打包执行的能力，但不支持回滚。
*   使用事务时确保所有命令的正确性，因为命令错误不会导致事务中止。
*   需要保证原子性的场景，使用 Lua 脚本可能是更好的选择。

### 7.2. 分布式锁
*   实现分布式锁的基本要求:
    *   互斥性: 在任意时刻，只能有一个客户端持有锁。
    *   避免死锁: 即使客户端崩溃，锁也能在一定时间后自动释放。
    *   容错性: Redis 节点故障不应导致锁失效或错误释放。
*   基本实现:
    ```go
    // 获取锁
    success := redis.SetNX(ctx, "lock:resource_name", clientID, expiration)
    
    // 释放锁 (使用Lua脚本确保安全释放)
    unlockScript := `
    if redis.call("get", KEYS[1]) == ARGV[1] then
        return redis.call("del", KEYS[1])
    else
        return 0
    end`
    ```
*   高可靠分布式锁推荐使用 Redisson 或参照 Redlock 算法实现。

## 8. 连接管理

### 8.1. 连接池配置
*   合理配置连接池参数，避免连接过多或过少:
    *   最小空闲连接数 (`MinIdleConns`): 建议 5-10
    *   最大连接数 (`PoolSize`): 建议为 CPU 核心数的 2-4 倍
    *   最大连接空闲时间 (`IdleTimeout`): 建议 5-10 分钟
    *   连接最大生命周期 (`MaxConnAge`): 建议 30-60 分钟
*   连接池参数应通过配置文件设置，便于不同环境调整。

### 8.2. 连接状态监控
*   定期检查连接池状态，监控以下指标:
    *   连接数量 (总数、活跃数、空闲数)
    *   请求等待时间
    *   连接获取超时次数
    *   连接错误率
*   当发现连接异常时及时报警并排查。

### 8.3. 优雅关闭
*   应用关闭时应正确关闭 Redis 客户端，确保所有命令执行完毕并释放资源:
    ```go
    func cleanup() {
        ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
        defer cancel()
        if err := rdb.Close(); err != nil {
            log.Printf("failed to close redis client: %v", err)
        }
    }
    ```

## 9. 性能优化

### 9.1. 批量操作
*   优先使用批量命令 (如 `MGET`, `MSET`, `HMSET`) 替代多次单个操作。
*   对于不支持原生批量操作的命令，使用 Pipeline 技术减少网络往返。
*   Lua 脚本可以将多个操作合并为一次网络交互，适用于需要原子性的场景。

### 9.2. 大键处理
*   避免过大的键值对 (>10MB)，会导致性能问题和内存碎片。
*   大集合数据考虑分片存储，如 `user:follows:1001:1`, `user:follows:1001:2`。
*   定期使用 `redis-cli --bigkeys` 或内部监控工具检测大键。

### 9.3. 扫描键空间
*   禁止在生产环境使用 `KEYS` 命令，会阻塞 Redis。
*   使用 `SCAN` 系列命令 (`SCAN`, `HSCAN`, `SSCAN`, `ZSCAN`) 进行渐进式扫描。
*   实现示例:
    ```go
    func scanKeys(ctx context.Context, pattern string) []string {
        var keys []string
        var cursor uint64
        for {
            var scanKeys []string
            var err error
            scanKeys, cursor, err = rdb.Scan(ctx, cursor, pattern, 100).Result()
            if err != nil {
                log.Printf("scan error: %v", err)
                break
            }
            keys = append(keys, scanKeys...)
            if cursor == 0 {
                break
            }
        }
        return keys
    }
    ```

### 9.4. 合理使用数据结构
*   根据实际需求选择合适的数据结构，避免过度使用复杂结构。
*   String 类型性能最佳，应优先考虑。
*   集合类操作注意元素数量，避免超大集合。

## 10. 监控与告警

### 10.1. 关键指标监控
*   **性能指标**:
    *   命令执行延迟
    *   命令执行次数/QPS
    *   连接数量
    *   命中率 (Keyspace hits/misses)
*   **资源指标**:
    *   内存使用率
    *   CPU 使用率
    *   网络流量
    *   复制延迟 (主从架构)
*   **异常指标**:
    *   被拒绝的连接数
    *   客户端超时次数
    *   主从复制中断次数

### 10.2. 告警设置
*   为关键指标设置合理的告警阈值:
    *   内存使用率 > 80%
    *   命令延迟 > 100ms
    *   主从复制延迟 > 60s
    *   命中率 < 75%
*   告警应分级处理，避免过度告警导致疲劳。

### 10.3. 慢日志
*   开启并监控 Redis 慢查询日志:
    ```
    # 配置超过10毫秒的命令被记录
    slowlog-log-slower-than 10000
    # 保留最近的1000条慢查询记录
    slowlog-max-len 1000
    ```
*   定期分析慢日志，优化问题命令。

## 11. 安全性

### 11.1. 认证与访问控制
*   生产环境必须设置强密码，禁止空密码或弱密码。
*   使用 Redis ACL (Redis 6.0+) 限制命令执行和键访问权限。
*   不要在应用代码中硬编码 Redis 密码，应使用环境变量或配置服务。

### 11.2. 网络安全
*   Redis 服务器应部署在私有网络中，避免直接暴露在公网。
*   使用防火墙限制可访问 Redis 的 IP 地址。
*   考虑使用 SSL/TLS 加密 Redis 连接 (Redis 6.0+ 原生支持)。
*   避免使用默认端口 (6379)，降低被扫描风险。

### 11.3. 敏感数据处理
*   不要在 Redis 中存储未加密的敏感数据 (如密码、支付信息)。
*   如需缓存敏感数据，应先进行加密或脱敏处理。
*   重要数据应设置合理的过期时间，减少暴露窗口。

## 12. 备份与恢复

### 12.1. 备份策略
*   定期执行 RDB 备份，保存到安全的外部存储。
*   考虑开启 AOF 持久化以提高数据安全性 (注意性能影响)。
*   主从架构下，可从从节点执行备份，减轻主节点负担。

### 12.2. 恢复演练
*   定期进行数据恢复演练，验证备份的有效性。
*   制定详细的恢复流程文档，包括各种故障场景。

## 13. 最佳实践总结

*   设计合理的键名前缀和结构，方便管理和扩展。
*   为所有缓存设置适当的过期时间。
*   使用批量操作和管道技术提高性能。
*   实现缓存穿透、击穿、雪崩防护措施。
*   监控关键指标并设置合理的告警阈值。
*   使用连接池并正确配置连接参数。
*   定期检查并处理大键和热点键。
*   遵循安全最佳实践，保护 Redis 实例和数据。
*   定期备份数据并验证恢复流程。
*   根据业务需求选择合适的缓存更新策略和数据结构。

---
*本规范将根据项目实践持续更新。*

# PRD -  学生端 - 题型组件 - 一期## **版本管理**

<table>
<tr>
<td>**版本号**<br/></td><td>**日期**<br/></td><td>**变更人**<br/></td><td>**变更类型**<br/></td><td>**变更详情**<br/></td></tr>
<tr>
<td>V 1.0 <br/></td><td>2025.03.05<br/></td><td>张博<br/></td><td>新建文档<br/></td><td>- 新建文档<br/></td></tr>
<tr>
<td>V 5.0 <br/></td><td>2025.03.19<br/></td><td>张博<br/></td><td>需求完善<br/></td><td><br/></td></tr>
<tr>
<td>V 5.1<br/></td><td>2025.03.21<br/></td><td>张博<br/></td><td>需求完善<br/></td><td>- 调整填空题按批改方式分为自评和自动批改。<br/>- 单选、多选支持二次作答。<br/></td></tr>
</table>

## **关联需求**

<table>
<tr>
<td>**关联需求名称**<br/></td><td>**所属 PM**<br/></td><td>**需求进度**<br/></td><td>**文档链接**<br/></td></tr>
<tr>
<td>PRD -  学生端  - 巩固练习<br/></td><td>ou_4075bb29f2fdec7724d8181104831d94<br/></td><td>待评审<br/></td><td>[PRD -  学生端  - 巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f)<br/></td></tr>
</table>

## **设计稿**

交互稿：
视觉稿：[Figma: The Collaborative Interface Design Tool](https://www.figma.com/design/j6wNSQxDFuZHscu1SVMuIA/AI%E8%AF%BE%E4%BD%93%E9%AA%8C%E7%89%88?node-id=2922-27245&t=tMaoye5c0E4lU71L-0)
动效：

## 一、背景和目标

### 需求背景

- 基于 6 月初发布首个产品版本 V 0.9 ，其目标在于覆盖校内教学环节的核心功能。在题型方面，一期优先支持高频题型，具体包括单选题、多选题、填空题以及解答题。

### 项目收益

- 分阶段逐步满足用户在日常学习过程中，对于练习、测验等场景下所需题型的要求。

### 覆盖用户

- 新初一、初二及新高一、高二用户。

### 方案简述

- 题目匹配：以学科和题型作为校验条件，从题库中精准匹配相应题目，确保在教师端和学生端均能正常查看并进行作答，满足不同学科和题型的教学及学习需求。
- 流程梳理：按照初始状态、作答状态、解析状态三个阶段，对高频题型在作答前、作答过程中以及作答后的展示形式和交互流程进行详细梳理，确保整个做题流程连贯、完整，用户反馈及时清晰，以提供良好的用户体验。
- 作答方式支持：根据不同题型的特点，提供适配的作答方式，如支持通过键盘输入完成单选题、多选题、填空题的作答，针对解答题等需要详细书写过程的题型，增加拍照上传作答内容的功能，方便用户使用。

### 未来要做的

- 二期 - 支持母子题。
- 三期 - 支持其他特殊题型，完形填空、阅读理解、七选五等。

## **二、名词说明**

暂无

## 三、业务流程

见各题型详情产品方案

## 四、需求概览

![ZMWEwwUcBhLt6pb1IJ6cQ3f9nCe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_ZMWEwwUcBhLt6pb1IJ6cQ3f9nCe.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】
该图片展示了“题目类型”的层级结构和规划阶段，具体分解如下：

```mermaid
graph TD
    A[题目类型] --> B(选择题<br/>一期)
    A --> C(填空题<br/>一期)
    A --> D(解答题<br/>一期)
    A --> E(子母题<br/>二期)
    A --> F(特殊题型<br/>三期)

    B --> B1(单选题)
    B --> B2(多选题)

    C --> C1(支持自动批改 - 英语学科)
    C --> C2(自评 - 其他学科)

    F --> F1(完形填空<br/>选择题)
    F --> F2(阅读理解)
    F --> F3(作图题)
    F --> F4(连线题)
    F --> F5(证明题)
```

【============== 图片解析 END ==============】



<table>
<tr>
<td>**序号**<br/></td><td>**需求名称**<br/></td><td>**内容概述**<br/></td><td>**优先级**<br/></td></tr>
<tr>
<td>1<br/></td><td>单选题<br/></td><td>支持单选题题型，实现完整展示和交互逻辑。<br/></td><td>P0<br/></td></tr>
<tr>
<td>2<br/></td><td>多选题<br/></td><td>支持多选题题型，实现完整展示和交互逻辑。<br/></td><td>P0<br/></td></tr>
<tr>
<td>3<br/></td><td>填空题 - 英语自动批改<br/></td><td>支持填空题题型，实现完整展示和交互逻辑。<br/></td><td>P0<br/></td></tr>
<tr>
<td>4<br/></td><td>填空题 - 其他自评<br/></td><td>支持填空题题型，实现完整展示和交互逻辑。<br/></td><td>P0<br/></td></tr>
<tr>
<td>5<br/></td><td>解答题<br/></td><td>支持拍照作答、画板及文字输入等多种解答方式。<br/></td><td>P0<br/></td></tr>
</table>

## 五、详细产品方案

### 单选题

![IoYxw8G5UhbcXFbQonpcnKj8n6c](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_IoYxw8G5UhbcXFbQonpcnKj8n6c.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

该图片是一张用户答题流程图，详细描述了用户从查看题目到最终获得答案解析及进行答疑的完整过程。以下是对图片内关键元素、组成部分的层级化解析，并使用 Mermaid flowchart 语法进行描述。

**一、流程阶段与核心元素**

用户答题流程主要分为三个阶段：初始状态、作答状态和解析状态。

1.  **初始状态 (User's Initial Interaction)**
    *   **核心动作**：`查看题目 (题干+作答区)`
        *   这是用户与题目交互的起点。
    *   **用户分支选择**：
        *   `选择选项 (仅可选择1个)`: 用户根据理解选择一个答案。
        *   `不确定`: 用户对答案没有把握。
    *   **状态转换**：
        *   从`查看题目`进入`选择选项`。
        *   从`查看题目`进入`不确定`。
        *   用户在`不确定`状态下，可以返回重新`选择选项`。

2.  **作答状态 (User's Answering Process)**
    *   **核心动作**：
        *   `提交`: 用户在`选择选项`后确认答案。
        *   `放弃作答 (二次确认)`: 用户在`不确定`后选择不完成作答，通常有防误触的二次确认机制。
    *   **作答反馈 (Feedback Sub-process)**：这是一个关键的反馈环节，根据用户的提交或放弃行为给出即时结果。
        *   `回答正确`: 用户`提交`的答案正确。
        *   `回答错误`: 用户`提交`的答案错误。
        *   `未作答`: 用户执行了`放弃作答`操作。
            *   注：OCR文本中亦将“未作答”列为“提交”的一种可能结果，但图中明确由“放弃作答”产生。此处遵循图片展示的流程。
    *   **状态转换**：
        *   从`选择选项`进入`提交`。
        *   从`不确定`进入`放弃作答`。
        *   `提交`后，根据答案正误，分别进入`回答正确`或`回答错误`。
        *   `放弃作答`后，进入`未作答`。

3.  **解析状态 (Post-Answer Analysis & Support)**
    *   **核心动作**：`查看答案、题目解析`
        *   用户在获得`作答反馈`（无论正确、错误或未作答）后，均可进入此环节，查看详细的解答和分析。
    *   **智能辅助**：`猜你想问`
        *   系统根据题目和用户作答情况，推荐相关问题。
        *   用户可选择：
            *   `点击推荐问题`: 直接查看系统预设的高频问题。
            *   `点击自由提问`: 输入个性化问题。
    *   **最终支持**：`答疑 chat`
        *   无论是通过推荐问题还是自由提问，用户最终都会进入答疑聊天界面，获得进一步的人工或智能客服支持。
    *   **状态转换**：
        *   从`回答正确`、`回答错误`或`未作答`均进入`查看答案、题目解析`。
        *   从`查看答案、题目解析`进入`猜你想问`。
        *   `猜你想问`后，用户选择`点击推荐问题`或`点击自由提问`。
        *   `点击推荐问题`或`点击自由提问`后，均进入`答疑 chat`。

**二、Mermaid 流程图描述**

```mermaid
flowchart LR
    subgraph 初始状态
        A["查看题目<br/>(题干+作答区)"] --> B["选择选项<br/>(仅可选择1个)"]
        A --> C["不确定"]
        C -.-> B
    end

    subgraph 作答状态
        B --> D["提交"]
        C --> E["放弃作答<br/>(二次确认)"]
        
        subgraph 作答反馈
            direction TB
            F["回答正确"]
            G["回答错误"]
            H["未作答"]
        end
        
        D --> F
        D --> G
        E --> H
    end

    subgraph 解析状态
        I["查看答案、题目解析"]
        J["猜你想问"]
        K["点击推荐问题"]
        L["点击自由提问"]
        M["答疑 chat"]
        
        F --> I
        G --> I
        H --> I
        I --> J
        J --> K
        J --> L
        K --> M
        L --> M
    end
```

【============== 图片解析 END ==============】



<table>
<tr>
<td>**状态**<br/></td><td>**原型图**<br/></td><td>**需求说明**<br/></td></tr>
<tr>
<td>**初始状态**<br/><br/></td><td>![in_table_ZMHLbhLb3oLnPgx1baccZegqnRH](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZMHLbhLb3oLnPgx1baccZegqnRH.png)<br/>![in_table_XyfubkidNoqIObxbiiOcnuDdn3o](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XyfubkidNoqIObxbiiOcnuDdn3o.png)<br/><br/><br/></td><td>**状态判断：**<br/>- 当进入题目且没有作答时（即单选题没有选中选项）为初始状态。<br/>**题型校验：**<br/>- 学科：语文、数学、英语、物理、化学、生物。<br/>- 录入题目作答方式为：单选题。<br/>- 非母子题。<br/>**页面框架：**<br/>- 仅支持全屏展示。<br/>- 退出学习：- 按钮名称：退出学习。- 交互：点击后退出至，xx 页面。<br/>- 作答计时：- 每题独立正计时（格式 00:00，上限 59:59）。- 用户进入题目开始计时，提交后停止计时。- 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时（例一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。<br/>- 题板结构：- 左侧为题干区、右侧为作答区。<br/>- 题干区：- 题目标签：- 题型标签：单选题。- 题目来源：数据源自题库中已录入题目，如“2024 春•浙江期中”。- 题干内容：- 数据源自题库中已录入题目。- 题干支持展示文字、图片（支持点开放大）。- 图片排版处理：![in_table_OQ9NbPQ5foqF1Gxc3Trc1FXMnfc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OQ9NbPQ5foqF1Gxc3Trc1FXMnfc.png)<br/>- 作答区：- 选项：- 选项内容：数据来自题库中已录入题目。- 展示处理：- 超出一行后，折行展示。![in_table_LHNtbkAjxoyPGLxdKnWcqouWnRc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LHNtbkAjxoyPGLxdKnWcqouWnRc.png)- 含图选项处理：![in_table_GOEWbMoOBo6fLExCJeScQRctnJf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GOEWbMoOBo6fLExCJeScQRctnJf.png)- 操作：- 【不确定】：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 点击【选项】，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 【提交】：- 按钮状态：不可点击，按钮样式置灰。- 交互：/<br/>- **勾画组件：**![in_table_XKUNb9JLhoHRiRxX6OIc4vJDnVg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XKUNb9JLhoHRiRxX6OIc4vJDnVg.png)- 入口：作答界面悬浮【勾画】按钮（常驻显示）。- 交互：支持用户在作答环节时，点击【勾画】按钮唤起勾画组件，对题目进行勾画和标记。- 勾画工具。- 笔迹调整：- 颜色调整：支持蓝色、红色、黑色。- 粗细调整：细、中等、粗。- 橡皮擦：- 粗细调整：细、中等、粗。- 撤回、恢复。- 一键清空。- 交互：- 点击【完成】，收起勾画组件，同时将勾画的内容自动保存并展示。<br/></td></tr>
<tr>
<td>**作答状态**<br/></td><td>![in_table_AFLQbFyjFocz6PxFnrkcleXKnhd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AFLQbFyjFocz6PxFnrkcleXKnhd.png)<br/><br/><br/></td><td>**状态判断：**<br/>- 当进入题目且有作答时（即单选题选中唯一选项）为作答状态。<br/>**批改方式：**<br/>- 自动批改<br/>**作答方式：**<br/>- 选项：- 支持选中唯一一个选项，并展示选中状态。- 当有已选中选项时，点击其他选项，此时取消已选中，重新选中点击的选项。<br/>- 操作：- 不确定，[同初始状态](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd#share-CIA8dBUyVoBjKPx3Meccu02LnWg)：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 点击【选项】，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 提交：- 按钮状态：可点击。- 交互：- 第一次点击提交，点击后提交作答数据，校验作答结果是否正确。- 正确：- 反馈弹窗。- 进入解析状态，展示解析状态内容。- 错误：- 反馈弹窗。- 停留在作答状态。仅展示猜你想问入口，用户可自主点击，不展示答案、解析等。- 重置作答，取消选中状态，用户可重新作答，进行第二次提交。- 第二次点击提交，点击后再次提交作答数据，校验作答结果是否正确。- 正确：- 反馈弹窗。- 进入解析状态，展示解析状态内容。- 错误：- 反馈弹窗。- 进入解析状态，展示解析状态内容。<br/></td></tr>
<tr>
<td>**解析状态**<br/></td><td>![in_table_VEZlblon3oPiDKxrcv1cUPwcnTe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VEZlblon3oPiDKxrcv1cUPwcnTe.png)<br/><br/>![in_table_R8HwbvPb9o4741xn2gfcxSe6nO1](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_R8HwbvPb9o4741xn2gfcxSe6nO1.png)<br/><br/>![in_table_C1S1brNyUoAK20xiVlmcgg3InAf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_C1S1brNyUoAK20xiVlmcgg3InAf.png)<br/><br/></td><td>**状态判断：**<br/>- 当题目有作答（即单选题选中唯一选项）并点击【提交】正确或第二次【提交】后为解析状态。<br/>- 当题目没有作答，但点击了【不确定】 - 【放弃作答】后为解析状态。<br/>**页面框架：**<br/>- 作答计时：- 停止计时，时间定格在提交时。<br/>- 反馈弹窗：见，[巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f)。<br/>- 选项占比：- 展示每个选项已作答用户的占比，格式 xx.x %。- 数据取自该题目历史回答数据，- 冷启动策略，当题目使用次数大于 20 后，开始向用户展示占比数字。- 更新策略，每日更新使用次数大于 20 的题目的作答占比。<br/>- 答案：- 展示正确的选项文本- 在选项上标记：- 正确选项标记为绿色；- 错误选项标记为红色；<br/>- 题目解析：- 内容：数据源自题库中已录入题目，对应字段【题目解析】。<br/>- 问一问：- 入口：右上角问一问 icon ，仅当题目进入解析状态后展示入口。- 交互：-  点击弹出答疑组件。- 展示： 支持自由提问 + 模型生成的猜你想问问题（最多 3 个 可以为 0 ）。- 由用户发起首轮对话。<br/>- 操作按钮：- 继续：- 出现时机：题目进入解析状态。- 按钮状态：可点击。- 交互：点击后进入下一题/下一节内容。<br/></td></tr>
</table>

![in_table_ZMHLbhLb3oLnPgx1baccZegqnRH](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZMHLbhLb3oLnPgx1baccZegqnRH.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片为一个在线答题界面的截图，主要构成元素如下：

*   **页面框架**：
    *   **顶部栏**：
        *   **返回按钮**：位于左上角，图标为向左箭头。
        *   **计时器**：显示 "用时 01:24"。
        *   **答题进度条**：橙色和灰色结合，表示当前答题进度。
    *   **题目内容区域**：
        *   **题型及来源**： "单选 (2024春·浙江期中)"。
        *   **题干描述**：
            *   文字内容："在四面体ABCD中，BCD为等边三角形$ADB=\pi/2$，二面角B-AD-C的大小为$a$，则$a$的取值范围是 ( )"
            *   **几何图形**：展示了一个四面体ABCD的示意图，顶点A, B, C, D已标注。BD之间为虚线。
            *   **图片查看功能**：几何图形右下角有一个放大镜图标。
    *   **选项区域**：
        *   **选项A**： "选项过长展示效果选项过长展示效果选项过长展示效果选项过长展示效果"
        *   **选项B**： "`(0, \pi/6]`"
        *   **选项C**： "`(0, \pi/6]`"
        *   **选项D**： "`(0, \pi/6]`"
    *   **底部操作栏**：
        *   **勾画按钮**：位于左下角，图标为铅笔，文字为 "勾画"。
        *   **不确定按钮**：中间偏右按钮，浅色背景，文字为 "不确定"。
        *   **提交按钮**：右下角按钮，橙色背景，文字为 "提交"。

【============== 图片解析 END ==============】

![in_table_XyfubkidNoqIObxbiiOcnuDdn3o](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XyfubkidNoqIObxbiiOcnuDdn3o.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线教育产品中的单选题答题界面，其关键元素和组成部分结构如下：

*   **一、 整体界面布局**
    *   **1. 顶部导航与状态区**
        *   **1.1. 应用内信息**
            *   左上角：返回按钮（`<`图标），用于返回上一界面。
            *   中部：用时显示（`用时 01:24`），指示当前答题所消耗的时间。
            *   右侧：橙色答题进度条，显示当前答题进度。
        *   **1.2. 设备状态栏信息** (位于最顶部，通常由操作系统提供)
            *   左侧：当前时间 (`7:35 Mon Jun 3`)。
            *   右侧：网络信号强度及电池电量 (`100%`)。
    *   **2. 题目内容区**
        *   **2.1. 题目基础信息**
            *   类型：`单选`。
            *   来源/分类：`(2024春·浙江期中)`。
        *   **2.2. 题干详情**
            *   文字描述：`在四面体ABCD中，BCD为等边三角形ADB=pai/2，二面角B-AD-C的大小为a，则a的取值范围是（ ）`。
                *   其中数学条件 `ADB=pai/2` 表示：
                    $$
                    \angle ADB = \frac{\pi}{2}
                    $$
            *   附带几何图形：
                *   一个四面体的三维示意图，标注了顶点 A, B, C, D。
                *   边 BD 以虚线表示。
                *   图形右下角有放大镜图标（`⊕`），表示可点击查看大图。
        *   **2.3. 答题辅助工具**
            *   左下角：`勾画`按钮（铅笔图标），提示用户可能具备在题目上进行涂鸦或标记的功能。
    *   **3. 选项区**
        *   包含四个选项，每个选项由字母和内容组成：
            *   A. `(0, pai/6]`，其数学区间表示为：
               $$
               (0, \frac{\pi}{6}]
               $$
            *   B. `(0, pai/6]`，其数学区间表示为：
               $$
               (0, \frac{\pi}{6}]
               $$
            *   C. `(0, pai/6]`，其数学区间表示为：
               $$
               (0, \frac{\pi}{6}]
               $$
            *   D. `(0, pai/6]`，其数学区间表示为：
               $$
               (0, \frac{\pi}{6}]
               $$
    *   **4. 底部操作区**
        *   `放弃作答`按钮：允许用户放弃当前题目的作答。
        *   `提交`按钮：用户选定答案后，用于提交答案。

【============== 图片解析 END ==============】

![in_table_OQ9NbPQ5foqF1Gxc3Trc1FXMnfc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OQ9NbPQ5foqF1Gxc3Trc1FXMnfc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片定义了“图片组合”的几种UI布局样式。

**1. 整体结构：图片组合**
   该区域展示了不同数量图片的组合方式。

**2. 组成部分及关键元素：**

   *   **单个图片占位符的共同属性：**
        *   **宽高比：** 所有图片占位符内均标注 `2 : 3`，表示其设计宽高比例。
        *   **交互元素：** 每个图片占位符右下角均有一个放大镜图标 `Q`，通常表示点击可查看大图或详情。

   *   **具体组合样式：**

        *   **[未明确文字标题，根据布局推断为单图]** (位于“图片组合”标签下方左侧区域)
            *   包含 **1个** 主图片占位符。

        *   **双图** (位于“双图”标签下方)
            *   包含 **2个** 图片占位符。
            *   排列方式：水平并列。

        *   **3图** (位于“3图”标签下方)
            *   包含 **3个** 图片占位符。
            *   排列方式：
                *   顶部：1个较大的图片占位符。
                *   底部：2个较小的图片占位符，水平并列。

        *   **4图** (位于“4图”标签下方)
            *   包含 **4个** 图片占位符。
            *   排列方式：2x2 网格排列。

【============== 图片解析 END ==============】

![in_table_LHNtbkAjxoyPGLxdKnWcqouWnRc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LHNtbkAjxoyPGLxdKnWcqouWnRc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片展示了一个在线答题界面的用户界面（UI）。

1.  **整体界面布局**：分为顶部系统状态栏、应用导航栏和主体内容区。
    1.1. **系统状态栏 (顶部)**
        *   当前时间: "7:35 Mon Jun 3"
        *   系统图标: 如电池电量 "100%"
    1.2. **应用导航栏 (顶部，内容区之上)**
        *   返回按钮: 图标为 "<"
        *   计时器: "用时01:24"
        *   进度条: 水平条形，部分橙色填充，显示答题进度。
    1.3. **主体内容区 (分为左右两栏)**
        1.3.1. **左栏：题目展示区**
            *   题目信息:
                *   类型："单选"
                *   来源："(2024春·浙江期中)"
            *   题干内容:
                *   文字描述："在四面体ABCD中，BCD为等边三角形ADB=π/2，二面角B-AD-C的大小为α，则α的取值范围是（ ）"
                    *   其中包含数学条件：
                        $$
                        \angle ADB = \frac{\pi}{2}
                        $$
                    *   所求变量为 α (代表二面角B-AD-C的大小)。
                *   辅助图形：一个四面体的线框图，标注了顶点 A, B, D。
        1.3.2. **右栏：选项区**
            *   **选项 A**: "选项过长展示效果选项过长展示效果选项过长展示效果选项过长展示效果" (此为示例文字，用于展示选项文本过长时的显示效果)
            *   **选项 B**:
                $$
                [0, \frac{\pi}{6}]
                $$
            *   **选项 C**:
                $$
                (0, \frac{\pi}{6})
                $$
            *   **选项 D**:
                $$
                (0, \frac{\pi}{6}]
                $$

【============== 图片解析 END ==============】

![in_table_GOEWbMoOBo6fLExCJeScQRctnJf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GOEWbMoOBo6fLExCJeScQRctnJf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线答题界面的截图，主要包含以下组成部分：

1.  **顶部状态与导航栏**
    *   左上角：返回按钮 (`<` 图标)。
    *   中间：计时器，显示 "用时01:24"。
    *   计时器右侧：当前答题进度的橙色进度条。

2.  **题目内容区域**
    *   **题目元信息**
        *   类型标签: "单选"
        *   来源信息: "(2024春·浙江期中)"
    *   **题干文本**:
        在四面体ABCD中，BCD为等边三角形，$\angle ADB = \pi/2$，二面角B-AD-C的大小为$\alpha$，则$\alpha$的取值范围是（ ）
    *   左下角工具： "勾画" 图标（铅笔和纸张的样式）。

3.  **选项区域** (以两列两行方式排列)
    *   **选项A**
        *   标识: "A"
        *   内容: $(0, \pi/6]$
        *   附图: 一个四面体ABCD的几何图形。
        *   功能: 右下角有放大镜图标。
    *   **选项B**
        *   标识: "B"
        *   内容: $(0, \pi/6]$
        *   附图: 一个四面体ABCD的几何图形 (与选项A的图形在视角或辅助线上有所不同)。
        *   功能: 右下角有放大镜图标。
    *   **选项C**
        *   标识: "C"
        *   内容: $(0, \pi/6]$
        *   附图: 一个四面体ABCD的几何图形 (与选项A/B的图形在视角或辅助线上有所不同)。
        *   功能: 右下角有放大镜图标。
    *   **选项D**
        *   标识: "D"
        *   内容: $(0, \pi/6]$
        *   附图: 一个四面体ABCD的几何图形 (与选项A/B/C的图形在视角或辅助线上有所不同)。
        *   功能: 右下角有放大镜图标。

4.  **底部操作按钮区域**
    *   左侧按钮: "不确定" (灰色文字，白色背景)。
    *   右侧按钮: "提交" (白色文字，橙色背景)。

【============== 图片解析 END ==============】

![in_table_XKUNb9JLhoHRiRxX6OIc4vJDnVg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XKUNb9JLhoHRiRxX6OIc4vJDnVg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线学习界面的UI设计，具体元素如下：

*   **顶部导航栏:**
    *   左侧:
        *   返回按钮: 包含向左箭头图标和文字 "退出学习"。
        *   计时器: 显示 "用时 00:10"。
    *   右侧:
        *   按钮: "学习地图"。
        *   按钮: "反馈"。
*   **主要内容区域 (题目作答区):**
    *   题型标识: "单选"。
    *   题目描述: "关于空间几何，以下哪个描述是正确的？"。
    *   选项列表:
        *   A: "打点计时器"
        *   B: "打点计时器"
        *   C: "打点计时器"
        *   D: "打点计时器"
*   **底部工具栏 (悬浮):**
    *   第一行 (颜色及笔触选择):
        *   颜色选项: 蓝色 (当前选中)、橙色、黑色。
        *   笔触粗细选项: 三种不同粗细的线条图标。
    *   第二行 (功能操作):
        *   书写/画笔工具图标 (蓝色，当前选中)。
        *   橡皮擦工具图标。
        *   一个工具图标 (OCR识别为"/")。
        *   删除/垃圾桶图标。
        *   撤销图标。
        *   重做图标。
        *   按钮: "完成"。

【============== 图片解析 END ==============】

![in_table_AFLQbFyjFocz6PxFnrkcleXKnhd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AFLQbFyjFocz6PxFnrkcleXKnhd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线答题界面的截图，主要包含以下层面和元素：

1.  **状态栏区域 (顶部)**
    *   时间显示: "7:35 Mon Jun 3"
    *   系统图标: "100%" (可能表示电量或信号强度)

2.  **导航与计时区域 (顶部下方)**
    *   返回按钮: 图标 "<"
    *   用时显示: "用时01:24"
    *   进度条: 橙色的横向进度条，表示当前答题进度。

3.  **题目内容区域 (中部左侧)**
    *   题目类型与来源: "单选 (2024春·浙江期中)"
    *   题干描述:
        *   文字部分: "在四面体ABCD中，BCD为等边三角形ADB=$$ \frac{\pi}{2} $$，二面角B-AD-C的大小为a，则a的取值范围是（ ）"
        *   配图: 一个四面体ABCD的几何图形，其中顶点分别为A, B, C, D。线段BD以虚线表示。
    *   辅助工具:
        *   勾画按钮: 左下角有"勾画"文字和铅笔图标。

4.  **选项区域 (中部右侧)**
    *   选项A: "A $$(0, \frac{\pi}{6}]$$"
    *   选项B: "B $$(0, \frac{\pi}{6}]$$" (此选项被蓝色背景高亮选中)
    *   选项C: "C $$(0, \frac{\pi}{6}]$$"
    *   选项D: "D $$(0, \frac{\pi}{6}]$$"

5.  **操作按钮区域 (底部)**
    *   "不确定" 按钮 (灰色文字，白色背景)
    *   "提交" 按钮 (白色文字，橙色背景，为主要操作按钮)

【============== 图片解析 END ==============】

![in_table_VEZlblon3oPiDKxrcv1cUPwcnTe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VEZlblon3oPiDKxrcv1cUPwcnTe.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

本图片展示了一个在线教育产品中数学题目练习的界面截图。

**1. 界面顶部状态栏与计时信息:**
    *   **时间显示:** Mon Jun 3, 7:35
    *   **答题用时:** 01:24

**2. 题目区域:**
    *   **2.1. 题目类型与来源:**
        *   **类型:** 单选
        *   **来源:** (2024春·浙江期中)
    *   **2.2. 题干文字描述:**
        *   在四面体ABCD中，BCD为等边三角形，$\angle ADB = \frac{\pi}{2}$，二面角B-AD-C的大小为a，则a的取值范围是（ ）
    *   **2.3. 题目配图:**
        *   一个四面体ABCD的示意图。
            *   顶点: A, B, C, D
            *   棱: AB, AC, AD, BC, CD, BD (其中BD以虚线表示)
    *   **2.4. 选项:**
        *   **A.** $(0, \frac{\pi}{6}]$ (选择比例: 10.8%)
        *   **B.** $(0, \frac{\pi}{6}]$ (选择比例: 50.2%) (系统标记为正确答案)
        *   **C.** $(0, \frac{\pi}{6}]$ (选择比例: 20.9%)
        *   **D.** $(0, \frac{\pi}{6}]$ (选择比例: 18.1%)
    *   **2.5. 正确答案标识:**
        *   正确答案：B
    *   **2.6. 题目解析区域:**
        *   标题: "题目解析："
        *   内容: "题目解析"文字重复多次，为占位内容。

**3. 界面底部操作按钮:**
    *   （左下角，OCR未完整识别，根据图标推测） **显示/提示按钮** (图标为一个铅笔)
    *   **加入错题本按钮**
    *   **继续按钮**

【============== 图片解析 END ==============】

![in_table_R8HwbvPb9o4741xn2gfcxSe6nO1](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_R8HwbvPb9o4741xn2gfcxSe6nO1.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片为一个在线教育应用的答题界面截图，主要内容如下：

1.  **顶部状态栏**:
    *   时间: 7:35 Mon Jun 3
    *   电池状态: 100%

2.  **导航栏**:
    *   返回按钮: `<`
    *   用时: 01:24
    *   进度条
    *   帮助/疑问图标: `?`

3.  **题目区域**:
    *   **题型及来源**: 单选 (2024春·浙江期中)
    *   **题干**:
        在四面体ABCD中, BCD为等边三角形，∠ADB=$\frac{\pi}{2}$，二面角B-AD-C的大小为a，则a的取值范围是 ( )
    *   **配图**:
        *   一个四面体ABCD的示意图。顶点A在上方，底面大致为BCD。BD边上有虚线，暗示BD为底面的一部分或观察时的参考。

4.  **选项区域**:
    *   **A.** (0, $\frac{\pi}{6}$] (选择比例: 10.8%)
    *   **B.** (0, $\frac{\pi}{6}$] (选择比例: 50.2%) - 此选项被标记为选中且正确 (绿色背景和勾号)。
    *   **C.** (0, $\frac{\pi}{6}$] (选择比例: 20.9%) - 此选项被标记为错误 (红色背景和叉号)。
    *   **D.** (0, $\frac{\pi}{6}$] (选择比例: 18.1%)

5.  **答案与解析区域**:
    *   **正确答案**: B
    *   **题目解析**: 包含多行重复的“题目解析”文字，作为解析内容的占位符。

6.  **底部操作栏**:
    *   **加入错题本** 按钮
    *   **继续** 按钮

7.  **左下角浮动按钮**:
    *   **显示** (带有笔形图标)

【============== 图片解析 END ==============】

![in_table_C1S1brNyUoAK20xiVlmcgg3InAf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_C1S1brNyUoAK20xiVlmcgg3InAf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个学习界面中的问答交互弹窗。

1.  **问答交互弹窗 (居中显示):**
    *   1.1. **关闭按钮**: 位于弹窗右上角，图标为 'X'。
    *   1.2. **对话区域**:
        *   1.2.1. **用户提问气泡** (靠右): 显示文字 “题目中角A内角平分线是什么意思？”
        *   1.2.2. **AI (Dolli) 回复气泡** (靠左):
            *   1.2.2.1. **头像**: 显示 “Dolli” 字样及卡通厨师帽形象。
            *   1.2.2.2. **回复内容**: 显示文字 “指一个棱锥被平行于它的底面的一个平面所截后，截面与底面之间的几何形体。”
    *   1.3. **输入区域** (位于弹窗底部):
        *   1.3.1. **文本输入框**: 提示文字为 “请输入你的回答”。
        *   1.3.2. **图标按钮**: 位于输入框内右侧，图标样式为一个圆圈内包含一个类似麦克风的符号。
        *   1.3.3. **发送按钮**: 位于输入区域最右侧，橘色背景，图标为纸飞机样式。

2.  **背景学习界面 (部分可见，位于弹窗之后):**
    *   2.1. **退出学习按钮**: 位于界面左上角，文字为 “退出学习”，左侧有返回箭头图标。
    *   2.2. **题目相关区域** (位于弹窗左侧，内容部分被弹窗遮挡):
        *   2.2.1. 选项文本: 显示 “C 打点计时”。
        *   2.2.2. 文本标签: “题目解析”。
        *   2.2.3. 文本标签: “考察知识”。
        *   2.2.4. 文本标签: “猜你想问”，其上方有一个卡通厨师帽图标。

【============== 图片解析 END ==============】



### 多选题

![TILcwffRphk72dbIXtJc5Ettnhh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_TILcwffRphk72dbIXtJc5Ettnhh.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

此图片描绘了用户在互联网教育产品中进行在线答题及解析的完整流程。该流程可以分为三个主要阶段：初始状态、作答状态和解析状态。

**1. 关键元素与层级关联**

*   **I. 初始状态**
    *   **A. 查看题目**
        *   内容：包含题干与作答区。
        *   流向：进入“作答状态”。

*   **II. 作答状态** (包含用户答题操作及系统即时反馈)
    *   **A. 用户答题选择** (由“查看题目”进入)
        *   **1. 选择选项**
            *   说明：可选择1个或多个。
            *   流向：“提交”操作。
        *   **2. 不确定**
            *   流向1：“选择选项”（用户可能重新考虑并作答，虚线表示）。
            *   流向2：“放弃作答”操作。
    *   **B. 用户核心操作**
        *   **1. 提交** (在“选择选项”后进行)
            *   流向：触发“作答反馈”中的“回答正确”、“回答部分正确”或“回答错误”之一。
        *   **2. 放弃作答** (由“不确定”状态触发)
            *   说明：需要进行二次确认。
            *   流向1 (若确认放弃)：“作答反馈”中的“未作答”。
            *   流向2 (若取消放弃)：“选择选项”（用户返回选择，虚线表示）。
    *   **C. 作答反馈** (作为“作答状态”的子环节，展示答题结果)
        *   **1. 因“提交”产生的反馈：**
            *   a. 回答正确
            *   b. 回答部分正确 (具体原因如：漏选)
            *   c. 回答错误 (具体原因如：全错、部分错、多选)
        *   **2. 因“放弃作答”产生的反馈：**
            *   a. 未作答
        *   共同流向：所有“作答反馈”类型均导向“解析状态”的“查看答案、题目解析”。

*   **III. 解析状态**
    *   **A. 查看答案与解析** (由任一“作答反馈”进入)
        *   操作：用户查看答案和题目解析。
        *   流向：“猜你想问”。
    *   **B. 智能提问引导**
        *   **1. 猜你想问**
            *   流向：提供两种提问方式“点击推荐问题”或“点击自由提问”。
        *   **2. 点击推荐问题**
            *   流向：“答疑 chat”。
        *   **3. 点击自由提问**
            *   流向：“答疑 chat”。
    *   **C. 答疑交互**
        *   **1. 答疑 chat** (由“点击推荐问题”或“点击自由提问”进入)
            *   最终交互环节。

**2. 流程图 (Mermaid Flowchart)**

```mermaid
flowchart TD
    subgraph 初始状态
        A["查看题目\n(题干+作答区)"]
    end

    subgraph 作答状态
        B["选择选项\n(可选择1个或多个)"]
        C["不确定"]
        D["提交"]
        E["放弃作答\n(二次确认)"]

        subgraph 作答反馈
            F["回答正确"]
            G["回答部分正确\n(漏选)"]
            H["回答错误\n(全错、部分错、多选)"]
            I["未作答"]
        end
    end

    subgraph 解析状态
        J["查看答案、题目解析"]
        K["猜你想问"]
        L["点击推荐问题"]
        M["点击自由提问"]
        N["答疑 chat"]
    end

    A --> B
    A --> C

    C -.-> B
    C --> E

    E -.-> B
    E --> I

    B --> D

    D --> F
    D --> G
    D --> H

    F --> J
    G --> J
    H --> J
    I --> J

    J --> K
    K --> L
    K --> M
    L --> N
    M --> N
```
【============== 图片解析 END ==============】



<table>
<tr>
<td>**状态**<br/></td><td>**原型图**<br/></td><td>**需求说明**<br/></td></tr>
<tr>
<td>**初始状态**<br/><br/></td><td>![in_table_OD1Zbr8o7oZVuwxZJSic0gvtnRh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OD1Zbr8o7oZVuwxZJSic0gvtnRh.png)<br/><br/><br/></td><td>**状态判断：**<br/>- 当进入题目且没有作答时（即多选题没有选中选项）为初始状态。<br/>**题型校验：**<br/>- 学科：语文、数学、英语、物理、化学、生物。<br/>- 录入题目作答方式为：多选题。<br/>- 非母子题。<br/>**页面框架**<br/>- 仅支持全屏展示。<br/>- 退出学习：- 按钮名称：退出学习。- 交互：点击后退出至，xx 页面。<br/>- 作答计时：- 每题独立正计时（格式 00:00，上限 59:59）。- 用户进入题目开始计时，提交后停止计时。- 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时（例一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。<br/>- 题板结构：- 左侧为题干区、右侧为作答区。<br/>- 题干区：- 题目标签：多选。- 题目来源：数据源自题库中已录入题目，如“2024 春•浙江期中”。- 题干内容：- 数据源自题库中已录入题目。- 题干支持展示文字、图片（支持点开放大）。- 图片排版处理：![in_table_WcCTbFpfIoG2suxgKFTcSibenIf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WcCTbFpfIoG2suxgKFTcSibenIf.png)- 作答区：- 选项，同上：- 选项内容：数据源自题库中已录入题目。- 超出一行后，折行展示。![in_table_OssBbq2HhojYHSxhgbZcH2L0nye](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OssBbq2HhojYHSxhgbZcH2L0nye.png)- 含图选项处理：![in_table_OSIIb8blyo9dXRxLIyTckAR0nsb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OSIIb8blyo9dXRxLIyTckAR0nsb.png)- 操作，同上：- 【不确定】：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 点击【选项】，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 【提交】：- 按钮状态：不可点击，按钮样式置灰。- 交互：/<br/>- **勾画组件：**![in_table_SBribFrWVodELRxfTJvcZ6Tmnbh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SBribFrWVodELRxfTJvcZ6Tmnbh.png)- 入口：作答界面悬浮【勾画】按钮（常驻显示）。- 交互：支持用户在作答环节时，点击【勾画】按钮唤起勾画组件，对题目进行勾画和标记。- 勾画工具。- 笔迹调整：- 颜色调整：支持蓝色、红色、黑色。- 粗细调整：细、中等、粗。- 橡皮擦：- 粗细调整：细、中等、粗。- 撤回、恢复。- 一键清空。- 交互：- 点击【完成】，收起勾画组件，同时将勾画的内容自动保存并展示。<br/></td></tr>
<tr>
<td>**作答状态**<br/></td><td>![in_table_JjnbbhnpwogcHfxn8cucByuIn5d](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_JjnbbhnpwogcHfxn8cucByuIn5d.png)<br/><br/><br/></td><td>**状态判断：**<br/>- 当进入题目且有作答时（即单选题选中唯一选项）为作答状态。<br/>**批改方式：**<br/>- 自动批改<br/>**作答方式：**<br/>- 选项：- 支持选中一个或多个选项，并展示选中状态。- 当有已选中选项时，再次点击该选项，则取消选中状态。点击其他未选中选项，则选中点击的选项。<br/>- 操作：- 不确定，[同初始状态](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd#share-CIA8dBUyVoBjKPx3Meccu02LnWg)：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 点击【选项】，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 提交：- 按钮状态：可点击。- 交互：- 第一次点击提交，点击后作答数据，校验作答结果是否正确。- 正确：- 反馈：IP + 积分反馈 + 文案。- 进入解析状态，展示解析状态内容。- 错误，- 反馈：IP + 文案。- 停留在作答状态。仅展示猜你想问入口，用户可自主点击，不展示答案、解析等。- 重置作答，取消选中状态，用户可重新作答。- 特殊校验：当提交时，选中选项仅为 1 时，弹窗二次确认。- 弹窗内容：多选题答案有多个。- 操作按钮：- 我再想想，点击后关闭弹窗。- 继续提交，点击后提交作答，进入解析状态。- 第二次点击提交，点击后再次提交作答数据，校验作答结果是否正确。- 正确：- 反馈弹窗。- 进入解析状态，展示解析状态内容。- 错误：- 反馈弹窗。- 进入解析状态，展示解析状态内容。- 特殊校验：当提交时，选中选项仅为 1 时，弹窗二次确认。- 弹窗内容：多选题答案有多个。- 操作按钮：- 我再想想，点击后关闭弹窗。- 继续提交，点击后提交作答，进入解析状态。<br/></td></tr>
<tr>
<td>**解析状态**<br/></td><td>![in_table_ExPobyMKgof0mVxHRglcLpCVnFf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ExPobyMKgof0mVxHRglcLpCVnFf.png)<br/><br/>![in_table_XaGGbFPFcoRya1xzU0Ucr13PnOc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XaGGbFPFcoRya1xzU0Ucr13PnOc.png)<br/><br/>![in_table_WZ5Obcouco1ajex01pocVqSPnRc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WZ5Obcouco1ajex01pocVqSPnRc.png)<br/><br/><br/></td><td>**状态判断：**<br/>- 当题目有作答（即选中选项）并点击【提交】后为解析状态。<br/>- 当题目没有作答，但点击了【不确定】 - 【放弃作答】后为解析状态。<br/>**页面框架：**<br/>- 作答计时：- 停止计时，时间定格在提交时。<br/>- 反馈弹窗：见，[巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f)。<br/>- 答案：- 回答正确时，选中正确选项颜色变更为绿色。- 回答部分正确（漏选）时，正确选项变更为绿色，未选中的正确选选项展示漏选标签。- 回答错误时，- 全错时，选错选项颜色变更为红色，将正确选项变更为绿色。- 部分错时，选错选项颜色变更为红色，未选中的正确选项变更为绿色并展示漏选标签。- 多选时，正确选项变更为绿色，多选中的错误选项变更为红色。- 不确定 - 放弃作答时，正确选项变更为绿色。<br/>- 题目解析：- 内容：数据源自题库中已录入题目，对应字段【题目解析】。<br/>- 问一问，同上：- 入口：右上角问一问 icon ，仅当题目进入解析状态后展示入口。- 交互：-  点击弹出答疑组件。- 展示： 支持自由提问 + 模型生成的猜你想问问题（最多 3 个 可以为 0 ）。- 由用户发起首轮对话。<br/>- 操作按钮：- 继续：- 出现时机：题目进入解析状态。- 按钮状态：可点击。- 交互：点击后进入下一题/下一节内容。<br/></td></tr>
</table>

![in_table_OD1Zbr8o7oZVuwxZJSic0gvtnRh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OD1Zbr8o7oZVuwxZJSic0gvtnRh.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线答题界面的截图，具体元素构成分析如下：

**1. 顶部状态与导航区域:**
    *   **1.1 返回按钮:** 位于左上角，图标为向左的尖括号 "<"。
    *   **1.2 答题计时:** 显示 "用时 01:24"。
    *   **1.3 答题进度条:** 位于计时右侧，由橙色和浅灰色组成，指示答题进度。

**2. 题目内容区域:**
    *   **2.1 题目元信息:**
        *   题型: "单选"
        *   来源: "(2024春·浙江期中)"
    *   **2.2 题干文本:** "在四面体ABCD中，BCD为等边三角形ADB=pai/2，二面角B-AD-C的大小为a，则a的取值范围是（ ）"
        *   其中数学条件可表示为：
            *   BCD为等边三角形
            *   $\angle ADB = \pi/2$ (OCR原文: ADB=pai/2)
            *   二面角B-AD-C的大小为a
            *   求解：a的取值范围
    *   **2.3 辅助几何图形:**
        *   一个四面体的示意图，顶点标注为A, B, C, D。线段BD为虚线。
        *   图形右下角有一个放大镜图标 "Q"。

**3. 答题选项区域 (垂直排列):**
    *   **3.1 选项A:** 文字内容为 "A (0, pai/6]"，数学表示为：
        $$
        (0, \pi/6]
        $$
    *   **3.2 选项B:** 文字内容为 "B (0, pai/6]"，数学表示为：
        $$
        (0, \pi/6]
        $$
    *   **3.3 选项C:** 文字内容为 "C (0, pai/6]"，数学表示为：
        $$
        (0, \pi/6]
        $$
    *   **3.4 选项D:** 文字内容为 "D (0, pai/6]"，数学表示为：
        $$
        (0, \pi/6]
        $$

**4. 底部操作按钮区域:**
    *   **4.1 勾画工具:** 位于左下角，图标为铅笔，配文 "勾画"。
    *   **4.2 不确定按钮:** 按钮文字为 "不确定"。
    *   **4.3 提交按钮:** 按钮文字为 "提交"，背景色为橙色，位于 "不确定" 按钮右侧。

【============== 图片解析 END ==============】

![in_table_WcCTbFpfIoG2suxgKFTcSibenIf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WcCTbFpfIoG2suxgKFTcSibenIf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片展示了“图片组合”的几种不同布局方案。

*   **顶层标题**: “图片组合”
    *   **布局方案一 (单图展示)**:
        *   包含一个图片占位符。
        *   图片占位符内标注文字 “2 : 3”，表示宽高比。
        *   图片占位符右下角有放大镜图标。
    *   **布局方案二 (双图展示)**:
        *   标题: “双图”
        *   包含两个水平并列的图片占位符。
        *   每个图片占位符内标注文字 “2 : 3”，表示宽高比。
        *   每个图片占位符右下角有放大镜图标。
    *   **布局方案三 (3图展示)**:
        *   标题: “3图”
        *   包含三个图片占位符，排列方式为：
            *   上部：一个较大的图片占位符。
            *   下部：两个较小的图片占位符，水平并列。
        *   每个图片占位符内标注文字 “2 : 3”，表示宽高比。
        *   每个图片占位符右下角有放大镜图标。
    *   **布局方案四 (4图展示)**:
        *   标题: “4图”
        *   包含四个图片占位符，排列为2x2网格。
        *   每个图片占位符内标注文字 “2 : 3”，表示宽高比。
        *   每个图片占位符右下角有放大镜图标。

【============== 图片解析 END ==============】

![in_table_OssBbq2HhojYHSxhgbZcH2L0nye](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OssBbq2HhojYHSxhgbZcH2L0nye.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线答题界面的用户界面（UI）。

1.  **顶部状态栏**
    *   1.1. 左上角显示当前时间与日期：“7:35 Mon Jun 3”
    *   1.2. 右上角显示系统状态：Wi-Fi信号图标和电池电量“100%”

2.  **导航与进度区域**
    *   2.1. 左侧为返回按钮： “<” 图标
    *   2.2. 返回按钮右侧显示用时：“用时 01:24”
    *   2.3. 顶部中央为水平进度条：橙色部分表示已完成进度，灰色部分表示未完成进度。

3.  **题目内容区域**
    *   3.1. **题目信息**
        *   3.1.1. 题型：“单选”
        *   3.1.2. 题目来源：“(2024春·浙江期中)”
    *   3.2. **题干**
        *   3.2.1. 文字描述：“在四面体ABCD中，BCD为等边三角形ADB=π/2，二面角B-AD-C的大小为α，则α的取值范围是（ ）”
            *   数学表达：
                $$
                \angle ADB = \frac{\pi}{2}
                $$
        *   3.2.2. 辅助图形：一个四面体ABCD的示意图，其中顶点B和D之间的连线为虚线。
    *   3.3. **选项区域**
        *   3.3.1. 选项A：文字内容为“选项过长展示效果选项过长展示效果选项过长展示效果选项过长展示效果”
        *   3.3.2. 选项B：
            $$
            [0, \frac{\pi}{6}]
            $$
        *   3.3.3. 选项C：
            $$
            (0, \frac{\pi}{6}]
            $$
        *   3.3.4. 选项D：
            $$
            (0, \frac{\pi}{6}]
            $$

【============== 图片解析 END ==============】

![in_table_OSIIb8blyo9dXRxLIyTckAR0nsb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OSIIb8blyo9dXRxLIyTckAR0nsb.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线答题界面的截图，主要构成元素如下：

1.  **顶部状态栏/导航栏**:
    *   左上角: 返回按钮（`<`图标）。
    *   中间:
        *   计时器: 显示 "用时01:24"。
        *   答题进度条: 橙色部分表示已完成或当前进度。
    *   右上角: (系统状态信息，如时间 "7:35 Mon Jun 3", Wi-Fi, 电池 "100%")

2.  **题目区域**:
    *   **题型及来源**: "单选 (2024春·浙江期中)"。
    *   **题干**:
        "在四面体ABCD中，BCD为等边三角形，$$ \angle ADB = \frac{\pi}{2} $$，二面角B-AD-C的大小为 $$ \alpha $$，则 $$ \alpha $$ 的取值范围是（ ）"

3.  **选项区域 (2x2网格布局)**:
    *   **选项A**:
        *   标签: "A"
        *   内容: "$$(0, \frac{\pi}{6}]$$"
        *   配图: 一个四面体ABCD的示意图，右下角有放大镜图标。
    *   **选项B**:
        *   标签: "B"
        *   内容: "$$(0, \frac{\pi}{6}]$$"
        *   配图: 一个四面体ABCD的示意图，右下角有放大镜图标。
        *   状态: 该选项当前被选中（背景色与其他选项不同，呈浅蓝色）。
    *   **选项C**:
        *   标签: "C"
        *   内容: "$$(0, \frac{\pi}{6}]$$"
        *   配图: 一个四面体ABCD的示意图，右下角有放大镜图标。
    *   **选项D**:
        *   标签: "D"
        *   内容: "$$(0, \frac{\pi}{6}]$$"
        *   配图: 一个四面体ABCD的示意图，右下角有放大镜图标。

4.  **底部操作栏**:
    *   左侧边缘中部: "勾画" 工具图标（铅笔和纸张）。
    *   右下角:
        *   "不确定" 按钮。
        *   "提交" 按钮 (橙色高亮)。

**元素间关联**:
*   顶部状态栏提供导航和答题状态信息。
*   题目区域展示当前需解答的问题。
*   选项区域提供多个备选答案，用户可点选其一。每个选项由文本和辅助理解的几何图形构成。
*   底部操作栏提供辅助工具（勾画）和答题控制功能（不确定、提交）。

【============== 图片解析 END ==============】

![in_table_SBribFrWVodELRxfTJvcZ6Tmnbh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SBribFrWVodELRxfTJvcZ6Tmnbh.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线学习或答题界面的用户界面（UI）。

1.  **顶部导航栏**
    *   **左侧**：
        *   返回按钮（左箭头图标）与文字“退出学习”。
    *   **中间**：
        *   计时器显示：“用时 00:10”。
    *   **右侧**：
        *   按钮：“学习地图”。
        *   按钮：“反馈”。

2.  **题目区域**
    *   **题型**：标签“单选”。
    *   **题干**：“关于空间几何，以下哪个描述是正确的？”。
    *   **选项**：
        *   A. “打点计时器”
        *   B. “打点计时器”
        *   C. “打点计时器”
        *   D. “打点计时器”

3.  **底部悬浮工具栏**
    *   **颜色选择区**：
        *   圆形颜色按钮：蓝色（当前选中）、橙色、黑色。
    *   **笔触样式/粗细选择区**：
        *   三个图标，分别代表不同的笔触样式或粗细（例如，第一个为细笔触，第二个为中等笔触，第三个为粗笔触或毛笔样式）。
    *   **工具按钮区** (从左至右)：
        *   笔形图标（当前选中，显示为蓝色笔迹示例）。
        *   斜杠图标（橡皮擦）。
        *   笔刷/荧光笔图标。
        *   垃圾桶图标（删除）。
        *   向左弯曲箭头图标（撤销）。
        *   向右弯曲箭头图标（重做）。
    *   **操作按钮**：
        *   按钮：“完成”。

【============== 图片解析 END ==============】

![in_table_JjnbbhnpwogcHfxn8cucByuIn5d](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_JjnbbhnpwogcHfxn8cucByuIn5d.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为在线答题界面的用户界面截图。

**1. 屏幕顶层信息**
    *   **设备状态栏**: 显示时间 "7:35 Mon Jun 3" 及网络/电量图标 (均显示为100%或满格状态)。
    *   **应用导航栏**:
        *   **返回按钮**: 位于左上角，图标为 ` < `。
        *   **答题计时**: 显示 "用时01:24"。
        *   **答题进度条**: 位于计时右侧，橙色填充。

**2. 题目区域**
    *   **题型与来源**: 标签 "单选"，来源 " (2024春·浙江期中)"。
    *   **题干内容**:
        *   **文字描述**: "在四面体ABCD中，BCD为等边三角形ADB=pai/2，二面角B-AD-C的大小为a，则a的取值范围是（ ）"
            *   数学条件 "ADB=pai/2" 解析为：
                $$
                \angle ADB = \pi/2
                $$
        *   **几何图形**:
            *   一个四面体ABCD的线框图。
            *   顶点标注：A, B, C, D。
            *   线段BD以虚线绘制。
            *   右下角附有放大镜图标，提示可查看大图。
    *   **辅助工具**:
        *   **"勾画"按钮**: 位于题目区域左下方，带铅笔图标。

**3. 选项区域** (各选项垂直排列)
    *   **选项A**: 标签 "A"，内容为数学区间 (源自OCR文本 "(0, pai/6]")：
        $$
        (0, \pi/6]
        $$
    *   **选项B**: 标签 "B"，内容为数学区间 (源自OCR文本 "(0, pai/6]")：
        $$
        (0, \pi/6]
        $$
    *   **选项C**: 标签 "C"，内容为数学区间 (源自OCR文本 "(0, pai/6]")：
        $$
        (0, \pi/6]
        $$
    *   **选项D**: 标签 "D"，内容为数学区间 (源自OCR文本 "(0, pai/6]")：
        $$
        (0, \pi/6]
        $$

**4. 底部操作区域**
    *   **"不确定" 按钮**
    *   **"提交" 按钮** (橙色，表示主要操作)

【============== 图片解析 END ==============】

![in_table_ExPobyMKgof0mVxHRglcLpCVnFf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ExPobyMKgof0mVxHRglcLpCVnFf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

### 1. 整体描述
该图片展示了一个在线教育应用中的数学单选题作答或回顾界面。界面包含了状态栏、题目区域（含题干文字、几何图形）、选项区域、答案解析区域以及底部操作按钮。

### 2. 界面元素层级结构

#### 2.1. 顶部状态栏
*   **左侧**:
    *   **时间戳**: `7:35 Mon Jun 3`
    *   **返回按钮**: `<` 图标
*   **中间**:
    *   **用时提示**: `用时01:24`
    *   **进度条**: 橙色指示当前进度
*   **右侧**:
    *   **网络状态**: Wi-Fi 图标
    *   **电量**: `100%` 及电池图标

#### 2.2. 题目区域
*   **题目元信息**:
    *   **类型**: `单选`
    *   **来源**: `(2024春·浙江期中)`
*   **题干内容**:
    *   **文字描述**: "在四面体ABCD中, BCD为等边三角形ADB=pai/2, 二面角B-AD-C的大小为a, 则a的取值范围是（ ）"
    *   **几何图形**:
        *   一个四面体（Tetrahedron）ABCD的线框图。顶点 A, B, C, D 已标注。边 BD 以虚线表示。
        *   **图形交互**:
            *   左下角: "显示" 文字提示，旁边是一个铅笔图标。
            *   右下角: 放大镜图标。

#### 2.3. 选项区域
各选项均包含选项字母、内容（数学区间表示）、选择该选项的用户百分比。
*   **选项A**: `A (0, pai/6]` `10.8%`
*   **选项B**: `B (0, pai/6]` `50.2%` (背景高亮为浅绿色，并带有一个绿色勾选符号 ✓)
*   **选项C**: `C (0, pai/6]` `20.9%` (背景高亮为浅绿色，并带有一个绿色勾选符号 ✓)
*   **选项D**: `D (0, pai/6]` `18.1%`

#### 2.4. 答案与解析区域
*   **正确答案提示**: `正确答案: B、C`
*   **题目解析部分**:
    *   **标题**: `题目解析:`
    *   **内容**: "题目解析题目解析题目解析..." (文字内容被截断，未完整显示)

#### 2.5. 底部操作栏
*   **左侧按钮**: `加入错题本` (白色背景，灰色文字)
*   **右侧按钮**: `继续` (橙色背景，白色文字)

### 3. 关键数学元素表述
图片中题干及选项包含的关键数学元素，使用 Markdown 数学公式语法描述如下：
*   题干中的条件 "ADB=pai/2":
    $$
    \text{ADB} = \text{pai}/2
    $$
*   题干中表示二面角大小的变量 "a":
    $$
    a
    $$
*   选项中表示取值范围的格式，例如选项A的内容 "(0, pai/6]":
    $$
    (0, \text{pai}/6]
    $$

【============== 图片解析 END ==============】

![in_table_XaGGbFPFcoRya1xzU0Ucr13PnOc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_XaGGbFPFcoRya1xzU0Ucr13PnOc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线教育产品中的题目练习/作答结果界面。

1.  **顶部状态栏与导航栏**
    *   左上角：时间 “7:35”，日期 “星期一，6月3日”
    *   返回按钮：“<”
    *   用时指示：“用时 01:24”
    *   进度条：橙色，表示已用时长。
    *   右上角：系统图标 (Wi-Fi, 电池100%)

2.  **题目内容区域**
    *   **题型与来源**：
        *   题型：“单选题”
        *   来源：“(2024春·浙江期中)”
    *   **题干**：
        *   文字描述：“在四面体ABCD中，BCD为等边三角形$ADB=\pi/2$，二面角B-AD-C的大小为a，则a的取值范围是（ ）”
        *   配图：一个四面体ABCD的示意图。
    *   **选项区域** (显示用户选择统计及正确性，选项内容均为 $(0, \pi/6]$)：
        *   选项A： `(0, π/6]` - 标记为错误 (红色背景，10.8%选择率，带红色✖️图标)
        *   选项B (OCR识别为A)： `(0, π/6]` - 标记为正确 (绿色背景，50.2%选择率，带绿色✔️图标)
        *   选项C (OCR识别为B)： `(0, π/6]` - 标记为正确 (绿色背景，20.9%选择率，带绿色✔️图标)
        *   选项D： `(0, π/6]` - 未标记对错 (白色背景，18.1%选择率)
    *   **答案与解析**：
        *   正确答案：“正确答案：B、C”
        *   题目解析：“题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析” (占位文字)

3.  **底部操作按钮区域**
    *   按钮：“加入错题本” (浅色背景，深色文字)
    *   按钮：“继续” (橙色背景，白色文字)

4.  **左下角悬浮按钮**
    *   图标：铅笔图标
    *   文字：“显示”

【============== 图片解析 END ==============】

![in_table_WZ5Obcouco1ajex01pocVqSPnRc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_WZ5Obcouco1ajex01pocVqSPnRc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为互联网教育产品中的一个在线做题/题目回顾界面，具体解析如下：

**1. 整体界面布局**
    *   **1.1. 顶部状态栏**
        *   左上角显示当前时间与日期：“7:35 Mon Jun 3”
        *   右上角显示系统状态：Wi-Fi信号图标、电池电量“100%”
        *   **1.2. 页面导航与信息栏**
        *   左侧：返回按钮（“<”图标）
        *   中间：做题用时，“用时01:24”
        *   右侧：进度条（橙色表示已完成部分，灰色表示未完成部分）
        *   **1.3. 主内容区域**
        *   包含题目信息、选项、答案解析。
        *   **1.4. 底部操作栏**
        *   包含功能按钮。

**2. 主内容区域元素**
    *   **2.1. 题目模块 (白色卡片区域)**
        *   **2.1.1. 题目信息**
            *   类型与来源：“单选 (2024春·浙江期中)”
        *   **2.1.2. 题干**
            *   文字描述：“在四面体ABCD中，BCD为等边三角形ADB=pai/2，二面角B-AD-C的大小为a，则a的取值范围是（ ）”
                *   数学表述：在四面体ABCD中，BCD为等边三角形，$$ \angle ADB = \text{pai}/2 $$，二面角B-AD-C的大小为a，则a的取值范围是（ ）
            *   配图：一个四面体ABCD的几何图形。
                *   图像交互：图形右下角有放大镜图标（可能用于缩放查看图片）。
        *   **2.1.3. 左下角按钮**
            *   图标：铅笔编辑/涂鸦图标。
            *   OCR文本提示：附近有独立文本“显示”。
        *   **2.2. 选项模块**
        *   **2.2.1. 选项 A**
            *   内容：“A (0, pai/6]” (数学区间表示：$$(0, \text{pai}/6]$$)
            *   统计数据：“10.8%”
        *   **2.2.2. 选项 B**
            *   内容：“B (0, pai/6]” (数学区间表示：$$(0, \text{pai}/6]$$)
            *   统计数据：“50.2%”
            *   视觉标记：右侧有绿色打勾图标。
        *   **2.2.3. 选项 C**
            *   内容：“C (0, pai/6]” (数学区间表示：$$(0, \text{pai}/6]$$)
            *   统计数据：“20.9%”
            *   视觉标记：右侧有绿色打勾图标。
        *   **2.2.4. 选项 D**
            *   内容：“D (0, pai/6]” (数学区间表示：$$(0, \text{pai}/6]$$)
            *   状态提示：“漏选”
            *   统计数据：“20.9%”
            *   视觉标记：右侧有绿色打勾图标。
        *   **2.3. 答案与解析模块**
        *   **2.3.1. 正确答案**
            *   文本：“正确答案：B、C、D”
        *   **2.3.2. 题目解析**
            *   标题：“题目解析：”
            *   内容：占位文本 “题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析题目解析”

**3. 底部操作栏按钮**
    *   **3.1. “加入错题本”按钮**
        *   白色背景，文字为“加入错题本”。
    *   **3.2. “继续”按钮**
        *   橙色背景，文字为“继续”。

【============== 图片解析 END ==============】



### 填空题

![Z1QewOMCqhfuZBbNuDicojFknZg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_Z1QewOMCqhfuZBbNuDicojFknZg.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

该图片描述了用户在互联网教育产品中针对“其他学科（非自动批改，自评）”题目进行作答、查看解析、自评及提问的流程。

**关键元素与组成部分分析如下：**

此流程主要包含三个阶段：

1.  **题目作答阶段：**
    *   **查看题目 (题干+作答区):** 用户开始交互的入口，展示题目内容和作答区域。
    *   **填空作答区 输入内容:** 用户进行答案输入的区域。
    *   **不确定:** 用户在查看题目后，若不确定如何作答，可选择此路径。
        *   从此状态，用户可选择返回“填空作答区 输入内容”（图中虚线表示）。
        *   或选择“放弃作答”。

2.  **行为确认与状态转换阶段：**
    *   **提交:** 用户完成作答后提交答案。
    *   **放弃作答 (二次确认):** 用户选择不作答并进行确认。
    *   **未作答:** 用户放弃作答后的状态。

3.  **结果反馈与延伸学习阶段：**
    *   **查看答案、题目解析:** 用户在提交答案或放弃作答后，查看标准答案和题目解析。
    *   **自评:** 用户对照答案进行自我评价。
        *   **回答正确**
        *   **部分错误**
        *   **回答错误**
    *   **猜你想问:** 系统提供相关问题引导或自由提问入口。
        *   **点击推荐问题**
        *   **点击自由提问**
        *   **答疑 chat:** 最终引导至答疑机器人或人工客服。

**元素间关联（流程图）：**

```mermaid
flowchart TD
    A["查看题目<br/>(题干+作答区)"] --> B["填空作答区<br/>输入内容"];
    A --> D["不确定"];

    B --> C["提交"];
    C --> G["查看答案、题目<br/>解析"];

    D -.-> B;
    D --> E["放弃作答<br/>(二次确认)"];

    E --> G; 
    E --> F["未作答"];
    F --> G;

    G --> H["自评"];
    H --> I["回答正确"];
    H --> J["部分错误"];
    H --> K["回答错误"];

    G --> L["猜你想问"];
    L --> M["点击推荐问题"];
    L --> N["点击自由提问"];
    M --> O["答疑 chat"];
    N --> O;
```

【============== 图片解析 END ==============】



<table>
<tr>
<td>**状态**<br/></td><td>**原型图**<br/></td><td>**需求说明**<br/></td></tr>
<tr>
<td>初始状态<br/></td><td>![in_table_OX5hbeeyEoyqkbxDAj5c6MbInuc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OX5hbeeyEoyqkbxDAj5c6MbInuc.png)<br/><br/>![in_table_VjJhbuoEqoTsALxEl2Mc0MkGnEc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VjJhbuoEqoTsALxEl2Mc0MkGnEc.png)<br/><br/></td><td>**状态判断：**<br/>- 当进入题目且没有作答时为初始状态。<br/>**题型校验：**<br/>- 录入学科：语文、数学、物理、化学、生物。<br/>- 录入题目作答方式为：填空题。<br/>- 非母子题。<br/>**页面框架**<br/>- **仅支持全屏展示。**<br/>- **退出学习：**- 按钮名称：退出学习。- 交互：点击后退出至，xx 页面。<br/>- **作答计时：**- 每题独立正计时（格式 00:00，上限 59:59）。- 用户进入题目开始计时，提交后停止计时。- 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时（例一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。<br/>- **题板结构：**- 左侧为题干区、右侧为作答区。<br/>- **题干区：**- 题型标签：- 题型标签：填空 。- 题目来源：数据源自题库中已录入题目，如“2024 春•浙江期中”。- 题干内容：- 数据源自题库中已录入题目。- 题干支持展示文字、图片和作答区。- 如果题干存在多个作答区，作答区需标记顺序，从左到右从上到下标记 1 、2 、3 ...... 。- 点击题干上作答区标记，右侧作答区自动定位到选中位置。<br/>- **作答区：**- 填空区域：以输入框的形式插入题干中。- 长度：以答案长度作为作答区输入框长度。- 插入位置：数据源自题库中已录入题目。- 作答区选项卡：- 对应题干中的作答区数量和顺序，只有一个空时，不展示选项卡。- 点击作答区选项卡中的选项，左侧题干区自动定位到选中位置。- 不确定：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 点击【选项】，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 提交：- 按钮状态：不可点击，按钮样式置灰。- 交互：/<br/>- **勾画组件：**![in_table_AMqAbcFa3oYtQBxBQKNcVBeHnEh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AMqAbcFa3oYtQBxBQKNcVBeHnEh.png)- 入口：作答界面悬浮【勾画】按钮（常驻显示）。- 交互：支持用户在作答环节时，点击【勾画】按钮唤起勾画组件，对题目进行勾画和标记。- 勾画工具。- 笔迹调整：- 颜色调整：支持蓝色、红色、黑色。- 粗细调整：细、中等、粗。- 橡皮擦：- 粗细调整：细、中等、粗。- 撤回、恢复。- 一键清空。- 交互：- 点击【完成】，收起勾画组件，同时将勾画的内容自动保存并展示。<br/></td></tr>
<tr>
<td>作答状态<br/><br/></td><td>![in_table_BnRUbC553og3KZxcBRvcxjsUnGg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BnRUbC553og3KZxcBRvcxjsUnGg.png)<br/><br/>![in_table_YxHrbOcU6oqZT0xqByEc7trInNe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_YxHrbOcU6oqZT0xqByEc7trInNe.png)<br/><br/>![in_table_VZF3bsNjQot6Pxxk2kvcRIF0nX7](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VZF3bsNjQot6Pxxk2kvcRIF0nX7.png)<br/>![in_table_Ne0Gb7W5voQfHrxhdO6cVe6Vnub](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Ne0Gb7W5voQfHrxhdO6cVe6Vnub.png)<br/><br/>![in_table_RIdZbg5cRoilSkxc0SAcjh2kn8b](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RIdZbg5cRoilSkxc0SAcjh2kn8b.png)<br/><br/>![in_table_GUhdbGw2HoYAktxdBOWcQMCQnVd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GUhdbGw2HoYAktxdBOWcQMCQnVd.png)![in_table_Zvd7bYTEbofuV5x2KLacKUgmn8f](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Zvd7bYTEbofuV5x2KLacKUgmn8f.png)<br/>![in_table_EKFDbnuipoQDyNxtKK2cD2Hbnhg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EKFDbnuipoQDyNxtKK2cD2Hbnhg.png)<br/><br/><br/></td><td>**状态判断：**<br/>- 当进入题目且有至少一道题目作答时为作答状态。<br/><br/>**作答方式（单题内仅可选择一种方式作答）：**<br/>- 切换作答方式时，- 如果当前作答方式下已经有内容，切换作答时，展示最新的作答方式。- 键盘作答切到拍照作答，题干上清空已作答内容，但并不删除内容。方便用户从拍照作答切回键盘作答时无需重新作答。- 拍照作答切到键盘作答，不删除内容。方便用户从键盘作答切回拍照作答时无需重新作答。<br/>- 键盘作答（默认）：- 入口：选择键盘作答。- 提示：点击输入答案。- 支持通过键盘输入文字，将内容填充至填空的对应位置，具体交互规则如下：- 点击左侧题干区域的作答区标记或右侧作答区域的“点击输入答案”，该区域展示选中状态，并自动调起键盘，支持用户输入文字。- 选项卡作答区序号选中，题干中作答区序号选中。-  输入文字时，作答区不展示输入内容，而是在键盘上方新增一个展示区域用于呈现输入的文字。当输入文字超过一行时，自动换行以完整展示内容，且此区域最多展示3行文字。- 点击【确定】按钮，或者点击键盘区域外的其他区域（但不包括其他填空作答区）时，键盘收起，填空区域取消选中状态，系统自动保存刚刚输入的文字。- 若点击键盘外其他区域时，恰好点击到某一个作答区，此时键盘不收起，且键盘上的输入框展示该对应作答区已输入的内容 。 <br/>- 拍照：支持用户通过拍照作答作答内容。- 入口：选择拍照作答模式。- 最大上传数量：3 张。- 交互：- 点击拍照作答功能后，调起摄像头并进入拍摄界面。- 点击右上角“x”，可关闭拍摄界面。- 拍摄界面增加比例为16:9的长方形辅助线，以辅助用户构图。- 点击 x 关闭拍摄，点击拍摄键完成拍摄后，用户调整图片框选位置和大小，确认上传结果。- 点击上传，把拍摄的图片上传至作答区。 - 点击返回，回到拍摄界面。- 点击旋转，顺时逆旋转图片  90 度。![in_table_BkKIbv3lrofEY9xsFeqcjyBVnHf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BkKIbv3lrofEY9xsFeqcjyBVnHf.png)- 上传过程展示“上传中”状态，上传失败时，展示“上传失败 点击重试），点击后执行重新上传的流程。![in_table_G967bbzgsoddEJxIow8cNa44nBg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G967bbzgsoddEJxIow8cNa44nBg.png)- 已上传图片支持放大全屏查看、支持删除。- 已上传内容检测：**接入大模型进行识别**。- 识别内容为异常时，不保存图片， 弹窗提示“请上传和题目相关的内容”，3 秒后消失。<br/>- 操作按钮：- 不确定，[同初始状态](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd#share-CIA8dBUyVoBjKPx3Meccu02LnWg)：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 再次输入内容后，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 提交：- 按钮状态：可点击。- 交互（需校验）：- 点击时，当有上传的图片（拍照作答）/填空区域都有文字输入（文字作答），提交作答数据，题目进入解析状态，展示解析状态内容。- 点击时，当没有上传的图片（拍照作答）/有填空区域没有文字输入（文字作答）时内容，弹窗确认是否继续提交，继续提交，题目进入解析状态，展示解析状态内容。- 全部空为空时，- 弹窗内容：本题未作答，确认提交吗？- 操作按钮：- 我再想想，点击后关闭弹窗。- 继续提交，点击后提交作答，进入解析状态- 部分空为空时，- 弹窗内容：还有填空未作答，确认提交吗？- 操作按钮：- 我再想想，点击后关闭弹窗。- 继续提交，点击后提交作答，进入解析状态![in_table_BKbZbJQxdoQ7GaxW7hqc96ymnVc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BKbZbJQxdoQ7GaxW7hqc96ymnVc.png)<br/></td></tr>
<tr>
<td>解析状态<br/></td><td>![in_table_LrOQbLpKyo8y2Axm6a4cbZgYnmh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LrOQbLpKyo8y2Axm6a4cbZgYnmh.png)<br/><br/><br/><br/><br/></td><td>**状态判断：**<br/>- 当题目有作答并点击【提交】后为解析状态。<br/>- 当题目没有作答，点击了【不确定】 - 【放弃作答】后为解析状态。<br/>**批改方式：**<br/>- 自评<br/>**展示内容：**<br/>- 自评：- 标题：核对答案- 提示：对待错误是进步的第一步，相信你会认真对待!- 自评题目减少掌握度和积分赋予。- 异常行为监控：- 连续 3 题，批改时长均小于 2 秒时， toast 提示“认真核对哦”，展示时长 2 s。![in_table_SD3zblHhDoNnHDxG6a0cFKXTnRd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SD3zblHhDoNnHDxG6a0cFKXTnRd.png)- 选项：- 作答区选项卡：- 对应题干中的作答区数量和顺序，只有一个空时，不展示选项卡。- 点击作答区选项卡中的选项，左侧题干区自动定位到选中位置。- 自评后展示选项看展示对应的自评结果。![in_table_G0CRbeT6YoeD0gxcg9ncZerynGk](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G0CRbeT6YoeD0gxcg9ncZerynGk.png)- 符号 + 我答对了，代表我的答案和标准答案一致。- 符号 +部分答对，代表我的答案和标准答案部分一致。- 符号 +我答错了，代表我的答案和标准答案不一致。- 交互：- 点击不同选项按钮，展示对应的选中效果。- 单选逻辑，用户可以点击自评选项后自动进入下一个待自评的题目，如果是最后一题时，自评后判断是否有前置的作答区未自评，自动跳转到第一个未自评的作答区选项。- 作答区选项卡展示自评结果，四种状态待自评、答对、答错、部分答对。- 如果用户有自评未完成，提交按钮不可以点击。- 提交后展示自评结果，此时自评结果仍然可修改。- 提交后进行作答反馈，见[巩固练习](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f)。- 点击【继续】进入下一题/下一课。<br/>- 题目解析：- 内容：数据源自题库中已录入题目，对应字段【题目解析】。<br/>- 问一问，同上：- 入口：右上角问一问 icon ，仅当题目进入解析状态后展示入口。- 交互：-  点击弹出答疑组件。- 展示： 支持自由提问 + 模型生成的猜你想问问题（最多 3 个 可以为 0 ）。- 由用户发起首轮对话。<br/>- 操作按钮：- 继续：- 出现时机：题目进入解析状态。- 按钮状态：可点击。- 交互：点击后进入下一题/下一节内容。<br/></td></tr>
</table>

![in_table_OX5hbeeyEoyqkbxDAj5c6MbInuc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OX5hbeeyEoyqkbxDAj5c6MbInuc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线答题界面的用户界面截图。

1.  **顶部区域**：
    *   **状态栏信息**（屏幕最顶部）：
        *   左侧：显示当前时间 "7:35 Mon Jun 3"。
        *   右侧：显示电量 "100%" 及网络状态图标。
    *   **导航与计时**（内容区顶部）：
        *   左侧：返回按钮（"<" 图标）。
        *   中间：显示 "用时01:24"，其下方有一条橙色的横向进度条。

2.  **主要内容区域**（分为左右两栏）：
    *   **左栏：题目展示区**
        *   **题目元信息**：
            *   题型："单选"。
            *   来源/分类："(2024春·浙江期中)"。
        *   **题目内容模块** （以卡片形式呈现）：
            *   **标题**："`《宗子相集》序`"。
            *   **题干文本**：显示一段文言文，其中包含两个挖空处，分别用 `①` 和 `②` 标示：
                *   "元中，武陵人捕鱼为业。缘溪行，忘路之远近。忽逢桃花林，①，中无杂树，芳草鲜美，落英缤纷。渔人甚异之，复前行，欲穷其林。"
                *   "水源，便得一山，山有小口，仿佛若有光。便舍船，从口入。初极狭，才通人。复行数十步，豁然开朗。土地平旷，屋舍俨然，②。"
                *   "阡陌交通，鸡犬相闻。其中往来种作，男女衣着，悉如外人。黄发垂髫，并怡然自乐。"
                *   "人，乃大惊，问所从来。具答之。便要还家，设酒杀鸡作食。村中闻有此人，咸来问讯。自云先世避秦时乱，率妻子邑人来此绝境，不复出焉，遂与外人间隔。问今是何世，乃不知有汉，无论魏晋。此人一一"
    *   **右栏：答题互动区**
        *   **提示信息**："`请认真作答，老师会检查作答结果`"。
        *   **题号导航**：水平排列的圆形按钮，分别标有数字 "1"、"2"、"3"、"4"、"5"、"6"、"7"、"8"。其中 "1" 号按钮被选中并高亮显示。
        *   **答案输入区域**：一个较大的矩形区域，内部有占位提示文字 "`点击输入答案`"。
        *   **操作按钮组**：
            *   "`不确定`" 按钮（白色背景，灰色文字）。
            *   "`提交`" 按钮（橙色背景，白色文字）。

【============== 图片解析 END ==============】

![in_table_VjJhbuoEqoTsALxEl2Mc0MkGnEc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VjJhbuoEqoTsALxEl2Mc0MkGnEc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线答题界面。

### 1. 页面整体结构
*   **顶部区域**: 包含系统状态栏和应用导航栏。
*   **内容区域**: 左侧为题目文本，右侧为答题方式选择和提示。
*   **底部区域**: 操作按钮。

### 2. 关键元素及组成

#### 2.1. 系统状态栏 (最顶部)
*   **时间**: "Mon Jun 3"
*   **系统图标**: Wi-Fi 和电池图标。

#### 2.2. 应用导航栏
*   **返回按钮**: "<" 图标。
*   **计时器**: "用时01:24"。
*   **进度条**: 橙色指示条。

#### 2.3. 题目区域 (左侧主体)
*   **题目信息**: "单选 (2024春·浙江期中)"。
*   **文章标题**: "《宗子相集》序"。
*   **文章段落**:
    1.  "元中，武陵人捕鱼为业。缘溪行，忘路之远近。忽逢桃花林，[此处有一个填空框]，中无杂树，芳草鲜美，落英缤纷。渔人甚异之，复前行，欲穷其林。"
    2.  "水源，便得一山，山有小口，仿佛若有光。便舍船，从口入。初极狹，才通人。复行数十步，豁然开朗。土地平旷，屋舍俨然，[此处有一个填空框]。阡陌交通，鸡犬相闻。其中往来种作，男女衣着，悉如外人。黄发垂髫，并怡然自乐。"
    3.  "人乃大惊，问所从来。具答之。便要还家，设酒杀鸡作食。村中闻有此人，咸来问讯。自云先世避秦时乱，率妻子邑人来此绝境，不复出焉，遂与外人间隔。问今是何世，乃不知有汉，无论魏晋。此人一一为具言所闻，皆叹惋。余人各复延至其家，皆出酒食。停数日，辞去。此中人语云：“不足为外人道也。”"

#### 2.4. 答题操作区 (右侧)
*   **提示文本**: "请认真作答，老师会检查作答结果"。
*   **答题方式切换**:
    *   **键盘输入**: 按钮，当前未选中。
    *   **拍照作答**: 按钮，当前选中状态 (高亮)。
*   **拍照上传模块**:
    *   图标: 相机图标。
    *   文本: "拍照上传"。
    *   辅助文本: "最多可以传4张"。

#### 2.5. 底部操作按钮
*   **不确定按钮**: "不确定"。
*   **提交按钮**: "提交"。

### 3. 元素间关联
*   应用导航栏中的“用时”和“进度条”反映当前答题的状态。
*   题目区域的文章段落中包含两个填空框，是用户需要作答的位置。
*   答题操作区的“键盘输入”和“拍照作答”是两种互斥的答题方式。
*   当前选择“拍照作答”时，“拍照上传模块”可见，允许用户上传图片作为答案。
*   用户作答完毕后，可通过底部的“提交”按钮提交答案，或通过“不确定”按钮进行标记。

【============== 图片解析 END ==============】

![in_table_AMqAbcFa3oYtQBxBQKNcVBeHnEh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_AMqAbcFa3oYtQBxBQKNcVBeHnEh.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线学习答题界面，其关键元素及组成部分如下：

*   **页面整体**
    *   **顶部导航栏**
        *   左侧：
            *   **退出学习按钮**：包含一个向左的箭头图标和文字“退出学习”。
            *   **用时显示**：文本“用时 00:10”。
        *   右侧：
            *   **学习地图按钮**：文字“学习地图”。
            *   **反馈按钮**：文字“反馈”。
    *   **中部内容区域（答题区）**
        *   **题目信息**：
            *   **题目类型**：文字“单选”。
            *   **题干**：文字“关于空间几何，以下哪个描述是正确的？”。
        *   **选项区域**：
            *   **选项A**：文字“A 打点计时器”。
            *   **选项B**：文字“B 打点计时器”。
            *   **选项C**：文字“C 打点计时器”。
            *   **选项D**：文字“D 打点计时器”。
    *   **底部工具栏（批注/绘图工具）**
        *   **上层工具选项**：
            *   **颜色选择器**：包含三个圆形颜色选项：
                *   蓝色（当前选中状态，有蓝色外圈指示）。
                *   橙色。
                *   黑色。
            *   **笔触样式选择器**：包含三个不同粗细的笔触示例图标。
        *   **下层工具按钮**：
            *   **画笔工具**：一个画笔图标，当前为蓝色，表示选中状态。
            *   **铅笔/直线工具**：一个铅笔或直线工具的图标。
            *   **橡皮擦工具**：一个橡皮擦图标。
            *   **删除工具**：一个垃圾桶图标。
            *   **撤销按钮**：一个向左的弯曲箭头图标。
            *   **重做按钮**：一个向右的弯曲箭头图标。
            *   **完成按钮**：蓝色背景，白色文字“完成”。

【============== 图片解析 END ==============】

![in_table_BnRUbC553og3KZxcBRvcxjsUnGg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BnRUbC553og3KZxcBRvcxjsUnGg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线答题界面的用户界面截图。

1.  **设备状态栏 (屏幕顶部)**
    *   左侧：显示时间 “7:35 Mon Jun 3”
    *   右侧：显示 Wi-Fi 信号强度图标、电池电量 “100%”

2.  **应用导航与状态区 (设备状态栏下方)**
    *   返回按钮：位于左上角，图标为 "<"
    *   计时信息：“用时 01:24”
    *   答题进度条：位于计时信息下方，橙色条形，显示当前答题进度

3.  **题目展示区 (导航区下方，被键盘输入交互层部分遮挡)**
    *   题目类型及来源：“单选 (2024春·浙江期中)”
    *   篇名：“《宗子相集》序”
    *   题目文本内容：
        “元中，武陵人捕鱼为业。缘溪行，忘路之远近。忽逢桃花林，\_\_\_\_，中无杂树，芳草鲜美，落英缤纷。渔人甚异之，复前行，欲穷其林。”
    *   作答提示：“请认真作答，老师会检查作答结果”
    *   作答方式选项 (灰色，当前非激活状态):
        *   图标与文字：“键盘输入”
        *   图标与文字：“拍照作答”

4.  **键盘输入交互层 (浮于题目展示区之上)**
    *   文本输入框：内含用户已输入文字 “我认为这道题应该这样回答”
    *   确认按钮：“确定”，橙色背景

5.  **虚拟键盘区 (屏幕底部)**
    *   输入法联想词/快捷回复栏：包含 “嗯”、“我”、“你”、“好”、“哦”、“在”、“没有”、“哈哈”、“那” 等词语选项。
    *   键盘主体：标准QWERTY布局
        *   字母键：Q, W, E, R, T, Y, U, I, O, P, A, S, D, F, G, H, J, K, L, Z, X, C, V, B, N, M
        *   特殊功能键：
            *   “?123” (切换至数字及符号键盘)
            *   “中/英” (中英文输入法切换)
            *   表情符号键 (笑脸图标)
            *   “换行”键
            *   删除键 (带叉号的方块图标)

【============== 图片解析 END ==============】

![in_table_YxHrbOcU6oqZT0xqByEc7trInNe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_YxHrbOcU6oqZT0xqByEc7trInNe.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线答题的UI界面，主要包含以下关键元素和组成部分：

1.  **顶部导航与状态栏 (Top Navigation and Status Bar)**
    *   **返回按钮**: 位于左上角，图标为“<”，用于返回上一界面。
    *   **计时器**: 显示“用时 01:24”，表示当前答题所用时间。
    *   **答题进度条**: 位于计时器右侧，橙色条形，显示当前答题进度的一部分。
    *   **(设备状态信息)**: 位于最右上角，显示网络信号（Wi-Fi）、电量（100%）。

2.  **题目内容区域 (Question Content Area)**
    *   **题目类型与来源**: 标签“单选 (2024春·浙江期中)”。
    *   **文章标题**: “《宗子相集》序”。
    *   **文章正文**:
        *   古文段落：“元中，武陵人捕鱼为业。缘溪行，忘路之远近。忽逢桃花林，夹岸注数百步，中无杂树，芳草鲜美，落英缤纷。渔人甚异之，复前行，欲穷其林。水源，便得一山，山有小口，仿佛若有光。便舍船，从口入。初极狭，才通人。复行数十步，豁然开朗。土地平旷，屋舍俨然，[ ]。阡陌交通，鸡犬相闻。其中往来种作，男女衣着，悉如外人。黄发垂髫，并怡然自乐。人，乃大惊，问所从来。具答之。便要还家，设酒杀鸡作食。村中闻有此人，咸来问讯。自云先世避秦时乱，率妻子邑人来此绝境，不复出焉，遂与外人间隔。问今是何世，乃不知有汉，无论魏晋。此人一一”
        *   **待填写区域**: 文中“土地平旷，屋舍俨然，”后有一个由方括号`[ ]`（视觉上是输入框）表示的待填写处，后跟句号。

3.  **作答交互区域 (Answer Interaction Area)**
    *   **提示信息**: “请认真作答, 老师会检查作答结果”。
    *   **输入方式选择**:
        *   **键盘输入**: 按钮，包含键盘图标和文字“键盘输入”。
        *   **拍照作答**: 按钮，包含相机图标和文字“拍照作答”。
    *   **插图/占位符**: 包含书本、笔和沙漏的图标，下方文字为“点击横线区域, 开始作答”。

4.  **底部操作按钮区域 (Bottom Action Button Area)**
    *   **不确定按钮**: 浅色背景按钮，文字为“不确定”。
    *   **提交按钮**: 橙色背景按钮，文字为“提交”。

【============== 图片解析 END ==============】

![in_table_VZF3bsNjQot6Pxxk2kvcRIF0nX7](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_VZF3bsNjQot6Pxxk2kvcRIF0nX7.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
这似乎是一个在线学习或考试应用的答题界面，其关键元素和组成部分如下：

1.  **顶部系统状态栏:**
    *   左侧: 显示当前时间与日期: "7:35 Mon Jun 3"
    *   右侧: 显示系统状态，如图标所示为Wi-Fi信号和 "100%" 的电量。

2.  **应用导航与计时区:**
    *   左上角: 返回按钮 ( "<" 图标)。
    *   中央:
        *   作答用时: "用时01:24"。
        *   作答进度条: (一个水平条，部分被橙色填充，表示进度)。

3.  **主内容区 (分为左右两栏):**
    *   **左栏 - 题目内容区:**
        *   题型信息: "单选 (2024春·浙江期中)"。
        *   题目文本:
            *   标题: "《宗子相集》序"。
            *   正文: 一段文言文，其中包含两处标记：
                *   标记 "①" 位于 "夹岸数百步" 之前。
                *   标记 "②" 位于 "屋舍俨然，" 之后，并伴随一个下划线输入框样式，其中可见光标 "I"。
    *   **右栏 - 答题操作区:**
        *   提示信息: "请认真作答，老师会检查作答结果"。
        *   题号选择区:
            *   包含从 "1" 到 "8" 的圆形数字按钮，其中按钮 "2" 为高亮选中状态。
        *   答案输入区:
            *   提示文字: "点击输入答案"。
            *   下方为一个较大的矩形区域，用于显示或输入答案。
        *   操作按钮区:
            *   左下角: "不确定" 按钮 (白色背景)。
            *   右下角: "提交" 按钮 (橙色背景)。

【============== 图片解析 END ==============】

![in_table_Ne0Gb7W5voQfHrxhdO6cVe6Vnub](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Ne0Gb7W5voQfHrxhdO6cVe6Vnub.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图为一在线教育产品答题界面UI截图。

1.  **顶部状态栏**：
    *   左上角显示当前时间：“7:35 Mon Jun 3”。
    *   右上角显示网络及电量状态：“100%”。
2.  **导航与进度区域**：
    *   左上角：“<” 返回按钮。
    *   中间：“用时01:24” 计时显示。
    *   右侧：一个橙色的答题进度条。
3.  **左侧题目内容区**：
    *   **题目基本信息**：
        *   题型：“单选”。
        *   来源：“(2024春·浙江期中)”。
    *   **题目文本**：
        *   标题：“《宗子相集》序”。
        *   正文：显示一篇古文段落，具体内容如下：
            *   “元中，武陵人捕鱼为业。缘溪行，忘路之远近。忽逢桃花林，①夹岸数百步，中无杂树，芳草鲜美，落英缤纷。渔人甚异之，复前行，欲穷其林。”
            *   “水源，便得一山，山有小口，仿佛若有光。便舍船，从口入。初极狭，才通人。复行数十步，豁然开朗。土地平旷，屋舍俨然。②【字mo展示过多】阡陌交通，鸡犬相闻。其中往来种作，男女衣着，悉如外人。”
            *   后续文本被深色蒙层覆盖，并重复显示提示文字：“字数过多展示效果字数过多展示效果字数过多展示效果字数过多展示效果字数过多展示效果字数过多展示效果”。这表明原文超出当前显示区域。
4.  **右侧答题与导航区**：
    *   **提示信息**：“请认真作答,老师会检查作答结果”。
    *   **题号导航**：
        *   圆形按钮形式展示题号：“3”, “4”, “5”, “6”, “7”, “8”, “9”, “10”。
        *   题号“10”背景色为蓝色，表示当前作答题目或选中状态。
    *   **作答区域**：
        *   一个大的文本输入框，内部有浅灰色提示文字：“字数过多展示效果字数过多展示效果”。
    *   **操作按钮**：
        *   “不确定”按钮（白色背景，灰色文字）。
        *   “提交”按钮（橙色背景，白色文字）。

【============== 图片解析 END ==============】

![in_table_RIdZbg5cRoilSkxc0SAcjh2kn8b](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RIdZbg5cRoilSkxc0SAcjh2kn8b.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片为一个移动端在线答题界面的截图，主要元素和层级结构如下：

*   **一、顶部状态与导航栏**
    *   **1. 系统状态信息 (屏幕最顶部)**
        *   左侧：日期 "Mon Jun 3"
        *   右侧：Wi-Fi 及电池状态图标
    *   **2. 答题导航栏**
        *   左侧：返回按钮 ( `<` 图标)
        *   中间：计时器 "用时01:24"
        *   右侧：答题进度条 (橙色表示已完成部分，灰色表示未完成部分)

*   **二、题目与作答区域 (主体内容区，左右分栏)**
    *   **1. 左侧：题目内容展示区**
        *   题目类型及来源：
            *   标签："单选"
            *   信息："(2024春·浙江期中)"
        *   文章标题："《宗子相集》序"
        *   文章正文 (古文，包含填空)：
            *   "元中，武陵人捕鱼为业。缘溪行，忘路之远近。忽逢桃花林，[此处为一长方形空白填空框]，中无杂树，芳草鲜美，落英缤纷。渔人甚异之，复前行，欲穷其林。"
            *   "水源，便得一山，山有小口，仿佛若有光。便舍船，从口入。初极狭，才通人。复行数十步，豁然开朗。土地平旷，屋舍俨然，[此处为一长方形空白填空框]。阡陌交通，鸡犬相闻。其中往来种作，男女衣着，悉如外人。黄发垂髫，并怡然自乐。"
            *   "人，乃大惊，问所从来。具答之。便要还家，设酒杀鸡作食。村中闻有此人，咸来问讯。自云先世避秦时乱，率妻子邑人来此绝境，不复出焉，遂与外人间隔。问今是何世，乃不知有汉，无论魏晋。此人一一" (后续文字未完整显示)
    *   **2. 右侧：作答方式选择与操作区**
        *   提示信息："请认真作答，老师会检查作答结果"
        *   拍照上传模块：
            *   图标：相机图案
            *   文字："拍照上传"
            *   说明："最多可以传4张"
        *   作答方式切换按钮：
            *   按钮："键盘输入" (附带键盘图标)
            *   按钮："拍照作答" (附带相机图标)

*   **三、底部操作栏**
    *   左侧按钮："不确定"
    *   右侧按钮："提交" (橙色背景，高亮状态)

【============== 图片解析 END ==============】

![in_table_GUhdbGw2HoYAktxdBOWcQMCQnVd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GUhdbGw2HoYAktxdBOWcQMCQnVd.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

**例3**

**已知条件:**
*   曲线方程为：
    $$
    y = \frac{1}{3}x^3 + \frac{4}{3}
    $$

**求解问题:**
1.  求曲线在点 \( P(2, 4) \) 处的切线方程。
2.  求过点 \( P(2, 4) \) 的曲线的切线方程。
3.  求斜率为 4 的曲线的切线方程。

**解析:**

**(1) 求解曲线在点 \( P(2, 4) \) 处的切线方程：**
*   **求导数:**
    $$
    y' = x^2
    $$
*   **计算在点 \( P(2, 4) \) 处的切线斜率 \( k \):**
    根据题意，点 \( P(2, 4) \) 为切点，其 x 坐标为 2。将 x=2 代入导数方程：
    $$
    k = y'|_{x=2} = (2)^2 = 4
    $$
*   **切线方程:**
    使用点斜式 \( y - y_1 = k(x - x_1) \)，其中点为 \( P(2, 4) \)，斜率为 \( k=4 \)：
    $$
    y - 4 = 4(x - 2)
    $$
*   **化简切线方程:**
    $$
    4x - y - 4 = 0
    $$

**(2) 求解过点 \( P(2, 4) \) 的曲线的切线方程（部分解析）：**
*   **假设切点:**
    设曲线 \( y = \frac{1}{3}x^3 + \frac{4}{3} \) 与过点 \( P(2, 4) \) 的切线相切于点 \( A(x_0, \frac{1}{3}x_0^3 + \frac{4}{3}) \)。
*   **切线斜率 \( k \) 在切点 A 处:**
    $$
    k = y'|_{x=x_0} = x_0^2
    $$
    (后续步骤未在图片中完整展示)

【============== 图片解析 END ==============】

![in_table_Zvd7bYTEbofuV5x2KLacKUgmn8f](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Zvd7bYTEbofuV5x2KLacKUgmn8f.png)

###### 图片分析
【============== 图片解析 BEGIN ==============】

该图片为一个数学题目（例3）及其部分解析过程。

**一、题目：例3**

已知曲线方程为：
$$
y = \frac{1}{3}x^3 + \frac{4}{3}
$$

包含以下三个问题：
1.  求曲线在点 \( P(2, 4) \) 处的切线方程；
2.  求过点 \( P(2, 4) \) 的曲线的切线方程；
3.  求斜率为 4 的曲线的切线方程。

**二、解析**

**(1) 求解曲线在点 \( P(2, 4) \) 处的切线方程：**

*   首先，求曲线的导数：
    $$
    y' = x^2
    $$
*   然后，计算曲线在点 \( P(2, 4) \) 处切线的斜率 \( k \)：
    $$
    k = y'|_{x=2} = 2^2 = 4
    $$
*   因此，曲线在点 \( P(2, 4) \) 处的切线方程为 (使用点斜式 \( y - y_1 = k(x - x_1) \) )：
    $$
    y - 4 = 4(x - 2)
    $$
*   整理得切线方程为：
    $$
    4x - y - 4 = 0
    $$

**(2) 求解过点 \( P(2, 4) \) 的曲线的切线方程（解析过程开始部分）：**

*   设切点为 \( A(x_0, y_0) \)，其中 \( y_0 = \frac{1}{3}x_0^3 + \frac{4}{3} \)。则点 \( A \) 的坐标为：
    $$
    A(x_0, \frac{1}{3}x_0^3 + \frac{4}{3})
    $$
*   切线在点 \( A \) 处的斜率 \( k \) 为：
    $$
    k = y'|_{x=x_0} = x_0^2
    $$
    （后续解析步骤未在图片中完整显示）

【============== 图片解析 END ==============】

![in_table_EKFDbnuipoQDyNxtKK2cD2Hbnhg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EKFDbnuipoQDyNxtKK2cD2Hbnhg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线答题界面的截图，结构和元素如下：

1.  **顶部状态栏与导航区**
    *   **返回按钮**：位于左上角，图标为向左箭头。
    *   **计时器**：显示“用时01:24”。
    *   **进度条**：橙色，表示答题进度或时间消耗。
    *   **手机状态信息**：右上角显示时间“7:35 Mon Jun 3”，Wi-Fi信号强度图标准，以及电量“100%”。

2.  **题目内容区 (左侧)**
    *   **题目类型及来源**：标签“单选 (2024春·浙江期中)”。
    *   **文章标题**：“《宗子相集》序”。
    *   **文章内容**：展示了一段文言文节选，内容与OCR文本一致，核心为《桃花源记》片段。
        *   “元中，武陵人捕鱼为业。缘溪行，忘路之远近。忽逢桃花林，______，中无杂树，芳草鲜美，落英繽纷。渔人甚异之，复前行，欲穷其林。”
        *   “水源，便得一山，山有小口，仿佛若有光。便舍船，从口入。初极狭，才通人。复行数十步，豁然开朗。土地平旷，屋舍俨然，______。阡陌交通，鸡犬相闻。其中往来种作，男女衣着，悉如外人。黄发垂髫，并怡然自乐。”
        *   “见渔人，乃大惊，问所从来。具答之。便要还家，设酒杀鸡作食。村中闻有此人，咸来问讯。自云先世避秦时乱，率妻子邑人来此绝境，不复出焉，遂与外人间隔。问今是何世，乃不知有汉，无论魏晋。此人一一为具言所闻，皆叹惋。余人各复延至其家，皆出酒食。停数日，辞去。此中人语云：“不足为外人道也。””
        *   **填空区域**：文中有两处明显的下划线空白，分别在“忽逢桃花林，”之后和“屋舍俨然，”之后，等待用户填写。

3.  **答题操作区 (右侧)**
    *   **提示信息**：“请认真作答，老师会检查作答结果”。
    *   **答题方式按钮**：
        *   “键盘输入”按钮。
        *   “拍照作答”按钮。
    *   **图片上传区域**：
        *   已上传图片预览：显示两张图片缩略图，每张右上角有“X”删除按钮。
        *   上传提示：一个带有相机图标的虚线框，文字为“拍照上传 最多可以传4张”。

4.  **底部操作按钮区**
    *   **“不确定”按钮**：白色背景，灰色文字。
    *   **“提交”按钮**：橙色背景，白色文字。

【============== 图片解析 END ==============】

![in_table_BkKIbv3lrofEY9xsFeqcjyBVnHf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BkKIbv3lrofEY9xsFeqcjyBVnHf.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

**例3**

已知曲线 \( y = \frac{1}{3}x^3 + 4 \).

(1) 求曲线在点 \( P(2, 4) \) 处的切线方程；
(2) 求过点 \( P(2, 4) \) 的曲线的切线方程；
(3) 求斜率为 4 的曲线的切线方程。

**解析：**

**(1)** \( y' = x^2 \). 所以在点 \( P(2, 4) \) 处的切线的斜率 \( k = y'|_{x=2} = 4 \). 所以曲线在点 \( P(2, 4) \) 处的切线方程为 \( y - 4 = 4(x - 2) \), 即 \( 4x - y - 4 = 0 \).

**(2)** 设曲线 \( y = \frac{1}{3}x^3 + 4 \) 与过点 \( P(2, 4) \) 的切线相切于点 \( A(x_0, \frac{1}{3}x_0^3 + 4) \), 则切线的斜率 \( k = y'|_{x=x_0} = x_0^2 \). *(后续解析内容未在图片中完整显示)*

【============== 图片解析 END ==============】

![in_table_G967bbzgsoddEJxIow8cNa44nBg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G967bbzgsoddEJxIow8cNa44nBg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片展示了文件上传过程中的两种用户界面（UI）状态，及其关键元素和关联：

**1. 主体区域划分**
*   **顶部文字区域**：
    *   左侧卡片对应的上方文字：“上传中”
    *   右侧卡片对应的上方文字：“上传...”
*   **内容卡片区域**：包含两个并列的方形卡片，分别展示不同的上传状态。

**2. 左侧卡片：上传中状态**
*   **功能**：表示文件正在上传。
*   **视觉元素**：
    *   **背景**：深灰色方形卡片，内部为模糊的占位内容，暗示文件预览或类型。
    *   **指示器**：卡片中央为一个白色的圆形加载动画（spinner）。

**3. 右侧卡片：上传失败状态**
*   **功能**：表示文件上传失败，并提供重试操作。
*   **视觉元素**：
    *   **背景**：深灰色方形卡片，内部为模糊的占位内容，同左侧。
    *   **覆盖层信息（白色文字）**：
        *   第一行文字：“上传失败”
        *   第二行文字：“点击重试”（暗示该区域或文字可点击以触发重试操作）

**4. 元素间关联**：
*   这两个卡片代表同一个文件上传控件在不同时刻的两种独立状态。
*   “上传中”状态通过动态加载指示器提供文件正在处理的视觉反馈。
*   “上传失败”状态通过明确的文字提示告知用户上传操作的结果，并通过“点击重试”引导用户进行下一步交互。

【============== 图片解析 END ==============】

![in_table_BKbZbJQxdoQ7GaxW7hqc96ymnVc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_BKbZbJQxdoQ7GaxW7hqc96ymnVc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片展示了一个居中显示的弹窗界面：

*   **弹窗主体 (Modal/Dialog Box):**
    *   背景：白色，圆角矩形。
    *   **提示信息 (Prompt Message):**
        *   文本内容：“本题未作答，确认提交吗？”
    *   **操作按钮区域 (Action Button Area):**
        *   **按钮1 (Button 1):**
            *   位置：左侧
            *   文本标签：“继续学习”
            *   样式：浅灰色背景，深色文字。
        *   **按钮2 (Button 2):**
            *   位置：右侧
            *   文本标签：“我再想想”
            *   样式：橙色背景，白色文字。

层级关系：
1.  **弹窗容器**
    1.  **提示文本**
    2.  **按钮组**
        1.  **“继续学习”按钮**
        2.  **“我再想想”按钮**

【============== 图片解析 END ==============】

![in_table_LrOQbLpKyo8y2Axm6a4cbZgYnmh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_LrOQbLpKyo8y2Axm6a4cbZgYnmh.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线答题或批改的界面，分为左右两部分：

**一、 顶部区域**
*   **返回按钮：**左上角的 "<" 图标。
*   **用时显示：**"用时01:24"。
*   **进度条：**橙色和灰色组成，表示进度。

**二、 左侧区域：题目内容区**
1.  **题目信息：**
    *   类型："单选"
    *   来源："(2024春·浙江期中)"
2.  **文章标题：** `《宗子相集》序`
3.  **文章正文：**
    *   元中，武陵人捕鱼为业。缘溪行，忘路之远近。忽逢桃花林，①夹岸注数百步√ 中无杂树，芳草鲜美，落英缤纷。渔人甚异之，复前行，欲穷其林。
        *   **标注 ①：** 文本 "夹岸注数百步" 右侧有绿色勾号 "√"。
    *   水源，便得一山，山有小口，仿佛若有光。便舍船，从口入。初极狭，才通人。复行数十步，豁然开朗。土地平旷，屋舍俨然。②文字展示字… × 。阡陌交通，鸡犬相闻。其中往来种作，男女衣着，悉如外人。黄发垂髫，并怡然自乐。
        *   **标注 ②：** 文本 "文字展示字…" 右侧有红色叉号 "×"。
    *   人，乃大惊，问所从来。具答之。便要还家，设酒杀鸡作食。村中闻有此人，咸来问讯。自云先世避秦时乱，率妻子邑人来此绝境，不复出焉，遂与外人间隔。问今是何世，乃不知有汉，无论魏晋。此人一一为具言所闻，皆叹惋。余人各复延至其家，皆出酒食。停数日，辞去。此中人语云：“不足为外人道也。”
4.  **水印/用户名：** "xiaoxiao" (位于正文末尾左下方)。

**三、 右侧区域：答题与解析区**
1.  **提示信息：** "请认真作答，老师会检查作答结果"
2.  **题目导航条：**
    *   包含数字1至8的圆形图标。
    *   "1" 号图标为绿色背景。
    *   "2" 号图标为红色边框和浅红色背景。
3.  **核对答案模块：**
    *   标题："核对答案"
    *   内容："②错误答案错误答案错误答案错误答案错误答案错误答案错误答案错误答案错误答案错误答案错误答案错误答案"
    *   用户自评按钮组：
        *   "✔ 我答对了"
        *   "✖ 部分答对"
        *   "✖ 我答错了" (此按钮当前为选中状态，背景为橙色)
4.  **题目解析模块：**
    *   标题："题目解析"
    *   内容区：重复的 "题目解析题目解析..." 文本。
5.  **操作按钮：** "继续" 按钮，黄色背景。

【============== 图片解析 END ==============】

![in_table_SD3zblHhDoNnHDxG6a0cFKXTnRd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SD3zblHhDoNnHDxG6a0cFKXTnRd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片展示了一个用户界面（UI）中的提示组件。

*   **整体描述**：该组件为一个独立的视觉单元。
*   **组成元素及属性**：
    1.  **背景元素**：
        *   形状：圆角矩形。
        *   颜色：灰色。
    2.  **文本元素**：
        *   内容：`认真核对哦～`
        *   颜色：白色。
*   **元素间层级与关联**：
    *   文本元素位于背景元素的上方，并在背景元素内部居中显示。
    *   这两个元素组合在一起，在视觉上共同呈现了文字信息：“认真核对哦～”。

【============== 图片解析 END ==============】

![in_table_G0CRbeT6YoeD0gxcg9ncZerynGk](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_G0CRbeT6YoeD0gxcg9ncZerynGk.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片定义了“答案状态”按钮在不同交互状态下的UI样式，主要分为“选中态”和“常态”。

*   **核心元素**: 答案状态按钮 (Answer Status Buttons)
*   **层级与关联**:
    *   图片整体标题为“答案状态”。
    *   包含两个主要状态列：“选中态” 和 “常态”。
        *   **选中态 (Selected State)**:
            *   **“我答对了”按钮**:
                *   **边框**: 绿色。
                *   **图标**: 绿色对勾。
                *   **文字**: “我答对了”。
                *   **下方标注**: "Frame 1912056470"。
            *   **“部分答对”按钮**:
                *   **边框**: 橙色。
                *   **图标**: 橙色对勾。
                *   **文字**: “部分答对”。
                *   **下方标注**: "Frame 1912056470"。
            *   **“我答错了”按钮**:
                *   **边框**: 红色。
                *   **图标**: 红色叉号。
                *   **文字**: “我答错了”。
                *   **下方标注**: "Frame 1912056470"。
        *   **常态 (Normal State)**:
            *   **上方标注**: "Frame 224065"。
            *   **“我答对了”按钮**:
                *   **边框**: 白色。
                *   **图标**: 黑色对勾。
                *   **文字**: “我答对了”。
                *   **下方标注**: "Frame 1912056470"。
            *   **“部分答对”按钮**:
                *   **边框**: 白色。
                *   **图标**: 黑色对勾。
                *   **文字**: “部分答对”。
                *   **下方标注**: "Frame 1912056470"。
            *   **“我答错了”按钮**:
                *   **边框**: 白色。
                *   **图标**: 黑色叉号。
                *   **文字**: “我答错了”。
                *   **下方标注**: "Frame 1912056470"。

【============== 图片解析 END ==============】



### 英语填空题

![Cf39w5BB1hMgVKbAwMTcPqEhnsc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_Cf39w5BB1hMgVKbAwMTcPqEhnsc.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

该图片描述了用户在互联网教育产品中针对“英语（自动批改）”题目进行作答的完整流程。

**1. 核心阶段与元素:**

*   **初始状态:**
    *   **查看题目 (题干+作答区):** 用户开始作答的入口，展示题目内容和作答区域。
*   **作答状态:**
    *   **填空作答区 (输入内容):** 用户在指定区域输入答案。
        *   **提交:** 用户提交其输入的答案。
            *   **(自动批改):** 系统对提交的答案进行判断。
                *   **回答正确:** 答案被判定为正确。
                *   **回答错误:** 答案被判定为错误。
                *   **未作答:** 用户提交时未填写任何内容。
    *   **不确定:** 用户选择不确定，未直接提交。
        *   **放弃作答 (二次确认):** 用户确认放弃当前题目的作答。
            *   此路径也导向**未作答**状态。
*   **解析状态:**
    *   **查看答案、题目解析:** 在“回答正确”、“回答错误”或“未作答”后，用户可以查看标准答案和题目解析。
    *   **猜你想问 (仅在“回答错误”后出现):**
        *   **点击推荐问题:** 用户选择系统推荐的相关问题。
        *   **点击自由提问:** 用户自主输入问题。
        *   **答疑 chat:** 用户通过聊天形式获得针对性解答。

**2. 元素间关联 (Mermaid Flowchart):**

```mermaid
flowchart LR
    subgraph 初始状态
        B["查看题目<br/>(题干+作答区)"]
    end

    subgraph 作答状态
        C["填空作答区<br/>输入内容"]
        D["不确定"]
        E["提交"]
        F["放弃作答<br/>(二次确认)"]
        
        subgraph 自动批改
            direction LR
            G["回答正确"]
            H["回答错误"]
            I["未作答"]
        end
    end

    subgraph 解析状态
        J["查看答案、题目<br/>解析"]
        K["猜你想问"]
        L["点击推荐问题"]
        M["点击自由提问"]
        N["答疑 chat"]
    end

    A["英语 (自动批改)"] --> B

    B --> C
    B --> D

    C --> E
    
    D --> F
    F --> I

    E --> G
    E --> H
    E --> I

    G --> J
    H --> J
    I --> J
    
    H --> K
    K --> L
    K --> M
    L --> N
    M --> N
```

【============== 图片解析 END ==============】



<table>
<tr>
<td>**状态**<br/></td><td>**原型图**<br/></td><td>**需求说明**<br/></td></tr>
<tr>
<td>初始状态<br/></td><td>![in_table_MQbDb84rGocUN9xlXG3cq4ZVnDz](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_MQbDb84rGocUN9xlXG3cq4ZVnDz.png)<br/><br/></td><td>**状态判断：**<br/>- 当进入题目且没有作答时（即填空作答区没有输入内容）为初始状态。<br/>**题型校验：**<br/>- 录入学科：英语。<br/>- 录入题目作答方式为：填空题。<br/>- 非母子题。<br/>**页面框架**<br/>- 仅支持全屏展示。<br/>- 退出学习：- 按钮名称：退出学习。- 交互：点击后退出至，xx 页面。<br/>- **作答计时：**- 每题独立正计时（格式 00:00，上限 59:59）。- 用户进入题目开始计时，提交后停止计时。- 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时（例一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。<br/>- **题板结构：**- 左侧为题干区、右侧为作答区。<br/>- 题干区：- 题型标签：填空- 题干内容：- 数据源自题库中已录入题目。- 题干支持展示文字、图片和作答区。<br/>- **作答区：**- 填空区域：以输入框的形式插入题干中。- 长度：以答案长度作为作答区输入框长度。- 插入位置：数据源自题库中已录入题目。- 作答区选项卡：- 对应题干中的作答区数量和顺序，只有一个空时，不展示选项卡。- 点击作答区选项卡中的选项，左侧题干区自动定位到选中位置。- 不确定：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 点击【选项】，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 提交：- 按钮状态：不可点击，按钮样式置灰。- 交互：/<br/>- **勾画组件：**![in_table_W1HJbyVIko755gx2bfXcO1dznjd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_W1HJbyVIko755gx2bfXcO1dznjd.png)- 入口：作答界面悬浮【勾画】按钮（常驻显示）。- 交互：支持用户在作答环节时，点击【勾画】按钮唤起勾画组件，对题目进行勾画和标记。- 勾画工具。- 笔迹调整：- 颜色调整：支持蓝色、红色、黑色。- 粗细调整：细、中等、粗。- 橡皮擦：- 粗细调整：细、中等、粗。- 撤回、恢复。- 一键清空。- 交互：- 点击【完成】，收起勾画组件，同时将勾画的内容自动保存并展示。<br/></td></tr>
<tr>
<td>作答状态<br/></td><td>![in_table_OBFUbYqHAoKd5Yxjq7scW5ALn5f](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OBFUbYqHAoKd5Yxjq7scW5ALn5f.png)<br/><br/>![in_table_FQz8bCpQzoaRrixqwWkccrZlnvc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FQz8bCpQzoaRrixqwWkccrZlnvc.png)<br/><br/><br/></td><td>**状态判断：**<br/>- 当进入题目且有作答时（即填空作答区输入了内容）为作答状态。<br/>**批改方式：**<br/>- 英语学科  - 自动批改。<br/>**作答方式**<br/>- 键盘作答（仅支持一种）：- 支持通过键盘输入文字将内容填充到填空的对应位置。- 点击左侧题干区域的作答区标记或右侧作答区域的“点击输入答案”，该区域展示选中状态，并自动调起键盘，支持用户输入文字。- 选项卡作答区序号选中，题干中作答区序号选中，展示选中态。- 当对应作答区输入内容后，展示已输入状态。- 输入文字时，选项中不展示文字内容，键盘上方增加展示输入内容的区域。- 点击【确定】或点击键盘区域外其他区域，收起键盘，取消选中状态，保存刚刚输入的文字。- 如果点击键盘外其他区域时刚好点击了某一个作答区时，则不收起键盘，键盘上输入框展示对应作答区已输入的内容。<br/>- 操作按钮：- 不确定，[同初始状态](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd#share-CIA8dBUyVoBjKPx3Meccu02LnWg)：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 再次输入内容后，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 提交：- 按钮状态：可点击。- 交互（需校验）：- 点击时，有文字输入，提交作答数据，题目进入解析状态，展示解析状态内容。- 点击时，当有空没有文字输入（文字作答）时内容，弹窗确认是否继续提交，继续提交，题目进入解析状态，展示解析状态内容。- 全部空为空时，- 弹窗内容：本题未作答，确认提交吗？- 操作按钮：- 我再想想，点击后关闭弹窗。- 继续提交，点击后提交作答，进入解析状态- 部分空为空时，- 弹窗内容：还有填空未作答，确认提交吗？- 操作按钮：- 我再想想，点击后关闭弹窗。- 继续提交，点击后提交作答，进入解析状态<br/><br/></td></tr>
<tr>
<td>解析状态<br/></td><td>![in_table_T6xIbv5nAoy3xjxBYuocbx9LnUc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_T6xIbv5nAoy3xjxBYuocbx9LnUc.png)<br/><br/><br/></td><td>**状态判断：**<br/>- 当题目有作答（即填空作答区输入了内容）并点击【提交】后为解析状态。<br/>- 当题目没有作答，点击了【不确定】 - 【放弃作答】后为解析状态。<br/>**批改方式：**<br/>- 自动批改<br/>**展示内容：**<br/>- 反馈弹窗，见[巩固练习。](https://wcng60ba718p.feishu.cn/wiki/EClyw7T06iRxYpkK7PwcJcLHn4f)<br/>- 答案：- 展示正确答案：格式，“正确答案：”- 展示用户答案：格式，“你的答案：”- 不确定 - 放弃作答时，对应的作答区域标记为不确定突出展示。，并在下方展示正确答案和你的答案（“未作答”）。<br/>- 题目解析：- 内容：数据源自题库中已录入题目，对应字段【题目解析】。<br/>- 问一问，同上：- 入口：右上角问一问 icon ，仅当题目进入解析状态后展示入口。- 交互：-  点击弹出答疑组件。- 展示： 支持自由提问 + 模型生成的猜你想问问题（最多 3 个 可以为 0 ）。- 由用户发起首轮对话。<br/>- 操作按钮：- 继续：- 出现时机：题目进入解析状态。- 按钮状态：可点击。- 交互：点击后进入下一题/下一节内容。<br/></td></tr>
</table>

![in_table_MQbDb84rGocUN9xlXG3cq4ZVnDz](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_MQbDb84rGocUN9xlXG3cq4ZVnDz.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线答题界面的UI设计，主要面向教育场景中的学生用户。其关键元素及层级结构如下：

1.  **顶部状态栏 (Top Status Bar):**
    *   左上角：返回按钮（<图标）。
    *   中间：计时器，显示“用时 01:24”。
    *   右上角：进度条（橙色部分表示已用时长或进度）。
    *   最右上角：移动设备状态信息（时间“7:35 Mon Jun 3”，Wi-Fi信号图标，电池电量“100%”）。

2.  **答题主区域 (Main Content Area):** 此区域分为左右两栏。
    *   **左栏：题目内容区 (Question Content Area)**
        *   **题型及来源**: “单选 (2024春·浙江期中)”。
        *   **题目文本**:
            ```
            Go with your passion.It has been said that who see the invisible canca who se do the impossible. When I was nine years old living in a small town in North Carolina I for smalivinga greeting cards in the back of a children's ① . I can do this. Imyself magazi begged my mother to let me send for the kit. Two weeks later when the kit arrived, an sendd arrived ② the brown paper wrapper, grabbed the cards and dashed from the house. thebrow and an proclaiming, "Mama. all the people couldn't wait to buy my cards !"
            ```
            *   文本中包含两个待填空处，分别用圆圈数字 `①` 和 `②` 及下划线标出。

    *   **右栏：答题操作及导航区 (Answering and Navigation Area)**
        *   **提示信息**: “请认真作答, 老师会检查作答结果”。
        *   **题号导航**: 以圆形按钮形式展示题号，包含数字 1 至 8，当前选中第 2 题（背景色与其他题号不同，为蓝色）。
        *   **答案输入区**: 区域内有提示文字“点击输入答案”。
        *   **操作按钮**:
            *   “不确定”按钮。
            *   “提交”按钮（橙色背景）。

【============== 图片解析 END ==============】

![in_table_W1HJbyVIko755gx2bfXcO1dznjd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_W1HJbyVIko755gx2bfXcO1dznjd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线学习界面的截图，主要包含以下层级结构和关键元素：

*   **页面主体：在线答题/练习界面**
    *   **顶部导航栏**
        *   左侧区域
            *   按钮：“退出学习”（包含一个向左的箭头图标）
            *   文本：“用时 00:10”
        *   右侧区域
            *   按钮：“学习地图”
            *   按钮：“反馈”
    *   **内容区域**
        *   **题目展示区**
            *   题型标签：“单选”
            *   题目文本：“关于空间几何，以下哪个描述是正确的？”
        *   **答案选项区**（均为矩形按钮样式）
            *   选项 A：“A 打点计时器”
            *   选项 B：“B 打点计时器”
            *   选项 C：“C 打点计时器”
            *   选项 D：“D 打点计时器”
    *   **悬浮标注工具栏**
        *   **上排（工具属性选择）**
            *   颜色选择器：
                *   蓝色圆形（当前选中）
                *   橙色圆形
                *   黑色圆形
            *   画笔/笔触样式选择器：包含三个图标，分别表示不同粗细或样式的笔触。
        *   **下排（工具操作按钮）**
            *   图标：画笔（当前选中，颜色为蓝色）
            *   图标：橡皮擦
            *   图标：选择/工具（一个指针或手型图标）
            *   图标：删除（垃圾桶样式）
            *   图标：撤销（向左的弯曲箭头）
            *   图标：重做（向右的弯曲箭头）
            *   按钮：“完成”

【============== 图片解析 END ==============】

![in_table_OBFUbYqHAoKd5Yxjq7scW5ALn5f](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_OBFUbYqHAoKd5Yxjq7scW5ALn5f.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
该图片展示了一个移动应用界面，用户正在进行文本输入操作。界面主要包含以下层级和元素：

1.  **系统状态栏 (界面最顶部)**
    *   左侧：显示时间 "7:35" 和日期 "Mon Jun 3"。
    *   右侧：显示Wi-Fi信号图标、电量百分比 "100%" 及电池图标。

2.  **应用导航与信息栏 (系统状态栏下方)**
    *   左侧：返回按钮 (左箭头 "<" 图标)。
    *   中间：计时信息 "用时01:24"。
    *   右侧：橙色进度条，已填充一部分。

3.  **题目显示区 (背景层，部分被输入弹窗遮挡)**
    *   题目类型及来源："单选 (2024春·浙江期中)"。
    *   题干内容：英文文本 "Go with your passion. It has been said that who see the invisible canca who se do the impossible. When I was nine years old living in"。
    *   (位于输入弹窗下方，部分可见) 答题辅助信息区：
        *   提示文本："请认真作答, 老师会检查作答结果"。
        *   题号导航：一排圆形按钮，标有数字 "1" 至 "8"，其中 "2" 号按钮高亮显示。
        *   输入区域提示："点击输入答案" (位于题号导航下方)。

4.  **文本输入弹窗 (前景层，覆盖部分题目显示区和键盘区顶部)**
    *   输入框：位于弹窗左侧，内含用户已输入的文本 "我认为这道题应该这样回答"。
    *   确认按钮：位于弹窗右侧，橙色背景，文本为 "确定"。

5.  **虚拟键盘区 (界面底部，位于文本输入弹窗下方)**
    *   **候选词栏** (键盘最上方)：显示中文候选词 "嗯 我 你 好 哦 在 没有 哈哈 那"。
    *   **键盘主体** (标准QWERTY布局，中文输入模式)：
        *   第一行字母键：Q, W, E, R, T, Y, U, I, O, P
        *   第二行字母键：A, S, D, F, G, H, J, K, L
        *   第三行功能/字母键：大写锁定/Shift键 (向上箭头图标), Z, X, C, V, B, N, M, 删除键 (带 "X" 的左向箭头图标)
        *   第四行功能键："?123" (切换数字/符号键盘), "中/英" (中英文切换), 空格键 (包含 "W" 字符，可能为输入法特定标识), (一个包含 "W" 字符的按键), 表情符号键 (笑脸图标), "换行" 键。

【============== 图片解析 END ==============】

![in_table_FQz8bCpQzoaRrixqwWkccrZlnvc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_FQz8bCpQzoaRrixqwWkccrZlnvc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个平板电脑横屏状态下的在线答题界面，具体元素构成如下：

一、 **屏幕顶部状态栏 (System Level)**
    *   左上角：时间 "7:35"，日期 "Mon Jun 3"
    *   右上角：电池图标，电量 "100%"

二、 **应用内界面 (App Level)**
    *   **A. 导航与进度指示区 (Header)**
        *   左侧：返回箭头图标 "<"
        *   中间：计时显示 "用时 01:24"
        *   右侧：橙色答题进度条 (已完成一部分)
    *   **B. 题目内容展示区 (Content Area - Partially Visible)**
        *   题目标识：
            *   类型："单选"
            *   来源："(2024春·浙江期中)"
        *   题干文本 (部分)："Go with your passion.It has been said that who see the invisible canca who se do the"
        *   作答提示："请认真作答, 老师会检查作答结果"
        *   题目导航：圆形数字按钮 1 至 8，当前 "2" 被选中高亮。
        *   作答区域占位提示 (大部分被弹窗遮挡)："点击输入答案"
    *   **C. 答案输入弹窗 (Input Modal - Overlay)**
        *   **1. 文本输入框**
            *   已输入内容："我认为这道题应该这样回答" (重复多次)
        *   **2. 确认按钮**
            *   橙色背景，文字："确定"
        *   **3. 输入法键盘区 (Keyboard Area)**
            *   **a. 候选词区域**
                *   左侧：网格图标 (功能切换)
                *   候选词："嗯"、"我"、"你"、"好"、"哦"、"在"、"没有"、"哈哈"、"那"
            *   **b. 字母键盘 (QWERTY Layout)**
                *   第一行：Q W E R T Y U I O P
                *   第二行：A S D F G H J K L
                *   第三行：上档键 (向上箭头)、Z X C V B N M、退格键 (带X的方框)
                *   第四行：
                    *   "?123" (切换到符号/数字键盘)
                    *   "中/英" (中英文切换)
                    *   "W" (拼音候选，或特定功能键)
                    *   "W" (第二个拼音候选或功能键)
                    *   笑脸图标 (表情符号)
                    *   "换行" (回车键)

**元素间关联:**

*   用户在 **B. 题目内容展示区** 查看题目信息和题干。
*   点击 "点击输入答案" (或类似交互) 会触发 **C. 答案输入弹窗** 的显示。
*   用户通过 **C.3. 输入法键盘区** 在 **C.1. 文本输入框** 中输入答案。
*   输入完成后，点击 **C.2. 确认按钮** 提交答案。
*   **A. 导航与进度指示区** 的计时器和进度条反映用户当前的答题状态。返回箭头用于退出当前界面。
*   **屏幕顶部状态栏** 提供设备系统信息，与应用功能无直接交互。

【============== 图片解析 END ==============】

![in_table_T6xIbv5nAoy3xjxBYuocbx9LnUc](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_T6xIbv5nAoy3xjxBYuocbx9LnUc.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片展示了在线教育产品中的一个题目作答界面，具体为一项英语阅读理解填空题。

**关键元素与组成部分层级结构：**

1.  **整体界面布局 (Overall Interface Layout):**
    *   **左侧区域 (Left Pane): 题目展示区**
        *   **题目类型及来源标识 (Question Type and Source):**
            *   "单选 (2024春·浙江期中)"
        *   **题目正文 (Question Text):**
            *   英文段落，包含两处填空。
            *   原文内容: "Go with your passion. It has been said that who see the invisible canca who se do the impossible. When I was nine years old living in a small town in North Carolina I for smalivinga greeting cards in the back of a children's ① magazine . I can do this. Imyself magazi begged my mother to let me send for the kit. Two weeks later when the kit arrived, an sendd arrived ② magazine the brown paper wrapper, grabbed the cards and dashed from the house. thebrow and an proclaiming, “Mama. all the people couldn’t wait to buy my cards!”"
                *   填空①后的文字: "magazine"
                *   填空②后的文字: "magazine"
    *   **右侧区域 (Right Pane): 答题辅助与反馈区**
        *   **作答提示 (Answering Prompt):** "请认真作答，老师会检查作答结果"
        *   **题目导航 (Question Navigation):** 数字列表 "1 2 3 4 5 6 7 8" (当前焦点可能在题目6，因其颜色与其他不同)
        *   **正确答案显示 (Correct Answer Display):** "正确答案：magazine"
        *   **题目解析区 (Explanation Area):** "题目解析题目解析题目解析..." (重复的占位符文本)

【============== 图片解析 END ==============】



### 解答题

![L5RfwgghbhP8Rrb7st1c3LijnYd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_L5RfwgghbhP8Rrb7st1c3LijnYd.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

该图片为用户在线答题的流程图，清晰地展示了用户从查看题目到最终完成答疑的完整路径和不同选择分支。下面将使用Mermaid flowchart语法对该流程进行描述，并阐述其关键元素及关联。

```mermaid
flowchart LR
    subgraph 初始状态
        A1["查看题目\n（题干+作答区）"]
    end

    subgraph 作答状态
        B1[作答方式选择]
        B2[拍照上传]
        B3[键盘输入]
        B4[不确定]
        B5["放弃作答\n（二次确认）"]
        B6[提交]
    end

    subgraph 解析状态
        C1[自评]
        C2[回答正确]
        C3[回答错误]
        C4[回答错误]
				%% 根据图片和OCR，此处为两个独立的“回答错误”选项
        C5[查看答案、题目解析]
        C6[猜你想问]
        C7[点击推荐问题]
        C8[点击自由提问]
        C9[答疑 chat]
    end

    %% 流程连接
    A1 --> B1
    A1 --> B4

    B1 --> B2
    B1 --> B3

    B2 --> B6
    B3 --> B6

    B4 -.-> B1
    B4 --> B5

    B6 --> C1
    B6 --> C5

    C1 --> C2
    C1 --> C3
    C1 --> C4

    C5 --> C1
    C5 --> C6

    C6 --> C7
    C6 --> C8

    C7 --> C9
    C8 --> C9
```

**图片关键元素及层级关联阐述：**

该流程图主要包含三个阶段：初始状态、作答状态和解析状态。

1.  **初始状态**:
    *   `A1: 查看题目（题干+作答区）`：用户答题流程的起点，用户在此查看题目内容和作答区域。

2.  **作答状态**: (从 `A1` 查看题目后进入此阶段)
    *   用户首先面临选择：
        *   进入 `B1: 作答方式选择`。
        *   或标记为 `B4: 不确定`。
    *   如果选择 `B1: 作答方式选择`：
        *   可选择 `B2: 拍照上传` 方式作答，然后 `B6: 提交`答案。
        *   可选择 `B3: 键盘输入` 方式作答，然后 `B6: 提交`答案。
    *   如果选择 `B4: 不确定`：
        *   用户可以返回 `B1: 作答方式选择` (图中用虚线表示)。
        *   用户也可以选择 `B5: 放弃作答（二次确认）`，此为退出当前题目作答的一个路径。
    *   `B6: 提交`：是作答完成后的关键动作，将用户的作答结果送入下一阶段。

3.  **解析状态**: (从 `B6: 提交` 答案后进入此阶段)
    *   用户提交答案后，可选择：
        *   进行 `C1: 自评`。
        *   或直接 `C5: 查看答案、题目解析`。
    *   如果选择 `C1: 自评`：
        *   系统会引导用户判断自己的答案，可能的结果有 `C2: 回答正确`，或两种 `C3: 回答错误`、`C4: 回答错误` (代表不同类型的错误或仅为选项)。
    *   如果选择 `C5: 查看答案、题目解析`：
        *   用户在查看解析后，可以选择返回 `C1: 自评`。
        *   用户也可以进一步进入 `C6: 猜你想问` 模块。
    *   如果进入 `C6: 猜你想问`：
        *   用户可 `C7: 点击推荐问题`，然后进入 `C9: 答疑 chat`。
        *   用户也可 `C8: 点击自由提问`，然后进入 `C9: 答疑 chat`。
    *   `C9: 答疑 chat`：为用户提供答疑服务的模块，是当前流程的一个主要结束点。

【============== 图片解析 END ==============】



<table>
<tr>
<td>**状态**<br/></td><td>**原型图**<br/></td><td>**需求说明**<br/></td></tr>
<tr>
<td>初始状态<br/></td><td>![in_table_EkLgbJkt5oeaKBxfb7Tcj4yenDf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EkLgbJkt5oeaKBxfb7Tcj4yenDf.png)<br/><br/>![in_table_Q1UZb24j7oWaejxFENCc1XZmn9g](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Q1UZb24j7oWaejxFENCc1XZmn9g.png)<br/><br/></td><td>**状态判断：**<br/>- 当进入题目且没有作答时为初始状态。<br/>**题型校验：**<br/>- 录入学科：语文、数学、英语、物理、化学、生物。<br/>- 录入题目作答方式为：解答题。<br/>- 非母子题。<br/>**页面框架，**<br/>- 仅支持全屏展示。<br/>- 退出学习：- 按钮名称：退出学习。- 交互：点击后退出至，xx 页面。<br/>- **作答计时：**- 每题独立正计时（格式 00:00，上限 59:59）。- 用户进入题目开始计时，提交后停止计时。- 学生未作答退出，暂停计时，下次进入后在上次计时基础上继续计时（例一道题先看了 5 分钟没作答，退出后再进来，从 5 分钟开始计时）。<br/>- **题板结构：**- 左侧为题干区、右侧为作答区。<br/>- **题板内容：**- 题型标签：解答- 题目来源：数据源自题库中已录入题目，如“2024 春•浙江期中”。- 内容：数据源自题库中已录入题目。<br/>- 输入方式选择（单题内仅可选择一种方式作答）：- 切换作答方式时，- 如果当前作答方式下已经有内容，切换作答时，展示最新的作答方式。- 键盘作答切到拍照作答，题干上清空已作答内容，但并不删除内容。方便用户从拍照作答切回键盘作答时无需重新作答。- 拍照作答切到键盘作答，不删除内容。方便用户从键盘作答切回拍照作答时无需重新作答。- 拍照作答（默认）：支持用户通过拍照作答作答内容。- 展示作答示例。- 展示拍照作答入口。- 键盘作答：支持用户通过键盘输入文本进行作答。- 提示：点击输入答案。- 展示文本输入框和键盘- 操作：- 不确定：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 输入内容后，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 提交：- 按钮状态：不可点击，按钮样式置灰。- 交互：/<br/>- **勾画组件：**![in_table_GWmGbKabgoupdgxVeF0cSdg5nbh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GWmGbKabgoupdgxVeF0cSdg5nbh.png)- 入口：作答界面悬浮【勾画】按钮（常驻显示）。- 交互：支持用户在作答环节时，点击【勾画】按钮唤起勾画组件，对题目进行勾画和标记。- 勾画工具。- 笔迹调整：- 颜色调整：支持蓝色、红色、黑色。- 粗细调整：细、中等、粗。- 橡皮擦：- 粗细调整：细、中等、粗。- 撤回、恢复。- 一键清空。- 交互：- 点击【完成】，收起勾画组件，同时将勾画的内容自动保存并展示。<br/></td></tr>
<tr>
<td>作答状态<br/></td><td>![in_table_ZupTbomF3oZI4jxuRhzcqQjcnPF](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZupTbomF3oZI4jxuRhzcqQjcnPF.png)<br/>![in_table_HlTBbPcf6o4K4vxxGlscpUlenWh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_HlTBbPcf6o4K4vxxGlscpUlenWh.png)<br/><br/></td><td>**状态判断：**<br/>- 当进入题目且有作答时（即填空作答区输入了内容）为作答状态。<br/>**批改方式：**<br/>- 自评，核对答案。<br/>**作答方式（单题内仅可选择一种方式作答）：**<br/>- 拍照作答（默认）：支持用户通过拍照作答作答内容。- 入口：选择拍照作答。- 最大上传数量：4 张。- 交互：- 点击拍照作答调起摄像头进入拍摄界面，- 点击右上角 “x” 关闭拍摄界面。- 拍摄界面增加横竖九宫格辅助线。- 点击 x 关闭拍摄，点击拍摄键完成拍摄后，用户调整图片框选位置和大小，确认上传结果。- 点击上传，把拍摄的图片上传至作答区。 - 点击返回，回到拍摄界面。- 点击旋转，顺时逆旋转图片  90 度。![in_table_RkOBbwIHzoVq1axoMMXcUPWynZf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RkOBbwIHzoVq1axoMMXcUPWynZf.png)- 上传过程展示“上传中”状态，上传失败时，展示“上传失败 点击重试），点击后执行重新上传的流程。![in_table_R6B1bPOmBoLL91xE84GcTmmMnfe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_R6B1bPOmBoLL91xE84GcTmmMnfe.png)- 已上传图片支持放大全屏查看、支持删除。- 已上传内容检测：接入大模型进行识别。- 识别内容为异常时，不保存图片， 弹窗提示“请上传和题目相关的内容”，3 秒后消失。<br/>- 键盘作答：支持用户通过键盘输入文本进行作答。- 入口：选择键盘作答。- 提示：点击输入答案。- 支持通过键盘输入文字，将内容填充至作答区，具体交互规则如下：- 点击作答区域，选中状态，并自动调起键盘，支持用户输入文字。-  输入文字时，作答区展示输入内容。当输入文字超过一行时，自动换行以完整展示内容，最多展示 xx 行。- 点击键盘区域外的其他区域时，键盘收起，填空区域取消选中状态，系统自动保存刚刚输入的文字。<br/>- 操作按钮：- 不确定，[同初始状态](https://wcng60ba718p.feishu.cn/wiki/CXIWw1jTriZkiJkWUQ9cVFyBnBd#share-CIA8dBUyVoBjKPx3Meccu02LnWg)：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 输入内容后，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 提交：- 按钮状态：可点击。- 交互（需校验）：- 点击时，当有上传的图片（拍照作答）/有文字输入（文字作答时，提交作答数据，题目进入解析状态，展示解析状态内容。- 点击时，当没有上传的图片（拍照作答）/没有文字输入（文字作答）时内容，弹窗确认是否继续提交，继续提交，题目进入解析状态，展示解析状态内容。- 全部空为空时，- 弹窗内容：本题未作答，确认提交吗？- 操作按钮：- 我再想想，点击后关闭弹窗。- 继续提交，点击后提交作答，进入解析状态<br/></td></tr>
<tr>
<td>解析状态<br/></td><td>![in_table_TcU4bQiQEoeDBsxwGdUc72yPnrb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_TcU4bQiQEoeDBsxwGdUc72yPnrb.png)<br/><br/></td><td>**状态判断：**<br/>- 当题目有作答（即填空作答区输入了内容）并点击【提交】后为解析状态。<br/>- 当题目没有作答，点击了【不确定】 - 【放弃作答】后为解析状态。<br/>**展示内容：**<br/>- 正确答案：数据来自题库题目对应字段。<br/>- 我的答案- 已上传的图片，支持放大查看。- 已输入的文字。<br/>- 自评- 标题：核对答案- 副标题：对待错误是进步的第一步，相信你会认真对待!- 选项：![in_table_GQA1bq8YEoUpTCxLeFbcRT45npd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GQA1bq8YEoUpTCxLeFbcRT45npd.png)- 符号 + 我答对了，代表我的答案和标准答案一致。- 符号 +部分答对，代表我的答案和标准答案部分一致。- 符号 +我答错了，代表我的答案和标准答案不一致。- 交互：- 点击不同选项按钮，展示对应的选中效果。- 单选逻辑，用户可以点击自评选项进行自评结果的切换。- 提交后展示自评结果，此时自评结果仍然可修改。- 点击【继续】进入下一题/下一课。<br/>- 题目解析：- 内容：数据源自题库中已录入题目，对应字段【题目解析】。<br/>- 问一问，同上：- 入口：右上角问一问 icon ，仅当题目进入解析状态后展示入口。- 交互：-  点击弹出答疑组件。- 展示： 支持自由提问 + 模型生成的猜你想问问题（最多 3 个 可以为 0 ）。- 由用户发起首轮对话。<br/>- 操作按钮：- 继续：- 出现时机：题目进入解析状态- 按钮状态：- 自评已完成，可点击。- 自评未完成，不可点击，点击是未自评题目自评选项抖动提示。- 交互：点击后进入下一题/下一节内容。<br/></td></tr>
</table>

![in_table_EkLgbJkt5oeaKBxfb7Tcj4yenDf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_EkLgbJkt5oeaKBxfb7Tcj4yenDf.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

这是一个在线教育产品中的答题界面截图，主要包含以下元素：

1.  **顶部状态栏**:
    *   左上角：返回按钮（`<`）。
    *   中间靠上：用时计时器，显示“用时 01:24”。
    *   计时器下方：一个橙色的水平进度条。
    *   右上角：手机状态信息，显示“100%”电量和信号。

2.  **题目区域 (主体左侧白底卡片)**:
    *   **题型/来源**: “解答题 (2024春·浙江期中)”
    *   **题目描述文本**:
        *   一个可自由转动的转盘，被分成了三个大小相同的扇形，分别标有数字1、2、3。
        *   另有一个不透明的瓶子，装有分别标有数字1、2、3的三个完全相同的小球。
        *   小杰先转动一次转盘，停止后记下指针指向的数字（若指针指在分界线上则重转）。
        *   小玉再从瓶子中随机取出一个小球，记下小球上的数字。
    *   **题目示意图**:
        *   左侧图：一个圆形转盘，均分为三个扇形，分别标有数字“2”、“4”、“6”，指针指向标有“2”的扇形。
        *   右侧图：一个圆柱形不透明瓶子，示意性地展示内部有三个小球，分别标有数字“①”、“③”、“⑤”。
    *   **问题**:
        *   (1) 请用列表或画树状图的方法（选其中一种）表示出所有可能出现的结果；
        *   (2) 若得到的两数之和是偶数，则小杰赢；若得到的两数之和是奇数，则小玉赢。请问这个游戏公平吗？说明理由。

3.  **答题操作区域 (主体右侧)**:
    *   **提示信息**: “请认真作答，老师会检查作答结果”。
    *   **作答方式按钮**:
        *   “键盘输入”按钮（带键盘图标）。
        *   “拍照作答”按钮（带相机图标）。
    *   **答案输入区域**: 浅灰色背景，提示文字“点击输入答案”。

4.  **底部操作按钮**:
    *   “不确定”按钮 (白色背景，灰色文字)。
    *   “提交”按钮 (橙色背景，白色文字)。

【============== 图片解析 END ==============】

![in_table_Q1UZb24j7oWaejxFENCc1XZmn9g](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_Q1UZb24j7oWaejxFENCc1XZmn9g.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个移动应用界面截图，内容为一道数学解答题的作答页面。

1.  **顶部状态栏与导航栏**
    *   **状态栏**：
        *   时间显示："7:35 Mon Jun 3"
        *   网络与电量：Wi-Fi图标，"100%" 电量，电池图标。
    *   **导航栏**：
        *   返回按钮："<" 图标。
        *   计时器/状态："用时 01:24"。
        *   进度条：橙色填充的进度条，表示答题用时或进度。

2.  **主要内容区域**
    *   **左侧：题目展示区**
        *   **题目标题**："解答题 (2024春·浙江期中)"。
        *   **题目描述文本**：
            "有一个可自由转动的转盘，被分成了三个大小相同的扇形，分别标有数字，另有一个不透明的瓶子，装有分别标有数字的三个完全相同的小球。小杰先转动一次转盘，停止后记下指针指向的数字（若指针指在分界线上则重转），小玉再从瓶子中随机取出一个小球，记下小球上的数字。"
        *   **题目插图**：
            *   **转盘图**：一个圆形转盘，均分为三个扇形，扇区内分别标有数字 "2"、"6"、"4"。中心有指针。右下角有放大镜图标。
            *   **瓶子与小球示意图**：一个不透明的瓶子，内部用虚线示意性地画出三个小球。瓶子下方有三个带圈的数字标识 "①"、"③"、"⑤"，可能代表小球的编号或位置。右下角有放大镜图标。
        *   **设问**：
            "(1) 请用列表或画树状图的方法（选其中一种）表示出所有可能出现的结果；"
    *   **右侧：答题引导与操作区**
        *   **提示信息**："请认真作答, 老师会检查作答结果"。
        *   **答题方式选择**：
            *   **键盘输入**：图标与文字 "键盘输入"。
            *   **拍照作答**：图标与文字 "拍照作答"。
        *   **图片上传区域**：
            *   图标：相机图标。
            *   文字提示："拍照上传"。
            *   限制说明："最多可以传4张"。

3.  **底部操作栏**
    *   **不确定按钮**：白色背景，文字 "不确定"。
    *   **提交按钮**：橙色背景，文字 "提交"。

【============== 图片解析 END ==============】

![in_table_GWmGbKabgoupdgxVeF0cSdg5nbh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GWmGbKabgoupdgxVeF0cSdg5nbh.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片为一个在线学习平台的答题界面，主要包含以下几个部分：

1.  **顶部导航栏**:
    *   **左侧**:
        *   **退出学习按钮**: 带有左向箭头图标，文字为“退出学习”。
        *   **用时显示**: “用时 00:10”。
    *   **右侧**:
        *   **学习地图按钮**: 文字为“学习地图”。
        *   **反馈按钮**: 文字为“反馈”。

2.  **题目区域**:
    *   **题型**: “单选”。
    *   **题目描述**: “关于空间几何，以下哪个描述是正确的？”
    *   **选项**:
        *   A: “打点计时器”
        *   B: “打点计时器”
        *   C: “打点计时器”
        *   D: “打点计时器”

3.  **底部浮动工具栏 (批注工具)**:
    *   **颜色选择区**:
        *   蓝色圆形 (当前选中)
        *   橙色圆形
        *   黑色圆形
        *   三个不同粗细的黑色画笔线条示例图标。
    *   **功能按钮区**:
        *   画笔图标 (当前选中，蓝色)
        *   斜线/橡皮擦图标 (根据OCR为 "/")
        *   删除图标 (垃圾桶)
        *   撤销图标 (左弯箭头)
        *   重做图标 (右弯箭头)
        *   **完成按钮**: 文字为“完成”。

【============== 图片解析 END ==============】

![in_table_ZupTbomF3oZI4jxuRhzcqQjcnPF](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_ZupTbomF3oZI4jxuRhzcqQjcnPF.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个在线答题界面的用户界面。

1.  **整体布局**：界面分为顶部导航区、左侧题目区、右侧作答区和底部操作区。

2.  **顶部导航区**：
    *   左上角：一个向左的箭头图标，通常表示“返回”功能。
    *   中间靠左：“用时01:24”，表示当前答题已用时长。
    *   中间：一个橙色的水平进度条。

3.  **题目区域 (左侧)**：
    *   **题目标题**：“解答题 (2024春·浙江期中)”。
    *   **题目描述**：
        *   文字内容：“有一个可自由转动的转盘，被分成了三个大小相同的扇形，分别标有数字，另有一个不透明的瓶子，装有分别标有数字，的三个完全相同的小球。小杰先转动一次转盘，停止后记下指针指向的数字（若指针指在分界线上则重转），小玉再从瓶子中随机取出一个小球，记下小球上的数字。”
        *   图片内容：
            *   一个圆形转盘，均分为三个扇形，分别标有数字 $$2, 6, 4$$。
            *   一个瓶子图示，内有三个小球，分别标有数字 ③, ④, ⑤ (OCR识别为3, 4, 5)。
    *   **子问题**：“(1) 请用列表或画树状图的方法（选其中一种）表示出所有可能出现的结果；”

4.  **作答区域 (右侧)**：
    *   **提示信息**：“请认真作答，老师会检查作答结果”。
    *   **作答方式切换**：
        *   “键盘输入”按钮 (带键盘图标)。
        *   “拍照作答”按钮 (带相机图标，当前为选中或激活状态)。
    *   **图片上传区** (当“拍照作答”被选择时)：
        *   已上传图片：显示两张图片缩略图，每张缩略图右上角有“X”删除按钮。
        *   上传控件：“拍照上传 最多可以传4张”，表现为一个带相机图标的虚线框，用于触发新的图片上传。

5.  **底部操作区域**：
    *   左侧：“不确定”按钮 (灰色背景，白色文字)。
    *   右侧：“提交”按钮 (橙色背景，白色文字)。

【============== 图片解析 END ==============】

![in_table_HlTBbPcf6o4K4vxxGlscpUlenWh](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_HlTBbPcf6o4K4vxxGlscpUlenWh.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

### 1. 整体界面布局
图片展示了一个在线答题界面，主要包含以下几个区域：
*   **顶部状态区**:
    *   左上角：返回按钮（`<` 图标）。
    *   中间："用时 01:24"，表示答题所用时间。
    *   下方：一个橙色的进度条。
*   **左侧题目区域**:
    *   **题目标题**: "解答题（2024春·浙江期中）"
    *   **题目描述**:
        *   "有一个可自由转动的转盘，被分成了三个大小相同的扇形，分别标有数字，另有一个不透明的瓶子，装有分别标有数字的三个完全相同的小球。小杰先转动一次转盘，停止后记下指针指向的数字（若指针指在分界线上则重转），小玉再从瓶子中随机取出一个小球，记下小球上的数字。"
    *   **题目配图**:
        *   左图：一个圆形转盘，均分为三等份，分别标有数字 "2", "4", "6"。
        *   右图：一个不透明瓶子的示意图，内含三个小球，从外部可见小球上分别标有数字 "①", "③", "⑤" (对应数字1, 3, 5)。
    *   **子问题**:
        *   "(1)请用列表或画树状图的方法(选其中一种)表示出所有可能出现的结果；"
        *   "(2)若得到的两数字之和是偶数，则小杰赢；若得到的两数字之和是奇数，则小玉赢。这个游戏公平吗？请说明理由。"
*   **右侧辅助/作答区域**:
    *   **提示信息**: "请认真作答，老师会检查作答结果"
    *   **作答方式按钮**:
        *   "键盘输入" (带键盘图标)
        *   "拍照作答" (带相机图标)
    *   **文本框内容**:
        *   "共有9种等可能的结果，其中“和为3的倍数”的有3种（3、6、9），“和为7的倍数”的有3种（5、7、11）。因此，小芳和小亮的获胜概率都是1/3。由于两者的获胜概率相等，因此这个游戏是公平的。"
        *   *注意：此文本框内容描述的游戏规则（“和为3的倍数”、“和为7的倍数”，参与者“小芳”、“小亮”）与左侧题目描述的游戏规则（“和为偶数”、“和为奇数”，参与者“小杰”、“小玉”）不一致。*
*   **底部操作区域**:
    *   "不确定" 按钮
    *   "提交" 按钮 (橙色背景)

### 2. 关键元素及其关联
#### 2.1 题目核心逻辑 (基于左侧题目描述)
这道题目是一个概率问题，涉及两个独立的随机事件：
1.  小杰转动转盘得到一个数字。
2.  小玉从瓶中取球得到一个数字。

游戏胜负判断依据这两个数字之和的奇偶性。

#### 2.2 题目中描述的游戏流程
```mermaid
flowchart TD
    A[开始] --> B{小杰转动转盘};
    B -- 指针指向数字 --> C["记录转盘数字 (来自集合 {2, 4, 6})"];
    B -- 指针指在分界线 --> B;
    C --> D{小玉从瓶中随机取球};
    D --> E["记录小球数字 (来自集合 {1, 3, 5})"];
    E --> F[计算两数字之和];
    F --> G{和是偶数?};
    G -- 是 --> H[小杰赢];
    G -- 否 (和是奇数) --> I[小玉赢];
    H --> J[游戏结束];
    I --> J;
```

#### 2.3 界面交互元素
*   用户通过阅读左侧题目信息理解问题。
*   用户可以在右侧选择“键盘输入”或“拍照作答”来提交答案。
*   右侧文本框提供了一段分析文本，但其内容与当前题目不完全匹配。
*   用户最终通过“不确定”或“提交”按钮结束作答。

【============== 图片解析 END ==============】

![in_table_RkOBbwIHzoVq1axoMMXcUPWynZf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RkOBbwIHzoVq1axoMMXcUPWynZf.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

### 例3 已知曲线 \( y = \frac{1}{3}x^3 + \frac{4}{3} \)

#### (1) 求曲线在点 \( P(2, 4) \) 处的切线方程；
#### (2) 求过点 \( P(2, 4) \) 的曲线的切线方程；
#### (3) 求斜率为 4 的曲线的切线方程。

### 解析

#### (1)
*   ∵ \( y' = x^2 \)
*   ∴ 在点 \( P(2, 4) \) 处的切线的斜率 \( k = y'|_{x=2} = 4 \)
*   ∴ 曲线在点 \( P(2, 4) \) 处的切线方程为 \( y - 4 = 4(x - 2) \)，即 \( 4x - y - 4 = 0 \)

#### (2)
设曲线 \( y = \frac{1}{3}x^3 + \frac{4}{3} \) 与过点 \( P(2, 4) \) 的切线相切于点 \( A\left(x_0, \frac{1}{3}x_0^3 + \frac{4}{3}\right) \)，则切线的斜率 \( k = y'|_{x=x_0} = x_0^2 \)

【============== 图片解析 END ==============】

![in_table_R6B1bPOmBoLL91xE84GcTmmMnfe](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_R6B1bPOmBoLL91xE84GcTmmMnfe.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

图片展示了两个并列的UI元素，分别代表了文件上传过程中的两种不同状态。

1.  **左侧UI元素：上传中状态**
    *   **顶部标签文本：** “上传中”
    *   **核心视觉单元：** 一个深灰色圆角矩形卡片。
        *   **背景内容：** 卡片内部填充了模糊的、类似代码或文本的占位符图案。
        *   **状态指示器：** 卡片中央有一个白色的圆形旋转加载动画（菊花loading）。

2.  **右侧UI元素：上传失败状态**
    *   **顶部标签文本：** “上传...”
    *   **核心视觉单元：** 一个深灰色圆角矩形卡片。
        *   **背景内容：** 卡片内部填充了模糊的、类似代码或文本的占位符图案。
        *   **失败提示文本：** 卡片中央叠加显示白色文字 “上传失败”。
        *   **操作指引文本：** 在“上传失败”文本下方，叠加显示白色文字 “点击重试”。

【============== 图片解析 END ==============】

![in_table_TcU4bQiQEoeDBsxwGdUc72yPnrb](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_TcU4bQiQEoeDBsxwGdUc72yPnrb.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】
```json
{
  "title": "应用内题目解答界面",
  "description": "此界面展示了一道概率题目及其解答过程的反馈。",
  "elements": [
    {
      "type": "header",
      "label": "顶部状态栏",
      "properties": {
        "time": "7:35",
        "date": "Mon Jun 3",
        "network_status": "WiFi信号满格",
        "battery_status": "100%"
      }
    },
    {
      "type": "navigation_bar",
      "label": "导航栏",
      "elements": [
        {
          "type": "back_button",
          "label": "返回按钮",
          "icon": "向左箭头"
        },
        {
          "type": "timer",
          "label": "用时计时器",
          "text": "用时01:24"
        },
        {
          "type": "progress_bar",
          "label": "进度条"
        }
      ]
    },
    {
      "type": "main_content",
      "label": "题目内容区域",
      "elements": [
        {
          "type": "section_header",
          "label": "题目元信息",
          "text": "解答题 (2024春·浙江期中)"
        },
        {
          "type": "problem_description",
          "label": "题目描述",
          "text": "有一个可自由转动的转盘,被分成了三个大小相同的扇形,分别标有数字,另有一个不透明的瓶子,装有分别标有数字,的三个完全相同的小球。小杰先转动一次转盘,停止后记下指针指向的数字(若指针指在分界线上则重转),小玉再从瓶子中随机取出一个小球,记下小球上的数字。"
        },
        {
          "type": "images_container",
          "label": "图片展示",
          "elements": [
            {
              "type": "image",
              "description": "转盘示意图，平均分为三份，分别标有数字2, 4, 6，指针指向2和6之间的区域。",
              "icon": "放大镜图标"
            },
            {
              "type": "image",
              "description": "瓶子示意图，内有三个小球，分别标有数字3, 4, 5。",
              "icon": "放大镜图标"
            }
          ]
        },
        {
          "type": "question_prompt",
          "label": "问题",
          "text": "(1)请用列表或画树状图的方法(选其中一种)表示出所有可能出现的结果;"
        }
      ]
    },
    {
      "type": "answer_section",
      "label": "答案区域",
      "elements": [
        {
          "type": "user_answer_display",
          "label": "你的答案",
          "elements": [
            {
              "type": "image_thumbnail",
              "description": "用户答案的图片缩略图1"
            },
            {
              "type": "image_thumbnail",
              "description": "用户答案的图片缩略图2"
            }
          ]
        },
        {
          "type": "correct_answer_display",
          "label": "核对答案",
          "text": "(1) 温室大棚,前部采用固定的透光棚膜,可以保证植物体在白天获得充分的光照,增强光合作用,提高产量;(2) 根据纬度不同,设计“前屋面角度”不同。"
        },
        {
          "type": "feedback_buttons",
          "label": "答题反馈按钮组",
          "elements": [
            {
              "type": "button",
              "label": "我答对了",
              "icon": "√"
            },
            {
              "type": "button",
              "label": "部分答对",
              "icon": "部分√"
            },
             {
              "type": "button",
              "label": "我答错了",
              "icon": "×",
              "status": "selected"
            }
          ]
        }
      ]
    },
    {
      "type": "analysis_section",
      "label": "题目解析区域",
      "elements": [
        {
          "type": "section_title",
          "label": "题目解析标题",
          "text": "题目解析"
        },
        {
          "type": "analysis_content",
          "label": "解析内容",
          "text": "题目解析题目解析题目解析。"
        },
        {
          "type_action_buttons",
          "label": "操作按钮组",
          "elements": [
            {
              "type": "button",
              "label": "加入错题本"
            },
            {
              "type": "button",
              "label": "继续",
              "style": "primary"
            }
          ]
        }
      ]
    }
  ]
}
```
【============== 图片解析 END ==============】

![in_table_GQA1bq8YEoUpTCxLeFbcRT45npd](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_GQA1bq8YEoUpTCxLeFbcRT45npd.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

此图片展示了代表三种答题结果状态的UI元素（通常为按钮或标签），每种状态有两种视觉样式：

一、**顶部行为（默认/未选中样式）：**
    *   元素均具有白色背景和圆角矩形。
    *   **状态1: 我答对了**
        *   图标：黑色勾号（✓）
        *   文本："我答对了"（黑色）
    *   **状态2: 部分答对**
        *   图标：黑色 "x" 符号叠加小勾号（⨉✓）
        *   文本："部分答对"（黑色）
    *   **状态3: 我答错了**
        *   图标：黑色叉号（×）
        *   文本："我答错了"（黑色）

二、**底部行为（高亮/选中样式）：**
    *   元素均具有白色背景和圆角矩形，但边框、图标和文字颜色与状态对应。
    *   **状态1 (依据OCR为“绿色框”): 我答对了**
        *   图标：绿色勾号（✓）
        *   文本："我答对了"（绿色）
        *   边框：绿色
    *   **状态2 (依据OCR为“黄色框”): 部分答对**
        *   图标：橙色/黄色 "x" 符号叠加小勾号（⨉✓）
        *   文本："部分答对"（橙色/黄色）
        *   边框：橙色/黄色
    *   **状态3 (依据OCR为“红色框”): 我答错了**
        *   图标：红色叉号（×）
        *   文本："我答错了"（红色）
        *   边框：红色

【============== 图片解析 END ==============】



## 六、数据需求

#### 6.1 效果追踪

描述计划监控数据效果的方式及所需数据字段

#### 6.2 数据埋点

<table>
<tr>
<td>**页面**<br/></td><td>**模块**<br/></td><td>**动作**<br/></td><td>**埋点名称**<br/></td><td>**图示（如需）**<br/></td></tr>
<tr>
<td><br/></td><td><br/></td><td><br/></td><td><br/></td><td><br/></td></tr>
</table>

## 七、a/b 实验需求

暂无

## 附：评审记录

### 🗑️ 回收站

![Mt8LwvVXPhOtAIbkqfpcSvJ2nzf](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/board_Mt8LwvVXPhOtAIbkqfpcSvJ2nzf.png)

###### 图片分析
【============== 图片解析 BEGIN 逻辑图 ==============】

该图片展示了用户在互联网教育产品中进行答题、获取反馈及查看解析的完整流程。

**流程结构:**

1.  **初始状态**
    *   **查看题目 (题干+作答区)**: 用户进入答题界面的初始动作。
        *   分支A: **多个填空作答区输入内容**: 用户开始在作答区域输入答案。
        *   分支B: **不确定**: 用户对题目感到不确定。

2.  **作答状态**
    *   从 **多个填空作答区输入内容** 出发:
        *   **提交**: 用户完成作答并提交答案。
    *   从 **不确定** 出发:
        *   **放弃作答 (二次确认)**: 用户选择放弃作答，系统进行二次确认。
            *   可返回 **多个填空作答区输入内容** (由虚线箭头表示，可能代表取消放弃或重新作答)。

3.  **作答反馈** (这是一个状态集合)
    *   **回答正确**: 用户提交答案后，系统判定为正确。
    *   **回答错误**: 用户提交答案后，系统判定为错误。
    *   **未作答**: 用户放弃作答后，题目状态为未作答。

4.  **解析状态**
    *   从 **回答正确 / 回答错误 / 未作答** 三种反馈状态均可进入:
        *   **查看答案、题目解析**: 用户查看题目的正确答案和详细解析。
            *   **查看考察知识点**: 用户进一步查看该题目关联的知识点。
                *   **猜你想问**: 系统根据题目和用户行为，推荐相关问题。
                    *   分支1: **点击推荐问题**: 用户点击系统推荐的问题。
                    *   分支2: **点击自由提问**: 用户选择自主输入问题。
                    *   最终均导向: **答疑 chat**: 进入与AI或助教的问答交互界面。

**Mermaid 流程图描述:**

```mermaid
flowchart TD
    subgraph 初始状态
        A["查看题目\n(题干+作答区)"]
    end

    subgraph 作答状态
        B["多个填空作答区\n输入内容"]
        C[不确定]
        D[提交]
        E["放弃作答\n(二次确认)"]
    end
    
    subgraph 作答反馈
        F[回答正确]
        G[回答错误]
        H[未作答]
    end

    subgraph 解析状态
        I[查看答案、题目解析]
        J[查看考察知识点]
        K[猜你想问]
        L[点击推荐问题]
        M[点击自由提问]
        N[答疑 chat]
    end

    A --> B
    A --> C
    
    B --> D
    C --> E
    E -.-> B

    D --> F
    D --> G
    E --> H
    
    F --> I
    G --> I
    H --> I
    
    I --> J
    J --> K
    K --> L
    K --> M
    L --> N
    M --> N
```

【============== 图片解析 END ==============】



<table>
<tr>
<td>**状态**<br/></td><td>**原型图**<br/></td><td>**需求说明**<br/></td></tr>
<tr>
<td>初始状态<br/></td><td>![in_table_RDeJbuYxUoGOgPxoO9KcjJf2nGg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RDeJbuYxUoGOgPxoO9KcjJf2nGg.png)<br/><br/></td><td>**状态判断：**<br/>- 当进入题目且没有作答时（即填空作答区没有输入内容）为初始状态。<br/>**题型校验：**<br/>- 录入学科：<br/>- 录入题型：填空题。<br/>- 作答区 > 1。<br/>**页面框架：**<br/>- 仅支持全屏展示。<br/>- 退出学习：- 按钮名称：退出学习。- 交互：点击后退出至，xx 页面。<br/>- 作答计时：- 展示：用时 xx ：xx 。- 最大时间：59 ：59 。<br/>- 题干区：- 题型标签：填空- 作答进度：分子已作答数量，分母填空数量。- 题干内容：- 数据源自题库中已录入题目。- 题干支持展示文字、图片。<br/>- 作答区：- 填空区域，以输入框的形式插入题干中。- 插入位置，数据源自题库中已录入题目，取题目中 “____”或 “（）”位置。- 不确定：- 按钮状态：可点击。- 交互：点击后按钮变更为【放弃作答】，- 点击【放弃作答】，题目进入解析状态，展示解析状态内容。- 输入内容后，【放弃作答】按钮回退至【不确定】，同时【提交】按钮变更为可点击。- 提交：- 按钮状态：不可点击，按钮样式置灰。- 交互：/<br/></td></tr>
<tr>
<td>作答状态<br/></td><td>![in_table_SzC2bzRAXozphuxf4rzcZmDSn6c](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SzC2bzRAXozphuxf4rzcZmDSn6c.png)<br/><br/></td><td>**状态判断：**<br/>- 当进入题目且有作答时（即填空作答区输入了内容）为作答状态。<br/>**批改方式：**<br/>- 自动批改<br/>**作答方式：**<br/>- 支持通过键盘输入文字将内容填充到题目的对应位置。- 点击填空区域，展示选中状态并调起键盘支持输入文字。- 点击收起键盘按钮/点击填空区域外其他区域，收起键盘，取消选中状态。- 点击键盘回车键，进入下一空。<br/>- 操作按钮：- 提交：- 按钮状态：可点击。- 交互（需校验）：- 点击时，当输入框有内容时，提交作答数据，题目进入解析状态，展示解析状态内容。- 点击时，当输入框没有内容，弹窗确认是否继续提交，继续提交，题目进入解析状态，展示解析状态内容。- 全部空为空时，- 弹窗内容：本题全部未答，确认提交吗？- 操作按钮：- 我再想想，点击后关闭弹窗。- 继续提交，点击后提交作答，进入解析状态。- 部分空为空时，- 弹窗内容：本题尚未全部答完，确认提交吗？- 操作按钮：- 我再想想，点击后关闭弹窗。- 继续提交，点击后提交作答，进入解析状态。<br/></td></tr>
<tr>
<td>解析状态<br/></td><td>![in_table_YXU6bMAZ1oUa5IxqaeKcDUPun7c](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_YXU6bMAZ1oUa5IxqaeKcDUPun7c.png)<br/><br/></td><td>**状态判断：**<br/>- 当题目有作答（即填空作答区输入了内容）并点击【提交】后为解析状态。<br/>- 当题目没有作答，点击了【不确定】 - 【放弃作答】后为解析状态。<br/>**展示内容：**<br/>- 结果反馈弹窗：- 回答全部正确：- IP + 文案：- 回答有 >= 1 空错误：- IP + 文案：- 未答完提交：- IP + 文案：- 不确定：- IP + 文案：<br/>- 填空选择器：- 按照从上到下从左到右的顺序展示展示填空的序号，序号从 1 开始。- 支持用户通过点击序号来快速定位到各个空。<br/>- 答案：- 回答正确时，对应的作答区域标记为正确突出展示，并在下方展示正确答案和你的答案（用户输入的答案）。- 回答错误时，对应的作答区域标记为错误突出展示，并在下方展示正确答案和你的答案（用户输入的答案）。- 未作答时，对应的作答区域标记为错误突出展示，并在下方展示正确答案和你的答案（“未作答”）。- 不确定 - 放弃作答时，对应的作答区域标记为不确定突出展示。，并在下方展示正确答案和你的答案（“未作答”）。<br/>- 题目解析：- 内容：数据源自题库中已录入题目，对应字段【题目解析】。<br/>- 猜你想问：- 展示： 自由提问 + 模型生成的问题（最多 3 个 可以为 0 ）- 内容：可选问题由模型参考题目和解析生成，自由提问为固定入口。- 发起对话：由用户发起首轮对话。<br/>- 操作按钮：- 继续：- 出现时机：题目进入解析状态。- 按钮状态：可点击。- 交互：点击后进入下一题/下一节内容。<br/></td></tr>
</table>

![in_table_RDeJbuYxUoGOgPxoO9KcjJf2nGg](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_RDeJbuYxUoGOgPxoO9KcjJf2nGg.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

*   **页面名称**: 在线作答页面
*   **组成部分**:
    *   **1. 系统状态栏 (System Status Bar)**
        *   左上角: "Pad" (设备或运营商标识)
        *   顶部中央: "10:13 AM" (当前时间)
        *   右上角: "93%" (电池电量及图标)
    *   **2. 应用内导航栏 (In-App Navigation Bar)**
        *   左侧: 返回按钮 (图标为 "<")
        *   右侧: 计时器 (显示 "01:24")
    *   **3. 题目区域 (Question Area)**
        *   **3.1. 作答进度指示 (Answering Progress Indicator)**
            *   文本: "作答进度：0/3"
        *   **3.2. 题目详情 (Question Details)**
            *   题型: "填空"
            *   来源: "(2020温州一模)"
            *   题目文本及填空位:
                *   文本段1: "Go with your passion.It has been said that who see the invisible canca who se do the impossible. When I was nine years old living in a small town in North Carolina I for smalivinga greeting cards in the back of a children's"
                *   **填空框1 (Input Field 1)**
                *   文本段2: "I can do this. Imyself magazi begged my mother to let me send for"
                *   **填空框2 (Input Field 2)**
                *   文本段3: "when the kit arrived, an sendd arrivedo ripped off the brown paper wrapper, grabbed the cards and dashed from the house. thebrow and an proclaiming,"
                *   **填空框3 (Input Field 3)**
                *   文本段4: "“Mama. all the people couldn't wait to buy my cards!”"
    *   **4. 操作按钮区 (Action Button Area)**
        *   右下角: "提交" 按钮

【============== 图片解析 END ==============】

![in_table_SzC2bzRAXozphuxf4rzcZmDSn6c](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_SzC2bzRAXozphuxf4rzcZmDSn6c.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

该图片展示了一个平板电脑的界面截图，主要包含以下几个层面和关键元素：

一、 **顶部状态栏 (Top Status Bar)**
    *   左侧区域：
        *   返回按钮：一个向左的箭头图标 (`<`)。
        *   计时器/时间显示：`01:24`。
    *   中间区域：
        *   当前系统时间：`10:13 AM`。
    *   右侧区域：
        *   文本提示：`作答进度：1/3`。
        *   电池电量百分比：`93%`。
        *   电池状态图标。

二、 **内容显示与编辑区域 (Content Display and Editing Area)**
    *   **文本段落 (Text Paragraphs)**：
        *   显示多行英文文本，内容如下（根据 OCR 文本，结合图片显示情况）：
            *   "do the impossible. When I was nine years old living in a small town in North Carolina for smalivinga"
            *   "greeting cards in the back of a children's people I can do this. Imyself magazi"
            *   "begged my mother to let me send for According to an when the kit arrived, an sendd arrivedo"
            *   "ripped off the brown paper wrapper, grabbed the cards and dashed from the house. thebrow and an"
            *   "proclaiming, "Mama, all the people couldn't wait to buy my cards!""
        *   文本交互元素：
            *   单词高亮：单词 "people" 被黑色背景及白色文字高亮显示。
            *   输入建议框：一个蓝色边框的矩形框，内含文字 "According to an"，位于 "begged my mother to let me send for" 之后。
    *   **输入法辅助工具栏 (Input Method Auxiliary Toolbar)**：位于文本区域下方、虚拟键盘上方。
        *   左侧按钮组：
            *   撤销按钮 (向左弯曲箭头图标)。
            *   重做按钮 (向右弯曲箭头图标)。
            *   粘贴/格式按钮 (一个包含文档堆叠的图标)。
        *   右侧按钮组：
            *   向上箭头按钮。
            *   向下箭头按钮。

三、 **底部虚拟键盘区域 (Bottom Virtual Keyboard Area)**
    *   **键盘按键布局 (Keyboard Layout)**：标准的 QWERTY 英文键盘。
        *   第一行 (数字/字母行)：`tab`, `Q`(上标1), `W`(上标2), `E`(上标3), `R`(上标4), `T`(上标5), `Y`(上标6), `U`(上标7), `I`(上标8), `O`(上标9), `P`(上标0), `delete`。
        *   第二行 (字母行)：`拼音` (中文标签), `A`(上标@), `S`(上标#), `D`(上标$), `F`(上标&), `G`(上标*), `H`(上标-), `J`(上标(), `K`(上标)), `L`(上标'), `go` (蓝色背景，白色文字)。
        *   第三行 (字母行)：`shift` (向上箭头图标), `Z`(上标%), `X`(上标_), `C`(上标+), `V`(上标=), `B`(上标/), `N`(上标;), `M`(上标:), `,`(上标 ",", OCR中为","), `.`(上标 ".", OCR中为"!"), `!`(上标"!", OCR中为"?"), `?`(上标"?"), `shift` (向上箭头图标)。 (注：根据图片视觉，个别符号键的次要字符与OCR识别结果略有出入，已按图片视觉标注)
        *   第四行 (功能/空格行)：
            *   地球图标 (语言/输入法切换)。
            *   `.?123` (切换到数字/符号键盘)。
            *   麦克风图标 (语音输入)。
            *   空格键 (长条形按键)。
            *   `.?123` (切换到数字/符号键盘)。
            *   键盘收起图标。

**元素间关联性：**
*   顶部状态栏提供系统级及应用上下文信息（如作答进度、时间）。
*   内容显示与编辑区域是用户进行文本阅读和输入交互的主要场所。
    *   文本段落展示了当前编辑或阅读的内容。
    *   单词高亮和输入建议框是文本编辑过程中的辅助功能，通常与输入法或系统文本处理相关。
*   输入法辅助工具栏提供编辑相关的快捷操作，如撤销、重做。
*   底部虚拟键盘是主要的文本输入工具，用户通过点击按键在内容显示与编辑区域输入字符或执行命令（如`go`代表确认或换行）。
*   键盘上的 `拼音`、地球图标、`.?123` 等按键用于切换输入模式、语言或键盘布局。

【============== 图片解析 END ==============】

![in_table_YXU6bMAZ1oUa5IxqaeKcDUPun7c](https://static.test.xiaoluxue.cn/LuBan/format-prd/images/image_YXU6bMAZ1oUa5IxqaeKcDUPun7c.png)

###### 图片分析
【============== 图片解析 BEGIN UI图 ==============】

### 1. 最上部头部

*   **左侧**:
    *   `<`: 返回导航图标。
    *   `01:24`: 剩余时间或经过时间显示。
*   **中央**:
    *   `作答进度：3/3`: 解答进度状况。全3题中的第3题，或者在1个问题中3个空白处全部已作答完成。
*   **右侧**:
    *   `10:13 AM`: 当前时间显示。
    *   电池图标和剩余电量 `93%`: 设备的电池状态。

### 2. 题目显示区域

*   **题目类型**:
    *   `填空`: 表示题目形式为"填空题"。
*   **题目出处/标签**:
    *   `(2020温州一模)`: 题目的出处或相关信息标签。
*   **题目文本**:
    *   显示英文文本，文中包含多个空白处。
    *   以下3处用绿色框围起来，并附有绿色对勾标记。这些表示用户输入并答对的地方及其内容。
        1.  `children's` 后面显示的 `people`
        2.  `send for` 后面显示的 `According to an`
        3.  `proclaiming,` 后面显示的 `hello`
    *   题目文本（参考OCR）: "Go with your passion. It has been said that who see the invisible canca who se do the impossible. When I was nine years old living in a small town in North Carolina I for smalivinga greeting cards in the back of a children's [✓ people] I can do this. Imyself magazi begged my mother to let me send for [✓ According to an] when the kit arrived, an sendd arrivedo ripped off the brown paper wrapper, grabbed the cards and dashed from the house. thebrow and an proclaiming, [✓ hello] "Mama. all the people couldn't wait to buy my cards !""

### 3. 解答反馈及导航区域（下部）

*   **题目编号/空白处导航**:
    *   数字从 `1` 到 `10` 排列，其中 `2` 以蓝色背景高亮显示。这表示有多个小题或空白处，当前正在显示第2个小题/空白处的相关信息。
*   **正确答案・用户解答显示**:
    *   `正确答案：magazine`: 表示当前选择的小题/空白处（这里是第2个）的正确答案是 "magazine"。
    *   `你的答案：empower`: 表示用户对这个小题/空白处输入的答案是 "empower"。
*   **题目解析**:
    *   `题目解析：根据原电池的反应原理可知...`: 针对当前选择的小题/空白处的解析文本。
*   **操作按钮**:
    *   `下一题`: 用于进入"下一题"的按钮。

### 各元素间的关联性

此UI是在线教育平台中填空题的解答・反馈界面。

1.  **头部**提供当前解答会话的整体状况（时间、进度）和系统信息。
2.  **题目显示区域**呈现当前题目文本，并对已经答对的空白处内容进行视觉反馈。
3.  **解答反馈及导航区域**显示题目内特定小题/空白处（通过分页选择的）对应的用户解答和正确答案，以及其解析，并提供进入下一题的功能。
    *   题目编号/空白处导航（`1`～`10`）对应题目显示区域内的多个空白处，显示当前选择的空白处（这里是`2`）的"正确答案"和"你的答案"以及"题目解析"。
    *   题目文本中带绿框和对勾标记的元素 (`people`, `According to an`, `hello`) 分别表示其他空白处用户的正确答案内容。
    *   "作答进度：3/3"表示这道题有3个空白处，全部已作答完成。

【============== 图片解析 END ==============】

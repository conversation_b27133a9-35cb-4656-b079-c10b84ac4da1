# PostgreSQL 数据库设计规范与约束

本文件详细定义了在使用 PostgreSQL 数据库时必须遵循的数据库设计规范和技术约束。

## 1. 通用数据库设计原则 (适用于 PostgreSQL)

* **单一职责原则 (SRP - Single Responsibility Principle)**：每张表只表示一个明确的实体。例如：`users` 表表示用户信息，`orders` 表表示订单信息。避免将多个逻辑混在一张表中。
* **开闭原则 (OCP - Open/Closed Principle)**：表结构应能灵活扩展。例如，通过 JSON 字段存储动态扩展信息，而不直接修改表结构；新增表而非修改现有表结构来支持新功能。
* **里氏替换原则 (LSP - Liskov Substitution Principle)**：子表的设计应与其父表兼容。例如用户角色表 (`admin_users`, `customer_users`) 应与 `users` 表结构兼容。
* **数据隔离原则 (DSP - Data Segregation Principle)**：严格按照业务逻辑对表进行划分。例如用户表、订单表、支付表分开，避免在单张表中混合多种数据类型。
* **迪米特法则 (LoD - Law of Demeter) / 最少知识原则 (Least Knowledge Principle)**：表之间只建立绝对必要的关系。避免过度关联，保证关系清晰，例如订单表仅关联用户表的 `user_id`，而不是用户的详细信息。
* **高内聚低耦合 (High Cohesion and Low Coupling)**：确保每张表内部的数据高度相关，表间耦合度降至最低。用户表只存储用户信息，订单表只存储订单信息，通过逻辑外键或关联表实现耦合。
* **KISS (Keep It Simple, Stupid)**：数据库表设计应保持简单明了。字段命名简单明确，例如 `created_at` 而非 `ca`；复合索引遵循实际查询顺序。
* **YAGNI (You Ain't Gonna Need It)**：避免过度设计，不预先设计当前不需要的表或字段。仅在需求明确时新增表或字段。
* **关注点分离 (SoC - Separation of Concerns)**：将不同业务逻辑的数据清晰地划分到不同的表或数据库中。例如，核心业务数据（用户、订单）和日志数据（操作日志、错误日志）分库存储。
* **DRY (Don't Repeat Yourself)**：避免在多处重复存储相同的数据。使用逻辑外键而非重复数据；通过视图或 JOIN 查询组合数据，而非冗余字段。

## 2. 应避免的数据库设计反模式

* **数据孤岛 (Data Silos)**：多个数据库分散管理相同业务数据（如用户数据分散在多个表中）。
    * **正确设计**：在一个数据库中以逻辑外键或联合表方式管理关联数据；或建立数据中台（Data Platform）统一管理数据。
* **巨石表 (God Table)**：一个表包含所有与业务相关的字段，如"用户表"包含用户名、密码、地址、订单等信息。
    * **正确设计**：按领域分表，用户表只存用户核心信息，订单表存订单信息；通过 `user_id` 建立一对多关系。
* **过度规范化 (Over Normalization)**：数据表过度分拆（如每个订单状态单独一个表），导致查询复杂、性能低下。
    * **正确设计**：适度规范化，在三范式基础上，保持性能和可读性平衡；为高频查询字段建立冗余字段（如订单表中直接保存订单状态）。
* **重复数据 (Data Duplication)**：相同数据在多张表中重复存储，如用户信息在多个表重复。
    * **正确设计**：通过逻辑外键关联而非重复存储数据；使用视图对不同表数据组合形成统一视图。
* **过度依赖 JSON/Blob 字段**：大量核心业务数据存储在 JSON/Blob 字段中，难以检索和索引。
    * **正确设计**：高频查询字段应为结构化表字段，JSON 仅用于扩展信息或非核心数据；为 JSON 字段创建适当的索引（如 PostgreSQL 的 GIN 索引作用于 JSONB 字段）。
* **冗余索引 (Redundant Indexes)**：相同字段重复建立多个索引，或创建了多个基本等效的复合索引，增加写入成本和存储开销。
    * **正确设计**：根据实际查询模式设计最有效的索引组合，例如利用复合索引的最左前缀特性；定期检查并删除无效或冗余索引。
* **缺少 ACID 保证 (不当的事务处理)**：数据库事务设计不当，导致数据不一致。
    * **正确设计**：对多步原子性操作（如订单支付和库存扣减）使用事务；确保事务逻辑能正确捕获异常并执行回滚，保证操作失败时数据能恢复到一致状态。
* **过度依赖数据库触发器**：大量核心业务逻辑隐藏在数据库触发器中，使得业务逻辑分散且难以维护和调试。
    * **正确设计**：核心业务逻辑应在应用层实现，数据库主要负责数据存储和一致性保障；仅在必要时（如审计日志、维护冗余数据等场景）审慎使用触发器。
* **复杂的多表 JOIN 查询 (设计层面)**：如果表设计本身导致了常态化的复杂、低效的多表 JOIN。
    * **正确设计**：审视表结构，根据查询频率和关联复杂度进行适当的反规范化（引入冗余字段）或建立中间（汇总/物化）表；结合视图简化查询。

## 3. PostgreSQL 数据库设计约束

### 3.1 命名规范
  * **表名**: 推荐使用 `tbl_模块名_实体名` (例如 `tbl_user_op`)。对于系统中极其通用且核心的实体 (例如 `users`, `orders`), 可与团队确认后，考虑直接使用实体名词的复数形式作为表名，以求简洁。
  * **字段名**: 统一使用 `snake_case` (小写字母和下划线)，力求**简洁、明确，避免不必要的缩写**。
  * **索引名**:
      * 索引中引用的表名需要去掉前缀`tbl_` (例如 `tbl_user_op` 表的索引名应为 `idx_user_op_xxx`)
      * 单字段普通索引: `idx_{表名}_{字段名}` (例如 `idx_users_status`)
      * 单字段唯一索引: `uk_{表名}_{字段名}` (例如 `uk_users_username`)
      * 复合索引: `idx_{表名}_{字段1}_{字段2}` 或 `uk_{表名}_{字段1}_{字段2}` (例如 `idx_users_created_at_status`)
      * 函数索引等特殊索引: 使用能清晰描述索引目的的名称 (例如 `idx_users_lower_email`)。
  * **注释**: **所有表和关键字段都必须添加清晰、准确的中文注释** (注释内容应包括字段用途、枚举值的具体含义、特殊格式说明等)。

### 3.2 标准字段 (所有表必须包含)
  * `id BIGSERIAL PRIMARY KEY`
  * **重要变更**: **时间字段统一使用 `BIGINT` 存储 UTC 时间戳 (秒)**
  * `create_time BIGINT NOT NULL`: 创建时间 (UTC 秒数)
  * `update_time BIGINT NOT NULL`: 更新时间 (UTC 秒数)
  * `delete_time BIGINT DEFAULT NULL`: 删除时间 (UTC 秒数)，`NULL` 表示未删除
  * **可选标准字段 (按需添加)**:
      * `status SMALLINT NOT NULL DEFAULT 1` (务必在注释中说明各状态值的含义，例如：`状态 (0:禁用, 1:启用)`)
      * `lang VARCHAR(10) NOT NULL DEFAULT 'zh-CN'` (务必在注释中说明语言代码含义)
      * `created_by BIGINT NOT NULL`: 创建人 ID
      * `updated_by BIGINT NOT NULL`: 更新人 ID

### 3.3 字段类型选择
  * **主键**: 优先使用 `BIGSERIAL` (等同于 `bigint` + 默认自增序列)。若明确不需要超过 `2^31 - 1`，可使用 `SERIAL`。
  * **字符串**: **根据预估的最大长度精确选用 `VARCHAR(n)`**。仅在确实必要时 (例如存储大段文本内容) 才使用 `TEXT`。
  * **时间**: **重要变更**: 统一使用 `BIGINT` 存储 **UTC 时间戳 (秒)**。不推荐使用 `TIMESTAMP WITH TIME ZONE` 或 `TIMESTAMP WITHOUT TIME ZONE`。
  * **数字**: 整数类型根据数值范围选用 `INTEGER` 或 `BIGINT`。**小数或金额务必使用 `NUMERIC(p,s)` 类型** (p:总精度, s:小数位数)，以确保计算的准确性。避免使用 `FLOAT` 或 `REAL` (除非对精度要求不高)。
  * **布尔值/简单枚举**: 使用 `SMALLINT` 类型，并在字段注释中明确说明各数值的含义 (例如 `状态 (0:禁用, 1:启用)`)。也可以使用 `BOOLEAN` 类型表示真/假。
  * **JSON**: 对于半结构化数据，**强制使用 `JSONB` 类型**，它支持更高效的查询和索引。
  * **UUID**: 对于需要全局唯一标识符的场景 (尤其是在分布式系统中)，**推荐使用 `UUID` 类型**。PostgreSQL 可通过 `pgcrypto` 扩展的 `gen_random_uuid()` 函数生成。
  * **数组**: 可使用 PostgreSQL 的数组类型，如 `INTEGER[]`, `VARCHAR[]` 等，但需注意其查询和索引特性。

### 3.4 索引设计
  * **通用原则**：
      * 主键自动带有 B-Tree 索引。
      * 为所有**经常用于查询条件 (WHERE 子句)、排序 (ORDER BY 子句)、分组 (GROUP BY 子句) 的字段或字段组合**创建索引 (`CREATE INDEX ...`)。
      * 为**JOIN 操作的关联列**创建索引。
      * 为需要保证**唯一性的字段或字段组合**创建唯一索引 (`CREATE UNIQUE INDEX ...`)。
      * 设计复合索引时，务必遵循**最左前缀原则**。
      * **对于启用了软删除的表，其所有唯一索引和部分需要精确匹配的普通索引，都必须添加 `WHERE deleted_at IS NULL` 条件。**
      * **避免过度索引**，需在查询性能提升与写入性能损耗之间进行权衡。
      * **索引效率提示**：设计索引和查询时，应尽量避免在索引列上直接使用函数或进行类型转换，这可能导致索引失效。例如，`WHERE LOWER(column_name) = 'value'` 应考虑使用函数索引或在应用层处理大小写。
  * **PostgreSQL 索引类型选择**：
      * **B-Tree**: 默认索引类型，适用于大多数等值查询、范围查询和排序场景。
      * **Hash**: 仅适用于等值查询 (`=`)。在特定场景下可能比 B-Tree 快，但有其局限性（如 WAL 日志记录问题、不支持范围查询）。使用前需仔细评估。
      * **GIN (Generalized Inverted Index)**: 适用于数组、`JSONB`、全文搜索 (`tsvector`) 等包含多个"元素"或键值的数据类型。能高效处理"包含"类查询。
      * **GiST (Generalized Search Tree)**: 适用于更复杂的数据结构，如几何类型、范围类型、以及一些自定义数据类型和全文搜索。可以实现多种不同的索引策略。
      * **BRIN (Block Range Index)**: 适用于物理存储顺序与列值有强相关性的大表（例如时间序列数据中的时间戳列）。索引体积非常小，但查询性能依赖数据分布。
  * **函数索引**: 当查询条件经常涉及对列值进行函数运算的结果时，可以创建函数索引。例如：`CREATE INDEX idx_users_email_lower ON users (LOWER(email));`
  * **软删除索引规范**: 
      ```sql
      CREATE UNIQUE INDEX uk_users_username ON users(username) WHERE deleted_at IS NULL;
      CREATE INDEX idx_users_status ON users(status) WHERE deleted_at IS NULL;
      ```

### 3.5 分区表设计
  * **适用场景**: 对于记录数非常巨大（例如数亿甚至数十亿级别）且会持续增长的表，如果查询和维护操作经常只针对表中的一部分数据（如按时间范围），应考虑使用分区表。
  * **优点**: 提高查询性能（通过分区裁剪，仅扫描相关分区）、简化数据管理（如按分区快速删除旧数据、按分区进行备份恢复或维护操作）。
  * **设计要点**:
      * **选择分区键 (Partition Key)**: 通常选择查询中最常用的过滤条件列，如时间列（`created_at`）、地区ID、状态等。分区键的选择直接影响分区裁剪的效率。
      * **分区方法**: PostgreSQL 支持范围分区 (Range Partitioning)、列表分区 (List Partitioning) 和哈希分区 (Hash Partitioning)。根据分区键的数据特性和业务需求选择合适的方法。
      * **分区粒度**: 分区数量不宜过多（避免成千上万个分区导致元数据管理开销增大），也不宜过少（导致单个分区仍然过大）。需根据数据增长率、查询模式、维护周期等因素综合考虑。
      * **索引**: 通常为每个分区创建局部索引 (Local Indexes)，而不是在父表上创建全局索引 (Global Indexes 在 PostgreSQL 分区中不直接支持，但可以通过其他方式模拟)。
      * **主键与唯一约束**: 在分区表中，主键和唯一约束通常必须包含所有分区键列。

### 3.6 关键约束与行为
  * **外键约束**: **严格禁止在数据库层面使用物理外键约束 (FOREIGN KEY constraints)。** 表间关联通过逻辑外键字段在应用层面进行管理和维护。这样做是为了提升性能、降低表间耦合、简化分库分表和数据迁移操作，并给予应用层更大的灵活性来处理数据一致性问题。
  * **默认值**: 为字段设置**合理且明确的 `DEFAULT` 值** (例如 `0`, `''`, 时间戳字段使用相应的时间戳值)，或显式允许 `NULL` (`DEFAULT NULL`)。

## 4. PostgreSQL DDL 模板 (务必严格遵循)

```sql
-- SQL DDL 模板 (具体业务字段和索引需根据 PRD 智能填充)
CREATE TABLE 模块名_表名 (
  id BIGSERIAL PRIMARY KEY,
  -- === 业务字段 (请根据PRD智能填充，以下为示例) ===
  -- title VARCHAR(255) NOT NULL,
  -- content TEXT NULL,
  -- price NUMERIC(10, 2) NOT NULL DEFAULT 0.00,
  -- user_email VARCHAR(255) NULL, -- 示例: 此字段可能需要创建唯一索引
  -- settings JSONB NULL,           -- 示例: 使用 JSONB 类型存储配置信息
  -- unique_token UUID NULL DEFAULT gen_random_uuid(), -- 示例: 使用 UUID 类型作为唯一令牌
  -- === 标准字段 (请按实际需求添加或保留) ===
  status SMALLINT NOT NULL DEFAULT 1,
  lang VARCHAR(10) NOT NULL DEFAULT 'zh-CN',
  create_time BIGINT NOT NULL,
  update_time BIGINT NOT NULL,
  delete_time BIGINT DEFAULT NULL,
  -- created_by BIGINT NOT NULL,
  -- updated_by BIGINT NOT NULL
);

-- 添加表和字段的中文注释 (请根据PRD智能生成)
COMMENT ON TABLE 模块名_表名 IS '表用途的中文说明 (请根据PRD智能生成)';
COMMENT ON COLUMN 模块名_表名.id IS '主键ID';
-- === 业务字段注释 (请智能生成) ===
-- COMMENT ON COLUMN 模块名_表名.title IS '标题示例 (使用VARCHAR)';
-- COMMENT ON COLUMN 模块名_表名.content IS '内容示例 (必要时使用TEXT)';
-- COMMENT ON COLUMN 模块名_表名.price IS '价格示例 (使用NUMERIC)';
-- COMMENT ON COLUMN 模块名_表名.user_email IS '用户邮箱示例';
-- COMMENT ON COLUMN 模块名_表名.settings IS '配置信息 (使用JSONB存储)';
-- COMMENT ON COLUMN 模块名_表名.unique_token IS '唯一令牌 (使用UUID)';
-- === 标准字段注释 ===
COMMENT ON COLUMN 模块名_表名.status IS '状态 (例如 0:禁用, 1:启用, 2:待审核 - 请根据实际业务调整并明确说明)';
COMMENT ON COLUMN 模块名_表名.lang IS '语言代码 (例如 zh-CN, en-US)';
COMMENT ON COLUMN 模块名_表名.created_at IS '记录创建时间 (UTC秒数时间戳)';
COMMENT ON COLUMN 模块名_表名.updated_at IS '记录最后更新时间 (UTC秒数时间戳)';
COMMENT ON COLUMN 模块名_表名.deleted_at IS '软删除标记时间 (UTC秒数时间戳, NULL表示未删除)';
COMMENT ON COLUMN 模块名_表名.created_by IS '创建人的用户ID';
COMMENT ON COLUMN 模块名_表名.updated_by IS '最后更新人的用户ID';

-- === 索引定义示例 (请按需添加，并严格遵循索引命名规范和软删除规则) ===
-- CREATE INDEX idx_模块名_表名_status ON 模块名_表名(status) WHERE deleted_at IS NULL; -- 普通索引示例 (已考虑软删除)
-- CREATE UNIQUE INDEX uk_模块名_表名_user_email ON 模块名_表名(user_email) WHERE deleted_at IS NULL; -- 唯一索引示例 (已考虑软删除)
-- CREATE INDEX idx_模块名_表名_created_at_status ON 模块名_表名(created_at, status) WHERE deleted_at IS NULL; -- 复合索引示例 (已考虑软删除)
-- -- 针对 JSONB 字段的 GIN 索引示例:
-- -- CREATE INDEX idx_模块名_表名_settings_gin ON 模块名_表名 USING gin (settings);
-- -- 针对全文搜索的 GIN 或 GiST 索引示例 (假设有 tsvector 类型的字段 fulltext_content):
-- -- CREATE INDEX idx_模块名_表名_fulltext_content_gin ON 模块名_表名 USING gin (fulltext_content);
-- -- 函数索引示例:
-- -- CREATE INDEX idx_模块名_表名_lower_email ON 模块名_表名 (LOWER(user_email)) WHERE deleted_at IS NULL;
```
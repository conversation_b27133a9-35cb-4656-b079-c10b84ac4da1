# 教师端作业报告功能需求文档

## 1. 背景和目标

### 业务背景
老师在教学过程中需要关注学生的学习情况，包括学生的完成情况、正确率、错题情况和用时，以及答题和提问等多个维度。老师更需要作为"鼓励者"，对学生的进步进行表扬，对不当行为及时沟通。本项目旨在构建各类任务、素材和学生之间的底层数据关系，完善教师端核心功能。

### 项目目标
- 完成教师端P0核心功能，确保6月上线版本能跑通业务MVP流程
- 9月可完成全部核心功能
- 覆盖用户：合作校的教师

## 2. 产品范围与功能边界

### 核心功能
- 作业列表：展示当前老师布置的或有权限查看的全部作业
- 作业报告：按学生报告、答题结果、学生提问三个维度展示作业报告
- 学生作业报告：按答题结果、学生提问展示某个学生的作业报告
- 策略说明：包括鼓励、关注、共性错题的策略

### 辅助功能
- 提醒设置：支持用户自定义提醒策略

## 3. 用户场景与故事化解

### 场景一：查看班级作业概览
张老师想查看高一3班学生的"函数的最值"课程作业完成情况。他打开教师端后，在作业列表中找到这个任务卡片，点击进入作业报告页面。在页面中，他能看到课程基本信息、班级完成进度、正确率，以及系统推荐的需要表扬和关注的学生列表。张老师一目了然地了解到班级整体学习状况，并可以进入任意学生的详细报告页面查看个人表现。

### 场景二：针对共性错题进行教学调整
李老师浏览高二5班数学作业的答题结果，发现有3道题被系统标记为"共性错题"。他点击查看题目详情，看到正确率只有50%，有15名学生答错。李老师决定在下次课堂上针对这些错题进行重点讲解，并为学生布置针对性练习，以帮助学生克服这些知识点的困难。

### 场景三：鼓励表现优秀的学生
王老师在查看班级报告时，注意到系统推荐了几名"值得表扬"的学生。其中赵思露同学在本次作业中较上次有明显进步，学习分提升了20%。王老师点击"点赞鼓励"按钮，系统自动生成鼓励消息，他稍作修改后发送。这种及时的鼓励不仅肯定了学生的进步，也能激发学生继续保持良好的学习状态。

### 场景四：关注学习有困难的学生
刘老师发现李子涵同学的作业完成情况不太理想，正确率只有50%，远低于班级平均的78.5%；学习进度也只有60%，落后于班级平均的95%。系统将其标记为"需关注"学生。刘老师点击查看详情后，通过系统发送了关怀提醒，并记录了需要线下沟通的事项，计划在下次课后与该生进行一对一辅导。

### 场景五：回应学生课堂提问
陈老师在查看"复数的概念"课程报告时，切换到"学生提问"标签页，发现万洁同学对复数集组合方式有疑问。虽然已经有同学和AI助手回答了问题，但陈老师想进一步确认学生的理解程度。他在提问区域回复了更详细的解释，并在下次课堂上针对这个知识点进行了额外的讲解。

## 4. 功能详细说明

### 4.1 作业列表

#### 数据范围与权限
| 职务 | 可查看的数据 | 操作权限 |
|------|------------|---------|
| 学科老师 | 本班本学科任务数据 | 修改、删除、复制权限：仅本人布置的任务 |
| 班主任 | 本班全部学科任务数据 | 修改、删除、复制权限：仅本人布置的任务 |
| 学科主任 | 本年级本学科任务数据 | 无布置权限 |
| 年级主任 | 本年级全部班级全部学科任务数据 | 无布置权限 |
| 校长 | 本校全部班级全部学科任务数据 | 无布置权限 |

#### 筛选与搜索
- **筛选**：支持按照班级、学科、布置日期进行筛选
- **搜索**：支持根据关键词搜索任务名称，支持实时搜索和清空搜索

#### 卡片展示
- **卡片指标**：课程任务/作业任务/测验任务/资源任务的核心指标
- **卡片操作**：对于本人布置的任务，可以进行编辑、删除、复制操作

### 4.2 作业报告

#### 学生报告Tab
- **基本信息**：展示任务名称、发布时间、截止时间、班级、素材范围
- **建议鼓励学生**：展示作答表现突出的学生，教师可查看详情并进行鼓励
- **建议关注学生**：展示作答表现有波动的学生，教师可查看详情并进行关注
- **班级进度与正确率**：展示班级平均完成率和正确率
- **任务学生列表**：按学生ID顺序展示，需关注/建议鼓励的学生置顶，支持异常数据飘色展示

#### 答题结果Tab
- **题目列表**：展示任务中所有题目，包括共性错题标记
- **题目信息**：展示每道题的正确率、错题人数、作答人数
- **搜索功能**：支持按题目关键词搜索
- **题目面板**：侧边展示题目号和正确率颜色区分

#### 学生提问Tab
- **提问概览**：按课程幻灯片顺序展示课程内容和本班评论数统计
- **查看详情**：支持展开查看具体讨论内容

### 4.3 学生作业报告

- **基本信息**：展示学生名称、任务名称、开始时间、完成时间/进度、素材范围
- **答题结果Tab**：展示该学生的题目作答情况，包括正确/错误/部分正确/未作答状态
- **学生提问Tab**：展示该学生在任务中的提问内容

### 4.4 题目详情页

- **题目基本信息**：展示题干、选项、标签、答案解析
- **作答统计**：展示选择题/判断题/非选择题的统计数据
- **学生个人数据**：在学生题目详情页中仅展示该生数据

### 4.5 策略说明

#### 鼓励策略
系统根据以下指标识别值得鼓励的学生：
- 连对王者：连对题数创班级记录
- 自我突破：学习分增幅明显
- 连对突破：连对题数创个人记录
- 本层领跑：学习分高于同层平均
- 超前学霸：提前完成任务
- 探索达人：提问次数多

#### 关注策略
系统根据以下指标识别需要关注的学生：
- 进度未开始：班级平均进度达50%时仍未开始
- 逾期未完成：任务已逾期未完成
- 逾期完成：任务逾期后完成
- 偏离历史：学习分较上次同类任务下降20%以上
- 偏离同层：学习分低于同层平均20%以上
- 内容偏离：某课程学习分显著低于其他内容20%以上

#### 共性错题策略
- 答题人数>10人，任务中正确率<60%的题目标记为共性错题

## 5. 数据处理规则

### 鼓励与关注选择规则
- 鼓励学生：系统选择学习分提升、连对题数增加等表现优秀的学生
- 关注学生：系统选择进度落后、学习分下降、内容表现不均衡的学生

### 正确率计算规则
对作答了的同学，答对小空数量/题目中的总小空之和*100%

### 作答状态判定规则
1. 若这道题学生在当前任务素材中被推送了多次，只看学生首次作答的数据
2. 已经提交的：课堂、测试、作业任务下，学生看到了题、选"暂不未答"的记作已作答、答错
3. 未提交的作业可以先跳过作答别的题目，此时不算作答

## 6. 用户界面设计说明

### 作业列表页
- 顶部筛选与搜索区域
- 任务卡片区域，分为课程、作业、资源三类
- 每张卡片包含任务名称、发布时间、截止时间、适用班级、完成率/正确率等关键指标

### 作业报告页
- 页面顶部显示任务名称和返回按钮
- 三个主要Tab：学生报告、答题结果、学生提问
- 学生报告Tab:
  - 建议鼓励/关注学生区域
  - 班级进度和正确率数据
  - 学生列表表格
- 答题结果Tab:
  - 题目统计信息
  - 题目列表，包含正确率等指标
  - 侧边题目面板
- 学生提问Tab:
  - 按课程结构展示提问分布
  - 可展开查看具体讨论内容

### 学生作业报告页
- 页面顶部显示学生姓名+任务名称和返回按钮
- 两个主要Tab：答题结果、学生提问
- 答题结果Tab显示该学生的题目作答情况
- 学生提问Tab显示该学生的提问内容

### 题目详情页
- 题目内容区域，包含题干、选项、标签
- 答案解析区域
- 作答统计区域

### 鼓励/关注操作页
- 学生基本信息
- 学习情况数据与对比
- 建议干预措施文本
- 操作按钮：点赞鼓励/关注、线下沟通、Push提醒等
- 预设消息文本编辑区域

## 7. 流程图

### 7.1 作业报告整体流程

```mermaid
graph TD
    A[教师登录] --> B[作业列表页]
    B --> C[筛选/搜索作业]
    B --> D[点击作业卡片]
    D --> E[作业报告页]
    E --> F1[学生报告Tab]
    E --> F2[答题结果Tab]
    E --> F3[学生提问Tab]
    F1 --> G1[查看推荐鼓励学生]
    F1 --> G2[查看推荐关注学生]
    F1 --> G3[查看学生列表]
    G1 --> H1[发送鼓励消息]
    G2 --> H2[发送关注提醒]
    G3 --> I[查看学生详情]
    F2 --> J1[查看共性错题]
    F2 --> J2[搜索题目]
    J1 --> K[查看题目详情]
    F3 --> L[查看学生提问]
    L --> M[回复学生提问]
```

### 7.2 学生作业报告流程

```mermaid
graph TD
    A[作业报告页] --> B[点击学生'查看详情']
    B --> C[学生作业报告页]
    C --> D1[答题结果Tab]
    C --> D2[学生提问Tab]
    D1 --> E[查看学生答题情况]
    E --> F[查看题目详情]
    D2 --> G[查看学生提问内容]
    G --> H[回复学生提问]
```

### 7.3 鼓励和关注学生流程

```mermaid
graph TD
    A[学生报告Tab] --> B1[点击推荐鼓励学生]
    A --> B2[点击推荐关注学生]
    B1 --> C1[鼓励学生抽屉页]
    B2 --> C2[关注学生抽屉页]
    C1 --> D1[查看学生数据]
    C1 --> D2[点赞鼓励]
    C1 --> D3[线下沟通]
    C1 --> D4[Push提醒]
    C2 --> E1[查看学生数据]
    C2 --> E2[发布关注]
    C2 --> E3[线下沟通]
    C2 --> E4[Push提醒]
    D2 --> F1[编辑鼓励消息]
    D4 --> F2[编辑Push消息]
    E2 --> G1[编辑关注消息]
    E4 --> G2[编辑Push消息]
    F1 --> H1[发送消息]
    F2 --> H2[发送Push]
    G1 --> I1[发送消息]
    G2 --> I2[发送Push]
```

### 7.4 共性错题处理流程

```mermaid
graph TD
    A[答题结果Tab] --> B[筛选'只看共性错题']
    B --> C[查看共性错题列表]
    C --> D[点击'查看'按钮]
    D --> E[题目详情页]
    E --> F[查看作答统计]
    E --> G[查看学生答题分布]
    F --> H[制定教学调整计划]
    G --> H
```

## 8. 非功能需求

### 性能需求
- 作业列表页面加载时间应在3秒内
- 作业报告页面数据渲染应在5秒内完成
- 学生数据筛选和搜索响应时间应在2秒内

### 安全需求
- 教师只能查看自己有权限的班级和学科数据
- 系统需记录教师对学生数据的访问日志

### 兼容性需求
- 支持主流浏览器访问
- 支持教师端PC和移动设备访问

## 9. 需求检查清单

| 需求类别 | 原始需求 | 是否实现 | 实现说明 |
|---------|---------|---------|---------|
| 核心功能 | 作业列表：展示当前老师布置的或有权限查看的全部作业 | ✅ | 已完整设计作业列表功能，包含数据范围、筛选、搜索和卡片展示 |
| 核心功能 | 作业报告：按学生报告、答题结果、学生提问tab展示作业报告 | ✅ | 已设计三个维度的详细展示内容，包括建议鼓励学生、建议关注学生、班级进度、正确率、题目列表和提问概览 |
| 核心功能 | 学生作业报告：按答题结果、学生提问展示某个学生的作业报告 | ✅ | 已设计学生个人报告的两个Tab，包括学生题目作答情况和提问内容 |
| 核心功能 | 策略说明：鼓励、关注、共性错题 | ✅ | 已详细说明各类策略的判断标准和规则 |
| 辅助功能 | 提醒设置：支持用户自定义提醒策略 | ✅ | 已在鼓励和关注策略中说明用户可自定义阈值等设置 |
| 数据规则 | 正确率计算规则 | ✅ | 已明确定义：答对小空数量/题目中的总小空之和*100% |
| 数据规则 | 作答状态判定规则 | ✅ | 已明确定义三条规则，包括首次作答、暂不作答计为答错、跳过不算作答等 |
| 用户界面 | 作业列表页设计 | ✅ | 已设计筛选搜索区域和任务卡片区域 |
| 用户界面 | 作业报告页设计 | ✅ | 已设计三个Tab的页面结构和内容布局 |
| 用户界面 | 学生作业报告页设计 | ✅ | 已设计学生个人报告的页面结构和内容布局 |
| 用户界面 | 题目详情页设计 | ✅ | 已设计题目内容、答案解析和作答统计区域 |
| 用户界面 | 鼓励/关注操作页设计 | ✅ | 已设计学生信息、数据对比、建议措施和操作按钮区域 |
| 非功能需求 | 性能需求 | ✅ | 已明确页面加载时间、数据渲染和响应时间要求 |
| 非功能需求 | 安全需求 | ✅ | 已明确权限控制和访问日志要求 |
| 非功能需求 | 兼容性需求 | ✅ | 已明确支持主流浏览器和多设备访问要求 |
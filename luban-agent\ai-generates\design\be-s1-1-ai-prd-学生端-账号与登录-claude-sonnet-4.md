# 产品需求文档 - 学生端账号与登录 v1.0

## 1. 需求背景与目标

### 1.1 需求目标
- 通过该需求的实现，可以确保业务第一阶段能够比较好的兼顾管控及非管控场景的账号登录和使用
- 该需求上线后，学生用户登录成功率预计可以提升至95%以上，支持全量学生角色用户的登录需求

## 2. 功能范围

### 2.1 核心功能
- 学生端通用登录页: 提供账号密码登录功能，支持完整的UI交互流程，满足学生日常登录场景，解决学生无法进入系统的痛点
  - 优先级：高
  - 依赖关系：无
- 登录判定逻辑: 实现账号角色校验和订单状态验证，满足业务规则校验场景，解决无效账号登录的痛点
  - 优先级：高
  - 依赖关系：依赖于通用登录页
- MDM场景网络设置: 在管控环境下提供网络配置入口，满足受管控设备联网场景，解决无法连接网络的痛点
  - 优先级：中
  - 依赖关系：依赖于通用登录页

### 2.2 辅助功能
- 服务协议展示: 提供用户协议和隐私协议查看功能，为合规登录提供支持
- 登录状态记忆: 记住上次登录账号功能，为快速登录提供支持
- 数据埋点统计: 登录行为数据收集功能，为产品优化提供支持

### 2.3 非本期功能
- 手机号登录方式: 支持手机号码登录，建议迭代2再考虑
- 家长角色登录支持: 支持家长角色登录和角色选择，建议迭代2再考虑
- 多角色多组织选择: 支持账号下多角色或多组织选择，建议迭代3再考虑

## 3. 计算规则与公式

- 登录判定规则
  - 账号格式校验: 10位字符格式验证
    - 参数说明：
      - 前4位: 小写字母，排除i、l、o字符
      - 后6位: 数字0-9
    - 规则
      - 取值范围：总长度固定10位
  - 登录权限校验: 角色和订单状态验证
    - 参数说明：
      - 角色类型: 学生角色存在性
      - 订单状态: 试用中或付费中状态
    - 规则
      - 取值范围：仅学生角色且订单有效时允许登录

## 4. 用户场景与故事

### 首次登录场景
作为一个新学生用户，我希望能够使用老师提供的账号密码登录系统，这样我就可以开始使用学习产品进行学习。目前的痛点是首次使用不知道如何操作，如果能够提供清晰的登录界面和操作引导，对我的学习会有很大帮助。

**关键步骤：**
1. 打开应用进入登录页面
2. 输入老师提供的账号和密码
3. 勾选同意服务协议
4. 点击登录按钮进入系统

- 优先级：高
- 依赖关系：无

### 日常登录场景
作为一个已注册的学生用户，我希望能够快速登录系统继续我的学习，这样我就可以节省时间专注于学习内容。目前的痛点是每次都要重新输入完整账号，如果能够记住我的账号信息，对我的使用体验会有很大帮助。

**关键步骤：**
1. 打开应用看到预填的账号信息
2. 输入密码
3. 点击登录进入系统

- 优先级：中
- 依赖关系：依赖于首次登录场景

### MDM管控设备登录场景
作为一个使用学校配发设备的学生，我希望能够在受管控的设备上正常登录和使用，这样我就可以在学校环境中进行学习。目前的痛点是设备可能没有网络连接，如果能够提供网络设置入口，对我的使用会有很大帮助。

**关键步骤：**
1. 检查网络连接状态
2. 如需要则通过网络设置配置网络
3. 输入账号密码登录
4. 系统自动完成设备绑定

- 优先级：中
- 依赖关系：依赖于通用登录功能

**复杂场景处理说明**：
- 多角色交互场景：当前版本仅支持学生角色，未来版本需要明确家长角色的操作步骤
- 异常情况处理：需要描述网络异常、登录失败、服务异常的错误场景和恢复机制
- 数据依赖场景：需要说明账号数据、订单数据的前置条件和获取方式

## 5. 业务流程图

```mermaid
graph TD
    A[打开应用] --> B{网络连接检查}
    B -->|无网络| C[显示网络设置入口]
    C --> D[用户配置网络]
    D --> E[进入登录页面]
    B -->|有网络| E[进入登录页面]
    E --> F{是否首次登录}
    F -->|是| G[显示空白账号输入框]
    F -->|否| H[显示预填账号信息]
    G --> I[用户输入账号密码]
    H --> J[用户输入密码]
    I --> K{协议勾选检查}
    J --> K
    K -->|未勾选| L[提示勾选协议]
    L --> K
    K -->|已勾选| M[发起登录请求]
    M --> N{登录校验}
    N -->|账号格式错误| O[提示账号格式错误]
    N -->|密码错误| P[提示密码错误]
    N -->|角色权限不符| Q[提示服务异常]
    N -->|校验通过| R{是否MDM场景}
    R -->|是| S[自动设备绑定]
    R -->|否| T[进入应用首页]
    S --> T
    O --> I
    P --> J
    Q --> I
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：登录验证、页面加载的P95响应时间应不超过3秒
- 并发用户数：系统需能支持至少1000用户同时登录的场景，且性能无明显下降
- 数据处理能力/吞吐量：系统需能处理每秒100次登录请求
- 数据量：系统需支持十万级学生账号数据的存储和高效查询，登录验证响应时间应在2秒内

### 6.2 安全需求
- 用户认证与授权：所有登录接口必须经过严格的账号密码校验和角色权限验证，支持基于角色的访问控制
- 数据保护：用户密码在存储和传输过程中必须加密，符合教育行业数据安全标准要求
- 操作审计：对所有登录操作（成功/失败）必须记录详细的操作日志，包含用户账号、登录时间、IP地址、设备信息等关键信息
- 防护要求：系统应具备对常见Web攻击（如SQL注入、暴力破解）的基本防护能力

## 7. 验收标准

### 7.1 功能验收
- 学生端通用登录页：
  - 使用场景：当学生用户打开应用时
  - 期望结果：系统应展现完整的登录界面，包含账号输入框、密码输入框、协议勾选、登录按钮，满足基本登录交互需求
- 登录判定逻辑：
  - 使用场景：当用户输入账号密码点击登录时
  - 期望结果：系统应正确校验账号格式、角色权限、订单状态，仅允许有效学生账号登录成功
- MDM场景网络设置：
  - 使用场景：当用户在管控设备上无网络连接时
  - 期望结果：系统应提供网络设置入口，用户可以配置网络连接

### 7.2 性能验收
- 响应时间：
  - 测试场景：模拟100个并发用户同时进行登录操作
  - 验收标准：P95响应时间不超过3秒
- 并发用户数：
  - 测试场景：在标准硬件环境下，逐步增加并发用户至1000个，持续执行登录操作5分钟
  - 验收标准：系统应保持稳定运行，无错误率飙升，登录操作的平均响应时间不超过2秒

### 7.3 安全验收
- 用户认证与授权：
  - 测试场景：使用无效账号或错误密码尝试登录
  - 验收标准：系统应拒绝登录，并返回明确的错误提示
  - 测试场景：使用有效学生账号和正确密码登录
  - 验收标准：用户可以正常登录并进入系统
- 数据保护：
  - 验证点：通过安全审计流程检查密码存储和传输加密
  - 验收标准：密码在存储和传输层面符合加密标准和保护要求
- 操作审计：
  - 测试场景：执行一次登录操作（成功或失败）
  - 验收标准：操作日志被准确、完整地记录，包含所有必要的审计字段

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：登录功能的操作界面应简洁直观，学生用户在无额外培训的情况下，3步内可完成登录操作
- 容错处理：当发生网络异常或登录失败时，系统应能给出清晰的错误提示和引导，允许用户重新尝试
- 兼容性：产品核心功能需在主流移动设备和操作系统得到良好支持：iOS最新版、Android主流版本，保证界面和功能正常

### 8.2 维护性需求
- 配置管理需求：登录相关的业务规则参数（如账号格式规则、密码复杂度要求）应支持运营人员通过管理后台进行调整
- 监控与告警需求：登录失败率突增、登录服务不可用等异常情况发生时，系统应有机制触发告警
- 日志需求：用户登录操作（成功/失败）及系统关键事件必须被记录，日志内容应包含用户账号、操作时间、IP地址、设备信息等

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 产品应提供应用内反馈入口，收集用户对登录体验的反馈
- 应有机制定期收集、整理和分析登录相关的用户反馈，识别登录流程的问题和改进机会

### 9.2 迭代计划（高阶产品方向）
- 产品计划以每月为周期进行迭代，持续优化登录体验
- 下一个迭代重点可能包括：手机号登录支持、家长角色登录、多角色选择功能

## 10. 需求检查清单

使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 备注 |
|---------|-----------|--------|--------|--------|------|
| 学生端通用登录页-登录页核心模块 | 2.1-学生端通用登录页 | ✅完整 | ✅准确 | ✅一致 | 包含标题logo、账号密码区、服务协议区、登录按钮 |
| 学生端通用登录页-账号密码区交互逻辑 | 4-首次登录场景、日常登录场景 | ✅完整 | ✅准确 | ✅一致 | 包含输入限制、显示隐藏密码、键盘交互 |
| 学生端通用登录页-服务协议区功能 | 2.2-服务协议展示 | ✅完整 | ✅准确 | ✅一致 | 包含协议勾选、协议页面跳转 |
| 学生端通用登录页-登录按钮交互逻辑 | 7.1-功能验收 | ✅完整 | ✅准确 | ✅一致 | 包含按钮状态、校验逻辑、异常处理 |
| MDM场景账号流程-网络设置按钮 | 2.1-MDM场景网络设置 | ✅完整 | ✅准确 | ✅一致 | 管控场景下的网络配置入口 |
| MDM场景账号流程-首次登录逻辑 | 4-MDM管控设备登录场景 | ✅完整 | ✅准确 | ✅一致 | 包含设备绑定、自动注册流程 |
| 登录判定逻辑-账号角色校验 | 3-登录判定规则 | ✅完整 | ✅准确 | ✅一致 | 学生角色存在性和订单状态验证 |
| 登录判定逻辑-订单状态验证 | 3-登录权限校验 | ✅完整 | ✅准确 | ✅一致 | 试用中或付费中状态判断 |
| 数据需求-效果追踪 | 8.2-监控与告警需求 | ✅完整 | ✅准确 | ✅一致 | 登录触发事件及失败事件记录 |
| 数据需求-数据埋点 | 2.2-数据埋点统计 | ✅完整 | ✅准确 | ✅一致 | 登录行为数据收集方案 |
| 账号密码生成规则 | 3-账号格式校验 | ✅完整 | ✅准确 | ✅一致 | 10位字符，前4位字母后6位数字 |
| 未来拓展方向-手机号登录 | 2.3-手机号登录方式 | ✅完整 | ✅准确 | ✅一致 | 标记为非本期功能 |
| 未来拓展方向-家长角色支持 | 2.3-家长角色登录支持 | ✅完整 | ✅准确 | ✅一致 | 标记为非本期功能 |

## 11. 附录

### 11.1 原型图
- 登录页面设计稿：https://www.figma.com/design/COdZQZZOFHZHsdI1zWX8Tm/%E5%AD%A6%E7%94%9F%E7%AB%AF-1.0?node-id=64-6450&p=f&t=bCxLcVocfLM35KWq-0

### 11.2 术语表
- MDM：移动设备管理（Mobile Device Management），企业或学校统一管理移动设备的系统
- Launcher：启动器，作为设备的主要应用入口
- 管控场景：通过MDM系统管理的设备使用场景
- 非管控场景：普通的个人设备使用场景

### 11.3 参考资料
- 原始PRD文档：Format-PRD-学生端-账号与登录.md
- 账号升级需求PRD：运营后台产品_2期_账号升级_需求PRD
- 设计稿文档：学生端-1.0 Figma设计稿
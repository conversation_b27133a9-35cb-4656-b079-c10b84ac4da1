openapi: 3.0.3
info:
  title: AI课中学习服务 API
  description: |
    提供AI课中学习系统的核心功能，包括课程内容管理、学习进度跟踪、文档组件交互播放、掌握度计算等功能。
    主要解决学生在AI课程学习过程中的内容获取、进度管理、交互控制和学习效果评估问题。
    本文档遵循 OpenAPI 3.0.3 规范。
  version: v0.9
  contact:
    name: AI课中开发团队
    email: <EMAIL>

servers:
  - url: http://localhost:8080/api/v1
    description: 本地开发服务器
  - url: https://api-study.example.com/api/v1
    description: 生产环境服务器

tags:
  - name: CourseContent
    description: 课程内容相关接口
  - name: StudySession
    description: 学习会话管理相关接口
  - name: StudyInteraction
    description: 学习交互操作相关接口
  - name: StudyReport
    description: 学习报告和数据相关接口
  - name: StudyFeedback
    description: 学习反馈相关接口

components:
  schemas:
    # 通用响应结构
    GenericError:
      type: object
      properties:
        code:
          type: integer
          description: 业务错误码
          example: 4000001
        message:
          type: string
          description: 用户可读的错误信息
          example: '无效的参数'
        detail:
          type: string
          description: 可选的详细错误信息或调试信息
          nullable: true
          example: '字段 [courseId] 不能为空'
        data:
          type: object
          description: 可选的附加数据
          nullable: true
          example: {'field': 'courseId'}

    GenericResponse:
      type: object
      properties:
        code:
          type: integer
          description: 业务状态码，0表示成功
          example: 0
        message:
          type: string
          description: 响应消息
          example: '操作成功'
        data:
          type: object
          description: 响应数据
          nullable: true
        pageInfo:
          type: object
          description: 分页信息（如果适用）
          nullable: true

    # 课程相关实体
    OpeningPageConfig:
      type: object
      description: 课程开场页配置信息，包含开场页展示所需的所有资源和配置
      properties:
        courseId:
          type: integer
          format: int64
          description: 课程唯一标识ID
          example: 1001
        courseName:
          type: string
          description: 课程名称，取对应业务树末级知识点名称
          maxLength: 255
          example: 'AI基础知识-第一章-人工智能概述'
        courseInfo:
          type: string
          description: 课程描述性文案，简要介绍课程内容
          maxLength: 500
          example: '本章将带你了解人工智能的基本概念、发展历程和应用领域，为后续深入学习奠定基础'
        chapterInfo:
          type: string
          description: 章节信息，如"第一章"
          example: '第一章'
        courseSequence:
          type: string
          description: 课程序号，如"2.1.1"
          example: '1.1.1'
        subjectId:
          type: integer
          format: int64
          description: 学科ID，用于确定开场页样式
          example: 101
        openingConfig:
          type: object
          description: 开场页视觉配置，根据学科不同而变化
          properties:
            backgroundImage:
              type: string
              format: uri
              description: 开场页背景图片URL，不同学科可配置不同背景
              example: 'https://cdn.example.com/bg/ai-subject-bg.jpg'
            colorScheme:
              type: string
              description: 开场页主色调，十六进制颜色值
              pattern: '^#[0-9A-Fa-f]{6}$'
              example: '#007bff'
            logoUrl:
              type: string
              format: uri
              description: 学科或课程Logo URL
              example: 'https://cdn.example.com/logo/ai-logo.png'
        ipAnimationConfig:
          type: object
          description: IP角色动效配置，用于开场页自动播放
          properties:
            animationUrl:
              type: string
              format: uri
              description: IP动效资源URL，支持MP4格式
              example: 'https://cdn.example.com/animation/ai-teacher-intro.mp4'
            duration:
              type: integer
              description: IP动效播放时长（秒）
              minimum: 1
              maximum: 60
              example: 15
            autoPlay:
              type: boolean
              description: 是否自动播放动效
              default: true
              example: true
            loopPlay:
              type: boolean
              description: 是否循环播放
              default: false
              example: false
        audioConfig:
          type: object
          description: 开场页音频配置，与IP动效同步播放
          properties:
            audioUrl:
              type: string
              format: uri
              description: 开场音频资源URL，支持MP3格式
              example: 'https://cdn.example.com/audio/ai-course-intro.mp3'
            duration:
              type: integer
              description: 音频播放时长（秒）
              minimum: 1
              maximum: 120
              example: 30
            volume:
              type: number
              description: 音频音量（0.0-1.0）
              minimum: 0.0
              maximum: 1.0
              default: 0.8
              example: 0.8
            autoPlay:
              type: boolean
              description: 是否自动播放音频
              default: true
              example: true
        componentSequence:
          type: array
          description: 课程组件序列，定义组件播放顺序
          items:
            type: object
            properties:
              componentId:
                type: integer
                format: int64
                description: 组件ID
              componentOrder:
                type: integer
                description: 组件顺序
              componentType:
                type: string
                enum: [document, exercise]
                description: 组件类型
          example:
            - componentId: 2001
              componentOrder: 1
              componentType: 'document'
            - componentId: 2002
              componentOrder: 2
              componentType: 'exercise'
      required:
        - courseId
        - courseName
        - subjectId
        - ipAnimationConfig
        - audioConfig

    ComponentContent:
      type: object
      description: 文档组件详细内容，包含文档数据、勾画轨迹、字幕等学习资源
      properties:
        componentId:
          type: integer
          format: int64
          description: 组件唯一标识ID
          example: 2001
        courseId:
          type: integer
          format: int64
          description: 所属课程ID
          example: 1001
        componentName:
          type: string
          description: 组件名称，如"课程引入"、"知识点1"
          maxLength: 255
          example: '知识点1-人工智能定义与发展'
        componentType:
          type: string
          description: 组件类型，当前版本仅支持文档组件
          enum: [document, exercise]
          example: 'document'
        componentOrder:
          type: integer
          description: 组件在课程中的顺序位置
          minimum: 1
          example: 1
        documentContent:
          type: object
          description: 文档具体内容数据，包含交互式文档的所有元素
          properties:
            contentUrl:
              type: string
              format: uri
              description: 文档内容资源URL，JSON格式的交互式文档数据
              example: 'https://cdn.example.com/content/doc-2001.json'
            contentType:
              type: string
              description: 内容类型标识
              enum: [interactive-document, static-document, multimedia-document]
              example: 'interactive-document'
            contentVersion:
              type: string
              description: 内容版本号，用于缓存控制
              example: 'v1.2.0'
            previewImage:
              type: string
              format: uri
              description: 文档预览图URL
              example: 'https://cdn.example.com/preview/doc-2001-preview.jpg'
        drawingTrajectoryData:
          type: object
          description: 与音频同步的勾画轨迹数据，用于跟随模式播放
          properties:
            trajectoryUrl:
              type: string
              format: uri
              description: 轨迹数据文件URL，包含完整的绘制路径和时间信息
              example: 'https://cdn.example.com/trajectory/traj-2001.json'
            trajectoryVersion:
              type: string
              description: 轨迹数据版本号
              example: 'v1.0.0'
            syncPoints:
              type: array
              description: 关键同步时间点，用于精确控制轨迹播放
              items:
                type: object
                properties:
                  time:
                    type: number
                    description: 时间点（秒）
                    minimum: 0
                    example: 15.5
                  action:
                    type: string
                    description: 动作类型
                    enum: [start_drawing, pause_drawing, highlight, erase, complete]
                    example: 'start_drawing'
                  elementId:
                    type: string
                    description: 关联的文档元素ID
                    example: 'element_001'
              example:
                - time: 0
                  action: 'start_drawing'
                  elementId: 'title_001'
                - time: 15.5
                  action: 'highlight'
                  elementId: 'concept_001'
        totalDuration:
          type: integer
          description: 组件标准播放时长（秒），用于进度计算
          minimum: 1
          maximum: 3600
          example: 180
        subtitleData:
          type: object
          description: 字幕数据配置，默认开启字幕显示
          nullable: true
          properties:
            subtitleUrl:
              type: string
              format: uri
              description: 字幕文件URL，支持VTT格式
              example: 'https://cdn.example.com/subtitle/sub-2001.vtt'
            subtitleVersion:
              type: string
              description: 字幕版本号
              example: 'v1.0.0'
            defaultEnabled:
              type: boolean
              description: 是否默认开启字幕
              default: true
              example: true
            language:
              type: string
              description: 字幕语言
              default: 'zh-CN'
              example: 'zh-CN'
        playbackConfig:
          type: object
          description: 播放配置信息，定义播放行为和交互规则
          properties:
            defaultMode:
              type: string
              description: 默认播放模式
              enum: [follow, free]
              default: 'follow'
              example: 'follow'
            supportedSpeeds:
              type: array
              description: 支持的播放倍速选项
              items:
                type: number
                enum: [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 3.0]
              default: [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 3.0]
              example: [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 3.0]
            enableDoubleClick:
              type: boolean
              description: 是否启用双击播放/暂停功能
              default: true
              example: true
            enableLongPress:
              type: boolean
              description: 是否启用长按3倍速功能
              default: true
              example: true
            seekStepSeconds:
              type: integer
              description: 快进/快退步长（秒）
              default: 10
              example: 10
        status:
          type: string
          description: 组件状态
          enum: [active, inactive, maintenance]
          example: 'active'
      required:
        - componentId
        - courseId
        - componentName
        - componentType
        - documentContent
        - drawingTrajectoryData
        - totalDuration

    # 学习会话相关实体
    StudySession:
      type: object
      description: 学习会话信息
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: 'session_1001_20250526_001'
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 10001
        courseId:
          type: integer
          format: int64
          description: 课程ID
          example: 1001
        sessionStatus:
          type: string
          description: 会话状态
          enum: [in_progress, completed, exited]
          example: 'in_progress'
        startTime:
          type: string
          format: date-time
          description: 开始时间
          example: '2025-05-26T10:30:00Z'
        endTime:
          type: string
          format: date-time
          description: 结束时间
          nullable: true
          example: '2025-05-26T11:00:00Z'
        learningMode:
          type: string
          description: 学习模式
          enum: [follow, free]
          example: 'follow'

    StartSessionRequest:
      type: object
      description: 开始学习会话请求
      properties:
        userId:
          type: integer
          format: int64
          description: 用户ID
          example: 10001
        courseId:
          type: integer
          format: int64
          description: 课程ID
          example: 1001
      required:
        - userId
        - courseId

    EndSessionRequest:
      type: object
      description: 结束学习会话请求
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: 'session_1001_20250526_001'
      required:
        - sessionId

    # 学习交互相关实体
    InteractionRequest:
      type: object
      description: 学习交互操作请求
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: 'session_1001_20250526_001'
        componentId:
          type: integer
          format: int64
          description: 组件ID
          example: 2001
        operation:
          type: string
          description: 操作类型
          enum: [mode_switch, playback_control, progress_update]
          example: 'mode_switch'
        operationData:
          type: object
          description: 操作数据
          properties:
            mode:
              type: string
              description: 学习模式（用于模式切换）
              enum: [follow, free]
              example: 'free'
            playbackAction:
              type: string
              description: 播放操作（用于播放控制）
              enum: [play, pause, double_click, long_press]
              example: 'double_click'
            speed:
              type: number
              description: 播放倍速
              enum: [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 3.0]
              example: 1.5
            position:
              type: integer
              description: 播放位置（秒）
              example: 120
            interactionData:
              type: object
              description: 交互数据
              nullable: true
      required:
        - sessionId
        - componentId
        - operation

    InteractionResult:
      type: object
      description: 学习交互操作结果
      properties:
        success:
          type: boolean
          description: 操作是否成功
          example: true
        currentState:
          type: object
          description: 当前状态
          properties:
            mode:
              type: string
              description: 当前学习模式
              example: 'free'
            isPlaying:
              type: boolean
              description: 是否正在播放
              example: false
            speed:
              type: number
              description: 当前播放倍速
              example: 1.0
            position:
              type: integer
              description: 当前播放位置（秒）
              example: 120

    # 学习报告相关实体
    StudyReport:
      type: object
      description: 学习报告数据
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: 'session_1001_20250526_001'
        performance:
          type: object
          description: 学习表现数据
          properties:
            studyDuration:
              type: integer
              description: 学习用时（秒）
              example: 1800
            totalQuestions:
              type: integer
              description: 总答题数
              example: 10
            correctAnswers:
              type: integer
              description: 正确答题数
              example: 8
            accuracyRate:
              type: number
              description: 正确率（百分比）
              example: 80.0
            completionRate:
              type: number
              description: 完成率（百分比）
              example: 100.0
        mastery:
          type: object
          description: 掌握度数据
          properties:
            knowledgePointId:
              type: integer
              format: int64
              description: 知识点ID
              example: 3001
            currentMastery:
              type: number
              description: 当前掌握度值
              example: 0.75
            targetMastery:
              type: number
              description: 目标掌握度值
              example: 0.80
            masteryProgress:
              type: number
              description: 掌握度完成进度
              example: 0.94
            masteryChange:
              type: number
              description: 掌握度变化
              example: 0.15
            energyGained:
              type: integer
              description: 获得的能量值
              example: 50
        answerDetails:
          type: array
          description: 答题详情
          items:
            type: object
            properties:
              questionId:
                type: integer
                format: int64
                description: 题目ID
              isCorrect:
                type: boolean
                description: 是否正确
              userAnswer:
                type: string
                description: 用户答案
              correctAnswer:
                type: string
                description: 正确答案

    # 反馈相关实体
    FeedbackRequest:
      type: object
      description: 课程反馈请求
      properties:
        sessionId:
          type: string
          description: 会话ID
          example: 'session_1001_20250526_001'
        ratingLevel:
          type: integer
          description: 评价等级（1-5级）
          minimum: 1
          maximum: 5
          example: 4
        feedbackOptions:
          type: array
          description: 反馈选项
          items:
            type: string
          example: ['内容清晰', '节奏合适']
        textFeedback:
          type: string
          description: 文字反馈
          nullable: true
          example: '课程内容很好，希望增加更多练习题'
      required:
        - sessionId
        - ratingLevel

    FeedbackResult:
      type: object
      description: 反馈提交结果
      properties:
        submitted:
          type: boolean
          description: 是否提交成功
          example: true
        feedbackId:
          type: string
          description: 反馈记录ID
          example: 'feedback_20250526_001'

  parameters:
    courseIdQueryParameter:
      name: courseId
      in: query
      required: true
      description: 课程ID
      schema:
        type: integer
        format: int64
        example: 1001

    componentIdQueryParameter:
      name: componentId
      in: query
      required: true
      description: 组件ID
      schema:
        type: integer
        format: int64
        example: 2001

    sessionIdQueryParameter:
      name: sessionId
      in: query
      required: true
      description: 会话ID
      schema:
        type: string
        example: 'session_1001_20250526_001'

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "请输入JWT Token, 格式为: Bearer {token}"

  responses:
    UnauthorizedError:
      description: 未认证或认证令牌无效
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4010001
                message: "需要认证或认证失败"

    NotFoundError:
      description: 请求的资源未找到
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4040001
                message: "请求的资源不存在"

    ForbiddenError:
      description: 无权限访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4030001
                message: "无权限执行此操作"

    BadRequestError:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/GenericError'
          examples:
            default:
              value:
                code: 4000001
                message: "请求参数错误"

paths:
  /course/opening:
    get:
      tags:
        - CourseContent
      summary: 获取课程开场页配置
      description: |
        获取指定课程的开场页完整配置信息，用于课程学习开始前的展示。
        
        **功能说明：**
        - 返回课程基本信息（名称、描述、章节等）
        - 提供IP动效资源配置，支持自动播放
        - 提供开场音频配置，与动效同步播放
        - 返回课程组件序列，用于后续学习流程
        - 支持不同学科的个性化开场页样式
        
        **使用场景：**
        - 学生首次进入课程时调用
        - 系统根据返回配置自动播放开场页内容
        - 播放完成后根据组件序列进入第一个学习组件
        
        **性能要求：**
        - 响应时间不超过2秒
        - 支持CDN加速资源加载
        - 开场页资源支持预加载优化
      operationId: getCourseOpening
      parameters:
        - name: courseId
          in: query
          required: true
          description: |
            课程唯一标识ID，用于获取指定课程的开场页配置。
            
            **参数说明：**
            - 必须是有效的课程ID
            - 课程必须处于启用状态
            - 用户必须有该课程的学习权限
          schema:
            type: integer
            format: int64
            minimum: 1
            example: 1001
          examples:
            valid_course:
              summary: 有效课程ID示例
              value: 1001
            ai_course:
              summary: AI基础课程示例
              value: 2001
      responses:
        '200':
          description: 成功获取课程开场页配置信息
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/OpeningPageConfig'
              examples:
                ai_course_opening:
                  summary: AI课程开场页配置示例
                  value:
                    code: 0
                    message: "获取开场页配置成功"
                    data:
                      courseId: 1001
                      courseName: "AI基础知识-第一章-人工智能概述"
                      courseInfo: "本章将带你了解人工智能的基本概念、发展历程和应用领域"
                      chapterInfo: "第一章"
                      courseSequence: "1.1.1"
                      subjectId: 101
                      openingConfig:
                        backgroundImage: "https://cdn.example.com/bg/ai-subject-bg.jpg"
                        colorScheme: "#007bff"
                        logoUrl: "https://cdn.example.com/logo/ai-logo.png"
                      ipAnimationConfig:
                        animationUrl: "https://cdn.example.com/animation/ai-teacher-intro.mp4"
                        duration: 15
                        autoPlay: true
                        loopPlay: false
                      audioConfig:
                        audioUrl: "https://cdn.example.com/audio/ai-course-intro.mp3"
                        duration: 30
                        volume: 0.8
                        autoPlay: true
                      componentSequence:
                        - componentId: 2001
                          componentOrder: 1
                          componentType: "document"
                        - componentId: 2002
                          componentOrder: 2
                          componentType: "exercise"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                missing_course_id:
                  summary: 缺少课程ID参数
                  value:
                    code: 4000001
                    message: "请求参数错误"
                    detail: "courseId参数不能为空"
                    data:
                      field: "courseId"
                invalid_course_id:
                  summary: 无效的课程ID
                  value:
                    code: 4000002
                    message: "请求参数错误"
                    detail: "courseId必须是正整数"
                    data:
                      field: "courseId"
                      value: "invalid_id"
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          description: 无权限访问该课程
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                no_course_permission:
                  summary: 无课程访问权限
                  value:
                    code: 4030001
                    message: "无权限访问该课程"
                    detail: "用户未获得该课程的学习权限"
                    data:
                      courseId: 1001
                      userId: 10001
        '404':
          description: 课程不存在或已下线
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                course_not_found:
                  summary: 课程不存在
                  value:
                    code: 4040001
                    message: "课程不存在"
                    detail: "指定的课程ID不存在或课程已下线"
                    data:
                      courseId: 1001
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                server_error:
                  summary: 服务器错误示例
                  value:
                    code: 5000001
                    message: "服务器内部错误"
                    detail: "获取课程配置时发生异常，请稍后重试"
      security:
        - BearerAuth: []

  /course/component:
    get:
      tags:
        - CourseContent
      summary: 获取文档组件内容
      description: |
        获取指定文档组件的完整内容和配置信息，用于文档组件的学习播放。
        
        **功能说明：**
        - 返回文档组件的详细内容数据
        - 提供与音频同步的勾画轨迹数据
        - 包含字幕数据配置，默认开启字幕显示
        - 返回播放配置信息，支持跟随模式和自由模式
        - 提供播放控制相关的交互配置
        
        **使用场景：**
        - 学生进入文档组件学习时调用
        - 获取文档内容用于渲染学习界面
        - 获取勾画轨迹用于跟随模式播放
        - 获取播放配置用于交互控制
        
        **性能要求：**
        - 响应时间不超过2秒
        - 支持大文件内容的CDN加速
        - 勾画轨迹数据支持流式加载
        - 文档内容支持版本缓存控制
      operationId: getCourseComponent
      parameters:
        - name: componentId
          in: query
          required: true
          description: |
            文档组件唯一标识ID，用于获取指定组件的详细内容。
            
            **参数说明：**
            - 必须是有效的组件ID
            - 组件必须属于用户有权限访问的课程
            - 组件必须处于可用状态
            - 当前版本仅支持文档组件类型
          schema:
            type: integer
            format: int64
            minimum: 1
            example: 2001
          examples:
            document_component:
              summary: 文档组件ID示例
              value: 2001
            ai_concept_component:
              summary: AI概念文档组件
              value: 2101
      responses:
        '200':
          description: 成功获取文档组件内容
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/ComponentContent'
              examples:
                ai_concept_component:
                  summary: AI概念文档组件内容示例
                  value:
                    code: 0
                    message: "获取组件内容成功"
                    data:
                      componentId: 2001
                      courseId: 1001
                      componentName: "知识点1-人工智能定义与发展"
                      componentType: "document"
                      componentOrder: 1
                      documentContent:
                        contentUrl: "https://cdn.example.com/content/doc-2001.json"
                        contentType: "interactive-document"
                        contentVersion: "v1.2.0"
                        previewImage: "https://cdn.example.com/preview/doc-2001-preview.jpg"
                      drawingTrajectoryData:
                        trajectoryUrl: "https://cdn.example.com/trajectory/traj-2001.json"
                        trajectoryVersion: "v1.0.0"
                        syncPoints:
                          - time: 0
                            action: "start_drawing"
                            elementId: "title_001"
                          - time: 15.5
                            action: "highlight"
                            elementId: "concept_001"
                          - time: 45.0
                            action: "complete"
                            elementId: "summary_001"
                      totalDuration: 180
                      subtitleData:
                        subtitleUrl: "https://cdn.example.com/subtitle/sub-2001.vtt"
                        subtitleVersion: "v1.0.0"
                        defaultEnabled: true
                        language: "zh-CN"
                      playbackConfig:
                        defaultMode: "follow"
                        supportedSpeeds: [0.5, 0.75, 1.0, 1.25, 1.5, 1.75, 2.0, 3.0]
                        enableDoubleClick: true
                        enableLongPress: true
                        seekStepSeconds: 10
                      status: "active"
        '400':
          description: 请求参数错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                missing_component_id:
                  summary: 缺少组件ID参数
                  value:
                    code: 4000001
                    message: "请求参数错误"
                    detail: "componentId参数不能为空"
                    data:
                      field: "componentId"
                invalid_component_id:
                  summary: 无效的组件ID
                  value:
                    code: 4000002
                    message: "请求参数错误"
                    detail: "componentId必须是正整数"
                    data:
                      field: "componentId"
                      value: "invalid_id"
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '403':
          description: 无权限访问该组件
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                no_component_permission:
                  summary: 无组件访问权限
                  value:
                    code: 4030001
                    message: "无权限访问该组件"
                    detail: "用户未获得该组件所属课程的学习权限"
                    data:
                      componentId: 2001
                      courseId: 1001
                      userId: 10001
        '404':
          description: 组件不存在或不可用
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                component_not_found:
                  summary: 组件不存在
                  value:
                    code: 4040001
                    message: "组件不存在"
                    detail: "指定的组件ID不存在或组件不可用"
                    data:
                      componentId: 2001
                component_type_not_supported:
                  summary: 组件类型不支持
                  value:
                    code: 4040002
                    message: "组件类型不支持"
                    detail: "当前版本仅支持文档组件类型"
                    data:
                      componentId: 2001
                      componentType: "exercise"
        '500':
          description: 服务器内部错误
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GenericError'
              examples:
                content_load_error:
                  summary: 内容加载错误
                  value:
                    code: 5000001
                    message: "服务器内部错误"
                    detail: "加载组件内容时发生异常，请稍后重试"
                    data:
                      componentId: 2001
                trajectory_load_error:
                  summary: 轨迹数据加载错误
                  value:
                    code: 5000002
                    message: "服务器内部错误"
                    detail: "加载勾画轨迹数据时发生异常"
                    data:
                      componentId: 2001
                      trajectoryUrl: "https://cdn.example.com/trajectory/traj-2001.json"
      security:
        - BearerAuth: []

  /study/session/start:
    post:
      tags:
        - StudySession
      summary: 开始学习会话
      description: 创建新的学习会话记录，初始化学习状态
      operationId: startStudySession
      requestBody:
        description: 开始学习会话请求数据
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StartSessionRequest'
            examples:
              default:
                value:
                  userId: 10001
                  courseId: 1001
      responses:
        '201':
          description: 学习会话创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/StudySession'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
      security:
        - BearerAuth: []

  /study/session/end:
    post:
      tags:
        - StudySession
      summary: 结束学习会话
      description: 结束当前学习会话，触发掌握度计算和学习数据汇总
      operationId: endStudySession
      requestBody:
        description: 结束学习会话请求数据
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EndSessionRequest'
            examples:
              default:
                value:
                  sessionId: 'session_1001_20250526_001'
      responses:
        '200':
          description: 学习会话结束成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          sessionId:
                            type: string
                            description: 会话ID
                          completed:
                            type: boolean
                            description: 是否完成
                          masteryUpdated:
                            type: boolean
                            description: 掌握度是否已更新
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /study/interaction:
    post:
      tags:
        - StudyInteraction
      summary: 学习交互操作
      description: 处理学习过程中的各种交互操作，包括模式切换、播放控制、进度更新等
      operationId: studyInteraction
      requestBody:
        description: 学习交互操作请求数据
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InteractionRequest'
            examples:
              mode_switch:
                summary: 模式切换示例
                value:
                  sessionId: 'session_1001_20250526_001'
                  componentId: 2001
                  operation: 'mode_switch'
                  operationData:
                    mode: 'free'
              playback_control:
                summary: 播放控制示例
                value:
                  sessionId: 'session_1001_20250526_001'
                  componentId: 2001
                  operation: 'playback_control'
                  operationData:
                    playbackAction: 'double_click'
                    speed: 1.5
              progress_update:
                summary: 进度更新示例
                value:
                  sessionId: 'session_1001_20250526_001'
                  componentId: 2001
                  operation: 'progress_update'
                  operationData:
                    position: 120
      responses:
        '200':
          description: 交互操作执行成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/InteractionResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /study/report:
    get:
      tags:
        - StudyReport
      summary: 获取学习报告
      description: 获取指定学习会话的完整学习报告，包括学习表现、掌握度、答题详情等
      operationId: getStudyReport
      parameters:
        - $ref: '#/components/parameters/sessionIdQueryParameter'
      responses:
        '200':
          description: 成功获取学习报告
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/StudyReport'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []

  /study/feedback/submit:
    post:
      tags:
        - StudyFeedback
      summary: 提交课程反馈
      description: 提交用户对课程的评价和反馈信息
      operationId: submitStudyFeedback
      requestBody:
        description: 课程反馈请求数据
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FeedbackRequest'
            examples:
              default:
                value:
                  sessionId: 'session_1001_20250526_001'
                  ratingLevel: 4
                  feedbackOptions: ['内容清晰', '节奏合适']
                  textFeedback: '课程内容很好，希望增加更多练习题'
      responses:
        '201':
          description: 反馈提交成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/GenericResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/FeedbackResult'
        '400':
          $ref: '#/components/responses/BadRequestError'
        '401':
          $ref: '#/components/responses/UnauthorizedError'
        '404':
          $ref: '#/components/responses/NotFoundError'
      security:
        - BearerAuth: []
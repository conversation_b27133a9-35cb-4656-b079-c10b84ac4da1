---
description: 
globs: backend/app/application/**/*.py
alwaysApply: false
---
# Service 层 (应用服务层) 开发规范 (Go Kratos DDD)

## 1. 核心原则与职责 (增强)

1.  **业务用例驱动**: 实现具体的业务用例 (Use Case)。
2.  **编排与协调**: 协调 Domain 层和 DAO 层。
3.  **事务边界**: 管理事务的最佳位置。
4.  **应用层错误处理**: 捕获、转换、包装错误，**遵循项目统一错误码体系**。
5.  **数据转换**: 负责 Domain <-> DTO 转换。
6.  **框架适配 (有限)**: 可依赖 Kratos 核心组件 (Logger, TM)，但与 Web/RPC 框架解耦。
7.  **保持精简**: 聚焦流程控制，复杂业务规则下沉 Domain。
8.  **指导原则**: **遵循 KISS 和 SOLID (尤其是单一职责、依赖倒置) 原则**，优先保证代码清晰、可维护，再考虑性能优化。

## 2. 目录结构

```
app/{biz_module}/
└── service/
    ├── service.go         # 定义 Service 接口和依赖注入 ProviderSet
    ├── {usecase}_service.go # 具体业务用例的实现 (例如 user_service.go, auth_service.go)
    ├── {usecase}_service_test.go # 对应的单元测试
    └── errors.go          # (可选) Service 层特定的错误定义
```

## 3. 结构体定义与依赖注入 (增强)

1.  **接口与实现分离**:
    *   在 `service.go` 中定义 Service 接口 (例如 `UserService`)。
    *   在 `{usecase}_service.go` 中定义实现该接口的结构体 (例如 `userService`)。
2.  **命名**: 接口名通常为 `{Concept}Service`，实现结构体名为小写开头的 `{concept}Service`。
3.  **依赖**: Service 实现结构体应包含其所有依赖项作为字段，通常包括：
    *   相关的 Repository 接口 (来自 `domain/repository`)。
    *   可能需要的其他 Service 接口。
    *   Kratos 日志辅助对象 (`*log.Helper`)。
    *   Kratos 事务管理器 (`transaction.Manager`)。
    *   (可选) 其他基础设施接口，如缓存、消息队列等。
4.  **构造函数**: 提供 `New{Concept}Service` 构造函数，接收所有依赖项作为参数，返回 Service 接口类型。
5.  **依赖注入 (Wire)**:
    *   在 `service.go` 文件中使用 Kratos/Wire 定义 `ProviderSet`，将构造函数、依赖项绑定在一起。
    *   确保所有依赖项都已在其他层 (如 DAO, Domain) 或 `cmd` 目录的 `wire.go` 中提供了 Provider。
    *   **禁止**: **严禁在 Service 结构体内部使用全局变量或包级别变量来持有状态或依赖**。所有依赖必须通过构造函数注入。
    *   **Logger 获取**: (注释) *请注意：如果项目规定了标准的 Logger 获取方式（如 `app/core/logger/logger_new`），请确保这里的 `log.Logger` 依赖是按照该标准注入的。*
    ```go
    // app/user/service/service.go
    package service

    import (
        "github.com/go-kratos/kratos/v2/log"
        "github.com/google/wire"
        
        "your_project/app/user/domain/repository"
    )

    // UserService 用户服务接口
    type UserService interface {
        CreateUser(ctx context.Context, req *dto.CreateUserRequestDTO) (*dto.UserResponseDTO, error)
        GetUser(ctx context.Context, userID int64) (*dto.UserResponseDTO, error)
        UpdateUser(ctx context.Context, req *dto.UpdateUserRequestDTO) (*dto.UserResponseDTO, error)
        DeleteUser(ctx context.Context, userID int64) error
        ListUsers(ctx context.Context, req *dto.ListUsersRequestDTO) (*dto.ListUsersResponseDTO, error)
    }

    // userService 用户服务实现
    type userService struct {
        userRepo repository.UserRepository
        log      *log.Helper
        tm       transaction.Manager
    }

    // NewUserService 创建用户服务
    func NewUserService(
        userRepo repository.UserRepository,
        logger log.Logger,
        tm transaction.Manager,
    ) UserService {
        return &userService{
            userRepo: userRepo,
            log:      log.NewHelper(logger),
            tm:       tm,
        }
    }

    // ProviderSet Wire依赖注入
    var ProviderSet = wire.NewSet(NewUserService)

    import (
        "github.com/go-kratos/kratos/v2/log"
        "github.com/google/wire"
        "your_project/app/user/domain/repository"
        "your_project/app/pkg/transaction" // 假设的事务包路径
        v1 "your_project/api/user/v1" // 引入pb.go的定义，如果Service接口需要
    )

    // ProviderSet is service providers.
    var ProviderSet = wire.NewSet(NewUserService)

    // UserService defines the interface for user related services.
    // 注意：接口的方法签名通常直接使用 DTO 或原生类型，而不是pb.go的类型
    type UserService interface {
        Register(ctx context.Context, req *dto.RegisterRequestDTO) (*dto.UserDTO, error)
        Login(ctx context.Context, req *dto.LoginRequestDTO) (*dto.LoginResponseDTO, error)
        GetUserProfile(ctx context.Context, userID int64) (*dto.UserDTO, error)
        // ... 其他用户相关接口
    }

    // userService implements the UserService interface.
    type userService struct {
        repo   repository.UserRepository // 依赖 Repository 接口
        auth   AuthService               // 依赖其他 Service 接口 (示例)
        tm     transaction.Manager       // 依赖事务管理器
        log    *log.Helper               // 依赖日志辅助对象
    }

    // NewUserService creates a new userService.
    func NewUserService(repo repository.UserRepository, auth AuthService, tm transaction.Manager, logger log.Logger) UserService {
        return &userService{
            repo:   repo,
            auth:   auth,
            tm:     tm,
            log:    log.NewHelper(log.With(logger, "module", "service/user")), // 添加模块日志上下文
        }
    }

    // 实现 UserService 接口的方法在 user_service.go 文件中...
    ```

## 4. 方法定义 (增强)

1.  **签名**: 公开方法应定义在 Service 接口中。
2.  **`context.Context`**: **必须**将 `ctx context.Context` 作为第一个参数，用于传递链路追踪、超时和取消信号。
3.  **输入参数**: **必须**使用 DTO (`*.go` 文件中定义的结构体) 或 Go 原生类型。**禁止**直接使用 Protobuf 生成的 `*.pb.go` 结构体作为输入参数类型。
4.  **返回值**:
    *   通常返回 `(*ResponseTypeDTO, error)`。
    *   对于没有具体返回数据的操作 (如 `UpdateUserProfile`)，可以返回 `error`。
    *   **禁止**直接返回 Domain 实体给 Controller。
5.  **长度与复杂度**: **单个 Service 方法的实现代码长度建议不超过 50 行，圈复杂度不超过 15**。复杂逻辑应拆分到私有方法、Domain 层或 `app/util` 中。

## 5. 业务逻辑实现 (增强)

1.  **单一职责**: 每个 Service 方法应聚焦于完成一个独立的业务用例。
2.  **参数验证**: Service 层可进行复杂的业务逻辑验证。**优先使用 `app/util/validator` (如果存在) 或标准库进行结构化验证**。
3.  **数据转换 (DTO -> Domain)**: 在调用 Repository 或 Domain 方法前转换。
4.  **权限校验**: **必须**进行必要的权限校验。**建议封装成可复用的私有方法 `s.checkPermission(ctx, userID, resource, action)` 或使用通用的权限校验中间件/库 (通过依赖注入)**。
5.  **调用 Repository**: 通过注入的接口与数据层交互。
6.  **调用 Domain Logic**: **优先**调用 Domain 实体自身的方法。
7.  **调用其他 Service / 外部服务**: 
    *   注意避免循环依赖。
    *   **当下游是外部 HTTP/gRPC 服务时，应使用支持熔断、重试和超时的标准客户端库（如果项目有封装，优先使用封装库）**。
8.  **数据转换 (Domain -> DTO)**: 在方法返回前转换。
9.  **幂等性**: 对于写操作，根据业务需求考虑。可通过检查唯一业务 ID、Token 或利用数据库约束实现。

## 6. 事务管理

1.  **使用 `transaction.Manager`**: Kratos 通常提供一个事务管理器接口 (例如 `kratos/v2/transport/http.HandlerFunc`，或更通用的 `kratos/v2/middleware/transaction.Manager`，具体视项目采用的 Kratos 版本和组件而定)。此管理器**必须**通过依赖注入（通常在 `cmd/{service_name}/internal/wire/wire.go` 中配置 Provider）注入到 Service 实现结构体中。
2.  **包裹写操作 (`InTransaction`)**: 
    *   **必须**使用事务管理器提供的 `InTransaction(ctx context.Context, fn func(txCtx context.Context) error, opts ...Option) error` (或类似签名的方法) 来包裹所有涉及**一个或多个数据写入或修改**的业务逻辑单元。
    *   闭包 `fn` 接收一个新的上下文 `txCtx`，**后续所有在该事务单元内的 Repository 调用都必须使用此 `txCtx`**，以确保它们在同一事务中执行。
    *   如果闭包 `fn` 返回 `nil`，事务将自动提交。
    *   如果闭包 `fn` 返回任何非 `nil` 的 `error`，事务将自动回滚。
    ```go
    func (s *userService) Register(ctx context.Context, req *dto.RegisterRequestDTO) (*dto.UserDTO, error) {
        var registeredUser *entity.User
        var err error

        // 使用事务包裹注册流程
        // 强调：s.tm 是注入的 transaction.Manager
        err = s.tm.InTransaction(ctx, func(txCtx context.Context) error { // txCtx 是带有事务上下文的新 context
            // 1. 检查用户名是否已存在 (读操作，可在事务外，也可在事务内)
            // 注意：后续所有 repo 调用都使用 txCtx
            existingUser, findErr := s.repo.FindByUsername(txCtx, req.Username)
            if findErr != nil && !errors.Is(findErr, domainErrors.ErrUserNotFound) { // 忽略未找到错误
                return findErr // 返回 DAO 层或其他内部错误，将导致回滚
            }
            if existingUser != nil {
                return domainErrors.ErrUserExists // 返回领域错误，将导致回滚
            }

            // 2. 创建 User 实体 (调用领域工厂方法)
            newUser, domainErr := entity.NewUser(req.Username, req.Password, req.Email)
            if domainErr != nil {
                return domainErr // 返回领域错误，将导致回滚
            }

            // 3. 保存用户 (写操作，必须在事务内)
            saveErr := s.repo.Save(txCtx, newUser) // 使用 txCtx
            if saveErr != nil {
                // DAO 层应处理唯一键冲突等，并可能返回领域错误
                return saveErr // 返回 DAO 层错误或已转换的领域错误，将导致回滚
            }
            registeredUser = newUser // 将创建成功的用户传递出来

            // 4. (可选) 发送注册成功事件或执行其他需要在同一事务中的操作
            // eventErr := s.eventBus.Publish(txCtx, events.NewUserRegisteredEvent(newUser.ID)) // 使用 txCtx
            // if eventErr != nil { return eventErr }

            return nil // 事务成功提交
        })

        // 事务执行完毕后处理错误
        if err != nil {
            s.log.WithContext(ctx).Errorf("用户注册失败: %+v", err)
            // 将领域/DAO错误转换为 Service 层错误或直接向上传递
            if errors.Is(err, domainErrors.ErrUserExists) {
                return nil, serviceErrors.ErrUsernameConflict // 返回 Service 层特定错误
            }
            // ... 其他错误转换 ...
            return nil, serviceErrors.ErrRegistrationFailed.Wrap(err) // 返回通用 Service 错误并包装原始错误
        }

        // 5. 转换并返回 DTO
        return dto.ToUserDTO(registeredUser), nil
    }
    ```
3.  **只读操作**: 对于纯粹的数据查询操作（不涉及任何数据变更），**通常不需要**使用事务包裹。直接使用原始的 `ctx` 调用 Repository 方法即可。如果事务管理器支持只读事务 (`InReadOnlyTransaction`) 并且业务认为有必要（例如，确保在长查询期间数据的一致性视图，或利用数据库的只读事务优化），可以按需使用。
4.  **事务传播与嵌套**: 
    *   Kratos 的事务管理器通常支持事务传播。如果 Service A 的方法在 `InTransaction` 闭包内调用了 Service B 的某个也使用了 `InTransaction` 的方法，Service B 的操作会自动加入 Service A 已开启的事务中。具体行为依赖于所用 `transaction.Manager` 的实现细节。
    *   **应避免手动嵌套 `InTransaction` 调用** (即在一个 `InTransaction` 的闭包内部再次显式调用 `s.tm.InTransaction`)，除非对框架的事务传播机制和数据库的嵌套事务/保存点支持有深入理解和特定需求。通常，一个顶层的业务用例对应一个 `InTransaction` 调用。
5.  **事务超时**: 事务的执行时间同样受到传递给 `InTransaction` 的 `ctx` 中可能包含的超时设置的影响。如果业务操作（包括所有数据库交互）超过了 `ctx` 的截止时间，事务应能被中断并回滚。

## 7. 错误处理 (增强)

1.  **捕获与识别**: 必须捕获并识别 Domain/DAO 错误。
2.  **错误转换/包装**: **推荐使用 `app/util/errorx` (如果存在) 或 `github.com/pkg/errors` 提供的 `Wrap/WithMessage` 功能**，包装底层错误并附加业务上下文信息。保持错误链清晰。
3.  **遵循错误码体系**: 转换错误时，**必须**遵循项目定义的统一错误码规范（如 `app/util/errorx` 中定义的错误码）。
4.  **示例**:
    ```go
    // 假设 s.repo.Save 返回了 domainErrors.ErrUserExists
    if errors.Is(err, domainErrors.ErrUserExists) {
        // 转换为 Service 层特定错误，可能带有特定错误码
        return nil, serviceErrors.ErrUsernameConflict.Wrap(err).WithMsg("用户名已存在")
    }
    // 假设 s.repo.Save 返回了其他数据库错误
    if err != nil {
        // 包装为通用内部错误，记录详细信息，但向上层屏蔽细节
        s.log.WithContext(ctx).Errorf("保存用户失败, DB Error: %+v", err)
        return nil, serviceErrors.ErrInternal.Wrap(err).WithMsg("创建用户时发生内部错误")
    }
    ```
5.  **定义 Service 错误 (可选)**: 定义 Service 层特定错误（存放于 `service/errors.go`），**不应**包含 HTTP 状态码，但可关联业务错误码。
6.  **日志**: **必须**在返回错误前记录 Error 日志，包含完整错误链 (`%+v`) 和上下文 (TraceID, UserID 等)。
7.  **向上层返回**: 向 Controller 层返回的 `error` 应是经过转换/包装的 Service 层错误或 Domain 错误（如果业务允许直接透传）。

## 8. 日志记录 (增强)

1.  **注入与上下文**: **标准实践**: 通过构造函数注入 `log.Logger`。**优先使用项目标准的 Logger 获取方式 (如 `app/core/logger/logger_new`)** 来获取 `log.Logger` 实例。
2.  **创建 `log.Helper` 并附加模块信息 `log.NewHelper(log.With(logger, "module", "service/user"))`。
3. **必须**使用 `s.log.WithContext(ctx)` 传递上下文，确保 TraceID 等信息能自动记录。
4. **日志内容**: **必须**包含 TraceID (通常由框架中间件自动注入)。
5. **必须**包含 UserID (如果适用，**强烈推荐使用 `app/util/contextx.GetUserID(ctx)` 等安全工具函数获取**，避免 panic)。
6. **包含关键业务 ID (如 OrderID, ProductID), 以及清晰的操作描述。
7. **记录点**: 入口 (Info, 含脱敏参数), 出口 (Info, 成功), 错误处理 (Error, 含完整错误), 关键业务分支 (Info/Debug)。
8. **脱敏**: **严格禁止**记录密码、Token、手机号、身份证等敏感信息。**使用项目提供的脱敏工具或方法** (如果存在)。

## 9. DTO 转换

1. **位置**: **推荐将转换函数统一放在 `dto/converter.go` 中**，保持 Service 代码的简洁性。
2. **实现**: 编写清晰、健壮的转换函数，处理好 `nil` 指针、默认值、枚举与基础类型的转换。
3. **工具**: 可使用 `copier` 等库，但需注意深拷贝和类型差异。
4. **批量转换**: **必须**提供批量转换函数 (`ToUserDTOs`) 以优化性能。
5. **规范遵循**: **必须**确保 DTO 结构定义及其字段（特别是时间戳使用 UTC 秒数 `int64`，`int` 默认使用 `int64`）符合全局 DTO 定义规范（如 `model/dto` 约定）。

## 10. 代码复用与封装

1.  **识别通用逻辑**: 识别 Service 层中常见的重复模式，例如：
    *   从 `ctx` 获取用户信息或 TraceID。
    *   检查资源归属或执行权限校验。
    *   构造分页查询参数。
    *   统一的错误包装逻辑。
    *   调用缓存的通用模式。
2.  **封装策略**:
    *   **优先使用 `app/util`**: 对于项目全局通用的逻辑（如 Context 操作、错误处理、分页计算、常用验证），**必须**优先封装到 `app/util/` 下的对应工具包中，并在 Service 层调用。
    *   **Service 私有方法**: 对于仅在**单个 Service 实现内部**重复使用的逻辑（如特定的权限检查组合、复杂的 DTO 组装），封装为该 Service 的私有方法 (`s.checkCreatePermission(ctx, data)`）。
    *   **模块内共享**: 对于在**同一业务模块内**多个 Service 可能共享的逻辑（可能性较低，应优先考虑下沉到 Domain 或提升到 `app/util`），可以放在 `app/{biz_module}/service/common.go` 中，但需谨慎使用，避免 Service 间产生不必要的耦合。
    *   **禁止工具类膨胀**: 避免在 Service 层创建庞杂的 `Helper` 或 `Util` 类，坚持职责分离原则。
3.  **示例**: 获取用户信息
    ```go
    // 不推荐的方式：在每个 Service 方法中重复获取
    userIDStr := ctx.Value("user_id").(string)
    userID, _ := strconv.ParseInt(userIDStr, 10, 64)

    // 推荐的方式：使用工具包
    // import "your_project/app/util/contextx"
    userID, err := contextx.GetUserID(ctx)
    if err != nil {
        // 处理错误
    }
    ```

## 11. 性能优化与注意事项 (增强)

1.  **缓存使用 (Cache-Aside Pattern)**:
    *   对于需要缓存的数据，Service 层应协调缓存和数据库的交互。
    *   **注入缓存接口**: 定义 `CacheRepository` 接口 (类似 `DataRepository`)，注入到 Service。
    *   **读操作**: 先尝试从 `CacheRepository` 读取。若缓存命中，直接返回；若未命中，则调用 `DataRepository` 读取数据库，将结果写入 `CacheRepository` 后再返回。
    *   **写操作**: 先调用 `DataRepository` 更新数据库，**成功后**，再调用 `CacheRepository` **失效 (删除)** 相应的缓存键（而不是更新缓存，以避免脏数据）。
    *   **禁止**: Service 层直接依赖具体缓存实现 (如 Redis Client)，必须通过接口交互。
    ```go
    // 伪代码示例
    func (s *userService) GetUserProfile(ctx context.Context, userID int64) (*dto.UserDTO, error) {
        // 1. 尝试读缓存
        userDTO, err := s.cacheRepo.GetUserProfile(ctx, userID)
        if err == nil && userDTO != nil {
            s.log.WithContext(ctx).Debugf("缓存命中: UserID=%d", userID)
            return userDTO, nil
        }
        if err != nil && !errors.Is(err, cache.ErrCacheMiss) { // 假设有 ErrCacheMiss
             s.log.WithContext(ctx).Warnf("读取用户缓存失败: UserID=%d, Error: %+v", userID, err)
             // 缓存失败，可以选择继续读库或返回错误，取决于策略
        }

        // 2. 缓存未命中或失败，读数据库
        s.log.WithContext(ctx).Debugf("缓存未命中，读数据库: UserID=%d", userID)
        user, dbErr := s.repo.FindByID(ctx, userID)
        if dbErr != nil {
            // ... 处理数据库错误 ...
            return nil, serviceError
        }

        // 3. 转换结果
        resultDTO := dto.ToUserDTO(user)

        // 4. 回写缓存 (异步或同步，注意处理错误)
        go func(bgCtx context.Context, id int64, data *dto.UserDTO) {
             // 使用新的 Context，传递 TraceID 等必要信息
             // traceID := contextx.GetTraceID(ctx)
             // bgCtx = contextx.SetTraceID(context.Background(), traceID)
             if cacheErr := s.cacheRepo.SetUserProfile(bgCtx, id, data, time.Minute*5); cacheErr != nil {
                  s.log.WithContext(bgCtx).Errorf("回写用户缓存失败: UserID=%d, Error: %+v", id, cacheErr)
             }
        }(context.Background(), userID, resultDTO) // 注意Context和错误处理

        return resultDTO, nil
    }
    ```
2.  **避免 N+1 查询**:
    *   在处理列表或关联数据时，**必须警惕**在循环中调用 Repository 的单次查询方法。
    *   **要求 Repository 提供批量接口**: 确保 `UserRepository` 等提供了 `FindUsersByIDs`, `FindProductsByOrderIDs` 等批量查询方法。
    *   Service 层应收集所有需要查询的 ID，**一次性调用**批量查询接口获取数据，然后在内存中进行匹配和组装。
3.  **并发执行**: 
    *   如果一个业务用例需要调用多个**互相独立**的 Repository 方法或下游 Service，可以使用 `golang.org/x/sync/errgroup` 进行并发调用以缩短响应时间。
    *   **必须**正确处理 `errgroup` 返回的错误，并确保将原始请求的 `ctx` 传递给并发执行的函数。
    ```go
    // 伪代码示例
    func (s *productService) GetProductDetailWithRecommendations(ctx context.Context, productID int64, userID int64) (*dto.ProductDetailDTO, error) {
        var product *entity.Product
        var recommendations []*entity.Product
        var userWishlist []int64

        eg, childCtx := errgroup.WithContext(ctx)

        // 并发获取产品信息
        eg.Go(func() error {
            p, err := s.productRepo.FindByID(childCtx, productID)
            if err != nil { return err }
            product = p
            return nil
        })

        // 并发获取推荐产品
        eg.Go(func() error {
            r, err := s.recommendService.GetRecommendations(childCtx, productID, userID)
            if err != nil { return err }
            recommendations = r
            return nil
        })

        // 并发获取用户心愿单 (假设需要)
        eg.Go(func() error {
            w, err := s.wishlistRepo.GetUserWishlistIDs(childCtx, userID)
            if err != nil { return err }
            userWishlist = w
            return nil
        })

        // 等待所有并发任务完成
        if err := eg.Wait(); err != nil {
            s.log.WithContext(ctx).Errorf("获取产品详情并发任务失败: ProductID=%d, Error: %+v", productID, err)
            // 处理错误
            return nil, serviceError
        }

        // 组装 DTO
        return s.assembleProductDetailDTO(product, recommendations, userWishlist), nil
    }
    ```
4.  **大数据量处理**: 对于可能返回大量数据的查询，**必须**进行分页处理。Service 层应接收分页参数 (通常来自 DTO)，并传递给 Repository 的分页查询方法。
5.  **尊重 Context**: Service 方法**必须**尊重传入的 `ctx context.Context` 的超时和取消信号。在进行耗时操作或调用下游时，应确保 Context 能被正确传递和检查，避免资源泄露和无效计算。
6.  **性能理念**: **优先编写清晰、正确、可维护的代码**。性能优化应基于实际的性能分析 (Profiling) 结果，针对性地进行，避免过早或不必要的优化。

## 12. 常量使用 (增强)

1.  **Domain 常量优先**: **必须**优先使用在 `app/{biz_module}/domain/consts` 中定义的领域常量（如业务状态枚举 `UserStatusActive`）。
2.  **全局常量**: 如果需要项目全局共享的常量（如**默认分页大小、特定 Context Key、业务无关的配置键名**），应定义在 `app/consts` 中。
3.  **Service 内部常量**: 仅限于 Service 自身实现细节相关的、不具有普遍业务含义的常量（例如某个内部重试次数、特定的日志消息模板），可以定义在 Service 包内部。**避免将业务规则相关的常量定义在 Service 层**。
4.  **禁止硬编码**: **严禁**硬编码魔法数字、字符串，特别是**配置类参数（如缓存过期时间、重试次数、队列名称等），这些应来自配置中心或 `app/consts`**。

## 13. 监控与指标 (新增)

1.  **业务指标记录**: Service 层是记录关键业务指标的理想位置。
2.  **方式**: 通过注入 Metrics 客户端 (如 Prometheus Client)，在关键业务节点记录 Counter (计数), Gauge (瞬时值), Histogram/Summary (分布)。
3.  **场景**: 
    *   **计数器 (Counter)**: 统计业务操作次数（如 `user_registrations_total`, `login_failures_total`）。
    *   **仪表盘 (Gauge)**: 记录瞬时状态（如 `online_users`，如果 Service 负责维护）。
    *   **直方图/摘要 (Histogram/Summary)**: 记录操作耗时或处理大小的分布（如 `request_duration_seconds`，虽然通常在 Middleware 中记录，但 Service 内特定耗时操作也可记录）。
4.  **标签 (Labels)**: 指标应包含有意义的标签，以便于聚合和筛选（如 `service_method`, `error_type`, `region`）。
    ```go
    // 伪代码示例 (假设注入了 metrics.CounterVec)
    func (s *userService) Register(...) (*dto.UserDTO, error) {
        // ... (处理逻辑) ...
        if err != nil {
             // 记录失败指标
             s.metrics.RegisterTotal.WithLabelValues("failure", classifyError(err)).Inc()
             // ... 返回错误
        }
        // 记录成功指标
        s.metrics.RegisterTotal.WithLabelValues("success", "").Inc()
        return dto, nil
    }
    ```

## 14. 测试策略 (原 Section 13) (增强)

1.  **单元测试 (必须)**:
    *   **目标**: 验证 Service 方法的业务逻辑、分支、错误处理是否正确。
    *   **隔离**: **必须**使用 Mock 工具 (如 `gomock`) Mock 所有的外部依赖，特别是 Repository 接口、其他 Service 接口、**事务管理器 (`tm.InTransaction`)**、**缓存接口 (`CacheRepository`)**。
    *   **Mock Controller**: 使用 `gomock.NewController(t)` 创建 Mock 控制器。
    *   **创建 Mock 对象**: 为每个依赖接口创建 Mock 实例 (如 `mockRepo := mock_repository.NewMockUserRepository(ctrl)`）。
    *   **设置预期 (`EXPECT`)**: 精确设置 Mock 方法的预期调用次数、参数 (使用 `gomock.Eq()`, `gomock.Any()`, `gomock.AssignableToTypeOf()` 等) 和返回值 (使用 `Return()`)。
    *   **Mock 事务**: 
        ```go
        // 假设 tm 是 Mock Transaction Manager
        tm.EXPECT().InTransaction(gomock.Any(), gomock.Any()).DoAndReturn(
            func(ctx context.Context, fn func(ctx context.Context) error) error {
                // 直接在测试中执行传入的函数，模拟事务体内的逻辑
                return fn(ctx)
            },
        ).AnyTimes() // 或 .Times(1) 等，根据需要
        ```
    *   **覆盖**: 覆盖所有成功的业务路径、已知的错误路径、边界条件。
    *   **断言**: 使用 `testify/assert` 或 `testify/require` 精确断言返回值 (DTO) 的内容和返回的 `error` 类型/值是否符合预期。
    *   **测试结构**: **推荐使用表格驱动测试 (Table-Driven Tests)** 组织测试用例。
    *   **Setup/Teardown**: 使用 `func setup(t *testing.T) (*gomock.Controller, *mock_repository.MockUserRepository, *mock_transaction.MockManager, service.UserService)` 等辅助函数进行初始化。
    *   **覆盖率**: **单元测试覆盖率要求达到 80% 以上**。
2.  **集成测试 (可选但推荐)**:
    *   **目标**: 验证 Service 与其直接依赖的真实组件 (如数据库 через DAO) 的交互是否正确。
    *   **环境**: 通常需要连接到测试数据库，并进行数据初始化和清理。
    *   **范围**: 测试关键的业务流程，例如涉及多个 Repository 调用的复杂事务场景。
3. **测试文件**: 测试文件应与源文件放在同一目录下，命名为 `{usecase}_service_test.go`。

## 15. Go 语言示例 (原 Section 14) (增强)

```go
// app/user/service/auth_service.go (假设的依赖)
package service
import "context"
type AuthService interface {
    GenerateToken(ctx context.Context, userID int64, username string) (string, error)
}

// app/user/service/user_service_test.go
package service_test // 使用 _test 后缀

import (
    "context"
    "testing"
    "time"

    "github.com/go-kratos/kratos/v2/log"
    "github.com/golang/mock/gomock"
    "github.com/pkg/errors"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"

    "your_project/app/user/domain/entity"
    domainErrors "your_project/app/user/domain/errors"
    "your_project/app/user/domain/repository"
    mock_repository "your_project/app/user/domain/repository/mock" // Mock Repository
    "your_project/app/user/dto"
    "your_project/app/user/service"
    serviceErrors "your_project/app/user/service/errors"
    mock_service "your_project/app/user/service/mock" // Mock 其他 Service
    "your_project/app/pkg/transaction" // 假设的事务包
    mock_transaction "your_project/app/pkg/transaction/mock" // Mock 事务管理器
)

// setup 函数用于初始化测试环境
func setupUserServiceTest(t *testing.T) (*gomock.Controller, *mock_repository.MockUserRepository, *mock_service.MockAuthService, *mock_transaction.MockManager, service.UserService) {
    ctrl := gomock.NewController(t)

    mockRepo := mock_repository.NewMockUserRepository(ctrl)
    mockAuth := mock_service.NewMockAuthService(ctrl)
    mockTM := mock_transaction.NewMockManager(ctrl)
    logger := log.NewStdLogger(log.Writer(os.Stdout)) // 使用标准 Logger 或项目推荐方式

    // 模拟 InTransaction 的行为
    mockTM.EXPECT().InTransaction(gomock.Any(), gomock.Any()).DoAndReturn(
        func(ctx context.Context, fn func(ctx context.Context) error) error {
            // 在测试中直接执行事务体内的函数
            return fn(ctx)
        },
    ).AnyTimes()

    userService := service.NewUserService(mockRepo, mockAuth, mockTM, logger)
    return ctrl, mockRepo, mockAuth, mockTM, userService
}

func TestUserService_Login(t *testing.T) {
    ctrl, mockRepo, mockAuth, _, userService := setupUserServiceTest(t)
    defer ctrl.Finish()

    // 定义测试用例
    tests := []struct {
        name          string
        req           *dto.LoginRequestDTO
        mockRepoSetup func(mockRepo *mock_repository.MockUserRepository)
        mockAuthSetup func(mockAuth *mock_service.MockAuthService)
        wantResp      *dto.LoginResponseDTO
        wantErr       error
    }{
        {
            name: "成功登录",
            req: &dto.LoginRequestDTO{
                Username: "testuser",
                Password: "password123",
            },
            mockRepoSetup: func(mockRepo *mock_repository.MockUserRepository) {
                // 假设密码已哈希存储
                hashedPassword := "hashed_password" // 实际应从 PasswordHasher 获取
                user := &entity.User{
                    ID:        1,
                    Username: "testuser",
                    Password: hashedPassword,
                    Status:   entity.UserStatusActive, // 假设 UserStatusActive 定义在 entity 包
                }
                mockRepo.EXPECT().FindByUsername(gomock.Any(), "testuser").Return(user, nil)
                // 假设 User 实体有 VerifyPassword 方法
                // mockRepo.EXPECT().VerifyPassword(user.Password, "password123").Return(true) // 如果校验在 Repo
            },
            mockAuthSetup: func(mockAuth *mock_service.MockAuthService) {
                mockAuth.EXPECT().GenerateToken(gomock.Any(), int64(1), "testuser").Return("fake_token", nil)
            },
            wantResp: &dto.LoginResponseDTO{
                Token: "fake_token",
                User: &dto.UserDTO{
                    ID:        1,
                    Username: "testuser",
                    // Email, Mobile 等字段省略
                },
            },
            wantErr: nil,
        },
        {
            name: "用户不存在",
            req: &dto.LoginRequestDTO{
                Username: "nosuchuser",
                Password: "password123",
            },
            mockRepoSetup: func(mockRepo *mock_repository.MockUserRepository) {
                mockRepo.EXPECT().FindByUsername(gomock.Any(), "nosuchuser").Return(nil, domainErrors.ErrUserNotFound)
            },
            mockAuthSetup: func(mockAuth *mock_service.MockAuthService) {
            },
            wantResp:      nil,
            wantErr:       serviceErrors.ErrLoginFailed, // 期望返回 Service 层定义的登录失败错误
        },
        {
            name: "密码错误",
            req: &dto.LoginRequestDTO{
                Username: "testuser",
                Password: "wrongpassword",
            },
            mockRepoSetup: func(mockRepo *mock_repository.MockUserRepository) {
                hashedPassword := "hashed_password"
                user := &entity.User{
                    ID:        1,
                    Username: "testuser",
                    Password: hashedPassword,
                    Status:   entity.UserStatusActive,
                }
                mockRepo.EXPECT().FindByUsername(gomock.Any(), "testuser").Return(user, nil)
                // 假设 User 实体有 VerifyPassword 方法，并且它返回 false
                // 注意：密码校验逻辑应该在 Domain 或 Service，这里仅为示例 Repo 可能的职责
                // 如果校验在 Service，则此处不 Mock 密码验证，而是在 Service 逻辑中断言
            },
            mockAuthSetup: func(mockAuth *mock_service.MockAuthService) {
            },
            wantResp:      nil,
            wantErr:       serviceErrors.ErrLoginFailed,
        },
        {
            name: "生成 Token 失败",
            req: &dto.LoginRequestDTO{
                Username: "testuser",
                Password: "password123",
            },
            mockRepoSetup: func(mockRepo *mock_repository.MockUserRepository) {
                 hashedPassword := "hashed_password"
                user := &entity.User{ID: 1, Username: "testuser", Password: hashedPassword, Status: entity.UserStatusActive}
                mockRepo.EXPECT().FindByUsername(gomock.Any(), "testuser").Return(user, nil)
            },
            mockAuthSetup: func(mockAuth *mock_service.MockAuthService) {
                mockAuth.EXPECT().GenerateToken(gomock.Any(), int64(1), "testuser").Return("", errors.New("auth service internal error"))
            },
            wantResp:      nil,
            wantErr:       serviceErrors.ErrInternal, // 期望包装后的内部错误
        },
    }

    // 遍历执行测试用例
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 为每个子测试应用 Mock 设置
            tt.mockRepoSetup(mockRepo)
            tt.mockAuthSetup(mockAuth)

            // 调用被测试方法
            resp, err := userService.Login(context.Background(), tt.req)

            // 断言错误
            if tt.wantErr != nil {
                require.Error(t, err) // 必须有错误
                // 检查具体的错误类型或包装的错误
                assert.True(t, errors.Is(err, tt.wantErr) || errors.As(err, &tt.wantErr), "Expected error type %T, got %T (%v)", tt.wantErr, err, err)
            } else {
                require.NoError(t, err) // 必须没有错误
            }

            // 断言响应体 (如果期望非 nil)
            assert.Equal(t, tt.wantResp, resp)
        })
    }
}

// ... 其他测试方法 ...
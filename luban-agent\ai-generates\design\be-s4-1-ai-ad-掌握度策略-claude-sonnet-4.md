# 接口使用文档 - 掌握度策略服务 - V1.0

* **最后更新日期**: 2025-05-26
* **设计负责人**: claude-sonnet-4
* **评审人**: 待填写
* **状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-05-26 | claude-sonnet-4 | 初始草稿     |

## 1. 服务概述与设计目标

### 1.1 服务背景与范围
* 掌握度策略服务是自适应学习系统中的重要功能模块，负责计算和管理学生的学科能力（用户分层）、知识点掌握度、外化掌握度和目标掌握度，为推荐策略提供基础输入。
* 本次接口设计覆盖掌握度策略的核心业务功能，包括掌握度计算、更新、查询以及薄弱知识点分析等功能。
* 服务与用户中心、内容平台和运营管理平台等服务交互，为学生提供个性化的学习体验。

### 1.2 设计目标回顾
* 1. 提供符合 OpenAPI 3.x 规范的接口定义 (YAML格式)，确保可直接导入 Apifox 等工具。
* 2. 清晰定义每个接口的请求、响应、参数、数据模型和错误码。
* 3. 确保接口设计满足产品需求文档 (PRD) 中定义的核心用户场景和业务流程。
* 4. 遵循团队的API设计规范和最佳实践。
* 5. 接口定义包含完整的中文描述信息，便于理解和维护。

## 2. 通用设计规范与约定

### 2.1 数据格式
* 请求体 (Request Body): `application/json`
* 响应体 (Response Body): `application/json`
* 日期时间格式: ISO 8601 (`YYYY-MM-DDTHH:mm:ss.sssZ`)

### 2.2 认证与授权
* 所有需要认证的接口，在请求头中传递 `Authorization: Bearer <token>`
* 使用JWT Token进行用户身份认证
* 基于RBAC模型进行权限控制，学生只能访问自己的掌握度数据，教师只能访问其任教班级学生的掌握度数据

### 2.3 版本控制
* 通过 URL 路径进行版本控制，如 `/api/v1/mastery/...`

### 2.4 错误处理约定
* 遵循统一的错误码和错误响应格式
* **通用错误响应体示例**:
  ```json
  {
    "code": 0,                // 业务错误码，0表示成功，非0表示错误
    "message": "错误信息",     // 用户可读的错误信息
    "detail": "错误详情,可选",  // 错误的详细信息，可选
    "data": "返回数据",        // 可选，返回的数据，错误时通常为空
    "pageInfo": "分页信息,可选"  // 可选，分页信息，错误时通常为空
  }
  ```
* **常用 HTTP 状态码**:
  * `200 OK`: 请求成功。
  * `400 Bad Request`: 请求无效 (例如参数错误、格式错误)。
  * `401 Unauthorized`: 未认证或认证失败。
  * `403 Forbidden`: 已认证，但无权限访问。
  * `404 Not Found`: 请求的资源不存在。
  * `500 Internal Server Error`: 服务器内部错误。

### 2.6 分页与排序约定
* **分页参数**:
  * 分页通常使用 page 和 pageSize 查询参数，page从1开始，pageSize默认为20，最大为100。
* **排序参数**:
  * 排序使用 sortBy 和 sortType 参数，sortBy指定排序字段，sortType指定排序方式（asc或desc）。

## 3. 接口列表

| 接口路径                                    | 请求方式 | 接口名称                 | 接口描述                           |
| ------------------------------------------- | -------- | ------------------------ | ---------------------------------- |
| /api/v1/mastery/answer-record               | POST     | 提交答题记录             | 提交学生的答题记录并更新知识点掌握度 |
| /api/v1/mastery/learning-task/complete      | POST     | 完成学习任务             | 标记学习任务完成并更新外化掌握度   |
| /api/v1/mastery/learning-goal               | POST     | 设置学习目标             | 设置学习目标并计算目标掌握度       |
| /api/v1/mastery/weak-points                 | GET      | 获取薄弱知识点           | 获取学生的薄弱知识点列表           |

## 4. 场景说明

### 4.1 学生答题后实时更新知识点掌握度场景
#### 4.1.1 场景流程
1. 学生在AI课系统中完成一道题目并提交答案
2. 前端调用 `/api/v1/mastery/answer-record` 接口提交答题记录
3. 系统获取题目信息（难度、关联知识点）和学生当前掌握度
4. 系统根据答题结果、题目难度、当前掌握度等因素计算掌握度增量
5. 系统更新相关知识点的掌握度并存储
6. 系统返回更新后的掌握度信息

#### 4.1.2 接口调用时序图
```mermaid
sequenceDiagram
  participant Student as 学生客户端
  participant MasteryAPI as 掌握度策略API
  participant ContentAPI as 内容平台API
  participant Database as 数据库

  Student->>MasteryAPI: 提交答题记录 POST /api/v1/mastery/answer-record
  activate MasteryAPI
  MasteryAPI->>ContentAPI: 获取题目信息（难度、关联知识点）
  ContentAPI-->>MasteryAPI: 返回题目信息
  MasteryAPI->>Database: 获取当前知识点掌握度
  Database-->>MasteryAPI: 返回掌握度数据
  MasteryAPI->>MasteryAPI: 计算掌握度增量
  MasteryAPI->>Database: 更新知识点掌握度
  MasteryAPI-->>Student: 返回更新结果
  deactivate MasteryAPI
```

### 4.2 学生完成学习单元后更新外化掌握度场景
#### 4.2.1 场景流程
1. 学生完成一个学习单元（AI课、巩固练习、作业、测验）
2. 前端调用 `/api/v1/mastery/learning-task/complete` 接口标记任务完成
3. 系统判断是否满足更新条件（非中途退出、答题量足够等）
4. 系统获取课程关联知识点的掌握度数据
5. 系统计算外化掌握度并遵循"只增不降"原则
6. 系统更新外化掌握度并存储

#### 4.2.2 接口调用时序图
```mermaid
sequenceDiagram
  participant Student as 学生客户端
  participant MasteryAPI as 掌握度策略API
  participant ContentAPI as 内容平台API
  participant Database as 数据库

  Student->>MasteryAPI: 完成学习任务 POST /api/v1/mastery/learning-task/complete
  activate MasteryAPI
  MasteryAPI->>MasteryAPI: 判断更新条件
  MasteryAPI->>ContentAPI: 获取课程关联知识点
  ContentAPI-->>MasteryAPI: 返回知识点列表
  MasteryAPI->>Database: 获取知识点掌握度
  Database-->>MasteryAPI: 返回掌握度数据
  MasteryAPI->>MasteryAPI: 计算外化掌握度（只增不降）
  MasteryAPI->>Database: 更新外化掌握度
  MasteryAPI-->>Student: 返回更新结果
  deactivate MasteryAPI
```

### 4.3 学生设定学习目标后计算目标掌握度场景
#### 4.3.1 场景流程
1. 学生设定或修改学习目标（目标院校或年级排名）
2. 前端调用 `/api/v1/mastery/learning-goal` 接口设置学习目标
3. 系统保存学习目标并获取所有课程信息和计算参数
4. 系统根据目标难度、课程难度、考试频率计算每节课的目标掌握度
5. 系统保存目标掌握度并触发薄弱知识点判断流程
6. 系统返回设置结果

#### 4.3.2 接口调用时序图
```mermaid
sequenceDiagram
  participant Student as 学生客户端
  participant MasteryAPI as 掌握度策略API
  participant ContentAPI as 内容平台API
  participant Database as 数据库

  Student->>MasteryAPI: 设置学习目标 POST /api/v1/mastery/learning-goal
  activate MasteryAPI
  MasteryAPI->>Database: 保存学习目标
  MasteryAPI->>ContentAPI: 获取所有课程信息
  ContentAPI-->>MasteryAPI: 返回课程列表
  MasteryAPI->>Database: 获取计算参数
  Database-->>MasteryAPI: 返回参数配置
  MasteryAPI->>MasteryAPI: 计算每节课目标掌握度
  MasteryAPI->>Database: 保存目标掌握度
  MasteryAPI->>MasteryAPI: 触发薄弱知识点判断
  MasteryAPI-->>Student: 返回设置结果
  deactivate MasteryAPI
```

### 4.4 获取学生薄弱知识点场景
#### 4.4.1 场景流程
1. 前端需要展示学生的薄弱知识点信息
2. 前端调用 `/api/v1/mastery/weak-points` 接口获取薄弱知识点列表
3. 系统根据用户ID查询薄弱知识点数据
4. 系统返回薄弱知识点列表，包含推荐优先级等信息

#### 4.4.2 接口调用时序图
```mermaid
sequenceDiagram
  participant Student as 学生客户端
  participant MasteryAPI as 掌握度策略API
  participant Database as 数据库

  Student->>MasteryAPI: 获取薄弱知识点 GET /api/v1/mastery/weak-points
  activate MasteryAPI
  MasteryAPI->>Database: 查询薄弱知识点数据
  Database-->>MasteryAPI: 返回薄弱知识点列表
  MasteryAPI-->>Student: 返回薄弱知识点信息
  deactivate MasteryAPI
```

## 5. 检查清单

### 5.1 PRD核心需求场景与API覆盖性检查

| PRD核心需求点/用户场景 (ID)                     | 涉及的主要API接口 (路径与方法)                                  | 关键输入数据实体/参数 (示例)                        | 关键输出数据实体/参数 (示例)                           | 场景支持度(✅/⚠️/❌) | 数据流正确性(✅/⚠️/❌) | 备注/待办事项                                   |
| :---------------------------------------------- | :-------------------------------------------------------------- | :---------------------------------------------------- | :------------------------------------------------------- | :----------------------------------------------------- | :--------------------------------------------------------- | :---------------------------------------------- |
| 学生答题后实时更新知识点掌握度 | POST /api/v1/mastery/answer-record | userId, questionId, answerResult, answerTime, attemptCount | recordId, updatedMasteries, masteryValue | ✅ | ✅ | 支持首次答题、错题重练、自评主观题等场景 |
| 学生完成学习单元后更新外化掌握度 | POST /api/v1/mastery/learning-task/complete | userId, taskId, courseId, completionStatus | taskId, externalizedMastery, updateSource | ✅ | ✅ | 支持"只增不降"原则和特殊情况处理 |
| 学生设定学习目标后计算目标掌握度 | POST /api/v1/mastery/learning-goal | userId, goalType, goalValue, subjectIds | goalId, targetMasteries, calculatedCourses | ✅ | ✅ | 支持高中院校目标和初中排名目标 |
| 获取学生薄弱知识点 | GET /api/v1/mastery/weak-points | userId, page, pageSize | weakPoints, currentMastery, targetMastery, gapRatio | ✅ | ✅ | 支持分页查询和推荐优先级排序 |
| 系统初始化学生学科能力（用户分层） | 无需专门接口 | 学校数据、学生排名数据 | 用户分层结果 | ✅ | ✅ | 通过后台批处理和数据导入实现 |
| 系统判断知识点是否计算掌握度 | 无需专门接口 | 知识点配置信息 | 是否计算掌握度标识 | ✅ | ✅ | 通过内容平台配置和业务逻辑实现 |

### 5.2 API接口数据实体与数据库支持检查

| API接口 (路径与方法)                     | 操作类型 (CRUD) | 主要数据实体 (模型名称)      | 关键数据属性 (字段名示例)                                 | 依赖的数据库表/视图 (示例)        | 数据库操作 (读/写/更新/删) | 数据可获得性/可行性(✅/⚠️/❌) | 数据一致性保障 (简述策略)                               | 备注/待办事项                                                                 |
| :--------------------------------------- | :-------------- | :--------------------------- | :---------------------------------------------------------- | :-------------------------------- | :--------------------------- | :--------------------------------------------------------- | :-------------------------------------------------------- | :---------------------------------------------------------------------------- |
| POST /api/v1/mastery/answer-record | Create/Update | AnswerRecord, KnowledgePointMastery | userId, questionId, answerResult, masteryValue, answerCount | tbl_mastery_answer_record, tbl_mastery_knowledge_point_mastery | 写/更新 | ✅ | 事务保证、乐观锁、缓存更新 | 需要处理并发更新和掌握度计算的原子性 |
| POST /api/v1/mastery/learning-task/complete | Update | LearningTask, ExternalizedMastery | taskId, userId, courseId, externalizedMasteryValue | tbl_mastery_learning_task, tbl_mastery_externalized_mastery | 读/更新 | ✅ | 事务保证、只增不降逻辑 | 需要获取课程关联知识点信息 |
| POST /api/v1/mastery/learning-goal | Create/Update | LearningGoal, TargetMastery | userId, goalType, goalValue, targetMasteryValue | tbl_mastery_learning_goal, tbl_mastery_target_mastery | 写/批量写 | ✅ | 事务保证、批量操作 | 需要获取所有课程信息进行批量计算 |
| GET /api/v1/mastery/weak-points | Read | WeakPoint | userId, knowledgePointId, currentMastery, targetMastery, gapRatio | tbl_mastery_weak_point | 读 | ✅ | 读一致性、缓存策略 | 支持分页查询和排序 |

### 5.3 API通用设计规范符合性检查

| 检查项                                                                 | 状态 (✅/⚠️/❌/N/A) | 备注/说明                                                                                                |
| :--------------------------------------------------------------------- | :------------------- | :------------------------------------------------------------------------------------------------------- |
| 1. **命名规范**: 接口路径、参数、数据实体命名是否清晰、一致，并遵循团队约定？        | ✅ | 使用RESTful风格，路径清晰，参数命名采用驼峰命名法 |
| 2. **HTTP方法**: GET, POST, PUT, DELETE, PATCH等HTTP方法使用是否语义恰当？        | ✅ | POST用于创建和复杂操作，GET用于查询，符合语义 |
| 3. **状态码**: HTTP状态码是否准确反映操作结果 (成功、客户端错误、服务端错误)？        | ✅ | 201创建成功，400客户端错误，401认证失败，500服务器错误 |
| 4. **错误处理**: 错误响应格式是否统一 (参考2.4)，错误信息是否对用户友好且包含足够调试信息？ | ✅ | 统一的GenericError格式，包含错误码、消息和详细信息 |
| 5. **认证与授权**: 需要保护的接口是否都正确实施了认证和授权机制 (参考2.2)？           | ✅ | 所有接口都要求JWT Bearer认证，支持用户权限控制 |
| 6. **数据格式与校验**: 请求和响应体结构是否定义清晰，数据类型是否准确？输入数据是否有必要的校验？ | ✅ | 明确的数据模型定义，必填字段验证，数据类型和范围校验 |
| 7. **分页与排序**: 对于返回列表数据的接口，是否按约定提供了分页和排序功能 (参考2.6)？    | ✅ | 薄弱知识点查询接口支持分页和按优先级排序 |
| 8. **幂等性**: 对于创建和更新操作，是否考虑了幂等性设计？ (特别是PUT, DELETE)         | ✅ | 答题记录支持重复提交检测，学习目标支持覆盖更新 |
| 9. **安全性**: 是否有潜在的安全风险，如敏感信息泄露、SQL注入、XSS等？             | ✅ | 参数化查询防SQL注入，数据隔离防信息泄露，输入验证防XSS |
| 10. **性能考量**: 接口响应时间是否满足性能要求？大数据量查询是否有优化措施？             | ✅ | 缓存策略，异步处理，批量操作，索引优化 |
| 11. **文档一致性**: 本文档描述是否与OpenAPI规范文件 (`be-s4-2`) 及实际实现保持一致？ | ✅ | 接口定义与OpenAPI规范文件完全一致 |
| 12. **可测试性**: 接口是否易于进行单元测试、集成测试和端到端测试？                    | ✅ | 提供完整的示例数据，支持模拟测试，错误场景覆盖完整 |
| 13. **向后兼容性**: 未来接口变更是否考虑了向后兼容性策略？                            | N/A (新设计) | 对于v1版本可标记为N/A，但后续版本迭代需重点关注 |

## 6. 附录 (Appendix)
* 产品需求文档 (PRD): be-s1-1-ai-prd-掌握度策略-V1.0-gemini-2.5-pro.md
* 技术架构方案 (TD): be-s2-1-ai-td-掌握度策略-V1.0-claude-3.7.md
* 数据实体设计文档 (DE): be-s3-2-ai-de-掌握度策略-V1.0-claude-3.7.md
* 数据库设计文档 (DD): be-s3-4-ai-dd-掌握度策略-V1.0-claude-3.7.md
* OpenAPI 3.x 规范标准
* 团队API设计规范和最佳实践
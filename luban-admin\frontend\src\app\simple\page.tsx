'use client';

import Link from 'next/link';

export default function SimplePage() {
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col items-center justify-center p-8">
      <h1 className="text-4xl font-bold text-gray-800 mb-8">
        简化测试页面
      </h1>
      
      <p className="text-lg text-gray-600 mb-8">
        这是一个没有使用 MainLayout 的简化页面
      </p>
      
      <div className="space-y-4">
        <Link 
          href="/test"
          className="block px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition"
        >
          访问测试页面
        </Link>
        
        <Link 
          href="/workflow"
          className="block px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition"
        >
          访问工作流页面
        </Link>
        
        <a 
          href="/"
          className="block px-6 py-3 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition"
        >
          使用 a 标签访问首页
        </a>
      </div>
      
      <div className="mt-8 p-4 bg-white rounded-lg shadow">
        <p className="text-sm text-gray-500">
          当前时间: {new Date().toLocaleString()}
        </p>
      </div>
    </div>
  );
}
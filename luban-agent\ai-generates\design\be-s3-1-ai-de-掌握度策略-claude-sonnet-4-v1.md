# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** 产品需求文档 - 掌握度策略系统 v1.0
- **PRD版本号：** V1.0
- **提取日期：** 2025-06-05

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 用户分层信息 | 基于学生考试成绩和学校信息进行的用户能力分层，包含学科能力排名和分层等级 | 2.1, 3.1 |
| 知识点掌握度 | 根据学生答题表现动态计算的知识点掌握程度，用于学习内容动态调整 | 2.1, 3.2 |
| 外化掌握度 | 向学生展示的课程掌握情况的可视化指标，用于学习反馈和激励 | 2.1, 3.3 |
| 答题记录 | 学生的答题历史记录，包含答题结果、时间、题目信息等，用于掌握度计算 | 4.场景2, 5.业务流程图 |
| 目标掌握度 | 根据学习目标计算应达到的掌握度标准（V1.0暂不实现） | 2.1, 3.4 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`用户分层信息`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 用户ID | 学生的唯一标识符，关联用户中心 | 整数 | 唯一, 非空 |  | 主键 | 3.1 |
| 学科ID | 学科标识符，关联内容平台 | 整数 | 非空 |  | 外键组成部分 | 3.1 |
| 学校ID | 学校标识符，关联用户中心 | 整数 | 非空 |  | 外键 | 3.1 |
| 用户单科排名百分比 | 学生在年级中的排名，以百分比形式表示 | 数值型 (小数) | [0,1] 区间 |  |  | 3.1, 公式：学科能力排名计算 |
| 校准系数a | 根据学校本科录取率确定的校准参数 | 数值型 (小数) | [1,3] 区间 |  |  | 3.1, 高中学校水平校准 |
| 校准系数b | 根据生源波动情况确定的校准参数 | 数值型 (小数) | 大于0 |  |  | 3.1, 公式：√（历年排名中位数倒数平均 / 今年排名中位数倒数） |
| 学科能力排名 | 计算得出的学科能力排名值 | 数值型 (小数) | [0,1] 区间，向上取整 |  |  | 3.1, 公式：用户单科排名百分比 × 校准系数a × 校准系数b |
| 分层等级 | 用户能力分层标识 | 字符串 | S+, S, A, B, C 五个等级 |  |  | 3.1, 分层标准：S+[0,5]、S(5,10]、A(10,50]、B(50,80]、C(80,100] |
| 分层掌握系数 | 不同分层对应的掌握度系数 | 数值型 (小数) | S+为1.2，S为1.0，A为0.8，B为0.5，C为0.4 |  |  | 3.2, 初始掌握度设置 |
| 创建时间 | 记录创建时间 | 时间戳 | 非空 |  |  | PRD未明确 |
| 更新时间 | 记录更新时间 | 时间戳 | 非空 |  |  | PRD未明确 |

**实体：`知识点掌握度`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 用户ID | 学生的唯一标识符 | 整数 | 唯一, 非空 |  | 主键组成部分 | 3.2 |
| 知识点ID | 知识点标识符，关联内容平台业务树 | 整数 | 非空 |  | 主键组成部分 | 3.2 |
| 当前掌握度 | 学生对该知识点的当前掌握程度 | 数值型 (小数) | [0,1] 区间 | 学科初始值 × 分层掌握系数 |  | 3.2, 公式：初始掌握度 = 学科初始值 × 分层掌握系数 |
| 学科初始值 | 统一设置的学科初始掌握度基准值 | 数值型 (小数) | 固定值 | 0.3 |  | 3.2, 初始掌握度设置 |
| 累计答题次数 | 该知识点的累计答题次数 | 整数 | 大于等于0 | 0 |  | 3.2, 用于判断是否更新外化掌握度 |
| 创建时间 | 记录创建时间 | 时间戳 | 非空 |  |  | PRD未明确 |
| 更新时间 | 记录更新时间 | 时间戳 | 非空 |  |  | PRD未明确 |

**实体：`外化掌握度`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 用户ID | 学生的唯一标识符 | 整数 | 唯一, 非空 |  | 主键组成部分 | 3.3 |
| 课程ID | 课程标识符，关联内容平台 | 整数 | 非空 |  | 主键组成部分 | 3.3 |
| 外化掌握度值 | 向学生展示的掌握度百分比 | 数值型 (小数) | [0,1] 区间，四舍五入保留小数点后2位 |  |  | 3.3, 公式：外化掌握度 = 知识点掌握度 / 掌握度上限 |
| 掌握度上限 | 根据课程难度确定的掌握度上限 | 数值型 (小数) | 课程难度高为1，课程难度中为0.9，课程难度低为0.7 |  |  | 3.3, 外化掌握度展示 |
| 课程难度 | 课程的难度等级 | 字符串 | 高、中、低 三个等级 |  |  | 3.3, 掌握度上限计算依据 |
| 是否薄弱知识点 | 标识是否为薄弱知识点 | 布尔值 | true/false | false |  | 3.5, 薄弱知识点判断 |
| 创建时间 | 记录创建时间 | 时间戳 | 非空 |  |  | PRD未明确 |
| 更新时间 | 记录更新时间 | 时间戳 | 非空 |  |  | PRD未明确 |

**实体：`答题记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 记录ID | 答题记录的唯一标识符 | 整数 | 唯一, 非空, 自增 |  | 主键 | 场景2 |
| 用户ID | 学生的唯一标识符 | 整数 | 非空 |  | 外键 | 场景2 |
| 题目ID | 题目标识符，关联内容平台 | 整数 | 非空 |  | 外键 | 场景2 |
| 知识点ID | 知识点标识符 | 整数 | 非空 |  | 外键 | 场景2 |
| 课程ID | 课程标识符 | 整数 | 非空 |  | 外键 | 场景2 |
| 答题结果 | 答题的正确性结果 | 字符串 | 正确、错误、部分正确 |  |  | 3.2, 单次答题掌握度增量计算 |
| 题目难度系数 | 题目的难度系数 | 数值型 (小数) | L1为0.3，L2为0.5，L3为0.7，L4为0.9，L5为1.1 |  |  | 3.2, 单次答题掌握度增量 |
| 题目类型 | 题目的类型分类 | 字符串 | 客观题、主观题 |  |  | 场景2, 主观题权重调整 |
| 作答时间 | 学生作答所用时间（秒） | 整数 | 大于等于0 |  |  | 场景2, 主观题时间判断 |
| 是否重复答题 | 标识是否为重复答题 | 布尔值 | true/false | false |  | 场景2, 重复答题衰减机制 |
| 之前答题结果 | 重复答题时的之前结果 | 字符串 | 正确、错误、部分正确，可为空 |  |  | 场景2, 重复答题处理 |
| 掌握度增量 | 本次答题产生的掌握度变化 | 数值型 (小数) | 可正可负 |  |  | 3.2, 单次答题掌握度增量计算 |
| 权重系数 | 应用于掌握度计算的权重 | 数值型 (小数) | 主观题为0.5，客观题为1.0 | 1.0 |  | 场景2, 主观题权重调整 |
| 答错补偿系数 | 答错时的补偿系数 | 数值型 (小数) | L1为1.8，L2为0.8，L3为0.5，L4为0.3，L5为0.2 |  |  | 3.2, 答错时的增量计算 |
| 答题时间 | 答题发生的时间 | 时间戳 | 非空 |  |  | 场景2 |

**实体：`目标掌握度`**（V1.0暂不实现）

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 用户ID | 学生的唯一标识符 | 整数 | 唯一, 非空 |  | 主键组成部分 | 3.4 |
| 课程ID | 课程标识符 | 整数 | 非空 |  | 主键组成部分 | 3.4 |
| 目标掌握度值 | 根据学习目标计算的应达到掌握度 | 数值型 (小数) | [0,1] 区间，按0.05向上取整 |  |  | 3.4, 公式：目标掌握度 = 目标难度系数 × 单课难度系数 × 考试频率 |
| 目标难度系数 | 根据目标院校确定的难度系数 | 数值型 (小数) | C9为1.3，985/211为1.2，一本为1.1，二本为1，二本以下为0.9 |  |  | 3.4, 目标掌握度计算 |
| 单课难度系数 | 根据文理科和课程难度确定 | 数值型 (小数) | 文科高0.7/中0.65/低0.6，理科高0.8/中0.7/低0.6 |  |  | 3.4, 目标掌握度计算 |
| 考试频率 | 考试频率系数 | 数值型 (小数) | 高1/中0.9/低0.8 |  |  | 3.4, 目标掌握度计算 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 (例如：用户 '拥有一个' 学校档案) | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 学生拥有多个学科的分层信息 | 用户分层信息 | 学生（用户中心） | N:1 | 用户分层信息.用户ID -> 用户中心.用户ID | 3.1 |
| 分层信息关联学校信息 | 用户分层信息 | 学校（用户中心） | N:1 | 用户分层信息.学校ID -> 用户中心.学校ID | 3.1 |
| 分层信息关联学科信息 | 用户分层信息 | 学科（内容平台） | N:1 | 用户分层信息.学科ID -> 内容平台.学科ID | 3.1 |
| 学生拥有多个知识点的掌握度 | 知识点掌握度 | 学生（用户中心） | N:1 | 知识点掌握度.用户ID -> 用户中心.用户ID | 3.2 |
| 掌握度关联知识点信息 | 知识点掌握度 | 知识点（内容平台） | N:1 | 知识点掌握度.知识点ID -> 内容平台.知识点ID | 3.2 |
| 学生拥有多个课程的外化掌握度 | 外化掌握度 | 学生（用户中心） | N:1 | 外化掌握度.用户ID -> 用户中心.用户ID | 3.3 |
| 外化掌握度关联课程信息 | 外化掌握度 | 课程（内容平台） | N:1 | 外化掌握度.课程ID -> 内容平台.课程ID | 3.3 |
| 学生产生多条答题记录 | 答题记录 | 学生（用户中心） | N:1 | 答题记录.用户ID -> 用户中心.用户ID | 场景2 |
| 答题记录关联题目信息 | 答题记录 | 题目（内容平台） | N:1 | 答题记录.题目ID -> 内容平台.题目ID | 场景2 |
| 答题记录关联知识点信息 | 答题记录 | 知识点（内容平台） | N:1 | 答题记录.知识点ID -> 内容平台.知识点ID | 场景2 |
| 答题记录关联课程信息 | 答题记录 | 课程（内容平台） | N:1 | 答题记录.课程ID -> 内容平台.课程ID | 场景2 |
| 知识点掌握度基于答题记录计算 | 知识点掌握度 | 答题记录 | 1:N | 答题记录.用户ID + 知识点ID -> 知识点掌握度.用户ID + 知识点ID | 3.2, 场景2 |
| 外化掌握度基于知识点掌握度计算 | 外化掌握度 | 知识点掌握度 | 1:N | 通过用户ID和课程-知识点关系计算 | 3.3 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 学科能力排名计算（高中学校水平校准） | 用户单科排名百分比, 校准系数a, 校准系数b | 学科能力排名 | 3.1, 公式：学科能力排名 = 用户单科排名百分比 × 校准系数a × 校准系数b |
| 用户分层等级确定 | 学科能力排名 | 分层等级（S+, S, A, B, C） | 3.1, 分层标准：S+[0,5]、S(5,10]、A(10,50]、B(50,80]、C(80,100] |
| 初始掌握度设置 | 学科初始值（0.3）, 分层掌握系数 | 初始掌握度 | 3.2, 公式：初始掌握度 = 学科初始值 × 分层掌握系数 |
| 单次答题掌握度增量（答对） | 题目难度系数, 用户当前掌握度 | 掌握度增量 | 3.2, 公式：增量 = 0.2 × (题目难度系数 - 用户当前掌握度) |
| 单次答题掌握度增量（答错） | 题目难度系数, 用户当前掌握度, 答错补偿系数 | 掌握度增量 | 3.2, 公式：增量 = -0.1 × (题目难度系数 - 用户当前掌握度) × 答错补偿系数 |
| 外化掌握度计算 | 知识点掌握度, 掌握度上限 | 外化掌握度 | 3.3, 公式：外化掌握度 = 知识点掌握度 / 掌握度上限 |
| 薄弱知识点判断 | 外化掌握度, 目标掌握度, 知识点掌握度 | 是否薄弱知识点标识 | 3.5, 条件：外化掌握度/目标掌握度 ≤ 0.7 且 知识点掌握度 ≤ 0.5 |
| 校准系数b计算 | 历年排名中位数倒数平均, 今年排名中位数倒数 | 校准系数b | 3.1, 公式：√（历年排名中位数倒数平均 / 今年排名中位数倒数） |
| 目标掌握度计算（V1.0暂不实现） | 目标难度系数, 单课难度系数, 考试频率 | 目标掌握度 | 3.4, 公式：目标掌握度 = 目标难度系数 × 单课难度系数 × 考试频率 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 用户分层信息.学科能力排名 | 学生考试成绩更新，学校信息变更 | 考试周期性更新 | 3.1, 场景1 |
| 用户分层信息.分层等级 | 学科能力排名重新计算后 | 跟随学科能力排名更新 | 3.1 |
| 知识点掌握度.当前掌握度 | 学生每次完成答题后 | 实时更新/每次答题 | 3.2, 场景2 |
| 知识点掌握度.累计答题次数 | 学生每次答题后 | 实时更新/每次答题 | 3.2, 场景2 |
| 外化掌握度.外化掌握度值 | 学生累计答题≥5题后，或课程完成时 | 满足条件时更新 | 3.3, 场景2 |
| 外化掌握度.是否薄弱知识点 | 外化掌握度更新后，重新判断薄弱知识点条件 | 跟随外化掌握度更新 | 3.5 |
| 答题记录 | 学生每次答题后 | 实时创建/每次答题 | 场景2 |
| 用户分层信息.校准系数a | 学校本科录取率数据更新 | 年度或学期更新 | 3.1, PRD未明确具体频次 |
| 用户分层信息.校准系数b | 学校历年排名数据更新 | 年度更新 | 3.1, PRD未明确具体频次 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 学生各科年级排名百分比 | 学校提供，或对接学情系统获取 | 3.1, 场景1 |
| 学校本科录取率 | 学校提供或教育部门公开数据 | 3.1, 校准系数a计算 |
| 学校历年排名数据 | 学校提供或教育统计数据 | 3.1, 校准系数b计算 |
| 学生基础信息 | 用户中心服务提供（学生姓名、学号、班级等） | 场景1, 用户中心服务 |
| 学校基础信息 | 用户中心服务提供（学校名称、地址、等级等） | 场景1, 用户中心服务 |
| 题目信息 |
| 外化掌握度.外化掌握度值 | 学生累计答题≥5题后，或课程完成时 | 满足条件时更新 | 3.3, 场景2 |
| 外化掌握度.是否薄弱知识点 | 外化掌握度更新后，重新判断薄弱知识点条件 | 跟随外化掌握度更新 | 3.5 |
| 答题记录 | 学生每次答题后 | 实时创建/每次答题 | 场景2 |
| 用户分层信息.校准系数a | 学校本科录取率数据更新 | 年度或学期更新 | 3.1, PRD未明确具体频次 |
| 用户分层信息.校准系数b | 学校历年排名数据更新 | 年度更新 | 3.1, PRD未明确具体频次 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 学生各科年级排名百分比 | 学校提供，或对接学情系统获取 | 3.1, 场景1 |
| 学校本科录取率 | 学校提供或教育部门公开数据 | 3.1, 校准系数a计算 |
| 学校历年排名数据 | 学校提供或教育统计数据 | 3.1, 校准系数b计算 |
| 学生基础信息 | 用户中心服务提供（学生姓名、学号、班级等） | 场景1, 用户中心服务 |
| 学校基础信息 | 用户中心服务提供（学校名称、地址、等级等） | 场景1, 用户中心服务 |
| 题目信息 | 内容平台服务提供（题目难度、年份、地区、题干、答案等） | 场景2, 内容平台服务 |
| 知识点信息 | 内容平台服务提供（业务树节点信息） | 场景2, 内容平台服务 |
| 课程信息 | 内容平台服务提供（业务树节点信息，教材数据） | 场景2, 内容平台服务 |
| 学生答题结果 | 学生在系统中答题产生，系统自动记录 | 场景2 |
| 目标院校信息 | 学生在系统中自行设置（V1.0暂不实现） | 3.4, 目标掌握度计算 |

---

## 7. 当前版本明确排除的数据需求 (例如PRD中的V1.0版本)

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 (源自PRD"非本期功能"等章节) | PRD中说明的排除原因 (若有) | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 知识点迁移性分析相关数据存储 | 考虑知识点间的关联性和迁移效应，建议V2.0再考虑 | 2.3 |
| 个性化学习路径推荐相关数据 | 基于掌握度的完整学习路径规划，建议后续迭代再考虑 | 2.3 |
| 目标掌握度相关数据实体 | 根据学习目标计算应达到的掌握度标准，优先级低，V1.0暂不实现 | 2.1, 3.4 |
| 知识点间关联关系数据 | V1.0不考虑知识点迁移性，简化模型 | 2.3 |
| 学习路径历史数据 | 完整学习路径规划功能暂不实现 | 2.3 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别用户分层信息、知识点掌握度、外化掌握度、答题记录、目标掌握度等核心实体 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏（例如，本示例中的"题目信息"、"课程信息"）？                                                         | ✅ | 题目信息、课程信息、学校信息等已明确为公共服务提供的引用实体 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已从计算公式中提取所有必需的输入属性和输出属性 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ⚠️ | 部分属性的数据类型基于业务逻辑推断，PRD中未明确指定具体数据类型 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确实体间的关联关系和外键指向 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 掌握度计算的依赖关系已在关系表中体现 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 已列出所有计算公式的输入项和输出项 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 已明确各数据的更新触发条件和采集来源 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 已列出V1.0版本排除的功能相关数据需求 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 实体和属性名称与PRD中的术语保持一致 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 所有提取项都标注了PRD章节号或公式引用 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 所有信息均来源于PRD原文，推断部分已标注 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 部分数据类型和约束条件基于常规业务模式推断，建议与PM确认具体技术实现细节 |

---

## 9. 待与PM确认的问题

**在数据提取过程中发现的需要进一步澄清的问题：**

1. **数据类型精确定义**：PRD中未明确指定具体的数据类型（如整数位数、小数精度等），建议确认技术实现标准。

2. **校准系数更新机制**：校准系数a和校准系数b的具体更新频次和触发条件需要进一步明确。

3. **历史数据保留策略**：答题记录、掌握度变化历史等数据的保留期限和归档策略需要确认。

4. **异常情况处理**：当计算结果超出预期范围时的处理机制（如掌握度计算异常、分层计算异常等）。

5. **并发更新控制**：多个答题记录同时更新同一知识点掌握度时的并发控制策略。

---

## 10. 补充说明

**公共服务依赖说明：**
- **用户中心服务**：提供学生信息、学校信息、教师信息等基础数据
- **内容平台服务**：提供题目信息、知识点信息、课程信息、业务树结构等内容数据
- **教师服务端**：提供教师查看数据的接口支持

**数据一致性要求：**
- 所有外键关联必须保证引用完整性
- 掌握度计算结果必须在合理范围内[0,1]
- 外化掌握度只增不降的业务规则必须在数据层面保证

**性能考虑：**
- 答题记录表预计数据量较大，需要考虑分表分库策略
- 掌握度计算的实时性要求，需要考虑缓存策略
- 教师查看班级数据的聚合查询性能优化

---
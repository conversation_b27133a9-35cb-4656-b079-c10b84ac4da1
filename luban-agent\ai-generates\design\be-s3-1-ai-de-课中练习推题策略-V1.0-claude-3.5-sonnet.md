# PRD 数据提取（数据库设计导向）

- **PRD文档名称：** 课中练习推题策略-V1.0_analyzed-gemini-pro
- **PRD版本号：** V1.0
- **提取日期：** 2025-05-25

---

## 1. 核心数据实体

*针对PRD中定义的，系统需要存储其信息的关键概念或"事物"（通常来源于"功能范围"、"术语表"或"核心功能"等章节）。*

| 实体名称           | PRD中的描述                                       | PRD中对应的章节号 |
| ------------------ | ------------------------------------------------- | ----------------- |
| 练习组件 | 在单节AI课中，用于组织和呈现一组练习题的功能模块 | 2.1.1, 11.2 |
| 题目 | 练习组件内的单个练习题，包含题干、选项、答案等信息 | 2.1.1, 11.2 |
| 相似题组 | 由一道母题和若干道围绕相同知识点或考点的变体题目构成的一组题目 | 2.1.1, 11.2 |
| 学生答题记录 | 记录学生对特定题目的作答情况，包括答案和正确性 | 2.1.1, 4, 5.1 |
| 课程 | 单节AI课程，包含多个练习组件 | 2.1.1, 5.2 |

---

## 2. 实体属性详情

*针对以上识别的每个实体，详细列出其属性特征。信息主要来源于PRD中的"计算规则"、"数据准备"以及定义了各类系数或参数的表格。*

**实体：`练习组件`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 练习组件ID | 练习组件的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 2.1.1 |
| 课程ID | 所属课程的标识符 | 整数 | 非空 |  | 外键 | 2.1.1 |
| 组件名称 | 练习组件的名称 | 字符串 | 非空 |  |  | 2.1.1 |
| 组件顺序 | 在课程中的位置顺序 | 整数 | 非空, 正整数 |  |  | 2.1.1 |

**实体：`题目`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 题目ID | 题目的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 2.1.1 |
| 练习组件ID | 所属练习组件的标识符 | 整数 | 非空 |  | 外键 | 2.1.1 |
| 题目类型 | 区分单题和相似题组 | 字符串 | 枚举值: "单题", "相似题组" |  |  | 2.1.1 |
| 题目顺序 | 在练习组件内的推荐顺序 | 整数 | 非空, 正整数 |  |  | 2.1.1 |
| 知识点ID | 关联的知识点标识符 | 整数 | 非空 |  | 外键 | 2.1.1 |
| 难度等级 | 题目难度标签 | 字符串 | 主要为L1、L2 |  |  | 2.1.1 |
| 相似题组ID | 如果是相似题组，则为题组标识符 | 整数 | 可空 |  | 外键 | 2.1.1 |

**实体：`相似题组`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 相似题组ID | 相似题组的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 2.1.1 |
| 题组名称 | 相似题组的名称 | 字符串 | 非空 |  |  | 2.1.1 |
| 知识点ID | 关联的知识点标识符 | 整数 | 非空 |  | 外键 | 2.1.1 |

**实体：`学生答题记录`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 答题记录ID | 答题记录的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 2.1.1 |
| 学生ID | 作答学生的标识符 | 整数 | 非空 |  | 外键 | 2.1.1 |
| 题目ID | 作答题目的标识符 | 整数 | 非空 |  | 外键 | 2.1.1 |
| 练习组件ID | 所属练习组件的标识符 | 整数 | 非空 |  | 外键 | 2.1.1 |
| 答题结果 | 学生答题的正确性 | 布尔值 | 非空 |  |  | 2.1.1 |
| 是否相似题 | 标识该题是否为错题推送的相似题 | 布尔值 | 非空 | false |  | 2.1.1 |
| 原错题ID | 如果是相似题，记录原错题的ID | 整数 | 可空 |  | 外键 | 2.1.1 |
| 答题时间 | 学生提交答案的时间戳 | 时间戳 | 非空 |  |  | 2.1.1 |

**实体：`课程`**

| 属性名称                   | PRD中的描述 / 用途                               | 数据类型 (基于PRD推断或明确说明) | 约束条件 / 业务规则 (例如：取值范围、特定值、非空、唯一性、格式等) | 初始值/默认值 (源自PRD) | 是否为标识符? (主键/外键组成部分) | PRD中对应的章节号 / 公式引用 |
| -------------------------- | ------------------------------------------------ | ------------------------------ | ----------------------------------------------------------------- | ----------------------- | --------------------------- | -------------------------- |
| 课程ID | 课程的唯一标识符 | 整数 | 唯一, 非空 |  | 主键 | 2.1.1 |
| 课程名称 | 课程的名称 | 字符串 | 非空 |  |  | 2.1.1 |

---

## 3. 实体间关系

*描述实体之间是如何关联的，信息通常来源于PRD的"内容结构与关联"章节，或通过计算依赖关系推断得出。*

| 关系描述 (例如：用户 '拥有一个' 学校档案) | 实体 A          | 实体 B            | 基数 (1:1, 1:N, N:M) | 外键 (位于哪个实体，基于哪个属性)   | PRD中对应的章节号 |
| --------------------------------------- | --------------- | ----------------- | -------------------- | --------------------------------- | ----------------- |
| 课程包含多个练习组件 | 课程 | 练习组件 | 1:N | 练习组件表.课程ID -> 课程表.课程ID | 2.1.1, 5.2 |
| 练习组件包含多个题目 | 练习组件 | 题目 | 1:N | 题目表.练习组件ID -> 练习组件表.练习组件ID | 2.1.1 |
| 相似题组包含多个题目 | 相似题组 | 题目 | 1:N | 题目表.相似题组ID -> 相似题组表.相似题组ID | 2.1.1 |
| 学生对题目进行作答 | 学生 | 题目 | N:M | 学生答题记录表.学生ID -> 学生表.学生ID, 学生答题记录表.题目ID -> 题目表.题目ID | 2.1.1, 4 |
| 答题记录关联练习组件 | 学生答题记录 | 练习组件 | N:1 | 学生答题记录表.练习组件ID -> 练习组件表.练习组件ID | 2.1.1 |
| 相似题答题记录关联原错题 | 学生答题记录 | 学生答题记录 | N:1 | 学生答题记录表.原错题ID -> 学生答题记录表.答题记录ID | 2.1.1 |

---

## 4. 核心计算逻辑的输入项

*列出PRD中定义的核心计算（如掌握度计算、用户分层等）所必需的数据属性。重点关注为了支持这些计算，数据库*必须存储*的那些输入项。*

| 计算/公式名称 (源自PRD)                                     | 必需的存储输入属性 (源自PRD公式)                                                                                             | 输出属性 (如果该输出也需存储) | PRD中对应的章节号 / 公式引用 |
| ----------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------- | ------------------------- | -------------------------- |
| 推题策略判断 (答对后继续下一题) | 学生答题记录.答题结果, 题目.题目顺序, 练习组件ID | 下一推荐题目ID | 2.1.1, 5.1 |
| 推题策略判断 (答错后推相似题) | 学生答题记录.答题结果, 题目.相似题组ID, 学生答题记录.是否相似题, 学生答题记录.题目ID | 推荐相似题目ID | 2.1.1, 5.1 |
| 相似题组随机选择 | 相似题组.相似题组ID, 题目.题目ID, 学生答题记录.题目ID (已作答过的题目) | 随机选中题目ID | 2.1.1, 4 |
| 练习组件完成度计算 | 学生答题记录.练习组件ID, 学生答题记录.学生ID, 题目.练习组件ID | 练习完成状态 | 2.1.1, 7.1 |

---

## 5. 数据更新的触发条件与频次

*根据PRD的"更新机制"或类似章节，明确指出导致数据发生变化的事件或条件，以及变化的频率。*

| 受影响的数据实体/属性           | 触发事件/条件 (源自PRD)                                                                         | 更新频次 (若PRD中已指明) | PRD中对应的章节号 |
| ----------------------------- | ----------------------------------------------------------------------------------------------- | ---------------------- | ----------------- |
| 学生答题记录 | 学生提交答案后 | 实时/每次答题 | 2.1.1, 4 |
| 推荐题目状态 | 学生完成当前题目作答后，系统根据答题结果和推题策略确定下一题 | 实时/每次答题完成 | 2.1.1, 5.1 |
| 练习组件进度 | 学生在练习组件内完成题目作答 | 实时/每次答题 | 2.1.1, 7.1 |
| 相似题推送记录 | 学生答错题目且存在未推送的相似题时 | 按需/答错时触发 | 2.1.1, 7.1.4 |

---

## 6. 数据采集需求 (数据来源)

*明确哪些数据项需要从外部采集，以及可能的采集方式，通常来源于PRD的"数据准备"等相关章节。*

| 数据属性名称         | PRD中描述的数据来源/采集方式 (例如：用户输入, 系统生成, 学校提供) | PRD中对应的章节号 |
| -------------------- | ----------------------------------------------------------- | ----------------- |
| 题目内容、难度、知识点关联 | 教研在后台配置时提供，来源于内容平台服务 | 2.1.1 |
| 练习组件配置、题目顺序 | 教研在配课环节通过后台管理界面配置 | 2.1.1 |
| 相似题关系配置 | 教研在后台配置题目时建立相似题关系 | 2.1.1 |
| 学生答题数据 | 学生在前端练习组件中的实时作答行为 | 2.1.1, 4 |
| 学生基本信息 | 来源于用户中心服务 | 2.1.1 |

---

## 7. 当前版本明确排除的数据需求 (例如PRD中的V1.0版本)

*列出PRD中明确指出在当前版本（如V1.0）不予考虑的，且与数据存储或数据结构相关的功能点。*

| 被排除的与数据相关的特性/功能 (源自PRD"非本期功能"等章节) | PRD中说明的排除原因 (若有) | PRD中对应的章节号 |
| ---------------------------------------------------- | ------------------------ | ----------------- |
| 错误严重程度分类数据存储 | V1.0不考虑区分错误类型，简化推题逻辑 | 2.2 |
| 个性化推题参数存储 (如连续答对阈值) | V1.0不考虑基于实时表现的动态调整 | 2.2 |
| 题目跳过记录和规则存储 | V1.0不支持基于表现跳过题目的功能 | 2.2 |

---

## 8. PRD数据提取完整性与准确性自查

**本章节用于在完成PRD数据提取后，对提取内容的全面性和准确性进行自查。**
- ✅表示通过；❌表示未通过；⚠️表示部分正确；N/A表示不适用或无法判断。每个检查点都需要填写。

| 检查维度                 | 检查项                                                                                                                                      | 自查结果 | 备注 (如有遗漏、疑问或需澄清项)                                      |
| ------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------- | --------------------- | --------------------------------------------------------------------- |
| **1. 核心实体识别** | PRD中描述的所有核心业务概念是否都已作为"核心数据实体"被识别和列出？                                                                                     | ✅ | 已识别练习组件、题目、相似题组、学生答题记录、课程等核心实体 |
|                          | 是否有任何PRD中暗示的、但未明确列出的重要数据承载对象被遗漏（例如，本示例中的"题目信息"、"课程信息"）？                                                         | ✅ | 已包含所有PRD中涉及的数据实体 |
| **2. 属性完整性** | 对于每个实体，是否已从PRD（特别是计算规则、数据准备等章节）中提取了所有相关的描述性属性、计算输入属性和状态属性？                                                     | ✅ | 已提取推题策略所需的所有关键属性 |
|                          | 是否有属性的描述、数据类型推断、约束条件或初始/默认值与PRD原文存在偏差或遗漏？                                                                               | ✅ | 属性定义与PRD描述一致 |
| **3. 关系清晰度** | 实体间的核心关系（包括基数）是否已根据PRD的描述或业务逻辑清晰提取？                                                                                             | ✅ | 已明确各实体间的包含、关联关系 |
|                          | 是否所有依赖关系（例如，某个实体的属性来源于另一个实体的计算结果）都已体现在关系描述或属性来源中？                                                                 | ✅ | 推题逻辑的依赖关系已体现 |
| **4. 计算逻辑输入** | 所有PRD中定义的关键计算公式，其必需的输入数据项是否都已在"核心计算逻辑的输入项"中列出，并能追溯到具体的实体属性？                                                       | ✅ | 推题策略的所有判断逻辑输入已列出 |
| **5. 更新与来源** | 数据的更新触发条件、频次以及主要的数据采集来源是否已根据PRD准确记录？                                                                                             | ✅ | 已记录答题触发的实时更新机制 |
| **6. 版本范围** | PRD中明确排除在当前版本之外的、与数据相关的需求是否已在"当前版本明确排除的数据需求"中正确反映？                                                                          | ✅ | 已记录V1.0排除的高级推题功能 |
| **7. 术语一致性** | 模板中使用的实体和属性名称是否与PRD术语表（若有）或PRD正文中的常用名称保持一致或有清晰映射？                                                                              | ✅ | 使用PRD中的标准术语 |
| **8. 可追溯性** | 模板中每个提取项（特别是属性、关系、计算输入）是否都标注了其在PRD中的来源章节或具体描述位置，以便核对？                                                                   | ✅ | 所有项目都标注了PRD章节号 |
| **9. 无主观臆断** | 是否所有提取的信息均直接来源于PRD原文或基于原文的合理推断（并已注明"推断"），没有加入提取者主观添加或修改的需求？                                                               | ✅ | 所有信息均来源于PRD原文 |
| **10. 待澄清点记录** | 在提取过程中，遇到的任何PRD描述模糊、信息不一致或缺失的关键数据点，是否已在备注中标记，或有待后续与产品经理澄清？                                                                | ⚠️ | 题目内容的具体存储结构需与内容平台服务对接确认 |

--- 
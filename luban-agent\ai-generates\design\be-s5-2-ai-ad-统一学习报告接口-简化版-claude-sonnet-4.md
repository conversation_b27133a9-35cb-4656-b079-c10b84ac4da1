# 接口使用文档 - 统一学习报告接口（简化版） - V1.0

* **最后更新日期**: 2025-06-06
* **设计负责人**: claude-sonnet-4
* **评审人**: 待填写
* **状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-06-06 | claude-sonnet-4 | 简化版设计，合并AI课中和巩固练习的学习报告接口，支持多种学习场景 |

## 1. 服务概述与设计目标

### 1.1 服务背景与范围
本服务提供统一的学习报告接口，支持学生在多种学习场景下的报告查看需求。涵盖老师布置的课程任务、课后作业以及学生自学等全场景的学习报告管理。

服务范围涵盖：
- **课程任务报告**：老师布置的AI课、巩固练习、拓展练习等课程学习任务完成后的报告
- **作业任务报告**：老师布置的课后作业完成后的学习报告
- **自学报告**：学生自主选择章节进行AI课、巩固练习、拓展练习等学习活动的报告
- **任务列表管理**：支持学生查看所有学习任务（包括自学）的列表和状态
- **历史报告查询**：支持按时间范围筛选和查看历史学习报告

### 1.2 设计目标
1. 统一不同学习场景的报告接口，提供一致的调用体验
2. 支持多种报告类型和查询场景，通过参数控制返回内容
3. 提供丰富的学习数据：掌握度变化、学习统计、答题详情、时长分析等
4. 支持个性化反馈：IP角色反馈、再练推荐、学习建议
5. 确保高性能：P95响应时间<2秒，支持1000并发用户
6. 遵循团队API设计规范，所有参数通过query param传递

## 2. 通用设计规范与约定

### 2.1 数据格式
- 请求体 (Request Body): `application/json`
- 响应体 (Response Body): `application/json`
- 日期时间格式: ISO 8601 (`YYYY-MM-DDTHH:mm:ss.sssZ`)

### 2.2 认证与授权
- 采用Bearer Token (JWT)认证机制
- 所有接口在请求头中传递 `Authorization: Bearer <token>`
- Token包含用户ID、权限角色等信息，确保学生只能访问自己的学习报告

### 2.3 版本控制
- 通过 URL 路径进行版本控制，如 `/api/v1/...`
- 当前版本为 v1，后续版本迭代遵循语义化版本控制

### 2.4 错误处理约定
遵循统一的错误码和错误响应格式。

**通用错误响应体示例**:
```json
{
  "code": 0,                // 业务错误码，0表示成功，非0表示错误
  "message": "错误信息",     // 用户可读的错误信息
  "detail": "错误详情,可选",  // 错误的详细信息，可选
  "data": "返回数据",        // 可选，返回的数据，错误时通常为空
  "pageInfo": "分页信息,可选"  // 可选，分页信息，错误时通常为空
}
```

## 3. 接口列表

| 接口路径            | 请求方式 | 接口名称         | 接口描述               |
| -------------------| -------- | -------- | ---------------- |
| /api/v1/study_reports | GET | 获取学习报告列表 | 获取学习报告列表，支持历史报告查询和筛选 |
| /api/v1/study_reports/detail | GET | 获取学习报告详情 | 获取单个学习报告的详细内容 |
| /api/v1/study_tasks | GET | 获取学习任务列表 | 获取学生的所有学习任务（包括课程任务、作业任务、自学任务） |

## 4. 场景说明

### 4.1 老师布置课程学习任务场景

#### 4.1.1 课程任务学习流程
1. 老师在教师端布置课程学习任务（AI课、巩固练习、拓展练习等）
2. 学生收到任务通知，进入学习任务页面
3. 学生点击开始学习，系统创建学习会话并记录任务关联
4. 学生在学习过程中提交作答信息，系统实时记录学习进度
5. 学生完成学习任务，系统自动结束会话并生成学习报告
6. 前端调用学习报告接口，获取包含任务完成情况的报告

#### 4.1.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant TaskService as 任务服务
    participant ReportService as 学习报告服务

    Student->>Frontend: 查看学习任务
    Frontend->>TaskService: GET /api/v1/study_tasks?task_type=course_task
    TaskService-->>Frontend: 返回课程任务列表
    Frontend-->>Student: 展示任务列表

    Student->>Frontend: 点击开始学习任务
    Note over Student,ReportService: 学习过程...
    
    Student->>Frontend: 完成学习任务
    Frontend->>ReportService: GET /api/v1/study_reports/detail?task_id=xxx
    ReportService-->>Frontend: 返回任务学习报告
    Frontend-->>Student: 展示任务完成报告
```

### 4.2 学生查看任务列表和历史报告场景

#### 4.2.1 任务列表和历史查看流程
1. 学生进入学习中心或任务页面
2. 前端调用学习任务列表接口，获取所有任务
3. 学生可以按任务类型、完成状态、时间范围进行筛选
4. 学生点击某个任务，查看任务详情或学习报告
5. 学生也可以查看历史学习报告列表，支持时间范围筛选

#### 4.2.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant Frontend as 前端
    participant TaskService as 任务服务
    participant ReportService as 学习报告服务

    Student->>Frontend: 进入学习任务页面
    Frontend->>TaskService: GET /api/v1/study_tasks?date_range=month&status=all
    TaskService-->>Frontend: 返回任务列表
    Frontend-->>Student: 展示任务列表

    Student->>Frontend: 点击查看某个任务报告
    Frontend->>ReportService: GET /api/v1/study_reports/detail?task_id=xxx
    ReportService-->>Frontend: 返回完整任务报告
    Frontend-->>Student: 展示任务学习报告详情

    Student->>Frontend: 查看历史报告列表
    Frontend->>ReportService: GET /api/v1/study_reports?date_range=month
    ReportService-->>Frontend: 返回历史报告列表
    Frontend-->>Student: 展示历史报告列表
```

## 5. 接口详细说明

### 5.1 获取学习报告列表接口

**接口路径**: `GET /api/v1/study_reports`

**接口描述**: 获取学习报告列表，支持历史报告查询和多维度筛选

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| student_id | string | 否 | 学生ID，从Token中获取，用于权限验证 | "student_789" |
| report_type | string | 否 | 报告类型筛选：course_study(课中学习)、exercise_practice(巩固练习)、course_task(课程任务)、homework_task(作业任务)、self_study(自学) | "course_task" |
| course_id | string | 否 | 课程ID筛选 | "course_101" |
| task_id | string | 否 | 任务ID筛选 | "task_789" |
| start_date | string | 否 | 开始日期筛选（按完成时间） | "2025-06-01" |
| end_date | string | 否 | 结束日期筛选（按完成时间） | "2025-06-06" |
| date_range | string | 否 | 预设时间范围：today(今天)、week(最近一周)、month(最近一月)、quarter(最近三月) | "month" |
| status | string | 否 | 完成状态筛选：completed(已完成)、all(全部) | "completed" |
| page | integer | 否 | 页码，默认1 | 1 |
| page_size | integer | 否 | 每页数量，默认20 | 20 |
| sort_by | string | 否 | 排序字段：generated_at(生成时间)、completion_time(完成时间)、mastery_change(掌握度变化) | "completion_time" |
| sort_type | string | 否 | 排序方式，默认desc | "desc" |

**响应数据结构**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": [
    {
      "report_id": "report_123456",
      "report_type": "course_task",
      "task_id": "task_789",
      "task_name": "AI基础概念学习",
      "course_id": "course_101",
      "course_name": "AI基础课程",
      "generated_at": "2025-06-06T10:30:00.000Z",
      "completion_time": "2025-06-06T10:30:00.000Z",
      "study_duration": 1800,
      "mastery_change": 12.7,
      "accuracy_rate": 75.0,
      "summary": "完成AI基础课程学习，掌握度提升12.7%"
    }
  ],
  "pageInfo": {
    "current_page": 1,
    "page_size": 20,
    "total_count": 45,
    "total_pages": 3
  }
}
```

### 5.2 获取学习报告详情接口

**接口路径**: `GET /api/v1/study_reports/detail`

**接口描述**: 获取单个学习报告的详细内容

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| report_id | string | 否 | 报告ID，用于查看历史报告详情 | "report_456" |
| session_id | string | 否 | 学习会话ID，用于获取特定会话的报告 | "session_123456" |
| task_id | string | 否 | 任务ID，用于获取特定任务的报告 | "task_789" |
| student_id | string | 否 | 学生ID，从Token中获取，用于权限验证 | "student_789" |

**响应数据结构**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "report_id": "report_123456",
    "report_type": "course_task",
    "session_id": "session_123456",
    "task_id": "task_789",
    "student_id": "student_789",
    "course_id": "course_101",
    "course_name": "AI基础课程",
    "task_name": "AI基础概念学习",
    "task_type": "course_task",
    "deadline": "2025-06-10T23:59:59.000Z",
    "completion_time": "2025-06-06T10:30:00.000Z",
    "generated_at": "2025-06-06T10:30:00.000Z",
    "study_statistics": {
      "total_duration": 1800,
      "study_start_time": "2025-06-06T09:00:00.000Z",
      "study_end_time": "2025-06-06T09:30:00.000Z",
      "component_count": 5,
      "completed_components": 5,
      "interaction_count": 23,
      "mode_switches": 3
    },
    "mastery_analysis": {
      "previous_mastery": 65.5,
      "current_mastery": 78.2,
      "mastery_change": 12.7,
      "mastery_level": "良好",
      "knowledge_points": [
        {
          "point_name": "AI基础概念",
          "previous_score": 60.0,
          "current_score": 75.0,
          "improvement": 15.0
        }
      ]
    },
    "performance_details": {
      "total_questions": 8,
      "correct_answers": 6,
      "accuracy_rate": 75.0,
      "average_answer_time": 45.5,
      "question_details": [
        {
          "question_id": "q_001",
          "is_correct": true,
          "answer_time": 42,
          "difficulty": "medium"
        }
      ]
    },
    "ip_feedback": {
      "feedback_type": "celebration",
      "feedback_message": "太棒了！你在AI基础概念方面有了很大进步！",
      "feedback_animation": "celebration_star",
      "encouragement": "继续保持这样的学习状态，你会越来越棒的！"
    },
    "recommendations": {
      "next_actions": [
        {
          "action_type": "continue_course",
          "title": "继续下一课程",
          "description": "你已经掌握了基础概念，可以进入进阶课程了"
        }
      ],
      "practice_suggestions": [
        {
          "suggestion_type": "review_weak_points",
          "content": "建议复习一下机器学习基础概念"
        }
      ]
    }
  }
}
```

### 5.3 获取学习任务列表接口

**接口路径**: `GET /api/v1/study_tasks`

**接口描述**: 获取学生的所有学习任务列表，包括课程任务、作业任务、自学记录

**请求参数**:
| 参数名 | 类型 | 必填 | 描述 | 示例值 |
|--------|------|------|------|--------|
| student_id | string | 是 | 学生ID，从Token中获取 | "student_789" |
| task_type | string | 否 | 任务类型筛选：course_task(课程任务)、homework_task(作业任务)、self_study(自学) | "course_task" |
| status | string | 否 | 完成状态筛选：pending(待完成)、completed(已完成)、overdue(已逾期) | "completed" |
| start_date | string | 否 | 开始日期筛选 | "2025-06-01" |
| end_date | string | 否 | 结束日期筛选 | "2025-06-06" |
| date_range | string | 否 | 预设时间范围：today(今天)、week(最近一周)、month(最近一月)、quarter(最近三月) | "month" |
| course_id | string | 否 | 课程ID筛选 | "course_101" |
| page | integer | 否 | 页码，默认1 | 1 |
| page_size | integer | 否 | 每页数量，默认20 | 20 |
| sort_by | string | 否 | 排序字段：created_at(创建时间)、deadline(截止时间)、completion_time(完成时间) | "deadline" |
| sort_type | string | 否 | 排序方式，默认desc | "asc" |

**响应数据结构**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": [
    {
      "task_id": "task_123456",
      "task_name": "AI基础概念学习",
      "task_type": "course_task",
      "course_id": "course_101",
      "course_name": "AI基础课程",
      "chapter_name": "第一章 AI概述",
      "created_at": "2025-06-01T09:00:00.000Z",
      "deadline": "2025-06-10T23:59:59.000Z",
      "status": "completed",
      "completion_time": "2025-06-06T10:30:00.000Z",
      "progress": 100,
      "estimated_duration": 1800,
      "actual_duration": 1650,
      "mastery_improvement": 12.7,
      "accuracy_rate": 85.0
    },
    {
      "task_id": "task_789012",
      "task_name": "机器学习基础作业",
      "task_type": "homework_task",
      "course_id": "course_102",
      "course_name": "机器学习入门",
      "created_at": "2025-06-03T14:00:00.000Z",
      "deadline": "2025-06-08T23:59:59.000Z",
      "status": "pending",
      "completion_time": null,
      "progress": 60,
      "estimated_duration": 3600,
      "actual_duration": 2100,
      "mastery_improvement": null,
      "accuracy_rate": null
    },
    {
      "task_id": "self_345678",
      "task_name": "深度学习自学",
      "task_type": "self_study",
      "course_id": "course_103",
      "course_name": "深度学习进阶",
      "chapter_name": "第三章 神经网络",
      "created_at": "2025-06-05T16:30:00.000Z",
      "deadline": null,
      "status": "completed",
      "completion_time": "2025-06-05T18:00:00.000Z",
      "progress": 100,
      "estimated_duration": null,
      "actual_duration": 5400,
      "mastery_improvement": 8.3,
      "accuracy_rate": 92.0
    }
  ],
  "pageInfo": {
    "current_page": 1,
    "page_size": 20,
    "total_count": 25,
    "total_pages": 2
  }
}
```

## 6. 检查清单

### 6.1 PRD核心需求场景与API覆盖性检查

| PRD核心需求点/用户场景 (ID) | 涉及的主要API接口 (路径与方法) | 关键输入数据实体/参数 (示例) | 关键输出数据实体/参数 (示例) | 场景支持度(✅/⚠️/❌) | 数据流正确性(✅/⚠️/❌) | 备注/待办事项 |
| :------ | :------ | :------ | :------ | :------ | :------ | :------ |
| 老师布置课程学习任务 | `1. GET /api/v1/study_tasks` <br/> `2. GET /api/v1/study_reports` | `1. task_type=course_task` <br/> `2. query_type=task_report&task_id=xxx` | `1. 课程任务列表, 任务状态` <br/> `2. 任务学习报告, 完成情况` | ✅ | ✅ | 支持课程任务管理和报告生成，包含截止时间和完成状态 |
| 老师布置课后作业 | `1. GET /api/v1/study_tasks` <br/> `2. GET /api/v1/study_reports` | `1. task_type=homework_task` <br/> `2. query_type=task_report&task_id=xxx` | `1. 作业任务列表, 截止时间` <br/> `2. 作业完成报告, 提交状态` | ✅ | ✅ | 支持作业任务管理，包含截止时间控制和提交状态跟踪 |
| 学生自学场景 | `1. GET /api/v1/study_tasks` <br/> `2. GET /api/v1/study_reports` | `1. task_type=self_study` <br/> `2. query_type=single&session_id=xxx` | `1. 自学记录列表, 学习进度` <br/> `2. 自学成果报告, 掌握度提升` | ✅ | ✅ | 支持自学记录管理，无截止时间限制，记录学习历史 |
| 学生查看任务列表 | `GET /api/v1/study_tasks` | `筛选条件(任务类型、状态、时间范围)` | `任务列表(任务名、截止时间、完成时间、完成状态)` | ✅ | ✅ | 支持多维度筛选，包含时间范围筛选和任务状态管理 |
| 学生查看任务报告详情 | `GET /api/v1/study_reports` | `query_type=task_report&task_id=xxx` | `完整任务报告(任务信息、学习统计、掌握度分析)` | ✅ | ✅ | 提供任务维度的完整学习报告，包含任务评价和推荐 |
| AI课中学习完成后获取学习报告 | `GET /api/v1/study_reports` | `query_type=single&session_id=xxx&report_type=course_study` | `StudyReport (performance, mastery, ip_feedback)` | ✅ | ✅ | 统一接口支持课中学习报告，包含IP反馈和庆祝动效 |
| 巩固练习完成后获取学习报告 | `GET /api/v1/study_reports` | `query_type=single&session_id=xxx&report_type=exercise_practice` | `LearningReport (mastery_change, accuracy_rate, recommendations)` | ✅ | ✅ | 统一接口支持巩固练习报告，包含再练推荐 |
| 查看历史学习报告（支持时间范围筛选） | `GET /api/v1/study_reports` | `query_type=history&date_range=month&筛选条件` | `历史报告列表, 分页信息` | ✅ | ✅ | 支持多维度筛选和时间范围筛选，包含预设时间范围选项 |

### 6.2 API接口数据实体与数据库支持检查

| API接口 (路径与方法) | 操作类型 (CRUD) | 主要数据实体 (模型名称) | 关键数据属性 (字段名示例) | 依赖的数据库表/视图 (示例) | 数据库操作 (读/写/更新/删) | 数据可获得性/可行性(✅/⚠️/❌) | 数据一致性保障 (简述策略) | 备注/待办事项 |
| :------ | :------ | :------ | :------ | :------ | :------ | :------ | :------ | :------ |
| `GET /api/v1/study_reports` | Read/Create | `LearningReport, StudySession, ExerciseSession, LearningTask` | `report_id, session_id, task_id, study_statistics, mastery_analysis, performance_details` | `tbl_learning_reports`, `tbl_study_sessions`, `tbl_exercise_sessions`, `tbl_learning_tasks` | 读/写 | ✅ | `报告生成和查看合并，缓存策略优化，任务关联` | `支持多种查询类型，统一数据模型` |
| `GET /api/v1/study_tasks` | Read | `LearningTask` | `task_id, task_name, task_type, course_id, deadline, status, completion_time, progress` | `tbl_learning_tasks`, `tbl_task_assignments` | 读 | ✅ | `读取一致性，分页优化，多维度筛选索引` | `支持任务列表查询，包含时间范围和状态筛选` |

### 6.3 API通用设计规范符合性检查

| 检查项 | 状态 (✅/⚠️/❌/N/A) | 备注/说明 |
| :------ | :------ | :------ |
| 1. 命名规范 | ✅ | 使用清晰的接口路径，参数命名一致 |
| 2. HTTP方法 | ✅ | GET用于查询，符合规范 |
| 3. 状态码 | ✅ | 200成功，400参数错误，401认证失败，404资源不存在，500服务器错误 |
| 4. 错误处理 | ✅ | 统一错误响应格式，包含业务错误码、用户友好信息和详细错误信息 |
| 5. 认证与授权 | ✅ | 所有接口都需要JWT Token认证，确保学生只能访问自己的数据 |
| 6. 数据格式与校验 | ✅ | 使用JSON格式，包含必填项、长度限制、格式限制等校验规则 |
| 7. 分页与排序 | ✅ | 支持分页参数，默认分页参数合理 |
| 8. 幂等性 | ✅ | GET操作天然幂等，报告生成具有幂等性设计 |
| 9. 安全性 | ✅ | 参数校验和转义，敏感信息加密，JWT Token认证保护 |
| 10. 性能考量 | ✅ | P95响应时间<2秒，支持1000并发，缓存机制，分页查询优化 |
| 11. 文档一致性 | ✅ | 基于原有接口分析文档合并设计，保持一致性 |
| 12. 可测试性 | ✅ | 接口设计清晰，依赖明确，便于各层级测试 |
| 13. 向后兼容性 | N/A (新设计) | 对于v1版本可标记为N/A，但后续版本迭代需重点关注 |
| 14. 路径参数规范 | ✅ | 所有参数都通过query param传递，路径中不包含可变参数值 |

## 7. 接口合并说明

### 7.1 原有接口分析
**AI课中学习报告接口** (`/api/v1/study/report`):
- 主要用于课程框架+文档组件学习完成后的报告展示
- 包含学习时长、交互记录、掌握度变化等数据
- 重点关注课程学习过程的统计和反馈

**巩固练习报告接口** (`/api/v1/study_reports`):
- 主要用于巩固练习完成后的学习成果报告
- 包含作答统计、正确率、掌握度变化、再练推荐等
- 重点关注练习效果和知识巩固情况

### 7.2 简化设计原则
1. **接口数量最小化**: 只保留2个核心接口，通过参数控制不同功能
2. **参数规范化**: 所有参数都通过query param传递，路径中不包含可变参数值
3. **功能合并**: 将历史报告、任务报告、单个报告查询合并到一个接口中
4. **统一响应格式**: 采用一致的响应数据结构，便于前端统一处理

### 7.3 合并优势
1. **减少接口维护成本**: 最小化接口数量，降
| 2. HTTP方法 | ✅ | GET用于查询，符合规范 |
| 3. 状态码 | ✅ | 200成功，400参数错误，401认证失败，404资源不存在，500服务器错误 |
| 4. 错误处理 | ✅ | 统一错误响应格式，包含业务错误码、用户友好信息和详细错误信息 |
| 5. 认证与授权 | ✅ | 所有接口都需要JWT Token认证，确保学生只能访问自己的数据 |
| 6. 数据格式与校验 | ✅ | 使用JSON格式，包含必填项、长度限制、格式限制等校验规则 |
| 7. 分页与排序 | ✅ | 支持分页参数，默认分页参数合理 |
| 8. 幂等性 | ✅ | GET操作天然幂等，报告生成具有幂等性设计 |
| 9. 安全性 | ✅ | 参数校验和转义，敏感信息加密，JWT Token认证保护 |
| 10. 性能考量 | ✅ | P95响应时间<2秒，支持1000并发，缓存机制，分页查询优化 |
| 11. 文档一致性 | ✅ | 基于原有接口分析文档合并设计，保持一致性 |
| 12. 可测试性 | ✅ | 接口设计清晰，依赖明确，便于各层级测试 |
| 13. 向后兼容性 | N/A (新设计) | 对于v1版本可标记为N/A，但后续版本迭代需重点关注 |
| 14. 路径参数规范 | ✅ | 所有参数都通过query param传递，路径中不包含可变参数值 |

## 7. 接口合并说明

### 7.1 原有接口分析
**AI课中学习报告接口** (`/api/v1/study/report`):
- 主要用于课程框架+文档组件学习完成后的报告展示
- 包含学习时长、交互记录、掌握度变化等数据
- 重点关注课程学习过程的统计和反馈

**巩固练习报告接口** (`/api/v1/study_reports`):
- 主要用于巩固练习完成后的学习成果报告
- 包含作答统计、正确率、掌握度变化、再练推荐等
- 重点关注练习效果和知识巩固情况

### 7.2 简化设计原则
1. **接口数量最小化**: 只保留2个核心接口，通过参数控制不同功能
2. **参数规范化**: 所有参数都通过query param传递，路径中不包含可变参数值
3. **功能合并**: 将历史报告、任务报告、单个报告查询合并到一个接口中
4. **统一响应格式**: 采用一致的响应数据结构，便于前端统一处理

### 7.3 合并优势
1. **减少接口维护成本**: 最小化接口数量，降低开发和维护复杂度
2. **提升用户体验**: 一致的报告展示风格，用户学习体验更加统一
3. **便于功能扩展**: 统一的数据模型便于后续新增报告类型和功能
4. **优化性能**: 统一的缓存策略和数据聚合逻辑，提升系统性能
5. **规范化设计**: 遵循团队API规范，所有参数通过query param传递

### 7.4 迁移建议
1. **渐进式迁移**: 新接口上线后，原有接口保持一段时间兼容期
2. **数据迁移**: 将原有报告数据迁移到新的统一数据模型中
3. **前端适配**: 前端逐步切换到新接口，统一报告展示组件
4. **监控验证**: 密切监控新接口性能和数据准确性

## 8. 附录 (Appendix)

### 8.1 引用文档
- **AI课中学习服务文档**: `be-s4-2-ai-ad-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md`
- **巩固练习服务文档**: `be-s5-2-ai-ad-学生端-巩固练习-claude-sonnet-4.md`
- **API设计指令文档**: `ai-templates/prompts/be-design/be-s5-2-gen-ai-ad.md`

### 8.2 参考标准
- 团队API设计规范
- JWT Token 认证标准
- ISO 8601 日期时间格式标准

### 8.3 版本说明
本文档基于以下原有接口设计合并而成：
- AI课中学习报告接口 `/api/v1/study/report` (来源: AI课中-课程框架+文档组件V0.9)
- 巩固练习报告接口 `/api/v1/study_reports` (来源: 学生端-巩固练习)

通过简化接口设计，为学生提供一致的学习报告体验，支持多种学习场景下的报告查看需求。

### 8.4 简化设计特点
1. **接口数量最小化**: 仅保留2个核心接口，通过参数控制不同功能
2. **参数规范化**: 严格遵循团队规范，所有参数通过query param传递
3. **功能集中化**: 一个接口支持多种查询场景，减少接口碎片化
4. **易于维护**: 统一的数据模型和响应格式，降低维护成本

--- API使用文档产出完毕，等待您的命令，指挥官
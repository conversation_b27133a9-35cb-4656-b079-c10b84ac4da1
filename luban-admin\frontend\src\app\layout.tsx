import type { Metadata } from 'next';
import '@/styles/globals.css';
import { ChakraProvider } from '@chakra-ui/react';

// 使用系统字体，避免网络依赖
const systemFont = 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif';

export const metadata: Metadata = {
  title: '银河研发鲁班 Code-Agent - 智能代码分析与文档生成工具',
  description: '鲁班 Code-Agent 是一款基于AI的智能代码分析与文档生成工具，提升开发效率，简化工作流程。',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="zh-CN">
      <body style={{ fontFamily: systemFont }}>
        <ChakraProvider>
          {children}
        </ChakraProvider>
      </body>
    </html>
  );
} 
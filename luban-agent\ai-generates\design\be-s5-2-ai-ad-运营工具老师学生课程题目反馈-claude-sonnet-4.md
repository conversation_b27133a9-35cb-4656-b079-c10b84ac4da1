# 接口使用文档 - 运营工具老师学生课程题目反馈系统 - V1.0

* **最后更新日期**: 2025-05-30
* **设计负责人**: AI API使用文档撰写专家
* **评审人**: 待填写
* **状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-05-30 | AI API使用文档撰写专家 | 初始草稿     |

## 1. 服务概述与设计目标

### 1.1 服务背景与范围
本服务是一个智能化的教育内容质量反馈平台，核心目标是建立完整的课程内容质量反馈闭环，通过用户反馈驱动课程质量提升。系统支持教师端和学生端用户快速提交课程问题，通过智能Agent自动分类审核，生产后台高效处理，并及时反馈处理结果给用户，同时提供积分激励机制提升用户参与积极性。

### 1.2 设计目标回顾
1. 提供完整的反馈收集、审核、处理、通知闭环接口
2. 支持多端反馈提交（教师端、学生端）和多选反馈类型
3. 集成智能Agent审核能力，提升处理效率
4. 提供生产后台管理功能，支持高效反馈处理
5. 实现积分激励机制，提升用户参与积极性

## 2. 通用设计规范与约定

### 2.1 数据格式
* 请求体 (Request Body): `application/json`
* 响应体 (Response Body): `application/json`
* 日期时间格式: ISO 8601 (`YYYY-MM-DDTHH:mm:ss.sssZ`)

### 2.2 认证与授权
* 所有接口默认需要JWT认证，在请求头中传递 `Authorization: Bearer <token>`
* 基于角色的访问控制(RBAC)，区分教师、学生、生产人员、运营人员权限
* 敏感操作需要更高级别权限验证

### 2.3 版本控制
* 通过 URL 路径进行版本控制，如 `/api/v1/...`

### 2.4 错误处理约定
* **通用错误响应体示例**:
  ```json
  {
    "code": 0,                // 业务错误码，0表示成功，非0表示错误
    "message": "错误信息",     // 用户可读的错误信息
    "detail": "错误详情,可选",  // 错误的详细信息，可选
    "data": "返回数据",        // 可选，返回的数据，错误时通常为空
    "pageInfo": "分页信息,可选"  // 可选，分页信息，错误时通常为空
  }
  ```
* **常用 HTTP 状态码**:
  * `200 OK`: 请求成功
  * `400 Bad Request`: 请求无效 (例如参数错误、格式错误)
  * `401 Unauthorized`: 未认证或认证失败
  * `403 Forbidden`: 已认证，但无权限访问
  * `404 Not Found`: 请求的资源不存在
  * `500 Internal Server Error`: 服务器内部错误

### 2.6 分页与排序约定
* **分页参数**: 使用 page 和 pageSize 查询参数
* **排序参数**: 使用 sort_by 和 order 参数

## 3. 接口列表

| 接口路径            | 请求方式 | 接口名称         | 接口描述               |
| -------------------| -------- | -------- | ---------------- |
| /api/v1/feedback/types | GET | 反馈类型配置查询 | 获取反馈类型列表配置 |
| /api/v1/feedback/records | POST | 反馈记录提交 | 提交用户反馈记录 |
| /api/v1/feedback/screenshots | POST | 反馈截图上传 | 上传反馈相关截图 |
| /api/v1/agent/audit | POST | Agent智能审核 | 触发智能审核流程 |
| /api/v1/backoffice/feedback | GET | 生产后台反馈管理 | 查询和管理反馈列表 |
| /api/v1/backoffice/feedback/{feedbackId}/processing | POST/PUT | 反馈处理操作 | 创建和更新处理记录 |
| /api/v1/backoffice/feishu/sync | POST | 飞书多维表格同步 | 同步数据到飞书平台 |
| /api/v1/notifications/messages | GET/PUT | 用户消息管理 | 查询和管理用户消息 |
| /api/v1/notifications/send | POST | 处理结果通知发送 | 发送处理结果通知 |
| /api/v1/points | GET/POST | 积分管理 | 积分查询、下发和管理 |
| /api/v1/statistics | GET | 统计数据查询 | 查询各类统计分析数据 |

## 4. 场景说明

### 4.1 教师反馈提交场景

#### 4.1.1 教师反馈提交流程
1. 教师在AI课预览过程中发现问题（板书错误、字幕问题、配音问题等）
2. 点击悬浮的反馈按钮，调用反馈类型配置接口获取可选类型
3. 选择问题类型（支持多选）并填写问题描述
4. 系统自动截图或用户上传截图
5. 提交反馈记录，系统返回确认信息
6. 系统自动触发Agent智能审核流程

#### 4.1.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Teacher as 教师
    participant TeacherApp as 教师端应用
    participant FeedbackAPI as 反馈管理API
    participant UserAPI as 用户中心API
    participant ContentAPI as 内容平台API
    participant AgentAPI as 智能审核API

    Teacher->>TeacherApp: 发现课程问题，点击反馈按钮
    TeacherApp->>FeedbackAPI: GET /api/v1/feedback/types?user_type=teacher
    FeedbackAPI->>TeacherApp: 返回教师端反馈类型列表
    
    Teacher->>TeacherApp: 选择反馈类型（多选）并填写描述
    TeacherApp->>FeedbackAPI: POST /api/v1/feedback/screenshots
    FeedbackAPI->>TeacherApp: 返回截图上传结果
    
    TeacherApp->>UserAPI: GET /api/v1/users/profile
    UserAPI->>TeacherApp: 返回用户身份验证信息
    
    TeacherApp->>ContentAPI: GET /api/v1/courses/{courseId}/basic
    ContentAPI->>TeacherApp: 返回课程基本信息
    
    TeacherApp->>FeedbackAPI: POST /api/v1/feedback/records
    FeedbackAPI->>TeacherApp: 返回反馈提交成功，反馈ID
    
    FeedbackAPI->>AgentAPI: POST /api/v1/agent/audit
    AgentAPI->>FeedbackAPI: 触发智能审核流程
    
    TeacherApp->>Teacher: 显示"感谢您的反馈，我们会尽快处理！"
```

### 4.2 学生题目反馈场景

#### 4.2.1 学生题目反馈流程
1. 学生在答题过程中发现题目问题（题干错误、答案错误、解析错误等）
2. 点击题目页面的反馈按钮，获取学生端反馈类型配置
3. 选择题目问题类型并描述具体问题
4. 系统自动截图并收集题目相关信息
5. 提交反馈并收到确认提示
6. 系统触发Agent审核流程

#### 4.2.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Student as 学生
    participant StudentApp as 学生端应用
    participant FeedbackAPI as 反馈管理API
    participant ContentAPI as 内容平台API
    participant AgentAPI as 智能审核API

    Student->>StudentApp: 答题中发现题目错误
    StudentApp->>FeedbackAPI: GET /api/v1/feedback/types?user_type=student
    FeedbackAPI->>StudentApp: 返回学生端反馈类型列表
    
    Student->>StudentApp: 选择题目问题类型并描述问题
    StudentApp->>ContentAPI: GET /api/v1/questions/{questionId}/detail
    ContentAPI->>StudentApp: 返回题目详细信息
    
    StudentApp->>FeedbackAPI: POST /api/v1/feedback/screenshots
    FeedbackAPI->>StudentApp: 返回截图上传结果
    
    StudentApp->>FeedbackAPI: POST /api/v1/feedback/records
    FeedbackAPI->>StudentApp: 返回反馈提交成功
    
    FeedbackAPI->>AgentAPI: POST /api/v1/agent/audit
    AgentAPI->>FeedbackAPI: 触发智能审核
    
    StudentApp->>Student: 显示反馈提交成功提示
```

### 4.3 智能Agent审核场景

#### 4.3.1 Agent审核流程
1. 系统接收到反馈提交后自动触发Agent审核
2. 根据反馈类型选择相应的Dify工作流
3. Agent获取原始内容进行对比分析
4. 输出审核结果、置信度和分析详情
5. 更新反馈记录的审核状态

#### 4.3.2 接口调用时序图
```mermaid
sequenceDiagram
    participant FeedbackAPI as 反馈管理API
    participant AgentAPI as 智能审核API
    participant ContentAPI as 内容平台API
    participant DifyAPI as Dify工作流平台

    FeedbackAPI->>AgentAPI: POST /api/v1/agent/audit
    AgentAPI->>ContentAPI: GET /api/v1/content/original
    ContentAPI->>AgentAPI: 返回原始内容用于对比
    
    AgentAPI->>DifyAPI: POST /api/v1/agent/dify/workflow
    DifyAPI->>AgentAPI: 返回审核结果和分析详情
    
    AgentAPI->>FeedbackAPI: PUT /api/v1/feedback/records/{feedbackId}/audit
    FeedbackAPI->>AgentAPI: 确认审核结果更新成功
```

### 4.4 生产后台管理场景

#### 4.4.1 生产后台处理流程
1. 生产人员接收到新反馈通知（飞书消息）
2. 在后台系统查看待处理反馈列表
3. 查看反馈详情和Agent审核结果
4. 进行人工审核和分类处理
5. 更新处理状态和结果
6. 同步数据到飞书多维表格

#### 4.4.2 接口调用时序图
```mermaid
sequenceDiagram
    participant Producer as 生产人员
    participant BackofficeApp as 生产后台应用
    participant BackofficeAPI as 生产后台API
    participant FeishuAPI as 飞书平台API
    participant NotificationAPI as 消息通知API

    Producer->>BackofficeApp: 查看待处理反馈
    BackofficeApp->>BackofficeAPI: GET /api/v1/backoffice/feedback?status=pending
    BackofficeAPI->>BackofficeApp: 返回待处理反馈列表
    
    Producer->>BackofficeApp: 查看具体反馈详情
    BackofficeApp->>BackofficeAPI: GET /api/v1/backoffice/feedback/{feedbackId}
    BackofficeAPI->>BackofficeApp: 返回反馈详情和Agent审核结果
    
    Producer->>BackofficeApp: 开始处理反馈
    BackofficeApp->>BackofficeAPI: POST /api/v1/backoffice/feedback/{feedbackId}/processing
    BackofficeAPI->>BackofficeApp: 创建处理记录成功
    
    Producer->>BackofficeApp: 完成处理并更新结果
    BackofficeApp->>BackofficeAPI: PUT /api/v1/backoffice/feedback/{feedbackId}/processing
    BackofficeAPI->>BackofficeApp: 更新处理结果成功
    
    BackofficeAPI->>FeishuAPI: POST /api/v1/backoffice/feishu/sync
    FeishuAPI->>BackofficeAPI: 同步飞书表格成功
    
    BackofficeAPI->>NotificationAPI: POST /api/v1/notifications/send
    NotificationAPI->>BackofficeAPI: 发送用户通知成功
```

### 4.5 用户查看反馈结果场景

#### 4.5.1 用户查看结果流程
1. 用户点击头像菜单进入消息中心
2. 查看反馈处理结果消息列表
3. 查看具体反馈处理详情
4. 了解反馈是否被采纳及原因
5. 查看获得的积分奖励（如有）
6. 标记消息为已读

#### 4.5.2 接口调用时序图
```mermaid
sequenceDiagram
    participant User as 用户
    participant UserApp as 用户端应用
    participant NotificationAPI as 消息通知API
    participant PointsAPI as 积分管理API

    User->>UserApp: 点击消息中心
    UserApp->>NotificationAPI: GET /api/v1/notifications/messages
    NotificationAPI->>UserApp: 返回消息列表和未读数量
    
    User->>UserApp: 查看具体反馈处理结果
    UserApp->>NotificationAPI: GET /api/v1/notifications/feedback/{feedbackId}/result
    NotificationAPI->>UserApp: 返回处理结果详情
    
    alt 反馈被采纳
        UserApp->>PointsAPI: GET /api/v1/points/balance
        PointsAPI->>UserApp: 返回当前积分余额
        
        UserApp->>PointsAPI: GET /api/v1/points/records
        PointsAPI->>UserApp: 返回积分记录历史
    end
    
    User->>UserApp: 标记消息已读
    UserApp->>NotificationAPI: PUT /api/v1/notifications/messages/{messageId}/read
    NotificationAPI->>UserApp: 更新已读状态成功
```

### 4.6 积分下发管理场景

#### 4.6.1 积分下发流程
1. 反馈被采纳且确认有效时触发积分计算
2. 系统根据规则自动计算积分奖励
3. 运营人员审核并确认积分下发
4. 更新用户积分余额
5. 发送积分下发通知给用户

#### 4.6.2 接口调用时序图
```mermaid
sequenceDiagram
    participant System as 系统
    participant PointsAPI as 积分管理API
    participant Operator as 运营人员
    participant NotificationAPI as 消息通知API

    System->>PointsAPI: POST /api/v1/points/calculate
    PointsAPI->>System: 返回建议积分数量和计算规则
    
    Operator->>PointsAPI: POST /api/v1/points/distribute
    PointsAPI->>PointsAPI: 创建积分记录并更新余额
    PointsAPI->>Operator: 返回积分下发成功
    
    PointsAPI->>NotificationAPI: POST /api/v1/notifications/send
    NotificationAPI->>PointsAPI: 发送积分通知成功
```

## 5. 检查清单

### 5.1 PRD核心需求场景与API覆盖性检查

| PRD核心需求点/用户场景 (ID)                     | 涉及的主要API接口 (路径与方法)                                  | 关键输入数据实体/参数 (示例)                        | 关键输出数据实体/参数 (示例)                           | 场景支持度(✅/⚠️/❌) | 数据流正确性(✅/⚠️/❌) | 备注/待办事项                                   |
| :---------------------------------------------- | :-------------------------------------------------------------- | :---------------------------------------------------- | :------------------------------------------------------- | :----------------------------------------------------- | :--------------------------------------------------------- | :---------------------------------------------- |
| 教师端反馈系统（支持多选反馈类型） | `GET /api/v1/feedback/types`, `POST /api/v1/feedback/records`, `POST /api/v1/feedback/screenshots` | `用户类型, 反馈类型数组, 反馈内容, 课程ID, 截图信息` | `反馈类型列表, 反馈ID, 提交状态, 预估处理时间` | ✅ | ✅ | 支持多选反馈类型，满足教师端需求 |
| 学生端反馈系统 | `GET /api/v1/feedback/types`, `POST /api/v1/feedback/records` | `用户类型, 反馈类型数组, 反馈内容, 题目ID` | `反馈类型列表, 反馈ID, 提交状态` | ✅ | ✅ | 支持学生端题目反馈 |
| 智能Agent审核 | `POST /api/v1/agent/audit`, `GET /api/v1/content/original`, `POST /api/v1/agent/dify/workflow` | `反馈ID, 反馈类型, 反馈内容, 工作流类型` | `审核结果, 置信度, 分析详情, 审核记录ID` | ✅ | ✅ | 集成Dify工作流，支持智能审核 |
| 生产后台反馈管理 | `GET /api/v1/backoffice/feedback`, `POST/PUT /api/v1/backoffice/feedback/{feedbackId}/processing` | `分页参数, 筛选条件, 处理人员ID, 处理结果` | `反馈列表, 处理记录ID, 更新状态` | ✅ | ✅ | 支持后台管理和飞书集成 |
| 消息通知系统 | `GET /api/v1/notifications/messages`, `POST /api/v1/notifications/send` | `用户ID, 通知类型, 通知内容, 关联反馈ID` | `消息列表, 通知ID, 发送状态` | ✅ | ✅ | 支持多渠道消息通知 |
| 积分激励机制 | `POST /api/v1/points/calculate`, `POST /api/v1/points/distribute`, `GET /api/v1/points` | `反馈ID, 积分数量, 用户ID, 操作类型` | `积分余额, 积分记录ID, 下发状态` | ✅ | ✅ | 支持积分计算、下发和查询 |

### 5.2 API接口数据实体与数据库支持检查

| API接口 (路径与方法)                     | 操作类型 (CRUD) | 主要数据实体 (模型名称)      | 关键数据属性 (字段名示例)                                 | 依赖的数据库表/视图 (示例)        | 数据库操作 (读/写/更新/删) | 数据可获得性/可行性(✅/⚠️/❌) | 数据一致性保障 (简述策略)                               | 备注/待办事项                                                                 |
| :--------------------------------------- | :-------------- | :--------------------------- | :---------------------------------------------------------- | :-------------------------------- | :--------------------------- | :--------------------------------------------------------- | :-------------------------------------------------------- | :---------------------------------------------------------------------------- |
| `POST /api/v1/feedback/records` | Create | `FeedbackRecord` | `user_id, feedback_types, feedback_content, course_id, question_id` | `tbl_feedback_records` | 写 | ✅ | 事务保证原子性 | 支持多选反馈类型，使用JSONB存储 |
| `GET /api/v1/feedback/types` | Read | `FeedbackType` | `type_name, user_type, is_multiple_select` | 配置表或缓存 | 读 | ✅ | N/A (读取操作) | 支持动态配置，缓存优化 |
| `POST /api/v1/agent/audit` | Create | `AgentAuditRecord` | `feedback_id, workflow_type, audit_result, confidence_score` | `tbl_agent_audit_records` | 写 | ✅ | 唯一约束保证 | Agent审核处理时间<5秒 |
| `GET /api/v1/backoffice/feedback` | Read | `FeedbackRecord` (列表) | `feedback_status, created_at, user_type, feedback_types` | `tbl_feedback_records`, `tbl_agent_audit_records` | 读 | ✅ | N/A (读取操作) | 支持复杂筛选和分页 |
| `POST /api/v1/points/distribute` | Create | `PointRecord` | `user_id, feedback_id, point_amount, distributor_id` | `tbl_point_records` | 写 | ✅ | 事务保证和幂等性 | 防重复下发，支持审核流程 |

### 5.3 API通用设计规范符合性检查

| 检查项                                                                 | 状态 (✅/⚠️/❌/N/A) | 备注/说明                                                                                                |
| :--------------------------------------------------------------------- | :------------------- | :------------------------------------------------------------------------------------------------------- |
| 1. **命名规范**: 接口路径、参数、数据实体命名是否清晰、一致，并遵循团队约定？ | ✅ | 使用RESTful风格，路径清晰，参数命名一致 |
| 2. **HTTP方法**: GET, POST, PUT, DELETE等HTTP方法使用是否语义恰当？ | ✅ | GET用于查询，POST用于创建，PUT用于更新，符合语义 |
| 3. **状态码**: HTTP状态码是否准确反映操作结果？ | ✅ | 200成功，400客户端错误，401认证失败，500服务器错误 |
| 4. **错误处理**: 错误响应格式是否统一，错误信息是否友好？ | ✅ | 统一错误响应格式，包含错误码和详细信息 |
| 5. **认证与授权**: 需要保护的接口是否都正确实施了认证和授权机制？ | ✅ | 基于JWT和RBAC，区分不同角色权限 |
| 6. **数据格式与校验**: 请求和响应体结构是否定义清晰，数据类型是否准确？ | ✅ | JSON格式，数据类型明确，包含必要校验 |
| 7. **分页与排序**: 对于返回列表数据的接口，是否按约定提供了分页和排序功能？ | ✅ | 使用page和pageSize参数，支持sort_by排序 |
| 8. **幂等性**: 对于创建和更新操作，是否考虑了幂等性设计？ | ✅ | PUT操作幂等，POST操作防重复提交 |
| 9. **安全性**: 是否有潜在的安全风险？ | ✅ | 参数校验，权限控制，敏感信息保护 |
| 10. **性能考量**: 接口响应时间是否满足性能要求？ | ✅ | P95响应时间<3秒，Agent处理<5秒，支持缓存优化 |
| 11. **文档一致性**: 本文档描述是否与接口分析文档保持一致？ | ✅ | 严格基于接口分析文档，保持100%一致 |
| 12. **可测试性**: 接口是否易于进行测试？ | ✅ | 接口定义清晰，支持单元测试和集成测试 |
| 13. **向后兼容性**: 未来接口变更是否考虑了向后兼容性策略？ | N/A (新设计) | 通过版本控制策略保证向后兼容 |

## 6. 附录 (Appendix)

* **引用文档**:
  - 产品需求文档 (PRD): be-s1-2-ai-prd-运营工具老师学生课程题目反馈-claude-sonnet-4.md
  - 技术架构设计 (TD): be-s2-1-ai-td-运营工具老师学生课程题目反馈-claude-sonnet-4.md
  - 数据实体设计 (DE): be-s3-1-ai-de-运营工具老师学生课程题目反馈-claude-sonnet-4.md
  - 数据库设计 (DD): be-s4-2-ai-dd-运营工具老师学生课程题目反馈-claude-sonnet-4.md
  - 接口分析 (ADA): be-s5-1-ai-ada-运营工具老师学生课程题目反馈-claude-sonnet-4.md

* **核心行业标准**:
  - RESTful API设计规范
  - OpenAPI 3.x规范
  - JWT认证标准
  - HTTP状态码标准

---

**API使用文档产出完毕，等待您的命令，指挥官**
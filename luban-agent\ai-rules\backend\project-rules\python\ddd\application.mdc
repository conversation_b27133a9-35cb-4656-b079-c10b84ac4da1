---
description: 
globs: backend/app/application/**/*.py
alwaysApply: false
---
# 应用层 (Application Layer) 代码生成规则

> **核心原则：简洁优先**

## 边界
- 总是以`一个完整用例`为方法边界：开启事务 → 调用领域层 / 仓储 → 提交或回滚  
- 禁止依赖 HTTP 框架、ORM 细节；仅依赖 `Repository`、`Domain Service`、其他 `AppService`  
- 总是使用 `pydantic` DTO 作为输入；输出领域实体或 DTO，不暴露技术细节 
- 总是使用 `with session.begin(): …` 或显式 `commit` / `rollback` 管理事务  
- 总是通过构造函数注入依赖；禁止在方法内创建依赖  
- 总是记录异常后重新抛出或返回上层  
- 当操作预计 > 100 ms 时，总是封装为异步任务  
- 不需要考虑领域事件
- 不需要考虑幂等性的设计 

## 约定
- 类名以 `AppService` 结尾  
- 方法名采用 `<verb>_<object>`，并完整类型注解  
- 返回前可调用 `_map_to_dto(entity)` 转换领域对象  
- 日志使用 `logger.info` 记录用例入口、关键参数与结束状态 
- 异常路径必须 `rollback()`；成功路径必须 `commit()`  
- 捕获 `DomainError` 转换为 `ApplicationError`，记录日志后重新抛出  

## 示例代码
```python
class AuthAppService:
    """认证应用服务——事务显式提交 & 回滚"""

    def __init__(
        self,
        user_repo: IUserRepository,
        token_repo: ITokenRepository,
        session: Session,
    ):
        self._user_repo = user_repo
        self._token_repo = token_repo
        self._session = session

    def login(
        self,
        username_str: str,
        password_str: str,
        ip_address: str | None = None,
    ) -> tuple[User, Token]:
        logger.info("login start user=%s ip=%s", username_str, ip_address)
        try:
            user = self._user_repo.find_by_username(Username(username_str))
            AuthenticationService.validate_credentials(user, password_str)
            self._token_repo.invalidate_by_user_id(user.user_id)
            token = Token(user_id=user.user_id, token=AuthenticationService.generate_token_value())
            self._token_repo.save(token)
            self._session.commit()
            return user, token
        except DomainError:
            self._session.rollback()
            raise
```
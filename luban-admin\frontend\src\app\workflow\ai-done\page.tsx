// @ts-nocheck
'use client';

export const dynamic = 'force-dynamic';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import WorkflowSidebar from '@/components/workflow/WorkflowSidebar';
import { useWorkflowData } from '@/hooks/useWorkflowData';
import WorkflowDebugPanel from '@/components/debug/WorkflowDebugPanel';
import { getAiDoneDocuments, batchDownloadAiDoneDocuments } from '@/lib/api/document';
import {
  Button,
  Alert,
  AlertIcon,
  AlertTitle,
  AlertDescription,
  Box,
  useToast,
  Card,
  CardBody,
  CardHeader,
  Heading,
  Text,
  VStack,
  HStack,
  Divider,
  Badge,
  Spinner,
  Center
} from '@chakra-ui/react';
import { CopyIcon, DownloadIcon, ExternalLinkIcon } from '@chakra-ui/icons';
import { API_BASE_URL } from '@/app/configs/api';

function AiDonePageContent() {
  const router = useRouter();
  const toast = useToast();
  const { workflowData, getHiddenInputsData } = useWorkflowData('ai-done');
  const [documentsData, setDocumentsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  // 文档类型配置映射
  const documentTypeConfig = {
    'format-prd': {
      category: 'format-prd',
      title: '格式化PRD',
      description: '格式化后的产品需求文档',
      color: 'green',
      icon: '📋'
    },
    'ai-prd-extract': {
      category: 'ai-prd',
      title: '需求文档',
      description: 'AI生成的产品需求文档',
      color: 'purple',
      icon: '🤖'
    },
    'ai-prd': {
      category: 'ai-prd',
      title: '需求文档',
      description: 'AI生成的产品需求文档',
      color: 'purple',
      icon: '🤖'
    },
    'ai-td': {
      category: 'ai-td',
      title: '技术设计',
      description: 'AI生成的技术设计文档',
      color: 'orange',
      icon: '⚙️'
    },
    'ai-de': {
      category: 'ai-dd',
      title: '数据设计',
      description: 'AI生成的数据设计文档',
      color: 'teal',
      icon: '🗃️'
    },
    'ai-dd': {
      category: 'ai-dd',
      title: '数据设计',
      description: 'AI生成的数据设计文档',
      color: 'teal',
      icon: '🗃️'
    },
    'ai-ada': {
      category: 'ai-ad',
      title: '接口设计',
      description: 'AI生成的接口设计文档',
      color: 'pink',
      icon: '🔗'
    },
    'ai-ad': {
      category: 'ai-ad',
      title: '接口设计',
      description: 'AI生成的接口设计文档',
      color: 'pink',
      icon: '🔗'
    },
    'ai-api': {
      category: 'ai-ad',
      title: '接口设计',
      description: 'AI生成的接口设计文档',
      color: 'pink',
      icon: '🔗'
    }
  };

  // 从MD文件URL中提取token
  const extractTokenFromMdUrl = (url) => {
    if (!url) return null;
    try {
      // 如果是下载URL，从文件名中提取token
      if (url.includes('/download/')) {
        const fileName = url.split('/download/')[1];
        if (fileName) {
          // 匹配各种文件名格式中的token
          const patterns = [
            /format-prd-(.+)\.md$/,
            /ai-prd-extract-(.+)\.md$/,
            /ai-prd-(.+)\.md$/,
            /ai-td-(.+)\.md$/,
            /be-ai-de-(.+)\.md$/,
            /be-ai-dd-(.+)\.md$/,
            /be-ai-ada-(.+)\.md$/,
            /be-ai-ad-(.+)\.md$/,
            /be-ai-api-(.+)\.md$/
          ];
          
          for (const pattern of patterns) {
            const match = fileName.match(pattern);
            if (match) {
              console.log(`从文件名 ${fileName} 中提取到token: ${match[1]}`);
              return match[1];
            }
          }
        }
      }
      
      // 如果是普通URL，尝试从路径中提取
      const urlObj = new URL(url);
      const pathParts = urlObj.pathname.split('/');
      const token = pathParts[pathParts.length - 1];
      console.log(`从URL路径中提取到token: ${token}`);
      return token;
    } catch (error) {
      console.error('提取token失败:', error);
      return null;
    }
  };

  // 获取文档列表
  const fetchDocuments = async () => {
    setLoading(true);
    setError('');
    
    try {
      console.log('开始获取文档列表，当前工作流数据:', workflowData);
      
      // 智能参数处理逻辑
      let requestParams = {
        type: 'be' // 根据API文档，type是必填参数，ai-done页面默认使用后端类型
      };
      
      // 优先级1: 如果有feishu_url，直接使用
      if (workflowData.feishu_url) {
        requestParams.feishu_url = workflowData.feishu_url;
        console.log('使用feishu_url参数:', requestParams.feishu_url);
      } else {
        // 优先级2: 尝试从其他URL参数中提取token
        const urlKeys = ['ai_api_url', 'ai_ad_url', 'ai_dd_url', 'ai_de_url', 'ai_td_url', 'ai_prd_url', 'format_prd_url'];
        let extractedToken = null;
        
        for (const key of urlKeys) {
          if (workflowData[key]) {
            extractedToken = extractTokenFromMdUrl(workflowData[key]);
            if (extractedToken) {
              console.log(`从${key}中提取到token: ${extractedToken}`);
              break;
            }
          }
        }
        
        if (extractedToken) {
          // 如果成功提取到token，使用feishu_token参数
          requestParams.feishu_token = extractedToken;
          console.log('使用feishu_token参数:', requestParams.feishu_token);
        } else {
          // 优先级3: 如果无法提取token，尝试直接使用URL
          for (const key of urlKeys) {
            if (workflowData[key]) {
              requestParams.url = workflowData[key];
              console.log('使用url参数:', requestParams.url);
              break;
            }
          }
        }
      }
      
      // 检查是否有有效参数
      if (!requestParams.feishu_url && !requestParams.feishu_token && !requestParams.url) {
        throw new Error('无法找到有效的URL参数或token');
      }
      
      console.log('发送请求参数:', requestParams);
      const response = await getAiDoneDocuments(requestParams);
      console.log('接收到的文档数据:', response);
      
      setDocumentsData(response);
    } catch (error) {
      console.error('获取文档列表失败:', error);
      setError(error.message || '获取文档列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // 只要有任何一个URL参数，就尝试获取文档列表
    const hasAnyUrl = Object.values(workflowData).some(value => value && typeof value === 'string' && value.trim() !== '');
    if (hasAnyUrl) {
      fetchDocuments();
    }
  }, [workflowData]);

  // 按类别分组文档
  const groupDocumentsByCategory = () => {
    if (!documentsData || !documentsData.documents) return [];
    
    const grouped = {};
    
    documentsData.documents.forEach(doc => {
      if (!doc.exists) return; // 只显示存在的文档
      
      const config = documentTypeConfig[doc.doc_type];
      if (!config) return;
      
      const category = config.category;
      if (!grouped[category]) {
        grouped[category] = {
          ...config,
          files: []
        };
      }
      
      // 构建完整的下载URL，参考其他页面的URL拼接方式
      let fullUrl = null;
      if (doc.download_url) {
        const isDirectDownloadLink = doc.download_url.startsWith('/download');
        const backendHost = API_BASE_URL.replace(/\/api$/, '');
        
        fullUrl = doc.download_url.startsWith('http')
          ? doc.download_url
          : isDirectDownloadLink
            ? `${backendHost}${doc.download_url}`
            : `${API_BASE_URL}${doc.download_url.startsWith('/') ? '' : '/'}${doc.download_url}`;
      }
      
      grouped[category].files.push({
        ...doc,
        displayName: doc.filename.replace(/^(.*-)([A-Za-z0-9_-]+)(\.md)$/, '$1【$2】$3'),
        url: fullUrl,
        description: getDocumentDescription(doc.doc_type)
      });
    });
    
    return Object.values(grouped);
  };

  // 获取文档描述
  const getDocumentDescription = (docType) => {
    const descriptions = {
      'format-prd': '格式化PRD文档',
      'ai-prd-extract': '需求提取文档',
      'ai-prd': 'AI需求文档',
      'ai-td': '技术设计文档',
      'ai-de': '数据实体设计',
      'ai-dd': '数据库设计',
      'ai-ada': '接口架构设计',
      'ai-ad': '接口详细设计',
      'ai-api': 'API接口文档'
    };
    return descriptions[docType] || docType;
  };

  // 复制链接到剪贴板
  const copyToClipboard = (url, title) => {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(url)
          .then(() => {
            toast({
              title: "链接已复制",
              description: `${title}链接已成功复制到剪贴板`,
              status: "success",
              duration: 3000,
              isClosable: true,
            });
          })
          .catch(err => {
            console.error('使用Clipboard API复制失败: ', err);
            fallbackCopyTextToClipboard(url, title);
          });
      } else {
        fallbackCopyTextToClipboard(url, title);
      }
    } catch (err) {
      console.error('复制过程中出错: ', err);
      fallbackCopyTextToClipboard(url, title);
    }

    function fallbackCopyTextToClipboard(text, title) {
      try {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.top = '0';
        textArea.style.left = '0';
        textArea.style.position = 'fixed';
        textArea.style.opacity = '0';
        
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);
        
        if (successful) {
          toast({
            title: "链接已复制",
            description: `${title}链接已成功复制到剪贴板`,
            status: "success",
            duration: 3000,
            isClosable: true,
          });
        } else {
          throw new Error('execCommand复制失败');
        }
      } catch (err) {
        console.error('备用复制方法失败: ', err);
        toast({
          title: "复制失败",
          description: "无法复制链接到剪贴板，请手动复制",
          status: "error",
          duration: 5000,
          isClosable: true,
        });
      }
    }
  };

  // 批量下载所有文件为ZIP
  const downloadAllFiles = async () => {
    try {
      // 构建请求参数，复用现有的参数提取逻辑
      let requestParams = {
        type: 'be' // 根据API文档，type是必填参数，ai-done页面默认使用后端类型
      };
      
      // 优先级1: 如果有feishu_url，直接使用
      if (workflowData.feishu_url) {
        requestParams.feishu_url = workflowData.feishu_url;
      } else {
        // 优先级2: 尝试从其他URL参数中提取token
        const urlKeys = ['ai_api_url', 'ai_ad_url', 'ai_dd_url', 'ai_de_url', 'ai_td_url', 'ai_prd_url', 'format_prd_url'];
        let extractedToken = null;
        
        for (const key of urlKeys) {
          if (workflowData[key]) {
            extractedToken = extractTokenFromMdUrl(workflowData[key]);
            if (extractedToken) {
              break;
            }
          }
        }
        
        if (extractedToken) {
          // 如果成功提取到token，使用feishu_token参数
          requestParams.feishu_token = extractedToken;
        } else {
          // 优先级3: 如果无法提取token，尝试直接使用URL
          for (const key of urlKeys) {
            if (workflowData[key]) {
              requestParams.url = workflowData[key];
              break;
            }
          }
        }
      }
      
      // 检查是否有有效参数
      if (!requestParams.feishu_url && !requestParams.feishu_token && !requestParams.url) {
        throw new Error('无法找到有效的URL参数或token');
      }
      
      toast({
        title: "开始打包",
        description: "正在打包所有文档为ZIP文件...",
        status: "info",
        duration: 3000,
        isClosable: true,
      });
      
      // 调用批量下载API
      const blob = await batchDownloadAiDoneDocuments(requestParams);
      
      // 创建下载链接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      
      // 生成文件名
      const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
      const tokenPart = requestParams.feishu_token ? requestParams.feishu_token.slice(0, 8) : 'docs';
      link.download = `ai-documents-${tokenPart}-${timestamp}.zip`;
      
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // 清理URL对象
      window.URL.revokeObjectURL(url);
      
      toast({
        title: "下载成功",
        description: "ZIP文件已开始下载",
        status: "success",
        duration: 3000,
        isClosable: true,
      });
    } catch (error) {
      console.error('批量下载失败:', error);
      toast({
        title: "下载失败",
        description: error.message || "批量下载失败，请重试",
        status: "error",
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const groupedDocuments = groupDocumentsByCategory();
  const totalFiles = groupedDocuments.reduce((sum, category) => sum + category.files.length, 0);

  return (
    <MainLayout>
      <div className="flex justify-center min-h-screen">
        <div className="flex max-w-6xl w-full">
          {/* 左侧进度菜单 */}
          <WorkflowSidebar className="w-64 flex-shrink-0" />
          
          {/* 主内容区域 */}
          <div className="flex-1">
            <div className="p-6">
              {/* 页面标题 */}
              <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <div className="text-4xl mr-3">🎉</div>
            <Heading as="h1" size="xl" color="green.600">
              工作流完成！
            </Heading>
          </div>
          <Text fontSize="lg" color="gray.600">
            恭喜！您已完成整个AI文档生成工作流，以下是生成的所有文档：
          </Text>
        </div>

        {/* 统计信息 */}
        {documentsData && (
          <Card mb={6}>
            <CardBody>
              <HStack justify="space-between" align="center">
                <HStack>
                  <Text fontSize="lg" fontWeight="bold">工作流统计</Text>
                  <Badge colorScheme="green" fontSize="md" px={3} py={1}>
                    {groupedDocuments.length} 个文档类型已生成
                  </Badge>
                  <Badge colorScheme="blue" fontSize="md" px={3} py={1}>
                    {totalFiles} 个文档总计
                  </Badge>
                  <Badge colorScheme="purple" fontSize="md" px={3} py={1}>
                    完成率 {documentsData.summary.completion_rate}
                  </Badge>
                </HStack>
                <Button
                  leftIcon={<DownloadIcon />}
                  colorScheme="green"
                  size="lg"
                  onClick={downloadAllFiles}
                  isDisabled={totalFiles === 0}
                >
                  批量下载全部
                </Button>
              </HStack>
            </CardBody>
          </Card>
        )}

        {/* 加载状态 */}
        {loading && (
          <Center py={10}>
            <VStack>
              <Spinner size="xl" color="green.500" />
              <Text>正在获取生成的文档...</Text>
            </VStack>
          </Center>
        )}

        {/* 错误状态 */}
        {error && (
          <Alert status="error" borderRadius="md" mb={6}>
            <AlertIcon />
            <Box>
              <AlertTitle>获取文档列表失败！</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Box>
          </Alert>
        )}

        {/* 文档列表 */}
        {!loading && !error && (
          <>
            {groupedDocuments.length === 0 ? (
              <Alert status="warning" borderRadius="md">
                <AlertIcon />
                <Box>
                  <AlertTitle>未找到生成的文档！</AlertTitle>
                  <AlertDescription>
                    请确保您已完成工作流的各个步骤，或检查飞书文档链接是否正确。
                  </AlertDescription>
                </Box>
              </Alert>
            ) : (
              <VStack spacing={6} align="stretch">
                {groupedDocuments.map((category, categoryIndex) => (
                  <Card key={category.category} borderWidth={2} borderColor={`${category.color}.200`}>
                    <CardHeader bg={`${category.color}.50`} borderTopRadius="md">
                      <HStack justify="space-between">
                        <HStack>
                          <Text fontSize="2xl">{category.icon}</Text>
                          <VStack align="start" spacing={0}>
                            <Heading size="md" color={`${category.color}.700`}>
                              {category.title}
                            </Heading>
                            <Text fontSize="sm" color="gray.600">
                              {category.description}
                            </Text>
                          </VStack>
                        </HStack>
                        <Badge colorScheme={category.color} fontSize="sm">
                          步骤 {categoryIndex + 1}
                        </Badge>
                      </HStack>
                    </CardHeader>
                    <CardBody>
                      <VStack spacing={4} align="stretch">
                        {category.files.map((file, fileIndex) => (
                          <Box key={file.doc_type} p={4} bg="gray.50" borderRadius="md" border="1px" borderColor="gray.200">
                            <VStack align="stretch" spacing={3}>
                              <HStack justify="space-between">
                                <VStack align="start" spacing={1}>
                                  <Text fontWeight="bold" color="gray.800">
                                    {file.displayName}
                                  </Text>
                                  <Text fontSize="sm" color="gray.600">
                                    {file.description}
                                  </Text>
                                  <Text fontSize="xs" color="gray.500">
                                    文件大小: {(file.file_size / 1024).toFixed(1)} KB
                                  </Text>
                                </VStack>
                                <Badge colorScheme="gray" fontSize="xs">
                                  文档 {fileIndex + 1}
                                </Badge>
                              </HStack>
                              
                              <Text fontSize="sm" fontFamily="mono" bg="white" p={2} borderRadius="md" border="1px" borderColor="gray.300">
                                {file.url}
                              </Text>
                              
                              <HStack spacing={2}>
                                <Button
                                  leftIcon={<DownloadIcon />}
                                  size="sm"
                                  colorScheme="blue"
                                  variant="outline"
                                  onClick={() => {
                                    if (file.url) {
                                      const link = document.createElement('a');
                                      link.href = file.url;
                                      link.download = file.filename;
                                      document.body.appendChild(link);
                                      link.click();
                                      document.body.removeChild(link);
                                    }
                                  }}
                                >
                                  下载
                                </Button>
                                <Button
                                  leftIcon={<ExternalLinkIcon />}
                                  size="sm"
                                  colorScheme="green"
                                  variant="outline"
                                  onClick={() => window.open(`/workflow/preview-md?url=${encodeURIComponent(file.url)}`, '_blank')}
                                >
                                  预览
                                </Button>
                                <Button
                                  leftIcon={<CopyIcon />}
                                  size="sm"
                                  colorScheme="gray"
                                  variant="outline"
                                  onClick={() => copyToClipboard(file.url, file.displayName)}
                                >
                                  复制链接
                                </Button>
                              </HStack>
                            </VStack>
                          </Box>
                        ))}
                      </VStack>
                    </CardBody>
                  </Card>
                ))}
              </VStack>
            )}
          </>
        )}

        {/* 操作按钮 */}
        <Box mt={8} textAlign="center">
          <Divider mb={6} />
          <VStack spacing={4}>
            <Heading size="md" color="gray.700">开始新的工作流</Heading>
            <Button
              colorScheme="blue"
              size="lg"
              onClick={() => router.push('/workflow/new')}
            >
              返回首页
            </Button>
          </VStack>
        </Box>

        {/* 调试面板 */}
        <WorkflowDebugPanel
          workflowData={workflowData}
          hiddenInputsData={getHiddenInputsData()}
        />
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

export default function AiDonePage() {
  return (
    <Suspense fallback={
      <MainLayout>
        <Center h="50vh">
          <Spinner size="xl" />
        </Center>
      </MainLayout>
    }>
      <AiDonePageContent />
    </Suspense>
  );
}
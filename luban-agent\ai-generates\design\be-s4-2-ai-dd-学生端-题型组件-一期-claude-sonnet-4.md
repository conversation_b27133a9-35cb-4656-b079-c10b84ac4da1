# 数据库设计文档 - 学生端题型组件一期 - V1.0

**最后更新日期**: 2025-05-30
**设计负责人**: claude-sonnet-4
**评审人**: 待填写
**状态**: 草稿

## 0. 文档修订历史

| 版本号 | 修订日期   | 修订人             | 修订内容摘要 |
| ------ | ---------- | ------------------ | ------------ |
| V1.0   | 2025-05-30 | claude-sonnet-4 | 初始草稿     |

## 1. 项目概述与设计目标

### 1.1 项目背景与范围
本项目是学生端题型组件一期功能的数据库设计，主要支持单选题、多选题、填空题、解答题等核心题型的完整答题流程。包括答题记录管理、计时功能、自评机制、勾画工具、选项统计分析和异常行为监控等功能模块。

### 1.2 设计目标回顾
- 提供完整的、符合规范的表创建语句（DDL）
- 清晰描述表间关系，并提供 ER 图
- 详细说明核心用户场景/用户故事所涉及的表以及其 CRUD 操作方式
- 阐述关键表的索引策略及其设计原因
- 对核心表进行数据量预估和增长趋势分析
- 明确数据生命周期管理/归档策略
- 记录特殊设计考量与权衡
- 总结设计如何体现对应数据库（PostgreSQL/ClickHouse）的关键规范

## 2. 数据库环境与总体说明

### 2.1 目标数据库类型
- PostgreSQL 17.x（用于核心业务数据）
- ClickHouse 23.8.16（用于统计分析和日志数据）

### 2.2 数据库创建语句
使用现有数据库实例，目标数据库名称：
- PostgreSQL: `student_exercise_db`
- ClickHouse: `student_analytics_db`

## 3. 数据库表详细设计

### 3.1 数据库表清单

#### PostgreSQL表清单
| 表名 | 核心功能/用途简述 | 预定数据库类型 |
|------|------------------|--------------|
| `tbl_answer_records` | 存储学生答题记录，包括答案内容、批改结果、提交时间等 | PostgreSQL |
| `tbl_component_states` | 存储题型组件状态，管理初始状态、作答状态、解析状态 | PostgreSQL |
| `tbl_timing_records` | 存储每题独立计时记录，支持暂停和继续功能 | PostgreSQL |
| `tbl_self_evaluation` | 存储学生自评记录，包括自评结果和时长 | PostgreSQL |
| `tbl_drawing_data` | 存储学生勾画数据，包括轨迹、颜色、粗细等信息 | PostgreSQL |

#### ClickHouse表清单
| 表名 | 核心功能/用途简述 | 预定数据库类型 |
|------|------------------|--------------|
| `tbl_option_statistics` | 存储选项统计数据，用于分析选项选择占比 | ClickHouse |
| `tbl_behavior_anomalies` | 存储异常行为记录，监控自评过程中的异常操作 | ClickHouse |

---

### 3.2 表名: `tbl_answer_records`

#### 3.2.1 表功能说明
存储学生在各种题型中的答题记录，包括选择题、填空题、解答题的答案内容、批改结果、是否二次作答等核心信息。该表是整个答题系统的核心数据表，支持答题流程的完整记录和状态管理。

#### 3.2.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_answer_records (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  student_id BIGINT NOT NULL,
  question_id BIGINT NOT NULL,
  question_type SMALLINT NOT NULL,
  subject_id BIGINT NOT NULL,
  answer_content JSONB NOT NULL,
  correct_answer JSONB NULL,
  grading_result SMALLINT NOT NULL DEFAULT 0,
  is_second_attempt BOOLEAN NOT NULL DEFAULT FALSE,
  submission_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  grading_time TIMESTAMPTZ NULL,
  answer_duration INTEGER NOT NULL DEFAULT 0,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_answer_records IS '学生答题记录表 - 存储各种题型的答题信息和批改结果';
COMMENT ON COLUMN tbl_answer_records.id IS '答题记录主键ID';
COMMENT ON COLUMN tbl_answer_records.student_id IS '学生ID，关联用户中心服务';
COMMENT ON COLUMN tbl_answer_records.question_id IS '题目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_answer_records.question_type IS '题型类型 (1:单选题, 2:多选题, 3:填空题, 4:解答题)';
COMMENT ON COLUMN tbl_answer_records.subject_id IS '学科ID，关联学科信息';
COMMENT ON COLUMN tbl_answer_records.answer_content IS '学生答案内容，JSON格式存储不同题型的答案';
COMMENT ON COLUMN tbl_answer_records.correct_answer IS '标准答案，JSON格式，用于自评对比';
COMMENT ON COLUMN tbl_answer_records.grading_result IS '批改结果 (0:未批改, 1:正确, 2:错误, 3:部分正确)';
COMMENT ON COLUMN tbl_answer_records.is_second_attempt IS '是否为二次作答';
COMMENT ON COLUMN tbl_answer_records.submission_time IS '答案提交时间';
COMMENT ON COLUMN tbl_answer_records.grading_time IS '批改完成时间';
COMMENT ON COLUMN tbl_answer_records.answer_duration IS '答题用时（秒）';
COMMENT ON COLUMN tbl_answer_records.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_answer_records.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_answer_records.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_answer_records.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_answer_records.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_answer_records.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_answer_records_student_id ON tbl_answer_records(student_id) WHERE deleted_at IS NULL;
-- 支持按学生ID查询答题记录
CREATE INDEX idx_answer_records_question_id ON tbl_answer_records(question_id) WHERE deleted_at IS NULL;
-- 支持按题目ID查询答题记录
CREATE INDEX idx_answer_records_student_question ON tbl_answer_records(student_id, question_id) WHERE deleted_at IS NULL;
-- 支持按学生和题目组合查询
CREATE INDEX idx_answer_records_question_type ON tbl_answer_records(question_type) WHERE deleted_at IS NULL;
-- 支持按题型查询
CREATE INDEX idx_answer_records_submission_time ON tbl_answer_records(submission_time) WHERE deleted_at IS NULL;
-- 支持按提交时间排序查询
CREATE INDEX idx_answer_records_grading_result ON tbl_answer_records(grading_result) WHERE deleted_at IS NULL;
-- 支持按批改结果查询
CREATE INDEX idx_answer_records_answer_content_gin ON tbl_answer_records USING gin (answer_content);
-- 支持对答案内容JSON的查询
```

---

### 3.3 表名: `tbl_component_states`

#### 3.3.1 表功能说明
存储题型组件的状态管理信息，记录学生在答题过程中组件的状态变化，包括初始状态、作答状态、解析状态等。该表支持答题流程的状态控制和进度跟踪。

#### 3.3.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_component_states (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  student_id BIGINT NOT NULL,
  question_id BIGINT NOT NULL,
  current_state SMALLINT NOT NULL DEFAULT 1,
  state_enter_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  previous_state SMALLINT NULL,
  state_data JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_component_states IS '题型组件状态表 - 管理学生答题过程中的组件状态变化';
COMMENT ON COLUMN tbl_component_states.id IS '状态记录主键ID';
COMMENT ON COLUMN tbl_component_states.student_id IS '学生ID，关联用户中心服务';
COMMENT ON COLUMN tbl_component_states.question_id IS '题目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_component_states.current_state IS '当前状态 (1:初始状态, 2:作答状态, 3:解析状态)';
COMMENT ON COLUMN tbl_component_states.state_enter_time IS '进入当前状态的时间';
COMMENT ON COLUMN tbl_component_states.previous_state IS '前一个状态';
COMMENT ON COLUMN tbl_component_states.state_data IS '状态相关数据，JSON格式';
COMMENT ON COLUMN tbl_component_states.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_component_states.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_component_states.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_component_states.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_component_states.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_component_states.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_component_states_student_id ON tbl_component_states(student_id) WHERE deleted_at IS NULL;
-- 支持按学生ID查询状态记录
CREATE INDEX idx_component_states_question_id ON tbl_component_states(question_id) WHERE deleted_at IS NULL;
-- 支持按题目ID查询状态记录
CREATE UNIQUE INDEX uk_component_states_student_question ON tbl_component_states(student_id, question_id) WHERE deleted_at IS NULL;
-- 确保每个学生对每个题目只有一条状态记录
CREATE INDEX idx_component_states_current_state ON tbl_component_states(current_state) WHERE deleted_at IS NULL;
-- 支持按当前状态查询
CREATE INDEX idx_component_states_state_enter_time ON tbl_component_states(state_enter_time) WHERE deleted_at IS NULL;
-- 支持按状态进入时间查询
```

---

### 3.4 表名: `tbl_timing_records`

#### 3.4.1 表功能说明
存储每题独立的计时记录，支持暂停和继续计时功能。记录学生在每个题目上的答题用时，支持MM:SS格式显示，上限59:59。该表为计时功能提供精确的时间管理能力。

#### 3.4.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_timing_records (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  student_id BIGINT NOT NULL,
  question_id BIGINT NOT NULL,
  start_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  end_time TIMESTAMPTZ NULL,
  accumulated_duration INTEGER NOT NULL DEFAULT 0,
  is_paused BOOLEAN NOT NULL DEFAULT FALSE,
  pause_count INTEGER NOT NULL DEFAULT 0,
  last_pause_time TIMESTAMPTZ NULL,
  last_resume_time TIMESTAMPTZ NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_timing_records IS '计时记录表 - 存储每题独立的计时信息，支持暂停和继续功能';
COMMENT ON COLUMN tbl_timing_records.id IS '计时记录主键ID';
COMMENT ON COLUMN tbl_timing_records.student_id IS '学生ID，关联用户中心服务';
COMMENT ON COLUMN tbl_timing_records.question_id IS '题目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_timing_records.start_time IS '开始计时时间';
COMMENT ON COLUMN tbl_timing_records.end_time IS '结束计时时间';
COMMENT ON COLUMN tbl_timing_records.accumulated_duration IS '累计答题时长（秒），上限3599秒（59:59）';
COMMENT ON COLUMN tbl_timing_records.is_paused IS '当前是否处于暂停状态';
COMMENT ON COLUMN tbl_timing_records.pause_count IS '暂停次数统计';
COMMENT ON COLUMN tbl_timing_records.last_pause_time IS '最后一次暂停时间';
COMMENT ON COLUMN tbl_timing_records.last_resume_time IS '最后一次恢复时间';
COMMENT ON COLUMN tbl_timing_records.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_timing_records.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_timing_records.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_timing_records.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_timing_records.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_timing_records.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_timing_records_student_id ON tbl_timing_records(student_id) WHERE deleted_at IS NULL;
-- 支持按学生ID查询计时记录
CREATE INDEX idx_timing_records_question_id ON tbl_timing_records(question_id) WHERE deleted_at IS NULL;
-- 支持按题目ID查询计时记录
CREATE UNIQUE INDEX uk_timing_records_student_question ON tbl_timing_records(student_id, question_id) WHERE deleted_at IS NULL;
-- 确保每个学生对每个题目只有一条计时记录
CREATE INDEX idx_timing_records_is_paused ON tbl_timing_records(is_paused) WHERE deleted_at IS NULL;
-- 支持查询暂停状态的计时记录
CREATE INDEX idx_timing_records_accumulated_duration ON tbl_timing_records(accumulated_duration) WHERE deleted_at IS NULL;
-- 支持按答题时长查询
```

---

### 3.5 表名: `tbl_self_evaluation`

#### 3.5.1 表功能说明
存储学生对填空题和解答题的自我评价记录，包括自评结果、自评时长等信息。该表支持自评功能的完整记录，并提供异常行为监控的数据基础。

#### 3.5.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_self_evaluation (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  answer_record_id BIGINT NOT NULL,
  evaluation_result SMALLINT NOT NULL,
  evaluation_duration INTEGER NOT NULL,
  evaluation_time TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  evaluation_data JSONB NULL,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_self_evaluation IS '自评记录表 - 存储学生对填空题和解答题的自我评价结果';
COMMENT ON COLUMN tbl_self_evaluation.id IS '自评记录主键ID';
COMMENT ON COLUMN tbl_self_evaluation.answer_record_id IS '关联的答题记录ID';
COMMENT ON COLUMN tbl_self_evaluation.evaluation_result IS '自评结果 (1:我答对了, 2:部分答对, 3:我答错了)';
COMMENT ON COLUMN tbl_self_evaluation.evaluation_duration IS '自评操作耗时（秒）';
COMMENT ON COLUMN tbl_self_evaluation.evaluation_time IS '自评操作的时间戳';
COMMENT ON COLUMN tbl_self_evaluation.evaluation_data IS '自评相关数据，JSON格式';
COMMENT ON COLUMN tbl_self_evaluation.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_self_evaluation.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_self_evaluation.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_self_evaluation.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_self_evaluation.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_self_evaluation.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE UNIQUE INDEX uk_self_evaluation_answer_record ON tbl_self_evaluation(answer_record_id) WHERE deleted_at IS NULL;
-- 确保每个答题记录只有一条自评记录
CREATE INDEX idx_self_evaluation_evaluation_result ON tbl_self_evaluation(evaluation_result) WHERE deleted_at IS NULL;
-- 支持按自评结果查询
CREATE INDEX idx_self_evaluation_evaluation_duration ON tbl_self_evaluation(evaluation_duration) WHERE deleted_at IS NULL;
-- 支持按自评时长查询，用于异常行为检测
CREATE INDEX idx_self_evaluation_evaluation_time ON tbl_self_evaluation(evaluation_time) WHERE deleted_at IS NULL;
-- 支持按自评时间查询
```

---

### 3.6 表名: `tbl_drawing_data`

#### 3.6.1 表功能说明
存储学生在题目上进行的勾画和标记数据，包括画笔轨迹、颜色、粗细等详细信息。该表支持勾画工具的完整功能，为学生提供题目标记和思路整理的能力。

#### 3.6.2 表结构定义 (DDL)
```sql
CREATE TABLE tbl_drawing_data (
  id BIGSERIAL PRIMARY KEY,
  -- 业务字段
  student_id BIGINT NOT NULL,
  question_id BIGINT NOT NULL,
  drawing_content JSONB NOT NULL,
  tool_type SMALLINT NOT NULL,
  color VARCHAR(20) NOT NULL DEFAULT 'blue',
  thickness SMALLINT NOT NULL DEFAULT 2,
  drawing_order INTEGER NOT NULL,
  is_erased BOOLEAN NOT NULL DEFAULT FALSE,
  -- 标准字段
  status SMALLINT NOT NULL DEFAULT 1,
  created_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at TIMESTAMPTZ NULL DEFAULT NULL,
  created_by BIGINT NULL,
  updated_by BIGINT NULL
);

-- 添加表和字段注释
COMMENT ON TABLE tbl_drawing_data IS '勾画数据表 - 存储学生在题目上的勾画和标记信息';
COMMENT ON COLUMN tbl_drawing_data.id IS '勾画数据主键ID';
COMMENT ON COLUMN tbl_drawing_data.student_id IS '学生ID，关联用户中心服务';
COMMENT ON COLUMN tbl_drawing_data.question_id IS '题目ID，关联内容平台服务';
COMMENT ON COLUMN tbl_drawing_data.drawing_content IS '勾画的具体数据（坐标、轨迹等），JSON格式';
COMMENT ON COLUMN tbl_drawing_data.tool_type IS '使用的勾画工具 (1:画笔, 2:橡皮擦)';
COMMENT ON COLUMN tbl_drawing_data.color IS '勾画颜色 (blue, red, black)';
COMMENT ON COLUMN tbl_drawing_data.thickness IS '勾画粗细 (1:细, 2:中等, 3:粗)';
COMMENT ON COLUMN tbl_drawing_data.drawing_order IS '勾画顺序，用于重现勾画过程';
COMMENT ON COLUMN tbl_drawing_data.is_erased IS '是否已被擦除';
COMMENT ON COLUMN tbl_drawing_data.status IS '记录状态 (0:禁用, 1:启用)';
COMMENT ON COLUMN tbl_drawing_data.created_at IS '记录创建时间';
COMMENT ON COLUMN tbl_drawing_data.updated_at IS '记录最后更新时间';
COMMENT ON COLUMN tbl_drawing_data.deleted_at IS '软删除标记时间 (NULL表示未删除)';
COMMENT ON COLUMN tbl_drawing_data.created_by IS '创建人的用户ID';
COMMENT ON COLUMN tbl_drawing_data.updated_by IS '最后更新人的用户ID';

-- 索引定义
CREATE INDEX idx_drawing_data_student_id ON tbl_drawing_data(student_id) WHERE deleted_at IS NULL;
-- 支持按学生ID查询勾画数据
CREATE INDEX idx_drawing_data_question_id ON tbl_drawing_data(question_id) WHERE deleted_at IS NULL;
-- 支持按题目ID查询勾画数据
CREATE INDEX idx_drawing_data_student_question ON tbl_drawing_data(student_id, question_id) WHERE deleted_at IS NULL;
-- 支持按学生和题目组合查询
CREATE INDEX idx_drawing_data_drawing_order ON tbl_drawing_data(student_id, question_id, drawing_order) WHERE deleted_at IS NULL;
-- 支持按勾画顺序查询，用于重现勾画过程
CREATE INDEX idx_drawing_data_tool_type ON tbl_drawing_data(tool_type) WHERE deleted_at IS NULL;
-- 支持按工具类型查询
CREATE INDEX idx_drawing_data_drawing_content_gin ON tbl_drawing_data USING gin (drawing_content);
-- 支持对勾画内容JSON的查询
```

---

### 3.7 表名: `tbl_option_statistics` (ClickHouse)

#### 3.7.1 表功能说明
存储单选题和多选题的选项选择统计数据，用于分析选项选择占比。该表采用ClickHouse存储，支持高频写入和高效聚合查询，为选项统计分析提供数据基础。

#### 3.7.2 表结构定义 (DDL)
```sql
CREATE TABLE student_analytics_db.tbl_option_statistics
(
    -- 时间字段
    log_date Date MATERIALIZED toDate(event_time) COMMENT '统计日期（用于分区）',
    event_time DateTime64(3) COMMENT '事件发生时间（毫秒精度）',
    
    -- 业务字段
    question_id UInt64 COMMENT '题目ID',
    option_label String COMMENT '选项标识（A、B、C、D等）',
    student_id UInt64 COMMENT '学生ID',
    question_type UInt8 COMMENT '题型类型（1:单选题, 2:多选题）',
    is_correct UInt8 COMMENT '是否为正确选项（0:错误, 1:正确）',
    selection_count UInt32 DEFAULT 1 COMMENT '选择次数',
    
    -- 扩展数据
    session_id String COMMENT '会话ID',
    subject_id UInt64 COMMENT '学科ID',
    metadata Map(String, String) COMMENT '扩展元数据'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date) -- 按月分区
ORDER BY (log_date, question_id, option_label, event_time) -- 排序键
TTL log_date + INTERVAL 365 DAY DELETE -- 数据保留1年
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE student_analytics_db.tbl_option_statistics IS '选项统计表 - 存储单选题和多选题的选项选择统计数据';

-- 跳数索引
ALTER TABLE student_analytics_db.tbl_option_statistics ADD INDEX idx_option_statistics_question_id question_id TYPE minmax GRANULARITY 4;
ALTER TABLE student_analytics_db.tbl_option_statistics ADD INDEX idx_option_statistics_student_id student_id TYPE bloom_filter GRANULARITY 1;
-- 支持快速筛选特定学生的选择记录
ALTER TABLE student_analytics_db.tbl_option_statistics ADD INDEX idx_option_statistics_question_type question_type TYPE set(2) GRANULARITY 1;
-- 支持按题型筛选
```

---

### 3.8 表名: `tbl_behavior_anomalies` (ClickHouse)

#### 3.8.1 表功能说明
存储自评过程中的异常行为记录，监控连续快速自评等异常操作。该表采用ClickHouse存储，支持实时异常检测和行为分析，为系统安全和用户体验优化提供数据支撑。

#### 3.8.2 表结构定义 (DDL)
```sql
CREATE TABLE student_analytics_db.tbl_behavior_anomalies
(
    -- 时间字段
    log_date Date MATERIALIZED toDate(event_time) COMMENT '异常日期（用于分区）',
    event_time DateTime64(3) COMMENT '异常发生时间（毫秒精度）',
    
    -- 业务字段
    student_id UInt64 COMMENT '学生ID',
    anomaly_type LowCardinality(String) COMMENT '异常类型（continuous_fast_evaluation等）',
    trigger_time DateTime64(3) COMMENT '异常触发时间',
    consecutive_count UInt16 COMMENT '连续异常题目数量',
    average_duration Float32 COMMENT '平均操作时长（秒）',
    threshold_value Float32 COMMENT '触发阈值',
    
    -- 详细信息
    question_ids Array(UInt64) COMMENT '涉及的题目ID数组',
    evaluation_durations Array(Float32) COMMENT '各题目的自评时长数组',
    session_id String COMMENT '会话ID',
    
    -- 扩展数据
    detection_rule String COMMENT '检测规则描述',
    severity_level UInt8 COMMENT '严重程度（1:低, 2:中, 3:高）',
    metadata Map(String, String) COMMENT '扩展元数据'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date) -- 按月分区
ORDER BY (log_date, student_id, event_time, anomaly_type) -- 排序键
TTL log_date + INTERVAL 180 DAY DELETE -- 数据保留6个月
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE student_analytics_db.tbl_behavior_anomalies IS '异常行为记录表 - 监控自评过程中的异常操作行为';

-- 跳数索引
ALTER TABLE student_analytics_db.tbl_behavior_anomalies ADD INDEX idx_behavior_anomalies_student_id student_id TYPE minmax GRANULARITY 4;
-- 支持快速筛选特定学生的异常记录
ALTER TABLE student_analytics_db.tbl_behavior_anomalies ADD INDEX idx_behavior_anomalies_anomaly_type anomaly_type TYPE set(5) GRANULARITY 1;
-- 支持按异常类型筛选
ALTER TABLE student_analytics_db.tbl_behavior_anomalies ADD INDEX idx_behavior_anomalies_severity severity_level TYPE set(3) GRANULARITY 1;
-- 支持按严重程度筛选
```

---

## 4. 表间关系与 ER 图

### 4.1 表间关系描述

| 主表           | 关系类型                       | 从表               | 外键字段 (从表)                           | 关联描述                                               |
| -------------- | ------------------------------ | ------------------ | ----------------------------------------- | ------------------------------------------------------ |
| `tbl_answer_records`   | 一对一 (1:1)                   | `tbl_component_states`     | `student_id, question_id`                                 | 每个答题记录对应一个组件状态                                 |
| `tbl_answer_records` | 一对一 (1:1) | `tbl_timing_records` | `student_id, question_id` | 每个答题记录对应一个计时记录 |
| `tbl_answer_records` | 一对零或一 (1:0..1) | `tbl_self_evaluation` | `answer_record_id` | 填空题和解答题可能有自评记录 |
| `tbl_answer_records` | 一对多 (1:N) | `tbl_drawing_data` | `student_id, question_id` | 一个答题记录可能有多个勾画数据 |
| `tbl_answer_records` | 一对多 (1:N) | `tbl_option_statistics` | `question_id` | 选择题答题记录产生选项统计数据 |
| `tbl_self_evaluation` | 一对多 (1:N) | `tbl_behavior_anomalies` | `student_id` | 自评记录可能触发异常行为检测 |

### 4.2 ER 图 (使用 Mermaid 语法)
注意：本ER图中的外键关系（FK）表示的是表之间的逻辑关联和参照完整性期望，实际建表语句中不使用数据库层面的物理外键约束，数据一致性由应用层保障。

```mermaid
erDiagram
    %% PostgreSQL 核心业务表
    ANSWER_RECORDS {
        BIGSERIAL id PK
        BIGINT student_id FK
        BIGINT question_id FK
        SMALLINT question_type
        BIGINT subject_id FK
        JSONB answer_content
        JSONB correct_answer
        SMALLINT grading_result
        BOOLEAN is_second_attempt
        TIMESTAMPTZ submission_time
        TIMESTAMPTZ grading_time
        INTEGER answer_duration
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    COMPONENT_STATES {
        BIGSERIAL id PK
        BIGINT student_id FK
        BIGINT question_id FK
        SMALLINT current_state
        TIMESTAMPTZ state_enter_time
        SMALLINT previous_state
        JSONB state_data
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    TIMING_RECORDS {
        BIGSERIAL id PK
        BIGINT student_id FK
        BIGINT question_id FK
        TIMESTAMPTZ start_time
        TIMESTAMPTZ end_time
        INTEGER accumulated_duration
        BOOLEAN is_paused
        INTEGER pause_count
        TIMESTAMPTZ last_pause_time
        TIMESTAMPTZ last_resume_time
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    SELF_EVALUATION {
        BIGSERIAL id PK
        BIGINT answer_record_id FK
        SMALLINT evaluation_result
        INTEGER evaluation_duration
        TIMESTAMPTZ evaluation_time
        JSONB evaluation_data
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }
    
    DRAWING_DATA {
        BIGSERIAL id PK
        BIGINT student_id FK
        BIGINT question_id FK
        JSONB drawing_content
        SMALLINT tool_type
        VARCHAR color
        SMALLINT thickness
        INTEGER drawing_order
        BOOLEAN is_erased
        SMALLINT status
        TIMESTAMPTZ created_at
        TIMESTAMPTZ updated_at
    }

    %% ClickHouse 分析表
    OPTION_STATISTICS {
        Date log_date
        DateTime64 event_time
        UInt64 question_id
        String option_label
        UInt64 student_id
        UInt8 question_type
        UInt8 is_correct
        UInt32 selection_count
        String session_id
        UInt64 subject_id
    }
    
    BEHAVIOR_ANOMALIES {
        Date log_date
        DateTime64 event_time
        UInt64 student_id
        String anomaly_type
        DateTime64 trigger_time
        UInt16 consecutive_count
        Float32 average_duration
        Float32 threshold_value
        Array_UInt64 question_ids
        String session_id
    }

    %% 关系定义
    ANSWER_RECORDS ||--|| COMPONENT_STATES : "has_state"
    ANSWER_RECORDS ||--|| TIMING_RECORDS : "has_timing"
    ANSWER_RECORDS ||--o| SELF_EVALUATION : "may_have_evaluation"
    ANSWER_RECORDS ||--o{ DRAWING_DATA : "may_have_drawings"
    ANSWER_RECORDS ||--o{ OPTION_STATISTICS : "generates_statistics"
    SELF_EVALUATION ||--o{ BEHAVIOR_ANOMALIES : "may_trigger_anomaly"
```

## 5. 核心用户场景/用户故事与数据操作分析

### 5.1 场景/故事: 学生进行单选题作答

#### 5.1.1 场景描述
学生进入单选题界面，查看题目内容和选项，选择认为正确的选项，提交答案并查看结果反馈。系统需要记录完整的答题过程，包括计时、状态管理和结果记录。

#### 5.1.2 涉及的主要数据表
- `tbl_answer_records` - 记录答题内容和结果
- `tbl_component_states` - 管理组件状态变化
- `tbl_timing_records` - 记录答题计时
- `tbl_option_statistics` - 统计选项选择数据

#### 5.1.3 数据操作流程 (CRUD 分析)
- **步骤 1: 学生进入题目**
  - **创建 (Create)**:
    - 在 `tbl_component_states` 表中插入初始状态记录
    - 涉及字段：`student_id`, `question_id`, `current_state = 1`, `state_enter_time`
    - 在 `tbl_timing_records` 表中插入计时记录
    - 涉及字段：`student_id`, `question_id`, `start_time`, `is_paused = false`

- **步骤 2: 学生选择选项并提交**
  - **更新 (Update)**:
    - 更新 `tbl_component_states` 表状态为作答状态
    - 更新字段：`current_state = 2`, `previous_state = 1`, `updated_at`
  - **创建 (Create)**:
    - 在 `tbl_answer_records` 表中插入答题记录
    - 涉及字段：`student_id`, `question_id`, `question_type = 1`, `answer_content`, `submission_time`
  - **更新 (Update)**:
    - 更新 `tbl_timing_records` 表结束计时
    - 更新字段：`end_time`, `accumulated_duration`, `updated_at`

- **步骤 3: 系统自动批改**
  - **更新 (Update)**:
    - 更新 `tbl_answer_records` 表批改结果
    - 更新字段：`grading_result`, `grading_time`, `updated_at`
  - **创建 (Create)**:
    - 在 `tbl_option_statistics` 表中插入选项统计数据
    - 涉及字段：`question_id`, `option_label`, `student_id`, `question_type = 1`, `selection_count = 1`

- **步骤 4: 展示解析状态**
  - **更新 (Update)**:
    - 更新 `tbl_component_states` 表状态为解析状态
    - 更新字段：`current_state = 3`, `previous_state = 2`, `state_enter_time`, `updated_at`

#### 5.1.4 性能考量与索引支持
- 状态查询通过 `uk_component_states_student_question` 唯一索引快速定位
- 答题记录查询通过 `idx_answer_records_student_question` 复合索引优化
- 计时记录更新通过 `uk_timing_records_student_question` 唯一索引快速定位
- 选项统计写入通过ClickHouse的高性能写入能力支持高并发

---

### 5.2 场景/故事: 学生进行填空题自评

#### 5.2.1 场景描述
学生完成非英语学科的填空题作答，提交答案后查看标准答案，进行自我评价选择（我答对了/部分答对/我答错了）。系统需要监控自评时长，检测异常行为。

#### 5.2.2 涉及的主要数据表
- `tbl_answer_records` - 记录填空题答案
- `tbl_self_evaluation` - 记录自评结果
- `tbl_behavior_anomalies` - 监控异常行为

#### 5.2.3 数据操作流程 (CRUD 分析)
- **步骤 1: 学生提交填空题答案**
  - **创建 (Create)**:
    - 在 `tbl_answer_records` 表中插入答题记录
    - 涉及字段：`student_id`, `question_id`, `question_type = 3`, `answer_content`, `correct_answer`

- **步骤 2: 学生进行自评**
  - **创建 (Create)**:
    - 在 `tbl_self_evaluation` 表中插入自评记录
    - 涉及字段：`answer_record_id`, `evaluation_result`, `evaluation_duration`, `evaluation_time`

- **步骤 3: 异常行为检测**
  - **读取 (Read)**:
    - 从 `tbl_self_evaluation` 表中查询最近3题的自评时长
    - 查询条件：按学生ID和时间倒序查询最近3条记录
  - **创建 (Create)**:
    - 如果检测到异常（连续3题<2秒），在 `tbl_behavior_anomalies` 表中插入异常记录
    - 涉及字段：`student_id`, `anomaly_type = 'continuous_fast_evaluation'`, `consecutive_count = 3`, `average_duration`

#### 5.2.4 性能考量与索引支持
- 自评记录通过 `uk_self_evaluation_answer_record` 唯一索引确保数据一致性
- 异常检测查询通过 `idx_self_evaluation_evaluation_time` 索引优化时间范围查询
- 异常记录写入通过ClickHouse的列式存储高效处理

---

### 5.3 场景/故事: 学生使用勾画工具

#### 5.3.1 场景描述
学生在答题过程中使用勾画工具对题目进行标记，包括选择画笔颜色、调整粗细、进行勾画操作、使用橡皮擦等。系统需要实时保存勾画数据并支持撤回恢复操作。

#### 5.3.2 涉及的主要数据表
- `tbl_drawing_data` - 存储勾画轨迹数据

#### 5.3.3 数据操作流程 (CRUD 分析)
- **步骤 1: 学生开始勾画**
  - **创建 (Create)**:
    - 在 `tbl_drawing_data` 表中插入勾画数据
    - 涉及字段：`student_id`, `question_id`, `drawing_content`, `tool_type = 1`, `color`, `thickness`, `drawing_order`

- **步骤 2: 学生使用橡皮擦**
  - **创建 (Create)**:
    - 插入橡皮擦操作记录
    - 涉及字段：`tool_type = 2`, `drawing_content`（擦除区域坐标）
  - **更新 (Update)**:
    - 标记被擦除的勾画数据
    - 更新字段：`is_erased = true`

- **步骤 3: 一键清空操作**
  - **更新 (Update)**:
    - 批量更新该题目的所有勾画数据
    - 更新条件：`student_id = ? AND question_id = ?`
    - 更新字段：`is_erased = true`, `updated_at`

#### 5.3.4 性能考量与索引支持
- 勾画数据查询通过 `idx_drawing_data_student_question` 复合索引优化
- 勾画顺序查询通过 `idx_drawing_data_drawing_order` 索引支持轨迹重现
- JSON内容查询通过 `idx_drawing_data_drawing_content_gin` GIN索引优化

---

## 6. 数据量预估与性能优化策略

### 6.1 数据量预估

| 表名 | 初期数据量 (3个月) | 一年后预估 | 数据特征 | 增长模式 |
|------|-------------------|------------|----------|----------|
| `tbl_answer_records` | 100万条 | 600万条 | 核心答题数据，高频读写 | 线性增长，随用户活跃度提升 |
| `tbl_component_states` | 100万条 | 600万条 | 状态管理数据，频繁更新 | 与答题记录1:1增长 |
| `tbl_timing_records` | 100万条 | 600万条 | 计时数据，实时更新 | 与答题记录1:1增长 |
| `tbl_self_evaluation` | 30万条 | 200万条 | 自评数据，约30%答题产生 | 随填空题和解答题数量增长 |
| `tbl_drawing_data` | 200万条 | 1500万条 | 勾画数据，可能产生多条 | 快速增长，平均每题2-3条 |
| `tbl_option_statistics` (ClickHouse) | 500万条 | 4000万条 | 选项统计，高频写入 | 快速增长，每次选择产生一条 |
| `tbl_behavior_anomalies` (ClickHouse) | 1万条 | 10万条 | 异常记录，低频产生 | 缓慢增长，异常检测触发 |

### 6.2 数据增长趋势分析

**PostgreSQL表增长特点**：
- **核心业务型**：答题记录、状态、计时等数据与用户学习活动强相关
- **实时更新型**：状态和计时数据需要频繁更新，对写入性能要求高
- **关联查询型**：多表关联查询较多，需要优化索引策略

**ClickHouse表增长特点**：
- **高频写入型**：选项统计数据写入频率极高，需要优化写入性能
- **分析导向型**：主要用于统计分析和异常监控，读取以聚合查询为主
- **时间序列型**：数据按时间顺序产生，适合时间分区策略

### 6.3 性能优化策略

#### 6.3.1 PostgreSQL优化策略
**索引优化**：
- 为高频查询组合建立复合索引，如 `(student_id, question_id)`
- 使用部分索引减少索引大小，如 `WHERE deleted_at IS NULL`
- 为JSON字段使用GIN索引支持复杂查询

**查询优化**：
- 使用连接池管理数据库连接
- 对大表查询使用分页和限制条件
- 优化关联查询，避免N+1问题

#### 6.3.2 ClickHouse优化策略
**存储优化**：
- 使用合适的压缩算法减少存储空间
- 设置合理的TTL策略，自动清理过期数据
- 使用物化视图预计算常用统计结果

**查询优化**：
- 利用排序键优化常用查询模式
- 使用跳数索引加速特定字段的过滤查询
- 合理设计分区键，支持分区裁剪优化

---

## 7. 数据生命周期管理/归档策略

### 7.1 PostgreSQL数据生命周期

| 表名 | 热数据保留期 | 温数据处理 | 冷数据归档 | 删除策略 |
|------|-------------|-----------|-----------|----------|
| `tbl_answer_records` | 1年 | 迁移到历史表 | 3年后归档到对象存储 | 5年后物理删除 |
| `tbl_component_states` | 6个月 | 软删除标记 | 1年后物理删除 | 不需要长期保存 |
| `tbl_timing_records` | 1年 | 迁移到历史表 | 3年后归档 | 5年后删除 |
| `tbl_self_evaluation` | 2年 | 保持在线 | 5年后归档 | 用于长期分析 |
| `tbl_drawing_data` | 1年 | 压缩存储 | 3年后归档 | 5年后删除 |

### 7.2 ClickHouse数据生命周期

| 表名 | TTL设置 | 分区策略 | 压缩策略 |
|------|---------|----------|----------|
| `tbl_option_statistics` | 365天 | 按月分区 | ZSTD压缩 |
| `tbl_behavior_anomalies` | 180天 | 按月分区 | ZSTD压缩 |

---

## 8. 特殊设计考量与权衡

### 8.1 JSON字段使用考量
**设计决策**：在答案内容、勾画数据等字段使用JSONB类型
**权衡分析**：
- **优势**：支持不同题型的灵活数据结构，便于扩展
- **劣势**：查询性能相对较低，需要GIN索引优化
- **解决方案**：为常用查询路径建立表达式索引

### 8.2 状态管理设计
**设计决策**：独立的组件状态表而非在答题记录中添加状态字段
**权衡分析**：
- **优势**：状态变化历史可追踪，支持复杂状态流转
- **劣势**：增加了表的复杂度和关联查询
- **解决方案**：通过唯一索引确保数据一致性

### 8.3 计时精度设计
**设计决策**：使用秒级精度而非毫秒级
**权衡分析**：
- **优势**：满足业务需求（MM:SS格式），减少存储空间
- **劣势**：无法支持更精确的时间分析
- **解决方案**：如需更高精度，可在应用层处理

---

## 9. 设计检查清单 (Design Checklist)

### 9.1 需求与架构对齐性检查

| 检查项 | 检查结果 | 说明 |
|--------|----------|------|
| **PRD核心实体覆盖** | ✅ 完整 | 所有PRD中定义的核心实体均已设计对应表结构 |
| **业务流程支持** | ✅ 完整 | 答题流程、计时、自评、勾画等关键业务流程均有数据支撑 |
| **性能需求满足** | ✅ 满足 | 索引设计支持高并发访问和快速查询需求 |
| **扩展性考虑** | ✅ 充分 | JSON字段和预留字段支持未来功能扩展 |

### 9.2 数据库设计质量检查

| 检查维度 | 检查结果 | 详细说明 |
|----------|----------|----------|
| **表结构规范性** | ✅ 符合 | 严格遵循PostgreSQL和ClickHouse设计规范 |
| **索引设计合理性** | ✅ 合理 | 为高频查询场景设计了复合索引和专用索引 |
| **数据类型选择** | ✅ 恰当 | 根据业务需求选择合适的数据类型 |
| **约束条件完整性** | ✅ 完整 | 主键、唯一约束、非空约束等设计完整 |
| **关系设计清晰性** | ✅ 清晰 | 表间关系通过逻辑外键明确定义 |
| **性能优化策略** | ✅ 全面 | 分区策略、TTL策略、索引策略等优化措施完备 |

## 10. 参考资料与附录

### 10.1 核心输入文档
- **数据实体设计文档**: `be-s3-1-ai-de-学生端-题型组件-一期-claude-sonnet-4.md`
- **产品需求文档**: `be-s1-2-ai-prd-学生端-题型组件-一期-claude-sonnet-4.md`

### 10.2 设计规范与约束
- **PostgreSQL 数据库设计规范**: `luban-agent/ai-rules/backend/server-rules/rules-postgresql-code-v2.md`
- **ClickHouse 数据库设计规范**: `luban-agent/ai-rules/backend/server-rules/rules-clickhouse-code-v2.md`

### 10.3 参考设计文档
- **AI课中课程框架数据库设计**: `be-s3-4-ai-dd-AI课中-课程框架+文档组件V0.9-claude-sonnet-4.md`
- **掌握度策略数据库设计**: `be-ai-dd-掌握度策略-claude-3.7.md`

### 10.4 设计产出信息
- **设计模型**: claude-sonnet-4
- **设计日期**: 2025-05-30
- **文档版本**: V1.0
- **输出文件**: `luban-agent/ai-generates/design/be-s4-2-ai-dd-学生端-题型组件-一期-claude-sonnet-4.md`

---

**数据库设计任务已全部完成，最终文档已生成。**

--- 数据库设计完成，已按模板要求完成质量检查，等待您的命令，指挥官
-- 支持快速筛选特定题目的选项统计
ALTER TABLE student_analytics_db.tbl_option_statistics ADD INDEX idx_option_statistics_student_id student_id TYPE bloom_filter GRANULARITY 1;
-- 支持快速筛选特定学生的选择记录
ALTER TABLE student_analytics_db.tbl_option_statistics ADD INDEX idx_option_statistics_question_type question_type TYPE set(2) GRANULARITY 1;
-- 支持按题型筛选
```

---

### 3.8 表名: `tbl_behavior_anomalies` (ClickHouse)

#### 3.8.1 表功能说明
存储自评过程中的异常行为记录，监控连续快速自评等异常操作。该表采用ClickHouse存储，支持实时异常检测和行为分析，为系统安全和用户体验优化提供数据支撑。

#### 3.8.2 表结构定义 (DDL)
```sql
CREATE TABLE student_analytics_db.tbl_behavior_anomalies
(
    -- 时间字段
    log_date Date MATERIALIZED toDate(event_time) COMMENT '异常日期（用于分区）',
    event_time DateTime64(3) COMMENT '异常发生时间（毫秒精度）',
    
    -- 业务字段
    student_id UInt64 COMMENT '学生ID',
    anomaly_type LowCardinality(String) COMMENT '异常类型（continuous_fast_evaluation等）',
    trigger_time DateTime64(3) COMMENT '异常触发时间',
    consecutive_count UInt16 COMMENT '连续异常题目数量',
    average_duration Float32 COMMENT '平均操作时长（秒）',
    threshold_value Float32 COMMENT '触发阈值',
    
    -- 详细信息
    question_ids Array(UInt64) COMMENT '涉及的题目ID数组',
    evaluation_durations Array(Float32) COMMENT '各题目的自评时长数组',
    session_id String COMMENT '会话ID',
    
    -- 扩展数据
    detection_rule String COMMENT '检测规则描述',
    severity_level UInt8 COMMENT '严重程度（1:低, 2:中, 3:高）',
    metadata Map(String, String) COMMENT '扩展元数据'
)
ENGINE = MergeTree()
PARTITION BY toYYYYMM(log_date) -- 按月分区
ORDER BY (log_date, student_id, event_time, anomaly_type) -- 排序键
TTL log_date + INTERVAL 180 DAY DELETE -- 数据保留6个月
SETTINGS index_granularity = 8192;

-- 添加表注释
COMMENT ON TABLE student_analytics_db.tbl_behavior_anomalies IS '异常行为记录表 - 监控自评过程中的异常操作行为';

-- 跳数索引
ALTER TABLE student_analytics_db.tbl_behavior_anomalies ADD INDEX idx_behavior_anomalies_student_id student_id TYPE minmax GRANULARITY 4;
-- 支持快
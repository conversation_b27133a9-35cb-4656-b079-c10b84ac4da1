/**
 * API配置文件
 * 包含所有API相关的配置信息
 */

// 从环境配置文件导入API相关配置
import { API_URL, HOST_URL, API_TIMEOUT as ENV_API_TIMEOUT, API_RETRY_TIMES as ENV_API_RETRY_TIMES } from '../../../env';

// API基础URL - 从环境配置文件获取
export const API_BASE_URL = API_URL;
export const HOST_BASE_URL = HOST_URL;

// 其他API相关配置 - 从环境配置文件获取
export const API_TIMEOUT = ENV_API_TIMEOUT; // 30秒
export const API_RETRY_TIMES = ENV_API_RETRY_TIMES; // 请求失败重试次数
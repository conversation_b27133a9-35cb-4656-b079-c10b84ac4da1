# 产品需求文档 - 掌握度策略系统 v1.0

## 1. 需求背景与目标

### 1.1 需求目标
* **IMPORTANT AI (Chief Product Manager) INSTRUCTION:** 严格按照原始文档中的目标填写，不要想象，没有则无需填写。
- 通过掌握度策略的实现，可以精准评估学习状况，让学生和教师都能清楚了解学生在学习过程中的优势和不足
- 通过掌握度策略的实现，可以优化学习路径，为学生量身定制学习内容和练习题目，避免重复学习已经掌握的内容
- 该需求上线后，用户学习效率预计可以提升，个性化学习体验得到显著改善
- 为巩固练习和拓展练习等推荐策略提供前置输入，提升产品体验让用户感受到系统的个性化和智能性

## 2. 功能范围

### 2.1 核心功能
- **学科能力评估与分层**：基于学生考试成绩和学校信息进行用户能力分层，满足个性化学习路径规划场景，解决学生能力差异化评估痛点
  - 优先级：高
  - 依赖关系：无

- **知识点掌握度实时计算**：根据学生答题表现动态计算知识点掌握程度，满足学习内容动态调整场景，解决学习进度跟踪不准确痛点
  - 优先级：高
  - 依赖关系：依赖于学科能力评估与分层

- **外化掌握度展示**：向学生展示课程掌握情况的可视化指标，满足学习反馈和激励场景，解决学习进度不透明痛点
  - 优先级：高
  - 依赖关系：依赖于知识点掌握度实时计算

- **目标掌握度设定**：根据学习目标计算应达到的掌握度标准，满足目标导向学习场景，解决学习目标不明确痛点
  - 优先级：低（V1.0暂不实现）
  - 依赖关系：依赖于外化掌握度展示

### 2.2 辅助功能
- **薄弱知识点识别**：自动标识需要重点学习的知识点，为巩固练习推荐提供支持
- **掌握度数据收集**：收集学校基础信息和学生成绩数据，为掌握度计算提供支持
- **掌握度指标监控**：跟踪掌握度变化趋势和异常情况，为策略优化提供支持

### 2.3 非本期功能
- **知识点迁移性分析**：考虑知识点间的关联性和迁移效应，建议V2.0再考虑
- **个性化学习路径推荐**：基于掌握度的完整学习路径规划，建议后续迭代再考虑

## 3. 计算规则与公式

### 3.1 学科能力分层计算
- **高中学校水平校准**
  - 公式：学科能力排名 = 用户单科排名百分比 × 校准系数a × 校准系数b
    - 参数说明：
      - 校准系数a：根据学校本科录取率确定，取值范围[1, 3]
      - 校准系数b：根据生源波动情况确定，计算公式为√（历年排名中位数倒数平均 / 今年排名中位数倒数）
    - 规则：
      - 取值范围：[0,1]，向上取整
      - 分层标准：S+[0,5]、S(5,10]、A(10,50]、B(50,80]、C(80,100]

### 3.2 知识点掌握度计算
- **初始掌握度设置**
  - 公式：初始掌握度 = 学科初始值 × 分层掌握系数
    - 参数说明：
      - 学科初始值：统一设置为0.3
      - 分层掌握系数：S+为1.2，S为1.0，A为0.8，B为0.5，C为0.4
    - 规则：
      - 取值范围：[0,1]

- **单次答题掌握度增量**
  - 公式（首次答题）：
    - 作答正确：增量 = 0.2 × (题目难度系数 - 用户当前掌握度)
    - 作答错误：增量 = -0.1 × (题目难度系数 - 用户当前掌握度) × 答错补偿系数
    - 参数说明：
      - 题目难度系数：L1为0.3，L2为0.5，L3为0.7，L4为0.9，L5为1.1
      - 答错补偿系数：L1为1.8，L2为0.8，L3为0.5，L4为0.3，L5为0.2
    - 规则：
      - 累计掌握度 = 当前掌握度 + 单题掌握度增量
      - 结果限制在[0,1]区间

### 3.3 外化掌握度计算
- **外化掌握度展示**
  - 公式：外化掌握度 = 知识点掌握度 / 掌握度上限
    - 参数说明：
      - 掌握度上限：课程难度高为1，课程难度中为0.9，课程难度低为0.7
    - 规则：
      - 取值范围：[0,1]，四舍五入保留小数点后2位
      - 外化掌握度只增不降

### 3.4 目标掌握度计算（V1.0暂不实现）
- **目标掌握度设定**
  - 公式：目标掌握度 = 目标难度系数 × 单课难度系数 × 考试频率
    - 参数说明：
      - 目标难度系数：C9为1.3，985/211为1.2，一本为1.1，二本为1，二本以下为0.9
      - 单课难度系数：文科高0.7/中0.65/低0.6，理科高0.8/中0.7/低0.6
      - 考试频率：高1/中0.9/低0.8
    - 规则：
      - 换算成百分比，按0.05向上取整
      - 用于薄弱知识点判断：外化掌握度/目标掌握度 ≤ 0.7

### 3.5 薄弱知识点判断
- **薄弱知识点识别**
  - 判断条件：同时满足以下两个条件
    - 条件1：外化掌握度 / 目标掌握度 ≤ 0.7
    - 条件2：知识点掌握度 ≤ 0.5
  - 知识点掌握度计算条件：
    - 知识点有配置巩固练习
    - 知识点有配置拓展练习 或 巩固练习题目数量 ≥ 20

## 4. 用户场景与故事

### 三步场景识别分析

**步骤1-显性提取**：从原始需求中提取的明确场景
- 学生答题后掌握度更新
- 教师查看班级掌握度统计
- 新学生初始化掌握度设置

**步骤2-隐性推导**：基于业务逻辑推导的隐含场景
- 学生重复练习错题的处理
- 跨课程答题的掌握度管理
- 特殊题型（主观题）的掌握度计算
- 课程配置异常时的掌握度处理
- 薄弱知识点的识别和推荐

**步骤3-完整性验证**：确保覆盖完整用户旅程
- 从新用户注册到熟练使用的完整流程
- 从单次答题到长期学习的数据积累
- 从个人学习到班级管理的多角色协作

### 多维度场景矩阵分析

| 用户类型 | 使用阶段 | 目的状态 | 异常情况 |
|---------|---------|---------|---------|
| 新学生 | 初始化 | 获得个性化起点 | 数据缺失/错误 |
| 在校学生 | 日常学习 | 了解学习进度 | 网络异常/重复答题 |
| 优秀学生 | 提升阶段 | 挑战更高难度 | 掌握度虚高 |
| 薄弱学生 | 补强阶段 | 针对性提升 | 信心不足 |
| 任课教师 | 教学管理 | 调整教学策略 | 数据异常 |
| 班主任 | 班级管理 | 整体学情分析 | 学生差异大 |

### 场景1：新学生系统初始化（冷启动场景）

**7W深度分析：**
- **Who**：新入学的学生（高中/初中），具有不同的学习基础和能力水平
- **When**：学期开始时、转学时、首次使用AI课系统时，频次为一次性
- **Where**：学校机房、家庭环境、移动设备上
- **What**：获得基于真实能力的个性化掌握度起点，避免千篇一律的默认值
- **Why**：确保后续学习推荐的准确性，提升个性化学习体验
- **How**：数据收集→能力评估→分层计算→初始掌握度设置→验证确认
- **What if**：数据缺失时使用默认分层、数据错误时提供修正机制、系统异常时保存进度

**用户故事：**
作为一个刚入学的高一学生小明，我的数学在年级排名前20%，但我们学校是普通中学。我希望系统能够综合考虑我的成绩和学校水平，给我设置合适的初始掌握度，这样推荐给我的题目难度才会适中，不会太简单浪费时间，也不会太难打击信心。

**关键步骤：**
1. 学生/教师录入基础信息（成绩排名、学校类型）
2. 系统计算校准系数和学科能力分层
3. 为每个知识点设置个性化初始掌握度
4. 生成初始化报告供学生确认

- 优先级：高
- 依赖关系：无

### 场景2：学生日常答题学习（核心使用场景）

**7W深度分析：**
- **Who**：在校学生（各个能力层级），包括学霸、中等生、学困生
- **When**：每日学习时、课后练习时、考前复习时，频次为每日多次
- **Where**：教室、家庭、图书馆、通勤路上等各种学习环境
- **What**：通过答题获得即时的掌握度反馈，了解学习效果和进步情况
- **Why**：及时调整学习策略，保持学习动力，识别薄弱环节
- **How**：选择题目→答题→系统计算→掌握度更新→结果展示→学习建议
- **What if**：重复答题时衰减奖励、主观题时间过短不计分、网络异常时本地缓存

**用户故事：**
作为一个高二学生小红，我在做物理练习时，发现自己在"电磁感应"这个知识点上总是出错。我希望系统能够准确记录我的答题情况，当我重复练习错题时不要给我虚高的掌握度，同时能够识别出这是我的薄弱知识点，推荐相关的巩固练习。

**关键步骤：**
1. 学生在各种学习场景中答题（AI课、练习、作业）
2. 系统识别题目类型、难度、重复情况
3. 应用相应的计算规则更新掌握度
4. 实时展示掌握度变化和学习建议
5. 累积数据用于薄弱知识点识别

- 优先级：高
- 依赖关系：依赖于场景1

### 场景3：教师数据驱动教学（管理场景）

**7W深度分析：**
- **Who**：任课教师、班主任，具有不同的数据需求和关注重点
- **When**：备课时、课后总结时、家长会前、期中期末考试后，频次为每周数次
- **Where**：办公室、家庭、教研室等教师工作环境
- **What**：获得班级整体和个体学生的掌握度数据，指导教学决策
- **Why**：提高教学针对性，关注学困生，优化教学资源分配
- **How**：登录系统→选择班级/学生→查看数据报告→分析问题→调整教学
- **What if**：数据异常时提供异常标记、学生差异过大时分层建议、数据不足时给出提示

**用户故事：**
作为一个高三数学老师王老师，我需要了解班上40个学生在"导数应用"这个重点知识上的掌握情况。我希望系统能够告诉我有多少学生掌握度不足，哪些是薄弱知识点，这样我就可以在下节课重点讲解，并为不同水平的学生安排不同的练习。

**关键步骤：**
1. 教师选择查看的班级和时间范围
2. 系统生成掌握度分布报告和薄弱知识点统计
3. 教师分析数据识别教学问题
4. 根据数据调整教学计划和资源分配

- 优先级：中
- 依赖关系：依赖于场景2

### 场景4：学生自主学习规划（反思场景）

**7W深度分析：**
- **Who**：有自主学习意识的学生，通常是中等以上水平的学生
- **When**：周末总结时、考试前复习时、制定学习计划时，频次为每周一次
- **Where**：安静的学习环境，如家庭书房、图书馆
- **What**：查看个人学习数据，制定针对性的学习计划
- **Why**：提高学习效率，实现自我管理和持续改进
- **How**：查看报告→分析薄弱点→制定计划→执行学习→跟踪效果
- **What if**：数据不足时给出建议、目标过高时提醒调整、进步缓慢时鼓励坚持

**用户故事：**
作为一个有学习规划习惯的高一学生小李，我每周都会查看自己的学习报告。我希望系统能够告诉我这周在哪些知识点上有进步，哪些还需要加强，并且能够推荐适合我当前水平的学习内容，帮我制定下周的学习重点。

**关键步骤：**
1. 学生进入个人学习分析页面
2. 查看各科目掌握度趋势和变化
3. 系统识别薄弱知识点并推荐学习内容
4. 学生制定个性化学习计划
5. 设置学习目标和提醒

- 优先级：中
- 依赖关系：依赖于场景2


**复杂场景处理说明**：
- **多角色交互场景**：学生-教师-系统管理员的权限分离和数据共享机制
- **异常情况处理**：网络异常、数据错误、计算异常的分级处理和恢复机制
- **数据依赖场景**：学校数据、成绩数据的获取、验证、更新的完整流程
- **边界条件处理**：极端掌握度值、异常答题行为、系统性能瓶颈的处理策略

**复杂场景处理说明**：
- 多角色交互场景：需要明确学生、教师、系统管理员的操作步骤和权限
- 异常情况处理：需要描述网络异常、数据错误、计算异常的恢复机制
- 数据依赖场景：需要说明学校数据、成绩数据的获取方式和更新机制
- 边界条件处理：需要处理掌握度计算中的特殊情况和边界值

## 5. 业务流程图

### 新用户初始化流程（场景1）
```mermaid
graph TD
    A[新用户注册] --> B[收集学校信息]
    B --> C[收集成绩数据]
    C --> D[计算校准系数]
    D --> E[确定用户分层]
    E --> F[设置初始掌握度]
    F --> G[初始化完成]
```

### 学生答题掌握度更新流程（场景2）
```mermaid
graph TD
    A[学生答题] --> B{题目类型检查}
    B -->|客观题| C{是否重复答题}
    B -->|主观题| D{作答时间≥5秒}
    D -->|是| E[应用0.5权重系数]
    D -->|否| F[不更新掌握度]
    C -->|首次| G[正常计算增量]
    C -->|重复且之前答错| H[应用衰减系数]
    C -->|重复且之前答对| I[不更新掌握度]
    E --> J[更新知识点掌握度]
    G --> J
    H --> J
    J --> K{累计答题≥5题}
    K -->|是| L[更新外化掌握度]
    K -->|否| M{课程是否完成}
    M -->|是且题目<5| L
    M -->|是且无题目| N[设置默认掌握度0.2]
    L --> O[展示掌握度结果]
    N --> O
    F --> P[结束]
    I --> P
    O --> Q[判断薄弱知识点]
    Q --> R[推荐学习内容]
```

### 教师查看数据流程（场景3）
```mermaid
graph TD
    A[教师登录] --> B[选择班级]
    B --> C[系统统计掌握度数据]
    C --> D[展示班级掌握度分布]
    D --> E[标识薄弱知识点]
    E --> F[教师调整教学策略]
```

### 学生查看报告流程（场景4）
```mermaid
graph TD
    A[学生进入报告页] --> B[系统分析掌握度数据]
    B --> C[展示各科目掌握度]
    C --> D[识别薄弱知识点]
    D --> E[推荐学习内容]
    E --> F[学生制定学习计划]
```

## 6. 性能与安全需求

### 6.1 性能需求
- 响应时间：掌握度计算和更新的P95响应时间应不超过2秒
- 并发用户数：系统需能支持至少1000用户同时在线答题并更新掌握度，且性能无明显下降
- 数据处理能力：系统需能处理每秒500次掌握度更新请求
- 数据量：系统需支持千万级学生数据和亿级答题记录的存储和高效查询，掌握度查询响应时间应在1秒内

### 6.2 安全需求
- 用户认证与授权：所有访问掌握度数据的接口必须经过严格的身份认证和权限校验，支持基于角色的访问控制
- 数据保护：学生成绩和掌握度等敏感信息在存储和传输过程中必须加密，符合教育数据安全标准
- 操作审计：对所有掌握度数据修改操作必须记录详细的操作日志，包含操作人、操作时间、操作对象、操作内容等关键信息
- 防护要求：系统应具备对常见Web攻击的基本防护能力，确保掌握度计算的准确性和可靠性

## 7. 验收标准

### 7.1 功能验收
- **学科能力评估与分层**：
  - 使用场景：当新学生提供成绩和学校信息时
  - 期望结果：系统应正确计算校准系数并分配合适的能力分层，满足分层标准要求

- **知识点掌握度实时计算**：
  - 使用场景：当学生完成答题后
  - 期望结果：系统应根据答题结果和题目难度准确计算掌握度增量，更新后的掌握度在合理范围内

- **外化掌握度展示**：
  - 使用场景：当学生累计答题5题以上后
  - 期望结果：系统应展示准确的外化掌握度百分比，且只增不降

### 7.2 性能验收
- 响应时间：
  - 测试场景：模拟100个并发用户同时答题并更新掌握度
  - 验收标准：P95响应时间不超过2秒

- 并发用户数：
  - 测试场景：在标准硬件环境下，逐步增加并发用户至1000个，持续执行答题和掌握度更新操作10分钟
  - 验收标准：系统应保持稳定运行，无错误率飙升，掌握度更新的平均响应时间不超过1秒

### 7.3 安全验收
- 用户认证与授权：
  - 测试场景：未授权用户尝试访问掌握度数据
  - 验收标准：系统应拒绝访问，并返回明确的权限不足提示
  
- 数据保护：
  - 验证点：通过数据库检查和传输抓包验证敏感数据加密情况
  - 验收标准：学生成绩和掌握度数据在存储和传输层面符合加密标准

- 操作审计：
  - 测试场景：执行掌握度数据修改操作
  - 验收标准：操作日志被准确、完整地记录，包含所有必要的审计字段

## 8. 其他需求

### 8.1 可用性需求
- 界面友好：掌握度展示界面应简洁直观，学生在无额外培训的情况下，3步内可查看自己的掌握度情况
- 容错处理：当发生网络异常或计算错误时，系统应能给出清晰的错误提示，保存用户答题进度，避免数据丢失
- 兼容性：掌握度功能需在主流浏览器和移动设备上正常运行，保证界面和功能正常

### 8.2 维护性需求
- 配置管理需求：关键业务规则参数（如难度系数、校准系数、分层阈值）应支持运营人员通过管理后台进行可视化调整，调整后可实时生效
- 监控与告警需求：掌握度计算异常、数据同步失败、性能指标超出阈值时，系统应触发告警并通知技术支持团队
- 日志需求：掌握度计算、更新、查询等关键操作必须被记录，日志应包含用户ID、操作时间、计算参数、结果等信息，支持问题排查和数据分析

## 9. 用户反馈和迭代计划

### 9.1 用户反馈机制
- 产品应提供至少2种用户反馈渠道：应用内反馈入口、客服邮箱
- 应有机制定期收集、整理和分析来自各渠道的用户反馈，识别掌握度策略的问题和改进机会

### 9.2 迭代计划（高阶产品方向）
- 产品计划以每月为周期进行迭代，持续优化掌握度算法和用户体验
- 下一个迭代重点可能包括：知识点迁移性分析、个性化学习路径推荐、掌握度预测模型优化

## 10. 需求检查清单

使用下面的检查清单，确保原始需求都被正确地包含和体现在整理后的需求文档中。

| 原始需求 | 对应需求点 | 完整性 | 正确性 | 一致性 | 备注 |
|---------|-----------|--------|--------|--------|------|
| 学科能力（用户分层）基于考试数据更新 | 2.1-学科能力评估与分层 | ✅完整 | ✅准确 | ✅一致 | 已包含完整计算逻辑 |
| 知识点掌握度每次答题后更新 | 2.1-知识点掌握度实时计算 | ✅完整 | ✅准确 | ✅一致 | 已包含实时更新机制 |
| 外化掌握度完成学习任务后更新 | 2.1-外化掌握度展示 | ✅完整 | ✅准确 | ✅一致 | 已包含更新时机和展示规则 |
| 目标掌握度根据目标推导 | 2.1-目标掌握度设定 | ✅完整 | ✅准确 | ✅一致 | 已包含计算公式和规则 |
| 高中学校水平校准系数计算 | 3.1-学科能力分层计算 | ✅完整 | ✅准确 | ✅一致 | 已包含详细计算公式 |
| 初中学校类型分层 | 3.1-学科能力分层计算 | ✅完整 | ✅准确 | ✅一致 | 已包含初中分层逻辑 |
| 知识点初始掌握度设置 | 3.2-知识点掌握度计算 | ✅完整 | ✅准确 | ✅一致 | 已包含初始值和系数 |
| 单次答题掌握度增量计算 | 3.2-知识点掌握度计算 | ✅完整 | ✅准确 | ✅一致 | 已包含完整计算公式 |
| 错题重练衰减系数 | 3.2-知识点掌握度计算 | ✅完整 | ✅准确 | ✅一致 | 已包含衰减机制 |
| 外化掌握度计算方式 | 3.3-外化掌握度计算 | ✅完整 | ✅准确 | ✅一致 | 已包含计算公式和上限 |
| 首次答题量限制5题 | 3.3-外化掌握度计算 | ✅完整 | ✅准确 | ✅一致 | 已包含答题量限制 |
| 薄弱知识点判断逻辑 | 2.2-薄弱知识点识别 | ✅完整 | ✅准确 | ✅一致 | 已包含判断条件 |
| 知识点是否有掌握度判断 | 2.2-薄弱知识点识别 | ✅完整 | ✅准确 | ✅一致 | 已包含判断逻辑 |
| 学校数据收集要求 | 2.2-掌握度数据收集 | ✅完整 | ✅准确 | ✅一致 | 已包含数据收集规范 |
| 指标跟踪监控 | 2.2-掌握度指标监控 | ✅完整 | ✅准确 | ✅一致 | 已包含监控要求 |
| V1.0不考虑知识点迁移性 | 2.3-非本期功能 | ✅完整 | ✅准确 | ✅一致 | 已明确标注为非本期 |
| 错题重练衰减机制 | 4-场景4：学生重复练习同一题目 | ✅完整 | ✅准确 | ✅一致 | 已包含衰减系数和重练逻辑 |
| 同一课程重复答题限制 | 4-场景4：学生重复练习同一题目 | ✅完整 | ✅准确 | ✅一致 | 已包含答对题目不再更新的规则 |
| 跨课程答题处理 | 4-场景7：跨课程答题的掌握度处理 | ✅完整 | ✅准确 | ✅一致 | 已包含跨课程答题规则 |
| AI课程无题目特殊处理 | 4-场景5：AI课程无题目或题目不足的处理 | ✅完整 | ✅准确 | ✅一致 | 已包含默认掌握度0.2的处理 |
| 自评主观题权重调整 | 4-场景6：自评主观题的特殊处理 | ✅完整 | ✅准确 | ✅一致 | 已包含作答时间和权重系数 |
| 薄弱知识点判断和推荐 | 4-场景8：薄弱知识点识别与推荐 | ✅完整 | ✅准确 | ✅一致 | 已包含判断条件和推荐机制 |
| 部分正确答题处理 | 3.2-知识点掌握度计算 | ✅完整 | ✅准确 | ✅一致 | 已包含部分正确的计算公式 |
| 特殊情况边界值处理 | 3.2-知识点掌握度计算 | ✅完整 | ✅准确 | ✅一致 | 已包含边界值处理规则 |
| 目标掌握度计算公式 | 3.4-目标掌握度计算 | ✅完整 | ✅准确 | ✅一致 | 已包含完整计算公式（V1.0暂不实现） |
| 目标难度系数设置 | 3.4-目标掌握度计算 | ✅完整 | ✅准确 | ✅一致 | 已包含高中和初中的系数表 |
| 单课难度系数和考试频率 | 3.4-目标掌握度计算 | ✅完整 | ✅准确 | ✅一致 | 已包含文理科难度系数 |
| 知识点掌握度计算条件判断 | 3.5-薄弱知识点判断 | ✅完整 | ✅准确 | ✅一致 | 已包含练习配置判断逻辑 |
| 指标跟踪监控要求 | 8.2-维护性需求 | ✅完整 | ✅准确 | ✅一致 | 已包含外化掌握度变化和答题量监控 |

## 11. 附录

### 11.1 原型图
[此处需要补充掌握度展示界面的原型图和交互设计图]

### 11.2 术语表
- **学科能力**：量化学生在某个学科的能力水平，用于学生分层和学习路径规划
- **知识点掌握度**：量化学生对某个知识点的熟练程度，用于动态调整学习内容
- **外化掌握度**：向学生展示的课程掌握程度，用于激励和牵引用户学习
- **目标掌握度**：根据学习目标推导出的应达到的掌握程度标准
- **校准系数**：用于调整不同学校和年级间差异的修正参数
- **业务树**：层级化的知识点组织结构，末级节点对应单节课程

### 11.3 参考资料
- 原始需求文档：《PRD - 掌握度策略 - V1.0》
- 关联需求：《PRD - AI课中 - V1.0》
- 产品需求文档模板 v2.3